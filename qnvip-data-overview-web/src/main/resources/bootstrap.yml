server:
  port: 20310

spring:
  main:
    allow-bean-definition-overriding: true
  application:
#    name: qnvip-data-overview-pre
   #   name: qnvip-data-overview2
     name: qnvip-data-overview-gray
  cloud:
    nacos:
      discovery:
        server-addr: ${nacos.server-addr}
      config:
        enabled: true
        server-addr: ${nacos.server-addr}
        namespace: ${nacos.config-namespace}
        file-extension: yml
        group: DEFAULT_GROUP
        refresh-enabled: true
        # 常规配置文件
        # 优先级大于 shared-configs，在 shared-configs 之后加载
        extension-configs:
          - data-id: ${spring.application.name}.yml
            group: DEFAULT_GROUP
            refresh: true

management:
  server:
    port: 9310 # 找运维分配端口, 这个端口和提供 web 服务的端口隔离，ECS 安全组策略不允许外网访问
  # health 显示详情
  endpoint.health.show-details: "ALWAYS"
  endpoints:
    web:
      exposure:
        include: ['health', 'info', 'loggers', 'prometheus']
dubbo:
  cloud:
    subscribed-services: ${spring.application.name}
  config-center:
    check: false
  application:
    name: ${spring.application.name}
    shutwait: 300
  provider:
    register: false
  consumer:
    timeout: 5000
    check: false

