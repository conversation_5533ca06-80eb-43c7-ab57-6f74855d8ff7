<?xml version="1.0" encoding="utf-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="qnvip.data.overview.mapper.order.RentOrderMapper">

    <select id="select12"
            resultType="map"><![CDATA[


        ]]></select>

    <select id="select13"
            resultType="map"><![CDATA[


        ]]></select>

    <select id="select14"
            resultType="map"><![CDATA[
        select
               ROUND(if(weekday(create_time) = 3 ,'1.1587' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.999') ,
                        if(weekday(create_time) = 2 , '1.181' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9968') ,
                           if(weekday(create_time) = 1 , '1.2753' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9855') ,
                              if(weekday(create_time) = 0 , '1.1201' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0051') ,
                                 if(weekday(create_time) = 6 , '1.1505' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.001') ,
                                    if(weekday(create_time) = 5 , '1.2' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9938') ,
                                       if(weekday(create_time) = 4 , '1.1696' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9985'),
                                          null)))))))) fifteen,
               ROUND(if(weekday(create_time) = 3 ,'1.3183' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.999') ,
                        if(weekday(create_time) = 2 , '1.2963' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0022') ,
                           if(weekday(create_time) = 1 , '1.4908' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9814') ,
                              if(weekday(create_time) = 0 , '1.2768' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0054') ,
                                 if(weekday(create_time) = 6 , '1.3183' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9991') ,
                                    if(weekday(create_time) = 5 , '1.406' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9889') ,
                                       if(weekday(create_time) = 4 , '1.2847' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0041'),
                                          null)))))))) sixteen,
               ROUND(if(weekday(create_time) = 3 ,'1.4881' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9968') ,
                        if(weekday(create_time) = 2 , '1.4921' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9975') ,
                           if(weekday(create_time) = 1 , '1.6831' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9795') ,
                              if(weekday(create_time) = 0 , '1.3342' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0157') ,
                                 if(weekday(create_time) = 6 , '1.6268' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9838') ,
                                    if(weekday(create_time) = 5 , '1.6658' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9791') ,
                                       if(weekday(create_time) = 4 , '1.4292' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0041'),
                                          null)))))))) seventeen,
               ROUND(if(weekday(create_time) = 3 ,'1.4897' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0127') ,
                        if(weekday(create_time) = 2 , '1.6528' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.998') ,
                           if(weekday(create_time) = 1 , '1.9962' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9684') ,
                              if(weekday(create_time) = 0 , '1.4534' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0176') ,
                                 if(weekday(create_time) = 6 , '1.8394' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9788') ,
                                    if(weekday(create_time) = 5 , '1.8587' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9766') ,
                                       if(weekday(create_time) = 4 , '1.6251' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.001'),
                                          null)))))))) eighteen,
               ROUND(if(weekday(create_time) = 3 ,'1.5862' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0144') ,
                        if(weekday(create_time) = 2 , '1.8469' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9923') ,
                           if(weekday(create_time) = 1 , '2.3019' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9571') ,
                              if(weekday(create_time) = 0 , '1.5818' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0159') ,
                                 if(weekday(create_time) = 6 , '2.0288' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9749') ,
                                    if(weekday(create_time) = 5 , '1.9706' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.978') ,
                                       if(weekday(create_time) = 4 , '1.7457' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0014'),
                                          null)))))))) nineteen,
               ROUND(if(weekday(create_time) = 3 ,'1.646' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0171') ,
                        if(weekday(create_time) = 2 , '1.8863' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.995') ,
                           if(weekday(create_time) = 1 , '2.3759' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9609') ,
                              if(weekday(create_time) = 0 , '1.7519' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0093') ,
                                 if(weekday(create_time) = 6 , '2.1856' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9726') ,
                                    if(weekday(create_time) = 5 , '2.1305' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9744') ,
                                       if(weekday(create_time) = 4 , '1.9125' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9966'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3 ,'1.8848' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0033') ,
                        if(weekday(create_time) = 2 , '1.9302' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0001') ,
                           if(weekday(create_time) = 1 , '2.5501' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9581') ,
                              if(weekday(create_time) = 0 , '1.8691' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0058') ,
                                 if(weekday(create_time) = 6 , '2.6172' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9511') ,
                                    if(weekday(create_time) = 5 , '2.3669' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9657') ,
                                       if(weekday(create_time) = 4 , '1.9777' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9978'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3 ,'1.9352' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0079') ,
                        if(weekday(create_time) = 2 , '2.2977' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9834') ,
                           if(weekday(create_time) = 1 , '2.776' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9523') ,
                              if(weekday(create_time) = 0 , '2.0282' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0032') ,
                                 if(weekday(create_time) = 6 , '2.7276' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9548') ,
                                    if(weekday(create_time) = 5 , '2.5156' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9645') ,
                                       if(weekday(create_time) = 4 , '2.1896' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9912'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3 ,'2.0794' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0042') ,
                        if(weekday(create_time) = 2 , '2.3673' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9846') ,
                           if(weekday(create_time) = 1 , '2.9931' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9476') ,
                              if(weekday(create_time) = 0 , '2.1289' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'1.0014') ,
                                 if(weekday(create_time) = 6 , '3.1246' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9392') ,
                                    if(weekday(create_time) = 5 , '2.7588' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9566') ,
                                       if(weekday(create_time) = 4 , '2.408' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9834'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3 ,'2.337' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9914') ,
                        if(weekday(create_time) = 2 , '2.5181' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9799') ,
                           if(weekday(create_time) = 1 , '3.2212' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9416') ,
                              if(weekday(create_time) = 0 , '2.2373' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9983') ,
                                 if(weekday(create_time) = 6 , '3.2602' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9388') ,
                                    if(weekday(create_time) = 5 , '3.0128' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.9489') ,
                                       if(weekday(create_time) = 4 , '2.4286' * pow(count(distinct if(HOUR(create_time) < 14, customer_id , null)),'0.987'),
                                          null)))))))) twenty_four
        from rent_order ro where is_deleted = 0 and type = 1 and merchant_id = 100 and parent_id = 0 and date(create_time) = curdate()
        ]]></select>

    <select id="select15"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '1.1671' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.997'),
                        if(weekday(create_time) = 2,
                           '1.099' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0053'),
                           if(weekday(create_time) = 1,
                              '1.1183' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0028'),
                              if(weekday(create_time) = 0,
                                 '1.064' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0107'),
                                 if(weekday(create_time) = 6, '1.2034' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9909'),
                                    if(weekday(create_time) = 5,
                                       '1.0269' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '1.0144'),
                                       if(weekday(create_time) = 4, '1.0365' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0142'),
                                          null)))))))) sixteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.2629' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0013'),
                        if(weekday(create_time) = 2,
                           '1.2378' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0044'),
                           if(weekday(create_time) = 1,
                              '1.1526' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0147'),
                              if(weekday(create_time) = 0,
                                 '1.1079' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '1.0216'),
                                 if(weekday(create_time) = 6, '1.3647' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9886'),
                                    if(weekday(create_time) = 5,
                                       '1.1902' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '1.0083'),
                                       if(weekday(create_time) = 4, '1.1197' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0185'),
                                          null)))))))) seventeen,
               ROUND(if(weekday(create_time) = 3,
                        '1.2318' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.021'),
                        if(weekday(create_time) = 2,
                           '1.4892' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9925'),
                           if(weekday(create_time) = 1,
                              '1.3962' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0014'),
                              if(weekday(create_time) = 0,
                                 '1.1831' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '1.0261'),
                                 if(weekday(create_time) = 6, '1.5178' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9861'),
                                    if(weekday(create_time) = 5,
                                       '1.4924' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.989'),
                                       if(weekday(create_time) = 4, '1.2151' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0214'),
                                          null)))))))) eighteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.3569' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0182'),
                        if(weekday(create_time) = 2,
                           '1.6026' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9921'),
                           if(weekday(create_time) = 1,
                              '1.5097' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0002'),
                              if(weekday(create_time) = 0,
                                 '1.2633' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.027'),
                                 if(weekday(create_time) = 6, '1.5934' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9894'),
                                    if(weekday(create_time) = 5,
                                       '1.6219' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.9869'),
                                       if(weekday(create_time) = 4, '1.2611' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0262'),
                                          null)))))))) nineteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.3389' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0278'),
                        if(weekday(create_time) = 2,
                           '1.6346' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9976'),
                           if(weekday(create_time) = 1,
                              '1.6385' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9968'),
                              if(weekday(create_time) = 0,
                                 '1.3928' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '1.0215'),
                                 if(weekday(create_time) = 6, '1.607' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9969'),
                                    if(weekday(create_time) = 5,
                                       '1.8215' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.9779'),
                                       if(weekday(create_time) = 4, '1.5659' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0034'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3,
                        '1.6844' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0031'),
                        if(weekday(create_time) = 2,
                           '1.8285' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9887'),
                           if(weekday(create_time) = 1,
                              '1.8008' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9907'),
                              if(weekday(create_time) = 0,
                                 '1.6008' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '1.0097'),
                                 if(weekday(create_time) = 6, '1.8228' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.986'),
                                    if(weekday(create_time) = 5,
                                       '1.9497' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.9755'),
                                       if(weekday(create_time) = 4, '1.61' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0069'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.678' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0094'),
                        if(weekday(create_time) = 2,
                           '2.065' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9781'),
                           if(weekday(create_time) = 1,
                              '2.0991' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9753'),
                              if(weekday(create_time) = 0,
                                 '1.6494' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '1.0128'),
                                 if(weekday(create_time) = 6, '1.936' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9845'),
                                    if(weekday(create_time) = 5,
                                       '2.129' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                     '0.9694'),
                                       if(weekday(create_time) = 4, '1.6501' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0103'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.7561' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0092'),
                        if(weekday(create_time) = 2,
                           '2.317' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9678'),
                           if(weekday(create_time) = 1,
                              '2.3621' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9644'),
                              if(weekday(create_time) = 0,
                                 '1.9009' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                '0.9987'),
                                 if(weekday(create_time) = 6, '2.0086' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9859'),
                                    if(weekday(create_time) = 5,
                                       '2.3975' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.9585'),
                                       if(weekday(create_time) = 4, '1.7329' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.0095'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '2.1605' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9857'),
                        if(weekday(create_time) = 2,
                           '2.5103' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9614'),
                           if(weekday(create_time) = 1,
                              '2.611' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9552'),
                              if(weekday(create_time) = 0,
                                 '2.2246' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.981'),
                                 if(weekday(create_time) = 6, '2.0867' * pow(
                                         count(distinct if(HOUR(create_time) < 15, customer_id, null)), '0.9862'),
                                    if(weekday(create_time) = 5,
                                       '2.5446' * pow(count(distinct if(HOUR(create_time) < 15, customer_id, null)),
                                                      '0.9552'),
                                       if(weekday(create_time) = 4, '1.8798' * pow(
                                               count(distinct if(HOUR(create_time) < 15, customer_id, null)), '1.003'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select16"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '1.0709' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0054'),
                        if(weekday(create_time) = 2,
                           '1.0724' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0057'),
                           if(weekday(create_time) = 1,
                              '1.1214' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9992'),
                              if(weekday(create_time) = 0,
                                 '1.0025' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                '1.0158'),
                                 if(weekday(create_time) = 6, '1.2148' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9875'),
                                    if(weekday(create_time) = 5,
                                       '1.109' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                     '1'),
                                       if(weekday(create_time) = 4, '1.0914' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9982'),
                                          null)))))))) seventeen,
               ROUND(if(weekday(create_time) = 3,
                        '1.012' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0289'),
                        if(weekday(create_time) = 2,
                           '1.3086' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9923'),
                           if(weekday(create_time) = 1,
                              '1.2521' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9976'),
                              if(weekday(create_time) = 0,
                                 '1.1216' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                '1.0141'),
                                 if(weekday(create_time) = 6, '1.3483' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9853'),
                                    if(weekday(create_time) = 5,
                                       '1.2616' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9948'),
                                       if(weekday(create_time) = 4, '1.1587' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0102'),
                                          null)))))))) eighteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.0759' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0309'),
                        if(weekday(create_time) = 2,
                           '1.4278' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9901'),
                           if(weekday(create_time) = 1,
                              '1.35' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9966'),
                              if(weekday(create_time) = 0,
                                 '1.3135' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.002'),
                                 if(weekday(create_time) = 6, '1.5065' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9795'),
                                    if(weekday(create_time) = 5,
                                       '1.3231' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9978'),
                                       if(weekday(create_time) = 4, '1.2798' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0066'),
                                          null)))))))) nineteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.1128' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0347'),
                        if(weekday(create_time) = 2,
                           '1.5331' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9881'),
                           if(weekday(create_time) = 1,
                              '1.4158' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.998'),
                              if(weekday(create_time) = 0,
                                 '1.4347' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                '0.9978'),
                                 if(weekday(create_time) = 6, '1.6046' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9786'),
                                    if(weekday(create_time) = 5,
                                       '1.4418' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9932'),
                                       if(weekday(create_time) = 4, '1.4685' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9954'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3,
                        '1.2376' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0267'),
                        if(weekday(create_time) = 2,
                           '1.645' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9853'),
                           if(weekday(create_time) = 1,
                              '1.5313' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9941'),
                              if(weekday(create_time) = 0,
                                 '1.6039' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                '0.9897'),
                                 if(weekday(create_time) = 6, '1.8259' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9674'),
                                    if(weekday(create_time) = 5,
                                       '1.6072' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9851'),
                                       if(weekday(create_time) = 4, '1.574' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.993'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.3361' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0226'),
                        if(weekday(create_time) = 2,
                           '1.8116' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9785'),
                           if(weekday(create_time) = 1,
                              '1.6986' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9858'),
                              if(weekday(create_time) = 0,
                                 '1.6656' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                '0.9917'),
                                 if(weekday(create_time) = 6, '1.9378' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9659'),
                                    if(weekday(create_time) = 5,
                                       '1.7809' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9771'),
                                       if(weekday(create_time) = 4, '1.645' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9936'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.5636' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '1.0045'),
                        if(weekday(create_time) = 2,
                           '1.7288' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9901'),
                           if(weekday(create_time) = 1,
                              '2.0128' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9667'),
                              if(weekday(create_time) = 0,
                                 '1.7992' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.986'),
                                 if(weekday(create_time) = 6, '2.0625' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9636'),
                                    if(weekday(create_time) = 5,
                                       '1.9874' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9671'),
                                       if(weekday(create_time) = 4, '1.6865' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9948'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '1.6863' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9989'),
                        if(weekday(create_time) = 2,
                           '1.844' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9862'),
                           if(weekday(create_time) = 1,
                              '2.1564' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9622'),
                              if(weekday(create_time) = 0,
                                 '2.0145' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.975'),
                                 if(weekday(create_time) = 6, '2.1004' * pow(
                                         count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9668'),
                                    if(weekday(create_time) = 5,
                                       '2.1295' * pow(count(distinct if(HOUR(create_time) < 16, customer_id, null)),
                                                      '0.9625'),
                                       if(weekday(create_time) = 4, '1.7517' * pow(
                                               count(distinct if(HOUR(create_time) < 16, customer_id, null)), '0.9943'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select17"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '0.9731' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0195'),
                        if(weekday(create_time) = 2,
                           '1.1047' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0009'),
                           if(weekday(create_time) = 1,
                              '1.1885' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9893'),
                              if(weekday(create_time) = 0,
                                 '1.0817' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '1.0029'),
                                 if(weekday(create_time) = 6, '1.1058' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9984'),
                                    if(weekday(create_time) = 5,
                                       '1.1081' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.9987'),
                                       if(weekday(create_time) = 4, '1.1324' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9977'),
                                          null)))))))) eighteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.057' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0179'),
                        if(weekday(create_time) = 2,
                           '1.2326' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9957'),
                           if(weekday(create_time) = 1,
                              '1.3669' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9789'),
                              if(weekday(create_time) = 0,
                                 '1.173' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0017'),
                                 if(weekday(create_time) = 6, '1.235' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9931'),
                                    if(weekday(create_time) = 5,
                                       '1.1679' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '1.0008'),
                                       if(weekday(create_time) = 4, '1.2142' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9983'),
                                          null)))))))) nineteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.0754' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0241'),
                        if(weekday(create_time) = 2,
                           '1.3287' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9933'),
                           if(weekday(create_time) = 1,
                              '1.4062' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.983'),
                              if(weekday(create_time) = 0,
                                 '1.2971' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '0.9958'),
                                 if(weekday(create_time) = 6, '1.3277' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9912'),
                                    if(weekday(create_time) = 5,
                                       '1.2597' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.9978'),
                                       if(weekday(create_time) = 4, '1.3277' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9941'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3,
                        '1.1773' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0184'),
                        if(weekday(create_time) = 2,
                           '1.4044' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9928'),
                           if(weekday(create_time) = 1,
                              '1.5125' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.98'),
                              if(weekday(create_time) = 0,
                                 '1.3917' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '0.9936'),
                                 if(weekday(create_time) = 6, '1.48' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9833'),
                                    if(weekday(create_time) = 5,
                                       '1.4112' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.989'),
                                       if(weekday(create_time) = 4, '1.4463' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9892'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.266' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0152'),
                        if(weekday(create_time) = 2,
                           '1.5255' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9881'),
                           if(weekday(create_time) = 1,
                              '1.6499' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9743'),
                              if(weekday(create_time) = 0,
                                 '1.4624' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '0.9939'),
                                 if(weekday(create_time) = 6, '1.6621' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9739'),
                                    if(weekday(create_time) = 5,
                                       '1.4964' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.9875'),
                                       if(weekday(create_time) = 4, '1.5151' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9895'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.3723' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0103'),
                        if(weekday(create_time) = 2,
                           '1.6464' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9835'),
                           if(weekday(create_time) = 1,
                              '1.7895' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.969'),
                              if(weekday(create_time) = 0,
                                 '1.5772' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '0.9894'),
                                 if(weekday(create_time) = 6, '1.7967' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9694'),
                                    if(weekday(create_time) = 5,
                                       '1.6574' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.9792'),
                                       if(weekday(create_time) = 4, '1.6698' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9818'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '1.5176' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '1.0012'),
                        if(weekday(create_time) = 2,
                           '1.7112' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9833'),
                           if(weekday(create_time) = 1,
                              '1.8904' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9664'),
                              if(weekday(create_time) = 0,
                                 '1.7314' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                '0.9813'),
                                 if(weekday(create_time) = 6, '1.8877' * pow(
                                         count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.968'),
                                    if(weekday(create_time) = 5,
                                       '1.8156' * pow(count(distinct if(HOUR(create_time) < 17, customer_id, null)),
                                                      '0.9715'),
                                       if(weekday(create_time) = 4, '1.8091' * pow(
                                               count(distinct if(HOUR(create_time) < 17, customer_id, null)), '0.9758'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select18"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '1.0519' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0034'),
                        if(weekday(create_time) = 2,
                           '1.0884' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9985'),
                           if(weekday(create_time) = 1,
                              '1.1537' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9894'),
                              if(weekday(create_time) = 0,
                                 '1.0826' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9991'),
                                 if(weekday(create_time) = 6, '1.0976' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9971'),
                                    if(weekday(create_time) = 5,
                                       '1.0524' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '1.0024'),
                                       if(weekday(create_time) = 4, '1.0555' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0029'),
                                          null)))))))) nineteen,
               ROUND(if(weekday(create_time) = 3,
                        '1.0696' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0096'),
                        if(weekday(create_time) = 2,
                           '1.1656' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.997'),
                           if(weekday(create_time) = 1,
                              '1.1832' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9939'),
                              if(weekday(create_time) = 0,
                                 '1.1977' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9932'),
                                 if(weekday(create_time) = 6, '1.1774' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9956'),
                                    if(weekday(create_time) = 5,
                                       '1.1332' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9996'),
                                       if(weekday(create_time) = 4, '1.147' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9996'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3,
                        '1.1716' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.004'),
                        if(weekday(create_time) = 2,
                           '1.2288' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9969'),
                           if(weekday(create_time) = 1,
                              '1.2718' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.991'),
                              if(weekday(create_time) = 0,
                                 '1.2836' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9912'),
                                 if(weekday(create_time) = 6, '1.3121' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9879'),
                                    if(weekday(create_time) = 5,
                                       '1.2691' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.991'),
                                       if(weekday(create_time) = 4, '1.2482' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.995'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.2596' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0008'),
                        if(weekday(create_time) = 2,
                           '1.3375' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.992'),
                           if(weekday(create_time) = 1,
                              '1.3876' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9853'),
                              if(weekday(create_time) = 0,
                                 '1.3491' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9914'),
                                 if(weekday(create_time) = 6, '1.4742' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9785'),
                                    if(weekday(create_time) = 5,
                                       '1.3447' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9896'),
                                       if(weekday(create_time) = 4, '1.3076' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9952'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.367' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9958'),
                        if(weekday(create_time) = 2,
                           '1.4451' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9873'),
                           if(weekday(create_time) = 1,
                              '1.5051' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9801'),
                              if(weekday(create_time) = 0,
                                 '1.4591' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9866'),
                                 if(weekday(create_time) = 6, '1.5951' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9739'),
                                    if(weekday(create_time) = 5,
                                       '1.4907' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9813'),
                                       if(weekday(create_time) = 4, '1.4411' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9877'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '1.5112' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9869'),
                        if(weekday(create_time) = 2,
                           '1.5033' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.987'),
                           if(weekday(create_time) = 1,
                              '1.5899' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9776'),
                              if(weekday(create_time) = 0,
                                 '1.6066' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9782'),
                                 if(weekday(create_time) = 6, '1.6787' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9723'),
                                    if(weekday(create_time) = 5,
                                       '1.6341' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9736'),
                                       if(weekday(create_time) = 4, '1.5652' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9814'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select19"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '1.0106' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.007'),
                        if(weekday(create_time) = 2,
                           '1.0644' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9995'),
                           if(weekday(create_time) = 1,
                              '1.0224' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0049'),
                              if(weekday(create_time) = 0,
                                 '1.1046' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9944'),
                                 if(weekday(create_time) = 6, '1.0702' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9988'),
                                    if(weekday(create_time) = 5,
                                       '1.0756' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9974'),
                                       if(weekday(create_time) = 4, '1.0847' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.997'),
                                          null)))))))) twenty,
               ROUND(if(weekday(create_time) = 3,
                        '1.1082' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0013'),
                        if(weekday(create_time) = 2,
                           '1.1194' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9997'),
                           if(weekday(create_time) = 1,
                              '1.0972' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '1.0023'),
                              if(weekday(create_time) = 0,
                                 '1.1817' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9927'),
                                 if(weekday(create_time) = 6, '1.1924' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9912'),
                                    if(weekday(create_time) = 5,
                                       '1.2049' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9889'),
                                       if(weekday(create_time) = 4, '1.1804' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9924'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.1883' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9985'),
                        if(weekday(create_time) = 2,
                           '1.2187' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9948'),
                           if(weekday(create_time) = 1,
                              '1.197' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9967'),
                              if(weekday(create_time) = 0,
                                 '1.2416' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9929'),
                                 if(weekday(create_time) = 6, '1.3399' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9819'),
                                    if(weekday(create_time) = 5,
                                       '1.2768' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9875'),
                                       if(weekday(create_time) = 4, '1.236' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9927'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.2892' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9936'),
                        if(weekday(create_time) = 2,
                           '1.3169' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9902'),
                           if(weekday(create_time) = 1,
                              '1.2985' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9915'),
                              if(weekday(create_time) = 0,
                                 '1.3429' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9882'),
                                 if(weekday(create_time) = 6, '1.45' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9774'),
                                    if(weekday(create_time) = 5,
                                       '1.4151' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9793'),
                                       if(weekday(create_time) = 4, '1.363' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9852'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '1.4235' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9849'),
                        if(weekday(create_time) = 2,
                           '1.3705' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9898'),
                           if(weekday(create_time) = 1,
                              '1.3697' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9892'),
                              if(weekday(create_time) = 0,
                                 '1.4803' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                '0.9796'),
                                 if(weekday(create_time) = 6, '1.5273' * pow(
                                         count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9757'),
                                    if(weekday(create_time) = 5,
                                       '1.5519' * pow(count(distinct if(HOUR(create_time) < 19, customer_id, null)),
                                                      '0.9716'),
                                       if(weekday(create_time) = 4, '1.4809' * pow(
                                               count(distinct if(HOUR(create_time) < 19, customer_id, null)), '0.9789'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select20"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '1.0943' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9946'),
                        if(weekday(create_time) = 2,
                           '1.0496' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '1.0005'),
                           if(weekday(create_time) = 1,
                              '1.0713' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9976'),
                              if(weekday(create_time) = 0,
                                 '1.0681' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                '0.9986'),
                                 if(weekday(create_time) = 6, '1.1089' * pow(
                                         count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9931'),
                                    if(weekday(create_time) = 5,
                                       '1.12' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                    '0.9916'),
                                       if(weekday(create_time) = 4, '1.0876' * pow(
                                               count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9956'),
                                          null)))))))) twenty_one,
               ROUND(if(weekday(create_time) = 3,
                        '1.1707' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9922'),
                        if(weekday(create_time) = 2,
                           '1.1415' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9958'),
                           if(weekday(create_time) = 1,
                              '1.1675' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9922'),
                              if(weekday(create_time) = 0,
                                 '1.1209' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.999'),
                                 if(weekday(create_time) = 6, '1.2436' * pow(
                                         count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9842'),
                                    if(weekday(create_time) = 5,
                                       '1.1865' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                      '0.9902'),
                                       if(weekday(create_time) = 4, '1.1383' * pow(
                                               count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9959'),
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '1.2687' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9875'),
                        if(weekday(create_time) = 2,
                           '1.233' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9912'),
                           if(weekday(create_time) = 1,
                              '1.2652' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9872'),
                              if(weekday(create_time) = 0,
                                 '1.2125' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                '0.9942'),
                                 if(weekday(create_time) = 6, '1.3446' * pow(
                                         count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9799'),
                                    if(weekday(create_time) = 5,
                                       '1.315' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                     '0.9821'),
                                       if(weekday(create_time) = 4, '1.2557' * pow(
                                               count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9884'),
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '1.4009' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9789'),
                        if(weekday(create_time) = 2,
                           '1.2828' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9909'),
                           if(weekday(create_time) = 1,
                              '1.3327' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9851'),
                              if(weekday(create_time) = 0,
                                 '1.3377' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                '0.9857'),
                                 if(weekday(create_time) = 6, '1.4173' * pow(
                                         count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.978'),
                                    if(weekday(create_time) = 5,
                                       '1.4424' * pow(count(distinct if(HOUR(create_time) < 20, customer_id, null)),
                                                      '0.9744'),
                                       if(weekday(create_time) = 4, '1.3652' * pow(
                                               count(distinct if(HOUR(create_time) < 20, customer_id, null)), '0.9821'),
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select21"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '-0.000006' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0625' - '2.3184',
                        if(weekday(create_time) = 2,
                           '0.00001' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0201 ' + '17.226',
                           if(weekday(create_time) = 1,
                              '0.000003' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.036' + '9.2588',
                              if(weekday(create_time) = 0,
                                 '0.000003' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0462' + '2.9615',
                                 if(weekday(create_time) = 6, '0.00001' * pow(
                                         count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                              '1.0258' + '14.479',
                                    if(weekday(create_time) = 5, '-0.000002' * pow(
                                            count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                 '1.0494' + '2.1102',
                                       if(weekday(create_time) = 4, '0.000002' * pow(
                                               count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                    '1.0467' + '0.7766',
                                          null)))))))) twenty_two,
               ROUND(if(weekday(create_time) = 3,
                        '-0.0000003' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0925' + '12.719',
                        if(weekday(create_time) = 2,
                           '0.00002' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 21, customer_id, null)) * ' 1.0548' + '26.418',
                           if(weekday(create_time) = 1,
                              '0.000009' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0655' + '21.608',
                              if(weekday(create_time) = 0,
                                 '0.000008' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.08' + '11.519',
                                 if(weekday(create_time) = 6, '0.000008' * pow(
                                         count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                              '1.0824' + '12.98',
                                    if(weekday(create_time) = 5, '-0.000008' * pow(
                                            count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                 '1.1057' + '2.8549',
                                       if(weekday(create_time) = 4, '0.000006' * pow(
                                               count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                    '1.0801' + '9.2204',
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '0.000002' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.1249' + '20.546',
                        if(weekday(create_time) = 2,
                           '0.00002' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 21, customer_id, null)) * ' 1.0876' + '30.963',
                           if(weekday(create_time) = 1,
                              '0.00002' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.0797' + '35.093',
                              if(weekday(create_time) = 0,
                                 '0.000007' * pow(count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) * '1.1169' + '18.991',
                                 if(weekday(create_time) = 6, '0.000003' * pow(
                                         count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                              '1.1371' + '8.2615',
                                    if(weekday(create_time) = 5, '0.000002' * pow(
                                            count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                 '1.1222' + '17.48',
                                       if(weekday(create_time) = 4, '0.000006' * pow(
                                               count(distinct if(HOUR(create_time) < 21, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 21, customer_id, null)) *
                                                                    '1.1182' + '16.21',
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select22"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '0.000006' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0264' + '15.82',
                        if(weekday(create_time) = 2,
                           '0.000004' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 22, customer_id, null)) * ' 1.0336' + '8.5804',
                           if(weekday(create_time) = 1,
                              '0.000005' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0284' + '11.915',
                              if(weekday(create_time) = 0,
                                 '0.000006' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0273' + '11.16',
                                 if(weekday(create_time) = 6, '-0.000003' * pow(
                                         count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                              '1.0543' - '1.7142',
                                    if(weekday(create_time) = 5, '-0.000005' * pow(
                                            count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                                 '1.0533' + '0.7048',
                                       if(weekday(create_time) = 4, '0.000005' * pow(
                                               count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                                    '1.0288' + '10.361',
                                          null)))))))) twenty_three,
               ROUND(if(weekday(create_time) = 3,
                        '0.000009' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0548' + '24.557',
                        if(weekday(create_time) = 2,
                           '0.000007' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 22, customer_id, null)) * ' 1.0656' + '12.467',
                           if(weekday(create_time) = 1,
                              '0.00002' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0415' + '25.384',
                              if(weekday(create_time) = 0,
                                 '0.000005' * pow(count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 22, customer_id, null)) * '1.0642' + '17.522',
                                 if(weekday(create_time) = 6, '-0.000007' * pow(
                                         count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                              '1.1073' - '7.075',
                                    if(weekday(create_time) = 5, '0.000004' * pow(
                                            count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                                 '1.0685' + '15.454',
                                       if(weekday(create_time) = 4, '0.000006' * pow(
                                               count(distinct if(HOUR(create_time) < 22, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 22, customer_id, null)) *
                                                                    '1.0635' + '18.234',
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>
    <select id="select23"
            resultType="map"><![CDATA[
        select ROUND(if(weekday(create_time) = 3,
                        '0.000003' * pow(count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                        count(distinct if(HOUR(create_time) < 23, customer_id, null)) * '1.0273' + '8.2629',
                        if(weekday(create_time) = 2,
                           '0.000003' * pow(count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                           count(distinct if(HOUR(create_time) < 23, customer_id, null)) * ' 1.0298' + '3.9487',
                           if(weekday(create_time) = 1,
                              '0.00001' * pow(count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                              count(distinct if(HOUR(create_time) < 23, customer_id, null)) * '1.0123' + '13.393',
                              if(weekday(create_time) = 0,
                                 '-0.000001' * pow(count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                                 count(distinct if(HOUR(create_time) < 23, customer_id, null)) * '1.0343' + '7.2416',
                                 if(weekday(create_time) = 6, '-0.000004' * pow(
                                         count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                                                              count(distinct if(HOUR(create_time) < 23, customer_id, null)) *
                                                              '1.0502' - '5.4441',
                                    if(weekday(create_time) = 5, '0.000009' * pow(
                                            count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                                                                 count(distinct if(HOUR(create_time) < 23, customer_id, null)) *
                                                                 '1.0144' + '14.472',
                                       if(weekday(create_time) = 4, '-0.0000002' * pow(
                                               count(distinct if(HOUR(create_time) < 23, customer_id, null)), 2) +
                                                                    count(distinct if(HOUR(create_time) < 23, customer_id, null)) *
                                                                    '1.0343' + '6.8245',
                                          null)))))))) twenty_four
        from rent_order ro
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate()
        ]]></select>

    <select id="selectRealCount"
            resultType="map"><![CDATA[
        select count(distinct if(HOUR(create_time) < 1, customer_id, null))  one,
               count(distinct if(HOUR(create_time) < 2, customer_id, null))  two,
               count(distinct if(HOUR(create_time) < 3, customer_id, null))  three,
               count(distinct if(HOUR(create_time) < 4, customer_id, null))  four,
               count(distinct if(HOUR(create_time) < 5, customer_id, null))  five,
               count(distinct if(HOUR(create_time) < 6, customer_id, null))  six,
               count(distinct if(HOUR(create_time) < 7, customer_id, null))  seven,
               count(distinct if(HOUR(create_time) < 8, customer_id, null))  eight,
               count(distinct if(HOUR(create_time) < 9, customer_id, null))  nine,
               count(distinct if(HOUR(create_time) < 10, customer_id, null)) ten,
               count(distinct if(HOUR(create_time) < 11, customer_id, null)) eleven,
               count(distinct if(HOUR(create_time) < 12, customer_id, null)) twelve,
               count(distinct if(HOUR(create_time) < 13, customer_id, null)) thirteen,
               count(distinct if(HOUR(create_time) < 14, customer_id, null)) fourteen,
               count(distinct if(HOUR(create_time) < 15, customer_id, null)) fifteen,
               count(distinct if(HOUR(create_time) < 16, customer_id, null)) sixteen,
               count(distinct if(HOUR(create_time) < 17, customer_id, null)) seventeen,
               count(distinct if(HOUR(create_time) < 18, customer_id, null)) eighteen,
               count(distinct if(HOUR(create_time) < 19, customer_id, null)) nineteen,
               count(distinct if(HOUR(create_time) < 20, customer_id, null)) twenty,
               count(distinct if(HOUR(create_time) < 21, customer_id, null)) twenty_one,
               count(distinct if(HOUR(create_time) < 22, customer_id, null)) twenty_two,
               count(distinct if(HOUR(create_time) < 23, customer_id, null)) twenty_three,
               count(distinct if(HOUR(create_time) < 24, customer_id, null)) twenty_four
        from rent_order
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate();
        ]]></select>


    <select id="selectOrderUvCount"
            resultType="map"><![CDATA[
        select count(distinct customer_id) orderUvCount
        from rent_order
        where is_deleted = 0
          and type = 1
          and merchant_id = 100
          and parent_id = 0
          and date(create_time) = curdate();
        ]]></select>

<!-- 通用模块 -->
    <select id="getAllGeneralData" resultType="qnvip.data.overview.domain.risk.GeneralRecord">
        SELECT
            DATE(ro.rent_start_date) count_day,
            rorp.order_id,
            ro.renew_time,
            rorp.stage_no,
            IFNULL(MIN(aa.hitValue), '') hit_value,
            ro.no order_no,
            ro.mini_type,
            DATE(repay_date) repay_date,
            real_repay_time real_repay_time,
            cc.zmf_level zmf_level,
            IFNULL(ff.is_rent, '非租物') is_rent,
            (
            CASE
            WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126)
            THEN '支付宝'
            WHEN ro.mini_type IN (6, 8, 10, 11)
            THEN '微信'
            WHEN ro.mini_type = 2
            THEN '字节跳动'
            WHEN ro.mini_type = 3
            THEN 'app'
            END
            ) platform,
            rate_config_type finance_type,
            rorp.term term,
            cl.riskOpinion risk_level,
            CASE
            WHEN rc.realDepositFreeAmount>=rc.realDepositAmount AND rc.realDepositFreeAmount >0 THEN '全额免押'
            WHEN rc.realDepositFreeAmount >0 AND rc.realDepositAmount >0 THEN '部分免押'
            WHEN rc.freezeAmount=realDepositAmount AND rc.freezeAmount >0  THEN '实付免押'
            ELSE '未免押'
        END deposit_free_type, --  免押类型
      CASE
    WHEN cl.quotientName LIKE '%租物渠道%' THEN '租物'
    WHEN bb.name LIKE '%W%' THEN 'CPS导流商'
    WHEN cl.quotientName  REGEXP '.*[0-9]{11}$' THEN 'CPS盟主'
    WHEN bb.name LIKE '%T%' THEN '商业化投放'
    WHEN cl.quotientName='' THEN '自然流量'
    WHEN cl.quotientName LIKE '%Y%' THEN '私域'
    WHEN cl.quotientName LIKE '%N%' THEN '内部运营'
    WHEN cl.quotientName LIKE '%Z%' OR cl.quotientName IN ('支付宝端活动', '支付宝订阅消息','芝麻租物-擦亮')  THEN '支付宝端内露出'
    ELSE '其他' END traffic_type,-- 流量类型
    CASE
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=6 THEN '6+6'
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=12 THEN '6+12'
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=18 THEN '6+18'
    WHEN c.renew_type=2 AND c.origin_term=3 AND c.origin_renew_term=9 THEN '3+9'
    WHEN c.renew_type IN (1,4) AND c.origin_term=9 AND c.origin_renew_term=3 THEN '9+3'
    WHEN c.renew_type IN (1,4) AND c.origin_term=12  THEN '12+n'
    WHEN c.renew_type=3 AND c.origin_term=6 AND c.origin_renew_term=0 THEN '6+0'
    WHEN c.renew_type=3 AND c.origin_term=12 AND c.origin_renew_term=0 THEN '12+0'
        END financial_solutions, -- 金融方案
      IF(
        cl.scene = '',
        '未标记上场景值',
        cl.scene
      ) scene,
      IF(
        cl.quotientname = '',
        '未标记上导流商',
        cl.quotientname
      ) quotient_name,
      IF(LOCATE('-V',SN.`riskStrategy`)>0,LEFT(SN.`riskStrategy`,LOCATE('-V',SN.`riskStrategy`)-1),SN.`riskStrategy`) risk_strategy, --  风控策略
      CONCAT(rmc.platform_code,'_',rmc.name) application_name, -- 应用名称
      CASE WHEN roi.equipment_state=1 THEN '新机'
           WHEN roi.equipment_state=0 THEN '未知'
           ELSE '二手机' END equipment_state, -- 新旧程度
      IF(cl.supervisoryMachine IN (1,2),'是','否') supervised_machine,-- 是否监管  supervisedMachine
      cl.shortName machine_type, -- 机型
      artificialAuditorId audit_type,
      cla.artificial_auditor_name audit_type_name,
      (
        CASE
          WHEN DATE_ADD(
            LAST_DAY(rorp.`repay_date`),
            INTERVAL 2 DAY
          ) > DATE(CURDATE())
          THEN (
            CASE
              WHEN YEAR(repay_date) = YEAR(CURDATE())
              AND MONTH(repay_date) > MONTH (CURDATE())
              THEN '0'
              WHEN term = 1
              THEN IF(
                DATE(repay_date) >= DATE(CURDATE()),
                '0',
                IF(
                  MIN(repay_status) = 1
                  AND MIN(overdue) = 5,
                  '1',
                  '0'
                )
              )
              ELSE IF(
                MIN(repay_status) = 1
                AND MIN(overdue) = 5,
                '1',
                IF(MIN(repay_status) = 5, '0', '2')
              )
            END
          )
          ELSE (
            CASE
              WHEN DATE(IFNULL(real_repay_time, CURDATE())) <![CDATA[<=]]> DATE_ADD(
                LAST_DAY(`repay_date`),
                INTERVAL 2 DAY
              )
              THEN IF(MIN(repay_status) = 5, '0', '1')
              ELSE IF(
                DATEDIFF(
                  DATE(IFNULL(real_repay_time, CURDATE())),
                  DATE_ADD(
                    LAST_DAY(`repay_date`),
                    INTERVAL 2 DAY
                  )
                ) > 3,
                '1',
                IF(
                  real_repay_time IS NOT NULL
                  AND MIN(repay_status) = 5,
                  '0',
                  '1'
                )
              )
            END
          )
        END
      ) is_overdue,
      IFNULL(
        IF(
          MIN(g.ext) != '',
          JSON_EXTRACT (MIN(g.ext), '$.discountReturnAmt'),
          '0'
        ),
        '0'
      ) discount_return_amt,
      IF(
        MIN(c.renew_type) != 2,
        MIN(c.actual_financing_amt),
        AVG(rorp.capital) * (
          IF(
            MIN(ro.renew_time) IS NULL
            AND (
              (
                c.renew_type IN (1, 4)
                AND c.origin_term = 12
                AND c.origin_renew_term = 6
              )
              OR (
                c.renew_type IN (1, 4)
                AND c.origin_term = 12
                AND c.origin_renew_term = 8
              )
            ),
            MIN(c.origin_term),
            MIN(c.origin_term) + MIN(c.origin_renew_term)
          )
        )
      ) rent_total,
      IF(
        MIN(c.renew_type) != 2,
        MAX(c.buyout_amt),
        0
      ) buyout_amt,
      IFNULL(MIN(rorp.discount_amt), 0) discount_amt,
      IF(
        c.rate_config_type != 10,
        MAX(h.user_actual_buyout_amt),
        0
      ) act_buyout_amt,
      IF(
        c.rate_config_type != 10,
        MAX(h.manage_buyout_discount) + MAX(h.offline_buyout_discount),
        0
      ) buyout_discount,
      IFNULL(MIN(rorp.overdue_fine), 0) overdue_fine,
      IFNULL(MIN(c.bond_amt), 0) bond_amt,
      IFNULL(MIN(x.before_discount), 0) before_discount,
      IFNULL(
        MIN(c.diff_pricing_discount_amt),
        0
      ) + IFNULL(MIN(c.coupon_discount_amt), 0) total_discount,
      IF(
        ext_json IS NOT NULL
        AND rate_config_type = 10,
        '1',
        '0'
      ) forced_conversion,
      ROUND(
        IFNULL(
          MIN(c.bond_amt) / (
            (
              IF(
                c.rate_config_type != 10,
                MIN(c.actual_financing_amt),
                AVG(rorp.capital) * (
                  MIN(c.origin_term) + MIN(c.origin_renew_term)
                )
              ) + IF(
                c.rate_config_type != 10,
                MIN(c.buyout_amt),
                0
              )
            ) - IFNULL(MIN(x.total_discount), 0)
          ),
          0
        ),
        2
      ) bond_rate,
      (
        CASE
          WHEN MIN(repay_status) = 5
          THEN AVG(rorp.capital)
          WHEN MIN(repay_status) = 1
          THEN AVG(capital) - (
            AVG(real_repay_capital) - AVG(discount_amt)
          )
        END
      ) real_capital,
      AVG(rorp.capital) capital,
      MIN(rorp.is_deleted) deleted,
      MIN(repay_status) repay_status,
      MIN(overdue) overdue,
      IF(real_repay_time IS NULL, 1, 0) real_repay_time_status,
      MIN(c.renew_total_rent) renew_total_rent,
      MIN(
        IF(
          c.discount_pricing_mode IS NULL,
          0,
          c.discount_pricing_mode
        )
      ) discount_pricing_mode,
      MAX(repayment_term) max_term,
      MIN(renew_term) renew_term,
        IF(MIN(ro.status) = 15
            AND DATEDIFF(DATE(CURDATE()), DATE(MIN(ro.rent_end_date))) > 7,
            1,
            0) renew_status,
      DATEDIFF(
        DATE(CURDATE()),
        DATE(MIN(ro.rent_end_date))
      ) renew_day,
      IFNULL(MIN(c.bond_rest_fund_amount), 0) bond_rest_fund_amount,
      IFNULL(
        MIN(c.diff_pricing_discount_amt),
        0
      ) diff_pricing_discount_amt,
      IFNULL(MIN(c.coupon_discount_amt), 0) coupon_discount_amt,
      IFNULL(MIN(c.surplus_bond_amt), 0) surplus_bond_amt,
      MIN(roi.actual_supply_price) actual_supply_price,
      MIN(roos.current_overdue_days) current_overdue_days,
      MIN(rorp.overdue_days) term_overdue_days,
      IF(MIN(ro.settle_date) IS NOT NULL, 1, 0) is_settle,
      MIN(ro.status) order_status,
      SUM(
        IF(
          repay_status = 1,
          IF(
            real_repay_time IS NOT NULL,
            real_repay_capital,
            capital
          ),
          0
        )
      ) surplus_amt,
      SUM(
        IF(
          repay_status = 5,
          real_repay_capital,
          0
        )
      ) already_pay,
      IF(
        MIN(y.id) IS NOT NULL
        AND MIN(k.lawsuit_status) NOT IN (2, 5, 9),
        1,
        0
      ) renew_type,
      IFNULL(d.buy_out_capital, 0) buy_out_capital,
      IFNULL(d.buy_out_real_repay_capital, 0) buy_out_real_repay_capital
    FROM
      `qnvip_rent`.rent_order ro
      INNER JOIN `qnvip_rent`.rent_order_repayment_plan rorp
        ON ro.id = rorp.order_id
        AND rorp.is_deleted = 0
        AND rorp.is_deleted = 0
      -- AND ro.rent_start_date>='2023-08-01' AND ro.rent_start_date <![CDATA[<=]]> '2024-01-31'
       -- AND ro.no IN ('23091511433853685765')
      --  AND ro.no IN ('23120813582648182756','23011916472158007057','23092509172572216435','21052009174381530069','21051911554848791270')
      INNER JOIN `qnvip_rent`.rent_order_item roi
        ON ro.id = roi.order_id
        AND roi.is_deleted = 0 AND roi.item_type=1
      LEFT JOIN qnvip_rent.rent_mini_config rmc
      ON ro.mini_type = rmc.mini_type
      INNER JOIN `qnvip_rent`.rent_order_overdue_stat roos
        ON ro.id = roos.order_id
        AND roos.is_deleted = 0
      LEFT JOIN `qnvip_rent`.rent_order_source b ON ro.id=b.order_id --
      LEFT JOIN `qnvip_rent`.rent_diversion_quotient bb ON b.quotient_id=bb.id --
      LEFT JOIN
        (SELECT
          id,
          order_id,
          capital AS buy_out_capital,
          real_repay_capital AS buy_out_real_repay_capital
        FROM
          qnvip_rent.rent_order_buyout_detail
        WHERE is_deleted = 0) d
        ON ro.id = d.`order_id`
      LEFT JOIN
        (SELECT
          MIN(ext) ext,
          order_id
        FROM
          `qnvip_rent`.rent_order_flow b
        WHERE b.biz_type = 3
          AND b.is_deleted = 0
          AND b.pay_status = 10
          AND (
            b.mark_refund IS NULL
            OR b.mark_refund = 0
          )
          AND b.flow_type = 1
          AND b.refunded_amt = 0
          AND b.ext IS NOT NULL
          AND b.ext != ''
        GROUP BY order_id) g
        ON ro.id = g.order_id
      INNER JOIN `qnvip_rent`.rent_order_finance_detail c
        ON ro.id = c.order_id
        AND c.is_deleted = 0
      LEFT JOIN
        (SELECT
          order_id,
          SUM(manage_write_off_amt) manage_write_off_amt,
          SUM(before_write_off_amt) before_discount,
          MIN(total_before_write_off_amt) total_discount
        FROM
          (SELECT
            x.order_id,
            IF(
              ISNULL(MIN(x.bind_order_time))
              OR MIN(y.payment_time) <![CDATA[<]]> MIN(x.bind_order_time),
              IF(
                x.type IN (1, 3),
                IF(
                  MIN(x.use_status) = 1,
                  MAX(x.write_off_amt),
                  0
                ),
                0
              ) + IF(
                x.type IN (2, 4),
                SUM(
                  IF(
                    x.use_status = 1,
                    x.write_off_amt,
                    0
                  )
                ),
                0
              ),
              0
            ) manage_write_off_amt,
            IF(
              ! ISNULL(MIN(x.bind_order_time))
              AND MIN(y.payment_time) >= MIN(x.bind_order_time),
              IF(
                x.type IN (1, 3),
                IF(
                  MIN(x.use_status) = 1,
                  MAX(x.write_off_amt),
                  0
                ),
                0
              ) + IF(
                x.type IN (2, 4),
                SUM(
                  IF(
                    x.use_status = 1,
                    x.write_off_amt,
                    0
                  )
                ),
                0
              ),
              0
            ) before_write_off_amt,
            MIN(z.total_before_write_off_amt) total_before_write_off_amt
          FROM
            `qnvip_rent`.rent_customer_coupon X
            INNER JOIN `qnvip_rent`.rent_order Y
              ON x.order_id = y.id
            INNER JOIN
              (SELECT
                x.order_id,
                SUM(write_off_amt) total_before_write_off_amt
              FROM
                (SELECT
                  order_id,
                  IFNULL(x.write_off_amt, 0) write_off_amt,
                  IF(
                    x.type IN (2, 4),
                    1,
                    row_number () over (
                      PARTITION BY x.order_id,
                      x.use_term
                      ORDER BY IFNULL(x.write_off_amt, 0) DESC
                    )
                  ) rowid
                FROM
                  `qnvip_rent`.rent_customer_coupon X
                  INNER JOIN `qnvip_rent`.rent_order Y
                    ON x.order_id = y.id
                WHERE x.order_id > 0
                  AND x.scene = 1
                  AND x.is_deleted = 0
                  AND x.bind_order_time IS NOT NULL
                  AND y.payment_time >= x.bind_order_time
                  AND y.is_deleted = 0) X
              WHERE x.rowid = 1
              GROUP BY x.order_id) z
              ON z.order_id = y.id
          WHERE x.order_id > 0
            AND x.scene = 1
            AND x.is_deleted = 0
            AND y.is_deleted = 0
          GROUP BY x.order_id,
            x.type) a
        GROUP BY order_id) X
        ON ro.id = x.order_id
      INNER JOIN alchemist.cl_loan cl
        ON cl.loanno = ro.no
      INNER JOIN alchemist.rc_assess_record rc
      ON cl.id = rc.loanId
      INNER JOIN alchemist.serial_no sn
        ON sn.businessno = ro.no
      LEFT JOIN alchemist.cl_loan_artificial_info cla ON ro.no = cla.loan_no
      LEFT JOIN `qnvip_rent`.rent_order_infomore k
        ON ro.id = k.order_id
        AND k.is_deleted = 0
      LEFT JOIN
        (SELECT
          order_id,
          SUM(user_actual_buyout_amt) user_actual_buyout_amt,
          SUM(buyout_coupon_amount) manage_buyout_discount,
          SUM(buyout_coupon_offline_amount) offline_buyout_discount
        FROM
          `qnvip_rent`.rent_order_account_check
        WHERE is_deleted = 0
        GROUP BY order_id) h
        ON ro.id = h.order_id
      LEFT JOIN
        (SELECT
          id,
          parent_id,
          id AS n_id
        FROM
          `qnvip_rent`.rent_order
        WHERE is_deleted = 0) Y
        ON ro.id = y.parent_id
      INNER JOIN
        (SELECT
          ro.id
        FROM
          `qnvip_rent`.rent_order ro
          INNER JOIN `qnvip_rent`.rent_order_logistics rol
            ON ro.id = rol.order_id
        WHERE ro.merchant_id IN (100, ********)
          AND rol.sign_time IS NOT NULL
          AND ro.parent_id = 0
          AND ro.termination != 5
          AND ro.is_deleted = 0
          AND ro.type = 1
          AND DATE(ro.rent_start_date) >= DATE '2020-10-01'
        ORDER BY ro.id LIMIT #{startPage},#{pageSize}) z
        ON ro.id = z.id
        LEFT JOIN
        (SELECT
        rc.hitValue,
        sn.businessNo
        FROM
        `alchemist`.serial_no sn
        INNER JOIN `alchemist`.rc_risk_access_rule_result_2023 rc
        ON sn.id = rc.serialnoId
        INNER JOIN `alchemist`.rc_risk_strategy_rule_set rr
        ON rr.id = rc.ruleId
        WHERE rr.scene = 3
        AND rc.hitValue != ''
        AND rr.masterModel = 1
        AND sn.riskStrategy = 'qnvip_d') aa
        ON ro.no = aa.businessNo
        LEFT JOIN
        (SELECT
        order_id,
        (
        CASE
        WHEN freeze_amt = 0
        AND deposit_free_amt > 0
        THEN '750分+'
        WHEN freeze_amt = 1
        AND deposit_free_amt > 0
        THEN '700-750分'
        WHEN freeze_amt = 2
        AND deposit_free_amt > 0
        THEN '650-700分'
        WHEN freeze_amt = 3
        AND deposit_free_amt > 0
        THEN '600-650分'
        WHEN freeze_amt > 3
        AND deposit_free_amt > 0
        THEN '600分以下'
        WHEN freeze_amt = 0
        AND deposit_free_amt = 0
        THEN '非免押用户'
        END
        ) AS zmf_level
        FROM
        (SELECT
        order_id,
        IF(
        finance.bond_free_status = 0,
        0,
        finance.frozen_amt - finance.bond_free_credit_amt
        ) freeze_amt,
        IF(
        finance.bond_free_status = 0,
        0,
        finance.bond_free_credit_amt
        ) AS deposit_free_amt
        FROM
        `qnvip_rent`.rent_order_finance_detail finance
        WHERE is_deleted = 0) a) cc
        ON cc.order_id = ro.id
        LEFT JOIN
        (SELECT
        TYPE,
        IF(
        remark LIKE '%租物%',
        '租物',
        '非租物'
        ) is_rent
        FROM
        `qnvip_rent`.rent_finance_template
        WHERE STATUS = 1
        AND is_deleted = 0) ff
        ON ff.type = c.rate_config_type
        GROUP BY DATE(ro.rent_start_date),
        ro.mini_type,
        rorp.order_id,
        ro.no,
        cl.scene,
        cl.quotientname,
        rate_config_type,
        IF(
        ext_json IS NOT NULL
        AND rate_config_type = 10,
        '1',
        '0'
        ),
        rorp.term,
        cl.riskOpinion,
        IF(LOCATE('-V',SN.`riskStrategy`)>0,LEFT(SN.`riskStrategy`,LOCATE('-V',SN.`riskStrategy`)-1),SN.`riskStrategy`),
        cl.artificialAuditorId,
        repay_date,
        is_rent,
        zmf_level,
        CASE
        WHEN rc.realDepositFreeAmount>=rc.realDepositAmount AND rc.realDepositFreeAmount >0 THEN '全额免押'
        WHEN rc.realDepositFreeAmount >0 AND rc.realDepositAmount >0 THEN '部分免押'
        WHEN rc.freezeAmount=rc.realDepositAmount AND rc.freezeAmount >0  THEN '实付免押'
        ELSE '未免押'
        END,
      CASE
    WHEN cl.quotientName LIKE '%租物渠道%' THEN '租物'
    WHEN bb.name LIKE '%W%' THEN 'CPS导流商'
    WHEN cl.quotientName  REGEXP '.*[0-9]{11}$' THEN 'CPS盟主'
    WHEN bb.name LIKE '%T%' THEN '商业化投放'
    WHEN cl.quotientName='' THEN '自然流量'
    WHEN cl.quotientName LIKE '%Y%' THEN '私域'
    WHEN cl.quotientName LIKE '%N%' THEN '内部运营'
    WHEN cl.quotientName LIKE '%Z%' OR cl.quotientName IN ('支付宝端活动', '支付宝订阅消息','芝麻租物-擦亮')  THEN '支付宝端内露出'
    ELSE '其他' END ,
    CASE
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=6 THEN '6+6'
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=12 THEN '6+12'
    WHEN c.renew_type=2 AND c.origin_term=6 AND c.origin_renew_term=18 THEN '6+18'
    WHEN c.renew_type=2 AND c.origin_term=3 AND c.origin_renew_term=9 THEN '3+9'
    WHEN c.renew_type IN (1,4) AND c.origin_term=9 AND c.origin_renew_term=3 THEN '9+3'
    WHEN c.renew_type IN (1,4) AND c.origin_term=12  THEN '12+n'
    WHEN c.renew_type=3 AND c.origin_term=6 AND c.origin_renew_term=0 THEN '6+0'
    WHEN c.renew_type=3 AND c.origin_term=12 AND c.origin_renew_term=0 THEN '12+0'
        END,
    CASE WHEN roi.equipment_state=1 THEN '新机'
           WHEN roi.equipment_state=0 THEN '未知'
           ELSE '二手机' END,
      CONCAT(rmc.platform_code,'_',rmc.name),
      cl.supervisoryMachine,
      cl.shortName,
      real_repay_time,
      (
        c.bond_amt / (
          IF(
            c.rate_config_type != 10,
            c.actual_financing_amt,
            rorp.capital * (
              IF(
                ro.renew_time IS NULL
                AND (
                  (
                    c.renew_type IN (1, 4)
                    AND c.origin_term = 12
                    AND c.origin_renew_term = 6
                  )
                  OR (
                    c.renew_type IN (1, 4)
                    AND c.origin_term = 12
                    AND c.origin_renew_term = 8
                  )
                ),
                c.origin_term,
                c.origin_term + c.origin_renew_term
              )
            )
          ) + IF(
            c.rate_config_type != 10,
            c.buyout_amt,
            0
          )
        ) - IFNULL(c.diff_pricing_discount_amt, 0) - IFNULL(c.coupon_discount_amt, 0)
      )
    ORDER BY DATE(ro.rent_start_date)
    </select>

    <select id="getGeneralCount" resultType="java.lang.Integer">
        SELECT COUNT(*) FROM rent_order ro INNER JOIN rent_order_logistics rol ON ro.id = rol.order_id  WHERE
            rol.sign_time IS NOT NULL
            AND ro.merchant_id IN (100,********)
            AND ro.termination != 5
            AND ro.biz_type = 2
            AND ro.type = 1
            AND ro.parent_id = 0
            AND DATE(ro.rent_start_date) >= '2020-10-01'
    </select>

<!--  通用模块的续租订单 -->
    <select id="getRenewCountTidb" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM qnvip_rent.rent_order
        WHERE merchant_id IN (100,********)
          AND termination != 5
        AND biz_type = 2
        AND TYPE = 1
        AND parent_id > 0
        AND DATE(rent_start_date) >= '2020-10-01'
    </select>
    <select id="getAllGeneralRenewData" resultType="qnvip.data.overview.domain.risk.GeneralRenewRecord">
        <![CDATA[
        SELECT
            DATE(z.rent_start_date) rent_start_date,
            rorp.order_id,
            z.no order_no,
            ro.mini_type,
            cl.scene scene,
            cl.quotientname quotient_name,
            DATE(repay_date) repay_date,
            real_repay_time,
            IFNULL(auto_renewal, 1) auto_renewal,
            IF(settle_date IS NOT NULL, '1', '0') is_settle,
            DATE(settle_date) settle_date,
            (
            CASE
            WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126)
            THEN '支付宝'
            WHEN ro.mini_type IN (6, 8, 10, 11)
            THEN '微信'
            WHEN ro.mini_type = 2
            THEN '字节跳动'
            WHEN ro.mini_type = 3
            THEN 'app'
            END
            ) platform,
            rate_config_type finance_type,
            rorp.term term,
            cl.riskOpinion risk_level,
            sn.riskStrategy risk_strategy,
            IF(
            artificialAuditorId = 1,
            '自动风控',
            '人工审核'
            ) audit_type,
            IF(
            ext_json IS NOT NULL
            AND rate_config_type = 10,
            '1',
            '0'
            ) forced_conversion,
            (
            CASE
            WHEN DATE_ADD(
            LAST_DAY(rorp.`repay_date`),
            INTERVAL 2 DAY
            ) > DATE(CURDATE())
            THEN (
            CASE
            WHEN YEAR(repay_date) = YEAR(CURDATE())
            AND MONTH(repay_date) > MONTH (CURDATE())
            THEN '0'
            WHEN term = 1
            THEN IF(
            DATE(repay_date) >= DATE(CURDATE()),
            '0',
            IF(
            MIN(repay_status) = 1
            AND MIN(overdue) = 5,
            '1',
            '0'
            )
            )
            ELSE IF(
            MIN(repay_status) = 1
            AND MIN(overdue) = 5,
            '1',
            IF(MIN(repay_status) = 5, '0', '2')
            )
            END
            )
            ELSE (
            CASE
            WHEN DATE(IFNULL(real_repay_time, CURDATE())) <= DATE_ADD(
            LAST_DAY(`repay_date`),
            INTERVAL 2 DAY
            )
            THEN IF(MIN(repay_status) = 5, '0', '1')
            ELSE IF(
            DATEDIFF(
            DATE(IFNULL(real_repay_time, CURDATE())),
            DATE_ADD(
            LAST_DAY(`repay_date`),
            INTERVAL 2 DAY
            )
            ) > #{overdueDay},
            '1',
            IF(
            real_repay_time IS NOT NULL
            AND MIN(repay_status) = 5,
            '0',
            '1'
            )
            )
            END
            )
            END
            ) is_overdue,
            IFNULL(
            IF(
            MIN(g.ext) != '',
            JSON_EXTRACT (MIN(g.ext), '$.discountReturnAmt'),
            '0'
            ),
            '0'
            ) discount_return_amt,
            IF(
            MIN(c.renew_type) != 2,
            MIN(c.actual_financing_amt),
            AVG(rorp.capital) * 12
            ) rent_total,
            IFNULL(MIN(rorp.overdue_fine), 0) overdue_fine,
            IFNULL(MIN(c.act_bond_amt), 0) bond_amt,
            IFNULL(MIN(x.before_discount), 0) before_discount,
            IFNULL(MIN(rorpp.bond_rate), 0) bond_rate,
            (
            CASE
            WHEN MIN(repay_status) = 5
            THEN AVG(rorp.capital)
            WHEN MIN(repay_status) = 1
            THEN (
            AVG(capital) - (
            AVG(real_repay_capital) - AVG(discount_amt)
            )
            )
            END
            ) real_capital,
            AVG(rorp.capital) capital,
            MIN(rorp.is_deleted) deleted,
            MIN(repay_status) repay_status,
            MIN(overdue) overdue,
            IF(real_repay_time IS NULL, 1, 0) real_repay_time_status,
            MIN(z.parent_id) parent_id,
            MIN(z.status) order_status,
            MIN(roos.current_overdue_days) current_overdue_days,
            MIN(rorp.overdue_days) term_overdue_days,
            SUM(
            IF(
            repay_status = 5,
            real_repay_capital,
            0
            )
            ) already_pay,
            SUM(
            IF(
            repay_status = 1,
            IF(
            real_repay_time IS NOT NULL,
            real_repay_capital,
            capital
            ),
            0
            )
            ) surplus_amt,
            IFNULL(d.buy_out_capital, 0) buy_out_capital,
            IFNULL(d.buy_out_real_repay_capital, 0) buy_out_real_repay_capital
        FROM
            (SELECT
            id,
            parent_id,
            NO,
            rent_start_date,
            STATUS
            FROM
            `qnvip_rent`.rent_order ro
            WHERE merchant_id IN (100, ********)
            AND ro.parent_id > 0
            AND biz_type = 2
            AND termination != 5
            AND is_deleted = 0
            AND ro.type = 1
            AND DATE(rent_start_date) >= DATE '2020-10-01'
            ORDER BY id
            LIMIT #{startPage}, ${pageSize}) z
            INNER JOIN `qnvip_rent`.rent_order ro
        ON ro.id = z.parent_id
            INNER JOIN `qnvip_rent`.rent_order_repayment_plan rorp
            ON z.id = rorp.order_id
            INNER JOIN `qnvip_rent`.rent_order_infomore roi
            ON roi.order_id = z.id
            INNER JOIN `qnvip_rent`.rent_order_overdue_stat roos
            ON z.id = roos.order_id
            LEFT JOIN
            (SELECT
            id,
            order_id,
            capital AS buy_out_capital,
            real_repay_capital AS buy_out_real_repay_capital
            FROM
            qnvip_rent.rent_order_buyout_detail
            WHERE is_deleted = 0) d
            ON z.id = d.`order_id`
            LEFT JOIN
            (SELECT
            MIN(ext) ext,
            order_id
            FROM
            `qnvip_rent`.rent_order_flow b
            WHERE b.biz_type = 3
            AND b.is_deleted = 0
            AND b.pay_status = 10
            AND (
            b.mark_refund IS NULL
            OR b.mark_refund = 0
            )
            AND b.flow_type = 1
            AND b.refunded_amt = 0
            AND b.ext IS NOT NULL
            AND b.ext != ''
            GROUP BY order_id) g
            ON z.id = g.order_id
            INNER JOIN `qnvip_rent`.rent_order_finance_detail c
            ON z.id = c.order_id
            INNER JOIN
            (SELECT
            IFNULL(
            MIN(b.bond_amt) / (
            IF(
            MIN(b.rate_config_type) != 10,
            MIN(b.actual_financing_amt),
            AVG(capital) * 12
            ) + IF(
            MIN(b.rate_config_type != 10),
            MIN(b.buyout_amt),
            0
            ) - MIN(xx.total_discount)
            ),
            0
            ) bond_rate,
            a.order_id
            FROM
            `qnvip_rent`.rent_order_repayment_plan a
            INNER JOIN `qnvip_rent`.rent_order_finance_detail b
            ON a.order_id = b.order_id
            LEFT JOIN
            (SELECT
            order_id,
            IF(MIN(isbefore) = 1, MIN(amt1) + MIN(amt2), 0) total_discount,
            IF(
            MIN(isbefore) = 1,
            MIN(yuji_amt1) + MIN(yuji_amt2),
            0
            ) before_discount
            FROM
            (SELECT
            x.order_id,
            IF(
            MIN(x.bind_order_time) IS NOT NULL
            AND MIN(y.payment_time) >= MIN(x.bind_order_time),
            1,
            0
            ) isbefore,
            IF(
            x.type IN (1, 3),
            MAX(x.write_off_amt),
            0
            ) amt1,
            IF(
            x.type IN (2, 4),
            SUM(x.write_off_amt),
            0
            ) amt2,
            IF(
            x.type IN (1, 3)
            AND MIN(x.use_status = 2),
            MAX(x.write_off_amt),
            0
            ) yuji_amt1,
            IF(
            x.type IN (2, 4)
            AND MIN(x.use_status = 2),
            SUM(x.write_off_amt),
            0
            ) yuji_amt2
            FROM
            `qnvip_rent`.rent_customer_coupon X
            INNER JOIN qnvip_rent.rent_order Y
            ON x.order_id = y.id
            WHERE x.order_id > 0
            AND x.scene = 1
            AND x.is_deleted = 0
            AND y.is_deleted = 0
            GROUP BY x.order_id,
            x.type) d
            GROUP BY order_id) xx
            ON b.order_id = xx.order_id
            GROUP BY a.order_id) rorpp
            ON z.parent_id = rorpp.order_id
            LEFT JOIN
            (SELECT
            order_id,
            IF(MIN(isbefore) = 1, MIN(amt1) + MIN(amt2), 0) total_discount,
            IF(
            MIN(isbefore) = 1,
            MIN(yuji_amt1) + MIN(yuji_amt2),
            0
            ) before_discount
            FROM
            (SELECT
            x.order_id,
            IF(
            MIN(x.bind_order_time) IS NOT NULL
            AND MIN(y.payment_time) >= MIN(x.bind_order_time),
            1,
            0
            ) isbefore,
            IF(
            x.type IN (1, 3),
            MAX(x.write_off_amt),
            0
            ) amt1,
            IF(
            x.type IN (2, 4),
            SUM(x.write_off_amt),
            0
            ) amt2,
            IF(
            x.type IN (1, 3)
            AND MIN(x.use_status = 2),
            MAX(X.write_off_amt),
            0
            ) yuji_amt1,
            IF(
            x.type IN (2, 4)
            AND MIN(x.use_status = 2),
            SUM(X.write_off_amt),
            0
            ) yuji_amt2
            FROM
            `qnvip_rent`.rent_customer_coupon X
            INNER JOIN `qnvip_rent`.rent_order Y
            ON x.order_id = y.id
            WHERE x.order_id > 0
            AND x.scene = 1
            AND x.is_deleted = 0
            AND y.is_deleted = 0
            GROUP BY x.order_id,
            x.type) a
            GROUP BY order_id) X
            ON z.id = x.order_id
            INNER JOIN `alchemist`.cl_loan cl
            ON cl.loanno = ro.no
            INNER JOIN `alchemist`.serial_no sn
            ON sn.businessno = ro.no
        WHERE rorp.is_deleted = 0
        GROUP BY DATE(z.rent_start_date),
            ro.mini_type,
            rorp.order_id,
            rate_config_type,
            IF(
            ext_json IS NOT NULL
            AND rate_config_type = 10,
            '1',
            '0'
            ),
            rorp.term,
            cl.riskOpinion,
            sn.riskStrategy,
            auto_renewal,
            settle_date,
            cl.artificialAuditorId,
            z.no,
            repay_date,
            cl.scene,
            cl.quotientname,
            real_repay_time,
            (
            c.bond_amt / (
            IF(
            c.rate_config_type != 10,
            c.actual_financing_amt,
            rorp.capital * 12
            ) + IF(
            c.rate_config_type != 10,
            c.buyout_amt,
            0
            )
            ) - IFNULL(c.diff_pricing_discount_amt, 0) - IFNULL(c.coupon_discount_amt, 0)
            )
        ORDER BY DATE(z.rent_start_date)
        ]]>
    </select>
    <select id="getShCountTidb" resultType="java.lang.Integer">
        SELECT
        COUNT(1)
        FROM `qnvip_merchant`.sh_order so
        WHERE
            so.is_deleted = 0
          AND so.status in (90,120)
          AND so.test_role=0
          AND so.closing_time IS  NULL
    </select>
    <select id="getShAllData" resultType="qnvip.data.overview.domain.risk.ShRecordData">
        <![CDATA[
        SELECT
            DATE(rent_start_time) rent_start_date,
            so.order_uid NO,
          CASE WHEN sn.riskStrategy  LIKE '%高定价%' THEN '10'
          ELSE '0' END biz_type,
            so.id order_id,
            CASE WHEN sn.riskStrategy  LIKE '%分期渠道-贷中%' THEN '贷中'
            WHEN  sn.riskStrategy  LIKE '%分期渠道-V%' THEN '贷前'
            ELSE '其他' END risk_strategy, -- 风控策略
               CASE
                WHEN cl.firstCategoryName='二手手机' THEN '二手手机'
                WHEN cl.firstCategoryName IN ('爆款手机', '全新手机') THEN '爆款或全新手机'
                WHEN cl.firstCategoryName LIKE '%黄金%' THEN '黄金珠宝'
                WHEN cl.firstCategoryName IN ('数码','3C数码','数码产品','酒饮酒品','名品腕表') THEN '类套现品'
                ELSE '其他' END product_type, -- 商品类型
               CASE WHEN  (cl.firstCategoryName LIKE '%黄金%' AND cl.secondCategoryName LIKE '%投资%')
            OR  cl.secondCategoryName LIKE '%金砖%'
            OR cl.secondCategoryName LIKE '%攒黄金%'
            THEN '黄金_投资类'
            WHEN  cl.firstCategoryName LIKE '%黄金%'
            THEN '黄金_非投资类' ELSE '其他' END glod_type,
            CONCAT(rmc.platform_code,'_',rmc.name) application_name,
            IF(cl.supervisoryMachine IN (1,2),'是','否') supervised_machine,
--             artificialAuditorId audit_type,
            IFNULL(s_c.name, '') first_category,
            IFNULL(s_c_new.name, '') second_category,
--             IF(
--             gg.paytime IS NULL
--             OR DATE(so.create_time) <= gg.paytime,
--             0,
--             1
--             ) customer_type,
            (
            CASE
            WHEN rmc.platform_code = 'ALIPAY'
            THEN '支付宝'
            WHEN rmc.platform_code = 'WECHAT'
            THEN '微信'
            WHEN rmc.platform_code = 'BYTEDANCE'
            THEN '字节跳动'
            WHEN rmc.platform_code = 'APP'
            THEN 'APP'
            WHEN rmc.platform_code = 'KUAISHOU'
            THEN '快手'
            ELSE so.mini_type
            END
            ) platform,
            (
            CASE
            WHEN DATE_ADD(
            LAST_DAY(normal_repay_date),
            INTERVAL 2 DAY
            ) > DATE(CURDATE())
            THEN (
            CASE
            WHEN YEAR(normal_repay_date) > YEAR(CURDATE())
            THEN '0'
            WHEN YEAR(normal_repay_date) = YEAR(CURDATE())
            AND MONTH(normal_repay_date) > MONTH(CURDATE())
            THEN '0'
            WHEN period_no = 1
            THEN IF(
            DATE(normal_repay_date) >= DATE(CURDATE()),
            '0',
            IF(
            srp.status <> 10
            AND overdue_day_count > 0,
            '1',
            '0'
            )
            )
            ELSE IF(
            srp.status <> 10
            AND overdue_day_count > 0,
            '1',
            IF(srp.status = 10, '0', '2')
            )
            END
            )
            ELSE (
            CASE
            WHEN DATE(IFNULL(real_repay_date, CURDATE())) <= DATE_ADD(
            LAST_DAY(normal_repay_date),
            INTERVAL 2 DAY
            )
            THEN IF(srp.status = 10, '0', '1')
            ELSE IF(
            DATEDIFF(
            DATE(IFNULL(real_repay_date, CURDATE())),
            DATE_ADD(
            LAST_DAY(normal_repay_date),
            INTERVAL 2 DAY
            )
            ) > 3,
            '1',
            IF(
            real_repay_date IS NOT NULL
            AND srp.status = 10,
            '0',
            '1'
            )
            )
            END
            )
            END
            ) is_overdue,
            total_rent_amount rent_total,
            real_repay_date real_repay_time,
            DATE(normal_repay_date) repay_date,
            period_no term,
            payable_amount capital,
            real_repay_amount real_capital,
            srp.status repay_status,
            so.mini_type,
            repayment_period_count max_term,
            overdue_retention_fine overdue_fine,
            sof.coupon_discount_money + reduce_overdue_fine discount_amt,
            total_cash_deposit bond_amt,
            IFNULL(aa.hitValue, '') hit_value,
            srp.is_deleted,
            overdue_day_count overdue
        FROM
            `qnvip_merchant`.sh_order so
            INNER JOIN `qnvip_merchant`.sh_order_finance sof
        ON so.order_uid = sof.order_uid
            AND sof.is_deleted = 0
            INNER JOIN `qnvip_merchant`.sh_repayment_plan srp
            ON so.order_uid = srp.order_uid
            AND srp.is_deleted = 0
            INNER JOIN `qnvip_merchant`.sh_customer sc
            ON so.customer_id = sc.id
            AND sc.is_deleted = 0
            INNER JOIN `alchemist`.cl_loan cl
            ON cl.loanno = so.order_uid
            INNER JOIN `alchemist`.serial_no sn
            ON sn.businessno = so.order_uid
            INNER JOIN `qnvip_merchant`.sh_mini_config rmc
            ON so.mini_type = rmc.mini_type
            AND rmc.is_deleted = 0
            INNER JOIN `qnvip_merchant`.sh_order_item soi
            ON so.order_uid = soi.order_uid
            AND soi.is_deleted = 0
            LEFT JOIN `qnvip_merchant`.sh_item si
            ON si.item_uid = soi.item_uid
            AND si.is_deleted = 0
            LEFT JOIN `qnvip_merchant`.sh_category s_c
            ON si.first_category = s_c.id
            AND s_c.is_deleted = 0
            LEFT JOIN `qnvip_merchant`.sh_category s_c_new
            ON si.second_category = s_c_new.id
            AND s_c_new.is_deleted = 0
            LEFT JOIN
            (SELECT
            rc.hitValue,
            sn.businessNo
            FROM
            `alchemist`.serial_no sn
            INNER JOIN `alchemist`.rc_risk_access_rule_result_2023 rc
            ON sn.id = rc.serialnoId
            INNER JOIN `alchemist`.rc_risk_strategy_rule_set rr
            ON rr.id = rc.ruleId
            WHERE rr.scene = 3
            AND rc.hitValue != ''
            AND rr.masterModel = 1) aa
            ON so.order_uid = aa.businessNo
--             LEFT JOIN
--             (SELECT
--             so.customer_id,
--             MIN(STR_TO_DATE(paytime, '%Y-%m-%d')) paytime
--             FROM
--             `qnvip_merchant`.sh_order so
--             LEFT JOIN `qnvip_merchant`.sh_customer sc
--             ON so.customer_id = sc.id
--             LEFT JOIN `alchemist`.rc_user ru
--             ON sc.id_card_no = ru.idcardno
--             LEFT JOIN `alchemist`.cl_loan cl
--             ON cl.customerid = ru.id
--             GROUP BY so.customer_id) gg
--             ON gg.customer_id = so.customer_id
            INNER JOIN
            (SELECT
            so.order_uid
            FROM
            `qnvip_merchant`.sh_order so
           where
             so.status in (90,120)
            AND so.is_deleted = 0
            AND so.closing_time IS NULL
             ORDER BY order_uid
            LIMIT #{startPage}, #{pageSize}) cc
            ON cc.order_uid = so.order_uid
        ORDER BY so.order_uid,
            period_no
        ]]>
    </select>
    <select id="getGeneralCommonSql" resultType="java.util.HashMap">
        ${sql}
    </select>
    <select id="getShOrderInfo" resultType="qnvip.data.overview.domain.risk.ShOrderInfo">
        ${sql}
    </select>
    <select id="getNewRentOrderInfo" resultType="qnvip.data.overview.domain.risk.RiskNewRentOrderInfoVo">
        ${sql}
    </select>
    <select id="getRiskRentInfo" resultType="qnvip.data.overview.domain.risk.RiskRentInfoVo">
        ${sql}
    </select>
    <select id="getRenewCountDistributionTidb" resultType="java.lang.Integer">
        SELECT COUNT(*)
        FROM qnvip_rent.rent_order
        WHERE
           id >  12908556
          AND  merchant_id not IN (100,********)
          AND termination != 5
        AND biz_type = 2
        AND TYPE = 1
        AND parent_id > 0
    </select>

    <select id="getAllDistributionRenewData" resultType="qnvip.data.overview.domain.risk.GeneralRenewRecord">
        <![CDATA[
        SELECT
            DATE(z.rent_start_date) rent_start_date,
            rorp.order_id,
            z.no order_no,
            ro.mini_type,
            cl.scene scene,
            cl.quotientname quotient_name,
            DATE(repay_date) repay_date,
            real_repay_time,
            IFNULL(auto_renewal, 1) auto_renewal,
            IF(settle_date IS NOT NULL, '1', '0') is_settle,
            DATE(settle_date) settle_date,
            (
            CASE
            WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126)
            THEN '支付宝'
            WHEN ro.mini_type IN (6, 8, 10, 11)
            THEN '微信'
            WHEN ro.mini_type = 2
            THEN '字节跳动'
            WHEN ro.mini_type = 3
            THEN 'app'
            END
            ) platform,
            rate_config_type finance_type,
            rorp.term term,
            cl.riskOpinion risk_level,
            sn.riskStrategy risk_strategy,
            IF(
            artificialAuditorId = 1,
            '自动风控',
            '人工审核'
            ) audit_type,
            IF(
            ext_json IS NOT NULL
            AND rate_config_type = 10,
            '1',
            '0'
            ) forced_conversion,
            (
            CASE
            WHEN DATE_ADD(
            LAST_DAY(rorp.`repay_date`),
            INTERVAL 2 DAY
            ) > DATE(CURDATE())
            THEN (
            CASE
            WHEN YEAR(repay_date) = YEAR(CURDATE())
            AND MONTH(repay_date) > MONTH (CURDATE())
            THEN '0'
            WHEN term = 1
            THEN IF(
            DATE(repay_date) >= DATE(CURDATE()),
            '0',
            IF(
            MIN(repay_status) = 1
            AND MIN(overdue) = 5,
            '1',
            '0'
            )
            )
            ELSE IF(
            MIN(repay_status) = 1
            AND MIN(overdue) = 5,
            '1',
            IF(MIN(repay_status) = 5, '0', '2')
            )
            END
            )
            ELSE (
            CASE
            WHEN DATE(IFNULL(real_repay_time, CURDATE())) <= DATE_ADD(
            LAST_DAY(`repay_date`),
            INTERVAL 2 DAY
            )
            THEN IF(MIN(repay_status) = 5, '0', '1')
            ELSE IF(
            DATEDIFF(
            DATE(IFNULL(real_repay_time, CURDATE())),
            DATE_ADD(
            LAST_DAY(`repay_date`),
            INTERVAL 2 DAY
            )
            ) > #{overdueDay},
            '1',
            IF(
            real_repay_time IS NOT NULL
            AND MIN(repay_status) = 5,
            '0',
            '1'
            )
            )
            END
            )
            END
            ) is_overdue,
            IFNULL(
            IF(
            MIN(g.ext) != '',
            JSON_EXTRACT (MIN(g.ext), '$.discountReturnAmt'),
            '0'
            ),
            '0'
            ) discount_return_amt,
            IF(
            MIN(c.renew_type) != 2,
            MIN(c.actual_financing_amt),
            AVG(rorp.capital) * 12
            ) rent_total,
            IFNULL(MIN(rorp.overdue_fine), 0) overdue_fine,
            IFNULL(MIN(c.act_bond_amt), 0) bond_amt,
            IFNULL(MIN(x.before_discount), 0) before_discount,
            IFNULL(MIN(rorpp.bond_rate), 0) bond_rate,
            (
            CASE
            WHEN MIN(repay_status) = 5
            THEN AVG(rorp.capital)
            WHEN MIN(repay_status) = 1
            THEN (
            AVG(capital) - (
            AVG(real_repay_capital) - AVG(discount_amt)
            )
            )
            END
            ) real_capital,
            AVG(rorp.capital) capital,
            MIN(rorp.is_deleted) deleted,
            MIN(repay_status) repay_status,
            MIN(overdue) overdue,
            IF(real_repay_time IS NULL, 1, 0) real_repay_time_status,
            MIN(z.parent_id) parent_id,
            MIN(z.status) order_status,
            MIN(roos.current_overdue_days) current_overdue_days,
            MIN(rorp.overdue_days) term_overdue_days,
            SUM(
            IF(
            repay_status = 5,
            real_repay_capital,
            0
            )
            ) already_pay,
            SUM(
            IF(
            repay_status = 1,
            IF(
            real_repay_time IS NOT NULL,
            real_repay_capital,
            capital
            ),
            0
            )
            ) surplus_amt
        FROM
            (SELECT
            id,
            parent_id,
            NO,
            rent_start_date,
            STATUS
            FROM
            `qnvip_rent`.rent_order ro
            WHERE
            ro.id > ${id}
            AND merchant_id not IN (100, ********)
            AND ro.parent_id > 0
            AND biz_type = 2
            AND termination != 5
            AND is_deleted = 0
            AND ro.type = 1
            ORDER BY id
          LIMIT ${pageSize}   ) z
            INNER JOIN `qnvip_rent`.rent_order ro
        ON ro.id = z.parent_id
            INNER JOIN `qnvip_rent`.rent_order_repayment_plan rorp
            ON z.id = rorp.order_id
            INNER JOIN `qnvip_rent`.rent_order_infomore roi
            ON roi.order_id = z.id
            INNER JOIN `qnvip_rent`.rent_order_overdue_stat roos
            ON z.id = roos.order_id
            LEFT JOIN
            (SELECT
            MIN(ext) ext,
            order_id
            FROM
            `qnvip_rent`.rent_order_flow b
            WHERE b.biz_type = 3
            AND b.is_deleted = 0
            AND b.pay_status = 10
            AND (
            b.mark_refund IS NULL
            OR b.mark_refund = 0
            )
            AND b.flow_type = 1
            AND b.refunded_amt = 0
            AND b.ext IS NOT NULL
            AND b.ext != ''
            GROUP BY order_id) g
            ON z.id = g.order_id
            INNER JOIN `qnvip_rent`.rent_order_finance_detail c
            ON z.id = c.order_id
            INNER JOIN
            (SELECT
            IFNULL(
            MIN(b.bond_amt) / (
            IF(
            MIN(b.rate_config_type) != 10,
            MIN(b.actual_financing_amt),
            AVG(capital) * 12
            ) + IF(
            MIN(b.rate_config_type != 10),
            MIN(b.buyout_amt),
            0
            ) - MIN(xx.total_discount)
            ),
            0
            ) bond_rate,
            a.order_id
            FROM
            `qnvip_rent`.rent_order_repayment_plan a
            INNER JOIN `qnvip_rent`.rent_order_finance_detail b
            ON a.order_id = b.order_id
            LEFT JOIN
            (SELECT
            order_id,
            IF(MIN(isbefore) = 1, MIN(amt1) + MIN(amt2), 0) total_discount,
            IF(
            MIN(isbefore) = 1,
            MIN(yuji_amt1) + MIN(yuji_amt2),
            0
            ) before_discount
            FROM
            (SELECT
            x.order_id,
            IF(
            MIN(x.bind_order_time) IS NOT NULL
            AND MIN(y.payment_time) >= MIN(x.bind_order_time),
            1,
            0
            ) isbefore,
            IF(
            x.type IN (1, 3),
            MAX(x.write_off_amt),
            0
            ) amt1,
            IF(
            x.type IN (2, 4),
            SUM(x.write_off_amt),
            0
            ) amt2,
            IF(
            x.type IN (1, 3)
            AND MIN(x.use_status = 2),
            MAX(x.write_off_amt),
            0
            ) yuji_amt1,
            IF(
            x.type IN (2, 4)
            AND MIN(x.use_status = 2),
            SUM(x.write_off_amt),
            0
            ) yuji_amt2
            FROM
            `qnvip_rent`.rent_customer_coupon X
            INNER JOIN qnvip_rent.rent_order Y
            ON x.order_id = y.id
            WHERE x.order_id > 0
            AND x.scene = 1
            AND x.is_deleted = 0
            AND y.is_deleted = 0
            GROUP BY x.order_id,
            x.type) d
            GROUP BY order_id) xx
            ON b.order_id = xx.order_id
            GROUP BY a.order_id) rorpp
            ON z.parent_id = rorpp.order_id
            LEFT JOIN
            (SELECT
            order_id,
            IF(MIN(isbefore) = 1, MIN(amt1) + MIN(amt2), 0) total_discount,
            IF(
            MIN(isbefore) = 1,
            MIN(yuji_amt1) + MIN(yuji_amt2),
            0
            ) before_discount
            FROM
            (SELECT
            x.order_id,
            IF(
            MIN(x.bind_order_time) IS NOT NULL
            AND MIN(y.payment_time) >= MIN(x.bind_order_time),
            1,
            0
            ) isbefore,
            IF(
            x.type IN (1, 3),
            MAX(x.write_off_amt),
            0
            ) amt1,
            IF(
            x.type IN (2, 4),
            SUM(x.write_off_amt),
            0
            ) amt2,
            IF(
            x.type IN (1, 3)
            AND MIN(x.use_status = 2),
            MAX(X.write_off_amt),
            0
            ) yuji_amt1,
            IF(
            x.type IN (2, 4)
            AND MIN(x.use_status = 2),
            SUM(X.write_off_amt),
            0
            ) yuji_amt2
            FROM
            `qnvip_rent`.rent_customer_coupon X
            INNER JOIN `qnvip_rent`.rent_order Y
            ON x.order_id = y.id
            WHERE x.order_id > 0
            AND x.scene = 1
            AND x.is_deleted = 0
            AND y.is_deleted = 0
            GROUP BY x.order_id,
            x.type) a
            GROUP BY order_id) X
            ON z.id = x.order_id
            INNER JOIN `alchemist`.cl_loan cl
            ON cl.loanno = ro.no
            INNER JOIN `alchemist`.serial_no sn
            ON sn.businessno = ro.no
        WHERE rorp.is_deleted = 0
        GROUP BY DATE(z.rent_start_date),
            ro.mini_type,
            rorp.order_id,
            rate_config_type,
            IF(
            ext_json IS NOT NULL
            AND rate_config_type = 10,
            '1',
            '0'
            ),
            rorp.term,
            cl.riskOpinion,
            sn.riskStrategy,
            auto_renewal,
            settle_date,
            cl.artificialAuditorId,
            z.no,
            repay_date,
            cl.scene,
            cl.quotientname,
            real_repay_time,
            (
            c.bond_amt / (
            IF(
            c.rate_config_type != 10,
            c.actual_financing_amt,
            rorp.capital * 12
            ) + IF(
            c.rate_config_type != 10,
            c.buyout_amt,
            0
            )
            ) - IFNULL(c.diff_pricing_discount_amt, 0) - IFNULL(c.coupon_discount_amt, 0)
            )
        ORDER BY DATE(z.rent_start_date)
        ]]>
    </select>


</mapper>