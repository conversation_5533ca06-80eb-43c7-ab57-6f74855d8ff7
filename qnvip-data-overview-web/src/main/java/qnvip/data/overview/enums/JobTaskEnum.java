package qnvip.data.overview.enums;

public enum JobTaskEnum {
    //数仓商户明细基础表
    VINTAGE_TASK("VINTAGE_TASK"),
    //大盘-商户明细表
    DATAVIEW_RISK_DISTRIBUTION_ORDER_OVERDUE("DATAVIEW_RISK_DISTRIBUTION_ORDER_OVERDUE_GRAY"),
    //大盘-商户汇总表
    DATAVIEW_RISK_DISTRIBUTION_COUNT("DATAVIEW_RISK_DISTRIBUTION_COUNT_GRAY")
    ;

    private String value;

    public  String getValue(){
        return value;
    }


    JobTaskEnum(String value) {
        this.value = value;
    }
}
