package qnvip.data.overview.controller.whole;

import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;
import qnvip.data.overview.domain.whole.WholeLinkFlowDO;
import qnvip.data.overview.manager.whole.WholeFlowHealthManager;
import qnvip.data.overview.manager.whole.WholeLinkManager;
import qnvip.data.overview.param.WholeFlowHealthParam;
import qnvip.data.overview.vo.order.OperateOrderForecastVO;
import qnvip.data.overview.vo.whole.WholeFlowHealthVO;
import qnvip.data.overview.vo.whole.WholeLinkListVO;
import qnvip.data.overview.vo.whole.WholeLinkVO;
import qnvip.rent.common.base.SingleResult;

import java.util.List;
import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/7/27
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin/data/whole")
@Api(tags = {"全链路"})
@RequiredArgsConstructor
public class WholeLinkController {

    private final WholeLinkManager wholeLinkManager;
    private final WholeFlowHealthManager flowHealthManager;


    @GetMapping("/forecast/list")
    @ApiOperation("折线图")
    public SingleResult<OperateOrderForecastVO> forecastList(String time,
                                                             @RequestParam(defaultValue = "1") Integer bizType,
                                                             @RequestParam(defaultValue = "1") Integer countType) {
        return SingleResult.of(wholeLinkManager.getVo(time, bizType, countType));
    }

    @GetMapping("/flow/list")
    @ApiOperation("流量侧列表")
    public SingleResult<WholeLinkVO> flowList(@RequestParam String time,
                                              @RequestParam(defaultValue = "-5") Integer miniType,
                                              @RequestParam(defaultValue = "") String scene) {
        return wholeLinkManager.getFlowList(time, miniType, scene);
    }

    @PostMapping("/flow/health/miniType")
    @ApiOperation("流量健康度-桑基图(miniType)")
    public SingleResult<List<WholeFlowHealthVO>> flowHealthMiniTypeList(@RequestBody WholeFlowHealthParam param) {
        return flowHealthManager.getFlowList(param.getTime(), null, null, WholeLinkFlowDO::getMiniType);
    }

    @PostMapping("/flow/health/scene")
    @ApiOperation("流量健康度-桑基图(scene)")
    public SingleResult<List<WholeFlowHealthVO>> flowHealthSceneList(@RequestBody WholeFlowHealthParam param) {
        return flowHealthManager.getFlowList(param.getTime(), param.getMiniType(), param.getScene(),
                WholeLinkFlowDO::getScene);
    }

    @PostMapping("/flow/health/line")
    @ApiOperation("流量健康度-折线图")
    public SingleResult<OperateOrderForecastVO> flowHealthLineList(@RequestBody WholeFlowHealthParam param) {
        return flowHealthManager.getLineList(param.getTime(), param.getMiniType(), param.getScene(), param.getName());
    }

    @PostMapping("/flow/health/select")
    @ApiOperation("流量健康度-筛选器")
    public SingleResult<Map<String, List<?>>> flowHealthSelect() {
        return SingleResult.of(flowHealthManager.getSceneList());
    }

    @GetMapping("/base/list")
    @ApiOperation("基础通过")
    public SingleResult<WholeLinkVO> baseList(@RequestParam String time,
                                              @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getBaseList(time, miniType);
    }

    @GetMapping("/order/list")
    @ApiOperation("订单侧")
    public SingleResult<WholeLinkVO> orderList(@RequestParam String time,
                                               @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getOrderList(time, miniType);
    }

    @GetMapping("/bigdata/list")
    @ApiOperation("大数据通过")
    public SingleResult<WholeLinkVO> bigdataList(@RequestParam String time,
                                                 @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getBigdataList(time, miniType);
    }

    @GetMapping("/credit/list")
    @ApiOperation("征信")
    public SingleResult<WholeLinkVO> creditList(@RequestParam String time,
                                                @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getCreditList(time, miniType);
    }

    @GetMapping("/fraud/list")
    @ApiOperation("反欺诈")
    public SingleResult<WholeLinkVO> fraudList(@RequestParam String time,
                                               @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getFraudList(time, miniType);
    }

    @GetMapping("/pay/list")
    @ApiOperation("支付")
    public SingleResult<WholeLinkVO> payList(@RequestParam String time,
                                             @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getPayList(time, miniType);
    }

    @GetMapping("/sign/list")
    @ApiOperation("签收")
    public SingleResult<WholeLinkVO> signList(@RequestParam String time,
                                              @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.getSignList(time, miniType);
    }

    @GetMapping("/thirty/list")
    @ApiOperation("获取对应指标30天列表")
    public SingleResult<WholeLinkListVO> get30dayList(@RequestParam Integer code,
                                                      @RequestParam String time,
                                                      @RequestParam(defaultValue = "-5") Integer miniType) {
        return wholeLinkManager.get30dayList(code, time, miniType);
    }
}
