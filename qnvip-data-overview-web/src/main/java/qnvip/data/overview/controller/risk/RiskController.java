package qnvip.data.overview.controller.risk;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.qnvip.oauth2.security.controller.AbstractController;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.web.bind.annotation.*;
import qnvip.data.overview.aop.webauth.RequiredPermissions;
import qnvip.data.overview.constants.RedisKey;
import qnvip.data.overview.enums.DateEnum;
import qnvip.data.overview.manager.risk.*;
import qnvip.data.overview.param.risk.RiskMerchantParam;
import qnvip.data.overview.param.risk.RiskRentInfoParam;
import qnvip.data.overview.param.risk.RiskRentNewParam;
import qnvip.data.overview.request.RiskDistributionOverdueRequest;
import qnvip.data.overview.request.RiskOverdueRequest;
import qnvip.data.overview.service.risk.DataviewRiskNewRentOrderInfoService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.EasyPoiUtils;
import qnvip.data.overview.vo.risk.*;
import qnvip.data.overview.vo.risk.merchant.RiskSituationMerchantVO;
import qnvip.data.overview.vo.risk.rent.RiskRentDistributionInfoVO;
import qnvip.data.overview.vo.risk.rent.RiskRentInfoVO;
import qnvip.rent.common.base.SingleResult;
import qnvip.rent.common.exception.NormalBizException;

import javax.servlet.http.HttpServletResponse;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/admin/view/risk")
@RequiredArgsConstructor
@Api(tags = "风控数据总览")
public class RiskController extends AbstractController {

    private final RiskManager riskManager;
    private final RiskBusinessManager riskBusinessManager;
    private final RiskRenewalBusinessManager riskRenewalBusinessManager;
    private final RiskGeneralBusinessManager riskGeneralBusinessManager;
    private final RiskMerchantBusinessManager riskMerchantBusinessManager;
    private final RiskQuotientManager riskQuotientManager;
    private final RiskDistributionBusinessManager riskDistributionBusinessManager;
    private final RiskInfoManager riskInfoManager;
    private final RiskInfoFlinkManager riskInfoFlinkManager;
    private final ThirdPartManager thirdPartManager;
    private final RiskStatisticalDimensionManager riskStatisticalDimensionManager;

    private final RedisTemplate redisTemplate;


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/line")
    @ApiOperation("风控数据总览-折线图（明细表）")
    public SingleResult<List<RiskSituationVO>> overallLine(@RequestParam(value = "timeType") Integer timeType,
                                                           String sTime,
                                                           String eTime,
                                                           @RequestParam(defaultValue = "-5") String channels) {

        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        return SingleResult.of(riskManager.getListByTime(sTime, eTime, miniTypeList));
    }

//    @RequiredPermissions(value = {"/risk/overview"}, type = 1)  @RequestBody RiskMerchantParam param
    @PostMapping("overview/merchant/line")
    @ApiOperation("风控数据总览-折线图（明细表）")
    public SingleResult<List<RiskSituationMerchantVO>> overallMerchantLine() {
        RiskMerchantParam param = new RiskMerchantParam();
        param.setTimeType(12);
        if (param.getTimeType() != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(param.getTimeType());
            param.setSTime(date.get(0));
            param.setSTime(date.get(1));
        }
        if (!StringUtils.isNoneBlank(param.getSTime(),param.getETime())) {
            List<String> date = DateUtils.getDateTimeListByType(12);
            param.setSTime(date.get(0));
            param.setETime(date.get(1));
        }
        return SingleResult.of(riskManager.getMerchantListByTimeNew(param));
    }


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/merchant/line/new")
//    @PostMapping("overview/merchant/line")
    @ApiOperation("风控大盘-分期数据总览")
    public SingleResult<List<RiskSituationMerchantVO>> riskMerchant(@RequestBody RiskMerchantParam param) {
        if (!StringUtils.isNoneBlank(param.getSTime(),param.getETime())) {
            List<String> date = DateUtils.getDateTimeListByType(12);
            param.setSTime(date.get(0));
            param.setETime(date.get(1));
        }
        return SingleResult.of(riskManager.getMerchantListByTimeNew(param));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent/line")
    @ApiOperation("风控数据总览-折线图（明细表）")
    public SingleResult<List<RiskSituationVO>> overallRentLine(@RequestParam(value = "timeType") Integer timeType,
                                                               String sTime,
                                                               String eTime,
                                                               @RequestParam(defaultValue = "-5") String channels) {

        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        return SingleResult.of(riskManager.getRentListByTime(sTime, eTime, miniTypeList));
    }



    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview")
    @ApiOperation("主订单分析-数据总览")
    public SingleResult<RiskSituationVO> overall(@RequestParam(value = "timeType") Integer timeType,
                                                 String sTime,
                                                 String eTime,
                                                 @RequestParam(defaultValue = "-5") String channels) {
        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        return SingleResult.of(riskManager.getTotal(sTime, eTime, miniTypeList));
    }


//    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/merchant")
    @ApiOperation("风控大盘-分期数据总览")
    public SingleResult<RiskSituationVO> overallMerchant(@RequestParam(value = "timeType") Integer timeType,
                                                         String sTime,
                                                         String eTime,
                                                         @RequestParam(defaultValue = "-5") String channels) {
        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        return SingleResult.of(riskManager.getMerchantTotal(sTime, eTime, miniTypeList));
    }


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent")
    @ApiOperation("主订单分析-数据总览")
    public SingleResult<RiskSituationVO> overallRent(@RequestParam(value = "timeType") Integer timeType,
                                                     String sTime,
                                                     String eTime,
                                                     @RequestParam(defaultValue = "-5") String channels) {

        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());
        return SingleResult.of(riskManager.getRentTotal(sTime, eTime, miniTypeList));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @GetMapping("/exportOverall")
    @ApiOperation("导出明细表")
    public void exportOverall(@RequestParam(value = "timeType") Integer timeType,
                              String sTime,
                              String eTime,
                              @RequestParam(defaultValue = "-5") String channels,
                              HttpServletResponse response) {
        if (timeType != DateEnum.CUSTOM.getCode()) {
            List<String> date = DateUtils.getDateTimeListByType(timeType);
            sTime = date.get(0);
            eTime = date.get(1);
        }
        String[] split = channels.split(",");
        List<Integer> miniTypeList = Arrays.stream(split).map(Integer::valueOf).collect(Collectors.toList());

        List<RiskSituationVO> resultList = riskManager.getListByTime(sTime, eTime, miniTypeList);

        if (CollUtil.isEmpty(resultList)) {
            throw NormalBizException.instance("数据为空,暂不支持导出");
        }
        List<RiskSituationVO> collect = resultList.stream().peek(item -> {
            BigDecimal avgPayRate = item.getAvgPayRate();
            BigDecimal payUserRate = item.getPayUserRate();
            Long payUser = item.getPayUser();
            String concat = payUserRate.toString().concat("% ").concat("| ")
                    .concat(payUser.toString()).concat("(")
                    .concat(avgPayRate.toString()).concat("%)");
            item.setPayUserStr(concat);
        }).collect(Collectors.toList());
        EasyPoiUtils.exportExcel(collect, "风控明细", "风控明细",
                RiskSituationVO.class, "风控明细".concat(".xls"), true, response);
    }


    // @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("/overview/time")
    @ApiOperation("风控大盘-时间")
    public SingleResult<Map<String, Object>> getTime() {
        return SingleResult.of(riskManager.getUpdateTime());
    }


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("business/list")
    @ApiOperation("风控大盘-明细表")
    public SingleResult<List<RiskBusinessVO>> businessList(@ApiParam("统计维度") String countWay,
                                                           @ApiParam("统计类型") String countType,
                                                           @ApiParam("统计项") String countItem,
                                                           @ApiParam("平台") String miniType,
                                                           @ApiParam("起租日：开始时间") String rentStartDate,
                                                           @ApiParam("起租日：结束时间") String rentEndDate,
                                                           @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskBusinessManager.getList(rentStartDate, rentEndDate, overdueDay, miniType, countWay
                , countType, countItem));

    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("business/selector")
    @ApiOperation("风控大盘-运营选择框")
    public SingleResult<Map<Integer, Set<String>>> businesSelector(
            @ApiParam("平台") String miniType,
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskBusinessManager.getSelector(rentStartDate, rentEndDate, miniType, overdueDay));

    }

    @GetMapping("business/export")
    @ApiOperation("风控大盘明细-导出")
    public void exportRent(@ApiParam("统计维度") String countWay,
                           @ApiParam("统计类型") String countType,
                           @ApiParam("统计项") String countItem,
                           @ApiParam("平台") String miniType,
                           @ApiParam("起租日：开始时间") String rentStartDate,
                           @ApiParam("起租日：结束时间") String rentEndDate,
                           @ApiParam("宽限期") Integer overdueDay,
                           HttpServletResponse response) {

        riskBusinessManager.export(rentStartDate, rentEndDate, overdueDay, miniType, countWay
                , countType, countItem, response);
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("renewal/list")
    @ApiOperation("风控大盘-续租-明细表")
    public SingleResult<RiskRenewalVO> renewalList(@ApiParam("统计维度") String countWay,
                                                   @ApiParam("统计类型") String countType,
                                                   @ApiParam("统计项") String countItem,
                                                   @ApiParam("平台") String miniType,
                                                   @ApiParam("起租日：开始时间") String rentStartDate,
                                                   @ApiParam("起租日：结束时间") String rentEndDate,
                                                   @ApiParam("父订单状态") Integer isSettle,
                                                   @ApiParam("续租方式") Integer renewWay,
                                                   @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskRenewalBusinessManager.getList(rentStartDate, rentEndDate, overdueDay, miniType, countWay
                , isSettle, renewWay, countType, countItem));

    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("renewal/selector")
    @ApiOperation("风控大盘-运营选择框")
    public SingleResult<Map<Integer, Set<String>>> renewalSelector(
            @ApiParam("平台") String miniType,
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskRenewalBusinessManager.getSelector(rentStartDate, rentEndDate, miniType,
                overdueDay));

    }

    @GetMapping("renewal/export")
    @ApiOperation("风控大盘-续租明细-导出")
    public void exportRenewal(@ApiParam("统计维度") String countWay,
                              @ApiParam("统计类型") String countType,
                              @ApiParam("统计项") String countItem,
                              @ApiParam("平台") String miniType,
                              @ApiParam("起租日：开始时间") String rentStartDate,
                              @ApiParam("起租日：结束时间") String rentEndDate,
                              @ApiParam("父订单状态") Integer isSettle,
                              @ApiParam("续租方式") Integer renewWay,
                              @ApiParam("宽限期") Integer overdueDay,
                              HttpServletResponse response) {

        riskRenewalBusinessManager.export(rentStartDate, rentEndDate, overdueDay, miniType, countWay
                , isSettle, renewWay, countType, countItem, response);
    }


//     @NoRepeat
//    @PostMapping("general/list")
//    @ApiOperation("风控大盘-通用-明细表")
//    public SingleResult<RiskGeneralVO> generalList(RiskOverdueRequest request) {
//        return riskGeneralBusinessManager.getList(request);
//    }

    @PostMapping("general/listNew")
//    @PostMapping("general/list")
    @ApiOperation("风控大盘-通用-明细表")
    public SingleResult<RiskGeneralVO> generalList(RiskOverdueRequest request) {
        if (redisTemplate.hasKey(RedisKey.GENERAL_VIEW_VINTAGE + request.hashCode())) {
            return (SingleResult<RiskGeneralVO>) redisTemplate.opsForValue().get(RedisKey.GENERAL_VIEW_VINTAGE + request.hashCode());
        }else{
            SingleResult<RiskGeneralVO> result = riskGeneralBusinessManager.getList(request);
            if (result.getData()!= null && CollectionUtils.isNotEmpty(result.getData().getList())) {
                redisTemplate.opsForValue().set(RedisKey.GENERAL_VIEW_VINTAGE + request.hashCode(), result,
                        10, TimeUnit.SECONDS);
            }
            return result;
        }
    }

    @PostMapping("general/export")
    @ApiOperation("风控大盘-通用明细-导出")
    public void exportGeneral(RiskOverdueRequest request,
                              HttpServletResponse response) {

        riskGeneralBusinessManager.export(request, response);
    }

//    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("general/selectorNew")
//@PostMapping("general/selector")
    @ApiOperation("风控大盘-租赁自营-通用选择框")
    public SingleResult<Map<Integer, Set<String>>> generalSelectorNew() {
        return SingleResult.of(riskStatisticalDimensionManager.getSelectorNew("1"));
    }

    @PostMapping("general/selector")
    @ApiOperation("风控大盘-通用选择框")
    public SingleResult<Map<Integer, Collection<String>>> generalSelector(
            @ApiParam("平台") String miniType,
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskGeneralBusinessManager.getSelector(rentStartDate, rentEndDate, miniType,
                overdueDay));

    }


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("merchant/list")
    @ApiOperation("风控大盘-分期-明细表-低定价")
    public SingleResult<RiskGeneralVO> merchantList(RiskOverdueRequest request) {
        request.setBizType(0);
        if (redisTemplate.hasKey(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode())) {
            return (SingleResult<RiskGeneralVO>) redisTemplate.opsForValue().get(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode());
        }else{
            SingleResult<RiskGeneralVO> result = riskMerchantBusinessManager.getList(request);
            if (result.getData()!= null && CollectionUtils.isNotEmpty(result.getData().getList())) {
                redisTemplate.opsForValue().set(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode(), result,
                        10, TimeUnit.SECONDS);
            }
           return result;
        }
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("merchant/list/highPrice")
    @ApiOperation("风控大盘-赊销自营-高定价")
    public SingleResult<RiskGeneralVO> merchantListHighPrice(RiskOverdueRequest request) {
        request.setBizType(10);
        if (redisTemplate.hasKey(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode())) {
            return (SingleResult<RiskGeneralVO>) redisTemplate.opsForValue().get(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode());
        }else{
            SingleResult<RiskGeneralVO> result = riskMerchantBusinessManager.getList(request);
            if (result.getData()!= null && CollectionUtils.isNotEmpty(result.getData().getList())) {
                redisTemplate.opsForValue().set(RedisKey.MERCHANT_VIEW_VINTAGE + request.hashCode(), result,
                        10, TimeUnit.SECONDS);
            }
            return result;
        }
    }

    @PostMapping("merchant/export")
    @ApiOperation("风控大盘-分期-导出")
    public void merchantGeneral(RiskOverdueRequest request,
                                HttpServletResponse response) {

        riskMerchantBusinessManager.export(request, response);
    }

  //  @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("merchant/selector")
    @ApiOperation("风控大盘-分期选择框")
    public SingleResult<Map<Integer, Set<String>>> merchantSelector() {
        return SingleResult.of(riskStatisticalDimensionManager.getStagingSelectorNew("3"));
    }


//    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("distribution/listNew")
//@PostMapping("distribution/list")
    @ApiOperation("风控大盘-租赁商户-明细表")
    public SingleResult<RiskGeneralVO> distributionList(RiskDistributionOverdueRequest request) {
        if (redisTemplate.hasKey(RedisKey.DISTRIBUTED_VIEW_VINTAGE + request.hashCode())) {
            return (SingleResult<RiskGeneralVO>) redisTemplate.opsForValue().get(RedisKey.DISTRIBUTED_VIEW_VINTAGE + request.hashCode());
        }else{
            SingleResult<RiskGeneralVO> result = riskDistributionBusinessManager.getList(request);
            if (result.getData()!= null && CollectionUtils.isNotEmpty(result.getData().getList())) {
                redisTemplate.opsForValue().set(RedisKey.DISTRIBUTED_VIEW_VINTAGE + request.hashCode(), result,
                        10, TimeUnit.SECONDS);
            }
            return result;

        }
    }

    @PostMapping("distribution/export")
    @ApiOperation("风控大盘-商户长租-导出")
    public void distributionExport(RiskDistributionOverdueRequest request,
                                   HttpServletResponse response) {

        riskDistributionBusinessManager.export(request, response);
    }

//    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("distribution/selectorNew")
//@PostMapping("distribution/selector")
    @ApiOperation("风控大盘-租赁商户-选择框")
    public SingleResult<Map<Integer, Set<String>>> distributionSelectorNew() {
        return SingleResult.of(riskStatisticalDimensionManager.getSelectorMerChantNew("2"));

    }

    @PostMapping("distribution/selectorNew/shopName")
    @ApiOperation("风控大盘-租赁商户-商户名称")
    public SingleResult<Map<String, Set<String>>> distributionSelectorNewShopName(
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate) {
        return SingleResult.of(riskDistributionBusinessManager.selectorMap(rentStartDate, rentEndDate));

    }


    @PostMapping("distribution/selector")
    @ApiOperation("风控大盘-租赁商户-选择框")
    public SingleResult<Map<Integer, Set<String>>> distributionSelector(
            @ApiParam("平台") String miniType,
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("宽限期") Integer overdueDay) {
        return SingleResult.of(riskDistributionBusinessManager.getSelector(rentStartDate, rentEndDate, miniType,
                overdueDay));

    }

    @PostMapping("quotient/list")
    @ApiOperation("风控大盘-导流商")
    public SingleResult<RiskQuotientVO> getQuotientList(
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("miniType") String miniType,
            @ApiParam("导流商") String quotientName,
            @ApiParam("场景值") String scene,
            @ApiParam("策略") String riskStrategy,
            @ApiParam("渠道名称") String channelName
    ) {
        return SingleResult.of(riskQuotientManager.getList(rentStartDate, rentEndDate, miniType, quotientName,
                scene, riskStrategy, channelName));
    }

    @PostMapping("quotient/export")
    @ApiOperation("风控大盘-导流商导出")
    public void exportQuotientList(
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("miniType") String miniType,
            @ApiParam("导流商") String quotientName,
            @ApiParam("场景值") String scene,
            @ApiParam("策略") String riskStrategy,
            @ApiParam("渠道名称") String channelName,
            HttpServletResponse response) {
        riskQuotientManager.export(rentStartDate, rentEndDate, miniType, quotientName,
                scene, riskStrategy, channelName, response);
    }

    @PostMapping("quotient/selector")
    @ApiOperation("风控大盘-获取导流商和场景值")
    public SingleResult<RiskSelectorVO> getSelctorList(
            @ApiParam("起租日：开始时间") String rentStartDate,
            @ApiParam("起租日：结束时间") String rentEndDate,
            @ApiParam("起租日：结束时间") String miniType
    ) {
        return SingleResult.of(riskQuotientManager.getSelectorList(rentStartDate, rentEndDate, miniType));
    }


    @PostMapping("quotient/tag")
    @ApiOperation("风控大盘-标签")
    public SingleResult<RiskSelectorVO> getTag(
            @ApiParam("时间") String time,
            @ApiParam("导流商") String quotientName,
            @ApiParam("场景值") String scene,
            @ApiParam("平台") String miniType,
            @ApiParam("策略") String riskStrategy,
            @ApiParam("渠道名称") String channelName

    ) {
        return SingleResult.of(riskQuotientManager.getTags(time, miniType, quotientName, scene, riskStrategy, channelName));
    }

    @PostMapping("quotient/detail")
    @ApiOperation("风控大盘-导流商详情")
    public SingleResult<List<RiskQuotientDetailVO>> getQuotientDetail(
            @ApiParam("时间") String time,
            @ApiParam("导流商名称") String quotientName,
            @ApiParam("场景值") String scene,
            @ApiParam("风控策略") String riskStrategy,
            @ApiParam("miniType") String miniType,
            @ApiParam("阶段") String stage,
            @ApiParam("标签") String tag,
            @ApiParam("渠道名称") String channelName,
            @ApiParam("下单人数") Integer count) {
        return SingleResult.of(riskQuotientManager.getDetail(time, quotientName, scene, miniType,
                riskStrategy, stage,
                tag, channelName, count));
    }


    @PostMapping("quotient/detail/export")
    @ApiOperation("风控大盘-导流商导出")
    public void exportQuotientDetailList(
            @ApiParam("时间") String time,
            @ApiParam("导流商名称") String quotientName,
            @ApiParam("场景值") String scene,
            @ApiParam("风控策略") String riskStrategy,
            @ApiParam("风控策略") String miniTypeStr,
            @ApiParam("阶段") String stage,
            @ApiParam("标签") String tag,
            @ApiParam("渠道名称") String channelName,
            @ApiParam("下单人数") Integer count,
            HttpServletResponse response) {
        riskQuotientManager.exportQuotientDetail(time, quotientName, scene, miniTypeStr,
                riskStrategy, stage,
                tag, channelName, count, response);
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent/info")
    @ApiOperation("风控数据总览-租赁大盘报表")
    public SingleResult<List<RiskRentInfoVO>> overallRentLine(@RequestBody RiskRentInfoParam param) {
        // return SingleResult.of(riskInfoManager.getList(param));
        return SingleResult.of(riskInfoManager.newRentReport(BeanUtil.toBean(param,RiskRentNewParam.class)));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent/report")
    @ApiOperation("风控数据总览-新租赁新版本大盘报表")
    public SingleResult<List<RiskRentInfoVO>> newRentReport(@RequestBody RiskRentNewParam param) {
        return SingleResult.of(riskInfoManager.newRentReport(param));
    }


    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/flink/rent/info")
    @ApiOperation("风控数据总览-新租赁(实时)")
    public SingleResult<List<RiskRentInfoVO>> overallFlinkRentLine(@RequestBody RiskRentInfoParam param) {
        return SingleResult.of(riskInfoFlinkManager.getList(param));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent/distribution")
    @ApiOperation("风控数据总览-进客群分布")
    public SingleResult<List<RiskRentDistributionInfoVO>> overallRentDistribution(@RequestBody RiskRentInfoParam param) {
        return SingleResult.of(riskInfoManager.getDistributionList(param));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/rent/info/time")
    @ApiOperation("新租赁-时间")
    public SingleResult<Map<String, Object>> getRentTime() {
        return SingleResult.of(riskInfoManager.getUpdateTime());
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/scene")
    @ApiOperation("风控数据总览-场景值")
    public SingleResult<List<String>> getSceneList() {
        return SingleResult.of(riskInfoManager.getSceneList());
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/quotient")
    @ApiOperation("风控数据总览-导流商")
    public SingleResult<List<String>> getQuotientList() {
        return SingleResult.of(riskInfoManager.getQuotientList());
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("third/party")
    @ApiOperation("三方数据费用")
    public SingleResult<List<ThirdPartyServiceFeesCalcVO>> getThirdParty(@RequestParam("countMonth") String countMonth) {
        return SingleResult.of(thirdPartManager.getList(countMonth));
    }

    @RequiredPermissions(value = {"/risk/overview"}, type = 1)
    @PostMapping("overview/enum/map")
    @ApiOperation("风控数据总览-新旧程度,导流商渠道值,共租")
    public SingleResult<Map<String,Object>> getEnumMap() {
        return SingleResult.of(riskInfoManager.getEnumMap());
    }
}
