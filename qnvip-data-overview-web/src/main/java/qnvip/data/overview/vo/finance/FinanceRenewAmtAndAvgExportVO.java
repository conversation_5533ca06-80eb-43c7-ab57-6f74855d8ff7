package qnvip.data.overview.vo.finance;

import cn.afterturn.easypoi.excel.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * create by gw on 2022/3/10
 */
@Data
public class FinanceRenewAmtAndAvgExportVO {

    @Excel(name = "时间", width = 20)
    private String countDay;

    @ApiModelProperty("方案类型:1:12+N,2:3+N,3:12转3,4:9+3,5:12+N续租,6:3+N续租,7:12转3续租,8:9+3续租")
    private Integer financeType;

    @Excel(name = "金融方案", width = 20)
    private String financeName;

    @Excel(name = "基础项:续租本金", width = 20, type = 10)
    private BigDecimal buyoutRenewPrincipal = BigDecimal.ZERO;

    @Excel(name = "基础项:续租本金均值", width = 20, type = 10)
    private BigDecimal buyoutAvgRenewPrincipal = BigDecimal.ZERO;

    @Excel(name = "基础项:收取保证金", width = 20, type = 10)
    private BigDecimal bondRenewAmt = BigDecimal.ZERO;

    @Excel(name = "基础项:收取保证金均值", width = 20, type = 10)
    private BigDecimal bondAvgRenewAmt = BigDecimal.ZERO;

    @Excel(name = "基础项:协议总租金", width = 20, type = 10)
    private BigDecimal baseRentAmt = BigDecimal.ZERO;

    @Excel(name = "基础项:协议总租金均值", width = 20, type = 10)
    private BigDecimal baseAvgRentAmt = BigDecimal.ZERO;

    @Excel(name = "已收租金:已收月租金", width = 20, type = 10)
    private BigDecimal rentReceiveRent = BigDecimal.ZERO;

    @Excel(name = "已收租金:已收月租金均值", width = 20, type = 10)
    private BigDecimal rentAvgReceiveRent = BigDecimal.ZERO;

    @Excel(name = "已收租金:保证金抵扣租金", width = 20, type = 10)
    private BigDecimal rentDeductBondAmt = BigDecimal.ZERO;

    @Excel(name = "已收租金:保证金抵扣租金均值", width = 20, type = 10)
    private BigDecimal rentAvgDeductBondAmt = BigDecimal.ZERO;

    @Excel(name = "已收租金:售后减免租金", width = 20, type = 10)
    private BigDecimal rentDeductAfterRent = BigDecimal.ZERO;

    @Excel(name = "已收租金:售后减免租金均值", width = 20, type = 10)
    private BigDecimal rentAvgDeductAfterRent = BigDecimal.ZERO;

    @Excel(name = "已收租金:运营减免租金", width = 20, type = 10)
    private BigDecimal rentDeductMarketing = BigDecimal.ZERO;

    @Excel(name = "已收租金:运营减免租金均值", width = 20, type = 10)
    private BigDecimal rentAvgDeductMarketing = BigDecimal.ZERO;

    @Excel(name = "其他收入:逾期滞纳金", width = 20, type = 10)
    private BigDecimal otherOverdueAmt = BigDecimal.ZERO;

    @Excel(name = "其他收入:逾期滞纳金均值", width = 20, type = 10)
    private BigDecimal otherAvgOverdueAmt = BigDecimal.ZERO;

    @Excel(name = "保证金:结清退回", width = 20, type = 10)
    private BigDecimal bondBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "保证金:结清退回均值", width = 20, type = 10)
    private BigDecimal bondAvgBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "保证金:归还退回", width = 20, type = 10)
    private BigDecimal bondReturnBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "保证金:结清未退回", width = 20, type = 10)
    private BigDecimal bondNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "保证金:结清未退回均值", width = 20, type = 10)
    private BigDecimal bondAvgNotBackBondCount = BigDecimal.ZERO;

    @Excel(name = "保证金:归还退回均值", width = 20, type = 10)
    private BigDecimal bondAvgReturnBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "未逾期应收:未退保证金", width = 20, type = 10)
    private BigDecimal overZeroNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "未逾期应收:未退保证金均值", width = 20, type = 10)
    private BigDecimal overZeroAvgNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "未逾期应收:剩余总租金", width = 20, type = 10)
    private BigDecimal overZeroSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "未逾期应收:剩余总租金均值", width = 20, type = 10)
    private BigDecimal overZeroAvgSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:未退保证金", width = 20, type = 10)
    private BigDecimal overFirstNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:未退保证金均值", width = 20, type = 10)
    private BigDecimal overFirstAvgNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:剩余总租金", width = 20, type = 10)
    private BigDecimal overFirstSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:剩余总租金均值", width = 20, type = 10)
    private BigDecimal overFirstAvgSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:逾期滞纳金", width = 20, type = 10)
    private BigDecimal overFirstOverDueAmt = BigDecimal.ZERO;

    @Excel(name = "逾期7日内应收:逾期滞纳金均值", width = 20, type = 10)
    private BigDecimal overFirstAvgOverDueAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:未退保证金", width = 20, type = 10)
    private BigDecimal overSecondNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:未退保证金均值", width = 20, type = 10)
    private BigDecimal overSecondAvgNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:剩余总租金", width = 20, type = 10)
    private BigDecimal overSecondSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:剩余总租金均值", width = 20, type = 10)
    private BigDecimal overSecondAvgSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:逾期滞纳金", width = 20, type = 10)
    private BigDecimal overSecondOverDueAmt = BigDecimal.ZERO;

    @Excel(name = "逾期8-31日内应收:逾期滞纳金均值", width = 20, type = 10)
    private BigDecimal overSecondAvgOverDueAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:未退保证金", width = 20, type = 10)
    private BigDecimal overThirdNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:未退保证金均值", width = 20, type = 10)
    private BigDecimal overThirdAvgNotBackBondAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:剩余总租金", width = 20, type = 10)
    private BigDecimal overThirdSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:剩余总租金均值", width = 20, type = 10)
    private BigDecimal overThirdAvgSurplusRentAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:逾期滞纳金", width = 20, type = 10)
    private BigDecimal overThirdOverDueAmt = BigDecimal.ZERO;

    @Excel(name = "逾期32+日应收:逾期滞纳金均值", width = 20, type = 10)
    private BigDecimal overThirdAvgOverDueAmt = BigDecimal.ZERO;

}