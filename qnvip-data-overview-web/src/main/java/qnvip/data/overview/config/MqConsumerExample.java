package qnvip.data.overview.config;

import com.aliyun.openservices.ons.api.Consumer;
import com.aliyun.openservices.ons.api.ONSFactory;
import com.aliyun.openservices.ons.api.PropertyKeyConst;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.Properties;

/**
 * MQ消费者示例 - 展示如何集成优雅下线
 * 
 * 注意：这是一个示例文件，展示如何在实际的MQ消费者中集成优雅下线功能
 * 如果项目中有实际的MQ消费者，请参考此示例进行修改
 *
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@Component
public class MqConsumerExample {

    /**
     * 示例：如何在MQ消费者初始化时注册到优雅下线管理
     */
    @PostConstruct
    public void initConsumer() {
        // 这是一个示例，展示如何创建和注册MQ消费者
        // 实际使用时，请根据项目的具体配置进行调整
        
        /*
        Properties properties = new Properties();
        properties.put(PropertyKeyConst.AccessKey, "your-access-key");
        properties.put(PropertyKeyConst.SecretKey, "your-secret-key");
        properties.put(PropertyKeyConst.NAMESRV_ADDR, "your-nameserver-addr");
        properties.put(PropertyKeyConst.GROUP_ID, "your-group-id");
        
        Consumer consumer = ONSFactory.createConsumer(properties);
        
        // 订阅消息
        consumer.subscribe("your-topic", "your-tag", (message, context) -> {
            // 处理消息逻辑
            log.info("收到消息: {}", new String(message.getBody()));
            return Action.CommitMessage;
        });
        
        // 启动消费者
        consumer.start();
        
        // 重要：将消费者注册到优雅下线管理
        RocketMqSmartOffline.addConsumer(consumer);
        
        log.info("MQ消费者启动完成并已注册到优雅下线管理");
        */
        
        log.info("MQ消费者示例 - 当前项目中未发现标准的RocketMQ消费者");
        log.info("如果需要添加MQ消费者，请参考此示例文件中的注释代码");
    }
}
