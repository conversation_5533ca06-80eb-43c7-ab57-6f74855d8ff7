package qnvip.data.overview.config;

import com.qnvip.common.base.SmartOffline;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.client.serviceregistry.ServiceRegistry;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Spring Cloud优雅下线实现
 *
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@Component
public class SpringCloudSmartOffline implements SmartOffline {

    private static ServiceRegistry serviceRegistry;

    @Resource
    public void setServiceRegistry(ServiceRegistry serviceRegistry) {
        SpringCloudSmartOffline.serviceRegistry = serviceRegistry;
    }

    @Override
    public void offline() {
        log.warn("======springCloud服务准备下线======");
        if (serviceRegistry != null) {
            serviceRegistry.close();
        }
        log.warn("======springCloud服务已经下线======");
    }
}
