package qnvip.data.overview.config;

import com.qnvip.common.base.SmartOffline;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.registry.support.AbstractRegistryFactory;

/**
 * Dubbo优雅下线实现
 *
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
public class DubboSmartOffline implements SmartOffline {

    @Override
    public void offline() {
        log.warn("======dubbo服务准备下线======");
        AbstractRegistryFactory.destroyAll();
        log.warn("======dubbo服务已经下线======");
    }
}
