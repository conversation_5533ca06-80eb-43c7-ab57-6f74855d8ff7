package qnvip.data.overview.config;

import com.aliyun.openservices.ons.api.Consumer;
import com.qnvip.common.base.SmartOffline;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * RocketMQ优雅下线实现
 *
 * <AUTHOR>
 * @since 2024/12/04
 */
@Slf4j
@Component
public class RocketMqSmartOffline implements SmartOffline {

    private static final List<Consumer> consumers = new ArrayList<>();

    /**
     * 添加消费者到下线管理列表
     * 
     * @param consumer 消费者实例
     */
    public static void addConsumer(Consumer consumer) {
        if (consumer != null) {
            consumers.add(consumer);
            log.info("添加RocketMQ消费者到优雅下线管理列表，当前消费者数量: {}", consumers.size());
        }
    }

    @Override
    public void offline() {
        log.warn("======RocketMQ消费者准备下线======");
        for (Consumer consumer : consumers) {
            try {
                if (consumer != null) {
                    consumer.shutdown();
                    log.info("RocketMQ消费者已关闭");
                }
            } catch (Exception e) {
                log.error("关闭RocketMQ消费者异常", e);
            }
        }
        consumers.clear();
        log.warn("======RocketMQ消费者已经下线======");
    }
}
