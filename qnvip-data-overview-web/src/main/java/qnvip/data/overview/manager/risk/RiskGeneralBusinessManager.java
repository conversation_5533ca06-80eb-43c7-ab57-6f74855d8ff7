package qnvip.data.overview.manager.risk;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.risk.RiskGeneralCountDO;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.domain.risk.StatisticalDimension;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.SceneConfigEnum;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.enums.risk.BondSectionEnum;
import qnvip.data.overview.request.RiskOverdueRequest;
import qnvip.data.overview.service.risk.DataviewRiskStatisticalDimensionService;
import qnvip.data.overview.service.risk.RiskGeneralCountService;
import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
import qnvip.data.overview.util.*;
import qnvip.data.overview.util.dataindicators.IndicatorsUtil;
import qnvip.data.overview.vo.risk.RiskGeneralBusinessVO;
import qnvip.data.overview.vo.risk.RiskGeneralVO;
import qnvip.rent.common.base.SingleResult;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/5/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskGeneralBusinessManager {

    private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final RiskGeneralCountService riskGeneralCountService;
    private final DataviewRiskStatisticalDimensionService dataviewRiskStatisticalDimensionService;
    private static final int LOOP_COUNT = 30;

    private final AtomicInteger ATOMIC_INTEGER = new AtomicInteger();
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 5, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue<>(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskOrderOverdue-" + ATOMIC_INTEGER.incrementAndGet());
        return t;
    });


    public SingleResult<RiskGeneralVO> getList(RiskOverdueRequest request) {
        // 统计类型   {value: 1,label: '金额',},{value: 2,label: '单量',},{value: 3,label: '单量比',},{value: 4,label: '金额比',}
        String countType = request.getCountType();
        // 统计维度  1: '风控等级', 2: '风控策略', 3: '审核方式', 4: '方案', 5: '保证金百分比', 6: '场景值', 7: '导流商', 8: '芝麻分等级', 9: '是否租物'
        String countWay = request.getCountWay();
        //统计项 {value: 1,label: '逾期金额',},{value: 2,label: '保证金',},{value: 3,label: '其他收入',}
        String countItem = request.getCountItem();
        Integer renewWay = request.getRenewWay(); //是否自动续租
        //宽限期,模型3天
        Integer overdueDay = request.getOverdueDay();
        //平台类型
        String miniType = request.getMiniType();
        Integer minScore = request.getMinScore();
        Integer maxScore = request.getMaxScore();
        String rentStartDate = request.getRentStartDate();
        String rentEndDate = request.getRentEndDate();
        List<Integer> miniTypeList = IndicatorsUtil.rentMiniTypeStrToList(miniType);
        RiskGeneralVO riskRenewalVO = new RiskGeneralVO();
        // 获取统计信息
        CopyOnWriteArrayList<RiskGeneralBusinessVO> resultList = Lists.newCopyOnWriteArrayList();
        AtomicInteger maxTerm = new AtomicInteger();
        //分组
        LocalDate startDate = LocalDateTime.parse(rentStartDate, YYYY_MM_DD_HH_MM_SS).toLocalDate();
        LocalDate endTime = LocalDateTime.parse(rentEndDate, YYYY_MM_DD_HH_MM_SS).toLocalDate();

        List<DateSplitUtils.DateSplit> dateSplits = DateSplitUtils.splitByMonth(startDate, endTime, 1);
        if ((maxScore != null && minScore != null) && maxScore < minScore) {
            return SingleResult.error("最大评分大于最小评分");
        }
        CountDownLatch countDownLatch = new CountDownLatch(dateSplits.size());
        // 每一期一个线程
        for (DateSplitUtils.DateSplit dateSplit : dateSplits) {
            threadPoolExecutor.execute(() -> {
                ThreadLocalCacheUtil.put("countWay", countWay);
                ThreadLocalCacheUtil.put("countType", countType);
                ThreadLocalCacheUtil.put("countItem", countItem);
                try {
                    String startDateTimeStr = dateSplit.getStartDateTimeStr();
                    // 起租月
                    String rentStartDay = startDateTimeStr.substring(0, 7);
                    // 查询起租月的汇总数据 countMonth
                    List<RiskGeneralCountDO> value  = riskGeneralCountService.getList(rentStartDay, overdueDay);
                    // 过滤数据
                    value = filterList(countWay, renewWay, minScore, maxScore, miniTypeList, value);
                    if (CollUtil.isEmpty(value)) {
                        return;
                    }
                    Integer countDayMaxTerm = value.stream().map(RiskGeneralCountDO::getMaxTerm)
                            .max(Comparator.comparing(Function.identity())).get();
                    maxTerm.set(Math.max(maxTerm.get(), countDayMaxTerm));
                    RiskGeneralBusinessVO riskGeneralBusinessVO = RiskGeneralBusinessVO.getInstance();
                    RiskGeneralBusinessVO.InnerColumnVO dateTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();

                    if (CollUtil.isNotEmpty(miniTypeList)) {
                        Map<String, List<RiskGeneralCountDO>> overdue2List =
                                value.stream().peek(e -> e.setPlatform(MiniTypeEnum.getPlatformByCode(e.getMiniType())))
                                        .collect(Collectors.groupingBy(RiskGeneralCountDO::getPlatform));
                        // 里面会 计算起租日总合计
                        getPlatformList(riskGeneralBusinessVO, overdue2List, rentStartDay);
                        overdue2List.clear();
                    } else {
                        Map<String, List<RiskGeneralCountDO>> dtoOtherWay2List = Maps.newHashMap();
                        getOtherType(dtoOtherWay2List, value);
                        if (MapUtil.isNotEmpty(dtoOtherWay2List)) {
                            // 里面会 计算起租日总合计
                            getOtherTypeList(dtoOtherWay2List, riskGeneralBusinessVO, rentStartDay);
                        }
                    }
                    if (CollUtil.isEmpty(riskGeneralBusinessVO.getList())) {
                        // 下一层为空，外层也不展示
                        return;
                    }
                    // 计算起租日总合计
                    translateList(rentStartDay, value, dateTotalVO);
                    riskGeneralBusinessVO.setDesc(rentStartDay);
//                    // 给前端展示用的
                    riskGeneralBusinessVO.setCode(1000);
                    riskGeneralBusinessVO.setTotal(dateTotalVO);
                    //前端显示期数,也就是显示的月数
                    Long monthDiff = getMonthDiff(rentStartDay, value);
                    riskGeneralBusinessVO.setTerm(monthDiff.intValue());
                    value.clear();
                    resultList.add(riskGeneralBusinessVO);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                    ThreadLocalCacheUtil.release();
                }
            });
        }
        try {
            countDownLatch.await();
            riskRenewalVO.setMaxTerm(maxTerm.get());
            List<RiskGeneralBusinessVO> collect = resultList.stream().
                    sorted(Comparator.comparing(RiskGeneralBusinessVO::getDesc)).collect(Collectors.toList());
            riskRenewalVO.setList(collect);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return SingleResult.of(riskRenewalVO);
    }

    private List<RiskGeneralCountDO> filterList(String countWay, Integer renewWay, Integer minScore, Integer maxScore, List<Integer> finalMiniTypeList, List<RiskGeneralCountDO> value) {
        // 过滤 miniType 平台
        Predicate<RiskGeneralCountDO> predicate = o -> !CollUtil.isNotEmpty(finalMiniTypeList) || finalMiniTypeList.contains(o.getMiniType());
        value = getFilterList(value, predicate);
        // 过滤统计纬度
        List<String> countWays = CollUtil.toList(countWay.split(","));
        String type = countWays.get(0);
        if (countWays.size() > 1) {
            switch (type) {
                case "1":
                    predicate = o -> countWays.contains(o.getRiskLevel());
                    break;
                case "2":
                    predicate = o -> countWays.contains(o.getRiskStrategy());
                    break;
                case "3":
                    predicate = o -> "人工审核".equals(countWays.get(1)) != o.getAuditType().equals("1");
                    break;
                case "4":
                    predicate = o -> countWays.contains(FinanceTypeEnum.getTitleByType(o.getFinanceType()));
                    break;
                // 保证金比
                case "5":
                    predicate = o -> countWays.contains(o.getBondRateInterval().split(",")[0]);
                    break;
                case "6":
                    predicate = o -> countWays.contains(o.getScene());
                    break;
                case "7":
                    predicate = o -> countWays.contains(o.getQuotientName());
                    break;
                // 信用等级
                case "8":
                    predicate = o -> countWays.contains(o.getZmfLevel());
                    break;
                // 是否租物品
                case "9":
                    predicate = o -> countWays.contains(o.getRentType());
                    break;
                case "12":
                    predicate = o -> countWays.contains(o.getDepositFreeType());
                    break;
                case "13":
                    predicate = o -> countWays.contains(o.getTrafficType());
                    break;
                case "14":
                    predicate = o -> countWays.contains(o.getFinancialSolutions());
                    break;
                case "15":
                    predicate = o -> countWays.contains(o.getApplicationName());
                    break;
                case "16":
                    predicate = o -> countWays.contains(o.getEquipmentState());
                    break;
                case "17":
                    predicate = o -> countWays.contains(o.getSupervisedMachine());
                    break;
                case "18":
                    predicate = o -> countWays.contains(o.getMachineType());
                    break;
                default:
                    predicate = null;
            }
            value = getFilterList(value, predicate);
        }

        // 过滤续租方式
        if (renewWay != null) {
            predicate = o ->  renewWay.equals(o.getRenewWay());
            value = getFilterList(value, predicate);
        }
        //过滤分数 maxScore 和  minScore 都不为空，取分数位于两者直接的数据 等
        if (minScore != null  &&  maxScore != null){
            predicate =
                    o -> o.getScore() >= minScore && o.getScore() <= maxScore;
            value = getFilterList(value, predicate);
        }

        return value;
    }

    private List<RiskGeneralCountDO> getFilterList(List<RiskGeneralCountDO> list, Predicate<RiskGeneralCountDO> predicate) {
        return list.stream().filter(predicate).collect(Collectors.toList());
    }

    private void getPlatformList(
            RiskGeneralBusinessVO riskGeneralBusinessVO,
            Map<String, List<RiskGeneralCountDO>> overdue2List, String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        //明细
        for (Map.Entry<String, List<RiskGeneralCountDO>> platformEntry : overdue2List.entrySet()) {
            String platform = platformEntry.getKey();

            List<RiskGeneralCountDO> dtoList = overdue2List.get(platform);
            if (CollUtil.isEmpty(dtoList)) {
                continue;
            }
            Map<Integer, List<RiskGeneralCountDO>> dtoMiniType2List =
                    dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getMiniType));

            platformDetailVO = RiskGeneralBusinessVO.getInstance();

            getMiniTypeList(platformDetailVO, dtoMiniType2List, rentStartDay);
            if (CollUtil.isEmpty(platformDetailVO.getList())) {
                // 下一层为空，外层也不展示
                continue;
            }
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            platformDetailVO.setDesc(platform);
            platformDetailVO.setCode(SceneConfigEnum.getSortByName(platform));

            // 计算起租日金额
            translateList(rentStartDay, dtoList, platformTotalVO);

            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, dtoList);
            platformDetailVO.setTerm(monthDiff.intValue());
            detailVOList.add(platformDetailVO);

        }
        //合计
        detailVOList.sort(Comparator.comparing(RiskGeneralBusinessVO::getCode));
        riskGeneralBusinessVO.setList(detailVOList);
    }

    private void getMiniTypeList(
            RiskGeneralBusinessVO riskGeneralBusinessVO,
            Map<Integer, List<RiskGeneralCountDO>> dtoMiniType2List,
            String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        //明细
        for (Map.Entry<Integer, List<RiskGeneralCountDO>> platformEntry : dtoMiniType2List.entrySet()) {
            platformDetailVO = RiskGeneralBusinessVO.getInstance();
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            Integer miniType = platformEntry.getKey();

            List<RiskGeneralCountDO> dtoList = dtoMiniType2List.get(miniType);
            if (CollUtil.isEmpty(dtoList)) {
                continue;
            }
            platformDetailVO.setDesc(MiniTypeEnum.getValueByCode(miniType));
            translateList(rentStartDay, dtoList, platformTotalVO);
            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, dtoList);
            platformDetailVO.setTerm(monthDiff.intValue());
            Map<String, List<RiskGeneralCountDO>> dtoOtherWay2List = Maps.newHashMap();
            getOtherType(dtoOtherWay2List, dtoList);
            if (MapUtil.isNotEmpty(dtoOtherWay2List)) {
                getOtherTypeList(dtoOtherWay2List, platformDetailVO, rentStartDay);
            }
            if (CollUtil.isEmpty(platformDetailVO.getList())) {
                // 下一层为空，外层也不展示
                continue;
            }
            detailVOList.add(platformDetailVO);
        }
        //合计
        riskGeneralBusinessVO.setList(detailVOList);
    }

    private void getOtherType(
            Map<String, List<RiskGeneralCountDO>> map2,
            List<RiskGeneralCountDO> dtoList) {

        String countWay = (String) Optional.ofNullable(ThreadLocalCacheUtil.get("countWay")).orElse("");
        String[] split = countWay.split(",");
        int type = Integer.parseInt(split[0]);
        Map<String, List<RiskGeneralCountDO>> dtoOtherWay2List;
        switch (type) {
            case 1:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getRiskLevel));
                break;
            case 2:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getRiskStrategy));
                break;
            case 3:
                dtoOtherWay2List =
                        dtoList.stream().peek(e -> e.setAuditTypeDesc("1".equals(e.getAuditType()) ? "自动审核" : "人工审核"))
                                .collect(Collectors.groupingBy(RiskGeneralCountDO::getAuditTypeDesc));
                break;
            case 4:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(e -> FinanceTypeEnum.getTitleByType(e.getFinanceType())));
                break;
            // 保证金比
            case 5:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getBondRateInterval));
                break;
            case 6:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getScene));
                break;
            // 场景值
            case 7:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getQuotientName));
                break;
            case 8:
                dtoOtherWay2List =
                        dtoList.stream().sorted(Comparator.comparing(o -> BondSectionEnum.getZmfValueByCode(o.getZmfLevel()))).
                                collect(Collectors.groupingBy(RiskGeneralCountDO::getZmfLevel));
                break;
            // 是否租物品
            case 9:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getRentType));
                break;
            case 12:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getDepositFreeType));
                break;
            case 13:
                 dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getTrafficType));
                break;
            case 14:
                 dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getFinancialSolutions));
                break;
            case 15:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getApplicationName));
                break;
            case 16:
                 dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getEquipmentState));
                break;
            case 17:
                 dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getSupervisedMachine));
                break;
            case 18:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getMachineType));
                break;
            default:
                dtoOtherWay2List = Maps.newHashMap();
        }
        map2.putAll(dtoOtherWay2List);
    }


    private void getOtherTypeList(Map<String, List<RiskGeneralCountDO>> dtoOtherWay2List,
                                  RiskGeneralBusinessVO riskGeneralBusinessVO,
                                  String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        String countWay = (String) Optional.ofNullable(ThreadLocalCacheUtil.get("countWay")).orElse(0);
        int type = Integer.parseInt(countWay.split(",")[0]);
        //明细
        for (Map.Entry<String, List<RiskGeneralCountDO>> platformEntry : dtoOtherWay2List.entrySet()) {
            platformDetailVO = RiskGeneralBusinessVO.getInstance();
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            String key = platformEntry.getKey();

            if (Integer.valueOf(5).equals(type)) {
                String[] split = key.split(",");
                key = split[0];
                platformDetailVO.setCode(Integer.parseInt(split[1]));
            }

            List<RiskGeneralCountDO> platformList = platformEntry.getValue();
            if (Integer.valueOf(3).equals(type)) {
                if ("人工审核".equals(key)) {
                    Map<String, List<RiskGeneralCountDO>> auditorId2list =
                            platformList.stream().collect(Collectors.groupingBy(RiskGeneralCountDO::getAuditTypeName));
                    getAuditorIdList(auditorId2list, platformDetailVO, rentStartDay);
                }
            }
            if (StringUtils.isBlank(key) || key.contains("建议")) {
                continue;
            }
            platformDetailVO.setDesc(key);
            if (key.contains("风控等级")) {
                String substring = key.substring(4);
                int level = CalculateUtil.toDecimal(substring).intValue();
                platformDetailVO.setCode(level);
            }

            translateList(rentStartDay, platformList, platformTotalVO);
            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, platformList);
            platformDetailVO.setTerm(monthDiff.intValue());
            detailVOList.add(platformDetailVO);

        }
        if (Integer.valueOf(1).equals(type) || Integer.valueOf(5).equals(type)) {
            detailVOList.sort(Comparator.comparing(RiskGeneralBusinessVO::getCode));
        } else if (Integer.valueOf(8).equals(type)) {
            detailVOList.sort(Comparator.comparing(o -> BondSectionEnum.getZmfValueByCode(o.getDesc())));
        }
        //合计
        riskGeneralBusinessVO.setList(detailVOList);
    }


    private void getAuditorIdList(Map<String, List<RiskGeneralCountDO>> auditorId2list,
                                  RiskGeneralBusinessVO platformDetailVO,
                                  String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO auditorIdTotalVO;
        RiskGeneralBusinessVO auditorVO;
        //明细
        for (Map.Entry<String, List<RiskGeneralCountDO>> platformEntry : auditorId2list.entrySet()) {
            auditorIdTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            auditorVO = RiskGeneralBusinessVO.getInstance();
            String key = platformEntry.getKey();
//            String name = ArtificialEnum.getNameById(Integer.parseInt(key));
            String name = key;
            List<RiskGeneralCountDO> platformList = platformEntry.getValue();
            auditorVO.setDesc(name);
            translateList(rentStartDay, platformList, auditorIdTotalVO);
            auditorVO.setTotal(auditorIdTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, platformList);
            auditorVO.setTerm(monthDiff.intValue());
            detailVOList.add(auditorVO);

        }
        //合计
        platformDetailVO.setList(detailVOList);
    }


    /**
     * @param orderDos
     * @param columnVO
     */
    private void translateList(String rentStartDay, List<RiskGeneralCountDO> orderDos,
                               RiskGeneralBusinessVO.InnerColumnVO columnVO) {
        // 租期内订单的统计信息
        BigDecimal rentTotal = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getRentTotal);
        //逾期总订单数
        BigDecimal totalOrderCnt = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getTotalOrderCnt);
        //逾期买断金
        BigDecimal buyoutAmtTotal = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getBuyoutAmt);
        //逾期售前优惠总额
        BigDecimal discountsTotal = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getDiscountTotal);
        //优惠券金额
        BigDecimal couponDiscountAmtTotal=ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getCouponDiscountAmtTotal);

        //12期以内的总租金计算
        BigDecimal rentTotalInner = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getRentTotalInner);

        // 总租金
        BigDecimal renewTotalRent=ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getRenewTotalRent);

        //剩余可用预授权实付押金
//        BigDecimal bondRestFundAmountTotal = ObjectUtils.getSum(orderDos, RiskGeneralCountDO::getBondRestFundAmountTotal);

        // 分母
        // hefeng : 业务总额=总租金+买断金-couponDiscountAmtTotal,rentTotal实际就是应还本金,  12期+
        BigDecimal businessAmtTotal = rentTotal.subtract(couponDiscountAmtTotal);

        // 12 期以内的业务总额
        BigDecimal businessAmtTotalInner = rentTotalInner.add(buyoutAmtTotal).subtract(couponDiscountAmtTotal);
       // log.info("通用业务总额计算明细: "+"businessAmtTotal="+businessAmtTotal+" rentTotal="+rentTotal+"   buyoutAmtTotal="+buyoutAmtTotal+"  couponDiscountAmtTotal"+couponDiscountAmtTotal+"  businessAmtTotalInner"+businessAmtTotalInner);

        rentStartDay = rentStartDay.concat("-01");
        LocalDateTime endTime = LocalDateTime.now().withDayOfMonth(1);
        LocalDate startTime =  LocalDate.parse(rentStartDay);
        //计算月份差
        Long monthDiff = ChronoUnit.MONTHS.between(startTime, endTime);
        if (startTime.isAfter(LocalDate.parse("2024-03-01"))) {
            monthDiff = monthDiff + 1;
        }
        RiskGeneralBusinessVO.InnerRiskVO[] mobArray = new RiskGeneralBusinessVO.InnerRiskVO[monthDiff.intValue() + 1];
        monthDiff.intValue();
        //30期
        for (int i = 1; i <= monthDiff; i++) {
            try {
                RiskGeneralBusinessVO.InnerRiskVO mob = getInnerRiskVO(
                        orderDos,
                        totalOrderCnt,
                        businessAmtTotalInner,
                        buyoutAmtTotal,
                        renewTotalRent,
                        i,
                        getMethodReference(i, "getBuyoutAmt"),
                        getMethodReference(i, "getBondAmt"),
                        getMethodReference(i, "getDiscountReturnAmt"),
                        getMethodReference(i, "getBeforeDiscount"),
                        getMethodReference(i, "getParentBeforeDiscount"),
                        getMethodReference(i, "getParentDiscountReturnAmt"),
                        getMethodReference(i, "getCountTerm"),
                        getMethodReference(i, "getNotPayTerm"),
                        getMethodReference(i, "getOverdueFine"),
                        getMethodReference(i, "getRenewAmt"),
                        getMethodReference(i, "getSurplusBondAmt"),
                        getMethodReference(i, "getBondRestFundAmount"),
                        getMethodReference(i, "getDiffPricingDiscountAmt"),
                        getMethodReference(i, "getCouponDiscountAmt")
                );
                mobArray[i] = mob;
            } catch (Exception e) {
                // 处理异常，例如记录日志
                e.printStackTrace();
            }
        }
        setColumnVO(columnVO, mobArray, monthDiff.intValue());

        RiskGeneralBusinessVO.InnerRiskVO innerRiskVO = RiskGeneralBusinessVO.InnerRiskVO.getInstance();
        // 设置业务总额
        innerRiskVO.setOverdueRentTotal(businessAmtTotalInner);
        innerRiskVO.setOverdueOrderCnt(totalOrderCnt);
        columnVO.setAmtTotal(innerRiskVO);

    }

    /**
     * 设置列数据  setMob1...setMob30
     * @param columnVO
     * @param mobArray
     */
    private void setColumnVO(RiskGeneralBusinessVO.InnerColumnVO columnVO, RiskGeneralBusinessVO.InnerRiskVO[] mobArray, int monthDiff) {
        for (int i = 1; i <= monthDiff; i++) {
            try {
                String methodName = "setMob" + i;
                Method method = RiskGeneralBusinessVO.InnerColumnVO.class.getMethod(methodName, RiskGeneralBusinessVO.InnerRiskVO.class);
                method.invoke(columnVO, mobArray[i]);
            } catch (Exception e) {
                // 记录异常日志
                log.error("Error setting MOB{}:  {}", e, i);
            }
        }
    }

    // 根据参数动态获取方法引用每期是的数据
    private Function<RiskGeneralCountDO, BigDecimal> getMethodReference(int term, String methodName) {
        try {
            Method method = RiskGeneralCountDO.class.getMethod(methodName + term);
            return obj -> {
                try {
                    return (BigDecimal) method.invoke(obj);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            };
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    private synchronized  <T> RiskGeneralBusinessVO.InnerRiskVO getInnerRiskVO(List<T> orderDos,
                                                                 BigDecimal totalOrderCnt,
                                                                 BigDecimal businessAmtTotal,
                                                                 BigDecimal buyoutAmtTotal,
                                                                 BigDecimal renewTotalRent,
                                                                 Integer term,
                                                                 Function<T, BigDecimal> column1,
                                                                 Function<T, BigDecimal> column2,
                                                                 Function<T, BigDecimal> column3,
                                                                 Function<T, BigDecimal> column4,
                                                                 Function<T, BigDecimal> column5,
                                                                 Function<T, BigDecimal> column6,
                                                                 Function<T, BigDecimal> column7,
                                                                 Function<T, BigDecimal> column8,
                                                                 Function<T, BigDecimal> column9,
                                                                 Function<T, BigDecimal> column10,
                                                                 Function<T, BigDecimal> column11,
                                                                 Function<T, BigDecimal> column12,
                                                                 Function<T, BigDecimal> column13,
                                                                 Function<T, BigDecimal> column14) {
        // {value: 1,label: '逾期金额',},{value: 2,label: '保证金',},{value: 3,label: '其他收入',}
        String countItem = ThreadLocalCacheUtil.get("countItem");
        // {value: 1,label: '金额',},{value: 2,label: '单量',},{value: 3,label: '单量比',},{value: 4,label: '金额比',}
        String countType = ThreadLocalCacheUtil.get("countType");

        BigDecimal overdueBuyoutAmt = ObjectUtils.getSum(orderDos, column1); // 买断金
        BigDecimal overdueBondAmt = ObjectUtils.getSum(orderDos, column2);  // 保证金
        BigDecimal discountReturnAmt = ObjectUtils.getSum(orderDos, column3); // 折扣返还金额
        BigDecimal overdueBeforeDiscount = ObjectUtils.getSum(orderDos, column4); // 核销总金额
        BigDecimal overDueParentBeforeDiscount = ObjectUtils.getSum(orderDos, column5); // 父订单未使用售前优惠金额
        BigDecimal overDueParentDiscountAmt = ObjectUtils.getSum(orderDos, column6); // 父订单折扣返还金额
        BigDecimal overdueOrderCnt = ObjectUtils.getSum(orderDos, column7); // 每一期的逾期订单数
        BigDecimal notPayTerm = ObjectUtils.getSum(orderDos, column8); // 应还本金总额
        BigDecimal overdueFines = ObjectUtils.getSum(orderDos, column9); //逾期滞纳金总额
        BigDecimal renewAmt = ObjectUtils.getSum(orderDos, column10); // 每期的续租总租金
        BigDecimal surplusBondAmt = ObjectUtils.getSum(orderDos, column11); // 抵扣完剩余保证金
        BigDecimal bondRestFundAmount = ObjectUtils.getSum(orderDos, column12); // 剩余可用预授权实付押金
        BigDecimal diffPricingDiscountAmt = ObjectUtils.getSum(orderDos, column13); // 差异化优惠金额
        BigDecimal couponDiscountAmt = ObjectUtils.getSum(orderDos, column14); // 优惠券金额
        RiskGeneralBusinessVO.InnerRiskVO detailVO = RiskGeneralBusinessVO.InnerRiskVO.getInstance();


        //优惠返回+滞纳金+逾期订单未使用售前优惠金额
        BigDecimal otherAmt = discountReturnAmt.add(overdueFines);
        // 父订单折扣返还金额 + 逾期滞纳金总额
        BigDecimal parentOtherAmt = overDueParentDiscountAmt.add(overdueFines);

        assert countItem != null;
        List<String> countItems = Arrays.asList(countItem.split(","));

        // 应还本金总额 + 买断金
//        BigDecimal amount = notPayTerm.add(overdueBuyoutAmt);
        BigDecimal amount = notPayTerm.subtract(bondRestFundAmount).subtract(couponDiscountAmt);
        // 1逾期比 2保证金比 3其他收入比

        // 保证金比：逾期订单已收保证金/业务总额
        // 旧的逻辑
//        BigDecimal bondRate =
//                CalculateUtil.divAndReturnZero(overdueBondAmt,
//                        businessAmtTotal, 4);
        //overdueBondAmt 改成 surplusBondAmt
        BigDecimal bondRate =
                CalculateUtil.divAndReturnZero(surplusBondAmt,
                        businessAmtTotal, 4);

        // todo 逾期金额比如何把续租订单的逾期金额加进去
        // 逾期比：逾期订单未还租金+未还买断金-逾期订单未使用售前优惠金额/业务总额
        // 旧的 BigDecimal overdueAmt = notPayTerm.add(overdueBuyoutAmt).subtract(overDueParentBeforeDiscount).subtract(overdueBeforeDiscount);

        BigDecimal overdueAmt = notPayTerm
//                .add(overdueBuyoutAmt)
                .subtract(bondRestFundAmount).subtract(couponDiscountAmt);

        BigDecimal overdueRate =
                CalculateUtil.mul(CalculateUtil.divAndReturnZero(overdueAmt,
                        businessAmtTotal, 4), -1);

        // 其他收入比：优惠返回+逾期订单未使用售前优惠金额+滞纳金/业务总额
        BigDecimal finalOtherAmt = notPayTerm.add(renewAmt).subtract(parentOtherAmt).subtract(otherAmt);
        BigDecimal otherRate =
                CalculateUtil.mul(CalculateUtil.divAndReturnZero(finalOtherAmt,
                        businessAmtTotal, 4), -1);

        // 逾期订单比:逾期订单量/业务总单量
        BigDecimal overdueOrderRate =
                CalculateUtil.divAndReturnZero(overdueOrderCnt,
                        totalOrderCnt, 4);
        assert countType != null;
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());


        if (collect.contains(2)) {  //单量
            // 返回JSON:逾期订单量
            detailVO.setOverdueOrderCnt(overdueOrderCnt);
        }
        if (collect.contains(3)) { // 单量比
            //返回JSON:逾期订单率
            detailVO.setOverdueOrderRate(CalculateUtil.mul(CalculateUtil.mul(overdueOrderRate, 100, 2), -1));
        }
        if (collect.contains(4)) { // 金额比

            // 百分比
            BigDecimal total = BigDecimal.ZERO;
            if (countItems.contains("1")) { // 逾期金额
                total = total.add(overdueRate);
            }
            if (countItems.contains("2")) { // 保证金
                total = total.add(bondRate);
                amount = amount.subtract(overdueBondAmt);

            }
            if (countItems.contains("3")) { // 其他收入
                total = total.add(otherRate);
                amount = amount.subtract(overdueBuyoutAmt).add(renewAmt).subtract(otherAmt);
            }

//            log.info("通用模块逾期率计算明细和结果为:  overdueRate="+overdueRate+" overdueAmt="+overdueAmt+" notPayTerm="+notPayTerm+" overdueBuyoutAmt="+overdueBuyoutAmt+"  bondRate="+bondRate+" total="+total
//                    +" surplusBondAmt="+surplusBondAmt+" bondRestFundAmount="+bondRestFundAmount+" diffPricingDiscountAmt="+diffPricingDiscountAmt+" couponDiscountAmt="+couponDiscountAmt+" businessAmtTotal="+businessAmtTotal+"总单量totalOrderCnt="+totalOrderCnt+"  逾期订单量overdueOrderCnt="+overdueOrderCnt+"期数term="+term);

            // 返回JSON:合计比
            detailVO.setCountRate(CalculateUtil.mul(total, 100, 2));
        }


        // 金额
        if (collect.contains(1)) {
            // 返回JSON: 订单总租金
            detailVO.setOverdueRentTotal(amount);
        }
        return detailVO;
    }

    private Long getMonthDiff(String rentStartDay, List<RiskGeneralCountDO> overdueList) {
        // 判断当前起租日的封账日
        List<Integer> collect =
                Arrays.stream(rentStartDay.split("-")).map(Integer::parseInt).collect(Collectors.toList());
        LocalDate start = LocalDate.of(collect.get(0), collect.get(1), 1);

        LocalDate end = LocalDate.now();

        //获取起租日的年月
        String  rentStartDate=String.valueOf(start.getYear())+start.getMonth().getValue();
        //获取当前时间的年月
        String  currentEndDate=String.valueOf(end.getYear())+end.getMonth().getValue();

        LocalDate date1 = LocalDate.of(2024, 4, 1);

        if(rentStartDate.equals(currentEndDate)){
            return 0L;
        }

        Integer maxTerm = overdueList.stream().map(RiskGeneralCountDO::getMaxTerm)
                .max(Comparator.comparing(Function.identity())).get();
        return Math.min(start.isBefore(date1)?ChronoUnit.MONTHS.between(start, end):ChronoUnit.MONTHS.between(start, end)+1, maxTerm);
    }


    public Map<Integer, Collection<String>> getSelector(String startTime,
                                                        String endTime,
                                                        String miniType,
                                                        Integer overdueDay) {
        Map<Integer, Collection<String>> map = Maps.newHashMap();
        HashSet<String> auditTypeSet = Sets.newHashSet();
        HashSet<String> riskLevelSet = Sets.newHashSet();
        HashSet<String> riskStrategySet = Sets.newHashSet();
        HashSet<String> financeTypeSet = Sets.newHashSet();
        HashSet<String> bondRateSet = Sets.newHashSet();
        HashSet<String> sceneSet = Sets.newHashSet();
        HashSet<String> quotientSet = Sets.newHashSet();
        HashSet<String> zmfSet = Sets.newHashSet();
        HashSet<String> mianyaSet = Sets.newHashSet();
        List<Integer> miniTypeList = null;
        if (StringUtils.isNotEmpty(miniType)) {
            miniTypeList =
                    Arrays.stream(miniType.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        List<RiskGeneralOrderOverdueDO> auditType =
                riskGeneralOrderOverdueService.getAuditType(startTime, endTime, miniTypeList, overdueDay);
        for (RiskGeneralOrderOverdueDO riskOrderOverdueDO : auditType) {

            auditTypeSet.add("1".equals(riskOrderOverdueDO.getAuditType()) ? "自动审核" : "人工审核");
            riskLevelSet.add(riskOrderOverdueDO.getRiskLevel());
            riskStrategySet.add(riskOrderOverdueDO.getRiskStrategy());
            sceneSet.add(riskOrderOverdueDO.getScene());
            quotientSet.add(riskOrderOverdueDO.getQuotientName());
            zmfSet.add(riskOrderOverdueDO.getZmfLevel());
            mianyaSet.add(riskOrderOverdueDO.getIsRent());
            financeTypeSet.add(FinanceTypeEnum.getTitleByType(Integer.parseInt(riskOrderOverdueDO.getFinanceType())));
        }
        List<RiskGeneralOrderOverdueDO> bondRate =
                riskGeneralOrderOverdueService.getBondRate(startTime, endTime, miniTypeList, overdueDay);
        for (RiskGeneralOrderOverdueDO riskOrderOverdueDO : bondRate) {
            String[] split = riskOrderOverdueDO.getBondRate().split(",");
            bondRateSet.add(split[0]);
        }
        Set<String> riskLevelFinalSet =
                riskLevelSet.stream().filter(o -> StringUtils.isNotBlank(o) && !o.contains("建议"))
                        .collect(Collectors.toCollection(Sets::newHashSet));
        map.put(1, riskLevelFinalSet);
        Set<String> riskStrategyFinalSet =
                riskStrategySet.stream().filter(StringUtils::isNotBlank)
                        .collect(Collectors.toCollection(Sets::newHashSet));
        map.put(2, riskStrategyFinalSet);
        map.put(3, auditTypeSet);
        map.put(4, financeTypeSet);
        map.put(5, bondRateSet);
        map.put(6, sceneSet);
        map.put(7, quotientSet);
        List<String> collect = zmfSet.stream().
                sorted(Comparator.comparing(BondSectionEnum::getZmfValueByCode))
                .collect(Collectors.toList());
        map.put(8, collect);
        map.put(9, mianyaSet);
        return map;
    }

    public void export(RiskOverdueRequest request,
                       HttpServletResponse response) {
        ExportParams empExportParams = new ExportParams();
        empExportParams.setStyle(MyExcelExportStyler.class);
        empExportParams.setTitle("风控大盘分期明细");
        empExportParams.setSheetName("风控大盘分期明细");

        SingleResult<RiskGeneralVO> result = getList(request);
        // 创建sheet1使用得map
        // 递归将树形结构打平
        RiskGeneralVO vo = result.getData();
        if (Objects.isNull(vo)) {
            throw new RuntimeException("导出失败,数据为空");
        }
        List<RiskGeneralBusinessVO> list = vo.getList();
        List<Map<String, Object>> copyList = Lists.newArrayList();
        copyList.addAll(fetchExportData(list, request.getCountType()));
        Integer maxTerm = list.stream().mapToInt(RiskGeneralBusinessVO::getTerm).max().getAsInt();
        List<ExcelExportEntity> excelExportEntities = setExportExcelStyle(maxTerm, request.getCountType());
        EasyPoiUtils.dynamicColumnExport(empExportParams, excelExportEntities, copyList, "风控大盘分期明细.xls", response);
    }

    /**
     * 递归将树形结构打平
     *
     * @param list
     * @return
     */
    private List<Map<String, Object>> fetchExportData(List<RiskGeneralBusinessVO> list, String countType) {

        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> copyList = Lists.newArrayList();
        for (RiskGeneralBusinessVO vo : list) {
            copyList.add(translateAmtVO(vo, countType));
            copyList.addAll(fetchExportData(vo.getList(), countType));
        }
        return copyList;
    }

    /**
     * 定义表格样式
     *
     * @return java.util.List<cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity>
     * <AUTHOR>
     * @date 2019/6/21
     * @since 2.8.2
     */
    private List<ExcelExportEntity> setExportExcelStyle(Integer maxTerm, String countType) {
        //定义表格列名,该集合存放的就是表格的列明，每个对象就是表格中的一列
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());

        List<ExcelExportEntity> modelList = new ArrayList<>();
        //该对象就是定义列属性的对象
        ExcelExportEntity excelentity;
        int orderNum = 1;
        //定义第一个列
        excelentity = new ExcelExportEntity("描述", "desc");
        excelentity.setWidth(20);
        excelentity.setHeight(10);
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        excelentity = new ExcelExportEntity("业务总额-逾期单量", "overdueOrderCnt");
        excelentity.setWidth(20);
        excelentity.setNumFormat("#");
        excelentity.setHeight(10);
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        excelentity = new ExcelExportEntity("业务总额-逾期金额数", "overdueOrderAmt");
        excelentity.setWidth(20);
        excelentity.setHeight(10);
        excelentity.setNumFormat("#.##");
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        //定义第四个列,这边就是动态生成的,跟用用户选择的日期范围,动态生成列的数量
        //设置一个集合,存放动态生成的列
        for (int i = 1; i <= maxTerm; i++) {
            if (collect.contains(1)) {
                excelentity = new ExcelExportEntity("mob" + i + "_金额", "mob" + i + "Amt");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#.##");
                modelList.add(excelentity);
            }
            if (collect.contains(2)) {
                excelentity = new ExcelExportEntity("mob" + i + "_单量", "mob" + i + "Cnt");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#");
                modelList.add(excelentity);
            }
            if (collect.contains(3)) {
                excelentity = new ExcelExportEntity("mob" + i + "_单量比", "mob" + i + "CntRate");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#.##");
                excelentity.setSuffix("%");
                modelList.add(excelentity);
            }
            if (collect.contains(4)) {
                excelentity = new ExcelExportEntity("mob" + i + "_金额比", "mob" + i + "AmtRate");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setNumFormat("#.##");
                excelentity.setOrderNum(orderNum++);
                excelentity.setSuffix("%");
                modelList.add(excelentity);
            }
        }

        return modelList;
    }


    /**
     * 金额数值导出转换
     */
    private Map<String, Object> translateAmtVO(RiskGeneralBusinessVO sourceVO, String countType) {
        RiskGeneralBusinessVO.InnerColumnVO vo = sourceVO.getTotal();
        Map<String, Object> map = new HashMap();
        map.put("desc", sourceVO.getDesc());
        map.put("overdueOrderCnt", vo.getAmtTotal().getOverdueOrderCnt());
        map.put("overdueOrderAmt", vo.getAmtTotal().getOverdueRentTotal());
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (collect.contains(1)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "Amt";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueRentTotal());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }
        }
        if (collect.contains(2)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "Cnt";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueOrderCnt());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }
        }
        if (collect.contains(3)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "CntRate";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueOrderRate());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }

        }

        if (collect.contains(4)) {

            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "AmtRate";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getCountRate());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }

        }
        return map;
    }


    static Map<String, Integer> countWayMap = new HashMap<>();
    static {
        countWayMap.put("风控等级", 1);
        countWayMap.put("风控策略", 2);
        countWayMap.put("审核方式", 3);
        countWayMap.put("方案", 4);
        countWayMap.put("保证金比例", 5);
        countWayMap.put("客户类型", 6);
        countWayMap.put("一级品类", 7);
        countWayMap.put("场景值", 8);
        countWayMap.put("导流商", 9);
        countWayMap.put("商品类型", 11);
        countWayMap.put("免押类型", 12);
        countWayMap.put("流量类型", 13);
        countWayMap.put("金融方案", 14);
        countWayMap.put("应用", 15);
        countWayMap.put("新旧程度", 16);
        countWayMap.put("是否监管", 17);
        countWayMap.put("机型", 18);

    }

    public Map<Integer, Set<String>> getSelectorNew(String miniType) {
        List<StatisticalDimension> statisticalDimensionList =
                dataviewRiskStatisticalDimensionService.lambdaQuery().eq(StatisticalDimension::getBizType, miniType).list();
        Map<String, Set<String>> map = statisticalDimensionList.stream()
                .filter(statisticalDimension -> statisticalDimension != null &&
                        StringUtils.isNotEmpty(statisticalDimension.getFirstDimension()) &&
                        StringUtils.isNotEmpty(statisticalDimension.getSecondaryDimension()))
                .collect(Collectors.groupingBy(
                        StatisticalDimension::getFirstDimension,
                        Collectors.mapping(StatisticalDimension::getSecondaryDimension, Collectors.toSet())
                ));
        //替换map的key 中文换成 数字
        Map<Integer, Set<String>> result = new HashMap<>();
        // 遍历原始映射，将中文键替换为对应的数字
        for (Map.Entry<String, Set<String>> entry : map.entrySet()) {
            String key = entry.getKey();
            Set<String> values = entry.getValue();
            Integer countWay = countWayMap.get(key);
            if (countWay != null) {
                result.put(countWay, values);
            }
        }
        return result;
    }

}

