package qnvip.data.overview.manager.dataindicators.beforesale;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleDO;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleOrderDO;
import qnvip.data.overview.domain.dataindicators.IndicatorPassFilterDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.dataindicators.IndicatorTimeEnum;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleOrderService;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleService;
import qnvip.data.overview.service.dataindicators.IndicatorPassFilterService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.dataindicators.IndicatorsUtil;
import qnvip.data.overview.vo.dataindicators.BeforeSaleVO;
import qnvip.rent.common.util.AssertUtil;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/1/24
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class BeforeSaleManager {

    private static final DateTimeFormatter DAY_DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_DF = DateTimeFormatter.ofPattern("yyyy-MM");

    private final IndicatorBeforeSaleOrderService indicatorBeforeSaleOrderService;
    private final IndicatorBeforeSaleService indicatorBeforeSaleService;
    private final IndicatorPassFilterService indicatorPassFilterService;


    public List<BeforeSaleVO> list(String sTime, String eTime, Integer dayOrMonth, String miniTypeStr) {
        AssertUtil.checkNotNull(sTime, "请选择开始时间");
        AssertUtil.checkNotNull(eTime, "请选择结束时间");
        List<Integer> miniTypeList = IndicatorsUtil.miniTypeStrToList(miniTypeStr);
        List<IndicatorBeforeSaleDO> visitorsList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeList);
        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeList);
        List<IndicatorPassFilterDO> passFilterList = indicatorPassFilterService.getList(sTime, eTime, miniTypeList);
        List<BeforeSaleVO> resultVO = new ArrayList<>();
        Map<String, List<BeforeSaleVO.DetailVO>> map = getDailyOrMonthlyMap(dayOrMonth, visitorsList, orderList,
                passFilterList);
        for (Map.Entry<String, List<BeforeSaleVO.DetailVO>> dailyEntry : map.entrySet()) {
            List<BeforeSaleVO.DetailVO> detailVOList = dailyEntry.getValue();
            String countDay = dailyEntry.getKey();
            BeforeSaleVO dailyVO = getBeforeSaleVO(detailVOList, null);
            dailyVO.setCode(IndicatorTimeEnum.DAILY.getCode());
            dailyVO.setCountDay(countDay);
            resultVO.add(dailyVO);
        }
        handleRingCompare(resultVO, miniTypeStr);
        return resultVO;
    }


    public List<BeforeSaleVO> fixedList(String miniTypeStr) {
        Map<String, String> timeMap = IndicatorsUtil.getDefaultStartAndEndTime();
        String sTime = timeMap.get("sTime");
        String eTime = timeMap.get("eTime");
        List<Integer> miniTypeList = IndicatorsUtil.miniTypeStrToList(miniTypeStr);
        List<IndicatorBeforeSaleDO> visitorsList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeList);
        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeList);
        List<IndicatorPassFilterDO> passFilterList = indicatorPassFilterService.getList(sTime, eTime, miniTypeList);
        Map<String, List<BeforeSaleVO.DetailVO>> dailyMap = getDailyOrMonthlyMap(1, visitorsList, orderList,
                passFilterList);
        int i = 0;
        List<BeforeSaleVO> resultVO = new ArrayList<>();
        List<BeforeSaleVO.DetailVO> monthlyDetailList = new ArrayList<>();
        List<BeforeSaleVO.DetailVO> weeklyDetailList = new ArrayList<>();
        for (Map.Entry<String, List<BeforeSaleVO.DetailVO>> dailyEntry : dailyMap.entrySet()) {
            List<BeforeSaleVO.DetailVO> detailVOList = dailyEntry.getValue();
            //近七天
            if (i < 7) {
                weeklyDetailList.addAll(detailVOList);
            }
            //统计近30天的均值
            monthlyDetailList.addAll(detailVOList);
            i++;
        }
        BeforeSaleVO weeklyVO = getBeforeSaleVO(weeklyDetailList, 7);
        weeklyVO.setCode(IndicatorTimeEnum.WEEKLY.getCode());
        weeklyVO.setCountDay(IndicatorTimeEnum.WEEKLY.getDescribe());
        BeforeSaleVO monthlyVO = getBeforeSaleVO(monthlyDetailList, 30);
        monthlyVO.setCode(IndicatorTimeEnum.MONTHLY.getCode());
        monthlyVO.setCountDay(IndicatorTimeEnum.MONTHLY.getDescribe());
        resultVO.add(monthlyVO);
        resultVO.add(weeklyVO);
        handleRingCompare(resultVO, miniTypeStr);
        return resultVO;
    }


    /**
     * 计算环比
     *
     * @param resultVO
     * @param miniTypeStr
     */
    private void handleRingCompare(List<BeforeSaleVO> resultVO, String miniTypeStr) {
        BeforeSaleVO lastVO = lastFixedList(miniTypeStr);
        Map<Integer, BeforeSaleVO.DetailVO> lastDetailMap =
                lastVO.getDetailList().stream().collect(Collectors.toMap(BeforeSaleVO.DetailVO::getMiniType,
                        detailVO -> detailVO));
        for (BeforeSaleVO beforeSaleVO : resultVO) {
            BeforeSaleVO.DetailVO totalData = beforeSaleVO.getTotalData();
            BeforeSaleVO.DetailVO lastTotal = lastVO.getTotalData();
            calculateRingCompare(totalData, lastTotal);
            for (BeforeSaleVO.DetailVO detailVO : beforeSaleVO.getDetailList()) {
                BeforeSaleVO.DetailVO lastDetailVO =
                        Optional.ofNullable(lastDetailMap.get(detailVO.getMiniType())).orElse(new BeforeSaleVO.DetailVO());
                calculateRingCompare(detailVO, lastDetailVO);
            }
        }
    }


    private void calculateRingCompare(BeforeSaleVO.DetailVO detailVO, BeforeSaleVO.DetailVO lastDetailVO) {
        detailVO.setGtNseCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getGtNseCount(),
                lastDetailVO.getGtNseCount()), CalculateUtil.div(lastDetailVO.getGtNseCount(), 100), 2));
        detailVO.setIntentionCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getIntentionCount(),
                lastDetailVO.getIntentionCount()), CalculateUtil.div(lastDetailVO.getIntentionCount(), 100), 2));
        detailVO.setCancelCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getCancelCount(),
                lastDetailVO.getCancelCount()), CalculateUtil.div(lastDetailVO.getCancelCount(), 100), 2));
        detailVO.setOrderCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getOrderCount(),
                lastDetailVO.getOrderCount()), CalculateUtil.div(lastDetailVO.getOrderCount(), 100), 2));
        detailVO.setRiskAllowRefuseCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskAllowRefuseCount(),
                lastDetailVO.getRiskAllowRefuseCount()), CalculateUtil.div(lastDetailVO.getRiskAllowRefuseCount(),
                100), 2));
        detailVO.setRiskExecCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskExecCount(),
                lastDetailVO.getRiskExecCount()), CalculateUtil.div(lastDetailVO.getRiskExecCount(), 100), 2));
        detailVO.setRiskRhExecCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskRhExecCount(),
                lastDetailVO.getRiskRhExecCount()), CalculateUtil.div(lastDetailVO.getRiskRhExecCount(), 100), 2));
        detailVO.setRiskApprovedCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskApprovedCount(),
                lastDetailVO.getRiskApprovedCount()), CalculateUtil.div(lastDetailVO.getRiskApprovedCount(), 100), 2));
        detailVO.setRiskRefusedCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskRefusedCount(),
                lastDetailVO.getRiskRefusedCount()), CalculateUtil.div(lastDetailVO.getRiskRefusedCount(), 100), 2));
        detailVO.setPayCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPayCount(),
                lastDetailVO.getPayCount()), CalculateUtil.div(lastDetailVO.getPayCount(), 100), 2));
        detailVO.setNotPayCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getNotPayCount(),
                lastDetailVO.getNotPayCount()), CalculateUtil.div(lastDetailVO.getNotPayCount(), 100), 2));
        detailVO.setDeliveryCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getDeliveryCount(),
                lastDetailVO.getDeliveryCount()), CalculateUtil.div(lastDetailVO.getDeliveryCount(), 100), 2));
        detailVO.setCancelDeliveryCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getCancelDeliveryCount(),
                lastDetailVO.getCancelDeliveryCount()), CalculateUtil.div(lastDetailVO.getCancelDeliveryCount(), 100)
                , 2));
    }


    public BeforeSaleVO lastFixedList(String miniTypeStr) {
        Map<String, String> timeMap = IndicatorsUtil.getLastDefaultStartAndEndTime();
        String sTime = timeMap.get("sTime");
        String eTime = timeMap.get("eTime");
        List<Integer> miniTypeList = IndicatorsUtil.miniTypeStrToList(miniTypeStr);
        List<IndicatorBeforeSaleDO> visitorsList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeList);
        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeList);
        List<IndicatorPassFilterDO> passFilterList = indicatorPassFilterService.getList(sTime, eTime, miniTypeList);
        Map<String, List<BeforeSaleVO.DetailVO>> dailyMap = getDailyOrMonthlyMap(1, visitorsList, orderList,
                passFilterList);
        List<BeforeSaleVO.DetailVO> monthlyDetailList = new ArrayList<>();
        for (Map.Entry<String, List<BeforeSaleVO.DetailVO>> dailyEntry : dailyMap.entrySet()) {
            List<BeforeSaleVO.DetailVO> detailVOList = dailyEntry.getValue();
            //统计上一个30天的均值
            monthlyDetailList.addAll(detailVOList);
        }
        return getBeforeSaleVO(monthlyDetailList, 30);
    }


    private Map<String, List<BeforeSaleVO.DetailVO>> getDailyOrMonthlyMap(Integer dayOrMonth,
                                                                          List<IndicatorBeforeSaleDO> visitorsList,
                                                                          List<IndicatorBeforeSaleOrderDO> orderList,
                                                                          List<IndicatorPassFilterDO> passFilterList) {
        Map<String, List<IndicatorBeforeSaleDO>> visitorsGroupMap;
        Map<String, List<IndicatorBeforeSaleOrderDO>> orderGroupMap;
        Map<String, List<IndicatorPassFilterDO>> passFilterGroupMap;
        if (dayOrMonth == 1) {
            visitorsGroupMap =
                    visitorsList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
            orderGroupMap = orderList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
            passFilterGroupMap =
                    passFilterList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));

        } else {
            visitorsGroupMap =
                    visitorsList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
            orderGroupMap = orderList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
            passFilterGroupMap =
                    passFilterList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
        }
        Map<String, List<BeforeSaleVO.DetailVO>> dailyDetailMap = new TreeMap<>(Comparator.reverseOrder());
        for (Map.Entry<String, List<IndicatorBeforeSaleDO>> visitorsEntry : visitorsGroupMap.entrySet()) {
            String dayOrMonthValue = visitorsEntry.getKey();
            List<BeforeSaleVO.DetailVO> detailVOList = new ArrayList<>();
            List<IndicatorBeforeSaleDO> visitors = visitorsEntry.getValue();
            List<IndicatorBeforeSaleOrderDO> orders =
                    Optional.ofNullable(orderGroupMap.get(visitorsEntry.getKey())).orElse(new ArrayList<>());
            List<IndicatorPassFilterDO> passFilters =
                    Optional.ofNullable(passFilterGroupMap.get(visitorsEntry.getKey())).orElse(new ArrayList<>());
            Map<Integer, List<IndicatorBeforeSaleDO>> visitorsMiniTypeMap =
                    visitors.stream().collect(Collectors.groupingBy(IndicatorBeforeSaleDO::getMiniType));
            Map<Integer, List<IndicatorBeforeSaleOrderDO>> ordersMiniTypeMap =
                    orders.stream().collect(Collectors.groupingBy(IndicatorBeforeSaleOrderDO::getMiniType));
            Map<Integer, List<IndicatorPassFilterDO>> passFilterMiniTypeMap =
                    passFilters.stream().collect(Collectors.groupingBy(IndicatorPassFilterDO::getMiniType));
            for (Map.Entry<Integer, List<IndicatorBeforeSaleDO>> visitorsMiniTypeEntry :
                    visitorsMiniTypeMap.entrySet()) {
                Integer miniKey = visitorsMiniTypeEntry.getKey();
                List<IndicatorBeforeSaleDO> value1 = visitorsMiniTypeEntry.getValue();
                List<IndicatorBeforeSaleOrderDO> value2 =
                        Optional.ofNullable(ordersMiniTypeMap.get(miniKey)).orElse(new ArrayList<>());
                List<IndicatorPassFilterDO> value3 =
                        Optional.ofNullable(passFilterMiniTypeMap.get(miniKey)).orElse(new ArrayList<>());
                BeforeSaleVO.DetailVO detailVO = new BeforeSaleVO.DetailVO();
                detailVO.setMiniType(miniKey);
                for (IndicatorBeforeSaleDO visitorsDO : value1) {
                    detailVO.setOrderCount(CalculateUtil.add(visitorsDO.getOrderCount(), detailVO.getOrderCount()));
                    detailVO.setGtNseCount(CalculateUtil.add(visitorsDO.getGtNseCount(), detailVO.getGtNseCount()));
                    detailVO.setIntentionCount(CalculateUtil.add(visitorsDO.getIntentionCount(),
                            detailVO.getIntentionCount()));
                    detailVO.setCancelCount(CalculateUtil.add(visitorsDO.getCancelCount(), detailVO.getCancelCount()));
                    detailVO.setRiskAllowRefuseCount(CalculateUtil.add(visitorsDO.getRiskRefuseCount(),
                            detailVO.getRiskAllowRefuseCount()));
                    detailVO.setRiskExecCount(CalculateUtil.add(visitorsDO.getRiskExecCount(),
                            detailVO.getRiskExecCount()));
                }
                for (IndicatorBeforeSaleOrderDO orderPayDO : value2) {
                    detailVO.setPayCount(CalculateUtil.add(orderPayDO.getPayCount(), detailVO.getPayCount()));
                    detailVO.setRiskApprovedCount(CalculateUtil.add(orderPayDO.getRiskApprovedCount(),
                            detailVO.getRiskApprovedCount()));
                    detailVO.setRiskRefusedCount(CalculateUtil.add(orderPayDO.getRiskRefusedCount(),
                            detailVO.getRiskRefusedCount()));
                    detailVO.setNotPayCount(CalculateUtil.add(orderPayDO.getNotPayCount(), detailVO.getNotPayCount()));
                    detailVO.setDeliveryCount(CalculateUtil.add(orderPayDO.getDeliveryCount(),
                            detailVO.getDeliveryCount()));
                    detailVO.setCancelDeliveryCount(CalculateUtil.add(orderPayDO.getCancelDeliveryCount(),
                            detailVO.getCancelDeliveryCount()));
                }
                for (IndicatorPassFilterDO indicatorPassFilterDO : value3) {
                    detailVO.setRiskRhExecCount(CalculateUtil.add(indicatorPassFilterDO.getRiskRhExecCount(),
                            detailVO.getRiskRhExecCount()));
                }
                detailVOList.add(detailVO);
            }
            dailyDetailMap.put(dayOrMonthValue, detailVOList);
        }
        return dailyDetailMap;
    }


    private BeforeSaleVO getBeforeSaleVO(List<BeforeSaleVO.DetailVO> detailVOList, Integer divisor) {
        BeforeSaleVO beforeSaleVO = new BeforeSaleVO();
        Map<Integer, List<BeforeSaleVO.DetailVO>> detailMiniTypeMap =
                detailVOList.stream().collect(Collectors.groupingBy(BeforeSaleVO.DetailVO::getMiniType));
        List<BeforeSaleVO.DetailVO> resList = new ArrayList<>();
        for (Map.Entry<Integer, List<BeforeSaleVO.DetailVO>> entry : detailMiniTypeMap.entrySet()) {
            Integer key = entry.getKey();
            List<BeforeSaleVO.DetailVO> value = entry.getValue();
            BeforeSaleVO.DetailVO detailVO = new BeforeSaleVO.DetailVO();
            detailVO.setMiniType(key);
            detailVO.setMiniTypeSort(MiniTypeEnum.getSortByCode(key));
            addData(detailVO, value, divisor);
            resList.add(detailVO);
        }
        beforeSaleVO.setDetailList(resList);
        resList.sort(Comparator.comparing(BeforeSaleVO.DetailVO::getMiniTypeSort));
        BeforeSaleVO.DetailVO totalDataVO = new BeforeSaleVO.DetailVO();
        addData(totalDataVO, detailVOList, divisor);
        beforeSaleVO.setTotalData(totalDataVO);
        return beforeSaleVO;
    }


    private void addData(BeforeSaleVO.DetailVO totalDataVO, List<BeforeSaleVO.DetailVO> detailVOList, Integer divisor) {
        for (BeforeSaleVO.DetailVO detailVO : detailVOList) {
            totalDataVO.setGtNseCount(CalculateUtil.add(detailVO.getGtNseCount(), totalDataVO.getGtNseCount()));
            totalDataVO.setIntentionCount(CalculateUtil.add(detailVO.getIntentionCount(),
                    totalDataVO.getIntentionCount()));
            totalDataVO.setCancelCount(CalculateUtil.add(detailVO.getCancelCount(), totalDataVO.getCancelCount()));
            totalDataVO.setOrderCount(CalculateUtil.add(detailVO.getOrderCount(), totalDataVO.getOrderCount()));
            totalDataVO.setRiskAllowRefuseCount(CalculateUtil.add(detailVO.getRiskAllowRefuseCount(),
                    totalDataVO.getRiskAllowRefuseCount()));
            totalDataVO.setRiskExecCount(CalculateUtil.add(detailVO.getRiskExecCount(),
                    totalDataVO.getRiskExecCount()));
            totalDataVO.setRiskApprovedCount(CalculateUtil.add(detailVO.getRiskApprovedCount(),
                    totalDataVO.getRiskApprovedCount()));
            totalDataVO.setRiskRefusedCount(CalculateUtil.add(detailVO.getRiskRefusedCount(),
                    totalDataVO.getRiskRefusedCount()));
            totalDataVO.setPayCount(CalculateUtil.add(detailVO.getPayCount(), totalDataVO.getPayCount()));
            totalDataVO.setNotPayCount(CalculateUtil.add(detailVO.getNotPayCount(), totalDataVO.getNotPayCount()));
            totalDataVO.setDeliveryCount(CalculateUtil.add(detailVO.getDeliveryCount(),
                    totalDataVO.getDeliveryCount()));
            totalDataVO.setCancelDeliveryCount(CalculateUtil.add(detailVO.getCancelDeliveryCount(),
                    totalDataVO.getCancelDeliveryCount()));
            totalDataVO.setRiskRhExecCount(CalculateUtil.add(detailVO.getRiskRhExecCount(),
                    totalDataVO.getRiskRhExecCount()));
        }
        if (divisor != null && divisor != 0 && divisor != 1) {
            totalDataVO.setGtNseCount(CalculateUtil.div(totalDataVO.getGtNseCount(), divisor, 2));
            totalDataVO.setIntentionCount(CalculateUtil.div(totalDataVO.getIntentionCount(), divisor, 2));
            totalDataVO.setCancelCount(CalculateUtil.div(totalDataVO.getCancelCount(), divisor, 2));
            totalDataVO.setOrderCount(CalculateUtil.div(totalDataVO.getOrderCount(), divisor, 2));
            totalDataVO.setRiskAllowRefuseCount(CalculateUtil.div(totalDataVO.getRiskAllowRefuseCount(), divisor, 2));
            totalDataVO.setRiskExecCount(CalculateUtil.div(totalDataVO.getRiskExecCount(), divisor, 2));
            totalDataVO.setRiskApprovedCount(CalculateUtil.div(totalDataVO.getRiskApprovedCount(), divisor, 2));
            totalDataVO.setRiskRefusedCount(CalculateUtil.div(totalDataVO.getRiskRefusedCount(), divisor, 2));
            totalDataVO.setPayCount(CalculateUtil.div(totalDataVO.getPayCount(), divisor, 2));
            totalDataVO.setNotPayCount(CalculateUtil.div(totalDataVO.getNotPayCount(), divisor, 2));
            totalDataVO.setDeliveryCount(CalculateUtil.div(totalDataVO.getDeliveryCount(), divisor, 2));
            totalDataVO.setCancelDeliveryCount(CalculateUtil.div(totalDataVO.getCancelDeliveryCount(), divisor, 2));
            totalDataVO.setRiskRhExecCount(CalculateUtil.div(totalDataVO.getRiskRhExecCount(), divisor, 2));
        }
        totalDataVO.setOrderCountRate(CalculateUtil.div(totalDataVO.getOrderCount(),
                CalculateUtil.div(totalDataVO.getIntentionCount(), 100), 2));
        totalDataVO.setRiskApprovedCountRate(CalculateUtil.div(totalDataVO.getRiskApprovedCount(),
                CalculateUtil.div(totalDataVO.getRiskExecCount(), 100), 2));
        totalDataVO.setPayCountRate(CalculateUtil.div(totalDataVO.getPayCount(),
                CalculateUtil.div(totalDataVO.getRiskApprovedCount(), 100), 2));
        totalDataVO.setDeliveryCountRate(CalculateUtil.div(totalDataVO.getDeliveryCount(),
                CalculateUtil.div(totalDataVO.getPayCount(), 100), 2));
    }

}