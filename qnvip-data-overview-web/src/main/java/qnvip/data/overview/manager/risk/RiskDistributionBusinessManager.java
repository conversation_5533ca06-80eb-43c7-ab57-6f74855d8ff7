package qnvip.data.overview.manager.risk;

import cn.afterturn.easypoi.excel.entity.ExportParams;
import cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.OrderDO;
import qnvip.data.overview.domain.risk.RiskDistributionCountDO;
import qnvip.data.overview.domain.risk.RiskDistributionOrderOverdueDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.SceneConfigEnum;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.enums.risk.ArtificialEnum;
import qnvip.data.overview.request.RiskDistributionOverdueRequest;
import qnvip.data.overview.service.risk.RiskDistributionCountService;
import qnvip.data.overview.service.risk.RiskDistributionOrderOverdueService;
import qnvip.data.overview.util.*;
import qnvip.data.overview.util.dataindicators.IndicatorsUtil;
import qnvip.data.overview.vo.risk.RiskGeneralBusinessVO;
import qnvip.data.overview.vo.risk.RiskGeneralVO;
import qnvip.rent.common.base.SingleResult;

import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Predicate;
import java.util.stream.Collectors;


/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/5/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskDistributionBusinessManager {

    private final RiskDistributionOrderOverdueService distributionOrderOverdueService;
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final RiskDistributionCountService distributionCountService;
    private final RedisTemplate redisTemplate;
    private static final int LOOP_COUNT = 30;

    private final AtomicInteger ATOMIC_INTEGER = new AtomicInteger();
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 5, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskOrderOverdue-" + ATOMIC_INTEGER.incrementAndGet());
        return t;
    });

    //租赁明细
    public SingleResult<RiskGeneralVO> getList(RiskDistributionOverdueRequest request) {
        // 统计类型 {value: 1,label: '金额',},{value: 2,label: '单量',},{value: 3,label: '单量比',},{value: 4,label: '金额比',}
        String countType = request.getCountType();
        // 统计维度  1: '风控等级', 2: '风控策略', 3: '审核方式', 4: '方案', 5: '保证金百分比', 6: '场景值', 7: '导流商', 8: '芝麻分等级', 9: '是否租物'
        //  11免押类型 12 流量类型 13 金融方案 14 应用 15 新旧程度 16 机型
        String countWay = request.getCountWay();
        //统计项 {value: 1,label: '逾期金额',},{value: 2,label: '保证金',},{value: 3,label: '其他收入',}
        String countItem = request.getCountItem();
        Integer overdueDay = request.getOverdueDay();
        Integer orderType = request.getOrderType();
        String miniType = request.getMiniType();
        String rentStartDate = request.getRentStartDate();
        String rentEndDate = request.getRentEndDate();
        String shopName = request.getShopName();

        List<Integer> miniTypeList = IndicatorsUtil.rentMiniTypeStrToList(miniType);
        RiskGeneralVO riskRenewalVO = new RiskGeneralVO();
        // 获取统计信息
        CopyOnWriteArrayList<RiskGeneralBusinessVO> resultList = Lists.newCopyOnWriteArrayList();
        AtomicInteger maxTerm = new AtomicInteger();
        //分组
        LocalDate startDate = LocalDateTime.parse(rentStartDate, YYYY_MM_DD_HH_MM_SS).toLocalDate();
        LocalDate endTime = LocalDateTime.parse(rentEndDate, YYYY_MM_DD_HH_MM_SS).toLocalDate();

        List<DateSplitUtils.DateSplit> dateSplits = DateSplitUtils.splitByMonth(startDate, endTime, 1);
        CountDownLatch countDownLatch = new CountDownLatch(dateSplits.size());
        for (DateSplitUtils.DateSplit dateSplit : dateSplits) {
            threadPoolExecutor.execute(() -> {
                ThreadLocalCacheUtil.put("countWay", countWay);
                ThreadLocalCacheUtil.put("countType", countType);
                ThreadLocalCacheUtil.put("countItem", countItem);
                try {
                    String startDateTimeStr = dateSplit.getStartDateTimeStr();
                    // 起租月 例如：2024-12
                    String rentStartDay = startDateTimeStr.substring(0, 7);
                    List<RiskDistributionCountDO> value = distributionCountService.getList(rentStartDay, overdueDay);
                    // 过滤数据
                    value = filterList(shopName, countWay, orderType, miniTypeList, value);
                    if (CollUtil.isEmpty(value)) {
                        return;
                    }
                    Integer countDayMaxTerm = value.stream().map(RiskDistributionCountDO::getMaxTerm)
                            .max(Comparator.comparing(Function.identity())).get();
                    maxTerm.set(Math.max(maxTerm.get(), countDayMaxTerm));
                    RiskGeneralBusinessVO riskGeneralBusinessVO = RiskGeneralBusinessVO.getInstance();
                    RiskGeneralBusinessVO.InnerColumnVO dateTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
                    if (CollUtil.isNotEmpty(miniTypeList)) {
                        Map<String, List<RiskDistributionCountDO>> overdue2List =
                                value.stream().peek(e -> e.setPlatform(MiniTypeEnum.getPlatformByCode(e.getMiniType())))
                                        .collect(Collectors.groupingBy(RiskDistributionCountDO::getPlatform));
                        getPlatformList(riskGeneralBusinessVO, overdue2List, rentStartDay);
                    } else {
                        Map<String, List<RiskDistributionCountDO>> dtoOtherWay2List = Maps.newHashMap();
                        getOtherType(dtoOtherWay2List, value);
                        if (MapUtil.isNotEmpty(dtoOtherWay2List)) {
                            getOtherTypeList(dtoOtherWay2List, riskGeneralBusinessVO, rentStartDay);
                        }
                    }
                    if (CollUtil.isEmpty(riskGeneralBusinessVO.getList())) {
                        // 下一层为空，外层也不展示
                        return;
                    }
                    // 计算起租日总合计
                    translateList(rentStartDay, value, dateTotalVO);
                    riskGeneralBusinessVO.setDesc(rentStartDay);
                    // 给前端展示用的
                    riskGeneralBusinessVO.setCode(1000);
                    riskGeneralBusinessVO.setTotal(dateTotalVO);

                    //决定前端页面显示的个数
                    Long monthDiff = getMonthDiff(rentStartDay, value);
                    riskGeneralBusinessVO.setTerm(monthDiff.intValue());
                    value.clear();
                    resultList.add(riskGeneralBusinessVO);
                } catch (Exception e) {
                    log.error(e.getMessage(), e);
                } finally {
                    countDownLatch.countDown();
                    ThreadLocalCacheUtil.release();
                }
            });
        }
        try {
            countDownLatch.await();
            riskRenewalVO.setMaxTerm(maxTerm.get());
            List<RiskGeneralBusinessVO> collect = resultList.stream().
                    sorted(Comparator.comparing(RiskGeneralBusinessVO::getDesc)).collect(Collectors.toList());
            riskRenewalVO.setList(collect);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return SingleResult.of(riskRenewalVO);
    }

    private List<RiskDistributionCountDO> filterList(String shopName, String countWay,
                                                     Integer orderType,
                                                     List<Integer> finalMiniTypeList,
                                                     List<RiskDistributionCountDO> value) {
        Predicate<RiskDistributionCountDO> predicate = o -> !CollUtil.isNotEmpty(finalMiniTypeList) || finalMiniTypeList.contains(o.getMiniType());
        value = getFilterList(value, predicate);

        // 过滤统计纬度
        List<String> countWays = CollUtil.toList(countWay.split(","));
        String type = countWays.get(0);
        if (countWays.size() > 1) {
            switch (type) {
                case "1":
                    predicate = o -> countWays.contains(o.getRiskLevel());
                    break;
                case "2":
                    predicate = o -> countWays.contains(o.getRiskStrategy());
                    break;
                case "3":
                    predicate = o -> "人工审核".equals(countWays.get(1)) != o.getAuditType().equals("1");
                    break;
                case "4":
                    predicate = o -> countWays.contains(FinanceTypeEnum.getTitleByType(o.getFinanceType()));
                    break;
                // 保证金比
                case "5":
                    predicate = o -> countWays.contains(o.getBondRateInterval().split(",")[0]);
                    break;
                // 是否免押
                case "6":
                    predicate = o -> countWays.contains(Integer.valueOf(0).equals(o.getIsMortgage()) ? "否" : "是");
                    break;
                // 是否租物订单
                case "7":
                    predicate = o -> countWays.contains(Integer.valueOf(0).equals(o.getIsRent()) ?
                            "否" : "是");
                    break;
                // 商户名称
                case "8":
                    predicate = o -> countWays.contains(o.getShopName());
                    break;
                case "9":
                    predicate = o -> countWays.contains(o.getScene());
                    break;
                // 场景值
                case "10":
                    predicate = o -> countWays.contains(o.getQuotientName());
                    break;
                case "11":
                    predicate = o -> countWays.contains(o.getZmfLevel());
                    break;

                case "12":
                    predicate = o -> countWays.contains(o.getDepositFreeType());
                    break;
                case "13":
                    predicate = o -> countWays.contains(o.getTrafficType());
                    break;
                case "14":
                    predicate = o -> countWays.contains(o.getFinancialSolutions());
                    break;
                case "15":
                    predicate = o -> countWays.contains(o.getApplicationName());
                    break;
                case "16":
                    predicate = o -> countWays.contains(o.getEquipmentState());
                    break;
                case "17":
                    predicate = o -> countWays.contains(o.getSupervisedMachine());
                    break;
                case "18":
                    predicate = o -> countWays.contains(o.getMachineType());
                    break;
                default:
                    predicate = null;
            }
            value = getFilterList(value, predicate);
        }

        if (orderType != null) {
            // 1：商户自主线下引流 2：优品自动拒绝 3：优品人工拒绝 4：审核通过
            if (orderType == 4) {
                predicate = o -> o.getRefuseType().equals(orderType) && !o.getDrainageType().equals(1);
            } else {
                predicate = orderType > 1 ? o -> o.getRefuseType().equals(orderType) :
                        o -> o.getDrainageType().equals(orderType);
            }
            value = getFilterList(value, predicate);
        }
        //过滤商户名称
        if (StringUtils.isNotEmpty(shopName)) {
            List<String> shopNameList = Arrays.asList(shopName.split(","));
            if (CollectionUtils.isNotEmpty(shopNameList)) {
                predicate = o -> shopNameList.contains(o.getShopName());
                value = getFilterList(value, predicate);
            }
        }

        return value;
    }

    private List<RiskDistributionCountDO> getFilterList(List<RiskDistributionCountDO> list, Predicate<RiskDistributionCountDO> predicate) {
        return list.stream().filter(predicate).collect(Collectors.toList());
    }

    private void getPlatformList(
            RiskGeneralBusinessVO riskGeneralBusinessVO,
            Map<String, List<RiskDistributionCountDO>> overdue2List, String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        //明细
        for (Map.Entry<String, List<RiskDistributionCountDO>> platformEntry : overdue2List.entrySet()) {
            String platform = platformEntry.getKey();

            List<RiskDistributionCountDO> dtoList = overdue2List.get(platform);
            if (CollUtil.isEmpty(dtoList)) {
                continue;
            }
            Map<Integer, List<RiskDistributionCountDO>> dtoMiniType2List =
                    dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getMiniType));

            platformDetailVO = RiskGeneralBusinessVO.getInstance();

            getMiniTypeList(platformDetailVO, dtoMiniType2List, rentStartDay);
            if (CollUtil.isEmpty(platformDetailVO.getList())) {
                // 下一层为空，外层也不展示
                continue;
            }
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            platformDetailVO.setDesc(platform);
            platformDetailVO.setCode(SceneConfigEnum.getSortByName(platform));
            translateList(rentStartDay, dtoList, platformTotalVO);
            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, dtoList);
            platformDetailVO.setTerm(monthDiff.intValue());
            detailVOList.add(platformDetailVO);

        }
        //合计
        detailVOList.sort(Comparator.comparing(RiskGeneralBusinessVO::getCode));
        riskGeneralBusinessVO.setList(detailVOList);
    }

    private void getMiniTypeList(
            RiskGeneralBusinessVO riskGeneralBusinessVO,
            Map<Integer, List<RiskDistributionCountDO>> dtoMiniType2List,
            String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        //明细
        for (Map.Entry<Integer, List<RiskDistributionCountDO>> platformEntry : dtoMiniType2List.entrySet()) {
            platformDetailVO = RiskGeneralBusinessVO.getInstance();
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            Integer miniType = platformEntry.getKey();

            List<RiskDistributionCountDO> dtoList = dtoMiniType2List.get(miniType);
            if (CollUtil.isEmpty(dtoList)) {
                continue;
            }
            platformDetailVO.setDesc(MiniTypeEnum.getValueByCode(miniType));
            translateList(rentStartDay, dtoList, platformTotalVO);
            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, dtoList);
            platformDetailVO.setTerm(monthDiff.intValue());
            Map<String, List<RiskDistributionCountDO>> dtoOtherWay2List = Maps.newHashMap();
            getOtherType(dtoOtherWay2List, dtoList);
            if (MapUtil.isNotEmpty(dtoOtherWay2List)) {
                getOtherTypeList(dtoOtherWay2List, platformDetailVO, rentStartDay);
            }
            if (CollUtil.isEmpty(platformDetailVO.getList())) {
                // 下一层为空，外层也不展示
                continue;
            }
            detailVOList.add(platformDetailVO);
        }
        //合计
        riskGeneralBusinessVO.setList(detailVOList);
    }

    private void getOtherType(
            Map<String, List<RiskDistributionCountDO>> map2,
            List<RiskDistributionCountDO> dtoList) {

        String countWay = (String) Optional.ofNullable(ThreadLocalCacheUtil.get("countWay")).orElse("");
        String[] split = countWay.split(",");
        int type = Integer.parseInt(split[0]);
        Map<String, List<RiskDistributionCountDO>> dtoOtherWay2List;
        switch (type) {
            case 1:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getRiskLevel));
                break;
            case 2:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getRiskStrategy));
                break;
            case 3:
                dtoOtherWay2List =
                        dtoList.stream().peek(e -> e.setAuditTypeDesc("1".equals(e.getAuditType()) ? "自动审核" : "人工审核"))
                                .collect(Collectors.groupingBy(RiskDistributionCountDO::getAuditTypeDesc));
                break;
            case 4:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(e -> FinanceTypeEnum.getTitleByType(e.getFinanceType())));
                break;
            // 保证金比
            case 5:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getBondRateInterval));
                break;
            // 是否免押
            case 6:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(o -> Integer.valueOf(0).equals(o.getIsMortgage()) ? "否" : "是"));
                break;
            // 是否租物订单
            case 7:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(o -> Integer.valueOf(0).equals(o.getIsRent()) ?
                                "否" : "是"));
                break;
            // 商户
            case 8:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getShopName));
                break;
            case 9:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getScene));
                break;
            // 场景值
            case 10:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getQuotientName));
                break;
            case 11:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getZmfLevel));
                break;
            case 12:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getDepositFreeType));
                break;
            case 13:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getTrafficType));
                break;
            case 14:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getFinancialSolutions));
                break;
            case 15:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getApplicationName));
                break;
            case 16:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getEquipmentState));
                break;
            case 17:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getSupervisedMachine));
                break;
            case 18:
                dtoOtherWay2List =
                        dtoList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getMachineType));
                break;
            default:
                dtoOtherWay2List = Maps.newHashMap();
        }
        map2.putAll(dtoOtherWay2List);
    }


    private void getOtherTypeList(Map<String, List<RiskDistributionCountDO>> dtoOtherWay2List,
                                  RiskGeneralBusinessVO riskGeneralBusinessVO,
                                  String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO platformTotalVO;
        RiskGeneralBusinessVO platformDetailVO;
        String countWay = (String) Optional.ofNullable(ThreadLocalCacheUtil.get("countWay")).orElse(0);
        int type = Integer.parseInt(countWay.split(",")[0]);
        //明细
        for (Map.Entry<String, List<RiskDistributionCountDO>> platformEntry : dtoOtherWay2List.entrySet()) {
            platformDetailVO = RiskGeneralBusinessVO.getInstance();
            platformTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            String key = platformEntry.getKey();

            if (Integer.valueOf(5).equals(type)) {
                String[] split = key.split(",");
                key = split[0];
                platformDetailVO.setCode(Integer.parseInt(split[1]));
            }

            List<RiskDistributionCountDO> platformList = platformEntry.getValue();
            if (Integer.valueOf(3).equals(type)) {
                if ("人工审核".equals(key)) {
                    Map<String, List<RiskDistributionCountDO>> auditorId2list =
                            platformList.stream().collect(Collectors.groupingBy(RiskDistributionCountDO::getAuditTypeName));
                    getAuditorIdList(auditorId2list, platformDetailVO, rentStartDay);
                }
            }
            if (StringUtils.isBlank(key) || key.contains("建议")) {
                continue;
            }
            platformDetailVO.setDesc(key);
            if (Integer.valueOf(1).equals(type)) {
                if (key.contains("风控等级")) {
                    String substring = key.substring(4);
                    int level = CalculateUtil.toDecimal(substring).intValue();
                    platformDetailVO.setCode(level);
                } else {
                    platformDetailVO.setCode(-1);
                }
            }

            translateList(rentStartDay, platformList, platformTotalVO);
            platformDetailVO.setTotal(platformTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, platformList);
            platformDetailVO.setTerm(monthDiff.intValue());
            detailVOList.add(platformDetailVO);

        }
        if (Integer.valueOf(1).equals(type) || Integer.valueOf(5).equals(type)) {
            detailVOList.sort(Comparator.comparing(RiskGeneralBusinessVO::getCode));
        }
        //合计
        riskGeneralBusinessVO.setList(detailVOList);
    }


    private void getAuditorIdList(Map<String, List<RiskDistributionCountDO>> auditorId2list,
                                  RiskGeneralBusinessVO platformDetailVO,
                                  String rentStartDay) {
        LinkedList<RiskGeneralBusinessVO> detailVOList = Lists.newLinkedList();
        RiskGeneralBusinessVO.InnerColumnVO auditorIdTotalVO;
        RiskGeneralBusinessVO auditorVO;
        //明细
        for (Map.Entry<String, List<RiskDistributionCountDO>> platformEntry : auditorId2list.entrySet()) {
            auditorIdTotalVO = RiskGeneralBusinessVO.InnerColumnVO.getInstance();
            auditorVO = RiskGeneralBusinessVO.getInstance();
            String key = platformEntry.getKey();
            String name = key;
            //String name = ArtificialEnum.getNameById(Integer.parseInt(key));
            List<RiskDistributionCountDO> platformList = platformEntry.getValue();
            auditorVO.setDesc(name);
            translateList(rentStartDay, platformList, auditorIdTotalVO);
            auditorVO.setTotal(auditorIdTotalVO);
            Long monthDiff = getMonthDiff(rentStartDay, platformList);
            auditorVO.setTerm(monthDiff.intValue());
            detailVOList.add(auditorVO);

        }
        //合计
        platformDetailVO.setList(detailVOList);
    }

    public static void main(String[] args) {
        String rentStartDay = "2023-12-01";
        //取当前月份的 第一天
        LocalDateTime endTime = LocalDateTime.now().withDayOfMonth(1);
        LocalDate startTime =  LocalDate.parse(rentStartDay);
        //计算月份差
        Long monthDiff = ChronoUnit.MONTHS.between(startTime, endTime);


        System.out.println(monthDiff);
    }

    /**
     * @param rentStartDay 起租月 2024-01
     * @param orderDos
     * @param columnVO
     */
    private void translateList(String rentStartDay, List<RiskDistributionCountDO> orderDos,
                               RiskGeneralBusinessVO.InnerColumnVO columnVO) {
        rentStartDay = rentStartDay.concat("-01");
        LocalDateTime endTime = LocalDateTime.now().withDayOfMonth(1);
        LocalDate startTime = LocalDate.parse(rentStartDay);
        //计算月份差
        Long monthDiff = ChronoUnit.MONTHS.between(startTime, endTime);
        if (startTime.isAfter(LocalDate.parse("2024-03-01"))) {
            monthDiff = monthDiff + 1;
        }
        // 租期内订单的统计信息
        BigDecimal rentTotal = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getRentTotal);
        BigDecimal totalOrderCnt = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getTotalOrderCnt);
        BigDecimal buyoutAmtTotal = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getBuyoutAmt);
//        BigDecimal discountsTotal = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getBeforeDiscount);
        //优惠券金额
        BigDecimal couponDiscountAmtTotal = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getCouponDiscountAmtTotal);
        //现在修改成 从优惠券总额获取
        BigDecimal discountsTotal = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getDiscountTotal);

        //总租金
        BigDecimal renewTotalRent = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getRenewTotalRent);

        //12期以内的总租金计算
        BigDecimal rentTotalInner = ObjectUtils.getSum(orderDos, RiskDistributionCountDO::getRentTotalInner);

        //业务总额=总租金+续租总租金-优惠券(discountsTotal),作为 分母
        BigDecimal businessAmtTotal = rentTotal.subtract(couponDiscountAmtTotal);

        // 12 期以内的业务总额  业务总额=总租金+买断金-售前优惠(discountsTotal),作为 分母
        BigDecimal businessAmtTotalInner = rentTotalInner.add(buyoutAmtTotal).subtract(couponDiscountAmtTotal);

//        log.info("商户长租业务总额计算明细: "+"businessAmtTotal="+businessAmtTotal+" rentTotal="+rentTotal+"   buyoutAmtTotal="+buyoutAmtTotal+"  couponDiscountAmtTotal"+couponDiscountAmtTotal+"   businessAmtTotalInner="+businessAmtTotalInner);
        RiskGeneralBusinessVO.InnerRiskVO[] mobArray = new RiskGeneralBusinessVO.InnerRiskVO[monthDiff.intValue() + 1];
        //30期
        for (int i = 1; i <= monthDiff; i++) {
            try {
                RiskGeneralBusinessVO.InnerRiskVO mob = getInnerRiskVO(
                        orderDos,
                        totalOrderCnt,
                        businessAmtTotalInner,
                        buyoutAmtTotal,
                        renewTotalRent,
                        i,
                        getMethodReference(i, "getBuyoutAmt"),
                        getMethodReference(i, "getBondAmt"),
                        getMethodReference(i, "getDiscountReturnAmt"),
                        getMethodReference(i, "getBeforeDiscount"),
                        getMethodReference(i, "getParentBeforeDiscount"),
                        getMethodReference(i, "getParentDiscountReturnAmt"),
                        getMethodReference(i, "getCountTerm"),
                        getMethodReference(i, "getNotPayTerm"),
                        getMethodReference(i, "getOverdueFine"),
                        getMethodReference(i, "getRenewAmt"),
                        getMethodReference(i, "getSurplusBondAmt"),
                        getMethodReference(i, "getBondRestFundAmount"),
                        getMethodReference(i, "getDiffPricingDiscountAmt"),
                        getMethodReference(i, "getCouponDiscountAmt")
                );
                mobArray[i] = mob;
            } catch (Exception e) {
                // 处理异常，例如记录日志
                e.printStackTrace();
            }
        }
        setColumnVO(columnVO, mobArray, monthDiff.intValue());
        RiskGeneralBusinessVO.InnerRiskVO innerRiskVO = RiskGeneralBusinessVO.InnerRiskVO.getInstance();
        // 设置业务总额
        innerRiskVO.setOverdueRentTotal(businessAmtTotalInner);
        innerRiskVO.setOverdueOrderCnt(totalOrderCnt);
        columnVO.setAmtTotal(innerRiskVO);

    }

    /**
     * 设置列数据  setMob1...setMob30
     * @param columnVO
     * @param mobArray
     */
    private void setColumnVO(RiskGeneralBusinessVO.InnerColumnVO columnVO, RiskGeneralBusinessVO.InnerRiskVO[] mobArray,int monthDiff) {
        for (int i = 1; i <= monthDiff; i++) {
            try {
                String methodName = "setMob" + i;
                Method method = RiskGeneralBusinessVO.InnerColumnVO.class.getMethod(methodName, RiskGeneralBusinessVO.InnerRiskVO.class);
                method.invoke(columnVO, mobArray[i]);
            } catch (Exception e) {
                // 记录异常日志
                log.error("Error setting MOB{}:  {}", e, i);
            }
        }
    }

    // 根据参数动态获取方法引用每期是的数据
    private Function<RiskDistributionCountDO, BigDecimal> getMethodReference(int term, String methodName) {
        try {
            Method method = RiskDistributionCountDO.class.getMethod(methodName + term);
            return obj -> {
                try {
                    return (BigDecimal) method.invoke(obj);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            };
        } catch (NoSuchMethodException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 获取每期数据   mob1...mob30
     * ("订单总租金")
     * ("订单量单量")
     * ("逾期订单比")
     * ("合计比")
     */
    private <T> RiskGeneralBusinessVO.InnerRiskVO getInnerRiskVO(List<T> orderDos,
                                                                 BigDecimal totalOrderCnt,
                                                                 BigDecimal businessAmtTotal,
                                                                 BigDecimal buyoutAmtTotal,
                                                                 BigDecimal renewTotalRent,
                                                                 Integer term,
                                                                 Function<T, BigDecimal> getBuyoutAmt,
                                                                 Function<T, BigDecimal> getBondAmt,
                                                                 Function<T, BigDecimal> getDiscountReturnAmt,
                                                                 Function<T, BigDecimal> getBeforeDiscount,
                                                                 Function<T, BigDecimal> getParentBeforeDiscount,
                                                                 Function<T, BigDecimal> getParentDiscountReturnAmt,
                                                                 Function<T, BigDecimal> getCountTerm,
                                                                 Function<T, BigDecimal> getNotPayTerm,
                                                                 Function<T, BigDecimal> getOverdueFine,
                                                                 Function<T, BigDecimal> getRenewAmt,
                                                                 Function<T, BigDecimal> getSurplusBondAmt,
                                                                 Function<T, BigDecimal> getBondRestFundAmount,
                                                                 Function<T, BigDecimal> getDiffPricingDiscountAmt,
                                                                 Function<T, BigDecimal> getCouponDiscountAmt) {
        // {value: 1,label: '逾期金额',},{value: 2,label: '保证金',},{value: 3,label: '其他收入',}
        String countItem = ThreadLocalCacheUtil.get("countItem");
        // {value: 1,label: '金额',},{value: 2,label: '单量',},{value: 3,label: '单量比',},{value: 4,label: '金额比',}
        String countType = ThreadLocalCacheUtil.get("countType");

        // 租期内订单的逾期总买断金
        BigDecimal overdueBuyoutAmt = ObjectUtils.getSum(orderDos, getBuyoutAmt);// 买断金
        BigDecimal overdueBondAmt = ObjectUtils.getSum(orderDos, getBondAmt); // 保证金
        BigDecimal discountReturnAmt = ObjectUtils.getSum(orderDos, getDiscountReturnAmt);  // 折扣返还金额
        BigDecimal overdueBeforeDiscount = ObjectUtils.getSum(orderDos, getBeforeDiscount); // 核销总金额
        BigDecimal overDueParentBeforeDiscount = ObjectUtils.getSum(orderDos, getParentBeforeDiscount); // 父订单未使用售前优惠金额
        BigDecimal overDueParentDiscountAmt = ObjectUtils.getSum(orderDos, getParentDiscountReturnAmt);// 父订单折扣返还金额
        BigDecimal overdueOrderCnt = ObjectUtils.getSum(orderDos, getCountTerm); // 每一期的逾期订单数
        BigDecimal notPayTerm = ObjectUtils.getSum(orderDos, getNotPayTerm); // 应还本金总额
        BigDecimal overdueFines = ObjectUtils.getSum(orderDos, getOverdueFine);//逾期滞纳金总额
        BigDecimal renewAmt = ObjectUtils.getSum(orderDos, getRenewAmt);// 每期的续租总租金
        BigDecimal surplusBondAmt = ObjectUtils.getSum(orderDos, getSurplusBondAmt); // 抵扣完剩余保证金
        BigDecimal bondRestFundAmount = ObjectUtils.getSum(orderDos, getBondRestFundAmount); // 剩余可用预授权实付押金
        BigDecimal diffPricingDiscountAmt = ObjectUtils.getSum(orderDos, getDiffPricingDiscountAmt); // 差异化优惠金额
        BigDecimal couponDiscountAmt = ObjectUtils.getSum(orderDos, getCouponDiscountAmt); // 优惠券金额

        RiskGeneralBusinessVO.InnerRiskVO detailVO = RiskGeneralBusinessVO.InnerRiskVO.getInstance();


        //优惠返回+逾期订单未使用售前优惠金额+滞纳金
        BigDecimal otherAmt = discountReturnAmt.add(overdueFines);
        // 父订单折扣返还金额 + 逾期滞纳金总额
        BigDecimal parentOtherAmt = overDueParentDiscountAmt.add(overdueFines);

        assert countItem != null;
        List<String> countItems = Arrays.asList(countItem.split(","));
        BigDecimal amount = notPayTerm.subtract(bondRestFundAmount).subtract(couponDiscountAmt);
        // 1逾期比 2保证金比 3其他收入比

        // 保证金比：逾期订单已收保证金/业务总额
//        BigDecimal bondRate =
//                CalculateUtil.divAndReturnZero(overdueBondAmt,
//                        businessAmtTotal, 4);
        BigDecimal bondRate =
                CalculateUtil.divAndReturnZero(surplusBondAmt,
                        businessAmtTotal, 4);

        // 逾期比：逾期订单未还租金+未还买断金-逾期订单未使用售前优惠金额/业务总额
//        BigDecimal overdueAmt = notPayTerm.add(overdueBuyoutAmt).subtract(overDueParentBeforeDiscount).subtract(overdueBeforeDiscount);
        BigDecimal overdueAmt = notPayTerm
//                .add(overdueBuyoutAmt)
                .subtract(bondRestFundAmount).subtract(couponDiscountAmt);
        BigDecimal overdueRate =
                CalculateUtil.mul(CalculateUtil.divAndReturnZero(overdueAmt,
                        businessAmtTotal, 4), -1);

        // 其他收入比：优惠返回+逾期订单未使用售前优惠金额+滞纳金/业务总额
        BigDecimal finalOtherAmt = notPayTerm.add(renewAmt).subtract(parentOtherAmt).subtract(otherAmt);
        BigDecimal otherRate =
                CalculateUtil.mul(CalculateUtil.divAndReturnZero(finalOtherAmt,
                        businessAmtTotal, 4), -1);

        // 逾期订单比:逾期订单量/业务总单量
        BigDecimal overdueOrderRate =
                CalculateUtil.divAndReturnZero(overdueOrderCnt,
                        totalOrderCnt, 4);
        assert countType != null;
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());

        if (collect.contains(2)) { //查询 单量
            // 逾期订单量
            detailVO.setOverdueOrderCnt(overdueOrderCnt);
        }
        if (collect.contains(3)) { //查询 单量比
            detailVO.setOverdueOrderRate(CalculateUtil.mul(CalculateUtil.mul(overdueOrderRate, 100, 2), -1));
        }
        if (collect.contains(4)) { //查询 金额比

            // 百分比
            BigDecimal total = BigDecimal.ZERO;
            if (countItems.contains("1")) {  // 查询 逾期金额
                total = total.add(overdueRate);
            }
            if (countItems.contains("2")) { //查询 保证金
                total = total.add(bondRate);
                // amount = amount.subtract(overdueBondAmt);

            }
            if (countItems.contains("3")) { // 查询 其他收入
                total = total.add(otherRate);
                // amount = amount.subtract(overdueBuyoutAmt).add(renewAmt).subtract(otherAmt);
            }

//            log.info("商户长租模块逾期率计算明细和结果为:  overdueRate=" + overdueRate + " overdueAmt=" + overdueAmt + " notPayTerm=" + notPayTerm + " overdueBuyoutAmt=" + overdueBuyoutAmt + "  bondRate=" + bondRate + " total=" + total
//                    + " surplusBondAmt=" + surplusBondAmt + " bondRestFundAmount=" + bondRestFundAmount + " diffPricingDiscountAmt=" + diffPricingDiscountAmt
//                    + " couponDiscountAmt=" + couponDiscountAmt + " businessAmtTotal=" + businessAmtTotal + "总单量totalOrderCnt=" + totalOrderCnt + "  逾期订单量,overdueOrderCnt=" + overdueOrderCnt + "期数term=" + term);

            detailVO.setCountRate(CalculateUtil.mul(total, 100, 2));
        }


        // 金额
        if (collect.contains(1)) {
            detailVO.setOverdueRentTotal(amount);
        }
        return detailVO;
    }

    private Long getMonthDiff(String rentStartDay, List<RiskDistributionCountDO> overdueList) {
        // 判断当前起租日的封账日
        List<Integer> collect =
                Arrays.stream(rentStartDay.split("-")).map(Integer::parseInt).collect(Collectors.toList());
        LocalDate start = LocalDate.of(collect.get(0), collect.get(1), 1);
        LocalDate end = LocalDate.now();

        //获取起租日的年月
        String rentStartDate = String.valueOf(start.getYear()) + start.getMonth().getValue();
        //获取当前时间的年月
        String currentEndDate = String.valueOf(end.getYear()) + end.getMonth().getValue();

        LocalDate date1 = LocalDate.of(2024, 4, 1);

        if (rentStartDate.equals(currentEndDate)) {
            return 0L;
        }
        Integer maxTerm = overdueList.stream().map(RiskDistributionCountDO::getMaxTerm)
                .max(Comparator.comparing(Function.identity())).get();
        return Math.min(start.isBefore(date1) ? ChronoUnit.MONTHS.between(start, end) : ChronoUnit.MONTHS.between(start, end) + 1, maxTerm);
    }


    public Map<Integer, Set<String>> getSelector(String startTime,
                                                 String endTime,
                                                 String miniType,
                                                 Integer overdueDay) {
        Map<Integer, Set<String>> map = Maps.newHashMap();
        HashSet<String> auditTypeSet = Sets.newHashSet();
        HashSet<String> riskLevelSet = Sets.newHashSet();
        HashSet<String> riskStrategySet = Sets.newHashSet();
        HashSet<String> financeTypeSet = Sets.newHashSet();
        HashSet<String> bondRateSet = Sets.newHashSet();
        HashSet<String> rentSet = Sets.newHashSet();
        HashSet<String> mortgageSet = Sets.newHashSet();
        HashSet<String> shopNameSet = Sets.newHashSet();
        HashSet<String> sceneSet = Sets.newHashSet();
        HashSet<String> quotientSet = Sets.newHashSet();
        HashSet<String> zmfLevelSet = Sets.newHashSet();
        List<Integer> miniTypeList = null;
        if (StringUtils.isNotEmpty(miniType)) {
            miniTypeList =
                    Arrays.stream(miniType.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        }

        List<RiskDistributionOrderOverdueDO> auditType =
                distributionOrderOverdueService.getAuditType(startTime, endTime, miniTypeList);
        for (RiskDistributionOrderOverdueDO riskOrderOverdueDO : auditType) {

            auditTypeSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getAuditType()) ? "自动审核" : "人工审核");
            riskLevelSet.add(riskOrderOverdueDO.getRiskLevel());
            riskStrategySet.add(riskOrderOverdueDO.getRiskStrategy());
            financeTypeSet.add(FinanceTypeEnum.getTitleByType(riskOrderOverdueDO.getFinanceType()));
            rentSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getIsRent()) ? "是" : "否");
            mortgageSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getIsMortgage()) ? "是" : "否");
            shopNameSet.add(riskOrderOverdueDO.getShopName());
            sceneSet.add(riskOrderOverdueDO.getScene());
            quotientSet.add(riskOrderOverdueDO.getQuotientName());
            zmfLevelSet.add(riskOrderOverdueDO.getZmfLevel());
        }
        List<RiskDistributionOrderOverdueDO> bondRate =
                distributionOrderOverdueService.getBondRate(startTime, endTime, miniTypeList, overdueDay);
        for (RiskDistributionOrderOverdueDO riskOrderOverdueDO : bondRate) {
            String[] split = riskOrderOverdueDO.getBondRate().split(",");
            bondRateSet.add(split[0]);
        }
        Set<String> riskLevelFinalSet =
                riskLevelSet.stream().filter(o -> StringUtils.isNotBlank(o) && !o.contains("建议"))
                        .collect(Collectors.toCollection(Sets::newHashSet));
        map.put(1, riskLevelFinalSet);
        Set<String> riskStrategyFinalSet =
                riskStrategySet.stream().filter(StringUtils::isNotBlank)
                        .collect(Collectors.toCollection(Sets::newHashSet));
        map.put(2, riskStrategyFinalSet);
        map.put(3, auditTypeSet);
        map.put(4, financeTypeSet);
        map.put(5, bondRateSet);
        map.put(6, rentSet);
        map.put(7, mortgageSet);
        map.put(8, shopNameSet);
        map.put(9, sceneSet);
        map.put(10, quotientSet);
        map.put(11, zmfLevelSet);

        return map;
    }

    //查询条件
    public Map<String, Set<String>> selectorMap(String startTime, String endTime) {
        Map<String, Set<String>> map = Maps.newHashMap();
        HashSet<String> shopNameSet = Sets.newHashSet();
        String redisKey = "selectorMap:" + startTime + ":" + endTime;
        if (redisTemplate.hasKey(redisKey)) {
            return (Map<String, Set<String>>) redisTemplate.opsForValue().get(redisKey);
        }
        List<RiskDistributionOrderOverdueDO> auditType =
                distributionOrderOverdueService.getAuditType(startTime, endTime, null);
        for (RiskDistributionOrderOverdueDO riskOrderOverdueDO : auditType) {
            shopNameSet.add(riskOrderOverdueDO.getShopName());
        }
        map.put("shopName", shopNameSet);
        redisTemplate.opsForValue().setIfAbsent(redisKey, map);
        redisTemplate.expire(redisKey, 24, TimeUnit.HOURS);

        return map;
    }

    public void export(RiskDistributionOverdueRequest request,
                       HttpServletResponse response) {
        ExportParams empExportParams = new ExportParams();
        empExportParams.setStyle(MyExcelExportStyler.class);
        empExportParams.setTitle("租赁商户大盘明细");
        empExportParams.setSheetName("租赁商户大盘明细");

        SingleResult<RiskGeneralVO> result = getList(request);
        // 创建sheet1使用得map
        // 递归将树形结构打平
        RiskGeneralVO vo = result.getData();
        if (Objects.isNull(vo)) {
            throw new RuntimeException("导出失败,数据为空");
        }
        List<RiskGeneralBusinessVO> list = vo.getList();
        //假设起始月 2024-02  当前日期是 2024-12-13  那么 2024-02这个起租月总共展示 mob1-到 mob10(12-2)
        //假设起始月 2024-03  当前日期是 2024-12-13  那么 2024-02这个起租月总共展示 mob1-到 mob9 (12-3)
        List<Map<String, Object>> copyList = Lists.newArrayList();
        copyList.addAll(fetchExportData(list, request.getCountType()));
        Integer maxTerm = list.stream().mapToInt(RiskGeneralBusinessVO::getTerm).max().getAsInt();
        List<ExcelExportEntity> excelExportEntities = setExportExcelStyle(maxTerm, request.getCountType());
        EasyPoiUtils.dynamicColumnExport(empExportParams, excelExportEntities, copyList, "租赁商户大盘明细.xls", response);
    }

    /**
     * 递归将树形结构打平
     *
     * @param list
     * @return
     */
    private List<Map<String, Object>> fetchExportData(List<RiskGeneralBusinessVO> list, String countType) {

        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        List<Map<String, Object>> copyList = Lists.newArrayList();
        for (RiskGeneralBusinessVO vo : list) {
            copyList.add(translateAmtVO(vo, countType));
            copyList.addAll(fetchExportData(vo.getList(), countType));
        }
        return copyList;
    }

    /**
     * 定义表格样式
     *
     * @return java.util.List<cn.afterturn.easypoi.excel.entity.params.ExcelExportEntity>
     * <AUTHOR>
     * @date 2019/6/21
     * @since 2.8.2
     */
    private List<ExcelExportEntity> setExportExcelStyle(Integer maxTerm, String countType) {
        //定义表格列名,该集合存放的就是表格的列明，每个对象就是表格中的一列
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());

        List<ExcelExportEntity> modelList = new ArrayList<>();
        //该对象就是定义列属性的对象
        ExcelExportEntity excelentity;
        int orderNum = 1;
        //定义第一个列
        excelentity = new ExcelExportEntity("描述", "desc");
        excelentity.setWidth(20);
        excelentity.setHeight(10);
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        excelentity = new ExcelExportEntity("业务总额-逾期单量", "overdueOrderCnt");
        excelentity.setWidth(20);
        excelentity.setNumFormat("#");
        excelentity.setHeight(10);
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        excelentity = new ExcelExportEntity("业务总额-逾期金额数", "overdueOrderAmt");
        excelentity.setWidth(20);
        excelentity.setHeight(10);
        excelentity.setNumFormat("#.##");
        excelentity.setOrderNum(orderNum++);
        modelList.add(excelentity);

        //定义第四个列,这边就是动态生成的,跟用用户选择的日期范围,动态生成列的数量
        //设置一个集合,存放动态生成的列
        for (int i = 1; i <= maxTerm; i++) {
            if (collect.contains(1)) {
                excelentity = new ExcelExportEntity("mob" + i + "_金额", "mob" + i + "Amt");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#.##");
                modelList.add(excelentity);
            }
            if (collect.contains(2)) {
                excelentity = new ExcelExportEntity("mob" + i + "_单量", "mob" + i + "Cnt");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#");
                modelList.add(excelentity);
            }
            if (collect.contains(3)) {
                excelentity = new ExcelExportEntity("mob" + i + "_单量比", "mob" + i + "CntRate");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setOrderNum(orderNum++);
                excelentity.setNumFormat("#.##");
                excelentity.setSuffix("%");
                modelList.add(excelentity);
            }
            if (collect.contains(4)) {
                excelentity = new ExcelExportEntity("mob" + i + "_金额比", "mob" + i + "AmtRate");
                excelentity.setWidth(20);
                excelentity.setHeight(10);
                excelentity.setNumFormat("#.##");
                excelentity.setOrderNum(orderNum++);
                excelentity.setSuffix("%");
                modelList.add(excelentity);
            }
        }

        return modelList;
    }


    /**
     * 金额数值导出转换
     */
    private Map<String, Object> translateAmtVO(RiskGeneralBusinessVO sourceVO, String countType) {
        RiskGeneralBusinessVO.InnerColumnVO vo = sourceVO.getTotal();
        Map<String, Object> map = new HashMap();
        map.put("desc", sourceVO.getDesc());
        map.put("overdueOrderCnt", vo.getAmtTotal().getOverdueOrderCnt());
        map.put("overdueOrderAmt", vo.getAmtTotal().getOverdueRentTotal());
        List<Integer> collect = Arrays.stream(countType.split(",")).map(Integer::parseInt).collect(Collectors.toList());
        if (collect.contains(1)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "Amt";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueRentTotal());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }
        }
        if (collect.contains(2)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "Cnt";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueOrderCnt());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }
        }
        if (collect.contains(3)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "CntRate";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getOverdueOrderRate());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }

        }

        if (collect.contains(4)) {
            for (int i = 1; i <= 30; i++) {
                String key = "mob" + i + "AmtRate";
                try {
                    Method getMethod = vo.getClass().getMethod("getMob" + i);
                    Object mob = getMethod.invoke(vo);
                    if (mob != null) {
                        map.put(key, ((RiskGeneralBusinessVO.InnerRiskVO) mob).getCountRate());
                    }
                } catch (Exception e) {
                    // 记录日志并处理异常，例如设置默认值或跳过
                    log.error("导出excel异常{}", e.getMessage());
                }
            }

        }
        return map;
    }

}

