package qnvip.data.overview.manager.customer;

import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RestController;
import qnvip.data.overview.domain.customer.OperateCustomerAgeDO;
import qnvip.data.overview.domain.customer.OperateCustomerEducationDO;
import qnvip.data.overview.domain.customer.OperateCustomerEducationDO;
import qnvip.data.overview.service.customer.OperateCustomerAgeService;
import qnvip.data.overview.service.customer.OperateCustomerEducationService;
import qnvip.data.overview.service.customer.impl.OperateCustomerEducationServiceImpl;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.vo.customer.OperateCustomerAgeVO;
import qnvip.data.overview.vo.customer.OperateCustomerEducationVO;
import qnvip.data.overview.vo.customer.OperateCustomerEducationVO;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/16
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class OperateCustomerEduManager {

    private final OperateCustomerEducationService educationServicee;


    /**
     * get data
     *
     * @param fromTime
     * @param toTime
     * @param miniType
     * @return
     */
    public List<OperateCustomerEducationVO> getListByEdu(String fromTime,
                                                         String toTime,
                                                         Integer miniType,
                                                         Integer type) {
        LinkedList<OperateCustomerEducationVO> list = Lists.newLinkedList();

        List<OperateCustomerEducationDO> collect = Optional
                .ofNullable(educationServicee.getList(fromTime, toTime, miniType, type))
                .orElse(Lists.newArrayList());
        // 总人数
        BigDecimal totalCount = ObjectUtils.getSum(collect, OperateCustomerEducationDO::getAmount);
        collect.stream()
                .collect(Collectors.groupingBy(OperateCustomerEducationDO::getEducation))
                .forEach((education, eduList) -> {
                    OperateCustomerEducationVO eduVO = new OperateCustomerEducationVO();
                    eduVO.setEducation(education);
                    BigDecimal count = ObjectUtils.getSum(eduList, OperateCustomerEducationDO::getAmount);
                    eduVO.setAmount(count.longValue());
                    eduVO.setRate(CalculateUtil.mul(CalculateUtil.div(count, totalCount), 100));
                    list.add(eduVO);
                });
        OperateCustomerEducationVO totalVo = new OperateCustomerEducationVO();
        totalVo.setAmount(totalCount.longValue());
        totalVo.setEducation("小计");
        list.sort(Comparator.comparing(OperateCustomerEducationVO::getAmount).reversed());
        list.addLast(totalVo);
        return list;
    }


}
