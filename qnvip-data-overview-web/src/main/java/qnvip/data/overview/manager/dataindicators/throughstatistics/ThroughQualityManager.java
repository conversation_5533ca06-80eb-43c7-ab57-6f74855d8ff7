package qnvip.data.overview.manager.dataindicators.throughstatistics;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleDO;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleOrderDO;
import qnvip.data.overview.domain.dataindicators.IndicatorPassCustomerDO;
import qnvip.data.overview.domain.dataindicators.IndicatorPassQualityDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.dataindicators.IndicatorTimeEnum;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleOrderService;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleService;
import qnvip.data.overview.service.dataindicators.IndicatorPassCustomerService;
import qnvip.data.overview.service.dataindicators.IndicatorPassQualityService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.dataindicators.IndicatorsUtil;
import qnvip.data.overview.vo.dataindicators.through.ThroughQualityVO;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/1/21
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ThroughQualityManager {

    private static final DateTimeFormatter DAY_DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_DF = DateTimeFormatter.ofPattern("yyyy-MM");

    private final IndicatorBeforeSaleService indicatorBeforeSaleService;
    private final IndicatorBeforeSaleOrderService indicatorBeforeSaleOrderService;
    private final IndicatorPassCustomerService indicatorPassCustomerService;
    private final IndicatorPassQualityService indicatorPassQualityService;


    /**
     * 质量结构
     */
    public List<ThroughQualityVO> customerQualityCompositionList(String sTime, String eTime, Integer dayOrMonth,
                                                                 List<Integer> miniTypeStrToList) {

        List<IndicatorBeforeSaleDO> visitorList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);

        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorPassCustomerDO> customerList = indicatorPassCustomerService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorPassQualityDO> qualityList = indicatorPassQualityService.getList(sTime, eTime, miniTypeStrToList);


        Map<String, List<ThroughQualityVO.DetailVO>> dailyOrMonthlyMap = getDailyOrMonthlyMap(dayOrMonth,
                visitorList, orderList, customerList, qualityList);
        //处理数据
        List<ThroughQualityVO> resultVO = dailyOrMonthlyMap.entrySet().stream().map(item -> {
            ThroughQualityVO dailyVO = getThroughGoodsVO(item.getValue(), null);
            dailyVO.setCode(IndicatorTimeEnum.DAILY.getCode());
            dailyVO.setCountDay(item.getKey());
            return dailyVO;
        }).collect(Collectors.toList());
        handleRingCompare(resultVO, miniTypeStrToList);
        return resultVO;
    }


    /**
     * 质量结构 30天和7天数据
     */
    public List<ThroughQualityVO> customerQualityCompositionFixedList(String sTime, String eTime,
                                                                      List<Integer> miniTypeStrToList) {
        List<IndicatorBeforeSaleDO> visitorList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);

        List<IndicatorPassCustomerDO> customerList = indicatorPassCustomerService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorPassQualityDO> qualityList = indicatorPassQualityService.getList(sTime, eTime, miniTypeStrToList);

        Map<String, List<ThroughQualityVO.DetailVO>> dailyOrMonthlyMap =
                getDailyOrMonthlyMap(1, visitorList, orderList, customerList, qualityList);

        List<ThroughQualityVO> resultVO = new ArrayList<>();
        List<ThroughQualityVO.DetailVO> monthlyDetailList = new ArrayList<>();
        List<ThroughQualityVO.DetailVO> weeklyDetailList = new ArrayList<>();
        AtomicInteger i = new AtomicInteger();
        dailyOrMonthlyMap.forEach((key, value) -> {
            if (i.get() < 7) {
                weeklyDetailList.addAll(value);
            }
            monthlyDetailList.addAll(value);
            i.incrementAndGet();
        });
        ThroughQualityVO weeklyVO = getThroughGoodsVO(weeklyDetailList, 7);
        weeklyVO.setCode(IndicatorTimeEnum.WEEKLY.getCode());
        weeklyVO.setCountDay(IndicatorTimeEnum.WEEKLY.getDescribe());
        ThroughQualityVO monthlyVO = getThroughGoodsVO(monthlyDetailList, 30);
        monthlyVO.setCode(IndicatorTimeEnum.MONTHLY.getCode());
        monthlyVO.setCountDay(IndicatorTimeEnum.MONTHLY.getDescribe());
        resultVO.add(monthlyVO);
        resultVO.add(weeklyVO);
        handleRingCompare(resultVO, miniTypeStrToList);
        return resultVO;
    }


    private Map<String, List<ThroughQualityVO.DetailVO>> getDailyOrMonthlyMap(Integer dayOrMonth,
                                                                              List<IndicatorBeforeSaleDO> visitorsList,
                                                                              List<IndicatorBeforeSaleOrderDO> orderList,
                                                                              List<IndicatorPassCustomerDO> passCustomerList,
                                                                              List<IndicatorPassQualityDO> qualityList) {
        Map<String, List<IndicatorBeforeSaleDO>> visitorsGroupMap;
        Map<String, List<IndicatorBeforeSaleOrderDO>> orderGroupMap;
        Map<String, List<IndicatorPassCustomerDO>> passCustomerGroupMap;
        Map<String, List<IndicatorPassQualityDO>> passQualityGroupMap;
        if (dayOrMonth == 1) {
            visitorsGroupMap =
                    visitorsList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
            orderGroupMap = orderList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
            passCustomerGroupMap =
                    passCustomerList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
            passQualityGroupMap =
                    qualityList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(DAY_DF)));
        } else {
            visitorsGroupMap =
                    visitorsList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
            orderGroupMap = orderList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
            passCustomerGroupMap =
                    passCustomerList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
            passQualityGroupMap =
                    qualityList.stream().collect(Collectors.groupingBy(o -> o.getCountDay().format(MONTH_DF)));
        }
        Map<String, List<ThroughQualityVO.DetailVO>> dailyDetailMap = new TreeMap<>(Comparator.reverseOrder());
        for (Map.Entry<String, List<IndicatorBeforeSaleDO>> visitorsEntry : visitorsGroupMap.entrySet()) {
            String dayOrMonthValue = visitorsEntry.getKey();
            List<ThroughQualityVO.DetailVO> detailVOList = new ArrayList<>();
            List<IndicatorBeforeSaleDO> visitors = visitorsEntry.getValue();
            List<IndicatorBeforeSaleOrderDO> orders =
                    Optional.ofNullable(orderGroupMap.get(visitorsEntry.getKey())).orElse(new ArrayList<>());
            List<IndicatorPassCustomerDO> passCustomers =
                    Optional.ofNullable(passCustomerGroupMap.get(visitorsEntry.getKey())).orElse(new ArrayList<>());
            List<IndicatorPassQualityDO> passQualities =
                    Optional.ofNullable(passQualityGroupMap.get(visitorsEntry.getKey())).orElse(new ArrayList<>());

            Map<Integer, List<IndicatorBeforeSaleDO>> visitorsMiniTypeMap =
                    visitors.stream().collect(Collectors.groupingBy(IndicatorBeforeSaleDO::getMiniType));
            Map<Integer, List<IndicatorBeforeSaleOrderDO>> ordersMiniTypeMap =
                    orders.stream().collect(Collectors.groupingBy(IndicatorBeforeSaleOrderDO::getMiniType));
            Map<Integer, List<IndicatorPassCustomerDO>> passCustomerMiniTypeMap =
                    passCustomers.stream().collect(Collectors.groupingBy(IndicatorPassCustomerDO::getMiniType));
            Map<Integer, List<IndicatorPassQualityDO>> passQualityMiniTypeMap =
                    passQualities.stream().collect(Collectors.groupingBy(IndicatorPassQualityDO::getMiniType));

            for (Map.Entry<Integer, List<IndicatorBeforeSaleDO>> visitorsMiniTypeEntry :
                    visitorsMiniTypeMap.entrySet()) {
                Integer miniKey = visitorsMiniTypeEntry.getKey();
                List<IndicatorBeforeSaleDO> value1 = visitorsMiniTypeEntry.getValue();
                List<IndicatorBeforeSaleOrderDO> value2 =
                        Optional.ofNullable(ordersMiniTypeMap.get(miniKey)).orElse(new ArrayList<>());
                List<IndicatorPassCustomerDO> value3 =
                        Optional.ofNullable(passCustomerMiniTypeMap.get(miniKey)).orElse(new ArrayList<>());
                List<IndicatorPassQualityDO> value4 =
                        Optional.ofNullable(passQualityMiniTypeMap.get(miniKey)).orElse(new ArrayList<>());
                ThroughQualityVO.DetailVO detailVO = new ThroughQualityVO.DetailVO();
                detailVO.setMiniType(miniKey);
                for (IndicatorBeforeSaleDO visitorsDO : value1) {
                    detailVO.setRiskExecCount(CalculateUtil.add(visitorsDO.getOrderCount(),
                            detailVO.getRiskExecCount()));
                }
                for (IndicatorBeforeSaleOrderDO orderDO : value2) {
                    detailVO.setRiskPassCount(CalculateUtil.add(orderDO.getRiskApprovedCount(),
                            detailVO.getRiskPassCount()));
                    detailVO.setRiskRefusedCount(CalculateUtil.add(orderDO.getRiskRefusedCount(),
                            detailVO.getRiskRefusedCount()));
                }
                for (IndicatorPassCustomerDO customerDO : value3) {
                    detailVO.setPlanOneTotal(CalculateUtil.add(customerDO.getPlanFirstTotal() + customerDO.getPlanThirdTotal(),
                            detailVO.getPlanOneTotal()));
                    detailVO.setPlanTwoTotal(CalculateUtil.add(customerDO.getPlanSecondTotal(),
                            detailVO.getPlanTwoTotal()));
                    //3+N方案保证金期数
                    detailVO.setPlanOneTotalTerm(CalculateUtil.add(CalculateUtil.add(customerDO.getPlanFirstOneTerm(),
                            2 * customerDO.getPlanFirstTwoTerm()), CalculateUtil.add(customerDO.getPlanThirdOneTerm(),
                            2 * customerDO.getPlanThirdTwoTerm())));
                    //12+N方案保证金期数
                    detailVO.setPlanTwoTotalTerm(CalculateUtil.add(customerDO.getPlanSecondOneTerm(),
                            2 * customerDO.getPlanSecondTwoTerm()));
                }
                for (IndicatorPassQualityDO passQualityDO : value4) {
                    detailVO.setPlanOneLevelOne(CalculateUtil.add(passQualityDO.getPlanOneLevelOne(),
                            detailVO.getPlanOneLevelOne()));
                    detailVO.setPlanOneLevelTwo(CalculateUtil.add(passQualityDO.getPlanOneLevelTwo(),
                            detailVO.getPlanOneLevelTwo()));
                    detailVO.setPlanOneLevelThree(CalculateUtil.add(passQualityDO.getPlanOneLevelThree(),
                            detailVO.getPlanOneLevelThree()));
                    detailVO.setPlanOneLevelFour(CalculateUtil.add(passQualityDO.getPlanOneLevelFour(),
                            detailVO.getPlanOneLevelFour()));
                    detailVO.setPlanOneLevelFive(CalculateUtil.add(passQualityDO.getPlanOneLevelFive(),
                            detailVO.getPlanOneLevelFive()));
                    detailVO.setPlanTwoLevelOne(CalculateUtil.add(passQualityDO.getPlanTwoLevelOne(),
                            detailVO.getPlanTwoLevelOne()));
                    detailVO.setPlanTwoLevelTwo(CalculateUtil.add(passQualityDO.getPlanTwoLevelTwo(),
                            detailVO.getPlanTwoLevelTwo()));
                    detailVO.setPlanTwoLevelThree(CalculateUtil.add(passQualityDO.getPlanTwoLevelThree(),
                            detailVO.getPlanTwoLevelThree()));
                    detailVO.setPlanTwoLevelFour(CalculateUtil.add(passQualityDO.getPlanTwoLevelFour(),
                            detailVO.getPlanTwoLevelFour()));
                    detailVO.setPlanTwoLevelFive(CalculateUtil.add(passQualityDO.getPlanTwoLevelFive(),
                            detailVO.getPlanTwoLevelFive()));
                }
                detailVOList.add(detailVO);
            }
            dailyDetailMap.put(dayOrMonthValue, detailVOList);
        }
        return dailyDetailMap;
    }


    private ThroughQualityVO getThroughGoodsVO(List<ThroughQualityVO.DetailVO> detailVOList, Integer divisor) {
        ThroughQualityVO beforeSaleVO = new ThroughQualityVO();
        Map<Integer, List<ThroughQualityVO.DetailVO>> detailMiniTypeMap =
                detailVOList.stream().collect(Collectors.groupingBy(ThroughQualityVO.DetailVO::getMiniType));
        List<ThroughQualityVO.DetailVO> resList = new ArrayList<>();
        for (Map.Entry<Integer, List<ThroughQualityVO.DetailVO>> entry : detailMiniTypeMap.entrySet()) {
            Integer key = entry.getKey();
            List<ThroughQualityVO.DetailVO> value = entry.getValue();
            ThroughQualityVO.DetailVO detailVO = new ThroughQualityVO.DetailVO();
            detailVO.setMiniType(key);
            detailVO.setMiniTypeSort(MiniTypeEnum.getSortByCode(key));
            addData(detailVO, value, divisor);
            resList.add(detailVO);
        }
        beforeSaleVO.setDetailList(resList);
        resList.sort(Comparator.comparing(ThroughQualityVO.DetailVO::getMiniTypeSort));
        ThroughQualityVO.DetailVO totalDataVO = new ThroughQualityVO.DetailVO();
        addData(totalDataVO, detailVOList, divisor);
        beforeSaleVO.setTotalData(totalDataVO);
        return beforeSaleVO;
    }


    private void addData(ThroughQualityVO.DetailVO totalDataVO, List<ThroughQualityVO.DetailVO> detailVOList,
                         Integer divisor) {
        for (ThroughQualityVO.DetailVO detailVO : detailVOList) {
            totalDataVO.setRiskExecCount(CalculateUtil.add(totalDataVO.getRiskExecCount(),
                    detailVO.getRiskExecCount()));
            totalDataVO.setRiskPassCount(CalculateUtil.add(totalDataVO.getRiskPassCount(),
                    detailVO.getRiskPassCount()));
            totalDataVO.setRiskRefusedCount(CalculateUtil.add(totalDataVO.getRiskRefusedCount(),
                    detailVO.getRiskRefusedCount()));
            totalDataVO.setPlanOneTotal(CalculateUtil.add(totalDataVO.getPlanOneTotal(),
                    detailVO.getPlanOneTotal()));
            totalDataVO.setPlanOneTotalTerm(CalculateUtil.add(totalDataVO.getPlanOneTotalTerm(),
                    detailVO.getPlanOneTotalTerm()));
            totalDataVO.setPlanTwoTotal(CalculateUtil.add(totalDataVO.getPlanTwoTotal(),
                    detailVO.getPlanTwoTotal()));
            totalDataVO.setPlanTwoTotalTerm(CalculateUtil.add(totalDataVO.getPlanTwoTotalTerm(),
                    detailVO.getPlanTwoTotalTerm()));
            totalDataVO.setPlanOneLevelOne(CalculateUtil.add(totalDataVO.getPlanOneLevelOne(),
                    detailVO.getPlanOneLevelOne()));
            totalDataVO.setPlanOneLevelTwo(CalculateUtil.add(totalDataVO.getPlanOneLevelTwo(),
                    detailVO.getPlanOneLevelTwo()));
            totalDataVO.setPlanOneLevelThree(CalculateUtil.add(totalDataVO.getPlanOneLevelThree(),
                    detailVO.getPlanOneLevelThree()));
            totalDataVO.setPlanOneLevelFour(CalculateUtil.add(totalDataVO.getPlanOneLevelFour(),
                    detailVO.getPlanOneLevelFour()));
            totalDataVO.setPlanOneLevelFive(CalculateUtil.add(totalDataVO.getPlanOneLevelFive(),
                    detailVO.getPlanOneLevelFive()));
            totalDataVO.setPlanTwoLevelOne(CalculateUtil.add(totalDataVO.getPlanTwoLevelOne(),
                    detailVO.getPlanTwoLevelOne()));
            totalDataVO.setPlanTwoLevelTwo(CalculateUtil.add(totalDataVO.getPlanTwoLevelTwo(),
                    detailVO.getPlanTwoLevelTwo()));
            totalDataVO.setPlanTwoLevelThree(CalculateUtil.add(totalDataVO.getPlanTwoLevelThree(),
                    detailVO.getPlanTwoLevelThree()));
            totalDataVO.setPlanTwoLevelFour(CalculateUtil.add(totalDataVO.getPlanTwoLevelFour(),
                    detailVO.getPlanTwoLevelFour()));
            totalDataVO.setPlanTwoLevelFive(CalculateUtil.add(totalDataVO.getPlanTwoLevelFive(),
                    detailVO.getPlanTwoLevelFive()));
        }
        if (divisor != null && divisor != 0 && divisor != 1) {
            totalDataVO.setRiskExecCount(CalculateUtil.div(totalDataVO.getRiskExecCount(), divisor, 2));
            totalDataVO.setRiskPassCount(CalculateUtil.div(totalDataVO.getRiskPassCount(), divisor, 2));
            totalDataVO.setRiskRefusedCount(CalculateUtil.div(totalDataVO.getRiskRefusedCount(), divisor, 2));
            totalDataVO.setPlanOneTotal(CalculateUtil.div(totalDataVO.getPlanOneTotal(), divisor, 2));
            totalDataVO.setPlanTwoTotal(CalculateUtil.div(totalDataVO.getPlanTwoTotal(), divisor, 2));
            totalDataVO.setPlanOneTotalTerm(CalculateUtil.div(totalDataVO.getPlanOneTotalTerm(), divisor, 2));
            totalDataVO.setPlanTwoTotalTerm(CalculateUtil.div(totalDataVO.getPlanTwoTotalTerm(), divisor, 2));
            totalDataVO.setPlanOneLevelOne(CalculateUtil.div(totalDataVO.getPlanOneLevelOne(), divisor, 2));
            totalDataVO.setPlanOneLevelTwo(CalculateUtil.div(totalDataVO.getPlanOneLevelTwo(), divisor, 2));
            totalDataVO.setPlanOneLevelThree(CalculateUtil.div(totalDataVO.getPlanOneLevelThree(), divisor, 2));
            totalDataVO.setPlanOneLevelFour(CalculateUtil.div(totalDataVO.getPlanOneLevelFour(), divisor, 2));
            totalDataVO.setPlanOneLevelFive(CalculateUtil.div(totalDataVO.getPlanOneLevelFive(), divisor, 2));
            totalDataVO.setPlanTwoLevelOne(CalculateUtil.div(totalDataVO.getPlanTwoLevelOne(), divisor, 2));
            totalDataVO.setPlanTwoLevelTwo(CalculateUtil.div(totalDataVO.getPlanTwoLevelTwo(), divisor, 2));
            totalDataVO.setPlanTwoLevelThree(CalculateUtil.div(totalDataVO.getPlanTwoLevelThree(), divisor, 2));
            totalDataVO.setPlanTwoLevelFour(CalculateUtil.div(totalDataVO.getPlanTwoLevelFour(), divisor, 2));
            totalDataVO.setPlanTwoLevelFive(CalculateUtil.div(totalDataVO.getPlanTwoLevelFive(), divisor, 2));
        }
        totalDataVO.setPlanOneTotalRate(CalculateUtil.div(totalDataVO.getPlanOneTotal(),
                CalculateUtil.div(totalDataVO.getRiskExecCount(), 100), 2));
        totalDataVO.setPlanTwoTotalRate(CalculateUtil.div(totalDataVO.getPlanTwoTotal(),
                CalculateUtil.div(totalDataVO.getRiskExecCount(), 100), 2));
        totalDataVO.setPlanOneLevelOneRate(CalculateUtil.div(totalDataVO.getPlanOneLevelOne(),
                CalculateUtil.div(totalDataVO.getPlanOneTotal(), 100), 2));
        totalDataVO.setPlanOneLevelTwoRate(CalculateUtil.div(totalDataVO.getPlanOneLevelTwo(),
                CalculateUtil.div(totalDataVO.getPlanOneTotal(), 100), 2));
        totalDataVO.setPlanOneLevelThreeRate(CalculateUtil.div(totalDataVO.getPlanOneLevelThree(),
                CalculateUtil.div(totalDataVO.getPlanOneTotal(), 100), 2));
        totalDataVO.setPlanOneLevelFourRate(CalculateUtil.div(totalDataVO.getPlanOneLevelFour(),
                CalculateUtil.div(totalDataVO.getPlanOneTotal(), 100), 2));
        totalDataVO.setPlanOneLevelFiveRate(CalculateUtil.div(totalDataVO.getPlanOneLevelFive(),
                CalculateUtil.div(totalDataVO.getPlanOneTotal(), 100), 2));
        totalDataVO.setPlanTwoLevelOneRate(CalculateUtil.div(totalDataVO.getPlanTwoLevelOne(),
                CalculateUtil.div(totalDataVO.getPlanTwoTotal(), 100), 2));
        totalDataVO.setPlanTwoLevelTwoRate(CalculateUtil.div(totalDataVO.getPlanTwoLevelTwo(),
                CalculateUtil.div(totalDataVO.getPlanTwoTotal(), 100), 2));
        totalDataVO.setPlanTwoLevelThreeRate(CalculateUtil.div(totalDataVO.getPlanTwoLevelThree(),
                CalculateUtil.div(totalDataVO.getPlanTwoTotal(), 100), 2));
        totalDataVO.setPlanTwoLevelFourRate(CalculateUtil.div(totalDataVO.getPlanTwoLevelFour(),
                CalculateUtil.div(totalDataVO.getPlanTwoTotal(), 100), 2));
        totalDataVO.setPlanTwoLevelFiveRate(CalculateUtil.div(totalDataVO.getPlanTwoLevelFive(),
                CalculateUtil.div(totalDataVO.getPlanTwoTotal(), 100), 2));

    }


    /**
     * 计算环比
     *
     * @param resultVO
     * @param miniTypeStrToList
     */
    private void handleRingCompare(List<ThroughQualityVO> resultVO, List<Integer> miniTypeStrToList) {
        ThroughQualityVO lastVO = lastFixedList(miniTypeStrToList);
        Map<Integer, ThroughQualityVO.DetailVO> lastDetailMap =
                lastVO.getDetailList().stream().collect(Collectors.toMap(ThroughQualityVO.DetailVO::getMiniType,
                        detailVO -> detailVO));
        for (ThroughQualityVO beforeSaleVO : resultVO) {
            ThroughQualityVO.DetailVO totalData = beforeSaleVO.getTotalData();
            ThroughQualityVO.DetailVO lastTotal = lastVO.getTotalData();
            calculateRingCompare(totalData, lastTotal);
            for (ThroughQualityVO.DetailVO detailVO : beforeSaleVO.getDetailList()) {
                ThroughQualityVO.DetailVO lastDetailVO =
                        Optional.ofNullable(lastDetailMap.get(detailVO.getMiniType())).orElse(new ThroughQualityVO.DetailVO());
                calculateRingCompare(detailVO, lastDetailVO);
            }
        }
    }


    private void calculateRingCompare(ThroughQualityVO.DetailVO detailVO,
                                      ThroughQualityVO.DetailVO lastDetailVO) {
        detailVO.setRiskExecCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskExecCount(),
                lastDetailVO.getRiskExecCount()), CalculateUtil.div(lastDetailVO.getRiskExecCount(), 100), 2));
        detailVO.setRiskPassCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskPassCount(),
                lastDetailVO.getRiskPassCount()), CalculateUtil.div(lastDetailVO.getRiskPassCount(), 100), 2));
        detailVO.setRiskRefusedCountRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getRiskRefusedCount(),
                lastDetailVO.getRiskRefusedCount()), CalculateUtil.div(lastDetailVO.getRiskRefusedCount(), 100), 2));
        detailVO.setPlanOneTotalRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneTotal(),
                lastDetailVO.getPlanOneTotal()), CalculateUtil.div(lastDetailVO.getPlanOneTotal(), 100), 2));
        detailVO.setPlanOneTotalTermRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneTotalTerm(),
                lastDetailVO.getPlanOneTotalTerm()), CalculateUtil.div(lastDetailVO.getPlanOneTotalTerm(), 100), 2));
        detailVO.setPlanTwoTotalRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoTotal(),
                lastDetailVO.getPlanTwoTotal()), CalculateUtil.div(lastDetailVO.getPlanTwoTotal(), 100), 2));
        detailVO.setPlanTwoTotalTermRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoTotalTerm(),
                lastDetailVO.getPlanTwoTotalTerm()), CalculateUtil.div(lastDetailVO.getPlanTwoTotalTerm(), 100), 2));
        detailVO.setPlanOneLevelOneRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneLevelOne(),
                lastDetailVO.getPlanOneLevelOne()), CalculateUtil.div(lastDetailVO.getPlanOneLevelOne(), 100), 2));
        detailVO.setPlanOneLevelTwoRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneLevelTwo(),
                lastDetailVO.getPlanOneLevelTwo()), CalculateUtil.div(lastDetailVO.getPlanOneLevelTwo(), 100), 2));
        detailVO.setPlanOneLevelThreeRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneLevelThree(),
                lastDetailVO.getPlanOneLevelThree()), CalculateUtil.div(lastDetailVO.getPlanOneLevelThree(), 100), 2));
        detailVO.setPlanOneLevelFourRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneLevelFour(),
                lastDetailVO.getPlanOneLevelFour()), CalculateUtil.div(lastDetailVO.getPlanOneLevelFour(), 100), 2));
        detailVO.setPlanOneLevelFiveRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanOneLevelFive(),
                lastDetailVO.getPlanOneLevelFive()), CalculateUtil.div(lastDetailVO.getPlanOneLevelFive(), 100), 2));
        detailVO.setPlanTwoLevelOneRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoLevelOne(),
                lastDetailVO.getPlanTwoLevelOne()), CalculateUtil.div(lastDetailVO.getPlanTwoLevelOne(), 100), 2));
        detailVO.setPlanTwoLevelTwoRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoLevelTwo(),
                lastDetailVO.getPlanTwoLevelTwo()), CalculateUtil.div(lastDetailVO.getPlanTwoLevelTwo(), 100), 2));
        detailVO.setPlanTwoLevelThreeRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoLevelThree(),
                lastDetailVO.getPlanTwoLevelThree()), CalculateUtil.div(lastDetailVO.getPlanTwoLevelThree(), 100), 2));
        detailVO.setPlanTwoLevelFourRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoLevelFour(),
                lastDetailVO.getPlanTwoLevelFour()), CalculateUtil.div(lastDetailVO.getPlanTwoLevelFour(), 100), 2));
        detailVO.setPlanTwoLevelFiveRingCompare(CalculateUtil.div(CalculateUtil.sub(detailVO.getPlanTwoLevelFive(),
                lastDetailVO.getPlanTwoLevelFive()), CalculateUtil.div(lastDetailVO.getPlanTwoLevelFive(), 100), 2));
    }


    public ThroughQualityVO lastFixedList(List<Integer> miniTypeStrToList) {
        Map<String, String> timeMap = IndicatorsUtil.getLastDefaultStartAndEndTime();
        String sTime = timeMap.get("sTime");
        String eTime = timeMap.get("eTime");
        List<IndicatorBeforeSaleDO> visitorList = indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);

        List<IndicatorPassCustomerDO> customerList = indicatorPassCustomerService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorBeforeSaleOrderDO> orderList = indicatorBeforeSaleOrderService.getList(sTime, eTime,
                miniTypeStrToList);

        List<IndicatorPassQualityDO> qualityList = indicatorPassQualityService.getList(sTime, eTime, miniTypeStrToList);

        Map<String, List<ThroughQualityVO.DetailVO>> dailyOrMonthlyMap =
                getDailyOrMonthlyMap(1, visitorList, orderList, customerList, qualityList);

        List<ThroughQualityVO.DetailVO> monthlyDetailList = new ArrayList<>();
        dailyOrMonthlyMap.forEach((key, value) -> {
            monthlyDetailList.addAll(value);
        });
        return getThroughGoodsVO(monthlyDetailList, 30);
    }


}