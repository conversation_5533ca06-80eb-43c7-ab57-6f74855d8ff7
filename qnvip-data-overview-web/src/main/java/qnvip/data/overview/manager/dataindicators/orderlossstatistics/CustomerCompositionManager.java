package qnvip.data.overview.manager.dataindicators.orderlossstatistics;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleDO;
import qnvip.data.overview.domain.dataindicators.IndicatorLossOrderCustomerDO;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderCustomerDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.dataindicators.IndicatorTimeEnum;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleService;
import qnvip.data.overview.service.dataindicators.IndicatorLossOrderCustomerService;
import qnvip.data.overview.service.dataindicators.IndicatorOrderCustomerService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.dataindicators.IndicatorsUtil;
import qnvip.data.overview.vo.dataindicators.orderloss.CustomerCompositionVO;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * 流失统计(下单) - 客户组成
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerCompositionManager {

    private static final DateTimeFormatter DAY_DF = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter MONTH_DF = DateTimeFormatter.ofPattern("yyyy-MM");

    private final IndicatorOrderCustomerService indicatorOrderCustomerService;
    private final IndicatorLossOrderCustomerService indicatorLossOrderCustomerService;
    private final IndicatorBeforeSaleService indicatorBeforeSaleService;


    /**
     * 客户组成
     *
     * @param sTime       开始时间
     * @param eTime       结束时间
     * @param dayOrMonth  时间单位
     * @param miniTypeStr 来源平台
     * @return List<CustomerCompositionVO>
     */
    public List<CustomerCompositionVO> customerComposition(String sTime, String eTime,
                                                           Integer dayOrMonth, String miniTypeStr) {
        List<Integer> miniTypeStrToList = IndicatorsUtil.miniTypeStrToList(miniTypeStr);
        List<IndicatorOrderCustomerDO> indicatorOrderCustomerList =
                indicatorOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerList =
                indicatorLossOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorBeforeSaleDO> indicatorBeforeSaleList =
                indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);

        //处理组合数据
        Map<String, List<CustomerCompositionVO.DetailVO>> dailyOrMonthlyMap = getDailyOrMonthlyMap(dayOrMonth,
                indicatorBeforeSaleList, indicatorOrderCustomerList, indicatorLossOrderCustomerList);

        //Map 转List
        List<CustomerCompositionVO> resultVO = dailyOrMonthlyMap.entrySet().stream().map(item -> {
            CustomerCompositionVO dailyVO = getCustomerCompositionVO(item.getValue(), null);
            dailyVO.setCode(IndicatorTimeEnum.DAILY.getCode());
            dailyVO.setCountDay(item.getKey());
            return dailyVO;
        }).collect(Collectors.toList());
        //计算环比
        handleRingCompare(resultVO, miniTypeStrToList);
        return resultVO;
    }

    /**
     * 客户组成30天和7天的数据
     *
     * @param sTime       开始时间
     * @param eTime       结束时间
     * @param miniTypeStr 来源平台
     * @return List<CustomerCompositionVO>
     */
    public List<CustomerCompositionVO> customerCompositionFixedList(String sTime, String eTime, String miniTypeStr) {
        List<Integer> miniTypeStrToList = IndicatorsUtil.miniTypeStrToList(miniTypeStr);
        List<IndicatorOrderCustomerDO> indicatorOrderCustomerList =
                indicatorOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerList =
                indicatorLossOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorBeforeSaleDO> indicatorBeforeSaleList =
                indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);
        //处理组合数据
        Map<String, List<CustomerCompositionVO.DetailVO>> dailyOrMonthlyMap = getDailyOrMonthlyMap(1,
                indicatorBeforeSaleList, indicatorOrderCustomerList, indicatorLossOrderCustomerList);

        List<CustomerCompositionVO> resultVO = new ArrayList<>();
        List<CustomerCompositionVO.DetailVO> monthlyDetailList = new ArrayList<>();
        List<CustomerCompositionVO.DetailVO> weeklyDetailList = new ArrayList<>();

        AtomicInteger i = new AtomicInteger(0);
        dailyOrMonthlyMap.forEach((key, value) -> {
            if (i.get() < 7) {
                weeklyDetailList.addAll(value);
            }
            monthlyDetailList.addAll(value);
            i.getAndIncrement();
        });
        CustomerCompositionVO weeklyVO = getCustomerCompositionVO(weeklyDetailList, 7);
        weeklyVO.setCode(IndicatorTimeEnum.WEEKLY.getCode());
        weeklyVO.setCountDay(IndicatorTimeEnum.WEEKLY.getDescribe());
        CustomerCompositionVO monthlyVO = getCustomerCompositionVO(monthlyDetailList, 30);
        monthlyVO.setCode(IndicatorTimeEnum.MONTHLY.getCode());
        monthlyVO.setCountDay(IndicatorTimeEnum.MONTHLY.getDescribe());
        resultVO.add(monthlyVO);
        resultVO.add(weeklyVO);
        handleRingCompare(resultVO, miniTypeStrToList);
        return resultVO;
    }


    private Map<String, List<CustomerCompositionVO.DetailVO>> getDailyOrMonthlyMap(Integer dayOrMonth,
                                                                                   List<IndicatorBeforeSaleDO> indicatorBeforeSaleList,
                                                                                   List<IndicatorOrderCustomerDO> indicatorOrderCustomerList,
                                                                                   List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerList) {
        Map<String, List<IndicatorBeforeSaleDO>> indicatorBeforeSaleMap = indicatorBeforeSaleList
                .stream().collect(Collectors.groupingBy(o ->
                        o.getCountDay().format(Objects.equals(dayOrMonth, 1) ? DAY_DF : MONTH_DF)));

        Map<String, List<IndicatorOrderCustomerDO>> indicatorOrderCustomerMap = indicatorOrderCustomerList
                .stream().collect(Collectors.groupingBy(o ->
                        o.getCountDay().format(Objects.equals(dayOrMonth, 1) ? DAY_DF : MONTH_DF)));

        Map<String, List<IndicatorLossOrderCustomerDO>> indicatorLossOrderCustomerMap = indicatorLossOrderCustomerList
                .stream().collect(Collectors.groupingBy(o ->
                        o.getCountDay().format(Objects.equals(dayOrMonth, 1) ? DAY_DF : MONTH_DF)));

        Map<String, List<CustomerCompositionVO.DetailVO>> dailyDetailMap = new TreeMap<>(Comparator.reverseOrder());

        indicatorBeforeSaleMap.forEach((key, value) -> {

            List<CustomerCompositionVO.DetailVO> detailVOList = new ArrayList<>();

            List<IndicatorOrderCustomerDO> indicatorOrderCustomerDoList =
                    Optional.ofNullable(indicatorOrderCustomerMap.get(key)).orElse(new ArrayList<>());

            List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerDoList =
                    Optional.ofNullable(indicatorLossOrderCustomerMap.get(key)).orElse(new ArrayList<>());

            //根据MiniType进行分组
            Map<Integer, List<IndicatorBeforeSaleDO>> indicatorBeforeSaleMiniTypeMap = value.stream()
                    .collect(Collectors.groupingBy(IndicatorBeforeSaleDO::getMiniType));
            Map<Integer, List<IndicatorOrderCustomerDO>> indicatorOrderCustomerMiniTypeMap =
                    indicatorOrderCustomerDoList.stream().
                            collect(Collectors.groupingBy(IndicatorOrderCustomerDO::getMiniType));

            Map<Integer, List<IndicatorLossOrderCustomerDO>> indicatorLossOrderCustomerMiniTypeMap =
                    indicatorLossOrderCustomerDoList.stream()
                            .collect(Collectors.groupingBy(IndicatorLossOrderCustomerDO::getMiniType));

            indicatorBeforeSaleMiniTypeMap.forEach((miniType, list) -> {

                List<IndicatorOrderCustomerDO> indicatorOrderCustomerMiniTypeList =
                        Optional.ofNullable(indicatorOrderCustomerMiniTypeMap.get(miniType)).orElse(new ArrayList<>());

                List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerMiniTypeList =
                        Optional.ofNullable(indicatorLossOrderCustomerMiniTypeMap.get(miniType)).orElse(new ArrayList<>());

                CustomerCompositionVO.DetailVO detailVO = new CustomerCompositionVO.DetailVO();
                detailVO.setMiniType(miniType);

                list.forEach(item -> {
                    //下单意愿强烈人数
                    detailVO.setIntentionCount(CalculateUtil.add(item.getIntentionCount(), detailVO.getIntentionCount()));
                    //下单人数
                    detailVO.setOrderCount(CalculateUtil.add(item.getOrderCount(), detailVO.getOrderCount()));
                    //取消下单人数
                    detailVO.setCancelCount(CalculateUtil.add(item.getCancelCount(), detailVO.getCancelCount()));
                });
                //下单人数中，风控通过未付/风控N天之前被拒人数
                indicatorOrderCustomerMiniTypeList.forEach(item -> detailVO.setOrderRiskApprovedNotPay(CalculateUtil.add(
                        item.getOrderRiskApprovedNotPay(), detailVO.getOrderRiskApprovedNotPay())));

                indicatorLossOrderCustomerMiniTypeList.forEach(item -> {
                    //下单放弃人数中,N小时内注册人数
                    detailVO.setLtNhRegisterCount(CalculateUtil.add(
                            item.getLtNhRegisterCount(), detailVO.getLtNhRegisterCount()));
                    //下单放弃人数中,已注册未下单人数
                    detailVO.setRegisterAndNotOrder(CalculateUtil.add(
                            item.getRegisterAndNotOrder(), detailVO.getRegisterAndNotOrder()));
                    //下单放弃人数中,服务中客户数
                    detailVO.setInServiceCount(CalculateUtil.add(
                            item.getInServiceCount(), detailVO.getInServiceCount()));
                    //下单放弃人数中,服务结束客户数
                    detailVO.setOutServiceCount(CalculateUtil.add(
                            item.getOutServiceCount(), detailVO.getOutServiceCount()));
                    //下单放弃人数中,N天内被拒人数
                    detailVO.setLtNdRefusedCount(CalculateUtil.add(
                            item.getLtNdRefusedCount(), detailVO.getLtNdRefusedCount()));
                });
                detailVOList.add(detailVO);
            });
            dailyDetailMap.put(key, detailVOList);
        });
        return dailyDetailMap;
    }

    private CustomerCompositionVO getCustomerCompositionVO(List<CustomerCompositionVO.DetailVO> list,
                                                           Integer divisor) {
        CustomerCompositionVO dailyVO = new CustomerCompositionVO();
        Map<Integer, List<CustomerCompositionVO.DetailVO>> detailMiniTypeMap = list.stream()
                .collect(Collectors.groupingBy(CustomerCompositionVO.DetailVO::getMiniType));
        //miniType分分组数据
        List<CustomerCompositionVO.DetailVO> detailVOS = detailMiniTypeMap.entrySet().stream().map(item -> {
            Integer miniType = item.getKey();
            CustomerCompositionVO.DetailVO detailVO = new CustomerCompositionVO.DetailVO();
            detailVO.setMiniType(miniType);
            detailVO.setMiniTypeSort(MiniTypeEnum.getSortByCode(miniType));
            addData(detailVO, item.getValue(), divisor);
            return detailVO;
        }).sorted(Comparator.comparing(CustomerCompositionVO.DetailVO::getMiniTypeSort)).collect(Collectors.toList());
        dailyVO.setDetailList(detailVOS);
        //总体数据
        CustomerCompositionVO.DetailVO totalDataVO = new CustomerCompositionVO.DetailVO();
        addData(totalDataVO, list, divisor);
        dailyVO.setTotalData(totalDataVO);
        return dailyVO;
    }

    private void addData(CustomerCompositionVO.DetailVO totalDataVO,
                         List<CustomerCompositionVO.DetailVO> detailVOList, Integer divisor) {

        detailVOList.forEach(detailVO -> {
            //下单意愿强烈人数
            totalDataVO.setIntentionCount(CalculateUtil.add(detailVO.getIntentionCount(), totalDataVO.getIntentionCount()));
            //取消下单人数
            totalDataVO.setCancelCount(CalculateUtil.add(detailVO.getCancelCount(), totalDataVO.getCancelCount()));
            //下单成功人数
            totalDataVO.setOrderCount(CalculateUtil.add(detailVO.getOrderCount(), totalDataVO.getOrderCount()));
            //下单人数中，风控通过未付/风控N天之前被拒人数
            totalDataVO.setOrderRiskApprovedNotPay(CalculateUtil.add(
                    detailVO.getOrderRiskApprovedNotPay(), totalDataVO.getOrderRiskApprovedNotPay()));
            //下单放弃人数中,N小时内注册人数
            totalDataVO.setLtNhRegisterCount(CalculateUtil.add(
                    detailVO.getLtNhRegisterCount(), totalDataVO.getLtNhRegisterCount()));
            //下单放弃人数中,已注册未下单人数
            totalDataVO.setRegisterAndNotOrder(CalculateUtil.add(
                    detailVO.getRegisterAndNotOrder(), totalDataVO.getRegisterAndNotOrder()));
            //下单放弃人数中,服务中客户数
            totalDataVO.setInServiceCount(CalculateUtil.add(
                    detailVO.getInServiceCount(), totalDataVO.getInServiceCount()));
            //下单放弃人数中,服务结束客户数
            totalDataVO.setOutServiceCount(CalculateUtil.add(
                    detailVO.getOutServiceCount(), totalDataVO.getOutServiceCount()));
            //下单放弃人数中,N天内被拒人数
            totalDataVO.setLtNdRefusedCount(CalculateUtil.add(
                    detailVO.getLtNdRefusedCount(), totalDataVO.getLtNdRefusedCount()));
        });
        //计算均值
        if (divisor != null && divisor != 0 && divisor != 1) {
            //下单意愿强烈人数
            totalDataVO.setIntentionCount(CalculateUtil.div(totalDataVO.getIntentionCount(), divisor, 2));
            //下单成功人数
            totalDataVO.setOrderCount(CalculateUtil.div(totalDataVO.getOrderCount(), divisor, 2));
            //取消下单人数
            totalDataVO.setCancelCount(CalculateUtil.div(totalDataVO.getCancelCount(), divisor, 2));
            //下单人数中，风控通过未付/风控N天之前被拒人数
            totalDataVO.setOrderRiskApprovedNotPay(CalculateUtil.div(totalDataVO.getOrderRiskApprovedNotPay(), divisor, 2));
            //下单放弃人数中,N小时内注册人数
            totalDataVO.setLtNhRegisterCount(CalculateUtil.div(totalDataVO.getLtNhRegisterCount(), divisor, 2));
            //下单放弃人数中,已注册未下单人数
            totalDataVO.setRegisterAndNotOrder(CalculateUtil.div(totalDataVO.getRegisterAndNotOrder(), divisor, 2));
            //下单放弃人数中,服务中客户数
            totalDataVO.setInServiceCount(CalculateUtil.div(totalDataVO.getInServiceCount(), divisor, 2));
            //下单放弃人数中,服务结束客户数
            totalDataVO.setOutServiceCount(CalculateUtil.div(totalDataVO.getOutServiceCount(), divisor, 2));
            //下单放弃人数中,N天内被拒人数
            totalDataVO.setLtNdRefusedCount(CalculateUtil.div(totalDataVO.getLtNdRefusedCount(), divisor, 2));
        }
        //计算百分比
        //下单人数中，风控通过未付/风控N天之前被拒人数
        totalDataVO.setOrderRiskApprovedNotPayRate(CalculateUtil.div(
                totalDataVO.getOrderRiskApprovedNotPay(), CalculateUtil.div(totalDataVO.getOrderCount(), 100), 2));
        //下单放弃人数中,N小时内注册人数
        totalDataVO.setLtNhRegisterCountRate(CalculateUtil.div(
                totalDataVO.getLtNhRegisterCount(), CalculateUtil.div(totalDataVO.getCancelCount(), 100), 2));
        //下单放弃人数中,已注册未下单人数
        totalDataVO.setRegisterAndNotOrderRate(CalculateUtil.div(
                totalDataVO.getRegisterAndNotOrder(), CalculateUtil.div(totalDataVO.getCancelCount(), 100), 2));
        //下单放弃人数中,服务中客户数
        totalDataVO.setInServiceCountRate(CalculateUtil.div(
                totalDataVO.getInServiceCount(), CalculateUtil.div(totalDataVO.getCancelCount(), 100), 2));
        //下单放弃人数中,服务结束客户数
        totalDataVO.setOutServiceCountRate(CalculateUtil.div(
                totalDataVO.getOutServiceCount(), CalculateUtil.div(totalDataVO.getCancelCount(), 100), 2));
        //下单放弃人数中,N天内被拒人数
        totalDataVO.setLtNdRefusedCountRate(CalculateUtil.div(
                totalDataVO.getLtNdRefusedCount(), CalculateUtil.div(totalDataVO.getCancelCount(), 100), 2));
    }

    private void handleRingCompare(List<CustomerCompositionVO> resultVO, List<Integer> miniTypeStrToList) {

        CustomerCompositionVO lastVO = lastFixedList(miniTypeStrToList);
        Map<Integer, CustomerCompositionVO.DetailVO> lastDetailMap =
                lastVO.getDetailList().stream().
                        collect(Collectors.toMap(CustomerCompositionVO.DetailVO::getMiniType, detailVO -> detailVO));
        for (CustomerCompositionVO beforeSaleVO : resultVO) {
            CustomerCompositionVO.DetailVO totalData = beforeSaleVO.getTotalData();
            CustomerCompositionVO.DetailVO lastTotal = lastVO.getTotalData();
            calculateRingCompare(totalData, lastTotal);
            for (CustomerCompositionVO.DetailVO detailVO : beforeSaleVO.getDetailList()) {
                CustomerCompositionVO.DetailVO lastDetailVO =
                        Optional.ofNullable(lastDetailMap.get(detailVO.getMiniType()))
                                .orElse(new CustomerCompositionVO.DetailVO());
                calculateRingCompare(detailVO, lastDetailVO);
            }
        }

    }

    private void calculateRingCompare(CustomerCompositionVO.DetailVO detailVO,
                                      CustomerCompositionVO.DetailVO lastDetailVO) {
        //下单意愿强烈人数环比
        detailVO.setIntentionCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getIntentionCount(), lastDetailVO.getIntentionCount()),
                CalculateUtil.div(lastDetailVO.getIntentionCount(), 100), 2));
        //取消下单人数环比
        detailVO.setCancelCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getCancelCount(), lastDetailVO.getCancelCount()),
                CalculateUtil.div(lastDetailVO.getCancelCount(), 100), 2));
        //下单成功人数
        detailVO.setOrderCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getOrderCount(), lastDetailVO.getOrderCount()),
                CalculateUtil.div(lastDetailVO.getOrderCount(), 100), 2));
        //下单人数中，风控通过未付/风控N天之前被拒人数
        detailVO.setOrderRiskApprovedNotPayRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getOrderRiskApprovedNotPay(), lastDetailVO.getOrderRiskApprovedNotPay()),
                CalculateUtil.div(lastDetailVO.getOrderRiskApprovedNotPay(), 100), 2));
        //下单放弃人数中,N小时内注册人数
        detailVO.setLtNhRegisterCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getLtNhRegisterCount(), lastDetailVO.getLtNhRegisterCount()),
                CalculateUtil.div(lastDetailVO.getLtNhRegisterCount(), 100), 2));
        //下单放弃人数中,已注册未下单人数
        detailVO.setRegisterAndNotOrderRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getRegisterAndNotOrder(), lastDetailVO.getRegisterAndNotOrder()),
                CalculateUtil.div(lastDetailVO.getRegisterAndNotOrder(), 100), 2));
        //下单放弃人数中,服务中客户数
        detailVO.setInServiceCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getInServiceCount(), lastDetailVO.getInServiceCount()),
                CalculateUtil.div(lastDetailVO.getInServiceCount(), 100), 2));
        //下单放弃人数中,服务结束客户数
        detailVO.setOutServiceCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getOutServiceCount(), lastDetailVO.getOutServiceCount()),
                CalculateUtil.div(lastDetailVO.getOutServiceCount(), 100), 2));
        //下单放弃人数中,N天内被拒人数
        detailVO.setLtNdRefusedCountRingCompare(CalculateUtil.div(
                CalculateUtil.sub(detailVO.getLtNdRefusedCount(), lastDetailVO.getLtNdRefusedCount()),
                CalculateUtil.div(lastDetailVO.getLtNdRefusedCount(), 100), 2));


    }


    /**
     * 前三十天数据
     */
    public CustomerCompositionVO lastFixedList(List<Integer> miniTypeStrToList) {
        Map<String, String> timeMap = IndicatorsUtil.getLastDefaultStartAndEndTime();
        String sTime = timeMap.get("sTime");
        String eTime = timeMap.get("eTime");
        List<IndicatorOrderCustomerDO> indicatorOrderCustomerList =
                indicatorOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorLossOrderCustomerDO> indicatorLossOrderCustomerList =
                indicatorLossOrderCustomerService.getList(sTime, eTime, miniTypeStrToList);
        List<IndicatorBeforeSaleDO> indicatorBeforeSaleList =
                indicatorBeforeSaleService.getList(sTime, eTime, miniTypeStrToList);
        //处理组合数据
        Map<String, List<CustomerCompositionVO.DetailVO>> dailyOrMonthlyMap = getDailyOrMonthlyMap(1,
                indicatorBeforeSaleList, indicatorOrderCustomerList, indicatorLossOrderCustomerList);

        List<CustomerCompositionVO.DetailVO> monthlyDetailList = new ArrayList<>();
        dailyOrMonthlyMap.forEach((key, value) -> monthlyDetailList.addAll(value));
        return getCustomerCompositionVO(monthlyDetailList, 30);
    }

}
