package qnvip.data.overview.job.overdue;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.qnvip.base.vo.resp.JobTaskResp;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.risk.RiskDistributionCountDO;
import qnvip.data.overview.domain.risk.RiskDistributionOrderOverdueDO;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.domain.risk.StatisticalDimension;
import qnvip.data.overview.dto.RiskOverdueDTO;
import qnvip.data.overview.enums.JobTaskEnum;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.enums.risk.BondSectionEnum;
import qnvip.data.overview.business.rpc.JobApiService;
import qnvip.data.overview.service.risk.DataviewRiskStatisticalDimensionService;
import qnvip.data.overview.service.risk.RiskDistributionCountService;
import qnvip.data.overview.service.risk.RiskDistributionOrderOverdueService;
import qnvip.data.overview.util.DateSplitUtils;
import qnvip.data.overview.util.QiYeWeiXinUtil;
import qnvip.rent.common.util.CopierUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/7/28
 * @since 1.0.0
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class DistributionJob {

    private final JobApiService jobApiService;
    private final RiskDistributionOrderOverdueService distributionOrderOverdueService;
    private final RiskDistributionCountService distributionCountService;
    private final DataviewRiskStatisticalDimensionService dataviewRiskStatisticalDimensionService;

    @XxlJob("distributionJobTidb")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> countJob(String s) {
//        String s="3";
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        List<JobTaskResp> jobTaskResps =
                jobApiService.queryTaskByCode(JobTaskEnum.DATAVIEW_RISK_DISTRIBUTION_ORDER_OVERDUE.getValue());
        if (CollectionUtils.isEmpty(jobTaskResps)) {
            QiYeWeiXinUtil.sendPostByMap("大盘-商户汇总表加工失败-商户明细表加工未完成");
            return ReturnT.FAIL;
        }
        if (CollectionUtils.isNotEmpty(jobTaskResps)) {
            //以完成时间排序，取最新的一条
            JobTaskResp jobTaskResp =
                    jobTaskResps.stream().max(Comparator.comparing(JobTaskResp::getTaskSuccessTime)).get();
            LocalDateTime completeTime = jobTaskResp.getTaskSuccessTime();
            //如何完成时间不等于今天的时间,则告警跳出，不执行后续的任务
            LocalDate now = LocalDate.now();
            if (!completeTime.toLocalDate().equals(now)) {
                QiYeWeiXinUtil.sendPostByMap("大盘-商户汇总表加工失败-商户明细表加工未完成");
                return ReturnT.FAIL;
            }
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("商户长租汇总表dataview_risk_distribution_count1-开始");
        run(overdueDays);
        log.info("商户长租汇总表dataview_risk_distribution_count1-结束");
        return ReturnT.SUCCESS;
    }

    private void test() {
        String s = "3";
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        run(overdueDays);
    }

    public void run(int[] overdueDays) {
        LocalDate startDate = LocalDate.of(2020, 10, 1);
        LocalDate endTime = LocalDate.now();
        log.info("生产环境-计算总日期跨度: {} - {}", startDate, endTime);
        List<DateSplitUtils.DateSplit> dateSplits = DateSplitUtils.splitByMonth(startDate, endTime, 1);
        List<RiskDistributionCountDO> totalList = Lists.newArrayList();
        log.info("清理原有数据...");
        distributionCountService.deleteAll();
        for (int overdueDay : overdueDays) {
            for (DateSplitUtils.DateSplit dateSplit : dateSplits) {
                log.info("生产环境-计算日期跨度: {} - {}", dateSplit.getStartDateTimeStr(), dateSplit.getEndDateTimeStr());
                String startDateTimeStr = dateSplit.getStartDateTimeStr();
                String endDateTimeStr = dateSplit.getEndDateTimeStr();
                List<RiskOverdueDTO> list = distributionOrderOverdueService.
                        getList(startDateTimeStr, endDateTimeStr, overdueDay);
                totalList.addAll(CopierUtil.copyList(list, RiskDistributionCountDO.class));
                log.info("生产环境-保存 {} 条数据", totalList.size());
                distributionCountService.saveBatch(totalList);
                totalList.clear();
                list.clear();
            }
        }

        log.info("商户长租-开始生成下拉框选项统计维度表");
//        riskGeneralOrderOverdueService.saveAllSelectData();
        insertGeneralSelector();
    }

//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public void insertGeneralSelector(){
        Map<Integer, Set<String>> map = Maps.newHashMap();
        HashSet<String> auditTypeSet = Sets.newHashSet();
        HashSet<String> riskLevelSet = Sets.newHashSet();
        HashSet<String> riskStrategySet = Sets.newHashSet();
        HashSet<String> financeTypeSet = Sets.newHashSet();
//        HashSet<String> rentSet = Sets.newHashSet();
        HashSet<String> mortgageSet = Sets.newHashSet();
        HashSet<String> shopNameSet = Sets.newHashSet();
        HashSet<String> sceneSet = Sets.newHashSet();
        HashSet<String> quotientSet = Sets.newHashSet();
        HashSet<String> zmfLevelSet = Sets.newHashSet();

        HashSet<String> depositFreeTypeSet = Sets.newHashSet();
        HashSet<String> trafficTypeSet = Sets.newHashSet();
        HashSet<String> financialSolutionsSet = Sets.newHashSet();
        HashSet<String> applicationNameSet = Sets.newHashSet();
        HashSet<String> equipmentStateSet = Sets.newHashSet();
        HashSet<String> supervisedMachineSet = Sets.newHashSet();
        HashSet<String> machineTypeSet = Sets.newHashSet();

        List<Integer> miniTypeList = null;

        String startTime=null;
        String endTime=null;
        Integer overdueDay=3;

        //先清空
        dataviewRiskStatisticalDimensionService.deleteByBizType(2);


        List<RiskDistributionOrderOverdueDO> auditType =
                distributionOrderOverdueService.getAuditTypeNew(startTime, endTime, miniTypeList, overdueDay);
        for (RiskDistributionOrderOverdueDO riskOrderOverdueDO : auditType) {

            auditTypeSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getAuditType()) ? "自动审核" : "人工审核");
            riskLevelSet.add(riskOrderOverdueDO.getRiskLevel());
            riskStrategySet.add(riskOrderOverdueDO.getRiskStrategy());
            financeTypeSet.add(FinanceTypeEnum.getTitleByType(riskOrderOverdueDO.getFinanceType()));
           // rentSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getIsRent()) ? "是" : "否");
            mortgageSet.add(Integer.valueOf(1).equals(riskOrderOverdueDO.getIsMortgage()) ? "是" : "否");
            shopNameSet.add(riskOrderOverdueDO.getShopName());
            sceneSet.add(riskOrderOverdueDO.getScene());
            quotientSet.add(riskOrderOverdueDO.getQuotientName());
            zmfLevelSet.add(riskOrderOverdueDO.getZmfLevel());

            depositFreeTypeSet.add(riskOrderOverdueDO.getDepositFreeType());
            trafficTypeSet.add(riskOrderOverdueDO.getTrafficType());
            financialSolutionsSet.add(riskOrderOverdueDO.getFinancialSolutions());
            applicationNameSet.add(riskOrderOverdueDO.getApplicationName());
            equipmentStateSet.add(riskOrderOverdueDO.getEquipmentState());
            supervisedMachineSet.add(riskOrderOverdueDO.getSupervisedMachine());
            machineTypeSet.add(riskOrderOverdueDO.getMachineType());
        }

        Set<String> riskLevelFinalSet =
                riskLevelSet.stream().filter(o -> StringUtils.isNotBlank(o) && !o.contains("建议"))
                        .collect(Collectors.toCollection(Sets::newHashSet));

        Set<String> riskStrategyFinalSet =
                riskStrategySet.stream().filter(StringUtils::isNotBlank)
                        .collect(Collectors.toCollection(Sets::newHashSet));

        List<StatisticalDimension> statisticalDimensionList=new ArrayList<StatisticalDimension>();

        Iterator<String> auditTypeIterator = auditTypeSet.iterator();
        while (auditTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = auditTypeIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("审核方式");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> riskLevelIterator = riskLevelFinalSet.iterator();
        while (riskLevelIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = riskLevelIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("风控等级");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> riskStrategyIterator = riskStrategyFinalSet.iterator();
        while (riskStrategyIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = riskStrategyIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("风控策略");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }


        Iterator<String> financeTypeIterator = financeTypeSet.iterator();
        while (financeTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = financeTypeIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("方案");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        //是否免押
        Iterator<String> mortgageIterator = mortgageSet.iterator();
        while (mortgageIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = mortgageIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("是否免押");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        //商户名称 shopNameSet
        Iterator<String> shopNameIterator = shopNameSet.iterator();
        while (shopNameIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = shopNameIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("商户名称");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> quotientIterator = quotientSet.iterator();
        while (quotientIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = quotientIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("场景值");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> sceneIterator = sceneSet.iterator();
        while (sceneIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = sceneIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("导流商");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        List<String> collects = zmfLevelSet.stream().
                sorted(Comparator.comparing(BondSectionEnum::getZmfValueByCode))
                .collect(Collectors.toList());

        for(String collect: collects){
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            statisticalDimension.setFirstDimension("芝麻分等级");
            statisticalDimension.setBizType(2);
            statisticalDimension.setSecondaryDimension(collect);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> depositFreeTypeIterator = depositFreeTypeSet.iterator();
        while (depositFreeTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = depositFreeTypeIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("免押类型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> trafficTypeIterator = trafficTypeSet.iterator();
        while (trafficTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = trafficTypeIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("流量类型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> financialSolutionsIterator = financialSolutionsSet.iterator();
        while (financialSolutionsIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = financialSolutionsIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("金融方案");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> applicationNameIterator = applicationNameSet.iterator();
        while (applicationNameIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = applicationNameIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("应用");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> equipmentStateIterator = equipmentStateSet.iterator();
        while (equipmentStateIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = equipmentStateIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("新旧程度");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> supervisedMachineIterator = supervisedMachineSet.iterator();
        while (supervisedMachineIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = supervisedMachineIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("是否监管");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> machineTypeIterator = machineTypeSet.iterator();
        while (machineTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = machineTypeIterator.next();
            statisticalDimension.setBizType(2);
            statisticalDimension.setFirstDimension("机型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        //维度表插入数据
        boolean flag = dataviewRiskStatisticalDimensionService.saveOrUpdateBatch(statisticalDimensionList);
        if(flag){
            log.info("商户-统计维度表插入成功!!!");
        }
        else{
            log.info("商户-统计维度表插入失败!!!");
        }


    }

}
