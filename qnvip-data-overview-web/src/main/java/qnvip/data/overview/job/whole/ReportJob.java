package qnvip.data.overview.job.whole;

import cn.hutool.json.JSONUtil;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.stereotype.Component;
import qnvip.data.overview.business.whole.report.WholeLawReportBusiness;
import qnvip.data.overview.business.whole.report.WholeMerchantReportBusiness;
import qnvip.data.overview.business.whole.report.WholeRentReportBusiness;
import qnvip.data.overview.domain.whole.report.WholeLawReportDO;
import qnvip.data.overview.domain.whole.report.WholeMerchantReportDO;
import qnvip.data.overview.domain.whole.report.WholeRentReportDO;
import qnvip.data.overview.domain.whole.report.WholeReportCountDO;
import qnvip.data.overview.enums.RedisKeyEnum;
import qnvip.data.overview.enums.whole.WholeLawReportRowEnum;
import qnvip.data.overview.enums.whole.WholeMerchantReportRowEnum;
import qnvip.data.overview.enums.whole.WholeRentReportRowEnum;
import qnvip.data.overview.enums.whole.WholeRentReportTitleEnum;
import qnvip.data.overview.service.whole.report.WholeLawReportService;
import qnvip.data.overview.service.whole.report.WholeMerchantReportService;
import qnvip.data.overview.service.whole.report.WholeRentReportCountService;
import qnvip.data.overview.service.whole.report.WholeRentReportService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.ObjectUtils;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.temporal.TemporalAdjusters;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2023/2/13
 * @since 1.0.0
 */
@Component
@Slf4j
@EnableScheduling
@RequiredArgsConstructor
public class ReportJob {

    private final WholeRentReportService wholeRentReportService;
    private final WholeMerchantReportService wholeMerchantReportService;
    private final WholeRentReportCountService wholeRentReportCountService;
    private final WholeLawReportService wholeLawReportService;
    private final WholeRentReportBusiness wholeRentReportBusiness;
    private final WholeMerchantReportBusiness wholeMerchantReportBusiness;
    private final WholeLawReportBusiness wholeLawReportBusiness;
    private final RedisTemplate redisTemplate;
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("MM月dd日");
    public final static String REDIS_KEY_WEEK = RedisKeyEnum.WEEK_REPORT.getKey();
    public final static String REDIS_KEY_MONTH = RedisKeyEnum.MONTH_REPORT.getKey();
    // 需要特殊处理,求平均金额,不需要乘以100
    private final String[] CONDITION1 = {"avg_finance_amt", "avg_phone14_purchase_price",
            "avg_purchase_price"};
    // 需要特殊处理,需要保留小数点后两位,比如金额
    private final String[] CONDITION2 = {"finance_amt", "purchase_price", "un_overdue_real_pay_amt", "repay_amt",
            "renew_repay_amt", "renew_un_overdue_real_pay_amt", "overdue_repay_amt", "overdue_total_amt",
            "this_day30_should_amt", "month2_return_amt", "harmful_back_amt", "harmful_add_amt", "harmful_total_amt"
    };
    // 需要特殊处理,反射取值时不需要取的字段
    private final String[] CONDITION3 = {"countDay", "serialVersionUID", "ds"};
    // 需要特殊处理,像毛利率一样特殊的公式
    private final String[] CONDITION4 = {"gross_profit_rate"};

    private final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     *
     */
    @XxlJob("execReportData")
    public ReturnT<String> execData(String ds) {
        if (StringUtils.isBlank(ds)) {
            ds = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        }
        wholeRentReportBusiness.execData(ds);
        wholeLawReportBusiness.execData(ds);
        return ReturnT.SUCCESS;
    }

    /**
     *
     */
    @XxlJob("weekReport")
    public ReturnT<String> weekReport(String time) {
        runRentByWeek(time);
        return ReturnT.SUCCESS;
    }

    /**
     *
     */
    @XxlJob("monthReport")
    public ReturnT<String> monthReport(String time) {
        runByMonth(time);
        return ReturnT.SUCCESS;
    }

    // @PostConstruct
    public void getNum() {
        runByMonth("2023-06-01");
    }

    /**
     * 周报,需要周几跑,就调整cron表达式就行,cron表达式默认每周五跑
     */
    private void runRentByWeek(String time) {
        // 本周
        LocalDate startDate = LocalDate.now();
        if (StringUtils.isNotBlank(time)) {
            startDate = LocalDate.parse(time, FORMATTER);
        }
        LocalDate thisWeekE = startDate.minusDays(1);
        LocalDate thisWeekS = thisWeekE.minusDays(6);
        String thisWeekDS = thisWeekE.plusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 上周
        LocalDate lastWeekE = thisWeekS.minusDays(1);
        LocalDate lastWeekS = lastWeekE.minusDays(6);
        String lastWeekDS = thisWeekS.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 上上周
        LocalDate beforeLastWeekE = lastWeekS.minusDays(1);
        LocalDate beforeLastWeekS = beforeLastWeekE.minusDays(6);
        String beforeLastWeekDS = lastWeekS.format(DateTimeFormatter.ofPattern("yyyyMMdd"));


        Map<String, String> key2value = Maps.newHashMap();
        key2value.put("week3", getWeekTitle(thisWeekE, thisWeekS));
        key2value.put("week2", getWeekTitle(lastWeekE, lastWeekS));
        key2value.put("week1", getWeekTitle(beforeLastWeekE, beforeLastWeekS));

        redisTemplate.opsForValue().set(REDIS_KEY_WEEK, JSONUtil.toJsonStr(key2value));


        LocalDate finalStartDate = startDate;
        CompletableFuture.runAsync(() -> {
            List<WholeRentReportDO> thisWeekList = wholeRentReportService.getList(thisWeekS, thisWeekE, thisWeekDS);
            List<WholeRentReportDO> lastWeekList = wholeRentReportService.getList(lastWeekS, lastWeekE, lastWeekDS);
            List<WholeRentReportDO> beforeLastWeekList = wholeRentReportService.getList(beforeLastWeekS, beforeLastWeekE, beforeLastWeekDS);

            pretreatmentData(thisWeekList, lastWeekList, beforeLastWeekList,
                    WholeRentReportTitleEnum.WEEK.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.RENT.getCode(),
                    getWeekTitle(thisWeekE, thisWeekS),
                    WholeRentReportDO.class
            );
        });

        CompletableFuture.runAsync(() -> {
            String merchantReportThisWeekDS = thisWeekE.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

            List<WholeMerchantReportDO> thisWeekList = wholeMerchantReportService.getList(thisWeekS, thisWeekE, merchantReportThisWeekDS);
            List<WholeMerchantReportDO> lastWeekList = wholeMerchantReportService.getList(lastWeekS, lastWeekE, lastWeekDS);
            List<WholeMerchantReportDO> beforeLastWeekList = wholeMerchantReportService.getList(beforeLastWeekS, beforeLastWeekE, beforeLastWeekDS);

            pretreatmentData(thisWeekList, lastWeekList, beforeLastWeekList,
                    WholeRentReportTitleEnum.WEEK.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.MERCHANT.getCode(),
                    getWeekTitle(thisWeekE, thisWeekS),
                    WholeMerchantReportDO.class
            );
        });

        CompletableFuture.runAsync(() -> {
            List<WholeLawReportDO> thisWeekList = wholeLawReportService.getList(thisWeekS, thisWeekE, thisWeekDS, WholeRentReportTitleEnum.WEEK.getCode());
            List<WholeLawReportDO> lastWeekList = wholeLawReportService.getList(lastWeekS, lastWeekE, lastWeekDS, WholeRentReportTitleEnum.WEEK.getCode());
            List<WholeLawReportDO> beforeLastWeekList = wholeLawReportService.getList(beforeLastWeekS,
                    beforeLastWeekE, beforeLastWeekDS, WholeRentReportTitleEnum.WEEK.getCode());

            pretreatmentData(thisWeekList, lastWeekList, beforeLastWeekList,
                    WholeRentReportTitleEnum.WEEK.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.LAWSUIT.getCode(),
                    getWeekTitle(thisWeekE, thisWeekS),
                    WholeLawReportDO.class
            );
        });
    }

    // @Scheduled(fixedDelay = 1000000000)
    private void test() {
        runRentByWeek("2023-02-24");
    }

    /**
     * 月报,param调整统计范围,每个月1号跑
     */
    public void runByMonth(String time) {
        // 本周
        LocalDate startDate = LocalDate.now();
        if (StringUtils.isNotBlank(time)) {
            startDate = LocalDate.parse(time, FORMATTER);
        }
        LocalDate localDate = startDate.minusDays(1);
        // 本月第一天
        LocalDate thisMonthStart = LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 1);
        //本月的最后一天
        LocalDate thisMonthEnd = localDate.with(TemporalAdjusters.lastDayOfMonth());
        String thisMonthDs = thisMonthEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 上月第一天
        LocalDate lastMonthStart = LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 1).minusMonths(1);
        //上月的最后一天
        LocalDate lastMonthEnd = lastMonthStart.with(TemporalAdjusters.lastDayOfMonth());
        String lastMonthDS = thisMonthStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        // 上月第一天
        LocalDate beforeLastMonthStart =
                LocalDate.of(localDate.getYear(), localDate.getMonthValue(), 1).minusMonths(2);
        //上月的最后一天
        LocalDate beforeLastMonthEnd = beforeLastMonthStart.with(TemporalAdjusters.lastDayOfMonth());
        String beforeLastMonthDS = lastMonthStart.format(DateTimeFormatter.ofPattern("yyyyMMdd"));


        LocalDate finalStartDate = startDate;
        CompletableFuture.runAsync(() -> {
            List<WholeRentReportDO> thisMonthList = wholeRentReportService.getList(thisMonthStart, thisMonthEnd, thisMonthDs);
            List<WholeRentReportDO> lastMonthList = wholeRentReportService.getList(lastMonthStart, lastMonthEnd, lastMonthDS);
            List<WholeRentReportDO> beforeLastMonthList = wholeRentReportService.getList(beforeLastMonthStart, beforeLastMonthEnd, beforeLastMonthDS);

            pretreatmentData(thisMonthList, lastMonthList, beforeLastMonthList,
                    WholeRentReportTitleEnum.MONTH.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.RENT.getCode(),
                    getWeekTitle(thisMonthEnd, thisMonthStart),
                    WholeRentReportDO.class
            );
        });

        CompletableFuture.runAsync(() -> {
            String merchantReportThisMonthDS = thisMonthEnd.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            List<WholeMerchantReportDO> thisWeekList = wholeMerchantReportService.getList(thisMonthStart, thisMonthEnd, merchantReportThisMonthDS);
            List<WholeMerchantReportDO> lastWeekList = wholeMerchantReportService.getList(lastMonthStart, lastMonthEnd, lastMonthDS);
            List<WholeMerchantReportDO> beforeLastWeekList = wholeMerchantReportService.getList(beforeLastMonthStart, beforeLastMonthEnd, beforeLastMonthDS);

            pretreatmentData(thisWeekList, lastWeekList, beforeLastWeekList,
                    WholeRentReportTitleEnum.MONTH.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.MERCHANT.getCode(),
                    getWeekTitle(thisMonthEnd, thisMonthStart),
                    WholeMerchantReportDO.class
            );
        });

        CompletableFuture.runAsync(() -> {
            List<WholeLawReportDO> thisWeekList = wholeLawReportService.getList(thisMonthStart, thisMonthEnd,
                    thisMonthDs, WholeRentReportTitleEnum.MONTH.getCode());
            List<WholeLawReportDO> lastWeekList = wholeLawReportService.getList(lastMonthStart, lastMonthEnd,
                    lastMonthDS, WholeRentReportTitleEnum.MONTH.getCode());
            List<WholeLawReportDO> beforeLastWeekList = wholeLawReportService.getList(beforeLastMonthStart,
                    beforeLastMonthEnd, beforeLastMonthDS, WholeRentReportTitleEnum.MONTH.getCode());

            pretreatmentData(thisWeekList, lastWeekList, beforeLastWeekList,
                    WholeRentReportTitleEnum.MONTH.getCode(),
                    finalStartDate,
                    WholeRentReportTitleEnum.LAWSUIT.getCode(),
                    getWeekTitle(thisMonthEnd, thisMonthStart),
                    WholeLawReportDO.class
            );
        });
        Map<String, String> key2value = Maps.newHashMap();
        key2value.put("week3", getWeekTitle(thisMonthEnd, thisMonthStart));
        key2value.put("week2", getWeekTitle(lastMonthEnd, lastMonthStart));
        key2value.put("week1", getWeekTitle(beforeLastMonthEnd, beforeLastMonthStart));

        redisTemplate.opsForValue().set(REDIS_KEY_MONTH, JSONUtil.toJsonStr(key2value));
    }


    /**
     * 预处理,获取本周\上周\上上周的数据
     *
     * @param thisWeekList       本周列表
     * @param lastWeekList       上周列表
     * @param beforeLastWeekList 在上周之前列表
     * @return {@code LinkedList<WholeReportCountDO>}
     */
    private <T> void pretreatmentData(List<T> thisWeekList,
                                      List<T> lastWeekList,
                                      List<T> beforeLastWeekList,
                                      Integer type,
                                      LocalDate day,
                                      Integer model,
                                      String countDay,
                                      Class<T> clazz) {
        WholeReportCountDO wholeRentReportCountDO;
        LinkedList<WholeReportCountDO> list = Lists.newLinkedList();
        for (Field field : clazz.getDeclaredFields()) {
            if (StringUtils.equalsAny(field.getName(), CONDITION3)) {
                continue;
            }
            Function<T, ?> mapper = t -> {
                try {
                    Field f = t.getClass().getDeclaredField(field.getName());
                    f.setAccessible(true);
                    return f.get(t);
                } catch (IllegalAccessException | NoSuchFieldException e) {
                    log.error(e.getMessage(), e);
                }
                return BigDecimal.ZERO;
            };

            wholeRentReportCountDO = new WholeReportCountDO();
            BigDecimal thisWeek = ObjectUtils.getSUM(thisWeekList, mapper);
            BigDecimal lastWeek = ObjectUtils.getSUM(lastWeekList, mapper);
            BigDecimal beforeLastWeek = ObjectUtils.getSUM(beforeLastWeekList, mapper);
            String code = ObjectUtils.humpToUnderline(field.getName()).toLowerCase();
            wholeRentReportCountDO.setCode(code);
            wholeRentReportCountDO.setModel(model);
            wholeRentReportCountDO.setBeforeLastWeek(beforeLastWeek.setScale(0, RoundingMode.DOWN));
            wholeRentReportCountDO.setLastWeek(lastWeek.setScale(0, RoundingMode.DOWN));
            wholeRentReportCountDO.setThisWeek(thisWeek.setScale(0, RoundingMode.DOWN));
            if (StringUtils.equalsAnyIgnoreCase(code, CONDITION2)) {
                wholeRentReportCountDO.setBeforeLastWeek(beforeLastWeek);
                wholeRentReportCountDO.setLastWeek(lastWeek);
                wholeRentReportCountDO.setThisWeek(thisWeek);
            }
            wholeRentReportCountDO.setRate(getPercentage(thisWeek, lastWeek));
            wholeRentReportCountDO.setCountType(type);
            list.add(wholeRentReportCountDO);
        }
        Map<String, WholeReportCountDO> code2CountDO = list
                .stream().collect(Collectors.toMap(WholeReportCountDO::getCode, Function.identity()));
        // 预处理处理支付率/2天发货率/7天买断率等
        // 每加一个报表,就需要在这多做个判断
        // relations.length == 3 ? relations[2] : "" 个别指标需要多个指标进行计算,所以会传三个参数
        Map<String, ?> name2EnumCache = new HashMap<>();
        if (Objects.equals(model, WholeRentReportTitleEnum.RENT.getCode())) {
            for (WholeRentReportRowEnum _enum : WholeRentReportRowEnum.getRelations()) {
                String[] relations = _enum.getRelation();
                replenishMap(code2CountDO, type, model, _enum.name(), relations[0], relations[1],
                        relations.length == 3 ? relations[2] : "");
            }
            name2EnumCache = WholeRentReportRowEnum.name2EnumCache;
        } else if (Objects.equals(model, WholeRentReportTitleEnum.MERCHANT.getCode())) {
            for (WholeMerchantReportRowEnum _enum : WholeMerchantReportRowEnum.getRelations()) {
                String[] relations = _enum.getRelation();
                replenishMap(code2CountDO, type, model, _enum.name(), relations[0], relations[1],
                        relations.length == 3 ? relations[2] : "");
            }
            name2EnumCache = WholeMerchantReportRowEnum.name2EnumCache;
        } else if (Objects.equals(model, WholeRentReportTitleEnum.LAWSUIT.getCode())) {
            for (WholeLawReportRowEnum _enum : WholeLawReportRowEnum.getRelations()) {
                String[] relations = _enum.getRelation();
                replenishMap(code2CountDO, type, model, _enum.name(), relations[0], relations[1],
                        relations.length == 3 ? relations[2] : "");
            }
            name2EnumCache = WholeLawReportRowEnum.name2EnumCache;
        }

        wholeRentReportCountService.removeMongoDO(day, type, model);
        wholeRentReportCountService.saveWeekReport(countDay,
                model,
                type,
                day,
                code2CountDO,
                name2EnumCache);
        wholeRentReportCountService.deleteByCondition(type, model);
        wholeRentReportCountService.saveBatch(code2CountDO.values());
    }

    /**
     * 补充指标
     *
     * @param map        地图
     * @param targetCode 目标代码
     */
    private void replenishMap(Map<String, WholeReportCountDO> map,
                              Integer countType,
                              Integer model,
                              String targetCode,
                              String... code) {
        WholeReportCountDO countDO1 = map.get(code[0].toLowerCase());
        WholeReportCountDO countDO2 = map.get(code[1].toLowerCase());


        Object code1ThisWeek = countDO1.getThisWeek();
        Object code1LastWeek = countDO1.getLastWeek();
        Object code1BeforeLastWeek = countDO1.getBeforeLastWeek();

        BigDecimal code2ThisWeek = countDO2.getThisWeek();
        BigDecimal code2LastWeek = countDO2.getLastWeek();
        BigDecimal code2BeforeLastWeek = countDO2.getBeforeLastWeek();

        WholeReportCountDO countDO = new WholeReportCountDO();
        BigDecimal beforeLastWeek = CalculateUtil.getPercent(code1BeforeLastWeek, code2BeforeLastWeek);
        BigDecimal thisWeek = CalculateUtil.getPercent(code1ThisWeek, code2ThisWeek);
        BigDecimal lastWeek = CalculateUtil.getPercent(code1LastWeek, code2LastWeek);

        if (StringUtils.isNotBlank(code[2])) {
            WholeReportCountDO countDO3 = map.get(code[2].toLowerCase());
            BigDecimal code3ThisWeek = countDO3.getThisWeek();
            BigDecimal code3LastWeek = countDO3.getLastWeek();
            BigDecimal code3BeforeLastWeek = countDO3.getBeforeLastWeek();
            beforeLastWeek = CalculateUtil.getPercent(code1BeforeLastWeek, code2BeforeLastWeek).subtract(code3BeforeLastWeek);
            thisWeek = CalculateUtil.getPercent(code1ThisWeek, code2ThisWeek).subtract(code3ThisWeek);
            lastWeek = CalculateUtil.getPercent(code1LastWeek, code2LastWeek).subtract(code3LastWeek);
        }

        if (StringUtils.equalsAnyIgnoreCase(targetCode.toLowerCase(), CONDITION4)) {
            thisWeek = CalculateUtil.mul(CalculateUtil.sub(1, CalculateUtil.div(code1ThisWeek, code2ThisWeek)), 100, 2);
            lastWeek = CalculateUtil.mul(CalculateUtil.sub(1, CalculateUtil.div(code1LastWeek, code2LastWeek)), 100, 2);
            beforeLastWeek = CalculateUtil.mul(CalculateUtil.sub(1, CalculateUtil.div(code1BeforeLastWeek, code2BeforeLastWeek)), 100, 2);
        } else if (StringUtils.equalsAnyIgnoreCase(targetCode.toLowerCase(), CONDITION1)) {
            thisWeek = CalculateUtil.getPercentByAvg(code1ThisWeek, code2ThisWeek);
            lastWeek = CalculateUtil.getPercentByAvg(code1LastWeek, code2LastWeek);
            beforeLastWeek = CalculateUtil.getPercentByAvg(code1BeforeLastWeek, code2BeforeLastWeek);
        }
        countDO.setCode(targetCode.toLowerCase());
        countDO.setBeforeLastWeek(beforeLastWeek);
        countDO.setModel(model);
        countDO.setLastWeek(lastWeek);
        countDO.setThisWeek(thisWeek);
        countDO.setCountType(countType);
        if (StringUtils.equalsAnyIgnoreCase(targetCode.toLowerCase(), CONDITION1)) {
            countDO.setRate(getPercentage(thisWeek, lastWeek));
        } else {
            countDO.setRate(getPercentagePoint(thisWeek, lastWeek));
        }
        map.put(targetCode.toLowerCase(), countDO);
    }

    /**
     * 得到百分比
     *
     * @param thisWeek 本周
     * @param lastWeek 上周
     * @return {@code String}
     */
    private String getPercentage(BigDecimal thisWeek, BigDecimal lastWeek) {
        BigDecimal mul = CalculateUtil.mul(CalculateUtil.sub(CalculateUtil.div(thisWeek, lastWeek), 1), 100, 2);
        return mul + "%";
    }

    /**
     * 获取百分点
     *
     * @param thisWeek 本周
     * @param lastWeek 上周
     * @return {@code String}
     */
    private String getPercentagePoint(BigDecimal thisWeek, BigDecimal lastWeek) {
        BigDecimal mul = CalculateUtil.sub(thisWeek, lastWeek, 2);
        return mul + "pt";
    }

    private String getWeekTitle(LocalDate thisWeekE, LocalDate thisWeekS) {
        return dateFormatter.format(thisWeekS) + "-" + dateFormatter.format(thisWeekE);
    }

}
