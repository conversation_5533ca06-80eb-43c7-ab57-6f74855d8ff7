package qnvip.data.overview.job.overdue;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.risk.RiskGeneralCountDO;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.domain.risk.StatisticalDimension;
import qnvip.data.overview.dto.RiskOverdueDTO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.enums.risk.BondSectionEnum;
import qnvip.data.overview.service.risk.DataviewRiskStatisticalDimensionService;
import qnvip.data.overview.service.risk.RiskGeneralCountService;
import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
import qnvip.data.overview.util.DateSplitUtils;
import qnvip.rent.common.util.CopierUtil;

import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/7/28
 * @since 1.0.0
 */
@Slf4j
@Component
@EnableScheduling
@RequiredArgsConstructor
public class GeneralJob {


    private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
    private final RiskGeneralCountService riskGeneralCountService;
    private final DataviewRiskStatisticalDimensionService dataviewRiskStatisticalDimensionService;

//    @XxlJob("generalCountJob")
    public ReturnT<String> countJob(String s) {
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        run(overdueDays);
        return ReturnT.SUCCESS;
    }

    @XxlJob("generalCountJobTidb")
//@Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> countJobNew(String s) {
        if (StringUtils.isBlank(s)) {
             s = "3";
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("租赁自营汇总表 dataview_risk_general_count1 开始");
        run(overdueDays);
        log.info("租赁自营汇总表 dataview_risk_general_count1 结束");
        return ReturnT.SUCCESS;
    }

    // @Scheduled(fixedDelay = 1000000000)
    private void test() {
        String s = "3";
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        run(overdueDays);
    }

//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public void run(int[] overdueDays) {
        LocalDate startDate = LocalDate.of(2020, 10, 1);
        LocalDate endTime = LocalDate.now();
        List<DateSplitUtils.DateSplit> dateSplits = DateSplitUtils.splitByMonth(startDate, endTime, 1);
        List<RiskGeneralCountDO> totalList = Lists.newArrayList();
        riskGeneralCountService.removeAll();
        log.info("清理原有数据...");
        for (int overdueDay : overdueDays) {
            log.info("逾期天数 {}", overdueDay);
            for (DateSplitUtils.DateSplit dateSplit : dateSplits) {
                log.info("计算日期跨度: {} - {}", dateSplit.getStartDateTimeStr(), dateSplit.getEndDateTimeStr());
                String startDateTimeStr = dateSplit.getStartDateTimeStr();
                String endDateTimeStr = dateSplit.getEndDateTimeStr();
                List<RiskOverdueDTO> list = riskGeneralOrderOverdueService.
                        getList(startDateTimeStr, endDateTimeStr, overdueDay);
                totalList.addAll(CopierUtil.copyList(list, RiskGeneralCountDO.class));
                log.info("保存 {} 条数据", totalList.size());
                riskGeneralCountService.saveBatch(totalList);
                totalList.clear();
                list.clear();
            }
        }
        log.info("汇总数据统计完成");
        log.info("通用-开始生成下拉框选项统计维度表");
//        riskGeneralOrderOverdueService.saveAllSelectData();
        insertGeneralSelector();
    }

//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public void insertGeneralSelector(){
        Map<Integer, Collection<String>> map = Maps.newHashMap();
        HashSet<String> auditTypeSet = Sets.newHashSet();
        HashSet<String> riskLevelSet = Sets.newHashSet();
        HashSet<String> riskStrategySet = Sets.newHashSet();
        HashSet<String> financeTypeSet = Sets.newHashSet();
        HashSet<String> sceneSet = Sets.newHashSet();
        HashSet<String> quotientSet = Sets.newHashSet();
        HashSet<String> zmfSet = Sets.newHashSet();

        HashSet<String> depositFreeTypeSet = Sets.newHashSet();
        HashSet<String> trafficTypeSet = Sets.newHashSet();
        HashSet<String> financialSolutionsSet = Sets.newHashSet();
        HashSet<String> applicationNameSet = Sets.newHashSet();
        HashSet<String> equipmentStateSet = Sets.newHashSet();
        HashSet<String> supervisedMachineSet = Sets.newHashSet();
        HashSet<String> machineTypeSet = Sets.newHashSet();

//        HashSet<String> mianyaSet = Sets.newHashSet();
        List<Integer> miniTypeList = null;

        String startTime=null;
        String endTime=null;
        Integer overdueDay=3;

        //先清空
        dataviewRiskStatisticalDimensionService.deleteByBizType(1);

        List<RiskGeneralOrderOverdueDO> auditType =
                riskGeneralOrderOverdueService.getAuditTypeNew(startTime, endTime, miniTypeList, overdueDay);

        List<StatisticalDimension> statisticalDimensionList=new ArrayList<StatisticalDimension>();

        for (RiskGeneralOrderOverdueDO riskOrderOverdueDO : auditType) {

            auditTypeSet.add("1".equals(riskOrderOverdueDO.getAuditType()) ? "自动审核" : "人工审核");
//            riskLevelSet.add(riskOrderOverdueDO.getRiskLevel());
            riskStrategySet.add(riskOrderOverdueDO.getRiskStrategy());
            sceneSet.add(riskOrderOverdueDO.getScene());
            quotientSet.add(riskOrderOverdueDO.getQuotientName());
            zmfSet.add(riskOrderOverdueDO.getZmfLevel());

            depositFreeTypeSet.add(riskOrderOverdueDO.getDepositFreeType());
            trafficTypeSet.add(riskOrderOverdueDO.getTrafficType());
            financialSolutionsSet.add(riskOrderOverdueDO.getFinancialSolutions());
            applicationNameSet.add(riskOrderOverdueDO.getApplicationName());
            equipmentStateSet.add(riskOrderOverdueDO.getEquipmentState());
            supervisedMachineSet.add(riskOrderOverdueDO.getSupervisedMachine());
            machineTypeSet.add(riskOrderOverdueDO.getMachineType());
//            mianyaSet.add(riskOrderOverdueDO.getIsRent());
            financeTypeSet.add(FinanceTypeEnum.getTitleByType(Integer.parseInt(riskOrderOverdueDO.getFinanceType())));
        }


        Set<String> riskStrategyFinalSet =
                riskStrategySet.stream().filter(StringUtils::isNotBlank)
                        .collect(Collectors.toCollection(Sets::newHashSet));

        Iterator<String> riskStrategyIterator = riskStrategyFinalSet.iterator();
        while (riskStrategyIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String riskStrategy = riskStrategyIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("风控策略");
            statisticalDimension.setSecondaryDimension(riskStrategy);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }
        Iterator<String> auditTypeIterator = auditTypeSet.iterator();
        while (auditTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String auditType2 = auditTypeIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("审核方式");
            statisticalDimension.setSecondaryDimension(auditType2);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> financeTypeIterator = financeTypeSet.iterator();
        while (financeTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = financeTypeIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("方案");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String>sceneIterator = sceneSet.iterator();
        while (sceneIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = sceneIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("场景值");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> quotientIterator = quotientSet.iterator();
        while (quotientIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = quotientIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("导流商");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        List<String> collects = zmfSet.stream().
         sorted(Comparator.comparing(BondSectionEnum::getZmfValueByCode))
                .collect(Collectors.toList());

        for(String collect: collects){
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            statisticalDimension.setFirstDimension("芝麻分等级");
            statisticalDimension.setBizType(1);
            statisticalDimension.setSecondaryDimension(collect);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> depositFreeTypeIterator = depositFreeTypeSet.iterator();
        while (depositFreeTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = depositFreeTypeIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("免押类型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> trafficTypeIterator = trafficTypeSet.iterator();
        while (trafficTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = trafficTypeIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("流量类型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> financialSolutionsIterator = financialSolutionsSet.iterator();
        while (financialSolutionsIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = financialSolutionsIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("金融方案");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> applicationNameIterator = applicationNameSet.iterator();
        while (applicationNameIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = applicationNameIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("应用");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> equipmentStateIterator = equipmentStateSet.iterator();
        while (equipmentStateIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = equipmentStateIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("新旧程度");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> supervisedMachineIterator = supervisedMachineSet.iterator();
        while (supervisedMachineIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = supervisedMachineIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("是否监管");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        Iterator<String> machineTypeIterator = machineTypeSet.iterator();
        while (machineTypeIterator.hasNext()) {
            StatisticalDimension statisticalDimension=new StatisticalDimension();
            String value = machineTypeIterator.next();
            statisticalDimension.setBizType(1);
            statisticalDimension.setFirstDimension("机型");
            statisticalDimension.setSecondaryDimension(value);
            statisticalDimension.setSortNum(0);
            statisticalDimension.setCreateTime(new Date());
            statisticalDimensionList.add(statisticalDimension);
        }

        //维度表插入数据
        boolean flag = dataviewRiskStatisticalDimensionService.saveOrUpdateBatch(statisticalDimensionList);
        if(flag){
            log.info("通用-统计维度表插入成功!!!");
        }
        else{
            log.info("通用-统计维度表插入失败!!!");
        }

//        map.put(2, riskStrategyFinalSet);
//        map.put(3, auditTypeSet);
//        map.put(4, financeTypeSet);
//        map.put(6, sceneSet);
//        map.put(7, quotientSet);
//        List<String> collect = zmfSet.stream().
//                sorted(Comparator.comparing(BondSectionEnum::getZmfValueByCode))
//                .collect(Collectors.toList());
//        map.put(8, collect);
//        map.put(9, mianyaSet);


    }




}
