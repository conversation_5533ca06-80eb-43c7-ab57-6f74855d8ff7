package qnvip.data.overview.job;

import cn.hutool.json.JSONUtil;
import com.qnvip.base.vo.resp.JobTaskResp;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import qnvip.data.overview.business.risk.*;
import qnvip.data.overview.domain.risk.RiskRentInfoDO;
import qnvip.data.overview.enums.RedisKeyEnum;
import qnvip.data.overview.param.risk.RiskRentInfoParam;
import qnvip.data.overview.business.rpc.JobApiService;
import qnvip.data.overview.service.risk.RiskRentInfoService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.QiYeWeiXinUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/5/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
@EnableScheduling
public class RiskJob {

    private final RiskOverdueBusiness riskOverdueBusiness;
    private final RiskBusiness riskBusiness;
    private final RiskRenewalOverdueBusiness renewalOverdueBusiness;
    private final RiskRenewalBusiness riskRenewalBusiness;
    private final RiskQuotientBusiness riskQuotientBusiness;
    private final RiskMerchantOverdueBusiness merchantOverdueBusiness;
    private final RiskDistributionBusiness distributionBusiness;
    private final RiskRentInfoService riskRentInfoService;
    private final RedisTemplate redisTemplate;
    private final JobApiService jobApiService;
    //数仓任务是否完成
    private static final String VINTAGE_TASK = "VINTAGE_TASK";


    /**
     * 风控大盘-经营
     */
//    @XxlJob("riskOverdueJob")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskOverdueJob(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        riskOverdueBusiness.execOldData(ds, overdueDays);
        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-经营
     */
    @XxlJob("riskOverdueJob_new")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskOverdueJob_new(String s) {
//        String s="3";
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("riskOverdueJob_new开始执行...");
        riskOverdueBusiness.execOldData_new(ds, overdueDays);
        log.info("riskOverdueJob_new运行完成...");
        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-通用tidb版本
     */
    @XxlJob("riskOverdueJob_tidb")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskOverdueJob_tidb(String s) {
//        String s="3";
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("租赁自营明细-TIDB dataview_risk_general_order_overdue_gray-开始");
        riskOverdueBusiness.execOldData_tidb(ds, overdueDays);
        log.info("租赁自营明细-TIDB dataview_risk_general_order_overdue_gray-完成");
        return ReturnT.SUCCESS;
    }




    /**
     * 风控大盘-经营
     */
    @XxlJob("updateScore")
    public ReturnT<String> updateScore(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        riskOverdueBusiness.updateScore(ds);
        return ReturnT.SUCCESS;
    }


    /**
     * 风控大盘-逾期
     */
    @XxlJob("riskRenewJob")
    public ReturnT<String> riskRenewJob(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        renewalOverdueBusiness.execOldData(ds, overdueDays);
        riskRenewalBusiness.execCount(ds);
        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-逾期
     */
    @XxlJob("riskRenewJobNew")
    public ReturnT<String> riskRenewJobNew(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("riskRenewJobNew数据开始执行...");
        renewalOverdueBusiness.execOldData(ds, overdueDays);
        riskRenewalBusiness.execCount(ds);
        log.info("riskRenewJobNew数据运行完成...");
        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-通用-逾期
     */
    @XxlJob("riskRenewJobTidb")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskRenewJobTidb(String s) {
//        String s = "3";
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("riskRenewJobNew数据开始执行...租赁自营-续租TIDB dataview_risk_general_order_overdue_gray");
        renewalOverdueBusiness.execOldDataTidb(ds, overdueDays);
//        riskRenewalBusiness.execCount(ds);
        log.info("riskRenewJobNew数据运行完成...租赁自营-续租TIDB dataview_risk_general_order_overdue_gray");
        return ReturnT.SUCCESS;
    }

    @XxlJob("riskRenewTempJob")
    public ReturnT<String> riskRenewTempJob(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        riskRenewalBusiness.execCount(ds);
        return ReturnT.SUCCESS;
    }


    /**
     * 风控大盘-导流商
     */
    @XxlJob("riskQuotientJob")
    public ReturnT<String> riskQuotientJob(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        riskQuotientBusiness.execCount(ds);
        return ReturnT.SUCCESS;
    }


    /**
     * 风控大盘-分期
     */
    @XxlJob("riskMerchantJob")
    public ReturnT<String> riskMerchantJob(String s) {
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        merchantOverdueBusiness.execOldData(ds);

        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-分期 TIDB版本
     */
    @XxlJob("riskMerchantJobTidb")
    public ReturnT<String> riskMerchantJobTidb(String s) {
        log.info("	赊销自营明细统计, dataview_risk_merchant_order_overdue_gray跑数开始");
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        merchantOverdueBusiness.execOldDataTidb(ds);
        log.info("	赊销自营明细统计, dataview_risk_merchant_order_overdue_gray跑数结束");
        return ReturnT.SUCCESS;
    }

    public static void main(String[] args) {
        //2025-04-12 00:00:10.000
        LocalDateTime completeTime = LocalDateTime.parse("2025-07-23 00:21:10.000", DateTimeFormatter.ofPattern("yyyy" +
                "-MM-dd HH:mm:ss.SSS"));
        //如何完成时间不等于今天的时间,则告警跳出，不执行后续的任务
        LocalDate now = LocalDate.now();
        if(!completeTime.toLocalDate().equals(now)){
            log.info("商户长租明细表 dataview_risk_distribution_order_overdue1 跳出");
        }

    }
    /**
     * 风控大盘-商户长租
     */
    @XxlJob("riskDistributionJobTidb")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskDistributionJob(String s) {
        List<JobTaskResp> jobTaskResps = jobApiService.queryTaskByCode(VINTAGE_TASK);
        if (CollectionUtils.isEmpty(jobTaskResps)) {
            QiYeWeiXinUtil.sendPostByMap("大盘-商户明细表加工失败-数仓任务未完成");
            return ReturnT.FAIL;
        }
        if (CollectionUtils.isNotEmpty(jobTaskResps)) {
            //以完成时间排序，取最新的一条
            JobTaskResp jobTaskResp =
                    jobTaskResps.stream().max(Comparator.comparing(JobTaskResp::getTaskSuccessTime)).get();
            //2025-04-12 00:00:10.000
            LocalDateTime completeTime = jobTaskResp.getTaskSuccessTime();
            //如何完成时间不等于今天的时间,则告警跳出，不执行后续的任务
            LocalDate now = LocalDate.now();
            if (!completeTime.toLocalDate().equals(now)) {
                QiYeWeiXinUtil.sendPostByMap("大盘-商户明细表加工失败-数仓任务未完成");
                return ReturnT.FAIL;
            }
        }
//        String s="3";
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isNotBlank(s)) {
            ds = s;
        }
        log.info("商户长租明细表 dataview_risk_distribution_order_overdue_gray 开始");
        distributionBusiness.execOldData(3);
        return ReturnT.SUCCESS;
    }

    /**
     * 风控大盘-商户长租-逾期
     */
    @XxlJob("riskRenewDistributionJobTidb")
//    @Scheduled(fixedRate = 5000000, initialDelay = 0)
    public ReturnT<String> riskRenewDistributionJobTidb(String s) {
//        String s = "3";
        LocalDate now = LocalDate.now();
        String ds = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        if (StringUtils.isBlank(s)) {
            return ReturnT.FAIL;
        }
        int[] overdueDays = Arrays.stream(s.split(",")).mapToInt(Integer::parseInt).toArray();
        log.info("riskRenewDistributionJobTidb数据开始执行...商户长租续租");
        renewalOverdueBusiness.execOldDataDistributionTidb(ds, overdueDays);
//        riskRenewalBusiness.execCount(ds);
        log.info("riskRenewDistributionJobTidb数据运行完成...商户长租续租");
        return ReturnT.SUCCESS;
    }

    /**
     * 缓存数据,暂时不用
     */
    @XxlJob("riskCacheDataJob")
    @Deprecated
    public ReturnT<String> cacheData(String s) {
        RiskRentInfoParam riskRentInfoParam = new RiskRentInfoParam();
        LocalDateTime today = LocalDateTime.of(LocalDate.now(), LocalTime.MAX);
        LocalDateTime beforeToday = LocalDateTime.of(LocalDate.now().minusDays(7), LocalTime.MIN);
        riskRentInfoParam.setFromTime(DateUtils.dateToString(beforeToday));
        riskRentInfoParam.setToTime(DateUtils.dateToString(today));
        riskRentInfoParam.setType(Collections.singletonList(0));
        String key = String.format(RedisKeyEnum.NEW_RENT.getKey(), riskRentInfoParam);
        riskRentInfoParam.setToTime(DateUtils.dateToString(today.minusDays(1)));
        List<RiskRentInfoDO> last6DayList =
                riskRentInfoService.getList(riskRentInfoParam).stream()
                        .filter(t -> !today.isEqual(t.getCountDay()))
                        .collect(Collectors.toList());
        redisTemplate.delete(key);
        redisTemplate.opsForValue().set(key, JSONUtil.toJsonStr(last6DayList));
        redisTemplate.expire(key, 1, TimeUnit.DAYS);
        return ReturnT.SUCCESS;
    }
}
