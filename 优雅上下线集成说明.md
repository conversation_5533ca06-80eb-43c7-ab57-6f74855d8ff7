# 优雅上下线功能集成说明

## 已完成的集成步骤

### 1. 添加依赖
已在 `qnvip-data-overview-web/pom.xml` 中添加了优雅上下线组件依赖：
```xml
<dependency>
    <groupId>com.qnvip</groupId>
    <artifactId>qnvip-actuator-starter</artifactId>
    <version>1.0.0.RELEASE</version>
</dependency>
```

### 2. 实现SmartOffline接口
创建了三个优雅下线实现类：

#### DubboSmartOffline
- 文件位置：`qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/DubboSmartOffline.java`
- 功能：处理Dubbo服务的优雅下线，断开Dubbo调用流量

#### SpringCloudSmartOffline  
- 文件位置：`qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/SpringCloudSmartOffline.java`
- 功能：处理Spring Cloud服务的优雅下线，断开OpenFeign调用流量

#### RocketMqSmartOffline
- 文件位置：`qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/RocketMqSmartOffline.java`
- 功能：处理RocketMQ消费者的优雅下线

### 3. 创建SPI配置文件
- 文件位置：`qnvip-data-overview-web/src/main/resources/META-INF/services/com.qnvip.common.base.SmartOffline`
- 内容：注册了所有三个SmartOffline实现类

### 4. MQ消费者集成示例
- 文件位置：`qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/MqConsumerExample.java`
- 说明：展示了如何在MQ消费者中集成优雅下线功能

## 使用说明

### 如果项目中有RocketMQ消费者
如果项目中有实际的RocketMQ消费者，需要在消费者启动后调用：
```java
// 在消费者启动后添加到优雅下线管理
RocketMqSmartOffline.addConsumer(consumer);
```

### 验证步骤

#### 1. 测试环境验证
执行以下命令验证优雅下线功能：
```bash
curl "http://${ip}:${port}/devops/offline?key=qnvip"
```

验证结果应包括：
- Dubbo服务下线日志：`======dubbo服务准备下线======` 和 `======dubbo服务已经下线======`
- Spring Cloud服务下线日志：`======springCloud服务准备下线======` 和 `======springCloud服务已经下线======`
- RocketMQ消费者下线日志：`======RocketMQ消费者准备下线======` 和 `======RocketMQ消费者已经下线======`

#### 2. 检查服务状态
- **Nacos服务列表**：确认服务实例已从注册中心下线
- **阿里云MQ控制台**：确认消费组状态变更
- **Nginx状态页面**：如果配置了Nginx，检查服务状态是否为down
  - 地址：`http://alb-6frkot8iawgwtk4zvk.cn-hangzhou.alb.aliyuncs.com:18080/status`

#### 3. 生产环境验证
在生产环境部署后，需要验证：
- 服务能够正常下线和重新上线
- 调用方日志无异常
- 服务自身日志无异常

## 流水线配置

### Nginx配置
如果项目需要配置Nginx，请联系运维配置新的Nginx规则。

### 流水线修改
部署完成后，需要联系运维修改流水线配置，确保在发布过程中能够：
1. 先执行优雅下线
2. 再进行服务重启
3. 最后重新上线

## 注意事项

1. **当前项目状态**：项目中未发现标准的RocketMQ消费者实现，主要使用内部队列（DataViewTrackTaskQueue）
2. **MQ消费者**：如果后续添加了RocketMQ消费者，请参考MqConsumerExample示例进行集成
3. **测试建议**：建议先在测试环境充分验证后再部署到生产环境
4. **监控**：部署后请密切关注服务日志和调用方日志，确保无异常

## 相关文件清单

- `qnvip-data-overview-web/pom.xml` - 添加了依赖
- `qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/DubboSmartOffline.java` - Dubbo优雅下线
- `qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/SpringCloudSmartOffline.java` - Spring Cloud优雅下线  
- `qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/RocketMqSmartOffline.java` - RocketMQ优雅下线
- `qnvip-data-overview-web/src/main/resources/META-INF/services/com.qnvip.common.base.SmartOffline` - SPI配置
- `qnvip-data-overview-web/src/main/java/qnvip/data/overview/config/MqConsumerExample.java` - MQ集成示例
