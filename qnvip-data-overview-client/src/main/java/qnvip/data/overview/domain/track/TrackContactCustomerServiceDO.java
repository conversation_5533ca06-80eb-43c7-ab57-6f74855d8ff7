package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_contact_customer_service")
public class TrackContactCustomerServiceDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 客服方式:1：进入在线客服 2：客服热线 3：投诉电话
     */
    private Integer contactType;

    /**
     * 投诉电话
     */
    private String complaints;
}
