package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;

import java.time.LocalDate;

import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_access_source_quotient")
public class OperateAccessSourceQuotientDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 场景值名称
     */
    private String sceneName;

    private Integer miniType;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;


    /**
     * 点击量
     */
    private Long clickCount = 0L;

    /**
     * 点击人数
     */
    private Long clickUserCount = 0L;


    /**
     * 下单量
     */
    private Long orderCount = 0L;

    /**
     * 风控通过量
     */
    private Long riskPassCount = 0L;

    /**
     * 支付量
     */
    private Long paymentCount = 0L;

}
