package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_merchant_situation")
public class RiskMerchantSituationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 业务渠道
     */
    private Integer businessChannel;

    /**
     * 前置风控用户
     */
    private Long prepositionRiskUser;
    /**
     * 风控用户
     */
    private Long riskUser;

    /**
     * 有效用户
     */
    @Deprecated
    private Long validUser;

    /**
     * 基本准入通过
     */
    private Long passUser;

    /**
     * 反欺诈通过
     */
    private Long antiFraudPassCnt;

    /**
     * 人行风控执行总数
     */
    private Long centralBankInquiry;

    /**
     * 人行风控通过数
     */
    private Long centralBankPass;

    /**
     * 一级审批通过
     */
    private Long bigDataPass;

    /**
     * 成功付款人数
     */
    private Long payUser;

    /**
     * 成功单量
     */
    private Long payCount;

    /**
     * 全款付款人数
     */
    private Long payUserWhole;

    /**
     * 非差额通审人数
     */
    private Long unDifferenceBigDataPass;

    /**
     * 差额通审人数
     */
    private Long differenceBigDataPass;
    /**
     * 发货数
     */
    private Long sendCount;

    /**
     * 总租金
     */
    private BigDecimal totalAmt;

    /**
     * 保证金
     */
    private BigDecimal margin;

    /**
     * 买断金
     */
    private BigDecimal buyOutAmt;

    /**
     * 支付取消人数
     */
    private Long payUserClose;

    /**
     * 信审关闭人数
     */
    private Long payCreditAuditClose;

    /**
     * 人行风控当日订单
     */
    private Integer curDayCount;

    /**
     * 支付宝支付人数
     */
    private Long aliPay;

    /**
     * 微信支付人数
     */
    private Long weiXinPay;

    /**
     * 其他渠道支付人数
     */
    private Long otherPay;

}
