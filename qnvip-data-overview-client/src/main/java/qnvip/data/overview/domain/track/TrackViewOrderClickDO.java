package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_view_order_click")
public class TrackViewOrderClickDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;
    /**
     * 订单ID
     */
    private String orderId;
}
