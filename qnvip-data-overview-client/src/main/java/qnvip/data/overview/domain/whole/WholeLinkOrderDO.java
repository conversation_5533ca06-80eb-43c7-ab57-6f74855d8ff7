package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_order")
public class WholeLinkOrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 下单量
     */
    private Long orderCnt;

    /**
     * 下单人数
     */
    private Long orderUv;

    /**
     * 免押人数
     */
    private Long unDepositUv;

    /**
     * 下单新客
     */
    private Long newOrderUv;

    /**
     * 顶搜下单量
     */
    private Long topSearchUv;

    /**
     * 无渠道标记量
     */
    private Long noSceneUv;

    /**
     * 未审关闭
     */
    private Long unAuditCloseUv;

    /**
     * top5渠道
     */
    private Long orderTop5ChannelUv;

    /**
     * top5场景
     */
    private Long orderTop5SceneUv;

    /**
     * iPhone下单量
     */
    private Long orderIphoneOrderCnt;

    /**
     * 13pm订单量
     */
    private Long orderIphone13pmOrderCnt;

    /**
     * 12+x订单量
     */
    private Long orderTwelveOrderCnt;


    /**
     * iPhone下单量占比
     */
    private BigDecimal orderIphoneOrderRate;

    /**
     * 13pm订单量占比
     */
    private BigDecimal orderIphone13pmOrderRate;

    /**
     * 12+x订单量占比
     */
    private BigDecimal orderTwelveOrderRate;

    /**
     * 免押人数占比
     */
    private BigDecimal unDepositUvRate;

    /**
     * 下单新客占比
     */
    private BigDecimal newOrderUvRate;

    /**
     * 顶搜下单量占比
     */
    private BigDecimal topSearchUvRate;

    /**
     * 无渠道标记量占比
     */
    private BigDecimal noSceneUvRate;

    /**
     * top5渠道占比
     */
    private BigDecimal orderTop5ChannelUvRate;

    /**
     * top5场景占比
     */
    private BigDecimal orderTop5SceneUvRate;

}
