package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableField;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.data.overview.enums.EventTrackEnum;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/9/24
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EventTrackDO extends BaseDO {
    /**
     * 事件key
     */
    private String eventKey;

    /**
     * 事件描述
     */
    private String eventDesc;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 推广渠道
     */
    private Integer miniType;

    /**
     * 进入页面
     */
    private String enterPageUrl;

    /**
     * 进入页面Code
     */
    private String enterPageCode;

    /**
     * 页面详情
     */
    private String enterPageDesc;


    /**
     * 上报时间
     */
    private LocalDateTime reportTime;

    /**
     * 用户id
     */
    private Long customerId;

    /**
     * 用户第三方id
     */
    private String customerThirdId;

    /**
     * 小程序版本号
     */
    private String appVersion;

    /**
     * 设备型号
     */
    private String deviceModel;

    /**
     * 设备品牌
     */
    private String deviceBrand;

    /**
     * 设备操作系统
     */
    private String deviceOs;

    /**
     * 设备操作形态版本
     */
    private String deviceOsVersion;

    /**
     * 网络
     */
    private String deviceNet;


    /**
     * 商品id
     */
    private String itemId;

    /**
     * 商户id
     */
    private String merchantId;

    /**
     * 推广渠道
     */
    private String miniTypeDesc;


    @TableField(exist = false)
    private String itemIds;

    public void setEventKey(String eventKey) {
        this.eventKey = eventKey;
        eventDesc = EventTrackEnum.getEventDesc(eventKey);
    }


    public void setMiniType(Integer miniType) {
        this.miniType = miniType;
        miniTypeDesc = MiniTypeEnum.getValueByCode(miniType);
    }

}
