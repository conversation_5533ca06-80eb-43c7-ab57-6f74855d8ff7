package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_fraud")
public class WholeLinkFraudDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 规则命中人数
     */
    private Long hitUv;

    /**
     * top1规则命中人数
     */
    private Long hitUvTop1;

    /**
     * top2规则命中人数
     */
    private Long hitUvTop2;

    /**
     * top3规则命中人数
     */
    private Long hitUvTop3;

    /**
     * top4规则命中人数
     */
    private Long hitUvTop4;

    /**
     * top5规则命中人数
     */
    private Long hitUvTop5;

    /**
     * top5渠道人数
     */
    private Long fraudTop5ChannelUv;

    /**
     * top5场景人数
     */
    private Long fraudTop5SceneUv;

    /**
     * 支付宝顶搜人数
     */
    private Long fraudAliUv;

    /**
     * 微信人数
     */
    private Long fraudWechatUv;

    /**
     * 字节人数
     */
    private Long fraudOtherUv;


}
