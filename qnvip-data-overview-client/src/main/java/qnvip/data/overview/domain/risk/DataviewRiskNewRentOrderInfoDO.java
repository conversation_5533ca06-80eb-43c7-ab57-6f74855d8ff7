package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.io.Serializable;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* Created by zhanglei on 2023-09-21
*/
@Data
@TableName("dataview_risk_new_rent_order_info")
@ApiModel(value="DataviewRiskNewRentOrderInfoDO对象", description="租赁风控大盘-新租赁报表=20230921")
public class DataviewRiskNewRentOrderInfoDO implements Serializable {

    private static final long serialVersionUID = 1L;
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "订单号")
    @TableId(value = "order_no", type = IdType.INPUT)
    private String orderNo;

    @ApiModelProperty(value = "风控订单创建时间")
    private LocalDateTime riskCreateTime;

    @ApiModelProperty(value = "租赁订单创建时间")
    private LocalDateTime rentCreateTime;

    @ApiModelProperty(value = "风控订单创建时间")
    private LocalDateTime riskUpdateTime;

    @ApiModelProperty(value = "风控人行发起时间")
    private LocalDateTime riskRhTime;

    @ApiModelProperty(value = "租赁发货时间")
    private LocalDateTime rentSendTime;

    @ApiModelProperty(value = "风控发货时间")
    private String riskSendTime;

    @ApiModelProperty(value = "租赁支付时间时间")
    private LocalDateTime rentPaymentTime;

    @ApiModelProperty(value = "风控支付时间时间")
    private LocalDateTime riskPayTime;


    @ApiModelProperty(value = "风控客户id")
    private Long riskCustomerId;

    @ApiModelProperty(value = "租赁客户id")
    private Long rentCustomerId;

    @ApiModelProperty(value = "租赁商户id")
    private Long rentMerchantId;

    @ApiModelProperty(value = "风控商户id")
    private Long riskMerchantId;

    @ApiModelProperty(value = "订单关单状态1,正常,5终止")
    private Integer rentTermination;

    @ApiModelProperty(value = "订单实际状态")
    private Integer rentOrderStatus;

    @ApiModelProperty(value = "风控关闭订单状态0:否、1:是")
    private Integer riskCloseStatus;

    @ApiModelProperty(value = "风控渠道")
    private Integer riskBusinessChannel;

    @ApiModelProperty(value = "订单类型1=长租2=短租3=全款商品")
    private Integer rentType;

    @ApiModelProperty(value = "来源小程序")
    private Integer rentMiniType;

    @ApiModelProperty(value = "下单共租标记 5:无租用中 10:只有自营租用中订单 20:只有商户租用中订单 30:自营商户都有租用中订单")
    private Integer rentCommonRentFlag;

    @ApiModelProperty(value = "是否共租订单 0-非共租，1-共租")
    private Integer riskCoRent;

    @ApiModelProperty(value = "设备状态: 1-新机; 2-旧机; 3-99新; 4-95新 5-准新机 6-90新 7-80新")
    private Integer rentEquipmentState;

    @ApiModelProperty(value = "设备类型: 0-普通机; 1=监管机")
    private Integer rentEquipmentType;

    @ApiModelProperty(value = "监管机类型 0-非监管机 1-监管机 2-非监管机转监管机 3-监管机转非监管机")
    private Integer riskSupervisoryMachine;

    @ApiModelProperty(value = "进入小程序场景值")
    private String riskScene;

    @ApiModelProperty(value = "导流商id")
    private Long rentQuotientId;

    @ApiModelProperty(value = "导流商编号")
    private String riskQuotientCode;

    @ApiModelProperty(value = "导流商名称")
    private String riskQuotientName;

    @ApiModelProperty(value = "导流商场景值")
    private String riskQuotientScene;

    @ApiModelProperty(value = "订单来源标签，1租物活动(租物引流)，2租物活动app跳转")
    private Integer rentOrderSourceTag;
    @ApiModelProperty(value = "订单来源标签，0非租物，1租物引流")
    private Integer zwDrainageFlag;

    @ApiModelProperty(value = "金融方案类型")
    private Integer rentRateConfigType;

    @ApiModelProperty(value = "金融方案类型")
    private Integer riskFinanceTemplateType;

    @ApiModelProperty(value = "免押状态0=无1=全额2=部分")
    private Integer rentBondFreeStatus;

    @ApiModelProperty(value = "10基本准入-信用20基本准入-反欺诈30反欺诈策略 40人行风控50三方60一级审批策略70支付80信审90三级审批策略100四级审批策略")
    private Integer riskFicoStatus;

    @ApiModelProperty(value = "人工审核状态 1=待审核 5=查征信 10=审核通过 15=审核不通过")
    private Integer riskArtificialAuditStatus;

    @ApiModelProperty(value = "自动风控意见")
    private String riskOpinion;

    @ApiModelProperty(value = "来源平台,支付宝,微信....")
    private String rentPlatformCode;

    @ApiModelProperty(value = "0:等待分流;5:分流中;10:分流接单;15:分流拒绝;20:待次日分流;")
    private Integer rentMerchantTransfer;
    @ApiModelProperty(value = "是否需要信审 0-否 1-是")
    private Integer needRiskAuth;

    @ApiModelProperty(value = "新   是否前置1通过,0不通过,1通过")
    private Integer front1PassFlag;

    @ApiModelProperty(value = "新   是否前置2通过,0不通过,1通过")
    private Integer front2PassFlag;

    @ApiModelProperty(value = "新   风控通过状态,0不通过,1通过")
    private Integer riskPassFlag;

    @ApiModelProperty(value = "转人工信审通过,0不通过,1通过")
    private Integer labourAuditFlag;

    @ApiModelProperty(value = "信审通过标记,0不通过,1通过")
    private Integer creditAuditPassFlag;

    @ApiModelProperty(value = "租赁转人工信审通过,0不通过,1通过")
    private Integer rentArtificialAuditFlag;



}
