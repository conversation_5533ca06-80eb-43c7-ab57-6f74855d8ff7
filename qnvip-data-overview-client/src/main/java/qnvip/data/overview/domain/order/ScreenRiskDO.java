package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/8 11:43 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_screen_risk")
public class ScreenRiskDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 碎屏险销量
     */
    private Long saleCount;
    /**
     * 碎屏险GMV
     */
    private BigDecimal orderGmv;
    /**
     * 碎屏险成本价
     */
    private BigDecimal costPrice;
    /**
     * 碎屏险下单量
     */
    private Long orderCount;
    /**
     * 碎屏险支付单量
     */
    private Long payCount;
}