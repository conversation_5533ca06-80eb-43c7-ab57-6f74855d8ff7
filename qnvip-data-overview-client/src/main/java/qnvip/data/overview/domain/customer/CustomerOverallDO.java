package qnvip.data.overview.domain.customer;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_customer_overall")
public class CustomerOverallDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 访问渠道
     */
    private Integer miniType;

    /**
     * 访问小程序的人数
     */
    private Long uv = 0L;

    /**
     * 访问小程序的次数
     */
    private Long pv = 0L;

    /**
     * 总跳失数
     */
    private Long totalJumpLoss = 0L;

    /**
     * 总访问页数
     */
    private Long totalPageBrowsing = 0L;

    /**
     * 总停留时长
     */
    private BigDecimal totalKeepTime = BigDecimal.ZERO;

    /**
     * 新老访客标识（1：new 2：old）
     */
    private Integer type;

}
