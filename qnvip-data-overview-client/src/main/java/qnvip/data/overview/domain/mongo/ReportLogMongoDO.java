package qnvip.data.overview.domain.mongo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDate;

@Document(collection = "dataview_report_log")
@Data
@Accessors(chain = true)
public class ReportLogMongoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    private String id;
    @ApiModelProperty(value = "统计日期")
    private LocalDate day;
    @ApiModelProperty(value = "类型:月报=1,周报=0")
    private Integer type;

    @ApiModelProperty(value = "模块:租赁=1,分期=2,催收&发诉=3")
    private Integer model;

    @ApiModelProperty(value = "指标code")
    private String code;

    @ApiModelProperty(value = "统计时间")
    private String countDay;

    @ApiModelProperty(value = "值")
    private Object attribute;

    @ApiModelProperty(value = "单位")
    private String unit;
}

