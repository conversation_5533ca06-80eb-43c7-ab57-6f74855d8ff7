package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_general_order_overdue_count")
public class RiskGeneralOrderOverdueCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private LocalDateTime countDay;

    /**
     * 统计纬度
     */
    private String countType;

    /**
     * 业务总额
     */
    private BigDecimal amountTotal;

    /**
     * 总单量
     */
    private BigDecimal orderTotal;

    /**
     * 最大期数
     */
    private Integer maxDay;

    /**
     * 展示期数
     */
    private Integer term;

    /**
     * 逾期天数
     */
    private Integer overdueDay;

    /**
     * 统计方式
     */
    private Integer countWay;

    /**
     * 统计项 1:逾期比 2:保证金比 3:其他收入比 0:都不统计
     */
    private Integer countItem;

    /**
     * 上一级统计维度
     */
    private String parentCountType;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 是否自定续租 0:否,1:是 -1:未选择
     */
    private Integer isRenew;

    /**
     * MOB1
     */
    private String term1;

    /**
     * MOB2
     */
    private String term2;

    /**
     * MOB3
     */
    private String term3;

    /**
     * MOB4
     */
    private String term4;

    /**
     * MOB5
     */
    private String term5;

    /**
     * MOB6
     */
    private String term6;

    /**
     * MOB7
     */
    private String term7;

    /**
     * MOB8
     */
    private String term8;

    /**
     * MOB9
     */
    private String term9;

    /**
     * MOB10
     */
    private String term10;

    /**
     * MOB11
     */
    private String term11;

    /**
     * MOB12
     */
    private String term12;

    /**
     * MOB13
     */
    private String term13;

    /**
     * MOB14
     */
    private String term14;

    /**
     * MOB15
     */
    private String term15;

    /**
     * MOB16
     */
    private String term16;

    /**
     * MOB17
     */
    private String term17;

    /**
     * MOB18
     */
    private String term18;

    /**
     * MOB19
     */
    private String term19;

    /**
     * MOB20
     */
    private String term20;

    /**
     * MOB21
     */
    private String term21;

    /**
     * MOB22
     */
    private String term22;

    /**
     * MOB23
     */
    private String term23;

    /**
     * MOB24
     */
    private String term24;

    /**
     * MOB25
     */
    private String term25;

    /**
     * MOB26
     */
    private String term26;

    /**
     * MOB27
     */
    private String term27;

    /**
     * MOB28
     */
    private String term28;

    /**
     * MOB29
     */
    private String term29;

    /**
     * MOB30
     */
    private String term30;


}
