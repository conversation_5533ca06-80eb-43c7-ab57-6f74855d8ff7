package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_access_source")
public class OperateAccessSourceDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 访问小程序的人数
     */
    private Long uv = 0L;

    /**
     * 访问小程序的人数
     */
    private Long pv = 0L;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 场景名称
     */
    private String sceneName;

    /**
     * 订单
     */
    private Long orderCount = 0L;

    /**
     * 审核订单
     */
    private Long riskCount = 0L;

    /**
     * 通审定单
     */
    private Long riskPassCount = 0L;

    /**
     * 支付订单
     */
    private Long payCount = 0L;


    /**
     * 渠道
     */
    private Integer miniType;

    /**
     * 类型类型 1：用户来源场景分析 2：订单来源场景分析'
     */
    private Integer type;


    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OperateAccessSourceDO that = (OperateAccessSourceDO) o;
        return Objects.equals(scene, that.scene) && Objects.equals(miniType, that.miniType) && Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), scene, miniType, type);
    }
}
