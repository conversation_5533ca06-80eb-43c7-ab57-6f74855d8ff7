package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_order_overdue_count")
public class RiskOrderOverdueCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private LocalDateTime countDay;

    /**
     * 统计纬度
     */
    private String countType;

    /**
     * 业务总额
     */
    private BigDecimal amountTotal;

    /**
     * 总单量
     */
    private BigDecimal orderTotal;

    /**
     * MOB1
     */
    private String term1;

    /**
     * MOB2
     */
    private String term2;

    /**
     * MOB3
     */
    private String term3;

    /**
     * MOB4
     */
    private String term4;

    /**
     * MOB5
     */
    private String term5;

    /**
     * MOB6
     */
    private String term6;

    /**
     * MOB7
     */
    private String term7;

    /**
     * MOB8
     */
    private String term8;

    /**
     * MOB9
     */
    private String term9;

    /**
     * MOB10
     */
    private String term10;

    /**
     * MOB11
     */
    private String term11;

    /**
     * MOB12
     */
    private String term12;

    /**
     * 逾期天数
     */
    private Integer overdueDay;

    /**
     * 最大期数
     */
    private Integer maxDay;

    /**
     * 层级
     */
    private Integer level;

    /**
     * 统计方式
     */
    private Integer countWay;

    /**
     * 统计项
     */
    private Integer countItem;

    /**
     * 上一级
     */
    private String  parentCountType;
}
