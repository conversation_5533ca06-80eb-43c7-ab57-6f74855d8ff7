package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_icon_view")
public class TrackIconViewDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * IconId
     */
    private String iconId;

    /**
     * icon名称
     */
    private String name;


}
