package qnvip.data.overview.domain.alarm;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

/**
 * create by gw on 2021/12/27
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_alarm_config")
public class AlarmConfigDO extends BaseDO {

    @ApiModelProperty(value = "常量key")
    private String constKey;

    @ApiModelProperty(value = "常量value")
    private String constValue;

    @ApiModelProperty(value = "常量描述")
    private String constDescription;

}