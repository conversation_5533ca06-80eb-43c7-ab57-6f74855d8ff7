package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_access_core")
public class OperateAccessCoreDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 访问渠道
     */
    private Integer miniType;

    /**
     * 访问小程序的人数
     */
    private Long uv = 0L;

    /**
     * 访问小程序的次数
     */
    private Long pv = 0L;

    /**
     * 新增用户数
     */
    private Long uvNew = 0L;

    /**
     * 有效新用户
     */
    private Long uvNewValid = 0L;

    /**
     * 高质量新用户
     */
    private Long uvNewQuality = 0L;

    /**
     * 动销新用户
     */
    private Long dnpu = 0L;

    /**
     * 留存用户数
     */
    private Long uvKeep = 0L;

    /**
     * 流失
     */
    private Long uvLoss = 0L;

    /**
     * 回流用户数
     */
    private Long uvBackFlow = 0L;

    /**
     * 日登录次数
     */
    private Long dau = 0L;

}
