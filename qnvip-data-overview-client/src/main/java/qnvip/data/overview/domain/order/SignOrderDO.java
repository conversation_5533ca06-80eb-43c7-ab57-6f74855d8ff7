package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_sign_order")
public class SignOrderDO extends BaseDO {

    /**
     * 统计日期
     */
    private LocalDateTime countDay;


    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 商户id
     */
    private Long merchantId;

    /**
     * 订单类型1=长租2=短租3=全款商品
     */
    private Integer type;

    /**
     * 订单编号
     */
    private String orderNo;

}
