package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_merchant_order_overdue_gray")
public class RiskMerchantOrderOverdueDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租月
     */
    private LocalDateTime countDay;

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 客户性质 0:新客户,1:老客户
     */
    private Integer customerType;

    /**
     * 评分
     */
    private Integer score;
    /**
     * 主订单编号
     */
    private String no;

    private Integer miniType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 保证金期数
     */
    private String bondTermType;

    /**
     * 期数
     */
    private String termType;

    /**
     * 审核方式
     */
    private String auditType;

    /**
     * 风控等级
     */
    private String riskLevel;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;

    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 协议总租金
     */
    private BigDecimal rentTotal;

    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    /**
     * 折扣金额
     */
    private BigDecimal discountAmt;
    /**
     * MOB1
     */
    private BigDecimal term1;

    /**
     * MOB2
     */
    private BigDecimal term2;

    /**
     * MOB3
     */
    private BigDecimal term3;

    /**
     * MOB4
     */
    private BigDecimal term4;

    /**
     * MOB5
     */
    private BigDecimal term5;

    /**
     * MOB6
     */
    private BigDecimal term6;

    /**
     * MOB7
     */
    private BigDecimal term7;

    /**
     * MOB8
     */
    private BigDecimal term8;

    /**
     * MOB9
     */
    private BigDecimal term9;

    /**
     * MOB10
     */
    private BigDecimal term10;

    /**
     * MOB11
     */
    private BigDecimal term11;

    /**
     * MOB12
     */
    private BigDecimal term12;

    /**
     * 第一期是否逾期
     */
    private Integer isOverdue1;

    /**
     * 第二期是否逾期
     */
    private Integer isOverdue2;

    /**
     * 第三期是否逾期
     */
    private Integer isOverdue3;

    /**
     * 第四期是否逾期
     */
    private Integer isOverdue4;

    /**
     * 第五期是否逾期
     */
    private Integer isOverdue5;

    /**
     * 第六期是否逾期
     */
    private Integer isOverdue6;

    /**
     * 第七期是否逾期
     */
    private Integer isOverdue7;

    /**
     * 第八期是否逾期
     */
    private Integer isOverdue8;

    /**
     * 第九期是否逾期
     */
    private Integer isOverdue9;

    /**
     * 第十期是否逾期
     */
    private Integer isOverdue10;

    /**
     * 第11期是否逾期
     */
    private Integer isOverdue11;

    /**
     * 第12期是否逾期
     */
    private Integer isOverdue12;

    /**
     * 1期逾期滞纳金
     */
    private BigDecimal overdueFine1;

    /**
     * 2期逾期滞纳金
     */
    private BigDecimal overdueFine2;

    /**
     * 3期逾期滞纳金
     */
    private BigDecimal overdueFine3;

    /**
     * 4期逾期滞纳金
     */
    private BigDecimal overdueFine4;

    /**
     * 5期逾期滞纳金
     */
    private BigDecimal overdueFine5;

    /**
     * 6期逾期滞纳金
     */
    private BigDecimal overdueFine6;

    /**
     * 7期逾期滞纳金
     */
    private BigDecimal overdueFine7;

    /**
     * 8期逾期滞纳金
     */
    private BigDecimal overdueFine8;

    /**
     * 9期逾期滞纳金
     */
    private BigDecimal overdueFine9;

    /**
     * 10期逾期滞纳金
     */
    private BigDecimal overdueFine10;

    /**
     * 11期逾期滞纳金
     */
    private BigDecimal overdueFine11;

    /**
     * 12期逾期滞纳金
     */
    private BigDecimal overdueFine12;

    /**
     * 逾期天数
     */
    private Integer overdueDay;


    /**
     * 一级品类
     */
    private String firstCategory;

    /**
     * 二级品类
     */
    private String secondCategory;

    private String productType;
    private String glodType;
    private String applicationName;
    private String supervisedMachine;
    // 0 低定价 10高定价
    private Integer bizType;

}
