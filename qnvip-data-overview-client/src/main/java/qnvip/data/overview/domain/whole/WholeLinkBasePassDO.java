package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_base_pass")
public class WholeLinkBasePassDO extends BaseDO {
    private static final long serialVersionUID = 1L;



    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 申请人数
     */
    private Long applyUv;

    /**
     * 有效人数
     */
    private Long basePassValidUv;

    /**
     * 准入人数
     */
    private Long allowUv;

    /**
     * 阿里准入人数
     */
    private Long aliAllowUv;

    /**
     * 年龄禁入人数
     */
    private Long ageForbidUv;

    /**
     * 顶地区禁入人数
     */
    private Long areaForbidUv;

    /**
     * 自黑禁入人数
     */
    private Long blacklistForbidUv;


    /**
     * top渠道
     */
    private Long topChannelUv;

    /**
     * top5场景
     */
    private Long topSceneUv;

    /**
     * iPhone下单量
     */
    private Long iphoneOrderCnt;

    /**
     * 13pm订单量
     */
    private Long iphone13pmOrderCnt;

    /**
     * 12+x订单量
     */
    private Long twelveOrderCnt;

    /**
     * 准入占比
     */
    private BigDecimal allowRate;

    /**
     * 有效占比
     */
    private BigDecimal basePassValidRate;

}
