package qnvip.data.overview.domain.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.time.LocalDate;

@Data
@Document(collection = "whole_flow_health")
@Accessors(chain = true)
public class WholeFlowHealthMongoDO implements Serializable {

    private static final long serialVersionUID = 1L;
    @Id
    private String id;
    private LocalDate countDay;
    private Integer miniType;
    private String scene;
    private Integer hour;
    private String key;
    private String value;

}
