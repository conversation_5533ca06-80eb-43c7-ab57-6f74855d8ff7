package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_submit_order_click")
public class TrackSubmitOrderClickDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 订单ID
     */
    private String orderId;

}
