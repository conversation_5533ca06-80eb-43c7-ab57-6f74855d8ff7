package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 财务大盘报表 create by gw on 2022/2/24
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_finance_report")
public class FinanceReportDO extends BaseDO {
    @ApiModelProperty("统计时间")
    private LocalDateTime countDay;

    @ApiModelProperty("父订单起租时间")
    private LocalDateTime parentDay;

    @ApiModelProperty("到期时间")
    private LocalDateTime rentEndDate;

    @ApiModelProperty("父订单到期时间")
    private LocalDateTime parentEndDate;

    @ApiModelProperty("来源")
    private Integer miniType;

    @ApiModelProperty("业务类型 1:自营 ,0:分期")
    private Integer businessType;

    @ApiModelProperty("方案类型:1:12+N,2:3+N,3:12转3,4:9+3,5:12+N续租,6:3+N续租,7:12转3续租,8:9+3续租")
    private Integer financeType;

    @ApiModelProperty("基础项真实采购额")
    private BigDecimal baseActSupplyPrice = BigDecimal.ZERO;

    @ApiModelProperty("基础项真实采购额单量")
    private Integer baseActSupplyPriceCount = 0;

    @ApiModelProperty("基础项:收取保证金")
    private BigDecimal baseBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("基础项:收取保证金单量")
    private Integer baseBondCount = 0;

    @ApiModelProperty("基础项:协议总租金")
    private BigDecimal baseRentAmt = BigDecimal.ZERO;

    @ApiModelProperty("基础项协议总租金单量")
    private Integer baseRentCount = 0;

    @ApiModelProperty("基础项:售前优惠")
    private BigDecimal basePreDiscount = BigDecimal.ZERO;

    @ApiModelProperty("基础项:售前优惠单量")
    private Integer basePreDiscountCount = 0;

    @ApiModelProperty("基础项:到期买断金")
    private BigDecimal baseBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("基础项:到期买断金单量")
    private Integer baseBuyoutCount = 0;

    @ApiModelProperty("已收租金:已收月租金")
    private BigDecimal rentReceiveRent = BigDecimal.ZERO;

    @ApiModelProperty("已收租金:已收月租金单量")
    private Integer rentReceiveRentCount = 0;

    @ApiModelProperty("已收租金:保证金抵扣租金")
    private BigDecimal rentDeductBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收租金:保证金抵扣租金单量")
    private Integer rentDeductBondCount = 0;

    @ApiModelProperty("已收租金:租后减免租金")
    private BigDecimal rentDeductAfterRent = BigDecimal.ZERO;

    @ApiModelProperty("已收租金:租后减免租金单量")
    private Integer rentDeductAfterRentCount = 0;

    @ApiModelProperty("已收租金:运营减免租金")
    private BigDecimal rentDeductMarketing = BigDecimal.ZERO;

    @ApiModelProperty("已收租金:运营减免租金单量")
    private Integer rentDeductMarketingCount = 0;

    @ApiModelProperty("已收租金:归还优惠")
    private BigDecimal rentReturnDiscount = BigDecimal.ZERO;

    @ApiModelProperty("已收租金:归还优惠单量")
    private Integer rentReturnDiscountCount = 0;

    @ApiModelProperty("已收买断金:续租本金")
    private BigDecimal buyoutRenewPrincipal = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:续租本金单量")
    private Integer buyoutRenewPrincipalCount = 0;

    @ApiModelProperty("已收买断金:到期买断金")
    private BigDecimal buyoutReceiveBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:到期买断金单量")
    private Integer buyoutReceiveBuyoutCount = 0;

    @ApiModelProperty("已收买断金:保证金抵扣买断金")
    private BigDecimal buyoutDeductBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:保证金抵扣买断金单量")
    private Integer buyoutDeductBuyoutCount = 0;

    @ApiModelProperty("已收买断金:售后减免买断金(减免+返利)")
    private BigDecimal buyoutReduceAfterAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:售后减免买断金单量")
    private Integer buyoutReduceAfterCount = 0;

    @ApiModelProperty("已收买断金:运营减免买断金")
    private BigDecimal buyoutReduceMarketingAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:运营减免买断金单量")
    private Integer buyoutReduceMarketingCount = 0;

    @ApiModelProperty("已收买断金:归还本金")
    private BigDecimal buyoutReturnCapitalAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收买断金:归还本金单量")
    private Integer buyoutReturnCapitalCount = 0;

    @ApiModelProperty("其他收入:逾期滞纳金")
    private BigDecimal otherOverdueAmt = BigDecimal.ZERO;

    @ApiModelProperty("其他收入:逾期滞纳金单量")
    private Integer otherOverdueCount = 0;

    @ApiModelProperty("其他收入:优惠返还")
    private BigDecimal otherDiscountReturnAmt = BigDecimal.ZERO;

    @ApiModelProperty("其他收入:优惠返还单量")
    private Integer otherDiscountReturnCount = 0;

    @ApiModelProperty("其他收入:违约保证金")
    private BigDecimal otherBreachBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("其他收入:违约保证金单量")
    private Integer otherBreachBondCount = 0;

    @ApiModelProperty("保证金:转续租")
    private BigDecimal bondRenewAmt = BigDecimal.ZERO;

    @ApiModelProperty("保证金:转续租单量")
    private Integer bondRenewCount = 0;

    @ApiModelProperty("保证金:结清退回")
    private BigDecimal bondBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("保证金:结清退回单量")
    private Integer bondBackBondCount = 0;

    @ApiModelProperty("保证金:结清未退回")
    private BigDecimal bondNotBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("保证金:结清未退回单量")
    private Integer bondNotBackBondCount = 0;

    @ApiModelProperty("保证金:归还退回")
    private BigDecimal bondReturnBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("保证金:归还退回单量")
    private Integer bondReturnBackBondCount = 0;

    @ApiModelProperty("保证金:保证金抵扣赔偿金")
    private BigDecimal bondDeductDamageAmt = BigDecimal.ZERO;

    @ApiModelProperty("保证金:保证金抵扣赔偿金单量")
    private Integer bondDeductDamageCount = 0;

    @ApiModelProperty("未到期应收:未退还保证金")
    private BigDecimal overZeroNotBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("未到期应收:未退还保证金单量")
    private Integer overZeroNotBackBondCount = 0;

    @ApiModelProperty("未到期应收:剩余总租金")
    private BigDecimal overZeroSurplusRentAmt = BigDecimal.ZERO;

    @ApiModelProperty("未到期应收:剩余总租金单量")
    private Integer overZeroSurplusRentCount = 0;

    @ApiModelProperty("未到期应收:到期买断金")
    private BigDecimal overZeroBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("未到期应收:到期买断金单量")
    private Integer overZeroBuyoutCount = 0;

    @ApiModelProperty("逾期7日内应收:未退还保证金")
    private BigDecimal overFirstNotBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期7日内应收:未退还保证金单量")
    private Integer overFirstNotBackBondCount = 0;

    @ApiModelProperty("逾期7日内应收:剩余总租金")
    private BigDecimal overFirstSurplusRentAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期7日内应收:剩余总租金单量")
    private Integer overFirstSurplusRentCount = 0;

    @ApiModelProperty("逾期7日内应收:到期买断金")
    private BigDecimal overFirstBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期7日内应收:到期买断金单量")
    private Integer overFirstBuyoutCount = 0;

    @ApiModelProperty("逾期7日内应收:逾期滞纳金")
    private BigDecimal overFirstOverDueAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期7日内应收:逾期滞纳金单量")
    private Integer overFirstOverDueCount = 0;

    @ApiModelProperty("逾期8-31日内应收:未退还保证金")
    private BigDecimal overSecondNotBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期8-31日内应收:未退还保证金单量")
    private Integer overSecondNotBackBondCount = 0;

    @ApiModelProperty("逾期8-31日内应收:剩余总租金")
    private BigDecimal overSecondSurplusRentAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期8-31日内应收:剩余总租金单量")
    private Integer overSecondSurplusRentCount = 0;

    @ApiModelProperty("逾期8-31日内应收:到期买断金")
    private BigDecimal overSecondBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期8-31日内应收:到期买断金单量")
    private Integer overSecondBuyoutCount = 0;

    @ApiModelProperty("逾期8-31日内应收:逾期滞纳金")
    private BigDecimal overSecondOverDueAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期8-31日内应收:逾期滞纳金单量")
    private Integer overSecondOverDueCount = 0;

    @ApiModelProperty("逾期32日以上应收:未退还保证金")
    private BigDecimal overThirdNotBackBondAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期32日以上应收:未退还保证金单量")
    private Integer overThirdNotBackBondCount = 0;

    @ApiModelProperty("逾期32日以上应收:剩余总租金")
    private BigDecimal overThirdSurplusRentAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期32日以上应收:剩余总租金单量")
    private Integer overThirdSurplusRentCount = 0;

    @ApiModelProperty("逾期32日以上应收:到期买断金")
    private BigDecimal overThirdBuyoutAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期32日以上应收:到期买断金单量")
    private Integer overThirdBuyoutCount = 0;

    @ApiModelProperty("逾期32日以上应收:逾期滞纳金")
    private BigDecimal overThirdOverDueAmt = BigDecimal.ZERO;

    @ApiModelProperty("逾期32日以上应收:逾期滞纳金单量")
    private Integer overThirdOverDueCount = 0;

    @ApiModelProperty("已收售前优惠")
    private BigDecimal baseActBeforeRentDiscountAmt = BigDecimal.ZERO;

    @ApiModelProperty("已收售前优惠单量")
    private Integer baseActBeforeRentDiscountCount = 0;

    @ApiModelProperty("3+N结清优惠")
    private BigDecimal endDiscountAmt = BigDecimal.ZERO;

    @ApiModelProperty("3+N结清优惠单量")
    private Integer endDiscountCount = 0;

    @ApiModelProperty("3+N还机未收")
    private BigDecimal endReturnNotRepayAmt = BigDecimal.ZERO;

    @ApiModelProperty("3+N还机未收单量")
    private Integer endReturnNotRepayCount = 0;

    @ApiModelProperty("6+6结清优惠")
    private BigDecimal endDiscountFor6Amt = BigDecimal.ZERO;

    @ApiModelProperty("6+6结清优惠单量")
    private Integer endDiscountFor6Count = 0;

    @ApiModelProperty("6+6还机未收")
    private BigDecimal endReturnNotRepayFor6Amt = BigDecimal.ZERO;

    @ApiModelProperty("6+6还机未收单量")
    private Integer endReturnNotRepayFor6Count = 0;

    @ApiModelProperty("实收还机赔偿金")
    private BigDecimal endReturnDamageAmt = BigDecimal.ZERO;

    @ApiModelProperty("实收还机赔偿金单量")
    private Integer endReturnDamageCount = 0;

    @ApiModelProperty("售前优惠：预计收入")
    private BigDecimal expectedIncomeAmt = BigDecimal.ZERO;

    @ApiModelProperty("售前优惠：预计收入单量")
    private Integer expectedIncomeCount = 0;


}