package qnvip.data.overview.domain.order;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_screen_risk")
public class OperateScreenRiskDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 加购订单量
     */
    private Long orderCount= 0L;

    /**
     * 加购订单通审量
     */
    private Long orderPassCount= 0L;

    /**
     * 通审支付量
     */
    private Long passPayCount= 0L;


    /**
     * 加购（付款&未取消&签收）量
     */
    private Long signCount= 0L;

    /**
     * 理赔订单量
     */
    private Long claimsCount= 0L;

    /**
     * 理赔发生金
     */
    private BigDecimal claimsAmt;


    /**
     * 碎屏保障NMV
     */
    private BigDecimal nmv;
}
