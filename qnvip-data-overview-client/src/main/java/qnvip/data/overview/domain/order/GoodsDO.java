package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/9/30 11:04 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_goods")
public class GoodsDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 主分类
     */
    private String mainCategory;
    /**
     * 主分类名称
     */
    private String mainCategoryName;
    /**
     * 所属二级分类id集合
     */
    private String firstCategoryIds;
    /**
     * 所属二级分类名称
     */
    private String firstCategoryNames;
    /**
     * 所属三级分类id集合
     */
    private String secondCategoryIds;
    /**
     * 所属三级分类名称集合
     */
    private String secondCategoryNames;
    /**
     * 商品名称
     */
    private String goodsName;
    /**
     * 商品规格
     */
    private String model;
    /**
     * 租期
     */
    private Long totalTerm;
    /**
     * 商品总价值
     */
    private BigDecimal totalWorth;
    /**
     * 进货价
     */
    private BigDecimal totalSupplyPrice;
    /**
     * 采购价
     */
    private BigDecimal totalBuyPrice;
    /**
     * 支付单量
     */
    private Long payCount;
    /**
     * 下单人数
     */
    private Long orderCount;
    /**
     * 风控通过人数
     */
    private Long approvedCount;
    /**
     * 签收单数
     */
    private Long signCount;
}