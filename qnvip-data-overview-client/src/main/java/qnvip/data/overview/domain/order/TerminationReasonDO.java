package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/9/30 9:44 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_termination_reason")
public class TerminationReasonDO extends BaseDO {
    /**
     * 关闭日期
     */
    private LocalDateTime terminationDay;
    /**
     * 来源平台
     */
    private Integer miniType;
    /**
     * 关闭原因
     */
    private String reason;
    /**
     * 统计次数
     */
    private Long totalCount;
}