package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_distribution_count1")
public class RiskDistributionCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private String countMonth;

    private Integer miniType;

    private Integer overdueDay;

    private Integer maxTerm;

    /**
     * 平台
     */
    private String platform;

    private Integer financeType;


    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;
    /**
     * 风控等级
     */
    private String shopName;
    /**
     * 审核方式
     */
    private String auditType;

    /**
     * 审核方式名字
     */

    private String auditTypeName;

    /**
     * 审核方式
     */
    @TableField(exist = false)
    private String auditTypeDesc;

    /**
     * 保证金比
     */
    private String bondRateInterval;

    /**
     * 风控等级
     */
    private String riskLevel;

    /**
     * 风控策略
     */
    private String riskStrategy;

    // /**
    //  * 评分
    //  */
    // private Integer score;

    /**
     * 是否免押 0：否 1：是
     */
    private Integer isMortgage;

    /**
     * 是否租物订单 0：否 1：是
     */
    private Integer isRent;

    /**
     * 订单类型  2：优品自动拒绝 3：优品人工拒绝
     */
    private Integer refuseType;

    /**
     * 订单类型 1：商户自主线下引流
     */
    private Integer drainageType;


    private BigDecimal buyoutAmt;

    private BigDecimal beforeDiscount;

    private BigDecimal rentTotal;
    private BigDecimal renewTotalRent;
    // 计算12期以内的总租金
    private BigDecimal rentTotalInner;
    /*
     *  新增逻辑
     * */
    private BigDecimal discountTotal;
    private BigDecimal surplusBondAmtTotal;
    private BigDecimal bondRestFundAmountTotal;
    private BigDecimal diffPricingDiscountAmtTotal;
    private BigDecimal couponDiscountAmtTotal;

    private BigDecimal totalOrderCnt;

    private BigDecimal bondAmt1;

    private BigDecimal buyoutAmt1;

    private BigDecimal parentBeforeDiscount1;

    private BigDecimal parentDiscountReturnAmt1;

    private BigDecimal beforeDiscount1;

    private BigDecimal discountReturnAmt1;

    private BigDecimal bondAmt2;

    private BigDecimal buyoutAmt2;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt1;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount1;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt1;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt1;
    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt2;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount2;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt2;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt2;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt3;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount3;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt3;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt3;
    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt4;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount4;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt4;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt4;
    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt5;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount5;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt5;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt5;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt6;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount6;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt6;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt6;
    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt7;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount7;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt7;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt7;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt8;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount8;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt8;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt8;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt9;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount9;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt9;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt9;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt10;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount10;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt10;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt10;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt11;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount11;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt11;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt11;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt12;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount12;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt12;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt12;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt13;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount13;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt13;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt13;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt14;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount14;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt14;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt14;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt15;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount15;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt15;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt15;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt16;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount16;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt16;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt16;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt17;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount17;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt17;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt17;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt18;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount18;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt18;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt18;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt19;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount19;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt19;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt19;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt20;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount20;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt20;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt20;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt21;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount21;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt21;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt21;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt22;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount22;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt22;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt22;

    /*新增逻辑*/
    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt23;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount23;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt23;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt23;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt24;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount24;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt24;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt24;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt25;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount25;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt25;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt25;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt26;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount26;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt26;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt26;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt27;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount27;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt27;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt27;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt28;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount28;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt28;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt28;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt29;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount29;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt29;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt29;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt30;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount30;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt30;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt30;

    private BigDecimal parentBeforeDiscount2;

    private BigDecimal parentDiscountReturnAmt2;

    private BigDecimal beforeDiscount2;

    private BigDecimal discountReturnAmt2;

    private BigDecimal bondAmt3;

    private BigDecimal buyoutAmt3;

    private BigDecimal parentBeforeDiscount3;

    private BigDecimal parentDiscountReturnAmt3;

    private BigDecimal beforeDiscount3;

    private BigDecimal discountReturnAmt3;

    private BigDecimal bondAmt4;

    private BigDecimal buyoutAmt4;

    private BigDecimal parentBeforeDiscount4;

    private BigDecimal parentDiscountReturnAmt4;

    private BigDecimal beforeDiscount4;

    private BigDecimal discountReturnAmt4;

    private BigDecimal bondAmt5;

    private BigDecimal buyoutAmt5;

    private BigDecimal parentBeforeDiscount5;

    private BigDecimal parentDiscountReturnAmt5;

    private BigDecimal beforeDiscount5;

    private BigDecimal discountReturnAmt5;

    private BigDecimal bondAmt6;

    private BigDecimal buyoutAmt6;

    private BigDecimal parentBeforeDiscount6;

    private BigDecimal parentDiscountReturnAmt6;

    private BigDecimal beforeDiscount6;

    private BigDecimal discountReturnAmt6;

    private BigDecimal bondAmt7;

    private BigDecimal buyoutAmt7;

    private BigDecimal parentBeforeDiscount7;

    private BigDecimal parentDiscountReturnAmt7;

    private BigDecimal beforeDiscount7;

    private BigDecimal discountReturnAmt7;

    private BigDecimal bondAmt8;

    private BigDecimal buyoutAmt8;

    private BigDecimal parentBeforeDiscount8;

    private BigDecimal parentDiscountReturnAmt8;

    private BigDecimal beforeDiscount8;

    private BigDecimal discountReturnAmt8;

    private BigDecimal bondAmt9;

    private BigDecimal buyoutAmt9;

    private BigDecimal parentBeforeDiscount9;

    private BigDecimal parentDiscountReturnAmt9;

    private BigDecimal beforeDiscount9;

    private BigDecimal discountReturnAmt9;

    private BigDecimal bondAmt10;

    private BigDecimal buyoutAmt10;

    private BigDecimal parentBeforeDiscount10;

    private BigDecimal parentDiscountReturnAmt10;

    private BigDecimal beforeDiscount10;

    private BigDecimal discountReturnAmt10;

    private BigDecimal bondAmt11;

    private BigDecimal buyoutAmt11;

    private BigDecimal parentBeforeDiscount11;

    private BigDecimal parentDiscountReturnAmt11;

    private BigDecimal beforeDiscount11;

    private BigDecimal discountReturnAmt11;

    private BigDecimal bondAmt12;

    private BigDecimal buyoutAmt12;

    private BigDecimal parentBeforeDiscount12;

    private BigDecimal parentDiscountReturnAmt12;

    private BigDecimal beforeDiscount12;

    private BigDecimal discountReturnAmt12;

    private BigDecimal bondAmt13;

    private BigDecimal buyoutAmt13;

    private BigDecimal parentBeforeDiscount13;

    private BigDecimal parentDiscountReturnAmt13;

    private BigDecimal beforeDiscount13;

    private BigDecimal discountReturnAmt13;

    private BigDecimal bondAmt14;

    private BigDecimal buyoutAmt14;

    private BigDecimal parentBeforeDiscount14;

    private BigDecimal parentDiscountReturnAmt14;

    private BigDecimal beforeDiscount14;

    private BigDecimal discountReturnAmt14;

    private BigDecimal bondAmt15;

    private BigDecimal buyoutAmt15;

    private BigDecimal parentBeforeDiscount15;

    private BigDecimal parentDiscountReturnAmt15;

    private BigDecimal beforeDiscount15;

    private BigDecimal discountReturnAmt15;

    private BigDecimal bondAmt16;

    private BigDecimal buyoutAmt16;

    private BigDecimal parentBeforeDiscount16;

    private BigDecimal parentDiscountReturnAmt16;

    private BigDecimal beforeDiscount16;

    private BigDecimal discountReturnAmt16;

    private BigDecimal bondAmt17;

    private BigDecimal buyoutAmt17;

    private BigDecimal parentBeforeDiscount17;

    private BigDecimal parentDiscountReturnAmt17;

    private BigDecimal beforeDiscount17;

    private BigDecimal discountReturnAmt17;

    private BigDecimal bondAmt18;

    private BigDecimal buyoutAmt18;

    private BigDecimal parentBeforeDiscount18;

    private BigDecimal parentDiscountReturnAmt18;

    private BigDecimal beforeDiscount18;

    private BigDecimal discountReturnAmt18;

    private BigDecimal bondAmt19;

    private BigDecimal buyoutAmt19;

    private BigDecimal parentBeforeDiscount19;

    private BigDecimal parentDiscountReturnAmt19;

    private BigDecimal beforeDiscount19;

    private BigDecimal discountReturnAmt19;

    private BigDecimal bondAmt20;

    private BigDecimal buyoutAmt20;

    private BigDecimal parentBeforeDiscount20;

    private BigDecimal parentDiscountReturnAmt20;

    private BigDecimal beforeDiscount20;

    private BigDecimal discountReturnAmt20;

    private BigDecimal bondAmt21;

    private BigDecimal buyoutAmt21;

    private BigDecimal parentBeforeDiscount21;

    private BigDecimal parentDiscountReturnAmt21;

    private BigDecimal beforeDiscount21;

    private BigDecimal discountReturnAmt21;

    private BigDecimal bondAmt22;

    private BigDecimal buyoutAmt22;

    private BigDecimal parentBeforeDiscount22;

    private BigDecimal parentDiscountReturnAmt22;

    private BigDecimal beforeDiscount22;

    private BigDecimal discountReturnAmt22;

    private BigDecimal bondAmt23;

    private BigDecimal buyoutAmt23;

    private BigDecimal parentBeforeDiscount23;

    private BigDecimal parentDiscountReturnAmt23;

    private BigDecimal beforeDiscount23;

    private BigDecimal discountReturnAmt23;

    private BigDecimal bondAmt24;

    private BigDecimal buyoutAmt24;

    private BigDecimal parentBeforeDiscount24;

    private BigDecimal parentDiscountReturnAmt24;

    private BigDecimal beforeDiscount24;

    private BigDecimal discountReturnAmt24;

    private BigDecimal bondAmt25;

    private BigDecimal buyoutAmt25;

    private BigDecimal parentBeforeDiscount25;

    private BigDecimal parentDiscountReturnAmt25;

    private BigDecimal beforeDiscount25;

    private BigDecimal discountReturnAmt25;

    private BigDecimal bondAmt26;

    private BigDecimal buyoutAmt26;

    private BigDecimal parentBeforeDiscount26;

    private BigDecimal parentDiscountReturnAmt26;

    private BigDecimal beforeDiscount26;

    private BigDecimal discountReturnAmt26;

    private BigDecimal bondAmt27;

    private BigDecimal buyoutAmt27;

    private BigDecimal parentBeforeDiscount27;

    private BigDecimal parentDiscountReturnAmt27;

    private BigDecimal beforeDiscount27;

    private BigDecimal discountReturnAmt27;

    private BigDecimal bondAmt28;

    private BigDecimal buyoutAmt28;

    private BigDecimal parentBeforeDiscount28;

    private BigDecimal parentDiscountReturnAmt28;

    private BigDecimal beforeDiscount28;

    private BigDecimal discountReturnAmt28;

    private BigDecimal bondAmt29;

    private BigDecimal buyoutAmt29;

    private BigDecimal parentBeforeDiscount29;

    private BigDecimal parentDiscountReturnAmt29;

    private BigDecimal beforeDiscount29;

    private BigDecimal discountReturnAmt29;

    private BigDecimal bondAmt30;

    private BigDecimal buyoutAmt30;

    private BigDecimal parentBeforeDiscount30;

    private BigDecimal parentDiscountReturnAmt30;

    private BigDecimal beforeDiscount30;

    private BigDecimal discountReturnAmt30;

    private BigDecimal notPayTerm1;

    private BigDecimal overdueFine1;

    private BigDecimal renewAmt1;

    private BigDecimal countTerm1;

    private BigDecimal notPayTerm2;

    private BigDecimal overdueFine2;

    private BigDecimal renewAmt2;

    private BigDecimal countTerm2;

    private BigDecimal notPayTerm3;

    private BigDecimal overdueFine3;

    private BigDecimal renewAmt3;

    private BigDecimal countTerm3;

    private BigDecimal notPayTerm4;

    private BigDecimal overdueFine4;

    private BigDecimal renewAmt4;

    private BigDecimal countTerm4;

    private BigDecimal notPayTerm5;

    private BigDecimal overdueFine5;

    private BigDecimal renewAmt5;

    private BigDecimal countTerm5;

    private BigDecimal notPayTerm6;

    private BigDecimal overdueFine6;

    private BigDecimal renewAmt6;

    private BigDecimal countTerm6;

    private BigDecimal notPayTerm7;

    private BigDecimal overdueFine7;

    private BigDecimal renewAmt7;

    private BigDecimal countTerm7;

    private BigDecimal notPayTerm8;

    private BigDecimal overdueFine8;

    private BigDecimal renewAmt8;

    private BigDecimal countTerm8;

    private BigDecimal notPayTerm9;

    private BigDecimal overdueFine9;

    private BigDecimal renewAmt9;

    private BigDecimal countTerm9;

    private BigDecimal notPayTerm10;

    private BigDecimal overdueFine10;

    private BigDecimal renewAmt10;

    private BigDecimal countTerm10;

    private BigDecimal notPayTerm11;

    private BigDecimal overdueFine11;

    private BigDecimal renewAmt11;

    private BigDecimal countTerm11;

    private BigDecimal notPayTerm12;

    private BigDecimal overdueFine12;

    private BigDecimal renewAmt12;

    private BigDecimal countTerm12;

    private BigDecimal notPayTerm13;

    private BigDecimal overdueFine13;

    private BigDecimal renewAmt13;

    private BigDecimal countTerm13;

    private BigDecimal notPayTerm14;

    private BigDecimal overdueFine14;

    private BigDecimal renewAmt14;

    private BigDecimal countTerm14;

    private BigDecimal notPayTerm15;

    private BigDecimal overdueFine15;

    private BigDecimal renewAmt15;

    private BigDecimal countTerm15;

    private BigDecimal notPayTerm16;

    private BigDecimal overdueFine16;

    private BigDecimal renewAmt16;

    private BigDecimal countTerm16;

    private BigDecimal notPayTerm17;

    private BigDecimal overdueFine17;

    private BigDecimal renewAmt17;

    private BigDecimal countTerm17;

    private BigDecimal notPayTerm18;

    private BigDecimal overdueFine18;

    private BigDecimal renewAmt18;

    private BigDecimal countTerm18;

    private BigDecimal notPayTerm19;

    private BigDecimal overdueFine19;

    private BigDecimal renewAmt19;

    private BigDecimal countTerm19;

    private BigDecimal notPayTerm20;

    private BigDecimal overdueFine20;

    private BigDecimal renewAmt20;

    private BigDecimal countTerm20;

    private BigDecimal notPayTerm21;

    private BigDecimal overdueFine21;

    private BigDecimal renewAmt21;

    private BigDecimal countTerm21;

    private BigDecimal notPayTerm22;

    private BigDecimal overdueFine22;

    private BigDecimal renewAmt22;

    private BigDecimal countTerm22;

    private BigDecimal notPayTerm23;

    private BigDecimal overdueFine23;

    private BigDecimal renewAmt23;

    private BigDecimal countTerm23;

    private BigDecimal notPayTerm24;

    private BigDecimal overdueFine24;

    private BigDecimal renewAmt24;

    private BigDecimal countTerm24;

    private BigDecimal notPayTerm25;

    private BigDecimal overdueFine25;

    private BigDecimal renewAmt25;

    private BigDecimal countTerm25;

    private BigDecimal notPayTerm26;

    private BigDecimal overdueFine26;

    private BigDecimal renewAmt26;

    private BigDecimal countTerm26;

    private BigDecimal notPayTerm27;

    private BigDecimal overdueFine27;

    private BigDecimal renewAmt27;

    private BigDecimal countTerm27;

    private BigDecimal notPayTerm28;

    private BigDecimal overdueFine28;

    private BigDecimal renewAmt28;

    private BigDecimal countTerm28;

    private BigDecimal notPayTerm29;

    private BigDecimal overdueFine29;

    private BigDecimal renewAmt29;

    private BigDecimal countTerm29;

    private BigDecimal notPayTerm30;

    private BigDecimal overdueFine30;

    private BigDecimal renewAmt30;

    private BigDecimal countTerm30;

    /**
     * 审核人员id
     */
    private Long artificialityId;

    /**
     * 芝麻分等级
     */
    private String zmfLevel;
    // 12免押类型
    private String depositFreeType;
    //13 流量类型
    private String trafficType;
    // 14金融方案
    private String financialSolutions;
    //15 应用
    private String applicationName;
    //16 新旧程度
    private String equipmentState;
    //17是否监管
    private String supervisedMachine;
    //18机型
    private String machineType;

}
