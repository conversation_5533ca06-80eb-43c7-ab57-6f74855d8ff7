package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_access_flow_structure")
public class AccessFlowStructureDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;


    /**
     * 访问渠道
     */
    private Integer miniType;

    /**
     * 访客数
     */
    private Long uv = 0L;


    /**
     * 访问量
     */
    private Long pv = 0L;

    /**
     * 新增访问用户数
     */
    private Long uvNew = 0L;

    /**
     * 复访用户数
     */
    private Long uvOld = 0L;

    /**
     * 交易用户数
     */
    private Long uvTrading = 0L;

    /**
     * 交易笔数
     */
    private Long pvTrading = 0L;
}
