package qnvip.data.overview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_sign")
public class WholeLinkSignDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 发货量
     */
    private Long sendUv;

    /**
     * 签收量
     */
    private Long signUv;

    /**
     * 48h未发量
     */
    private Long unSendUv48h;

    /**
     * 8日待发货
     */
    private Long unSendUv8days;

    /**
     * top渠道
     */
    private Long signTop5ChannelUv;

    /**
     * top5场景
     */
    private Long signTop5SceneUv;

    /**
     * 支付宝发货量
     */
    private Long aliSendUv;

    /**
     * 微信发货量
     */
    private Long wechatSendUv;

    /**
     * 其他发货量
     */
    private Long otherSendUv;

    /**
     * 已发货关闭
     */
    private Long sendCloseUv;


}
