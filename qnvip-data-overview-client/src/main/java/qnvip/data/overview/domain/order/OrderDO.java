package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/9/29 1:22 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_order")
public class OrderDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 来源平台
     */
    private Integer miniType;
    /**
     * 租金GMV
     */
    private BigDecimal rentTotalGmv;
    /**
     * 订单数
     */
    private Long orderCount;
    /**
     * 下单人数
     */
    private Long orderUvCount;
    /**
     * 支付单数
     */
    private Long payCount;
    /**
     * 保证金支付金额
     */
    private BigDecimal marginTotal;
    /**
     * 碎屏险支付金额
     */
    private BigDecimal insuranceAmt;
    /**
     * 配件支付金额
     */
    private BigDecimal componentPrice;
    /**
     * 发货数量
     */
    private Long deliveryCount;
    /**
     * 订单关闭数量
     */
    private Long terminationCount;
    /**
     * 风控通过人数
     */
    private Long riskPassCount;
    /**
     * 风控通过订单量
     */
    private Long riskPassOrderCount;
    /**
     * 签收单数
     */
    private Long signCount;


}