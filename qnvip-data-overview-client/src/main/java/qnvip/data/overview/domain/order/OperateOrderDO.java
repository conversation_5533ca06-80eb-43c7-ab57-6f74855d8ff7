package qnvip.data.overview.domain.order;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_order")
public class OperateOrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 应收总租金
     */
    private BigDecimal rentTotal;

    /**
     * 应收买断金
     */
    private BigDecimal buyoutAmt;

    /**
     * 实收保证金
     */
    private BigDecimal actBondAmt;

    /**
     * 实收碎屏保障金
     */
    private BigDecimal screenRisks;

    /**
     * 实收配件金
     */
    private BigDecimal accessories;

    /**
     * 实收历史回款
     */
    private BigDecimal payAmt;

    /**
     * 发生营销费用
     */
    private BigDecimal discountAmt;

    /**
     * 实收买断金
     */
    private BigDecimal actBuyoutAmt;


}
