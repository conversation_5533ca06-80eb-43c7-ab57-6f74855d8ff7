package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_advertising_view")
public class TrackAdvertisingViewDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 广告类型，1:banner广告，2：三合一广告
     */
    private Integer advertisingType;

    /**
     * 广告id
     */
    private String advertisingId;

    /**
     * 广告名称
     */
    private String name;
}
