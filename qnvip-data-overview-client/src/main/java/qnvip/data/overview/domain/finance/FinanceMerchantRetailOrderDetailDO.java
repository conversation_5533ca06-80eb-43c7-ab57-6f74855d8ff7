package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_finance_merchant_retail_order_detail")
public class FinanceMerchantRetailOrderDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 发货日
     */
    private LocalDateTime sendTime;

    /**
     * 签收日
     */
    private LocalDateTime signTime;

    /**
     * 来源
     */
    private Integer miniType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 商品分类
     */
    private String firstCategoryName;

    /**
     * 商品子分类
     */
    private String secondCategoryName;

    /**
     * 商品名称
     */
    private String itemName;

    /**
     * 规格
     */
    private String itemSpec;

    /**
     * 真实采购价
     */
    private BigDecimal realPurchasePrice;

    /**
     * 总租金
     */
    private BigDecimal totalInstallmentAmt;

    /**
     * 毛利
     */
    private BigDecimal grossProfit;

    /**
     * 数量
     */
    private Integer number;


}
