package qnvip.data.overview.domain.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_goods_core")
public class OperateGoodsCoreDO extends BaseDO {

    private static final long serialVersionUID = 1L;


    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 上架SKU
     */
    private Long shelvesSku= 0L;

    /**
     * 更新SKU
     */
    private Long updateSku= 0L;

    /**
     * 在售SKU
     */
    private Long onSaleSku= 0L;

    /**
     * 下架SKU
     */
    private Long soldOutSku= 0L;

    /**
     * 动销SKU
     */
    private Long moveOffSku= 0L;

    /**
     * 高质量动销SKU
     */
    private Long qualityMoveOffSku= 0L;

    /**
     * 上架配件数
     */
    private Long shelvesComponent= 0L;

    /**
     * 更新配件数
     */
    private Long updateComponent= 0L;

    /**
     * 在售配件数
     */
    private Long onSaleComponent= 0L;

    /**
     * 下架配件数
     */
    private Long soldOutComponent= 0L;

    /**
     * 动销配件数
     */
    private Long moveOffComponent= 0L;


    /**
     * 高动销配件数
     */
    private Long qualityMoveOffComponent= 0L;



}
