package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * 在架商品数量
 * <AUTHOR>
 * @Date 2021/10/21 9:07 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_up_goods")
public class UpGoodsDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 在架商品数量
     */
    private Long upCount;
}