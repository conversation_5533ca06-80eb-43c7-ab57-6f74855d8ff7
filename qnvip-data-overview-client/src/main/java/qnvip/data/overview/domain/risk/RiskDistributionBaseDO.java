package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_distribution_base2")
public class RiskDistributionBaseDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 订单ID
     */
    private Long orderId;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * mini_type
     */
    private Integer miniType;

    /**
     * 应还款日
     */
    private LocalDateTime repayDate;

    /**
     * 实际还款日
     */
    private LocalDateTime realRepayTime;

    /**
     * 平台
     */
    private String platform;

    /**
     * 金融方案
     */
    private Integer financeType;

    /**
     * 还款期数
     */
    private Integer term;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;

    /**
     * 逾期日期
     */
    private Integer overdueDay;

    /**
     * 风控等级
     */
    private String riskLevel;

    /**
     * 风控等级
     */
    private String shopName;

    /**
     * 统计日期
     */
    private String riskStrategy;

    /**
     * 审核方式
     */
    private Integer auditType;

    /**
     * 审核方式名字
     */

    private String auditTypeName;

    /**
     * 是否强转
     */
    private Integer forcedConversion;

    /**
     * 折扣返还金额
     */
    private BigDecimal discountReturnAmt;

    /**
     * 总租金
     */
    private BigDecimal rentTotal;

    /**
     * 用户使用的优惠券金额
     */
    private BigDecimal discountAmt;

    /**
     * 优惠定价模型 0:普通 1:先优惠后差异化
     */
    private Integer discountPricingMode;

    /**
     * 子订单编号
     */
    private String childNo;

    /**
     * 子订单id
     */
    private Long childOrderId;

    /**
     * 买断金
     */
    private BigDecimal buyoutAmt;

    /**
     * 滞纳金
     */
    private BigDecimal overdueFine;

    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    /**
     * 售前优惠
     */
    private BigDecimal beforeDiscount;

    /**
     * 业务总金额
     */
    private BigDecimal totalDiscount;

    /**
     * 保证金比
     */
    private Double bondRate;

    /**
     * 是否逾期
     */
    private Integer isOverdue;

    /**
     * 实际还款金
     */
    private BigDecimal realCapital;

    /**
     * 还款金
     */
    private BigDecimal capital;

    /**
     * 是否支付
     */
    private Integer repayStatus;

    /**
     * 是否逾期
     */
    private Integer overdue;

    /**
     * 是否还款
     */
    private Integer realRepayTimeStatus;

    /**
     * 续租总租金
     */
    private BigDecimal renewTotalRent;

    /**
     * 最大还款期数
     */
    private Integer maxTerm;

    /**
     * 续租期数
     */
    private Integer renewTerm;

    /**
     * 续租状态
     */
    private Integer renewStatus;

    /**
     * 续租时间
     */
    private Integer renewDay;

    /**
     * 拒绝方式
     */
    private Integer refuseType;

    /**
     * 分流方式
     */
    private Integer drainageType;

    /**
     * 是否面押
     */
    private Integer isMortgage;

    /**
     * 是否租物订单
     */
    private Integer isRent;

    /**
     * 芝麻分等级
     */
    private String zmfLevel;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt;
    /**
     * 应还买断金
     */
    private BigDecimal buyOutCapital;
    /**
     * 实还买断金
     */
    private BigDecimal buyOutRealRepayCapital;

    private String depositFreeType;
    private String trafficType;
    private String financialSolutions;
    private String applicationName;
    private String equipmentState;
    private String supervisedMachine;
    private String machineType;
    private String renewTime;
    private String stageNo;

}
