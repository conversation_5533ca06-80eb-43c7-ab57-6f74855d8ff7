package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_distribution_order_overdue1")
public class RiskDistributionOrderOverdueDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private LocalDateTime countDay;

    /**
     * 订单id
     */
    private Long orderId;

    private Integer miniType;

    /**
     * 平台
     */
    private String platform;

    private Integer financeType;
    /**
     * 风控等级
     */
    private String shopName;
    /**
     * 审核方式
     */
    private Integer auditType;

    /**
     * 审核方式名字
     */

    private String auditTypeName;

    /**
     * 风控等级
     */
    private String riskLevel;


    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;
    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 协议总租金
     */
    private BigDecimal rentTotal;

    /**
     * 续租总租金
     */
    private BigDecimal renewTotalRent;

    /**
     * 使用的优惠券金额
     */
    private BigDecimal discountAmt;

    /**
     * 差异化优惠
     */
    private Integer discountPricingMode;

    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    /**
     * 买断金
     */
    private BigDecimal buyoutAmt;

    /**
     * 续租方式 1：自动续租 0：手动续租 2：未续租
     */
//    private Integer renewWay;

    /**
     * 父订单最大期数
     */
    private Integer pMaxTerm;

    /**
     * 最大期数
     */
    private Integer maxTerm;

    /**
     * 父订单折扣返还金额
     */
    private BigDecimal pDiscountReturnAmt;

    /**
     * 父订单未使用售前优惠金额
     */
    private BigDecimal pBeforeDiscount;

    /**
     * 折扣返还金额
     */
    private BigDecimal discountReturnAmt;

    /**
     * 未使用售前优惠金额
     */
    private BigDecimal beforeDiscount;

    /**
     * 抵扣完剩余保证金
     */
    private BigDecimal surplusBondAmt;
    /**
     * 剩余可用预授权实付押金
     */
    private BigDecimal bondRestFundAmount;
    /**
     * 差异化优惠金额
     */
    private BigDecimal diffPricingDiscountAmt;
    /**
     * 优惠券金额
     */
    private BigDecimal couponDiscountAmt;

    /**
     * 保证金比
     */
    private String bondRate;

    /**
     * 逾期天数
     */
    private Integer overdueDay;

    /**
     * 是否免押 0：否 1：是
     */
    private Integer isMortgage;

    /**
     * 是否租物订单 0：否 1：是
     */
    private Integer isRent;

    /**
     * 续租状态1:租后待买断
     */
    @TableField(exist = false)
    private Integer renewStatus;

    /**
     * 续租时间间隔
     */
    @TableField(exist = false)
    private Integer renewDay;

    /**
     * 可续租期数
     */
    @TableField(exist = false)
    private Integer renewTerm;
    /**
     * 拒绝类型 0：其他 1：优品自动拒绝 2：优品人工拒绝
     */
    private Integer refuseType;

    /**
     * 订单类型 1：商户自主线下引流 0：其他
     */
    private Integer drainageType;

    /**
     * MOB1
     */
    private BigDecimal term1;

    /**
     * MOB2
     */
    private BigDecimal term2;

    /**
     * MOB3
     */
    private BigDecimal term3;

    /**
     * MOB4
     */
    private BigDecimal term4;

    /**
     * MOB5
     */
    private BigDecimal term5;

    /**
     * MOB6
     */
    private BigDecimal term6;

    /**
     * MOB7
     */
    private BigDecimal term7;

    /**
     * MOB8
     */
    private BigDecimal term8;

    /**
     * MOB9
     */
    private BigDecimal term9;

    /**
     * MOB10
     */
    private BigDecimal term10;

    /**
     * MOB11
     */
    private BigDecimal term11;

    /**
     * MOB12
     */
    private BigDecimal term12;

    /**
     * MOB13
     */
    private BigDecimal term13;

    /**
     * MOB14
     */
    private BigDecimal term14;

    /**
     * MOB15
     */
    private BigDecimal term15;

    /**
     * MOB16
     */
    private BigDecimal term16;

    /**
     * MOB17
     */
    private BigDecimal term17;

    /**
     * MOB18
     */
    private BigDecimal term18;

    /**
     * MOB19
     */
    private BigDecimal term19;

    /**
     * MOB20
     */
    private BigDecimal term20;

    /**
     * MOB21
     */
    private BigDecimal term21;

    /**
     * MOB22
     */
    private BigDecimal term22;

    /**
     * MOB23
     */
    private BigDecimal term23;

    /**
     * MOB24
     */
    private BigDecimal term24;

    /**
     * MOB25
     */
    private BigDecimal term25;

    /**
     * MOB26
     */
    private BigDecimal term26;

    /**
     * MOB27
     */
    private BigDecimal term27;

    /**
     * MOB28
     */
    private BigDecimal term28;

    /**
     * MOB29
     */
    private BigDecimal term29;

    /**
     * MOB30
     */
    private BigDecimal term30;

    /**
     * MOB1 续租总租金
     */
    private BigDecimal renewAmt1;

    /**
     * MOB2 续租总租金
     */
    private BigDecimal renewAmt2;

    /**
     * MOB3 续租总租金
     */
    private BigDecimal renewAmt3;

    /**
     * MOB4 续租总租金
     */
    private BigDecimal renewAmt4;

    /**
     * MOB5 续租总租金
     */
    private BigDecimal renewAmt5;

    /**
     * MOB6 续租总租金
     */
    private BigDecimal renewAmt6;

    /**
     * MOB7 续租总租金
     */
    private BigDecimal renewAmt7;

    /**
     * MOB8 续租总租金
     */
    private BigDecimal renewAmt8;

    /**
     * MOB9 续租总租金
     */
    private BigDecimal renewAmt9;

    /**
     * MOB10续租总租金
     */
    private BigDecimal renewAmt10;

    /**
     * MOB11续租总租金
     */
    private BigDecimal renewAmt11;

    /**
     * MOB12续租总租金
     */
    private BigDecimal renewAmt12;

    /**
     * MOB13续租总租金
     */
    private BigDecimal renewAmt13;

    /**
     * MOB14续租总租金
     */
    private BigDecimal renewAmt14;

    /**
     * MOB15续租总租金
     */
    private BigDecimal renewAmt15;

    /**
     * MOB16续租总租金
     */
    private BigDecimal renewAmt16;

    /**
     * MOB17续租总租金
     */
    private BigDecimal renewAmt17;

    /**
     * MOB18续租总租金
     */
    private BigDecimal renewAmt18;

    /**
     * MOB19续租总租金
     */
    private BigDecimal renewAmt19;

    /**
     * MOB20续租总租金
     */
    private BigDecimal renewAmt20;

    /**
     * MOB21续租总租金
     */
    private BigDecimal renewAmt21;

    /**
     * MOB22续租总租金
     */
    private BigDecimal renewAmt22;

    /**
     * MOB23续租总租金
     */
    private BigDecimal renewAmt23;

    /**
     * MOB24续租总租金
     */
    private BigDecimal renewAmt24;

    /**
     * MOB25续租总租金
     */
    private BigDecimal renewAmt25;

    /**
     * MOB26续租总租金
     */
    private BigDecimal renewAmt26;

    /**
     * MOB27续租总租金
     */
    private BigDecimal renewAmt27;

    /**
     * MOB28续租总租金
     */
    private BigDecimal renewAmt28;

    /**
     * MOB29续租总租金
     */
    private BigDecimal renewAmt29;

    /**
     * MOB30续租总租金
     */
    private BigDecimal renewAmt30;

    /**
     * 第一期是否逾期
     */
    private Integer isOverdue1;

    /**
     * 第二期是否逾期
     */
    private Integer isOverdue2;

    /**
     * 第三期是否逾期
     */
    private Integer isOverdue3;

    /**
     * 第四期是否逾期
     */
    private Integer isOverdue4;

    /**
     * 第五期是否逾期
     */
    private Integer isOverdue5;

    /**
     * 第六期是否逾期
     */
    private Integer isOverdue6;

    /**
     * 第七期是否逾期
     */
    private Integer isOverdue7;

    /**
     * 第八期是否逾期
     */
    private Integer isOverdue8;

    /**
     * 第九期是否逾期
     */
    private Integer isOverdue9;

    /**
     * 第十期是否逾期
     */
    private Integer isOverdue10;

    /**
     * 第11期是否逾期
     */
    private Integer isOverdue11;

    /**
     * 第12期是否逾期
     */
    private Integer isOverdue12;

    /**
     * 第13期是否逾期
     */
    private Integer isOverdue13;

    /**
     * 第14期是否逾期
     */
    private Integer isOverdue14;

    /**
     * 第15期是否逾期
     */
    private Integer isOverdue15;

    /**
     * 第16期是否逾期
     */
    private Integer isOverdue16;

    /**
     * 第17期是否逾期
     */
    private Integer isOverdue17;

    /**
     * 第18期是否逾期
     */
    private Integer isOverdue18;

    /**
     * 第19期是否逾期
     */
    private Integer isOverdue19;

    /**
     * 第20期是否逾期
     */
    private Integer isOverdue20;

    /**
     * 第21期是否逾期
     */
    private Integer isOverdue21;

    /**
     * 第22期是否逾期
     */
    private Integer isOverdue22;

    /**
     * 第23期是否逾期
     */
    private Integer isOverdue23;

    /**
     * 第24期是否逾期
     */
    private Integer isOverdue24;

    /**
     * 第25期是否逾期
     */
    private Integer isOverdue25;

    /**
     * 第26期是否逾期
     */
    private Integer isOverdue26;

    /**
     * 第27期是否逾期
     */
    private Integer isOverdue27;

    /**
     * 第28期是否逾期
     */
    private Integer isOverdue28;

    /**
     * 第29期是否逾期
     */
    private Integer isOverdue29;

    /**
     * 第30期是否逾期
     */
    private Integer isOverdue30;

    /**
     * 1期逾期滞纳金
     */
    private BigDecimal overdueFine1;

    /**
     * 2期逾期滞纳金
     */
    private BigDecimal overdueFine2;

    /**
     * 3期逾期滞纳金
     */
    private BigDecimal overdueFine3;

    /**
     * 4期逾期滞纳金
     */
    private BigDecimal overdueFine4;

    /**
     * 5期逾期滞纳金
     */
    private BigDecimal overdueFine5;

    /**
     * 6期逾期滞纳金
     */
    private BigDecimal overdueFine6;

    /**
     * 7期逾期滞纳金
     */
    private BigDecimal overdueFine7;

    /**
     * 8期逾期滞纳金
     */
    private BigDecimal overdueFine8;

    /**
     * 9期逾期滞纳金
     */
    private BigDecimal overdueFine9;

    /**
     * 10期逾期滞纳金
     */
    private BigDecimal overdueFine10;

    /**
     * 11期逾期滞纳金
     */
    private BigDecimal overdueFine11;

    /**
     * 12期逾期滞纳金
     */
    private BigDecimal overdueFine12;

    /**
     * 13期逾期滞纳金
     */
    private BigDecimal overdueFine13;

    /**
     * 14期逾期滞纳金
     */
    private BigDecimal overdueFine14;

    /**
     * 15期逾期滞纳金
     */
    private BigDecimal overdueFine15;

    /**
     * 16期逾期滞纳金
     */
    private BigDecimal overdueFine16;

    /**
     * 17期逾期滞纳金
     */
    private BigDecimal overdueFine17;

    /**
     * 18期逾期滞纳金
     */
    private BigDecimal overdueFine18;

    /**
     * 19期逾期滞纳金
     */
    private BigDecimal overdueFine19;

    /**
     * 20期逾期滞纳金
     */
    private BigDecimal overdueFine20;

    /**
     * 21期逾期滞纳金
     */
    private BigDecimal overdueFine21;

    /**
     * 22期逾期滞纳金
     */
    private BigDecimal overdueFine22;

    /**
     * 23期逾期滞纳金
     */
    private BigDecimal overdueFine23;

    /**
     * 24期逾期滞纳金
     */
    private BigDecimal overdueFine24;

    /**
     * 25期逾期滞纳金
     */
    private BigDecimal overdueFine25;

    /**
     * 26期逾期滞纳金
     */
    private BigDecimal overdueFine26;

    /**
     * 27期逾期滞纳金
     */
    private BigDecimal overdueFine27;

    /**
     * 28期逾期滞纳金
     */
    private BigDecimal overdueFine28;

    /**
     * 29期逾期滞纳金
     */
    private BigDecimal overdueFine29;

    /**
     * 30期逾期滞纳金
     */
    private BigDecimal overdueFine30;

    /**
     * 主订单编号
     */
    private String no;

    /**
     * 子订单编号
     */
    private String childNo;

    /**
     * 子订单id
     */
    private Long childOrderId;

    /**
     * 芝麻分等级
     */
    private String zmfLevel;
    /**
     * 业务总金额
     */
    private BigDecimal totalDiscount;
    /**
     * 应还买断金
     */
    private BigDecimal buyOutCapital;
    /**
     * 实还买断金
     */
    private BigDecimal buyOutRealRepayCapital;

    private String depositFreeType;
    private String trafficType;
    private String financialSolutions;
    private String applicationName;
    private String equipmentState;
    private String supervisedMachine;
    private String machineType;

}
