package qnvip.data.overview.domain.overdue;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_repay_performance_buyout_repay")
public class RepayPerformanceBuyoutRepayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计时区 0当日1前一日2上周同期3上月同期4上季同期
     */
    private Integer statTimeZone;

    /**
     * 统计类型 1数量 0金额
     */
    private Integer statType;

    /**
     * 买断阶段：1：买断日前15天内 2：买断日后30天
     */
    private Integer buyoutStage;

    /**
     * 应催买断金/单量
     */
    private BigDecimal collectBuyout;

    /**
     * 应催中当日买断金/单量
     */
    private BigDecimal repaiedBuyout;

    /**
     * 已还买断金/单量比例
     */
    private BigDecimal buyoutRatio;

    /**
     * 统计日
     */
    private String countDay;


}
