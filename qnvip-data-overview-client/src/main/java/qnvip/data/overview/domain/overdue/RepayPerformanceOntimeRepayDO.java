package qnvip.data.overview.domain.overdue;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_repay_performance_ontime_repay")
public class RepayPerformanceOntimeRepayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计时区 0当日1前一日2上周同期3上月同期4上季同期11-22主一期-主十二期23-31续第一期-第十二期
     */
    private Long statTimeZone;

    /**
     * 统计类型 1数量 0金额
     */
    private Long statType;

    /**
     * 应催项
     */
    private BigDecimal collectionItem;

    /**
     * 提前还-已还项
     */
    private BigDecimal aheadRepaiedItem;

    /**
     * 提前还-已还比例
     */
    private BigDecimal aheadRepaiedRatio;

    /**
     * 提前还-主动还租金项
     */
    private BigDecimal aheadActiveCapitalItem;

    /**
     * 提前还-主动还租金比例
     */
    private BigDecimal aheadActiveCapitalRatio;

    /**
     * 提前还-代扣还租金项
     */
    private BigDecimal aheadWithholdCapitalItem;

    /**
     * 提前还-代扣还租金比例
     */
    private BigDecimal aheadWithholdCapitalRatio;

    /**
     * 提前还-主动还买断金项
     */
    private BigDecimal aheadActiveBuyoutItem;

    /**
     * 提前还-主动还买断金比例
     */
    private BigDecimal aheadActiveBuyoutRatio;

    /**
     * 正常还-已还项
     */
    private BigDecimal ontimeRepaiedItem;

    /**
     * 正常还-已还比例
     */
    private BigDecimal ontimeRepaiedRatio;

    /**
     * 正常还-减免项
     */
    private BigDecimal reduceItem;

    /**
     * 正常还-减免比例
     */
    private BigDecimal reduceRatio;

    /**
     * 正常还-未还项
     */
    private BigDecimal unpayItem;

    /**
     * 正常还-未还比例
     */
    private BigDecimal unpayRatio;

    /**
     * 正常还-主动还租金项
     */
    private BigDecimal ontimeActiveCapitalItem;

    /**
     * 正常还-主动还租金比例
     */
    private BigDecimal ontimeActiveCapitalRatio;

    /**
     * 正常还-代扣还租金项
     */
    private BigDecimal ontimeWithholdCapitalItem;

    /**
     * 正常还-代扣还租金比例
     */
    private BigDecimal ontimeWithholdCapitalRatio;

    /**
     * 正常还-主动还买断金项
     */
    private BigDecimal ontimeActiveBuyoutItem;

    /**
     * 正常还-主动还买断金比例
     */
    private BigDecimal ontimeActiveBuyoutRatio;

    /**
     * 统计日
     */
    private String countDay;


}
