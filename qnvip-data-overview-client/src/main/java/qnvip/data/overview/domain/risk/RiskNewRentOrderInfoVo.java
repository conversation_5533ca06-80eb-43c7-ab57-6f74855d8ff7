package qnvip.data.overview.domain.risk;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RiskNewRentOrderInfoVo {

    private String needRiskAuth;
    private String riskCreateTime;
    private String rentCreateTime;
    private String riskUpdateTime;
    private String riskRhTime;
    private String rentSendTime;
    private String riskSendTime;
    private String rentPaymentTime;
    private String riskPayTime;
    private String orderNo;
    private String riskCustomerId;
    private String rentCustomerId;
    private String rentMerchantId;
    private String riskMerchantId;
    private String rentTermination;
    private String rentOrderStatus;
    private String riskCloseStatus;
    private String riskBusinessChannel;
    private String rentType;
    private String rentMiniType;
    private String rentCommonRentFlag;
    private String riskCoRent;
    private String rentEquipmentState;
    private String rentEquipmentType;
    private String riskSupervisoryMachine;
    private String riskScene;
    private String rentQuotientId;
    private String riskQuotientCode;
    private String riskQuotientName;
    private String riskQuotientScene;
    private String rentOrderSourceTag;
    private String rentRateConfigType;
    private String riskFinanceTemplateType;
    private String rentBondFreeStatus;
    private String riskFicoStatus;
    private String riskArtificialAuditStatus;
    private String riskOpinion;
    private String rentPlatformCode;
    private String rentMerchantTransfer;
    private String front1PassFlag;
    private String front2PassFlag;
    private String riskPassFlag;
    private String labourAuditFlag;
    private String creditAuditPassFlag;
}
