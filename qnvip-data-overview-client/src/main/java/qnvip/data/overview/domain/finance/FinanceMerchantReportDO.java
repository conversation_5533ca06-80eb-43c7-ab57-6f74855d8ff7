package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_finance_merchant_report")
public class FinanceMerchantReportDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime rentStartDate;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源
     */
    private Integer miniType;

    /**
     * 方案类型
     */
    private Integer financeType;

    /**
     * 真实采购价
     */
    private BigDecimal actSupplyPrice;

    @ApiModelProperty("基础项：真实采购价单量")
    private Integer actSupplyPriceCount;

    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    @ApiModelProperty("基础项：收取保证金单量")
    private Integer bondAmtCount;

    /**
     * 总租金
     */
    private BigDecimal totalRent;
    @ApiModelProperty("基础项：协议总租金单量")
    private Integer totalRentCount;
    /**
     * 售前优惠
     */
    private BigDecimal beforeDiscount;
    @ApiModelProperty("基础项：售前优惠单量")
    private Integer beforeDiscountCount ;

    /**
     * 已收月租金
     */
    private BigDecimal actRentAmt;
    @ApiModelProperty("账单租金：已收月租金单量")
    private Integer actRentAmtCount ;

    /**
     * 保证金抵扣租金
     */
    private BigDecimal bondDeductRent;
    @ApiModelProperty("账单租金：保证金抵扣租金单量")
    private Integer bondDeductRentCount;

    /**
     * 租后优惠
     */
    private BigDecimal afterDiscount;
    @ApiModelProperty("账单租金：租后优惠单量")
    private Integer afterDiscountCount;

    /**
     * 剩余总租金
     */
    private BigDecimal surplusRentAmt;
    @ApiModelProperty("逾期未收：剩余总租金单量")
    private Integer surplusRentAmtCount;

    /**
     * 是否逾期 0否，1是
     */
    private Integer overdueStatus;

    /**
     * 未退还保证金
     */
    private BigDecimal noRefundBondAmt;
    @ApiModelProperty("逾期未收：收取保证金单量")
    private Integer noRefundBondAmtCount ;

    /**
     * 逾期滞纳金
     */
    private BigDecimal overdueFine;
    @ApiModelProperty("逾期未收：逾期滞纳金单量")
    private Integer overdueFineCount ;

}
