package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 配件
 * <AUTHOR>
 * @Date 2021/10/8 4:28 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_component")
public class ComponentDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 配件销量
     */
    private Long saleCount;
    /**
     * 配件GMV
     */
    private BigDecimal orderGmv;
    /**
     * 配件成本价
     */
    private BigDecimal costPrice;
    /**
     * 配件下单量
     */
    private Long orderCount;
    /**
     * 总配件下单量
     */
    private Long totalOrderCount;
    /**
     * 配件支付单量
     */
    private Long payCount;
    /**
     * 总配件支付单量
     */
    private Long totalPayCount;
}