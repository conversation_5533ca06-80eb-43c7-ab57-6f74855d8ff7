package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/13 3:28 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_margin")
public class MarginDO extends BaseDO {
    /**
     * 日期
     */
    private LocalDateTime countDay;
    /**
     *  区间
     */
    private Integer level;
    /**
     * 支付数量
     */
    private Long payCount;
    /**
     * 下单量
     */
    private Long orderCount;
}