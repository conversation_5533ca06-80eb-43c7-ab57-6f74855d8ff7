package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * create by gw on 2022/3/21
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_finance_order_detail")
public class FinanceOrderDetailDO extends BaseDO {

    @ApiModelProperty("起租时间")
    private LocalDateTime rentStartDate;

    @ApiModelProperty("到期时间")
    private LocalDateTime rentEndDate;

    @ApiModelProperty("父订单起租时间")
    private LocalDateTime parentDay;


    @ApiModelProperty("父订单到期时间")
    private LocalDateTime parentEndDate;


    @ApiModelProperty("来源")
    private Integer miniType;

    @ApiModelProperty("还款期数")
    private Integer term;

    @ApiModelProperty("是否二手,新旧程度")
    private String secondHandType;

    private Integer businessType;

    @ApiModelProperty("方案类型")
    private Integer financeType;

    @ApiModelProperty("订单编号")
    private String orderNo;

    @ApiModelProperty("用户姓名")
    private String customerName;

    @ApiModelProperty("用户身份证号")
    private String customerIdCardNo;

    @ApiModelProperty("用户手机号")
    private String customerMobile;

    @ApiModelProperty("订单状态")
    private Integer orderStatus;

    @ApiModelProperty("商品简称")
    private String shortName;

    @ApiModelProperty("颜色")
    private String color;
    @ApiModelProperty("型号")
    private String model;
    @ApiModelProperty("尺寸")
    private String size;

    @ApiModelProperty("真实采购价")
    private BigDecimal actSupplyPrice;

    @ApiModelProperty("总租金")
    private BigDecimal totalRent;

    @ApiModelProperty("月租金")
    private BigDecimal monthRent;

    @ApiModelProperty("买断金")
    private BigDecimal buyoutAmt;

    @ApiModelProperty("保证金")
    private BigDecimal bondAmt;

    @ApiModelProperty("实收总额 = 实收租金+实收买断金+已收续租租金")
    private BigDecimal actTotalAmt;

    @ApiModelProperty("实收租金")
    private BigDecimal actRentAmt;

    @ApiModelProperty("实收买断金")
    private BigDecimal actBuyoutAmt;

    @ApiModelProperty("3+N结清优惠")
    private BigDecimal endDiscountAmt;

    @ApiModelProperty("6+6结清优惠")
    private BigDecimal endDiscountFor6Amt = BigDecimal.ZERO;
    @ApiModelProperty("6+6还机未收")
    private BigDecimal endReturnNotRepayFor6Amt = BigDecimal.ZERO;

    @ApiModelProperty("售前优惠")
    private BigDecimal totalBeforeWriteOffAmt;

    @ApiModelProperty("已收售前优惠")
    private BigDecimal beforeDiscount;

    @ApiModelProperty("租后减免租金")
    private BigDecimal afterDiscount;

    @ApiModelProperty("运营减免租金")
    private BigDecimal manageDiscount;

    @ApiModelProperty("保证金抵扣租金")
    private BigDecimal bondDeductRent;

    @ApiModelProperty("保证金抵扣买断金")
    private BigDecimal bondDeductBuyoutAmt;

    @ApiModelProperty("保证金抵扣赔偿金")
    private BigDecimal bondDeductDamageAmt = BigDecimal.ZERO;

    @ApiModelProperty("售后减免买断金(buyoutReduceAmount+buyoutCouponAmount)")
    private BigDecimal buyoutReduceAfterCount = BigDecimal.ZERO;

    @ApiModelProperty("运营减免买断金")
    private BigDecimal buyoutReduceMarketingAmt = BigDecimal.ZERO;

    @ApiModelProperty("退还保证金")
    private BigDecimal bondRefundAmt;

    @ApiModelProperty("剩余租金")
    private BigDecimal surplusRentAmt;

    @ApiModelProperty("剩余保证金")
    private BigDecimal surplusBondAmt;

    @ApiModelProperty("剩余买断金")
    private BigDecimal surplusBuyoutAmt;

    @ApiModelProperty("归还本金")
    private BigDecimal buyoutReturnCapitalAmt = BigDecimal.ZERO;

    @ApiModelProperty("归还优惠")
    private BigDecimal rentReturnDiscount;

    @ApiModelProperty("是否逾期 0否，1是")
    private Integer overdueStatus;

    @ApiModelProperty("逾期滞纳金")
    private BigDecimal overdueFine;

    @ApiModelProperty("续租关联订单号")
    private String renewOrderNo;

    @ApiModelProperty("续租本金")
    private BigDecimal renewCapital;

    @ApiModelProperty("续租本金(续租报表体现)")
    private BigDecimal oldBuyoutAmt;

    @ApiModelProperty("续租总租金")
    private BigDecimal renewTotalRent;

    @ApiModelProperty("续租保证金")
    private BigDecimal renewBondAmt;

    @ApiModelProperty("续租期数")
    private Integer renewTerm;

    @ApiModelProperty("已收续租租金")
    private BigDecimal actRenewRentAmt;

    @ApiModelProperty("已收续租滞纳金")
    private BigDecimal actRenewOverdueFine;

    @ApiModelProperty("续租保证金抵扣租金")
    private BigDecimal renewBondDeductRent;

    @ApiModelProperty("续租剩余租金")
    private BigDecimal renewSurplusRentAmt;

    @ApiModelProperty("续租是否逾期")
    private Integer renewOverdueStatus;

    @ApiModelProperty("续租逾期滞纳金")
    private BigDecimal renewOverdueFine;

    @ApiModelProperty("3+N还机未收")
    private BigDecimal endReturnNotRepayAmt = BigDecimal.ZERO;

    @ApiModelProperty("实收还机赔偿金")
    private BigDecimal endReturnDamageAmt = BigDecimal.ZERO;

    @ApiModelProperty("资方简称")
    private String capitalName;
    // @ApiModelProperty("融资状态")
    // private String financeFlag;
    @ApiModelProperty("碎屏险收入")
    private BigDecimal screenRiskCapital;
    @ApiModelProperty("配件金")
    private BigDecimal operatingPurchasePrice;

    /**
     * 【ID1006135】
     * 【自营看板】和【商户看板】租赁主订单|租赁续租订单 ，导出明细字段新增
     * 新加字段
     * 1.是否监管 列值：是/否
     *
     * 2.是否租物 列值：是/否
     *
     * 3.流量归属
     *
     * 4.导流商名称
     */
    @ApiModelProperty("是否监管机,0非监管,1监管")
    private Integer ItemEquipmentType;
    @ApiModelProperty("是否租物引流,0租物,1租物")
    private Integer zwDrainageFlag;

    @ApiModelProperty("流量归属")
    private Integer orderAttributionCode;
    @ApiModelProperty("导流商名称")
    private String quotientName;

    @ApiModelProperty("溢价金")
    private BigDecimal premiumAmt;

    @ApiModelProperty("实际支付溢价金")
    private BigDecimal paidPremiumAmt;
}