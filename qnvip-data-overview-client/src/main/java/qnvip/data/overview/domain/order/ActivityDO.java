package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/8 9:54 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_activity")
public class ActivityDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 活动名称
     */
    private Long activityId;
    /**
     * 活动名称
     */
    private String activityName;
    /**
     * 订单量
     */
    private Long orderCount;
    /**
     * 支付单量
     */
    private Long payCount;
    /**
     * 审核通过单量
     */
    private Long auditCount;
    /**
     * 签收单量
     */
    private Long signCount;
    /**
     * 逾期单量
     */
    private Long overdueCount;
    /**
     * 类型（1单个活动数据，2当天所有活动总数据）
     */
    private Integer type;

    /**
     * 活动访问量
     */
    private Long pv;

    /**
     * 活动访问人数
     */
    private Long uv;
}