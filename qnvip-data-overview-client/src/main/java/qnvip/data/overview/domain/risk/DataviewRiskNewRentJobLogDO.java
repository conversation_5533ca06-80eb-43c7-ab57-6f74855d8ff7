package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* Created by zhanglei on 2023-09-21
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_new_rent_job_log")
@ApiModel(value="DataviewRiskNewRentJobLogDO对象", description="租赁风控大盘-新租赁定时job处理日志")
public class DataviewRiskNewRentJobLogDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "任务名称")
    private String jobName;

    @ApiModelProperty(value = "任务发起时间,精确到分钟")
    private String jobTime;

    @ApiModelProperty(value = "任务查询的数量")
    private Integer statCount;
    @ApiModelProperty(value = "0失败,1成功")
    private Integer status;

    @ApiModelProperty(value = "任务查询的sql")
    private String statSql;

    @ApiModelProperty(value = "任务异信息信息")
    private String errorInfo;


}
