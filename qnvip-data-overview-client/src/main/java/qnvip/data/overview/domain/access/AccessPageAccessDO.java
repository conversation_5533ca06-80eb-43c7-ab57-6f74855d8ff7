package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_access_page_access")
public class AccessPageAccessDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("访问渠道")
    private Integer miniType;


    @ApiModelProperty("页面code")
    private Long enterPageCode;

    @ApiModelProperty("统计日期")
    private LocalDateTime countDay;


    @ApiModelProperty("访问小程序的人数")
    private Long uv = 0L;


    @ApiModelProperty("访问小程序的人数")
    private Long pv = 0L;


    @ApiModelProperty("页面人均停留时长")
    private BigDecimal avgKeepTime = BigDecimal.ZERO;


}
