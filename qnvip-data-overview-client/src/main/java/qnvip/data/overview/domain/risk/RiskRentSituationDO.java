package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_rent_situation")
public class RiskRentSituationDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 业务渠道
     */
    private Integer businessChannel;

    /**
     * 风控用户
     */
    private Long riskUser;

    /**
     * 有效用户
     */
    private Long validUser;

    /**
     * 基础通过人数
     */
    private Long passUser;

    /**
     * 央行查询人数
     */
    private Long centralBankInquiry;

    /**
     * 央行通过人数
     */
    private Long centralBankPass;

    /**
     * 大数据通过人数
     */
    private Long bigDataPass;

    /**
     * 成功付款人数
     */
    private Long payUser;

    /**
     * 自营发货数
     */
    private Long sendCount;

    /**
     * 商户发货数
     */
    private Long merchantSendCount;

    /**
     * 总租金
     */
    private BigDecimal totalAmt;

    /**
     * 保证金
     */
    private BigDecimal margin;

    /**
     * 买断金
     */
    private BigDecimal buyOutAmt;

    /**
     * 支付取消人数
     */
    private Long payUserClose;

    /**
     * 自营支付人数(包含取消人数)
     */
    private Long paySelfCount;
    /**
     * 自营支付取消人数
     */
    private Long payUserSelfClose;
    /**
     * 信审关闭人数
     */
    private Long payCreditAuditClose;

    /**
     * 下单和人行风控同一天的人数
     */
    private Integer curDayCount;

    /**
     * 支付宝支付人数
     */
    private Long aliPay;

    /**
     * 微信支付人数
     */
    private Long weiXinPay;

    /**
     * 其他渠道支付人数
     */
    private Long otherPay;


    /**
     * 成功付款人数(商户)
     */
    private Long payUserMerchant;

    /**
     * 总租金(商户)
     */
    private BigDecimal totalAmtMerchant;

    /**
     * 保证金(商户)
     */
    private BigDecimal marginMerchant;

    /**
     * 买断金(商户)
     */
    private BigDecimal buyOutAmtMerchant;
}
