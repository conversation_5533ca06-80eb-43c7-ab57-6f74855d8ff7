package qnvip.data.overview.domain.customer;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_customer_analysis_city")
public class CustomerAnalysisCityDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 订单状态
     */
    private Integer status;

    /**
     * 城市
     */
    private String city;


    /**
     * 人数
     */
    private BigDecimal count;

    /**
     * 类型 1：用户城市 2：收货城市
     */
    private Integer type;


}
