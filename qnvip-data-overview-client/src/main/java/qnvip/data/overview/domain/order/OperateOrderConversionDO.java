package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import io.swagger.annotations.ApiModelProperty;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_order_conversion")
public class OperateOrderConversionDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 下单用户数
     */
    private Long orderUvCount= 0L;

    /**
     * 1=无效用户 2=有效低质量用户 3=有效高质量
     */
    private Integer type;

    @ApiModelProperty("用户数")
    private Long uvCount= 0L;

    /**
     * 下单量
     */
    private Long orderCount= 0L;

    /**
     * 审核订单
     */
    private Long riskControlCount= 0L;

    /**
     * 通审订单
     */
    private Long riskPassCount= 0L;

    /**
     * 通审用户数（待付款用户数）
     */
    private Long riskPassUvCount= 0L;

    /**
     * 付款订单
     */
    private Long payCount= 0L;

    /**
     * 付款用户数（待发货人数）
     */
    private Long payUvCount= 0L;

    /**
     * 发货订单数
     */
    private Long sendCount= 0L;

    /**
     * 商户发货订单数
     */
    private Long merchantSendCount= 0L;

    /**
     * 发货人数
     */
    private Long sendUvCount= 0L;

    /**
     * 签收订单数
     */
    private Long signCount= 0L;

    /**
     * 付款订单(订单创建口径)
     */
    private Long payCountOther= 0L;

    /**
     * 发货订单数(订单创建口径)
     */
    private Long sendCountOther= 0L;

    /**
     * 签收订单数(订单创建口径)
     */
    private Long signCountOther= 0L;

    @ApiModelProperty("通审订单订单(订单创建口径)")
    private Long riskPassCountOther= 0L;

}
