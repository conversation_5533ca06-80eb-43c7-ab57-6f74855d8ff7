package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operation_quotient_tag")
public class OperationQuotientTagDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日
     */
    private LocalDateTime countDay;

    /**
     * 统计口径 1:订单创建时间,2:事件发生时间
     */
    private Integer countType;

    /**
     * 平台
     */
    private Integer miniType;

    /**
     * 小程序
     */
    private String platform;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商
     */
    private String quotientName;

    /**
     * 风控审核标签
     */
    private String tag;

    /**
     * 风控策略
     */
    private String riskStrategy;


}
