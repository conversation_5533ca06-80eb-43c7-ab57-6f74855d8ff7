package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_credit")
public class WholeLinkCreditDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 征信吞
     */
    private Long creditThrough;

    /**
     * 今日今查
     */
    private Long onQueryT0;

    /**
     * 往日今查
     */
    private Long onQueryT1;

    /**
     * 报告未出
     */
    private Long queryUv;

    /**
     * 今查今出
     */
    private Long resultT0;

    /**
     * 往查今出
     */
    private Long resultT1;

    /**
     * 今查耗时
     */
    private Double resultTimeT0;

    /**
     * 往查耗时
     */
    private Double resultTimeT1;

    /**
     * 总平均耗时
     */
    private Double timeConsume;

    /**
     * 福建吞
     */
    private Long fjThrough;

    /**
     * 人行吞
     */
    private Long rhThrough;

    /**
     * 安徽吞
     */
    private Long ahThrough;

    /**
     * 征信吐
     */
    private Long creditPut;

    /**
     * 福建吐
     */
    private Long fjPut;

    /**
     * 人行吐
     */
    private Long rhPut;

    /**
     * 安徽吐
     */
    private Long ahPut;

}
