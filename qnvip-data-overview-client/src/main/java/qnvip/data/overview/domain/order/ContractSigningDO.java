package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_contract_signing")
public class ContractSigningDO extends BaseDO {

    /**
     * 订单id
     */
    private Long orderId;

    /**
     * 合同类型 200 -长租合同 207-短租合同
     */
    private Integer type;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;
}

