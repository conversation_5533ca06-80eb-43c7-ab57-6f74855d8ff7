package qnvip.data.overview.domain.marketing;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_coupon_core")
public class OperateCouponCoreDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 优惠券id
     */
    private Long couponId= 0L;

    /**
     * 优惠券简称
     */
    private String name;

    /**
     * 开始时间
     */
    private LocalDateTime activityBeginTime;

    /**
     * 结束时间
     */
    private LocalDateTime activityEndTime;

    /**
     * 有效天数
     */
    private Integer validityDays;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 使用场景
     */
    private String scene;

    /**
     * 使用方式
     */
    private Integer useType;

    /**
     * 券类型
     */
    private Integer type;

    /**
     * 发放数量
     */
    private Long grantCount= 0L;

    /**
     * 已领取
     */
    private Long receiveCount= 0L;

    /**
     * 已核销
     */
    private Long useCount= 0L;

    /**
     * 订单量
     */
    private Long orderCount= 0L;

    /**
     * gmv
     */
    private BigDecimal gmv;

    /**
     * 通审单量
     */
    private Long riskPassCount= 0L;

    /**
     * 支付订单
     */
    private Long payCount= 0L;

    /**
     * 营销费用
     */
    private BigDecimal marketingCosts;

    /**
     * 关联商品数
     */
    private Long associatedGoodsCnt= 0L;

    /**
     * 关联用户数
     */
    private Long associatedUserCnt= 0L;



}
