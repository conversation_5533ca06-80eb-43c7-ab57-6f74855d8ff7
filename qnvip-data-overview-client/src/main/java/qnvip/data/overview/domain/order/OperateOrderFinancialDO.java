package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_order_financial")
public class OperateOrderFinancialDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;


    /**
     * 金融方案
     */
    private String rentType;

    /**
     * 订单量
     */
    private Long orderCount= 0L;

    /**
     * 支付单量
     */
    private Long payCount= 0L;


    /**
     * 签收单量
     */
    private Long signCount= 0L;

}
