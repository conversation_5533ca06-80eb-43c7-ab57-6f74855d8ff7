package qnvip.data.overview.domain.customer;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_customer_age")
public class OperateCustomerAgeDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 性别
     */
    private String gender;

    /**
     * 年龄
     */
    private Double age;

    /**
     * 人数
     */
    private Long count = 0L;

    /**
     * 类型 1：基础类型
     */
    private Integer type;


}
