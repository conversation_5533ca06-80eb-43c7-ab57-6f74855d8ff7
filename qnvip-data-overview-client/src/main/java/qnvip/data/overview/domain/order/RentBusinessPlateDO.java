package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_rent_business_plate")
public class RentBusinessPlateDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日
     */
    private LocalDateTime countDay;

    /**
     * 平台
     */
    private Integer miniType;

    /**
     * 小程序
     */
    private String platform;

    /**
     * 预风控人数
     */
    private Integer riskCnt;

    /**
     * 审核通过
     */
    private Integer auditPassCnt;

    /**
     * 审核拒绝
     */
    private Integer auditRefuseCnt;

    /**
     * 自营支付
     */
    private Integer payCnt;

    /**
     * 自营发货
     */
    private Integer sendCnt;

    /**
     * 自营签收
     */
    private Integer signCnt;

    /**
     * 审核通过分流订单
     */
    private Integer merchantAuditPassCnt;

    /**
     * 分流总订单
     */
    private Integer merchantCnt;

    /**
     * 商户审核订单
     */
    private Integer merchantAuditCnt;

    /**
     * 分流支付
     */
    private Integer merchantPayCnt;

    /**
     * 分流发货
     */
    private Integer merchantSendCnt;

    /**
     * 分流签收
     */
    private Integer merchantSignCnt;

    /**
     * 自营审核拒绝分流订单
     */
    private Integer merchantAuditRefuseCnt;

    /**
     * 自营审核拒绝商户审核通过订单
     */
    private Integer merchantAuditCntRefuse;

    /**
     * 自营审核拒绝分流支付
     */
    private Integer merchantPayCntRefuse;

    /**
     * 自营审核拒绝分流发货
     */
    private Integer merchantSendCntRefuse;

    /**
     * 自营审核拒绝分流签收
     */
    private Integer merchantSignCntRefuse;


}
