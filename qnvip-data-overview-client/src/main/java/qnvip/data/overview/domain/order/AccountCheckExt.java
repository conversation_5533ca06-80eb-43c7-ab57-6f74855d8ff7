package qnvip.data.overview.domain.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @time 2021年06月30日 2:44 下午
 */
@Data
public class AccountCheckExt {

    /**
     * amount = 实际还款金+实际买断金 - 保证金抵扣 - 优惠金额 =  原始支付金额==0 ？流水表实际支付买断金：原始支付金额
     */
    private BigDecimal amount;
    /**
     * 保证金
     */
    private BigDecimal bondAmount;
    /**
     * 碎屏保证金
     */
    private BigDecimal screenRisksAmount;
    /**
     * 逾期金额
     */
    private BigDecimal overdueFine;
}
