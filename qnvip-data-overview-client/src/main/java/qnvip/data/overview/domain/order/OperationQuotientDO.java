package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operation_quotient")
public class OperationQuotientDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日
     */
    private LocalDateTime countDay;

    /**
     * 统计口径 1:订单创建时间,2:事件发生时间
     */
    private Integer countType;

    /**
     * 平台
     */
    private Integer miniType;

    /**
     * 小程序
     */
    private String platform;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 导流商
     */
    private String quotientName;

    /**
     * 预风控人数
     */
    private Integer riskCnt;

    /**
     * 审核中
     */
    private Integer inAuditCnt;

    /**
     * 审核通过
     */
    private Integer auditPassCnt;

    /**
     * 审核拒绝
     */
    private Integer auditRefuseCnt;

    /**
     * 自营支付
     */
    private Integer payCnt;

    /**
     * 自营发货
     */
    private Integer sendCnt;

    /**
     * 自营签收
     */
    private Integer signCnt;

    /**
     * 自营支付后取消
     */
    private Integer payCloseCnt;

    /**
     * 自营人工信审关闭
     */
    private Integer auditCloseCnt;

    /**
     * 分流总订单
     */
    private Integer merchantCnt;

    /**
     * 审核通过分流
     */
    private Integer merchantAuditPassCnt;

    /**
     * 审核拒绝分流
     */
    private Integer merchantAuditRefuseCnt;

    /**
     * 商户审核
     */
    private Integer merchantAuditCnt;

    /**
     * 分流支付
     */
    private Integer merchantPayCnt;

    /**
     * 分流发货
     */
    private Integer merchantSendCnt;

    /**
     * 分流签收
     */
    private Integer merchantSignCnt;

    /**
     * 分流支付后取消
     */
    private Integer merchantPayCloseCnt;

    /**
     * 风控审核标签
     */
    private String tag;


    /**
     * 自营审核拒绝商户通审订单
     */
    private Integer merchantAuditCntRefuse;
    /**
     * 自营审核拒绝商户支付订单
     */
    private Integer merchantPayCntRefuse;
    /**
     * 自营审核拒绝商户发货订单
     */
    private Integer merchantSendCntRefuse;
    /**
     * 自营审核拒绝商户签收订单
     */
    private Integer merchantSignCntRefuse;

    /**
     * 自营审核拒绝商户支付后取消
     */
    private Integer merchantPayCloseCntRefuse;
}
