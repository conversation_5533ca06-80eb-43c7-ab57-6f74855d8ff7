package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;


/**
 * @author:
 * @Description: 每日汇总表
 */
@Data
@TableName("dataview_risk_general_count1")
public class RiskGeneralCountDO  extends BaseDO {


    private static final long serialVersionUID = 1L;
    /**
     *  芝麻分等级
     */
    private String zmfLevel;


    /**
     * 是否租物
     */
    private String rentType;
    /**
     * 起租日
     */
    private String countMonth;
    /**
     * 平台
     */
    private Integer miniType;

    private Integer overdueDay;
    private Integer maxTerm;

    /**
     * 平台
     */
    private String platform;

    private Integer financeType;


    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;
    /**
     * 审核id
     */
    private String auditType;

    /**
     * 审核人名字
     */
    private String auditTypeName;

    /**
     * 审核方式
     */
    @TableField(exist = false)
    private String auditTypeDesc;

    /**
     * 保证金区间
     */
    private String bondRateInterval;

    /**
     * 风控等级
     */
    private String riskLevel;

    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 续租方式 1：自动续租 0：手动续租 2：未续租
     */
    private Integer renewWay;

    /**
     * 评分
     */
    private Integer score;

    private BigDecimal buyoutAmt;

    private BigDecimal beforeDiscount;

    private BigDecimal discountTotal;

    private BigDecimal rentTotal;

    // 计算12期以内的总租金
    private BigDecimal rentTotalInner;

    private BigDecimal renewTotalRent;

    private BigDecimal totalOrderCnt;

    /*
    * 新增
    * */
    private BigDecimal surplusBondAmtTotal;
    private BigDecimal bondRestFundAmountTotal;
    private BigDecimal diffPricingDiscountAmtTotal;
    private BigDecimal couponDiscountAmtTotal;

    private BigDecimal bondAmt1;

    private BigDecimal buyoutAmt1;

    /*新增逻辑*/
    private BigDecimal surplusBondAmt1;

    private BigDecimal bondRestFundAmount1;

    private BigDecimal diffPricingDiscountAmt1;

    private BigDecimal couponDiscountAmt1;

    private BigDecimal surplusBondAmt2;

    private BigDecimal bondRestFundAmount2;

    private BigDecimal diffPricingDiscountAmt2;

    private BigDecimal couponDiscountAmt2;

    private BigDecimal surplusBondAmt3;

    private BigDecimal bondRestFundAmount3;

    private BigDecimal diffPricingDiscountAmt3;

    private BigDecimal couponDiscountAmt3;

    private BigDecimal surplusBondAmt4;

    private BigDecimal bondRestFundAmount4;

    private BigDecimal diffPricingDiscountAmt4;

    private BigDecimal couponDiscountAmt4;

    private BigDecimal surplusBondAmt5;

    private BigDecimal bondRestFundAmount5;

    private BigDecimal diffPricingDiscountAmt5;

    private BigDecimal couponDiscountAmt5;

    private BigDecimal surplusBondAmt6;

    private BigDecimal bondRestFundAmount6;

    private BigDecimal diffPricingDiscountAmt6;

    private BigDecimal couponDiscountAmt6;

    private BigDecimal surplusBondAmt7;

    private BigDecimal bondRestFundAmount7;

    private BigDecimal diffPricingDiscountAmt7;

    private BigDecimal couponDiscountAmt7;

    private BigDecimal surplusBondAmt8;

    private BigDecimal bondRestFundAmount8;

    private BigDecimal diffPricingDiscountAmt8;

    private BigDecimal couponDiscountAmt8;

    private BigDecimal surplusBondAmt9;

    private BigDecimal bondRestFundAmount9;

    private BigDecimal diffPricingDiscountAmt9;

    private BigDecimal couponDiscountAmt9;

    private BigDecimal surplusBondAmt10;

    private BigDecimal bondRestFundAmount10;

    private BigDecimal diffPricingDiscountAmt10;

    private BigDecimal couponDiscountAmt10;

    private BigDecimal surplusBondAmt11;

    private BigDecimal bondRestFundAmount11;

    private BigDecimal diffPricingDiscountAmt11;

    private BigDecimal couponDiscountAmt11;

    private BigDecimal surplusBondAmt12;

    private BigDecimal bondRestFundAmount12;

    private BigDecimal diffPricingDiscountAmt12;

    private BigDecimal couponDiscountAmt12;

    private BigDecimal surplusBondAmt13;

    private BigDecimal bondRestFundAmount13;

    private BigDecimal diffPricingDiscountAmt13;

    private BigDecimal couponDiscountAmt13;

    private BigDecimal surplusBondAmt14;

    private BigDecimal bondRestFundAmount14;

    private BigDecimal diffPricingDiscountAmt14;

    private BigDecimal couponDiscountAmt14;

    private BigDecimal surplusBondAmt15;

    private BigDecimal bondRestFundAmount15;

    private BigDecimal diffPricingDiscountAmt15;

    private BigDecimal couponDiscountAmt15;

    private BigDecimal surplusBondAmt16;

    private BigDecimal bondRestFundAmount16;

    private BigDecimal diffPricingDiscountAmt16;

    private BigDecimal couponDiscountAmt16;

    private BigDecimal surplusBondAmt17;

    private BigDecimal bondRestFundAmount17;

    private BigDecimal diffPricingDiscountAmt17;

    private BigDecimal couponDiscountAmt17;

    private BigDecimal surplusBondAmt18;

    private BigDecimal bondRestFundAmount18;

    private BigDecimal diffPricingDiscountAmt18;

    private BigDecimal couponDiscountAmt18;

    private BigDecimal surplusBondAmt19;

    private BigDecimal bondRestFundAmount19;

    private BigDecimal diffPricingDiscountAmt19;

    private BigDecimal couponDiscountAmt19;

    private BigDecimal surplusBondAmt20;

    private BigDecimal bondRestFundAmount20;

    private BigDecimal diffPricingDiscountAmt20;

    private BigDecimal couponDiscountAmt20;

    private BigDecimal surplusBondAmt21;

    private BigDecimal bondRestFundAmount21;

    private BigDecimal diffPricingDiscountAmt21;

    private BigDecimal couponDiscountAmt21;

    private BigDecimal surplusBondAmt22;

    private BigDecimal bondRestFundAmount22;

    private BigDecimal diffPricingDiscountAmt22;

    private BigDecimal couponDiscountAmt22;

    private BigDecimal surplusBondAmt23;

    private BigDecimal bondRestFundAmount23;

    private BigDecimal diffPricingDiscountAmt23;

    private BigDecimal couponDiscountAmt23;

    private BigDecimal surplusBondAmt24;

    private BigDecimal bondRestFundAmount24;

    private BigDecimal diffPricingDiscountAmt24;

    private BigDecimal couponDiscountAmt24;


    private BigDecimal surplusBondAmt25;

    private BigDecimal bondRestFundAmount25;

    private BigDecimal diffPricingDiscountAmt25;

    private BigDecimal couponDiscountAmt25;

    private BigDecimal surplusBondAmt26;

    private BigDecimal bondRestFundAmount26;

    private BigDecimal diffPricingDiscountAmt26;

    private BigDecimal couponDiscountAmt26;

    private BigDecimal surplusBondAmt27;

    private BigDecimal bondRestFundAmount27;

    private BigDecimal diffPricingDiscountAmt27;

    private BigDecimal couponDiscountAmt27;
    private BigDecimal surplusBondAmt28;
    private BigDecimal bondRestFundAmount28;

    private BigDecimal diffPricingDiscountAmt28;

    private BigDecimal couponDiscountAmt28;

    private BigDecimal surplusBondAmt29;

    private BigDecimal bondRestFundAmount29;

    private BigDecimal diffPricingDiscountAmt29;

    private BigDecimal couponDiscountAmt29;

    private BigDecimal surplusBondAmt30;

    private BigDecimal bondRestFundAmount30;

    private BigDecimal diffPricingDiscountAmt30;

    private BigDecimal couponDiscountAmt30;

    private BigDecimal parentBeforeDiscount1;

    private BigDecimal parentDiscountReturnAmt1;

    private BigDecimal beforeDiscount1;

    private BigDecimal discountReturnAmt1;

    private BigDecimal bondAmt2;

    private BigDecimal buyoutAmt2;

    private BigDecimal parentBeforeDiscount2;

    private BigDecimal parentDiscountReturnAmt2;

    private BigDecimal beforeDiscount2;

    private BigDecimal discountReturnAmt2;

    private BigDecimal bondAmt3;

    private BigDecimal buyoutAmt3;

    private BigDecimal parentBeforeDiscount3;

    private BigDecimal parentDiscountReturnAmt3;

    private BigDecimal beforeDiscount3;

    private BigDecimal discountReturnAmt3;

    private BigDecimal bondAmt4;

    private BigDecimal buyoutAmt4;

    private BigDecimal parentBeforeDiscount4;

    private BigDecimal parentDiscountReturnAmt4;

    private BigDecimal beforeDiscount4;

    private BigDecimal discountReturnAmt4;

    private BigDecimal bondAmt5;

    private BigDecimal buyoutAmt5;

    private BigDecimal parentBeforeDiscount5;

    private BigDecimal parentDiscountReturnAmt5;

    private BigDecimal beforeDiscount5;

    private BigDecimal discountReturnAmt5;

    private BigDecimal bondAmt6;

    private BigDecimal buyoutAmt6;

    private BigDecimal parentBeforeDiscount6;

    private BigDecimal parentDiscountReturnAmt6;

    private BigDecimal beforeDiscount6;

    private BigDecimal discountReturnAmt6;

    private BigDecimal bondAmt7;

    private BigDecimal buyoutAmt7;

    private BigDecimal parentBeforeDiscount7;

    private BigDecimal parentDiscountReturnAmt7;

    private BigDecimal beforeDiscount7;

    private BigDecimal discountReturnAmt7;

    private BigDecimal bondAmt8;

    private BigDecimal buyoutAmt8;

    private BigDecimal parentBeforeDiscount8;

    private BigDecimal parentDiscountReturnAmt8;

    private BigDecimal beforeDiscount8;

    private BigDecimal discountReturnAmt8;

    private BigDecimal bondAmt9;

    private BigDecimal buyoutAmt9;

    private BigDecimal parentBeforeDiscount9;

    private BigDecimal parentDiscountReturnAmt9;

    private BigDecimal beforeDiscount9;

    private BigDecimal discountReturnAmt9;

    private BigDecimal bondAmt10;

    private BigDecimal buyoutAmt10;

    private BigDecimal parentBeforeDiscount10;

    private BigDecimal parentDiscountReturnAmt10;

    private BigDecimal beforeDiscount10;

    private BigDecimal discountReturnAmt10;

    private BigDecimal bondAmt11;

    private BigDecimal buyoutAmt11;

    private BigDecimal parentBeforeDiscount11;

    private BigDecimal parentDiscountReturnAmt11;

    private BigDecimal beforeDiscount11;

    private BigDecimal discountReturnAmt11;

    private BigDecimal bondAmt12;

    private BigDecimal buyoutAmt12;

    private BigDecimal parentBeforeDiscount12;

    private BigDecimal parentDiscountReturnAmt12;

    private BigDecimal beforeDiscount12;

    private BigDecimal discountReturnAmt12;

    private BigDecimal bondAmt13;

    private BigDecimal buyoutAmt13;

    private BigDecimal parentBeforeDiscount13;

    private BigDecimal parentDiscountReturnAmt13;

    private BigDecimal beforeDiscount13;

    private BigDecimal discountReturnAmt13;

    private BigDecimal bondAmt14;

    private BigDecimal buyoutAmt14;

    private BigDecimal parentBeforeDiscount14;

    private BigDecimal parentDiscountReturnAmt14;

    private BigDecimal beforeDiscount14;

    private BigDecimal discountReturnAmt14;

    private BigDecimal bondAmt15;

    private BigDecimal buyoutAmt15;

    private BigDecimal parentBeforeDiscount15;

    private BigDecimal parentDiscountReturnAmt15;

    private BigDecimal beforeDiscount15;

    private BigDecimal discountReturnAmt15;

    private BigDecimal bondAmt16;

    private BigDecimal buyoutAmt16;

    private BigDecimal parentBeforeDiscount16;

    private BigDecimal parentDiscountReturnAmt16;

    private BigDecimal beforeDiscount16;

    private BigDecimal discountReturnAmt16;

    private BigDecimal bondAmt17;

    private BigDecimal buyoutAmt17;

    private BigDecimal parentBeforeDiscount17;

    private BigDecimal parentDiscountReturnAmt17;

    private BigDecimal beforeDiscount17;

    private BigDecimal discountReturnAmt17;

    private BigDecimal bondAmt18;

    private BigDecimal buyoutAmt18;

    private BigDecimal parentBeforeDiscount18;

    private BigDecimal parentDiscountReturnAmt18;

    private BigDecimal beforeDiscount18;

    private BigDecimal discountReturnAmt18;

    private BigDecimal bondAmt19;

    private BigDecimal buyoutAmt19;

    private BigDecimal parentBeforeDiscount19;

    private BigDecimal parentDiscountReturnAmt19;

    private BigDecimal beforeDiscount19;

    private BigDecimal discountReturnAmt19;

    private BigDecimal bondAmt20;

    private BigDecimal buyoutAmt20;

    private BigDecimal parentBeforeDiscount20;

    private BigDecimal parentDiscountReturnAmt20;

    private BigDecimal beforeDiscount20;

    private BigDecimal discountReturnAmt20;

    private BigDecimal bondAmt21;

    private BigDecimal buyoutAmt21;

    private BigDecimal parentBeforeDiscount21;

    private BigDecimal parentDiscountReturnAmt21;

    private BigDecimal beforeDiscount21;

    private BigDecimal discountReturnAmt21;

    private BigDecimal bondAmt22;

    private BigDecimal buyoutAmt22;

    private BigDecimal parentBeforeDiscount22;

    private BigDecimal parentDiscountReturnAmt22;

    private BigDecimal beforeDiscount22;

    private BigDecimal discountReturnAmt22;

    private BigDecimal bondAmt23;

    private BigDecimal buyoutAmt23;

    private BigDecimal parentBeforeDiscount23;

    private BigDecimal parentDiscountReturnAmt23;

    private BigDecimal beforeDiscount23;

    private BigDecimal discountReturnAmt23;

    private BigDecimal bondAmt24;

    private BigDecimal buyoutAmt24;

    private BigDecimal parentBeforeDiscount24;

    private BigDecimal parentDiscountReturnAmt24;

    private BigDecimal beforeDiscount24;

    private BigDecimal discountReturnAmt24;

    private BigDecimal bondAmt25;

    private BigDecimal buyoutAmt25;

    private BigDecimal parentBeforeDiscount25;

    private BigDecimal parentDiscountReturnAmt25;

    private BigDecimal beforeDiscount25;

    private BigDecimal discountReturnAmt25;

    private BigDecimal bondAmt26;

    private BigDecimal buyoutAmt26;

    private BigDecimal parentBeforeDiscount26;

    private BigDecimal parentDiscountReturnAmt26;

    private BigDecimal beforeDiscount26;

    private BigDecimal discountReturnAmt26;

    private BigDecimal bondAmt27;

    private BigDecimal buyoutAmt27;

    private BigDecimal parentBeforeDiscount27;

    private BigDecimal parentDiscountReturnAmt27;

    private BigDecimal beforeDiscount27;

    private BigDecimal discountReturnAmt27;

    private BigDecimal bondAmt28;

    private BigDecimal buyoutAmt28;

    private BigDecimal parentBeforeDiscount28;

    private BigDecimal parentDiscountReturnAmt28;

    private BigDecimal beforeDiscount28;

    private BigDecimal discountReturnAmt28;

    private BigDecimal bondAmt29;

    private BigDecimal buyoutAmt29;

    private BigDecimal parentBeforeDiscount29;

    private BigDecimal parentDiscountReturnAmt29;

    private BigDecimal beforeDiscount29;

    private BigDecimal discountReturnAmt29;

    private BigDecimal bondAmt30;

    private BigDecimal buyoutAmt30;

    private BigDecimal parentBeforeDiscount30;

    private BigDecimal parentDiscountReturnAmt30;

    private BigDecimal beforeDiscount30;

    private BigDecimal discountReturnAmt30;

    private BigDecimal notPayTerm1;

    private BigDecimal overdueFine1;

    private BigDecimal renewAmt1;

    private BigDecimal countTerm1;

    private BigDecimal notPayTerm2;

    private BigDecimal overdueFine2;

    private BigDecimal renewAmt2;

    private BigDecimal countTerm2;

    private BigDecimal notPayTerm3;

    private BigDecimal overdueFine3;

    private BigDecimal renewAmt3;

    private BigDecimal countTerm3;

    private BigDecimal notPayTerm4;

    private BigDecimal overdueFine4;

    private BigDecimal renewAmt4;

    private BigDecimal countTerm4;

    private BigDecimal notPayTerm5;

    private BigDecimal overdueFine5;

    private BigDecimal renewAmt5;

    private BigDecimal countTerm5;

    private BigDecimal notPayTerm6;

    private BigDecimal overdueFine6;

    private BigDecimal renewAmt6;

    private BigDecimal countTerm6;

    private BigDecimal notPayTerm7;

    private BigDecimal overdueFine7;

    private BigDecimal renewAmt7;

    private BigDecimal countTerm7;

    private BigDecimal notPayTerm8;

    private BigDecimal overdueFine8;

    private BigDecimal renewAmt8;

    private BigDecimal countTerm8;

    private BigDecimal notPayTerm9;

    private BigDecimal overdueFine9;

    private BigDecimal renewAmt9;

    private BigDecimal countTerm9;

    private BigDecimal notPayTerm10;

    private BigDecimal overdueFine10;

    private BigDecimal renewAmt10;

    private BigDecimal countTerm10;

    private BigDecimal notPayTerm11;

    private BigDecimal overdueFine11;

    private BigDecimal renewAmt11;

    private BigDecimal countTerm11;

    private BigDecimal notPayTerm12;

    private BigDecimal overdueFine12;

    private BigDecimal renewAmt12;

    private BigDecimal countTerm12;

    private BigDecimal notPayTerm13;

    private BigDecimal overdueFine13;

    private BigDecimal renewAmt13;

    private BigDecimal countTerm13;

    private BigDecimal notPayTerm14;

    private BigDecimal overdueFine14;

    private BigDecimal renewAmt14;

    private BigDecimal countTerm14;

    private BigDecimal notPayTerm15;

    private BigDecimal overdueFine15;

    private BigDecimal renewAmt15;

    private BigDecimal countTerm15;

    private BigDecimal notPayTerm16;

    private BigDecimal overdueFine16;

    private BigDecimal renewAmt16;

    private BigDecimal countTerm16;

    private BigDecimal notPayTerm17;

    private BigDecimal overdueFine17;

    private BigDecimal renewAmt17;

    private BigDecimal countTerm17;

    private BigDecimal notPayTerm18;

    private BigDecimal overdueFine18;

    private BigDecimal renewAmt18;

    private BigDecimal countTerm18;

    private BigDecimal notPayTerm19;

    private BigDecimal overdueFine19;

    private BigDecimal renewAmt19;

    private BigDecimal countTerm19;

    private BigDecimal notPayTerm20;

    private BigDecimal overdueFine20;

    private BigDecimal renewAmt20;

    private BigDecimal countTerm20;

    private BigDecimal notPayTerm21;

    private BigDecimal overdueFine21;

    private BigDecimal renewAmt21;

    private BigDecimal countTerm21;

    private BigDecimal notPayTerm22;

    private BigDecimal overdueFine22;

    private BigDecimal renewAmt22;

    private BigDecimal countTerm22;

    private BigDecimal notPayTerm23;

    private BigDecimal overdueFine23;

    private BigDecimal renewAmt23;

    private BigDecimal countTerm23;

    private BigDecimal notPayTerm24;

    private BigDecimal overdueFine24;

    private BigDecimal renewAmt24;

    private BigDecimal countTerm24;

    private BigDecimal notPayTerm25;

    private BigDecimal overdueFine25;

    private BigDecimal renewAmt25;

    private BigDecimal countTerm25;

    private BigDecimal notPayTerm26;

    private BigDecimal overdueFine26;

    private BigDecimal renewAmt26;

    private BigDecimal countTerm26;

    private BigDecimal notPayTerm27;

    private BigDecimal overdueFine27;

    private BigDecimal renewAmt27;

    private BigDecimal countTerm27;

    private BigDecimal notPayTerm28;

    private BigDecimal overdueFine28;

    private BigDecimal renewAmt28;

    private BigDecimal countTerm28;

    private BigDecimal notPayTerm29;

    private BigDecimal overdueFine29;

    private BigDecimal renewAmt29;

    private BigDecimal countTerm29;

    private BigDecimal notPayTerm30;

    private BigDecimal overdueFine30;

    private BigDecimal renewAmt30;

    private BigDecimal countTerm30;

    private String depositFreeType;
    private String trafficType;
    private String financialSolutions;
    private String applicationName;
    private String equipmentState;
    private String supervisedMachine;
    private String machineType;
}
