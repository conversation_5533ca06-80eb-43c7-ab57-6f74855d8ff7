package qnvip.data.overview.domain.alarm;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

/**
 * create by gw on 2021/12/29
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_alarm_record")
public class AlarmRecordDO extends BaseDO {

    @ApiModelProperty(value = "常量key")
    private String constKey;

    @ApiModelProperty(value = "统计时间点")
    private Integer countHour;

    @ApiModelProperty(value = "正常范围值")
    private String normalInterval;

    @ApiModelProperty(value = "实际值")
    private BigDecimal actVal;

    @ApiModelProperty(value = "指标：0没有参考范围,1正常，2低于范围，3超出范围")
    private Integer result;

}