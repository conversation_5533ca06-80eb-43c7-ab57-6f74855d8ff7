package qnvip.data.overview.domain.whole.report;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_rent_report")
public class WholeRentReportDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDate countDay;

    /**
     * 分区信息,按日期分区
     */
    private String ds;

    /**
     * 下单量
     */
    private Integer orderCnt;

    /**
     * 下单人数
     */
    private Integer orderUv;

    /**
     * 信审驳回人数
     */
    private Integer auditUv;

    /**
     * 支付人数
     */
    private Integer payUv;

    /**
     * 发货人数
     */
    private Integer sendUv;

    /**
     * 签收人数
     */
    private Integer signUv;

    /**
     * 碎屏幕保障订单
     */
    private BigDecimal screenAmt;

    /**
     * 系统关闭
     */
    private Integer osClosed;

    /**
     * 用户主动关闭
     */
    private Integer userClosed;

    /**
     * 支付关闭人数
     */
    private Integer payClosed;

    /**
     * 发货总融
     */
    private BigDecimal sendFinanceAmt;

    /**
     * 采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 分流订单
     */
    private Integer merchantCnt;

    /**
     * 分流接单量
     */
    private Integer merchantReceive;

    /**
     * 分流支付订单
     */
    private Integer merchantPay;

    /**
     * 分流发货
     */
    private Integer merchantSend;

    /**
     * 分流签收
     */
    private Integer merchantSign;

    /**
     * 机审
     */
    private Integer machineAudit;

    /**
     * 48小时发货量
     */
    private Integer hour48SendCnt;

    /**
     * 电销后支付
     */
    private Integer afterCallPay;

    /**
     * 电销前支付
     */
    private Integer beforeCallPay;


    /**
     * 应还订单量
     */
    private Integer repayCnt;

    /**
     * 未逾期订单
     */
    private Integer unOverdueCnt;

    /**
     * 已逾期订单
     */
    private Integer overdueCnt;

    /**
     * 全量应还总额1
     */
    private BigDecimal wholeRepayAmt;

    /**
     * 未逾期应还总额
     */
    private BigDecimal unOverdueRepayAmt;

    /**
     * 未逾期实还总额2
     */
    private BigDecimal unOverdueRealPayAmt;

    /**
     * 已逾期应还总额
     */
    private BigDecimal overdueRepayAmt;

    /**
     * 已逾期实还总额
     */
    private BigDecimal overdueRealPayAmt;

    /**
     * 入催订单
     */
    private Integer collectionCnt;

    /**
     * 首逾
     */
    private Integer firstOverdue;

    /**
     * 首期订单
     */
    private Integer firstCnt;

    /**
     * 售前金额
     */
    private BigDecimal beforeAmt;

    /**
     * 全量应还总额xu1
     */
    private BigDecimal renewWholeRepayAmt;

    /**
     * 未逾期实还总额xu2
     */
    private BigDecimal renewUnOverdueRealPayAmt;

    /**
     * 应还订单量xu
     */
    private Integer renewRepayCnt;

    /**
     * 未逾期订单xu
     */
    private Integer renewUnOverdueCnt;

    /**
     * 首逾xu
     */
    private Integer renewFirstOverdue;

    /**
     * 首期订单xu
     */
    private Integer renewFirstCnt;

    /**
     * 售前金额xu
     */
    private BigDecimal renewBeforeAmt;

    /**
     * iphone14promax总融
     */
    private BigDecimal phone14FinanceAmt;

    /**
     * iphone14promax采购价
     */
    private BigDecimal phone14PurchasePrice;

    /**
     * iphone14promax单量
     */
    private Integer phone14Cnt;

    /**
     * 应买断订单
     */
    private Integer buyoutCnt;

    /**
     * 当日买断人数
     */
    private Integer nowBuyoutUv;

    /**
     * 7日买断
     */
    private Integer dayBuyoutCnt;

    /**
     * 28日买断
     */
    private Integer monthBuyoutCnt;

    /**
     * 续租订单
     */
    private Integer renewCnt;

    /**
     * 续租订单_上周
     */
    private Integer weekRenewCnt;

    /**
     * 归还订单
     */
    private Integer returnCnt;

    /**
     * 归还订单_上周
     */
    private Integer weekReturnCnt;

    /**
     * 当日总买断单量
     */
    private Integer nowBuyoutCnt;

    /**
     * 商户动销
     */
    private Integer merchantDynamic;

    /**
     * 商户新增
     */
    private Integer merchantNewAdd;

    /**
     * 商户总数
     */
    private Integer merchantTotal;

    /**
     * 商户动销_按月
     */
    private Integer merchantDynamicMonth;

    /**
     * 商户新增_按月
     */
    private Integer merchantNewAddMonth;

}
