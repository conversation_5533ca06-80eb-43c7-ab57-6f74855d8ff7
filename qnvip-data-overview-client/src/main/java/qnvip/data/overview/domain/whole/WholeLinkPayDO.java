package qnvip.data.overview.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_pay")
public class WholeLinkPayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 支付人数
     */
    private Long payUv;

    /**
     * T0支付量
     */
    private Long t0PayUv;

    /**
     * T1支付量
     */
    private Long t1PayUv;

    /**
     * T2支付量
     */
    private Long t2PayUv;

    /**
     * T0待支付量
     */
    private Long t0UnpaidUv;

    /**
     * T1待支付量
     */
    private Long t1UnpaidUv;

    /**
     * T2待支付量
     */
    private Long t2UnpaidUv;

    /**
     * top渠道
     */
    private Long payTop5ChannelUv;

    /**
     * top5场景
     */
    private Long payTop5SceneUv;

    /**
     * 支付宝支付量
     */
    private Long aliPayUv;

    /**
     * 微信支付量
     */
    private Long wechatPayUv;

    /**
     * 其他支付量
     */
    private Long otherPayUv;

    /**
     * 已支付关闭
     */
    private Long payCloseUv;


}
