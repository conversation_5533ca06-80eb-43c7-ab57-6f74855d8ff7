package qnvip.data.overview.domain.overdue;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_repay_performance_overdue_repay")
public class RepayPerformanceOverdueRepayDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 逾期类型 M1(1-30天) M2(2-60天) M2+(60+)
     */
    private String overdueType;

    /**
     * 统计时区 0当日1前一日2上周同期3上月同期4上季同期11-22主一期-主十二期23-31续第一期-九期
     */
    private Long statTimeZone;

    /**
     * 统计类型 1数量 0金额
     */
    private Long statType;

    /**
     * 应催项
     */
    private BigDecimal collectionItem;

    /**
     * 已还项
     */
    private BigDecimal repaiedItem;

    /**
     * 已还占比
     */
    private BigDecimal repaiedRatio;

    /**
     * 减免项
     */
    private BigDecimal reduceItem;

    /**
     * 减免占比
     */
    private BigDecimal reduceRatio;

    /**
     * 主动还租金项
     */
    private BigDecimal activeItem;

    /**
     * 主动还租金占比
     */
    private BigDecimal activeRatio;

    /**
     * 代扣还租金项
     */
    private BigDecimal withholdItem;

    /**
     * 代扣还租金占比
     */
    private BigDecimal withholdRatio;

    /**
     * 未还项
     */
    private BigDecimal unpayItem;

    /**
     * 未还占比
     */
    private BigDecimal unpayRatio;

    /**
     * 主动还买断金项
     */
    private BigDecimal activeBuyoutItem;

    /**
     * 主动还买断金占比
     */
    private BigDecimal activeBuyoutRatio;

    /**
     * 统计日
     */
    private String countDay;


}
