package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_access_sci")
public class OperateAccessSciDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 总用户数
     */
    private Long tolUsrCnt = 0L;

    /**
     * 留存用户数
     */
    private Long keepUsrCnt = 0L;

    /**
     * 留存率
     */
    private Double keepRate;

    /**
     * 支付笔数
     */
    private Long payCnt = 0L;

    /**
     * 支付GMV
     */
    private BigDecimal gmv;

    /**
     * 支付笔数
     */
    private Long payCntByDay = 0L;

    /**
     * 支付GMV
     */
    private BigDecimal gmvByDay;


    /**
     * 1: 前30日 ，2: 近30日
     */
    private Integer dateType;

    /**
     *
     */
    private Integer miniType;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OperateAccessSciDO that = (OperateAccessSciDO) o;
        return Objects.equals(dateType, that.dateType) && Objects.equals(miniType, that.miniType);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), dateType, miniType);
    }
}
