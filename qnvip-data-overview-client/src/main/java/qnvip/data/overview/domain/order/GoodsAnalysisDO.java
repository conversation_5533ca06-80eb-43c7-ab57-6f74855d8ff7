package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_goods_analysis")
public class GoodsAnalysisDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 商品id
     */
    private Long itemId;

    /**
     * 商品名称
     */
    private String name;

    /**
     * 访问人数
     */
    private Long uv = 0L;

    /**
     * 访问次数
     */
    private Long pv = 0L;

    /**
     * 平均停留时长
     */
    private BigDecimal avgKeepTime = BigDecimal.ZERO;


}
