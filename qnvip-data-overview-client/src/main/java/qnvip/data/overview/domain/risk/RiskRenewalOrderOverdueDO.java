package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_renewal_order_overdue")
public class RiskRenewalOrderOverdueDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private LocalDateTime countDay;

    /**
     * 订单id
     */
    private Long orderId;

    private Integer miniType;

    /**
     * 平台
     */
    private String platform;

    private String financeType;

    /**
     * 审核方式
     */
    private String auditType;

    /**
     * 风控等级
     */
    private String riskLevel;


    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;
    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 协议总租金
     */
    private BigDecimal rentTotal;


    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    /**
     * MOB1
     */
    private BigDecimal term1;

    /**
     * MOB2
     */
    private BigDecimal term2;

    /**
     * MOB3
     */
    private BigDecimal term3;

    /**
     * MOB4
     */
    private BigDecimal term4;

    /**
     * MOB5
     */
    private BigDecimal term5;

    /**
     * MOB6
     */
    private BigDecimal term6;

    /**
     * MOB7
     */
    private BigDecimal term7;

    /**
     * MOB8
     */
    private BigDecimal term8;

    /**
     * MOB9
     */
    private BigDecimal term9;

    /**
     * MOB10
     */
    private BigDecimal term10;

    /**
     * MOB11
     */
    private BigDecimal term11;

    /**
     * MOB12
     */
    private BigDecimal term12;

    /**
     * MOB13
     */
    private BigDecimal term13;

    /**
     * MOB14
     */
    private BigDecimal term14;

    /**
     * MOB15
     */
    private BigDecimal term15;

    /**
     * MOB16
     */
    private BigDecimal term16;

    /**
     * MOB17
     */
    private BigDecimal term17;

    /**
     * MOB18
     */
    private BigDecimal term18;

    /**
     * 第一期是否逾期
     */
    private Integer isOverdue1;

    /**
     * 第二期是否逾期
     */
    private Integer isOverdue2;

    /**
     * 第三期是否逾期
     */
    private Integer isOverdue3;

    /**
     * 第四期是否逾期
     */
    private Integer isOverdue4;

    /**
     * 第五期是否逾期
     */
    private Integer isOverdue5;

    /**
     * 第六期是否逾期
     */
    private Integer isOverdue6;

    /**
     * 第七期是否逾期
     */
    private Integer isOverdue7;

    /**
     * 第八期是否逾期
     */
    private Integer isOverdue8;

    /**
     * 第九期是否逾期
     */
    private Integer isOverdue9;

    /**
     * 第十期是否逾期
     */
    private Integer isOverdue10;

    /**
     * 第11期是否逾期
     */
    private Integer isOverdue11;

    /**
     * 第12期是否逾期
     */
    private Integer isOverdue12;

    /**
     * 第13期是否逾期
     */
    private Integer isOverdue13;

    /**
     * 第14期是否逾期
     */
    private Integer isOverdue14;

    /**
     * 第15期是否逾期
     */
    private Integer isOverdue15;

    /**
     * 第16期是否逾期
     */
    private Integer isOverdue16;

    /**
     * 第17期是否逾期
     */
    private Integer isOverdue17;

    /**
     * 第18期是否逾期
     */
    private Integer isOverdue18;

    /**
     * 1期逾期滞纳金
     */
    private BigDecimal overdueFine1;

    /**
     * 2期逾期滞纳金
     */
    private BigDecimal overdueFine2;

    /**
     * 3期逾期滞纳金
     */
    private BigDecimal overdueFine3;

    /**
     * 4期逾期滞纳金
     */
    private BigDecimal overdueFine4;

    /**
     * 5期逾期滞纳金
     */
    private BigDecimal overdueFine5;

    /**
     * 6期逾期滞纳金
     */
    private BigDecimal overdueFine6;

    /**
     * 7期逾期滞纳金
     */
    private BigDecimal overdueFine7;

    /**
     * 8期逾期滞纳金
     */
    private BigDecimal overdueFine8;

    /**
     * 9期逾期滞纳金
     */
    private BigDecimal overdueFine9;

    /**
     * 10期逾期滞纳金
     */
    private BigDecimal overdueFine10;

    /**
     * 11期逾期滞纳金
     */
    private BigDecimal overdueFine11;

    /**
     * 12期逾期滞纳金
     */
    private BigDecimal overdueFine12;

    /**
     * 13期逾期滞纳金
     */
    private BigDecimal overdueFine13;

    /**
     * 14期逾期滞纳金
     */
    private BigDecimal overdueFine14;

    /**
     * 15期逾期滞纳金
     */
    private BigDecimal overdueFine15;

    /**
     * 16期逾期滞纳金
     */
    private BigDecimal overdueFine16;

    /**
     * 17期逾期滞纳金
     */
    private BigDecimal overdueFine17;

    /**
     * 18期逾期滞纳金
     */
    private BigDecimal overdueFine18;

    /**
     * 折扣返还金额
     */
    private BigDecimal discountReturnAmt;

    /**
     * 未使用售前优惠金额
     */
    private BigDecimal beforeDiscount;

    /**
     * 保证金比
     */
    private Double bondRate;

    /**
     * 逾期天数
     */
    private Integer overdueDay;

    /**
     * 父订单是否结清
     */
    private Integer isSettle;

    /**
     * 父订单是否结清
     */
    private Integer renewWay;

    /**
     * 最大逾期天数
     */
    private Integer maxDay;


    /**
     * 最大逾期天数
     */
    private String no;
}
