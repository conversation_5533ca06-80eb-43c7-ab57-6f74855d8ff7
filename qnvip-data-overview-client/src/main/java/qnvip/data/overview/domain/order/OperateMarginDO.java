package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_margin")
public class OperateMarginDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 区间
     */
    private Integer level;

    /**
     * 下单量
     */
    private Long orderCount= 0L;

    /**
     * 通审单量
     */
    private Long riskPassCount= 0L;

    /**
     * 支付单量
     */
    private Long payCount= 0L;


}
