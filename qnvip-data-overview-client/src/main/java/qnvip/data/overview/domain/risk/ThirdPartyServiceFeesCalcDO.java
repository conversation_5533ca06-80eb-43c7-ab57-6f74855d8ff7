package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_third_party_service_fees_calc")
public class ThirdPartyServiceFeesCalcDO extends BaseDO {

    /**
     * 三方业务
     */
    private String company;

    /**
     * 三方主体
     */
    private String username;

    /**
     * 第三方服务接口名(福建征信、青年优品征信、均信上报为自定义名称)
     */
    private String serviceCode;

    /**
     * 第三方服务名称
     */
    private String serviceName;

    /**
     * 计费类型 1:查询计费、2:查得计费
     */
    private BigDecimal costType;

    /**
     * 价格
     */
    private BigDecimal price;

    /**
     * 租赁-查询量|查得量
     */
    private BigDecimal rentCnt;

    /**
     * 租赁-费用
     */
    private BigDecimal rentCost;

    /**
     * 分期-查询量|查得量
     */
    private BigDecimal shCnt;

    /**
     * 分期-费用
     */
    private BigDecimal shCost;

    /**
     * 不明-查询量|查得量
     */
    private BigDecimal unknownCnt;

    /**
     * 不明-费用
     */
    private BigDecimal unknownCost;

    /**
     * 九九租-查询量|查得量
     */
    private BigDecimal jjzCnt;

    /**
     * 九九租-费用
     */
    private BigDecimal jjzCost;

    /**
     * 公户-查询量|查得量
     */
    private BigDecimal coTenentCnt;

    /**
     * 公户-费用
     */
    private BigDecimal coTenentCost;

    /**
     * 统计月
     */
    private String countMonth;

    /**
     * 本地费用
     */
    private BigDecimal totalCost;

    /**
     * 本地数量
     */
    private BigDecimal totalCnt;

    /**
     * 总计
     */
    @TableField(exist = false)
    private BigDecimal sumTotalCost;


}
