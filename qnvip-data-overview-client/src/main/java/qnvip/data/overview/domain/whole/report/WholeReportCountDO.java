package qnvip.data.overview.domain.whole.report;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_report_count")
public class WholeReportCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;


    /**
     * 统计方式 0:按周,1:按月
     */
    private Integer countType;

    /**
     *  模块:租赁=1,分期=2,催收&发诉=3
     */
    private Integer model;
    /**
     * code
     */
    private String code;

    /**
     * 上上周
     */
    private BigDecimal beforeLastWeek;

    /**
     * 上周
     */
    private BigDecimal lastWeek;

    /**
     * 本周
     */
    private BigDecimal thisWeek;

    /**
     * 环比
     */
    private String rate;


}
