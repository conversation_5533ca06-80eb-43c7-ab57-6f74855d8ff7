package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_renew_overdue_base")
public class RiskRenewOverdueBaseDO extends RiskBaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 主订单ID
     */
    private Long parentId;

    /**
     * 是否自动续租
     */
    private Integer autoRenewal;

    /**
     * 结清日期
     */
    private LocalDateTime settleDate;

}
