package qnvip.data.overview.domain.whole.report;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_merchant_report")
public class WholeMerchantReportDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDate countDay;

    /**
     * 分区信息,按日期分区
     */
    private String ds;

    /**
     * 下单量
     */
    private Integer orderCnt;

    /**
     * 下单人数
     */
    private Integer orderUv;

    /**
     * 通审单量
     */
    private Integer auditCnt;

    /**
     * 通审人数
     */
    private Integer auditUv;
    /**
     * 全额订单过审人数
     */
    private Integer wholeAuditUv;

    /**
     * 全额订单过审单数
     */
    private Integer wholeAuditCnt;

    /**
     * 差额订单过审人数
     */
    private Integer marginAuditUv;

    /**
     * 差额订单过审人数
     */
    private Integer marginAuditCnt;
    /**
     * 支付人数
     */
    private Integer payUv;

    /**
     * 支付单数
     */
    private Integer payCnt;

    /**
     * 全额支付人数
     */
    private Integer wholePayUv;

    /**
     * 全额支付单数
     */
    private Integer wholePayCnt;

    /**
     * 差额支付人数
     */
    private Integer marginPayUv;

    /**
     * 差额支付单数
     */
    private Integer marginPayCnt;

    /**
     * 信审关闭单数
     */
    private Integer creditClose;

    /**
     * 发货量
     */
    private Integer sendCnt;

    /**
     * 总采购价
     */
    private BigDecimal purchasePrice;

    /**
     * 总业务额
     */
    private BigDecimal financeAmt;

    /**
     * 48小时发货量
     */
    private Integer hour48SendCnt;

    /**
     * 碎屏险
     */
    private BigDecimal insuranceAmount;

    /**
     * 全款下单人数
     */
    private BigDecimal fullPaymentOrderUv;

    /**
     * 全款支付人数
     */
    private BigDecimal fullPaymentPayUv;


}
