package qnvip.data.overview.domain.order;

import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_goods")
public class OperateGoodsDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 商品id
     */
    private Long itemId= 0L;

    /**
     * 主分类id
     */
    private String mainCategory;

    /**
     * 主分类名称
     */
    private String mainCategoryName;

    /**
     * 所属二级分类id集合
     */
    private String firstCategoryIds;

    /**
     * 所属二级分类名称集合
     */
    private String firstCategoryNames;

    /**
     * 所属三级分类id集合
     */
    private String secondCategoryIds;

    /**
     * 所属三级分类名称集合
     */
    private String secondCategoryNames;

    /**
     * 商品名称
     */
    private String goodsName;

    /**
     * 商品规格
     */
    private String model;

    /**
     * 租期
     */
    private Long totalTerm= 0L;

    /**
     * 商品总价值
     */
    private BigDecimal totalWorth;

    /**
     * 进货价
     */
    private BigDecimal totalSupplyPrice;

    /**
     * 采购价
     */
    private BigDecimal totalBuyPrice;

    /**
     * 支付单量
     */
    private Long payCount= 0L;

    /**
     * 下单人数
     */
    private Long orderCount= 0L;

    /**
     * 风控通过人数
     */
    private Long approvedCount= 0L;

    /**
     * 签收单数
     */
    private Long signCount= 0L;


}
