package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_access_core")
public class AccessCoreDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty("统计日期")
    private LocalDateTime countDay;


    @ApiModelProperty("访问渠道")
    private Integer miniType;


    @ApiModelProperty("访问小程序的人数")
    private Long uv = 0L;


    @ApiModelProperty("访问小程序的次数")
    private Long pv = 0L;


    @ApiModelProperty("新增访问用户数")
    private Long uvNew = 0L;


    @ApiModelProperty("复访用户数")
    private Long uvOld = 0L;


    @ApiModelProperty("商品访客数")
    private Long goodsUv = 0L;


    @ApiModelProperty("总浏览量")
    private Long totalCapitalBrowsing = 0L;


    @ApiModelProperty("总停留时长")
    private BigDecimal totalKeepTime = BigDecimal.ZERO;


    @ApiModelProperty("总跳失数")
    private Long totalJumpLoss = 0L;
}
