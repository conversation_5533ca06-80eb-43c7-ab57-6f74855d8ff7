package qnvip.data.overview.domain.risk;

import java.io.Serializable;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
* Created by zhanglei on 2023-10-09
*/
@Data
@TableName("dataview_risk_merchant_order_info")
@ApiModel(value="DataviewRiskMerchantOrderInfoDO对象", description="分期风控大盘-数据报表-20231008")
public class DataviewRiskMerchantOrderInfoDO implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "人行时间")
    private LocalDateTime updateTime;
    @ApiModelProperty(value = "订单号")
    @TableId(value = "order_no", type = IdType.INPUT)
    private String orderNo;
    @ApiModelProperty(value = "人行时间")
    private LocalDateTime rhTime;

    @ApiModelProperty(value = "分期订单创建时间")
    private LocalDateTime shCreateTime;

    @ApiModelProperty(value = "分期订单更新时间")
    private LocalDateTime shUpdateTime;

    @ApiModelProperty(value = "风控订单创建时间")
    private LocalDateTime riskCreateTime;

    @ApiModelProperty(value = "风控订单更新时间")
    private LocalDateTime riskUpdateTime;

    @ApiModelProperty(value = "分期发货时间")
    private LocalDateTime shSendTime;

    @ApiModelProperty(value = "风控发货时间")
    private String riskSendTime;

    @ApiModelProperty(value = "保证金支付时间时间")
    private LocalDateTime cashDepositPayTime;

    @ApiModelProperty(value = "风控支付时间时间")
    private LocalDateTime riskPayTime;


    @ApiModelProperty(value = "风控客户id")
    private Long riskCustomerId;

    @ApiModelProperty(value = "租赁客户id")
    private Long shCustomerId;

    @ApiModelProperty(value = "分期商户id")
    private String shMerchantUid;

    @ApiModelProperty(value = "风控商户id")
    private Long riskMerchantId;

    @ApiModelProperty(value = "订单实际状态")
    private Integer shOrderStatus;

    @ApiModelProperty(value = "关闭时间, 支付单量有用")
    private LocalDateTime shClosingTime;

    @ApiModelProperty(value = "风控关闭订单状态0:否、1:是")
    private Integer riskCloseStatus;

    @ApiModelProperty(value = "应付差额")
    private BigDecimal creditDifferenceAmt;

    @ApiModelProperty(value = "还款期数,等于一是全款")
    private Integer repaymentPeriodCount;

    @ApiModelProperty(value = "风控渠道")
    private Integer riskBusinessChannel;

    @ApiModelProperty(value = "来源小程序")
    private Integer shMiniType;

    @ApiModelProperty(value = "sh_equipment_type")
    private Integer shEquipmentType;

    @ApiModelProperty(value = "监管机类型 0-非监管机 1-监管机 2-非监管机转监管机 3-监管机转非监管机")
    private Integer riskSupervisoryMachine;

    @ApiModelProperty(value = "10基本准入-信用20基本准入-反欺诈30反欺诈策略 40人行风控50三方60一级审批策略70支付80信审90三级审批策略100四级审批策略")
    private Integer riskFicoStatus;

    @ApiModelProperty(value = "人工审核状态 1=待审核 5=查征信 10=审核通过 15=审核不通过")
    private Integer riskArtificialAuditStatus;

    @ApiModelProperty(value = "自动风控意见")
    private String riskOpinion;

    @ApiModelProperty(value = "来源平台,支付宝,微信....")
    private String shPlatformCode;

    @ApiModelProperty(value = "前置风控通过,(进入后置用户), 0不通过,1通过")
    private Integer frontPassFlag;

    @ApiModelProperty(value = "反欺诈通过 0不通过,1通过")
    private Integer antiFraudPassFlag;

    @ApiModelProperty(value = "一级审批通过->风控通过 0不通过,1通过")
    private Integer bigDataPassFlag;

    private String product;

    @ApiModelProperty(value = "订单额外类型：0-普通订单、10-小额电商订单")
    private Integer orderExtraType;

    @ApiModelProperty(value = "花呗编号")
    private Long hbPeriodNo;
    @ApiModelProperty(value = "风控策略")
    private String riskStrategy;

    @ApiModelProperty(value = "是否首付订单标记: 0-非; 1=是")
    private Integer downPaymentFlag;


}
