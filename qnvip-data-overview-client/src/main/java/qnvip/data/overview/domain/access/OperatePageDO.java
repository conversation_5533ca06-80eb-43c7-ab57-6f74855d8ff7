package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_page")
public class OperatePageDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 访问渠道
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 页面code
     */
    private Long enterPageCode = 0L;

    /**
     * 访问小程序的人数
     */
    private Long uv = 0L;

    /**
     * 访问小程序的人数
     */
    private Long pv = 0L;

    /**
     * 页面停留时长
     */
    private BigDecimal keepTime;


}
