package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_order_status")
public class OperateOrderStatusDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 未审核关闭
     */
    private Long unAuditClosed= 0L;


    /**
     * 审核中
     */
    private Long inAudit= 0L;


    /**
     * 审核未通过
     */
    private Long auditFailed= 0L;

    /**
     * 审核未通过关闭
     */
    private Long auditFailedClosed= 0L;

    /**
     * 审核通过关闭
     */
    private Long auditPassClosed= 0L;

    /**
     * 订单支付后关闭
     */
    private Long payClosedCount= 0L;

    /**
     * 签收后关闭
     */
    private Long signClosedCount= 0L;


}
