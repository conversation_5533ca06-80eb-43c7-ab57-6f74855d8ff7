package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_flow")
public class WholeLinkFlowDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    private String scene;
    private String platform;

    /**
     * 全量UV
     */
    private Long wholeUv;

    /**
     * 扣量后UV
     */
    private Long discountUv;

    /**
     * 未跳失uv
     */
    private Long unLossUv;

    /**
     * 跳失率
     */
    private BigDecimal lossRate;

    /**
     * h-1增长率
     */
    private BigDecimal h1Rate;

    /**
     * 地域top5比
     */
    private BigDecimal top5RegionalRate;

    /**
     * 有效uv
     */
    private Long validUv;

    /**
     * 新用户uv
     */
    private Long newUv;

    /**
     * 8m未下单uv
     */
    private Long unOrderUv;

    /**
     * 有效年龄uv
     */
    private Long validAgeUv;

    /**
     * 男性用户uv
     */
    private Long maleUv;

    /**
     * 有效性别人数
     */
    private Long validGenderUv;
    /**
     * top5地域uv
     */
    private Long top5RegionalUv;

    /**
     * 地域有效uv
     */
    private Long validMessageUv;
    /**
     * 活跃uv
     */
    private Long activeUv;

    /**
     * 复访uv
     */
    private Long repeatVisitUv;


    /**
     * h-1有效uv
     */
    private Long validUvH1;
    /**
     * h-2有效uv
     */
    private Long validUvH2;

    /**
     * 男性用户占比
     */
    private BigDecimal maleRate;

    /**
     * 有效年龄占比
     */
    private BigDecimal validAgeRate;

    /**
     * 新用户uv占比
     */
    private BigDecimal newUvRate;

    /**
     * 复访占比
     */
    private BigDecimal repeatVisitRate;

    /**
     * 活跃占比
     */
    private BigDecimal activeRate;

}
