package qnvip.data.overview.domain.mongo;

import lombok.Data;
import lombok.experimental.Accessors;
import org.springframework.data.mongodb.core.mapping.Document;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Document(collection = "whole_repay_log")
@Accessors(chain = true)
public class WholeRepayMongoDO implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 还款日
     */
    private LocalDate countDay;

    /**
     * 还款月
     */
    private String countMonth;

    /**
     * mini_type
     */
    private Integer miniType;

    /**
     * 业务类型 0:租赁,1:分期
     */
    private Integer businessType;

    /**
     * 订单类型 0:主订单 1:续租订单
     */
    private Integer orderType;

    /**
     * 回款类型 0:单量,1:金额
     */
    private Integer repayType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 导流商
     */
    private String quotientName;


    /**
     * 应回款单量/金额
     */
    private BigDecimal repay;

    /**
     * 提前回款(代扣)单量/金额
     */
    private BigDecimal aheadWithhold;

    /**
     * 提前回款(主动支付)单量/金额
     */
    private BigDecimal aheadActive;

    /**
     * 当日回款单量/金额
     */
    private BigDecimal t0Repay;

    /**
     * t1回款单量/金额
     */
    private BigDecimal t1Repay;

    /**
     * t2回款单量/金额
     */
    private BigDecimal t2Repay;

    /**
     * t3回款单量/金额
     */
    private BigDecimal t3Repay;

    /**
     * t4回款单量/金额
     */
    private BigDecimal t4Repay;

    /**
     * t5回款单量/金额
     */
    private BigDecimal t5Repay;

    /**
     * t6回款单量/金额
     */
    private BigDecimal t6Repay;

    /**
     * t7回款单量/金额
     */
    private BigDecimal t7Repay;

    /**
     * t8回款单量/金额
     */
    private BigDecimal t8Repay;

    /**
     * t9回款单量/金额
     */
    private BigDecimal t9Repay;

    /**
     * t10回款单量/金额
     */
    private BigDecimal t10Repay;

    /**
     * t11回款单量/金额
     */
    private BigDecimal t11Repay;

    /**
     * t12回款单量/金额
     */
    private BigDecimal t12Repay;

    /**
     * t13回款单量/金额
     */
    private BigDecimal t13Repay;

    /**
     * t14回款单量/金额
     */
    private BigDecimal t14Repay;

    /**
     * t15回款单量/金额
     */
    private BigDecimal t15Repay;

    /**
     * t16回款单量/金额
     */
    private BigDecimal t16Repay;

    /**
     * t17回款单量/金额
     */
    private BigDecimal t17Repay;

    /**
     * t18回款单量/金额
     */
    private BigDecimal t18Repay;

    /**
     * t19回款单量/金额
     */
    private BigDecimal t19Repay;

    /**
     * t20回款单量/金额
     */
    private BigDecimal t20Repay;

    /**
     * t21回款单量/金额
     */
    private BigDecimal t21Repay;

    /**
     * t22回款单量/金额
     */
    private BigDecimal t22Repay;

    /**
     * t23回款单量/金额
     */
    private BigDecimal t23Repay;

    /**
     * t24回款单量/金额
     */
    private BigDecimal t24Repay;

    /**
     * t25回款单量/金额
     */
    private BigDecimal t25Repay;

    /**
     * t26回款单量/金额
     */
    private BigDecimal t26Repay;

    /**
     * t27回款单量/金额
     */
    private BigDecimal t27Repay;

    /**
     * t28回款单量/金额
     */
    private BigDecimal t28Repay;

    /**
     * t29回款单量/金额
     */
    private BigDecimal t29Repay;

    /**
     * t30回款单量/金额
     */
    private BigDecimal t30Repay;

    /**
     * t30回款单量/金额
     */
    private BigDecimal m2Repay;

}
