package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
    @TableName("dataview_track_participate_activities")
public class TrackParticipateActivitiesDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 活动ID
     */
    private String activityId;
}
