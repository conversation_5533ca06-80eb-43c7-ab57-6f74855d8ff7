package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_order_forecast")
public class OperateOrderForecastDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 时间
     */
    private Integer hourTime;

    /**
     * 下单人数
     */
    private Long orderUvCount;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 业务类型 1:租赁,2:分期
     */

    private Integer bizType;

    /**
     * 统计类型 1:人数,2:单量 注意:租赁只统计人数
     */
    private Integer countType;


}
