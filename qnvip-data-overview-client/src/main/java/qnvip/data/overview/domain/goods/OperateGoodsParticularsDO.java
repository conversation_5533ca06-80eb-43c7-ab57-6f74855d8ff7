package qnvip.data.overview.domain.goods;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import java.util.Objects;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_goods_particulars")
public class OperateGoodsParticularsDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 商品简称
     */
    private String name;

    /**
     * 颜色
     */
    private String color;

    /**
     * 规格
     */
    private String specification;

    /**
     * uv
     */
    private Long uv= 0L;

    /**
     * 下单量
     */
    private Long orderCount= 0L;

    /**
     * 通审单量
     */
    private Long riskPassCount= 0L;

    /**
     * 支付单量
     */
    private Long payCount= 0L;

    /**
     * 发货单里
     */
    private Long sendCount= 0L;

    /**
     * 签收单量
     */
    private Long signCount= 0L;

    /**
     * 时间
     */
    private LocalDateTime goodsTime;

    /**
     * 类型 1：上架 2：下架 3：全部
     */
    private Integer type;

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        if (!super.equals(o)) return false;
        OperateGoodsParticularsDO that = (OperateGoodsParticularsDO) o;
        return Objects.equals(name, that.name) && Objects.equals(color, that.color) && Objects.equals(specification, that.specification) && Objects.equals(goodsTime, that.goodsTime) && Objects.equals(type, that.type);
    }

    @Override
    public int hashCode() {
        return Objects.hash(super.hashCode(), name, color, specification, goodsTime, type);
    }
}
