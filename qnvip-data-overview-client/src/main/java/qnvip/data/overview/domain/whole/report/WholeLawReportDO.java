package qnvip.data.overview.domain.whole.report;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_law_report")
public class WholeLawReportDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDate countDay;

    /**
     * 分区信息,按日期分区
     */
    private String ds;

    /**
     * 类型 周报,月报
     */
    private Integer type;

    /**
     * 应还订单量
     */
    private Integer repayCnt;

    /**
     * 应还订单量_总
     */
    private Integer totalRepayCnt;

    /**
     * 应还租金
     */
    private BigDecimal repayAmt;

    /**
     * 应还租金_总
     */
    private BigDecimal totalRepayAmt;

    /**
     * 已履约订单
     */
    private Integer unOverdueCnt;

    /**
     * 已履约订单_总
     */
    private Integer totalUnOverdueCnt;

    /**
     * 已逾期订单
     */
    private Integer overdueCnt;

    /**
     * 全量应还总额
     */
    private BigDecimal totalAmt;

    /**
     * 未逾期应还总额
     */
    private BigDecimal unOverdueRepayAmt;

    /**
     * 当天实还租金
     */
    private BigDecimal unOverdueRealPayAmt;

    /**
     * 当天实还租金_总
     */
    private BigDecimal totalUnOverdueRealPayAmt;

    /**
     * 已逾期应还总额(催收分配金额)
     */
    private BigDecimal overdueRepayAmt;

    /**
     * 已逾期实还总额
     */
    private BigDecimal overdueRealPayAmt;

    /**
     * 入催订单
     */
    private Integer collectionCnt;

    /**
     * 首逾订单
     */
    private Integer firstOverdueCnt;

    /**
     * 首期订单
     */
    private Integer firstCnt;

    /**
     * 售前金额
     */
    private BigDecimal beforeAmt;

    /**
     * 应还租金_续租
     */
    private BigDecimal renewRepayAmt;

    /**
     * 全量应还总额_续租
     */
    private BigDecimal renewWholeRepayAmt;

    /**
     * 当天实还租金_续租
     */
    private BigDecimal renewUnOverdueRealPayAmt;

    /**
     * 应还订单量_续租
     */
    private Integer renewRepayCnt;

    /**
     * 已履约订单_续租
     */
    private Integer renewUnOverdueCnt;

    /**
     * 首逾_续租
     */
    private Integer renewFirstOverdueCnt;

    /**
     * 首期订单_续租
     */
    private Integer renewFirstCnt;

    /**
     * 售前金额_续租
     */
    private BigDecimal renewBeforeAmt;

    /**
     * 逾期催收订单
     */
    private Integer overdueCollectionCnt;

    /**
     * 已逾期催收总额
     */
    private BigDecimal overdueCollectionAmt;

    /**
     * 催收回款总额
     */
    private BigDecimal overdueTotalAmt;

    /**
     * 7天催回金额
     */
    private BigDecimal totalDay7ReturnAmt;

    /**
     * 7天催回订单
     */
    private Integer totalDay7ReturnCnt;

    /**
     * 30天催回金额
     */
    private BigDecimal totalDay30ReturnAmt;

    /**
     * 30天催回订单
     */
    private Integer totalDay30ReturnCnt;

    /**
     * M2催回金额
     */
    private BigDecimal month2ReturnAmt;

    /**
     * M2催回订单
     */
    private Integer month2ReturnCnt;

    /**
     * 7天催回金额主
     */
    private BigDecimal day7ReturnAmt;

    /**
     * 30天催回金额主
     */
    private BigDecimal day30ReturnAmt;

    /**
     * 7天催回金额续
     */
    private BigDecimal renewDay7ReturnAmt;

    /**
     * 30天催回金额续
     */
    private BigDecimal renewDay30ReturnAmt;

    /**
     * 本周7天应催订单
     */
    private Integer thisDay7ShouldCnt;

    /**
     * 本周7天催回订单
     */
    private Integer thisDay7ReturnCnt;

    /**
     * 本周30天应催金额
     */
    private BigDecimal thisDay30ShouldAmt;

    /**
     * 本周30天催回金额
     */
    private BigDecimal thisDay30ReturnAmt;

    /**
     * 上周7天应催订单
     */
    private Integer lastDay7ShouldCnt;

    /**
     * 上周7天催回订单
     */
    private Integer lastDay7ReturnCnt;

    /**
     * 上周30天应催金额
     */
    private BigDecimal lastDay30ShouldAmt;

    /**
     * 上周30天催回金额
     */
    private BigDecimal lastDay30ReturnAmt;

    /**
     * 不良处理总应收款
     */
    private BigDecimal harmfulTotalAmt;

    /**
     * 不良处理本周新增应收
     */
    private BigDecimal harmfulAddAmt;

    /**
     * 不良处理本周追回金额
     */
    private BigDecimal harmfulBackAmt;

}
