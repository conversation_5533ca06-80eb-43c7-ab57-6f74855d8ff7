package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_finance_merchant_order_detail")
public class FinanceMerchantOrderDetailDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime rentStartDate;

    /**
     * 到期日
     */
    private LocalDateTime rentEndDate;

    /**
     * 商品品类
     */
    private String firstCategory;
    /**
     * 二级品类
     */
    private String secondCategory;
    /**
     * 碎屏险
     */
    private BigDecimal insuranceAmount;
    /**
     * 来源
     */
    private Integer miniType;

    /**
     * 期数
     */
    private Integer term;

    /**
     * 分区
     */
    private String ds;

    /**
     * 方案类型
     */
    private Integer financeType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 用户姓名
     */
    private String customerName;

    /**
     * 用户身份证号
     */
    private String customerIdCardNo;

    /**
     * 用户手机号
     */
    private String customerMobile;

    /**
     * 订单状态
     */
    private Integer orderStatus;

    /**
     * 真实采购价
     */
    private BigDecimal actSupplyPrice;

    /**
     * 保证金
     */
    private BigDecimal bondAmt;

    /**
     * 总租金
     */
    private BigDecimal totalRent;


    /**
     * 差价
     */
    private BigDecimal creditDifferenceAmt;

    /**
     * 售前优惠
     */
    private BigDecimal beforeDiscount;

    /**
     * 已收月租金
     */
    private BigDecimal actRentAmt;

    /**
     * 保证金抵扣租金
     */
    private BigDecimal bondDeductRent;

    /**
     * 租后优惠
     */
    private BigDecimal afterDiscount;

    /**
     * 剩余应收总租金
     */
    private BigDecimal surplusRentAmt;

    /**
     * 逾期剩余总租金
     */
    private BigDecimal overdueSurplusRentAmt;

    /**
     * 是否逾期 0否，1是
     */
    private Integer overdueStatus;

    /**
     * 未退还保证金
     */
    private BigDecimal noRefundBondAmt;

    /**
     * 逾期滞纳金
     */
    private BigDecimal overdueFine;

    /**
     * 实付溢价金
     */
    private BigDecimal premiumRealAmt;
    /**
     * 退款溢价金
     */
    private BigDecimal premiumRefundAmt;


}
