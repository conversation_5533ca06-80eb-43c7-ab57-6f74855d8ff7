package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/8 14:05
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_order_day_bill")
public class OrderDayBillDO extends BaseDO implements Cloneable {
    @ApiModelProperty("实际支付时间")
    private LocalDateTime countDay;
    @ApiModelProperty("实际支付金额")
    private BigDecimal realPayAmount;
    @ApiModelProperty("平台")
    private Integer miniType;
    @ApiModelProperty("资金类型")
    private Integer type;
    @ApiModelProperty("扩展字段")
    private String ext;

    @Override
    public OrderDayBillDO clone() throws CloneNotSupportedException {
        return (OrderDayBillDO) super.clone();
    }
}
