package qnvip.data.overview.domain.whole;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_whole_link_bigdata_pass")
public class WholeLinkBigdataPassDO extends BaseDO {
    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计小时
     */
    private Integer countHour;

    private Integer miniType;

    /**
     * 风控通过
     */
    private Long riskPassUv;

    /**
     * 风控拒绝
     */
    private Long riskRefuseUv;

    /**
     * 正在审核中
     */
    private Long riskUv;

    /**
     * 策略a通过
     */
    private Long strategyAPassUv;

    /**
     * 策略a拒绝
     */
    private Long strategyARefuseUv;

    /**
     * 策略a审核中
     */
    private Long strategyARiskUv;

    /**
     * 策略b通过
     */
    private Long strategyBPassUv;

    /**
     * 策略b拒绝
     */
    private Long strategyBRefuseUv;

    /**
     * 策略b审核中
     */
    private Long strategyBRiskUv;

    /**
     * 策略c通过
     */
    private Long strategyCPassUv;

    /**
     * 策略c拒绝
     */
    private Long strategyCRefuseUv;

    /**
     * 策略c审核中
     */
    private Long strategyCRiskUv;

    /**
     * 头部用户量
     */
    private Long headUv;

    /**
     * 腰部用户量
     */
    private Long waistUv;

    /**
     * 尾部用户量
     */
    private Long trailUv;

    /**
     * top5渠道
     */
    private Long top5ChannelUv;

    /**
     * top5场景
     */
    private Long top5SceneUv;

    /**
     * 未支付关闭关闭
     */
    private Long notPayCloseUv;

    /**
     * 阿里通过人数
     */
    private Long aliUv;

    /**
     * 阿里通过人数
     */
    private Long wechatUv;

    /**
     * 阿里通过人数
     */
    private Long otherUv;
}
