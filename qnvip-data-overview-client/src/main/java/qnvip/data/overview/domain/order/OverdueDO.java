package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

/**
 * 逾期订单数（总）
 * <AUTHOR>
 * @Date 2021/10/9 10:26 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_overdue")
public class OverdueDO extends BaseDO {
    /**
     * 统计日期
     */
    @ApiModelProperty("统计日期")
    private LocalDateTime countDay;
    /**
     * 来源平台
     */
    @ApiModelProperty("来源平台")
    private Integer miniType;
    /**
     * 当前逾期订单数
     */
    @ApiModelProperty("逾期订单数")
    private Long currentOverdue;
    /**
     * 累计逾期订单数
     */
    private Long maxOverdue;
}