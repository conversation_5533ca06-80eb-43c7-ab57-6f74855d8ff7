package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_merchant_count_gray")
public class RiskMerchantCountDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private String countMonth;

    private Integer miniType;

    private Integer overdueDay;

    /**
     * 平台
     */
    private String platform;

    /**
     * 客户性质 0:新客户,1:老客户
     */
    private Integer customerType;
    /**
     * 审核方式
     */
    @TableField(exist = false)
    private String customerTypeDesc;
    /**
     * 审核方式
     */
    @TableField(exist = false)
    private String auditTypeDesc;
    /**
     * 审核方式
     */
    private String auditType;

    /**
     * 保证金期数
     */
    private String bondTermType;

    /**
     * 风控等级
     */
    private String riskLevel;


    /**
     * 场景值
     */
    private String scene;

    /**
     * 导流商户
     */
    private String quotientName;

    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 期数
     */
    private String termType;
    /**
     * 一级品类
     */
    private String firstCategory;

    /**
     * 二级品类
     */
    private String secondCategory;

    /**
     * 评分
     */
    private Integer score;

    private BigDecimal rentTotal;

    private BigDecimal totalOrderCnt;

    private BigDecimal bondAmtTotal;

    private BigDecimal discountTotal;

    private BigDecimal bondAmt1;

    private BigDecimal discount1;

    private BigDecimal bondAmt2;

    private BigDecimal discount2;

    private BigDecimal bondAmt3;

    private BigDecimal discount3;

    private BigDecimal bondAmt4;

    private BigDecimal discount4;

    private BigDecimal bondAmt5;

    private BigDecimal discount5;

    private BigDecimal bondAmt6;

    private BigDecimal discount6;

    private BigDecimal bondAmt7;

    private BigDecimal discount7;

    private BigDecimal bondAmt8;

    private BigDecimal discount8;

    private BigDecimal bondAmt9;

    private BigDecimal discount9;

    private BigDecimal bondAmt10;

    private BigDecimal discount10;

    private BigDecimal bondAmt11;

    private BigDecimal discount11;

    private BigDecimal bondAmt12;

    private BigDecimal discount12;

    private BigDecimal notPayTerm1;

    private BigDecimal overdueFine1;

    private BigDecimal countTerm1;

    private BigDecimal notPayTerm2;

    private BigDecimal overdueFine2;

    private BigDecimal countTerm2;

    private BigDecimal notPayTerm3;

    private BigDecimal overdueFine3;

    private BigDecimal countTerm3;

    private BigDecimal notPayTerm4;

    private BigDecimal overdueFine4;

    private BigDecimal countTerm4;

    private BigDecimal notPayTerm5;

    private BigDecimal overdueFine5;

    private BigDecimal countTerm5;

    private BigDecimal notPayTerm6;

    private BigDecimal overdueFine6;

    private BigDecimal countTerm6;

    private BigDecimal notPayTerm7;

    private BigDecimal overdueFine7;

    private BigDecimal countTerm7;

    private BigDecimal notPayTerm8;

    private BigDecimal overdueFine8;

    private BigDecimal countTerm8;

    private BigDecimal notPayTerm9;

    private BigDecimal overdueFine9;

    private BigDecimal countTerm9;

    private BigDecimal notPayTerm10;

    private BigDecimal overdueFine10;

    private BigDecimal countTerm10;

    private BigDecimal notPayTerm11;

    private BigDecimal overdueFine11;

    private BigDecimal countTerm11;

    private BigDecimal notPayTerm12;

    private BigDecimal overdueFine12;

    private BigDecimal countTerm12;

    private String productType;
    private String glodType;
    private String applicationName;
    private String supervisedMachine;
    // 0 低定价 10高定价
    private int bizType;

}
