package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_access_total")
public class AccessTotalDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 访问小程序的人数
     */
    private Long uv = 0L;

    /**
     * 累计UV
     */
    private Long uvTotal = 0L;

    /**
     * 访问小程序的人数
     */
    private Long pv = 0L;

    /**
     * 累计PV
     */
    private Long pvTotal = 0L;

    /**
     * 注册量
     */
    private Long registrations = 0L;

    /**
     * 累计注册量
     */
    private Long registrationsTotal = 0L;

    @ApiModelProperty("统计日期")
    private LocalDateTime countDay;

    @ApiModelProperty("昨日新访客数")
    private Long uvNew = 0L;

    @ApiModelProperty("商品曝光数")
    private Long pvGoodsExposure = 0L;
}
