package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_rent_info_flink")
public class RiskRentInfoFlinkDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 小程序
     */
    private Integer businessChannel;

    /**
     * 是否共租 1:是,0:否   
     */
    private Integer isOnRent;

    /**
     * 导流商
     */
    private String quotientName;

    /**
     * 场景
     */
    private String scene;

    /**
     * 产品方案
     */
    private Integer financeType;

    /**
     * 客户分类 1:月新,2:半年新,3:一年新,4:非新
     */
    private Integer customerType;

    /**
     * 统计纬度 0:人数,1:单量
     */
    private Integer type;

    /**
     * 风控等级
     */
    private String riskOpinion;

    /**
     * 前置风控用户数
     */
    private Integer prepositionRiskCnt;

    /**
     * 风控用户数
     */
    private Integer riskCnt;

    /**
     * 基本准入通过数
     */
    private Integer passCnt;

    /**
     * 反欺诈通过数
     */
    private Integer antiFraudPassCnt;

    /**
     * 人行风控当日执行数
     */
    private Integer curDayCnt;

    /**
     * 人行风控执行总数
     */
    private Integer centralBankInquiryCnt;

    /**
     * 人行风控通过数
     */
    private Integer centralBankPassCnt;

    /**
     * 一级审批通过数
     */
    private Integer firstLvlPass;

    /**
     * 自营支付数(含取消)
     */
    private Integer payCnt;

    /**
     * 支付宝支付人数
     */
    private Integer aliPayCnt;

    /**
     * 微信支付人数
     */
    private Integer wechatPayCnt;

    /**
     * 其他渠道支付人数
     */
    private Integer otherPayCnt;

    /**
     * 自营支付取消数
     */
    private Integer payCloseCnt;

    /**
     * 信审通过数
     */
    private Integer creditAuditPassCnt;

    /**
     * 信审拒绝
     */
    private Integer creditAuditRefuseCnt;

    /**
     * 三级审批策略通过数
     */
    private Integer threeLvlCnt;

    /**
     * 自营发货数
     */
    private Integer sendCnt;

    /**
     * 商户分流数
     */
    private Integer merchantShuntCnt;

    /**
     * 商户接单数
     */
    private Integer merchantAcceptCnt;

    /**
     * 商户支付数
     */
    private Integer merchantPayCnt;

    /**
     * 商户支付取消数
     */
    private Integer merchantPayCloseCnt;

    /**
     * 商户发货数
     */
    private Integer merchantSendCnt;

    /**
     * 当日自营支付数
     */
    private Integer curPayCnt;

    /**
     * 当日自营取消数(支付后取消)
     */
    private Integer curPayCloseCnt;

    /**
     * 当日自营发货数
     */
    private Integer curSendCnt;

    /**
     * 当日商户分流数
     */
    private Integer curMerchantShuntCnt;

    /**
     * 当日商户接单数
     */
    private Integer curMerchantAcceptCnt;

    /**
     * 当日商户支付数
     */
    private Integer curMerchantPayCnt;

    /**
     * 当日商户支付取消数
     */
    private Integer curMerchantPayCloseCnt;

    /**
     * 当日商户发货数
     */
    private Integer curMerchantSendCnt;
}
