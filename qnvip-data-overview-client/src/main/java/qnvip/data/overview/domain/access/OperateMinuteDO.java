package qnvip.data.overview.domain.access;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_minute")
public class OperateMinuteDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 统计日期
     */
    private LocalDateTime countTime;
    /**
     * 访问渠道
     */
    private Integer miniType;

    /**
     * 访问小程序的人数
     */
    private Long uv= 0L;

    /**
     * 高质量UV
     */
    private Long uvQuality= 0L;

    /**
     * 访问小程序的次数
     */
    private Long pv= 0L;

    /**
     * 下单用户
     */
    private Long orderUv= 0L;


}
