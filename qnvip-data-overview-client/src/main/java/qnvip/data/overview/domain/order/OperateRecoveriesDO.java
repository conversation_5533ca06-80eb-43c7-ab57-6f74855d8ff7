package qnvip.data.overview.domain.order;

import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_recoveries")
public class OperateRecoveriesDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;


    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * 应回金额
     */
    private BigDecimal shouldAmt;
    /**
     * 已回金额
     */
    private BigDecimal alreadyAmt;

    /**
     * 提前还款金额
     */
    private BigDecimal aheadAmt;

    /**
     * 应回订单
     */
    private Long shouldOrderCount= 0L;

    /**
     * 已回订单
     */
    private Long alreadyOrderCount= 0L;


    /**
     * 提前订单
     */
    private Long aheadOrderCount= 0L;

}
