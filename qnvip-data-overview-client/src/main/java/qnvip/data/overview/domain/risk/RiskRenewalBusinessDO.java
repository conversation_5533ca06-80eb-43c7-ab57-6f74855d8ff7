package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_risk_renewal_business")
public class RiskRenewalBusinessDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 起租日
     */
    private LocalDateTime countDay;

    /**
     * 来源
     */
    private Integer miniType;

    /**
     * 平台
     */
    private String platform;

    /**
     * 方案类型
     */
    private Integer financeType;

    /**
     * 审核方式
     */
    private String auditType;

    /**
     * 风控等级
     */
    private String riskLevel;

    /**
     * 风控策略
     */
    private String riskStrategy;

    /**
     * 业务总单量
     */
    private Integer totalOrderCnt;

    /**
     * 业务总租金
     */
    private BigDecimal rentTotal;

    /**
     * 总售前优惠金额
     */
    private BigDecimal discountsTotal;

    /**
     * 业务总保证金
     */
    private BigDecimal bondAmtTotal;

    /**
     * 保证金比
     */
    private Double bondRate;

    /**
     * 父订单是否结清
     */
    private Integer isSettle;

    /**
     * 父订单是否结清
     */
    private Integer renewWay;


}
