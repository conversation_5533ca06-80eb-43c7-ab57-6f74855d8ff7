package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 每天23:59:59执行一次全量统计
 * <AUTHOR>
 * @Date 2021/10/10 4:28 下午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_total_order")
public class TotalOrderDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 订单数
     */
    private Long orderCount;
    /**
     * 支付单数
     */
    private Long payCount;
    /**
     * 保证金支付金额
     */
    private BigDecimal marginTotal;
    /**
     * 碎屏险支付金额
     */
    private BigDecimal insuranceAmt;
    /**
     * 配件支付金额
     */
    private BigDecimal componentPrice;
    /**
     * 发货数量
     */
    private Long deliveryCount;
    /**
     * 风控通过人数
     */
    private Long riskPassCount;

}