package qnvip.data.overview.domain.risk;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

@Data
@TableName("dataview_risk_statistical_dimension")
@ApiModel(value="StatisticalDimension对象", description="大盘-统计维度表")
public class StatisticalDimension {

    private int id;
    private int bizType;
    private String firstDimension;
    private String secondaryDimension;
    private int sortNum;
    private Date createTime;
    private Date updateTime;

}
