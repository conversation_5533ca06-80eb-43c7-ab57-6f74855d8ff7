package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_contract_click")
public class TrackContractClickDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 动作 1：进入 2：离开
     */
    private Integer actionType;

    /**
     * 停留时长，离开才会传递值
     */
    private String keepAliveTime;

    /**
     * 协议类似
     */
    private String contractType;

    /**
     * 协议名称
     */
    private String contractName;

}
