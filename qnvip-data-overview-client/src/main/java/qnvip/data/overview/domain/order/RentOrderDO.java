package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("rent_order")
public class RentOrderDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * mini_config
     */
    private Integer miniType;

    /**
     * 订单类型1=长租2=短租3=全款商品
     */
    private Integer type;

    /**
     * 业务状态 1=正常 5=已终止
     */
    private Integer termination;

    /**
     * 订单状态，详见OrderStatusEnum
     */
    private Integer status;

    /**
     * 业务类型 1 - 车辆业务 2 - 优品直购业务 3-借款业务
     */
    private Integer bizType;

    /**
     * 用户id
     */
    private Integer customerId;

    /**
     * 业务编号
     */
    private String no;

    /**
     * 入驻商户id
     */
    private Long merchantId;

    /**
     * 上一级订单id(续租用)
     */
    private Long parentId;

    /**
     * 碎屏险是否支付 0:未支付 1：已支付
     */
    private Integer isScreenRiskPayed;

    /**
     * 合同起租时间
     */
    private LocalDateTime rentStartDate;

    /**
     * 合同归还时间
     */
    private LocalDateTime rentEndDate;

    /**
     * 结清日期
     */
    private LocalDateTime settleDate;

    /**
     * 是否开具发票 0-不需要 1-需要
     */
    private Integer invoiceStatus;

    /**
     * 备注
     */
    private String remark;

    /**
     * 周期 1=旬/期, 2=月/期 3=日/期
     */
    private Integer termType;

    /**
     * 业务模式类型1=委托租赁2=售后回租3=直接租赁
     */
    private Integer rentType;

    /**
     * 是否上报人行0=不上报5=上报
     */
    private Integer rhReport;

    /**
     * 付款方式1=全款2=分期
     */
    private Integer paymentType;

    /**
     * （商城订单）支付时间 （订单首次保证金支付时间）
     */
    private LocalDateTime paymentTime;

    /**
     * 是否有转寄，0：无，1：有
     */
    private Integer forwardFlag;

    /**
     * 转寄时间
     */
    private LocalDateTime forwardTime;

    /**
     * 延期归还天数
     */
    private Integer orderDelayDays;

    /**
     * 区块链全局事务id
     */
    private String transactionId;

    /**
     * 是否上报租赁宝 1=是 2=否
     */
    private Integer reportTwc;

    /**
     * 上报租赁宝时间
     */
    private LocalDateTime reportTwcTime;

    /**
     * 场景值
     */
    private String scene;

    /**
     * 是否租转售 0=否 1=是
     */
    private Integer resaleFlag;

    /**
     * 租转售时间
     */
    private LocalDateTime resaleTime;

    /**
     * 订单转让标记，0未转让，1订单转让给优品商城
     */
    private Integer transferFlag;

    /**
     * 续租时间
     */
    private LocalDateTime renewTime;


}
