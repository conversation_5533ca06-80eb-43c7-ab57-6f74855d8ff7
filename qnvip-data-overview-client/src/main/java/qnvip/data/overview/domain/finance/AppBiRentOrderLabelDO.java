package qnvip.data.overview.domain.finance;

import com.baomidou.mybatisplus.annotation.TableName;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2023-08-30
*/
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("app_bi_rent_order_label")
@ApiModel(value="AppBiRentOrderLabelDO对象", description="BI-租赁订单标签表")
public class AppBiRentOrderLabelDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    @ApiModelProperty(value = "订单号")
    private String orderNo;

    @ApiModelProperty(value = "下单时间")
    private String orderTime;

    @ApiModelProperty(value = "起租日")
    private String rentStartDate;

    @ApiModelProperty(value = "支付时间")
    private String paymentTime;

    @ApiModelProperty(value = "发货时间")
    private String sendTime;

    @ApiModelProperty(value = "业务审核状态：1：通过，2：拒绝")
    private String businessAuditStatus;

    @ApiModelProperty(value = "订单一级归属（1：CPS_C，2：CPS_B，3：分销_B，4：分销_C，5：私域，6：支付宝公域,7:微信，8：抖音，9：APP）")
    private Integer firstOrderBelonging;

    @ApiModelProperty(value = "订单状态")
    private Integer orderStatus;

    @ApiModelProperty(value = "商户ID")
    private Integer merchantId;

    @ApiModelProperty(value = "下单共租标记 5:无租中 10:只有自营租用中订单 20:只有商户租用中订单 30:自营商户都有租用中订单")
    private Integer commonRentFlag;

    @ApiModelProperty(value = "业务状态")
    private Integer termination;

    @ApiModelProperty(value = "订单二级归属（1：CPS_C，2：CPS_B，3：分销_B，4：分销_C，5：私域，6：支付宝公域,7:微信，8：抖音，9：APP，10：OCR认证失败，11：关闭订单挽回）")
    private Integer secondOrderBelonging;

    @ApiModelProperty(value = "优品贷龄")
    private BigDecimal qnvipAge;

    @ApiModelProperty(value = "导流商名称")
    private String quotientName;


}
