package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;
import qnvip.rent.common.base.BaseDO;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/13 11:16 上午
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_component_detail")
public class ComponentDetailDO extends BaseDO {
    /**
     * 统计日期
     */
    private LocalDateTime countDay;
    /**
     * 商品id
     */
    private Long itemId;
    /**
     * 商品名称
     */
    private String itemName;
    /**
     * 商品gmv
     */
    private BigDecimal gmv;
    /**
     * 商品下单量
     */
    private Long orderCount;
    /**
     * 支付单量
     */
    private Long payCount;
}