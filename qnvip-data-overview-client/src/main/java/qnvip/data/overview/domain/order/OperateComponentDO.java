package qnvip.data.overview.domain.order;

import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.Version;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableId;
import qnvip.rent.common.base.BaseDO;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_operate_component")
public class OperateComponentDO extends BaseDO {

    private static final long serialVersionUID = 1L;

    /**
     * 统计日期
     */
    private LocalDateTime countDay;

    /**
     * 来源平台
     */
    private Integer miniType;

    /**
     * 商户id,100表示自营，大于100表示非自营
     */
    private String merchantId;

    /**
     * gmv
     */
    private BigDecimal gmv;

    /**
     * nmv
     */
    private BigDecimal nmv;

    /**
     * 配件名称
     */
    private String name;

    /**
     * 加购配件订单量
     */
    private Long orderCount= 0L;

    /**
     * 加购配件订单人数
     */
    private Long orderUvCount= 0L;

    /**
     * 加购配件订单量支付单量
     */
    private Long orderPayCount= 0L;

    /**
     * 加购配件订单量支付人数
     */
    private Long orderPayUvCount= 0L;


}
