package qnvip.data.overview.domain.track;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
@TableName("dataview_track_page_statistics")
public class TrackPageStatisticsDO extends EventTrackDO {

    private static final long serialVersionUID = 1L;

    /**
     * 地区
     */
    private String area;

    /**
     * 城市
     */
    private String city;

    /**
     * 省
     */
    private String province;

    /**
     * 经度
     */
    private String longitude;

    /**
     * 纬度
     */
    private String latitude;

    /**
     * 性别
     */
    private String gender;

    /**
     * 页面动作 1：进入 2：离开
     */
    private Integer actionType;

    /**
     * 页面停留时长，离开才会传递值
     */
    private String keepAliveTime;


}
