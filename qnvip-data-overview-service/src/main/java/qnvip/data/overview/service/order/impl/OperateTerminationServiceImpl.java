package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.domain.order.OperateTerminationDO;
import qnvip.data.overview.mapper.order.OperateScreenRiskMapper;
import qnvip.data.overview.mapper.order.OperateTerminationMapper;
import qnvip.data.overview.service.order.OperateTerminationService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-22
 */
@Slf4j
@Service
public class OperateTerminationServiceImpl extends BaseServiceImpl<OperateTerminationMapper, OperateTerminationDO> implements OperateTerminationService {



    @Autowired
    private OperateTerminationMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }


    @Override
    public List<OperateTerminationDO> getList(String fromTime,
                                              String toTime,
                                              Integer miniType) {
        QueryWrapper<OperateTerminationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateTerminationDO::getMiniType, miniType)
                .eq(OperateTerminationDO::getMerchantId, "100")
                .between(OperateTerminationDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public boolean saveOrUpdate(OperateTerminationDO domian) {
        QueryWrapper<OperateTerminationDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateTerminationDO::getCountDay, domian.getCountDay())
                .eq(OperateTerminationDO::getMiniType, domian.getMiniType())
                .eq(OperateTerminationDO::getMerchantId, domian.getMerchantId())
                .eq(OperateTerminationDO::getReason, domian.getReason());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateTerminationDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateTerminationDO::getCountDay, domian.getCountDay())
                    .eq(OperateTerminationDO::getMiniType, domian.getMiniType())
                    .eq(OperateTerminationDO::getReason, domian.getReason())
                    .eq(OperateTerminationDO::getMerchantId, domian.getMerchantId());
            update(domian, up);
        }
        return true;
    }
}
