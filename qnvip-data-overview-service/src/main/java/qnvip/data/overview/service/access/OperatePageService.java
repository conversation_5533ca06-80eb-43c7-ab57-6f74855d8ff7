package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.OperateAccessCoreDO;
import qnvip.data.overview.domain.access.OperatePageDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-19
*/
public interface OperatePageService extends BaseService<OperatePageDO>{

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperatePageDO> getList(String fromTime, String toTime, Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);

}
