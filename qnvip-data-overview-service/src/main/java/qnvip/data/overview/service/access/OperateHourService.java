package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.OperateHourDO;
import qnvip.data.overview.domain.access.OperateMinuteDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by <PERSON><PERSON><PERSON>ong on 2021-11-18
*/
public interface OperateHourService extends BaseService<OperateHourDO>{
    public List<OperateHourDO> getList(String fromTime, String toTime, Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);


}
