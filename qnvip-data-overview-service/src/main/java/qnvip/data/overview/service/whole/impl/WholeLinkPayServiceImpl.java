package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.WholeLinkPayDO;
import qnvip.data.overview.mapper.whole.WholeLinkPayMapper;
import qnvip.data.overview.service.whole.WholeLinkPayService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkPayServiceImpl extends BaseServiceImpl<WholeLinkPayMapper, WholeLinkPayDO> implements WholeLinkPayService {

    @Resource
    private WholeLinkPayMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkPayDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkPayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkPayDO::getCountHour, hour)
                .between(WholeLinkPayDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkPayDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkPayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkPayDO::getMiniType, miniType)
                .eq(WholeLinkPayDO::getCountDay, time)
                .orderByDesc(WholeLinkPayDO::getCountHour);
        return getOne(queryWrapper, false);
    }
}
