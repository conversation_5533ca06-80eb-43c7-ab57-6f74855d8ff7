package qnvip.data.overview.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceMerchantRetailOrderDetailDO;
import qnvip.data.overview.mapper.finance.FinanceMerchantRetailOrderDetailMapper;
import qnvip.data.overview.service.finance.FinanceMerchantRetailOrderDetailService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.List;

/**
 * Created by zhengtong on 2023-06-25
 */
@Slf4j
@Service
public class FinanceMerchantRetailOrderDetailServiceImpl extends BaseServiceImpl<FinanceMerchantRetailOrderDetailMapper, FinanceMerchantRetailOrderDetailDO> implements FinanceMerchantRetailOrderDetailService {

    @Autowired
    private FinanceMerchantRetailOrderDetailMapper mapper;

    @Override
    public void removeAll() {
        mapper.deleteAll();
    }

    @Override
    public List<FinanceMerchantRetailOrderDetailDO> getList(String fromTime,
                                                            String toTime,
                                                            Integer countType,
                                                            List<Integer> miniTypes) {
        QueryWrapper<FinanceMerchantRetailOrderDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().in(CollUtil.isNotEmpty(miniTypes), FinanceMerchantRetailOrderDetailDO::getMiniType, miniTypes);
        if (countType == 1) {
            queryWrapper.lambda().between(FinanceMerchantRetailOrderDetailDO::getSendTime, fromTime, toTime);
        } else {
            queryWrapper.lambda().between(FinanceMerchantRetailOrderDetailDO::getSignTime, fromTime, toTime);
        }
        return list(queryWrapper);
    }
}
