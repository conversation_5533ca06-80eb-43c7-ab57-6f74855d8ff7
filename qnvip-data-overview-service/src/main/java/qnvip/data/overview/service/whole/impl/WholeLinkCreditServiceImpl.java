package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkCreditDO;
import qnvip.data.overview.mapper.whole.WholeLinkCreditMapper;
import qnvip.data.overview.service.whole.WholeLinkCreditService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkCreditServiceImpl extends BaseServiceImpl<WholeLinkCreditMapper, WholeLinkCreditDO> implements WholeLinkCreditService {

    @Resource
    private WholeLinkCreditMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkCreditDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkCreditDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkCreditDO::getCountHour, hour)
                .between(WholeLinkCreditDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkCreditDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkCreditDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkCreditDO::getMiniType, miniType)
                .eq(WholeLinkCreditDO::getCountDay, time)
                .orderByDesc(WholeLinkCreditDO::getCountHour);
        return getOne(queryWrapper, false);
    }
}
