package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkFraudDO;
import qnvip.data.overview.mapper.whole.WholeLinkFraudMapper;
import qnvip.data.overview.service.whole.WholeLinkFraudService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkFraudServiceImpl extends BaseServiceImpl<WholeLinkFraudMapper, WholeLinkFraudDO> implements WholeLinkFraudService {


    @Resource
    private WholeLinkFraudMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkFraudDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkFraudDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkFraudDO::getCountHour, hour)
                .between(WholeLinkFraudDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkFraudDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkFraudDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkFraudDO::getMiniType, miniType)
                .eq(WholeLinkFraudDO::getCountDay, time)
                .orderByDesc(WholeLinkFraudDO::getCountHour);
        return getOne(queryWrapper, false);
    }
}
