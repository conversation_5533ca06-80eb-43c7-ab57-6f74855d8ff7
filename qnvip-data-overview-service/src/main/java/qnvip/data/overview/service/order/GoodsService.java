package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.GoodsDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;
import java.util.List;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 8:56 下午
 */
public interface GoodsService extends BaseService<GoodsDO> {
    List<GoodsDO> listByTime(LocalDateTime sTime, LocalDateTime eTime);

    MultiResult<GoodsDO> page(String goodsName, LocalDateTime startTime, LocalDateTime endTime, int current, int pageSize);

    List<GoodsDO> list(String goodsName, LocalDateTime startTime, LocalDateTime endTime);

    List<GoodsDO> getGroupListByDate(LocalDateTime startTime, LocalDateTime endTime, Integer pageNo, Integer pageSize);

    MultiResult<GoodsDO> getGroupPageByDate(LocalDateTime startTime, LocalDateTime endTime,String mainCategory, Integer pageNo, Integer pageSize);

    GoodsDO getTotalData(LocalDateTime startTime, LocalDateTime endTime);

    List<GoodsDO> getByIdListAndTime(List<Object> idList, LocalDateTime startTime, LocalDateTime endTime);

}
