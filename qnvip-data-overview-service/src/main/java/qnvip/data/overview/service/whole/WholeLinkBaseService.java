package qnvip.data.overview.service.whole;

import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;

public interface WholeLinkBaseService<T> extends BaseService<T> {

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<T> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public T getOneByMiniType(LocalDate time, Integer miniType);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public void removeByHour(Integer hour, LocalDate now);


}
