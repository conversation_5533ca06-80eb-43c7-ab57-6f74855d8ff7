package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderConversionDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by z<PERSON><PERSON>ong on 2021-11-29
*/
public interface OperateOrderConversionService extends BaseService<OperateOrderConversionDO>{
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateOrderConversionDO> getList(String fromTime, String toTime, Integer miniType,Integer type);

    public void removeDataByTime(LocalDateTime countDay,Integer miniType);

    List<OperateOrderConversionDO> getListByMiniTypeList(String fromTime,
                                           String toTime,
                                           List<Integer> miniType,
                                           Integer type);
}
