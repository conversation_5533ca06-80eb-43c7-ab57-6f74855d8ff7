package qnvip.data.overview.service.whole;

import qnvip.data.overview.domain.mongo.WholeFlowHealthMongoDO;
import qnvip.data.overview.domain.whole.WholeLinkFlowDO;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022-07-25
 */
public interface WholeLinkFlowService extends WholeLinkBaseService<WholeLinkFlowDO> {


    /**
     * 桑基图list
     *
     * @param time     时间
     * @param miniType 迷你类型
     * @param scene    场景
     * @return {@code List<WholeLinkFlowDO>}
     */
    List<WholeLinkFlowDO> getChartList(LocalDate time, Integer miniType, String scene);

    List<WholeLinkFlowDO> getList(LocalDate start, Integer miniType, String scene);

    List<WholeFlowHealthMongoDO> getListFromMongo(LocalDate time, Integer miniType, String scene, String name);

    void save2Mongo(List<WholeFlowHealthMongoDO> list);

    void removeMongoByTime(LocalDate start);

    public List<WholeLinkFlowDO> getSceneList();


    public List<WholeLinkFlowDO> getListByTime(LocalDate fromTime, LocalDate toTime);

    public List<WholeLinkFlowDO> getListByPlatform(LocalDate fromTime, LocalDate toTime, Integer hour,
                                                   String platform, Object obj);
}
