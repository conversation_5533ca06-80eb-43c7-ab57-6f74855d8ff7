package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.OperateHourDO;
import qnvip.data.overview.domain.access.OperatePageDO;
import qnvip.data.overview.domain.access.OperatePageDO;
import qnvip.data.overview.mapper.access.OperateMinuteMapper;
import qnvip.data.overview.mapper.access.OperatePageMapper;
import qnvip.data.overview.service.access.OperatePageService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2021-11-19
 */
@Slf4j
@Service
public class OperatePageServiceImpl extends BaseServiceImpl<OperatePageMapper, OperatePageDO> implements OperatePageService {

    @Autowired
    private OperatePageMapper pageMapper;

    @Override
    public List<OperatePageDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperatePageDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperatePageDO::getMiniType, miniType)
                .ge(OperatePageDO::getCountDay, fromTime)
                .le(OperatePageDO::getCountDay, toTime);
        return list(queryWrapper);
    }

    @Override
    public boolean saveOrUpdate(OperatePageDO domian) {
        QueryWrapper<OperatePageDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperatePageDO::getCountDay, domian.getCountDay())
                .eq(OperatePageDO::getMiniType, domian.getMiniType())
                .eq(OperatePageDO::getMerchantId, domian.getMerchantId())
                .eq(OperatePageDO::getEnterPageCode, domian.getEnterPageCode());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperatePageDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperatePageDO::getCountDay, domian.getCountDay())
                    .eq(OperatePageDO::getMiniType, domian.getMiniType())
                    .eq(OperatePageDO::getMerchantId, domian.getMerchantId())
                    .eq(OperatePageDO::getEnterPageCode, domian.getEnterPageCode());
            update(domian, up);
        }
        return true;
    }

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        pageMapper.deleteByCountDay(countDay);
    }
}
