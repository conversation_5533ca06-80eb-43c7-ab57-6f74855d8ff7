package qnvip.data.overview.service.whole.report;

import qnvip.data.overview.domain.whole.WholeRepayDO;
import qnvip.data.overview.param.WholeRepayParam;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhengtong on 2023-03-06
 */
public interface WholeRepayService extends BaseService<WholeRepayDO> {

    void removeMongoDO();

    void deleteAll();

    void save2Mongo(List<WholeRepayDO> list);

    List<WholeRepayDO> getList(WholeRepayParam param, Integer repayType);

    public List<WholeRepayDO> getListFromMongo(Integer repayType, LocalDate from, LocalDate to);

    List<WholeRepayDO> getSceneList();


}
