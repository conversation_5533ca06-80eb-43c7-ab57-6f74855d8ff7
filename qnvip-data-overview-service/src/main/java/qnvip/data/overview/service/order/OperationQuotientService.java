package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperationQuotientDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.util.List;
import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022-11-08
 */
public interface OperationQuotientService extends BaseService<OperationQuotientDO> {

    void deleteAll();

    /**
     * getPageList by condition
     *
     * @return
     */
    public List<OperationQuotientDO> getSelectionList(String fromTime,
                                                      String toTime,
                                                      Integer countType
    );


    /**
     * getPageList by condition
     *
     * @return
     */
    public MultiResult<OperationQuotientDO> getPageList(String fromTime,
                                                        String toTime,
                                                        List<Integer> miniType,
                                                        List<String> platformList,
                                                        List<String> sceneList,
                                                        List<String> quotientNameList,
                                                        Integer countType,
                                                        Integer pageSize,
                                                        Integer pageNo);


    /**
     * getPageList by condition
     *
     * @return
     */
    public List<OperationQuotientDO> getList(String fromTime,
                                                 String toTime,
                                                 List<Integer> miniType,
                                                 List<String> platformList,
                                                 List<String> sceneList,
                                                 List<String> quotientNameList,
                                                 Integer countType);

    Map<String,Object> getMap(String fromTime,
                              String toTime,
                              Integer countType);

    /**
     * 获取详情页下拉框
     *
     * @param time             时间
     * @param miniTypeList     迷你类型列表
     * @param quotientList     商名单
     * @param sceneList        场景列表
     * @param riskStrategyList 风险战略列表
     * @param stage            阶段
     * @return {@code List<OperationQuotientDO>}
     */
    List<OperationQuotientDO> getTagList(String time,
                                         Integer countType,
                                         List<Integer> miniTypeList,
                                         List<String> platformList,
                                         List<String> quotientList,
                                         List<String> sceneList,
                                         List<String> riskStrategyList,
                                         List<String> stage);
}
