package qnvip.data.overview.service.whole.report;

import qnvip.data.overview.domain.whole.report.WholeMerchantReportDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;
/**
* Created by z<PERSON>gtong on 2023-02-15
*/
public interface WholeMerchantReportService extends BaseService<WholeMerchantReportDO>{
    public void removeDataByDs(String ds);

    public List<WholeMerchantReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds);
}
