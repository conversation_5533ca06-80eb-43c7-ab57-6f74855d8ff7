package qnvip.data.overview.service.track.impl;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.track.TrackCouponsClickDO;
import qnvip.data.overview.mapper.track.TrackClearMapper;
import qnvip.data.overview.service.track.TrackClearService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Map;

/**
 * create by gw on 2022/5/7
 */
@Service
@RequiredArgsConstructor
public class TrackClearServiceImpl extends BaseServiceImpl<TrackClearMapper, TrackCouponsClickDO> implements TrackClearService {

    private static final int MAX_BATCH_SIZE = 10000;
    private static final int BEFORE_DAYS = 100;

    private final TrackClearMapper trackClearMapper;


    @Override
    public void deleteByTableAndDefaultRange(String tableName) {
        LocalDateTime defaultTimeSplit = getDefaultTimeSplit();
        Map<String, Long> idMap = trackClearMapper.getMaxIdByTable(tableName);
        Long minId = idMap.get("minId");
        getLeastMinId(tableName, defaultTimeSplit, idMap);
        Long splitId = idMap.get("splitId");
        if (splitId != null) {
            deleteByCondition(tableName, minId, splitId);
        }
    }

    private void getLeastMinId(String tableName, LocalDateTime defaultTimeSplit, Map<String, Long> idMap) {
        Long maxId = idMap.get("maxId");
        Long minId = idMap.get("minId");
        if (maxId - minId > 500) {
            //二分法
            long splitId = minId + (maxId - minId) / 2;
            idMap.put("splitId", splitId);
            LocalDateTime createTimeByTableAndId = trackClearMapper.getCreateTimeByTableAndId(tableName, splitId);
            if (createTimeByTableAndId.isBefore(defaultTimeSplit)) {
                idMap.put("minId", splitId);
            } else {
                idMap.put("maxId", splitId);
            }
            getLeastMinId(tableName, defaultTimeSplit, idMap);
        }
    }


    private void deleteByCondition(String tableName, Long minId, Long splitId) {
        long currentMinId = minId + MAX_BATCH_SIZE;
        if (currentMinId <= splitId) {
            // 删除
            trackClearMapper.deleteByIdRange(tableName, currentMinId);
            deleteByCondition(tableName, currentMinId, splitId);
        } else {
            currentMinId = Math.min(currentMinId, splitId);
            trackClearMapper.deleteByIdRange(tableName, currentMinId);
        }
    }

    private LocalDateTime getDefaultTimeSplit() {
        return LocalDateTime.of(LocalDate.now().minusDays(BEFORE_DAYS), LocalTime.MIN);
    }

}