package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.TotalOrderDO;
import qnvip.data.overview.mapper.order.TotalOrderMapper;
import qnvip.data.overview.service.order.TotalOrderService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.util.AssertUtil;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/10 5:58 下午
 */
@Service
public class TotalOrderServiceImpl extends BaseServiceImpl<TotalOrderMapper, TotalOrderDO> implements TotalOrderService {

    @Override
    public boolean saveOrUpdate(TotalOrderDO orderDO) {
        QueryWrapper<TotalOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TotalOrderDO::getCountDay, orderDO.getCountDay());
        int count = count(queryWrapper);
        if(count==0){
            save(orderDO);
        }else {
            UpdateWrapper<TotalOrderDO> up = new UpdateWrapper<>();
            up.lambda().eq( TotalOrderDO::getCountDay, orderDO.getCountDay());
            update(orderDO,up);
        }
        return true;
    }


    @Override
    public TotalOrderDO getTotalDataByDate(LocalDateTime countDate){
        AssertUtil.checkNotNull(countDate, "countDate 不能为空");

        QueryWrapper<TotalOrderDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TotalOrderDO::getCountDay, countDate);
        return getOne(queryWrapper);
    }

}