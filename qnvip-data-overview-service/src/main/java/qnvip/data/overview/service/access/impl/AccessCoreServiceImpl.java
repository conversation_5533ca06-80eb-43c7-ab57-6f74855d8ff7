package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.access.AccessCoreDO;
import qnvip.data.overview.mapper.access.AccessCoreMapper;
import qnvip.data.overview.service.access.AccessCoreService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2021-10-09
 */
@Slf4j
@Service
public class AccessCoreServiceImpl extends BaseServiceImpl<AccessCoreMapper, AccessCoreDO> implements AccessCoreService {

    @Override
    public boolean saveOrUpdate(AccessCoreDO accessCoreDO) {
        QueryWrapper<AccessCoreDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(AccessCoreDO::getMiniType, accessCoreDO.getMiniType())
                .eq(AccessCoreDO::getCountDay, accessCoreDO.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(accessCoreDO);
        } else {
            UpdateWrapper<AccessCoreDO> up = new UpdateWrapper<>();
            up.lambda().eq(AccessCoreDO::getMiniType, accessCoreDO.getMiniType())
                    .eq(AccessCoreDO::getCountDay, accessCoreDO.getCountDay());
            update(accessCoreDO, up);
        }
        return true;
    }

    @Override
    public AccessCoreDO getOne(Integer miniType,
                               String fromTime,
                               String toTime) {
        QueryWrapper<AccessCoreDO> queryWrapper = new QueryWrapper<>();

        if (miniType != null) {
            queryWrapper.
                    lambda().
                    eq(AccessCoreDO::getMiniType, miniType).
                    ge(AccessCoreDO::getCountDay, fromTime).
                    le(AccessCoreDO::getCountDay, toTime);
        } else {
            queryWrapper.
                    select("sum(uv) as uv, " +
                            "sum(pv) as pv, " +
                            "sum(uv_new) as uvNew, " +
                            "sum(uv_old) as uvOld ," +
                            "sum(goods_uv) as goodsUv, " +
                            "sum(total_capital_browsing) as totalCapitalBrowsing, " +
                            "sum(total_keep_time) as totalKeepTime, " +
                            "sum(total_jump_loss) as totalJumpLoss").
                    lambda().
                    ge(AccessCoreDO::getCountDay, fromTime).
                    le(AccessCoreDO::getCountDay, toTime);
        }
        return getOne(queryWrapper);
    }

    @Override
    public List<AccessCoreDO> getList(Integer miniType, String fromTime, String toTime) {
        QueryWrapper<AccessCoreDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                eq(miniType != null, AccessCoreDO::getMiniType, miniType).
                ge(AccessCoreDO::getCountDay, fromTime).
                le(AccessCoreDO::getCountDay, toTime);

        return list(queryWrapper);
    }
}
