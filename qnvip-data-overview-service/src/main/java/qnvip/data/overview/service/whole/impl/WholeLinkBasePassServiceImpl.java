package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkBasePassDO;
import qnvip.data.overview.mapper.whole.WholeLinkBasePassMapper;
import qnvip.data.overview.service.whole.WholeLinkBasePassService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkBasePassServiceImpl extends BaseServiceImpl<WholeLinkBasePassMapper, WholeLinkBasePassDO> implements WholeLinkBasePassService {

    @Resource
    private WholeLinkBasePassMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkBasePassDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkBasePassDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkBasePassDO::getCountHour, hour)
                .between(WholeLinkBasePassDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkBasePassDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkBasePassDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkBasePassDO::getMiniType, miniType)
                .eq(WholeLinkBasePassDO::getCountDay, time)
                .orderByDesc(WholeLinkBasePassDO::getCountHour);
        return getOne(queryWrapper, false);
    }
}
