package qnvip.data.overview.service.whole.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.mongo.ReportLogMongoDO;
import qnvip.data.overview.domain.whole.report.WholeReportCountDO;
import qnvip.data.overview.enums.whole.WholeLawReportRowEnum;
import qnvip.data.overview.enums.whole.WholeMerchantReportRowEnum;
import qnvip.data.overview.enums.whole.WholeRentReportRowEnum;
import qnvip.data.overview.mapper.whole.report.WholeRentReportCountMapper;
import qnvip.data.overview.service.whole.report.WholeRentReportCountService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhengtong on 2023-02-13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeRentReportCountServiceImpl extends BaseServiceImpl<WholeRentReportCountMapper, WholeReportCountDO> implements WholeRentReportCountService {

    private final MongoTemplate mongoTemplate;

    @Autowired
    private WholeRentReportCountMapper mapper;

    // 需要特殊处理,要加单位的字段
    public final String[] CONDITION = { "avg_phone14_purchase_price",
            "avg_purchase_price", "avg_finance_amt", "screen_amt",
            "finance_amt", "purchase_price","un_overdue_real_pay_amt","repay_amt",
            "renew_repay_amt","renew_un_overdue_real_pay_amt","overdue_repay_amt","overdue_total_amt",
            "this_day30_should_amt","month2_return_amt","insurance_amount"
    };

    @Override
    public void deleteByCondition(Integer type, Integer model) {
        mapper.deleteByCondition(type, model);
    }

    @Override
    public List<WholeReportCountDO> getList(Integer countType, Integer model) {
        QueryWrapper<WholeReportCountDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(WholeReportCountDO::getCountType, countType).eq(WholeReportCountDO::getModel, model);
        return list(queryWrapper);
    }

    @Override
    public <T> void saveWeekReport(String countDay,
                                   Integer codemodel,
                                   Integer type,
                                   LocalDate day,
                                   Map<String, WholeReportCountDO> map,
                                   Map<String, T> key2enum) {
        List<ReportLogMongoDO> collect = map.entrySet().stream().map(domain -> {
            T _enum = key2enum.get(domain.getKey());
            ReportLogMongoDO reportLogMongoDO = null;
            if (_enum != null) {
                reportLogMongoDO = new ReportLogMongoDO();
                reportLogMongoDO.setType(type);
                reportLogMongoDO.setModel(codemodel);
                reportLogMongoDO.setDay(day);
                reportLogMongoDO.setCode(domain.getKey().toLowerCase());
                reportLogMongoDO.setCountDay(countDay);
                reportLogMongoDO.setAttribute(domain.getValue().getThisWeek());
                reportLogMongoDO.setUnit(getStr(_enum));
            }
            return reportLogMongoDO;

        }).collect(Collectors.toList());

        mongoTemplate.insertAll(collect);
    }

    private <T> String getStr(T _enum) {
        String suffix = "";
        String name = "";
        String unit = "";
        if (_enum instanceof WholeLawReportRowEnum) {
            suffix = ((WholeLawReportRowEnum) _enum).getSuffix();
            name = ((WholeLawReportRowEnum) _enum).name();
        } else if (_enum instanceof WholeRentReportRowEnum) {
            suffix = ((WholeRentReportRowEnum) _enum).getSuffix();
            name = ((WholeRentReportRowEnum) _enum).name();

        } else if (_enum instanceof WholeMerchantReportRowEnum) {
            suffix = ((WholeMerchantReportRowEnum) _enum).getSuffix();
            name = ((WholeMerchantReportRowEnum) _enum).name();
        }
        if ("pt".equals(suffix)) {
            unit = "%";
        } else if (StringUtils.equalsAnyIgnoreCase(name,CONDITION)) {
            unit = "¥";
        }
        return unit;
    }

    @Override
    public List<ReportLogMongoDO> getWeekList(Integer model, Integer type, String code) {
        Query query = new Query(Criteria.where("code").is(code)
                .and("type").is(type)
                .and("model").is(model)
        );

        return mongoTemplate.find(query, ReportLogMongoDO.class);
    }

    @Override
    public void removeMongoDO(LocalDate time, Integer type, Integer model) {
        Query query = new Query(Criteria.where("day").is(time)
                .and("type").is(type)
                .and("model").is(model)
        );
        mongoTemplate.findAllAndRemove(query, ReportLogMongoDO.class);
    }

    @Override
    public void removeMongoDO() {
        Query query = new Query(Criteria.where("day").is(LocalDate.now())
        );
        mongoTemplate.findAllAndRemove(query, ReportLogMongoDO.class);
    }
}
