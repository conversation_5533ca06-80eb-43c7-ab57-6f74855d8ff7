package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.AccessCoreDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2021-10-09
 */
public interface AccessCoreService extends BaseService<AccessCoreDO> {

    /**
     * getOne by condition
     *
     * @param miniType
     * @param fromTime
     * @param toTime
     * @return
     */
    public AccessCoreDO getOne(Integer miniType,
                               String fromTime,
                               String toTime);

    /**
     * getListByHour by condition
     *
     * @param miniType
     * @param fromTime
     * @param toTime
     * @return
     */
    public List<AccessCoreDO> getList(Integer miniType,
                                      String fromTime,
                                      String toTime);
}
