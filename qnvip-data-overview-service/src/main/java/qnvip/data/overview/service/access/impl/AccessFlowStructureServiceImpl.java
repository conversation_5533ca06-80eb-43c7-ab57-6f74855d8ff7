package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.access.AccessFlowStructureDO;
import qnvip.data.overview.mapper.access.AccessFlowStructureMapper;
import qnvip.data.overview.service.access.AccessFlowStructureService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
 * Created by zhengtong on 2021-10-09
 */
@Slf4j
@Service
public class AccessFlowStructureServiceImpl extends BaseServiceImpl<AccessFlowStructureMapper, AccessFlowStructureDO> implements AccessFlowStructureService {
    @Override
    public boolean saveOrUpdate(AccessFlowStructureDO accessFlowStructureDO) {
        QueryWrapper<AccessFlowStructureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(AccessFlowStructureDO::getMiniType, accessFlowStructureDO.getMiniType())
                .eq(AccessFlowStructureDO::getCountDay, accessFlowStructureDO.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(accessFlowStructureDO);
        } else {
            UpdateWrapper<AccessFlowStructureDO> up = new UpdateWrapper<>();
            up.lambda().eq(AccessFlowStructureDO::getMiniType, accessFlowStructureDO.getMiniType())
                    .eq(AccessFlowStructureDO::getCountDay, accessFlowStructureDO.getCountDay());
            update(accessFlowStructureDO, up);
        }
        return true;
    }

    @Override
    public MultiResult<AccessFlowStructureDO> page(Integer miniType,
                                                   String startTime,
                                                   String endTime,
                                                   int current,
                                                   int pageSize) {
        QueryWrapper<AccessFlowStructureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(miniType != null, AccessFlowStructureDO::getMiniType, miniType)
                .ge(AccessFlowStructureDO::getCountDay, startTime)
                .le(AccessFlowStructureDO::getCountDay, endTime);
        Page<AccessFlowStructureDO> page = new Page<>(current, pageSize);
        Page<AccessFlowStructureDO> flowStructureDOPage = page(page, queryWrapper);
        return MultiResult.of(flowStructureDOPage.getRecords(), flowStructureDOPage.getTotal());
    }

    @Override
    public List<AccessFlowStructureDO> getList(Integer miniType, String startTime, String endTime) {
        QueryWrapper<AccessFlowStructureDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .eq(miniType != null, AccessFlowStructureDO::getMiniType, miniType)
                .ge(AccessFlowStructureDO::getCountDay, startTime)
                .le(AccessFlowStructureDO::getCountDay, endTime);
        return list(queryWrapper);
    }


}
