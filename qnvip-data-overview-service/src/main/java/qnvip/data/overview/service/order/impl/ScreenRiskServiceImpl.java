package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.ScreenRiskDO;
import qnvip.data.overview.mapper.order.ScreenRiskMapper;
import qnvip.data.overview.service.order.ScreenRiskService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/10 3:16 下午
 */
@Service
public class ScreenRiskServiceImpl extends BaseServiceImpl<ScreenRiskMapper, ScreenRiskDO> implements ScreenRiskService {
    @Override
    public boolean saveOrUpdate(ScreenRiskDO screenRiskDO) {
        QueryWrapper<ScreenRiskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ScreenRiskDO::getCountDay, screenRiskDO.getCountDay());
        int count = count(queryWrapper);
        if(count==0){
            save(screenRiskDO);
        }else {
            UpdateWrapper<ScreenRiskDO> up = new UpdateWrapper<>();
            up.lambda().eq( ScreenRiskDO::getCountDay, screenRiskDO.getCountDay());
            update(screenRiskDO,up);
        }
        return true;
    }


    @Override
    public List<ScreenRiskDO> getListByTime(LocalDateTime startTime,LocalDateTime endTime){
        QueryWrapper<ScreenRiskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(ScreenRiskDO::getCountDay, startTime)
                .lt(ScreenRiskDO::getCountDay, endTime);
        return list(queryWrapper);
    }

}