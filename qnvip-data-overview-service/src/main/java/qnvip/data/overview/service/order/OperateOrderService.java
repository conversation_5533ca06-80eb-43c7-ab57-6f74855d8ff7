package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by z<PERSON>gtong on 2021-11-15
*/
public interface OperateOrderService extends BaseService<OperateOrderDO>{
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateOrderDO> getList(String fromTime, String toTime, Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);


}
