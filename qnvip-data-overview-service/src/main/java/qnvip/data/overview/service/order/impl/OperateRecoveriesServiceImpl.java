package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.mapper.order.OperateOrderMapper;
import qnvip.data.overview.mapper.order.OperateRecoveriesMapper;
import qnvip.data.overview.service.order.OperateRecoveriesService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by zhengtong on 2021-11-25
*/
@Slf4j
@Service
@RequiredArgsConstructor
public class OperateRecoveriesServiceImpl extends BaseServiceImpl<OperateRecoveriesMapper, OperateRecoveriesDO> implements OperateRecoveriesService {


    private final OperateRecoveriesMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }


    @Override
    public List<OperateRecoveriesDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateRecoveriesDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateRecoveriesDO::getMiniType, miniType)
                .eq(OperateRecoveriesDO::getMerchantId, "100")
                .between(OperateRecoveriesDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public boolean saveOrUpdate(OperateRecoveriesDO domian) {
        QueryWrapper<OperateRecoveriesDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateRecoveriesDO::getCountDay, domian.getCountDay())
                .eq(OperateRecoveriesDO::getMiniType, domian.getMiniType())
                .eq(OperateRecoveriesDO::getMerchantId, domian.getMerchantId());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateRecoveriesDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateRecoveriesDO::getCountDay, domian.getCountDay())
                    .eq(OperateRecoveriesDO::getMiniType, domian.getMiniType())
                    .eq(OperateRecoveriesDO::getMerchantId, domian.getMerchantId());
            update(domian, up);
        }
        return true;
    }
}
