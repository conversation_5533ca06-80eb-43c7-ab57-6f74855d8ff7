package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.AccessFlowStructureDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021-10-09
 */
public interface AccessFlowStructureService extends BaseService<AccessFlowStructureDO> {

    /**
     * getPage
     * @param startTime
     * @param endTime
     * @param current
     * @param pageSize
     * @return
     */
    MultiResult<AccessFlowStructureDO> page(
            Integer miniType,
            String startTime,
            String endTime,
            int current,
            int pageSize);

    /**
     * getListByHour
     * @param miniType
     * @param startTime
     * @param endTime
     * @return
     */
    List<AccessFlowStructureDO> getList(
            Integer miniType,
            String startTime,
            String endTime
    );
}
