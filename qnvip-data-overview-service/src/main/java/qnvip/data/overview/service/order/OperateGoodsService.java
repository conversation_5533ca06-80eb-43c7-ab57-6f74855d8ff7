package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.GoodsDO;
import qnvip.data.overview.domain.order.OperateGoodsDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-18
*/
public interface OperateGoodsService extends BaseService<OperateGoodsDO>{
    MultiResult<OperateGoodsDO> getGroupPageByDate(String startTime,
                                                   String endTime,
                                                   String mainCategory,
                                                   Integer pageNo,
                                                   Integer pageSize
                                                   );
    OperateGoodsDO getTotalData(String startTime, String endTime);

    public void removeDataByTime(LocalDateTime countDay);


}
