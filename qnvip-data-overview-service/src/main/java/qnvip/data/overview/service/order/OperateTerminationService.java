package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.data.overview.domain.order.OperateTerminationDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON>ong on 2021-11-22
 */
public interface OperateTerminationService extends BaseService<OperateTerminationDO> {
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateTerminationDO> getList(String fromTime, String toTime, Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);

}
