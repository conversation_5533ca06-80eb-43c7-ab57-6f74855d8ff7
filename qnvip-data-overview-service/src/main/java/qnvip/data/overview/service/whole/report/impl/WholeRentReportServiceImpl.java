package qnvip.data.overview.service.whole.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeRentReportDO;
import qnvip.data.overview.mapper.whole.report.WholeRentReportMapper;
import qnvip.data.overview.service.whole.report.WholeRentReportService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2023-02-09
 */
@Slf4j
@Service
public class WholeRentReportServiceImpl extends BaseServiceImpl<WholeRentReportMapper, WholeRentReportDO> implements WholeRentReportService {

    @Autowired
    private WholeRentReportMapper mapper;

    @Override
    public void removeDataByDs(String ds) {
        mapper.deleteAll(ds);
    }

    @Override
    public List<WholeRentReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds) {
        QueryWrapper<WholeRentReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(WholeRentReportDO::getCountDay, fromTime, toTime)
                .eq(WholeRentReportDO::getDs, ds);
        return list(queryWrapper);
    }
}
