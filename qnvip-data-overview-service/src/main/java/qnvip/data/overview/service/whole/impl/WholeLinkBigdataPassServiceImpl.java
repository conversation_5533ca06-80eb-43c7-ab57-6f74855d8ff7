package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkBigdataPassDO;
import qnvip.data.overview.mapper.whole.WholeLinkBigdataPassMapper;
import qnvip.data.overview.service.whole.WholeLinkBigdataPassService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkBigdataPassServiceImpl extends BaseServiceImpl<WholeLinkBigdataPassMapper, WholeLinkBigdataPassDO> implements WholeLinkBigdataPassService {

    @Resource
    private WholeLinkBigdataPassMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkBigdataPassDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkBigdataPassDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkBigdataPassDO::getCountHour, hour)
                .between(WholeLinkBigdataPassDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkBigdataPassDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkBigdataPassDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkBigdataPassDO::getMiniType, miniType)
                .eq(WholeLinkBigdataPassDO::getCountDay, time)
                .orderByDesc(WholeLinkBigdataPassDO::getCountHour);
        return getOne(queryWrapper, false);
    }


}
