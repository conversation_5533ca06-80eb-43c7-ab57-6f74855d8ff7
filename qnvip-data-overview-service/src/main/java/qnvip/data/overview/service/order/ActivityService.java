package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.ActivityDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 8:05 下午
 */
public interface ActivityService extends BaseService<ActivityDO> {

    List<ActivityDO> listByIdAndTime(String  activityName, LocalDateTime startTime, LocalDateTime endTime);
}