package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.TerminationReasonDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 6:48 下午
 */
public interface TerminationReasonService extends BaseService<TerminationReasonDO> {

    List<TerminationReasonDO> getListByTime(LocalDateTime startTime, LocalDateTime endTime);

}
