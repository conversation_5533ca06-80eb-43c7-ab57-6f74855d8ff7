package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateScreenRiskDO;
import qnvip.data.overview.domain.order.OperateScreenRiskDO;
import qnvip.data.overview.mapper.order.OperateRecoveriesMapper;
import qnvip.data.overview.mapper.order.OperateScreenRiskMapper;
import qnvip.data.overview.service.order.OperateScreenRiskService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by zhengtong on 2021-11-25
*/
@Slf4j
@Service
public class OperateScreenRiskServiceImpl extends BaseServiceImpl<OperateScreenRiskMapper, OperateScreenRiskDO> implements OperateScreenRiskService {



    @Autowired
    private OperateScreenRiskMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }



    @Override
    public List<OperateScreenRiskDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateScreenRiskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateScreenRiskDO::getMiniType, miniType)
                .eq(OperateScreenRiskDO::getMerchantId, "100")
                .between(OperateScreenRiskDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public boolean saveOrUpdate(OperateScreenRiskDO domian) {
        QueryWrapper<OperateScreenRiskDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateScreenRiskDO::getCountDay, domian.getCountDay())
                .eq(OperateScreenRiskDO::getMiniType, domian.getMiniType())
                .eq(OperateScreenRiskDO::getMerchantId, domian.getMerchantId());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateScreenRiskDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateScreenRiskDO::getCountDay, domian.getCountDay())
                    .eq(OperateScreenRiskDO::getMiniType, domian.getMiniType())
                    .eq(OperateScreenRiskDO::getMerchantId, domian.getMerchantId());
            update(domian, up);
        }
        return true;
    }
}
