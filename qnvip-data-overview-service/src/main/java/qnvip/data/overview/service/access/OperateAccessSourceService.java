package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.OperateAccessSourceDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2021-12-13
 */
public interface OperateAccessSourceService extends BaseService<OperateAccessSourceDO> {
    public void removeDataByTime(LocalDateTime countDay);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public MultiResult<OperateAccessSourceDO> getPageList(String fromTime,
                                                          String toTime,
                                                          String scene,
                                                          Integer pageNo,
                                                          Integer pageSize,
                                                          Integer type);


    /**
     * getListByHour by condition
     *
     * @return
     */
    public MultiResult<OperateAccessSourceDO> getPageListByMiniType(String fromTime,
                                                                    String toTime,
                                                                    String scene,
                                                                    Integer pageNo,
                                                                    Integer pageSize,
                                                                    Integer type,
                                                                    List<Integer> miniType);
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateAccessSourceDO> getList(String fromTime,
                                               String toTime,
                                               String scene,
                                               Integer type,
                                               List<Integer> miniType
    );

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateAccessSourceDO> getSceneList(String fromTime,
                                                    String toTime,
                                                    Integer type);
}
