package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.GoodsAnalysisDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2021-10-13
 */
public interface GoodsAnalysisService extends BaseService<GoodsAnalysisDO> {
    /**
     * getOne by condition
     *
     * @param fromTime
     * @param toTime
     * @return
     */
    public GoodsAnalysisDO getOne(
            String fromTime,
            String toTime);

    /**
     * getListByHour by condition
     *
     * @param fromTime
     * @param toTime
     * @return
     */
    public MultiResult<GoodsAnalysisDO> page(
            String fromTime,
            String toTime,
            Integer pageNo,
            Integer pageSize);

    public List<GoodsAnalysisDO> getList(String fromTime, String toTime);
}
