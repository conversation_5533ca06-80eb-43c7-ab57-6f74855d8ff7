package qnvip.data.overview.service.whole.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeLawReportDO;
import qnvip.data.overview.mapper.whole.report.WholeLawReportMapper;
import qnvip.data.overview.service.whole.report.WholeLawReportService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2023-02-20
 */
@Slf4j
@Service
public class WholeLawReportServiceImpl extends BaseServiceImpl<WholeLawReportMapper, WholeLawReportDO> implements WholeLawReportService {

    @Autowired
    private WholeLawReportMapper mapper;


    @Override
    public void removeDataByDs(String ds) {
        mapper.deleteByCountDay(ds);
    }

    @Override
    public List<WholeLawReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds, Integer type) {
        QueryWrapper<WholeLawReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(WholeLawReportDO::getCountDay, fromTime, toTime)
                .eq(WholeLawReportDO::getDs, ds)
                .eq(WholeLawReportDO::getType, type);
        return list(queryWrapper);
    }
}
