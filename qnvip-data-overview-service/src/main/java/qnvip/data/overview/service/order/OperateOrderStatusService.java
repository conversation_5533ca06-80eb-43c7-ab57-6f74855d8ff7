package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.data.overview.domain.order.OperateOrderStatusDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021-12-03
*/
public interface OperateOrderStatusService extends BaseService<OperateOrderStatusDO>{

    public List<OperateOrderStatusDO> getList(String fromTime, String toTime, Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);
}
