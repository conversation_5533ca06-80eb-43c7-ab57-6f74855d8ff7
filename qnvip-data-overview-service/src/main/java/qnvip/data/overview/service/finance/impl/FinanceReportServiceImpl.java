package qnvip.data.overview.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceReportDO;
import qnvip.data.overview.domain.web.DataCountOverviewDO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.mapper.finance.FinanceReportMapper;
import qnvip.data.overview.service.finance.FinanceReportService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/2/24
 */
@Service
@RequiredArgsConstructor
public class FinanceReportServiceImpl extends BaseServiceImpl<FinanceReportMapper, FinanceReportDO> implements FinanceReportService {

    private final FinanceReportMapper financeReportMapper;
    // 1:自营 0:商户
    private  final Integer selfSupport = 1;

    @Override
    public boolean saveOrUpdate(FinanceReportDO riskPayDO) {
        QueryWrapper<FinanceReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinanceReportDO::getCountDay, riskPayDO.getCountDay())
                .eq(FinanceReportDO::getMiniType, riskPayDO.getMiniType())
                .eq(FinanceReportDO::getBusinessType, selfSupport)
                .eq(FinanceReportDO::getFinanceType, riskPayDO.getFinanceType());
        if (!exist(queryWrapper)) {
            save(riskPayDO);
        } else {
            UpdateWrapper<FinanceReportDO> up = new UpdateWrapper<>();
            up.lambda().eq(FinanceReportDO::getCountDay, riskPayDO.getCountDay())
                    .eq(FinanceReportDO::getMiniType, riskPayDO.getMiniType())
                    .eq(FinanceReportDO::getBusinessType, selfSupport)
                    .eq(FinanceReportDO::getFinanceType, riskPayDO.getFinanceType());
            update(riskPayDO, up);
        }
        return true;
    }
    @Override
    public List<FinanceReportDO> getList(String sTime, String eTime,
                                         String sEndTime, String eEndTime,
                                         String sParentTime, String eParentTime,
                                         String sParentEndTime, String eParentEndTime,
                                         Integer businessType,
                                         List<Integer> miniTypeList, List<Integer> financeTypeList, Integer renewFlag) {
        String applySql = " 1=1 ";
        if (Objects.equals(renewFlag, 0)) {
            applySql = " parent_day is null ";
        } else if (Objects.equals(renewFlag, 1)) {
            applySql = " parent_day is not null ";
        }
        QueryWrapper<FinanceReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(StringUtils.isNotBlank(sTime), FinanceReportDO::getCountDay, sTime)
                .le(StringUtils.isNotBlank(eTime), FinanceReportDO::getCountDay, eTime)
                .ge(StringUtils.isNotBlank(sEndTime), FinanceReportDO::getRentEndDate, sEndTime)
                .le(StringUtils.isNotBlank(eEndTime), FinanceReportDO::getRentEndDate, eEndTime)
                .ge(StringUtils.isNotBlank(sParentTime), FinanceReportDO::getParentDay, sParentTime)
                .le(StringUtils.isNotBlank(eParentTime), FinanceReportDO::getParentDay, eParentTime)
                .ge(StringUtils.isNotBlank(sParentEndTime), FinanceReportDO::getParentEndDate, sParentEndTime)
                .le(StringUtils.isNotBlank(eParentEndTime), FinanceReportDO::getParentEndDate, eParentEndTime)
                .eq(FinanceReportDO::getBusinessType,businessType)
                .in(CollUtil.isNotEmpty(miniTypeList), FinanceReportDO::getMiniType, miniTypeList)
                .in(CollUtil.isNotEmpty(financeTypeList), FinanceReportDO::getFinanceType, financeTypeList);
        queryWrapper.apply(applySql);
        return list(queryWrapper);
    }



    /**
     * 删除全部数据
     */
    public void deleteAllWithBusinessType(Integer businessType) {
        financeReportMapper.deleteAllWithBusinessType(businessType);
    }

    @Override
    public List<DataCountOverviewDO> getBusinessAmountByMonth(Integer type) {
        List<Integer> collect = FinanceTypeEnum.MASTER_FINANCE.stream()
                .map(FinanceTypeEnum::getType).collect(Collectors.toList());
        QueryWrapper<FinanceReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("DATE_FORMAT(count_day, '%Y-%m') count_month," +
                        "sum(base_act_supply_price) act_supply_price," +
                        "sum(base_act_supply_price) act_supply_price," +
                        "sum(base_rent_count) rent_count," +
                        " sum(base_rent_amt) + sum(base_buyout_amt) - sum(base_pre_discount) rent_total" +
                        " ").
                lambda()
                .in(FinanceReportDO::getFinanceType, collect)
                .eq(FinanceReportDO::getBusinessType, type)
                .last("group by DATE_FORMAT(count_day, '%Y-%m')");
        List<Map<String, Object>> mapList = listMaps(queryWrapper);
        return mapList.stream().map(e -> {
            DataCountOverviewDO domain = new DataCountOverviewDO();
            domain.setRentStartDate(String.valueOf(e.get("count_month")));
            domain.setActualSupplyPrice(new BigDecimal(String.valueOf(e.get("act_supply_price"))));
            domain.setRentTotal(new BigDecimal(String.valueOf(e.get("rent_total"))));
            domain.setOrderCount(Integer.valueOf(String.valueOf(e.get("rent_count"))));
            return domain;
        }).collect(Collectors.toList());
    }

}