package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.RentBusinessPlateDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.mapper.order.RentBusinessPlateMapper;
import qnvip.data.overview.service.order.RentBusinessPlateService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-12-01
 */
@Slf4j
@Service
public class RentBusinessPlateServiceImpl extends BaseServiceImpl<RentBusinessPlateMapper, RentBusinessPlateDO> implements RentBusinessPlateService {


    @Override
    public List<RentBusinessPlateDO> getList(String fromTime, List<Integer> miniType) {
        QueryWrapper<RentBusinessPlateDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(RentBusinessPlateDO::getCountDay, fromTime).
                in(CollUtil.isNotEmpty(miniType), RentBusinessPlateDO::getMiniType, miniType).
                eq(CollUtil.isEmpty(miniType), RentBusinessPlateDO::getMiniType, MiniTypeEnum.TOTAl.getCode());
        return list(queryWrapper);
    }


    @Override
    public List<RentBusinessPlateDO> getList(String fromTime, String endTime, List<Integer> miniType) {
        QueryWrapper<RentBusinessPlateDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                between(RentBusinessPlateDO::getCountDay, fromTime, endTime).
                in(CollUtil.isNotEmpty(miniType), RentBusinessPlateDO::getMiniType, miniType).
                eq(CollUtil.isEmpty(miniType), RentBusinessPlateDO::getMiniType, MiniTypeEnum.TOTAl.getCode());
        return list(queryWrapper);
    }
}
