package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.WholeLinkSignDO;
import qnvip.data.overview.mapper.whole.WholeLinkSignMapper;
import qnvip.data.overview.service.whole.WholeLinkSignService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkSignServiceImpl extends BaseServiceImpl<WholeLinkSignMapper, WholeLinkSignDO> implements WholeLinkSignService {

    @Resource
    private WholeLinkSignMapper mapper;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkSignDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkSignDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkSignDO::getCountHour, hour)
                .between(WholeLinkSignDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public WholeLinkSignDO getOneByMiniType(LocalDate time,Integer miniType) {
        QueryWrapper<WholeLinkSignDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkSignDO::getMiniType, miniType)
                .eq(WholeLinkSignDO::getCountDay, time)
                .orderByDesc(WholeLinkSignDO::getCountHour);
        return getOne(queryWrapper, false);
    }
}
