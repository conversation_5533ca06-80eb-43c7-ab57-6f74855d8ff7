package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderFinancialDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by zhengtong on 2021-12-09
*/
public interface OperateOrderFinancialService extends BaseService<OperateOrderFinancialDO>{
    public void removeDataByTime(LocalDateTime countDay);

    /**
    * getListByHour by condition
    *
    * @return
    */
    public List<OperateOrderFinancialDO> getList(String fromTime,
                                                 String toTime,
                                                 Integer miniType);
}
