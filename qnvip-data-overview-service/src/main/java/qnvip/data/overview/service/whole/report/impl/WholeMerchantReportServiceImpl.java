package qnvip.data.overview.service.whole.report.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeMerchantReportDO;
import qnvip.data.overview.domain.whole.report.WholeRentReportDO;
import qnvip.data.overview.mapper.whole.report.WholeMerchantReportMapper;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.data.overview.service.whole.report.WholeMerchantReportService;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2023-02-15
 */
@Slf4j
@Service
public class WholeMerchantReportServiceImpl extends BaseServiceImpl<WholeMerchantReportMapper, WholeMerchantReportDO> implements WholeMerchantReportService {

    @Autowired
    private WholeMerchantReportMapper mapper;


    @Override
    public void removeDataByDs(String ds) {
        mapper.deleteByCountDay(ds);
    }
    @Override
    public List<WholeMerchantReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds) {
        QueryWrapper<WholeMerchantReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(WholeMerchantReportDO::getCountDay, fromTime, toTime)
                .eq(WholeMerchantReportDO::getDs, ds);
        return list(queryWrapper);
    }
}
