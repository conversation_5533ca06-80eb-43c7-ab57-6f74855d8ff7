package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OrderDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 11:55 上午
 */
public interface OrderService extends BaseService<OrderDO> {

    List<OrderDO> getOrderListDataByDate(LocalDateTime startTime, LocalDateTime endTime, Integer miniType);

    List<OrderDO> getByDateAndMiniType(String startTime, String endTime, List<Integer> miniTypeList);

}