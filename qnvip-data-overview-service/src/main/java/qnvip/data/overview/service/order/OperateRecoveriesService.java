package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-25
*/
public interface OperateRecoveriesService extends BaseService<OperateRecoveriesDO>{
    /**
     * getListByHour by condition
     *
     * @return
     */
    List<OperateRecoveriesDO> getList(String fromTime, String toTime, Integer miniType);

    void removeDataByTime(LocalDateTime countDay);


}
