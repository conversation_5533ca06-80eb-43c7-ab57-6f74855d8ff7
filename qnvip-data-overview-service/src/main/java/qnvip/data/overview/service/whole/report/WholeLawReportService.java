package qnvip.data.overview.service.whole.report;

import qnvip.data.overview.domain.whole.report.WholeLawReportDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON>gtong on 2023-02-20
 */
public interface WholeLawReportService extends BaseService<WholeLawReportDO> {
    public void removeDataByDs(String ds);

    /**
     * getList by condition
     *
     * @return
     */
    public List<WholeLawReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds, Integer type);
}
