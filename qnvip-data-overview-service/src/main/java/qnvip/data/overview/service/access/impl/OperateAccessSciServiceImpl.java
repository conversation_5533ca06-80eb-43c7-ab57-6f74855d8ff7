package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.OperateAccessSciDO;
import qnvip.data.overview.mapper.access.OperateAccessSciMapper;
import qnvip.data.overview.service.access.OperateAccessSciService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-01-10
 */
@Slf4j
@Service
public class OperateAccessSciServiceImpl extends BaseServiceImpl<OperateAccessSciMapper, OperateAccessSciDO> implements OperateAccessSciService {

    @Autowired
    private OperateAccessSciMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateAccessSciDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateAccessSciDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateAccessSciDO::getMiniType, miniType)
                .between(OperateAccessSciDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }
}
