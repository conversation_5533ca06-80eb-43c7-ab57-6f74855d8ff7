package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.ComponentDetailDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @Date 2021/10/13 1:27 下午
 */
public interface ComponentDetailService extends BaseService<ComponentDetailDO> {
    MultiResult<ComponentDetailDO> getGroupPageByDate(LocalDateTime startTime, LocalDateTime endTime, Integer pageNo, Integer pageSize);
}