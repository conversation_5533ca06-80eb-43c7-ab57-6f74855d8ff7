package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OperateOrderForecastDO;
import qnvip.data.overview.mapper.order.OperateOrderForecastMapper;
import qnvip.data.overview.service.order.OperateOrderForecastService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-03-08
 */
@Slf4j
@Service
public class OperateOrderForecastServiceImpl extends BaseServiceImpl<OperateOrderForecastMapper, OperateOrderForecastDO> implements OperateOrderForecastService {

    @Autowired
    private OperateOrderForecastMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateOrderForecastDO> getList(LocalDate fromTime, LocalDate toTime) {
        QueryWrapper<OperateOrderForecastDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(OperateOrderForecastDO::getCountDay, fromTime,toTime) ;
        return list(queryWrapper);
    }

    @Override
    public List<OperateOrderForecastDO> getList(LocalDateTime fromTime, Integer bizType, Integer countType) {
        QueryWrapper<OperateOrderForecastDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateOrderForecastDO::getCountDay, fromTime)
                .eq(OperateOrderForecastDO::getBizType, bizType)
                .eq(OperateOrderForecastDO::getCountType, countType)
        ;
        return list(queryWrapper);
    }
}
