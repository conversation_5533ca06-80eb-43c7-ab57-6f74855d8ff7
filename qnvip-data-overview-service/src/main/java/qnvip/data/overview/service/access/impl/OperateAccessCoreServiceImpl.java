package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.AccessTotalDO;
import qnvip.data.overview.domain.access.OperateAccessCoreDO;
import qnvip.data.overview.domain.access.OperateAccessSourceDO;
import qnvip.data.overview.mapper.access.OperateAccessCoreMapper;
import qnvip.data.overview.mapper.access.OperateMinuteMapper;
import qnvip.data.overview.service.access.OperateAccessCoreService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Collection;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-15
 */
@Slf4j
@Service
public class OperateAccessCoreServiceImpl extends BaseServiceImpl<OperateAccessCoreMapper, OperateAccessCoreDO> implements OperateAccessCoreService {
    @Autowired
    private OperateAccessCoreMapper accessCoreMapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        accessCoreMapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateAccessCoreDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateAccessCoreDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateAccessCoreDO::getMiniType, miniType)
                .ge(OperateAccessCoreDO::getCountDay, fromTime)
                .le(OperateAccessCoreDO::getCountDay, toTime);
        return list(queryWrapper);
    }

    @Override
    public boolean saveOrUpdate(OperateAccessCoreDO domian) {
        QueryWrapper<OperateAccessCoreDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateAccessCoreDO::getCountDay, domian.getCountDay())
                .eq(OperateAccessCoreDO::getMiniType, domian.getMiniType());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateAccessCoreDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateAccessCoreDO::getCountDay, domian.getCountDay())
                    .eq(OperateAccessCoreDO::getMiniType, domian.getMiniType());
            update(domian, up);
        }
        return true;
    }
}
