package qnvip.data.overview.service.finance;

import qnvip.data.overview.domain.finance.FinanceMerchantOrderDetailDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2022-10-17
 */
public interface FinanceMerchantOrderDetailService extends BaseService<FinanceMerchantOrderDetailDO> {

    List<FinanceMerchantOrderDetailDO> getList(String sTime, String eTime,
                                               String sEndTime, String eEndTime,
                                               List<Integer> miniTypeList,
                                               List<Integer> financeTypeList);

    void deleteAll();
}
