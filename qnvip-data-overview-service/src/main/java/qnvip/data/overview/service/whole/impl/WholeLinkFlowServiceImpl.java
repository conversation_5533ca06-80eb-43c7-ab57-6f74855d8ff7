package qnvip.data.overview.service.whole.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.mongo.WholeFlowHealthMongoDO;
import qnvip.data.overview.domain.whole.WholeLinkFlowDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.mapper.whole.WholeLinkFlowMapper;
import qnvip.data.overview.service.whole.WholeLinkFlowService;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by zhengtong on 2022-07-25
 */
@Slf4j
@Service
public class WholeLinkFlowServiceImpl extends BaseServiceImpl<WholeLinkFlowMapper, WholeLinkFlowDO> implements WholeLinkFlowService {

    @Resource
    private WholeLinkFlowMapper mapper;
    @Resource
    private MongoTemplate mongoTemplate;

    @Override
    public void removeByHour(Integer hour, LocalDate now) {
        mapper.removeByHour(now, hour);
    }

    @Override
    public List<WholeLinkFlowDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkFlowDO::getCountHour, hour)
                .between(WholeLinkFlowDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<WholeLinkFlowDO> getListByTime(LocalDate fromTime, LocalDate toTime) {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(WholeLinkFlowDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<WholeLinkFlowDO> getListByPlatform(LocalDate fromTime, LocalDate toTime, Integer hour,
                                                   String platform, Object obj) {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkFlowDO::getPlatform, platform)
                .between(WholeLinkFlowDO::getCountDay, fromTime, toTime);
        if (obj instanceof Integer) {
            queryWrapper.lambda().eq(WholeLinkFlowDO::getMiniType, obj);
        } else if (obj instanceof String) {
            queryWrapper.lambda().eq(WholeLinkFlowDO::getScene, obj);
        }
        return list(queryWrapper);
    }

    @Override
    public WholeLinkFlowDO getOneByMiniType(LocalDate time, Integer miniType) {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(WholeLinkFlowDO::getMiniType, miniType)
                .eq(WholeLinkFlowDO::getCountDay, time)
                .orderByDesc(WholeLinkFlowDO::getCountHour);
        return getOne(queryWrapper, false);
    }


    @Override
    public List<WholeLinkFlowDO> getChartList(LocalDate time, Integer miniType, String scene) {
        int startHour = LocalDateTime.now().minusHours(1).getHour();
        int endHour = LocalDateTime.now().plusHours(1).getHour();
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(WholeLinkFlowDO::getValidUv,
                        WholeLinkFlowDO::getMiniType,
                        WholeLinkFlowDO::getScene,
                        WholeLinkFlowDO::getCountHour,
                        WholeLinkFlowDO::getPlatform)
                .eq(miniType != null, WholeLinkFlowDO::getMiniType, miniType)
                .eq(WholeLinkFlowDO::getCountDay, time)
                .ne(WholeLinkFlowDO::getMiniType, MiniTypeEnum.TOTAl.getCode())
                .eq(StringUtils.isNotBlank(scene), WholeLinkFlowDO::getScene, scene)
                .between(WholeLinkFlowDO::getCountHour, startHour,
                        endHour)
                .orderByDesc(WholeLinkFlowDO::getValidUv);
        List<WholeLinkFlowDO> list = list(queryWrapper);
        if (CollUtil.isEmpty(list)) {
            return Lists.newArrayList();
        }
        TreeMap<Integer, List<WholeLinkFlowDO>> collect2 =
                list.stream().collect(Collectors.groupingBy(WholeLinkFlowDO::getCountHour, TreeMap::new, Collectors.toList()));
        return collect2.lastEntry().getValue().stream().
                sorted(Comparator.comparing(WholeLinkFlowDO::getValidUv).reversed()).collect(Collectors.toList());
    }

    @Override
    public List<WholeLinkFlowDO> getList(LocalDate start, Integer miniType, String scene) {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        if (miniType == null && StringUtils.isBlank(scene)) {
            queryWrapper.lambda().eq(WholeLinkFlowDO::getMiniType, MiniTypeEnum.TOTAl.getCode());
        }
        queryWrapper.
                select("count_hour," +
                        "       sum(whole_uv) whole_uv," +
                        "       sum(discount_uv) discount_uv," +
                        "       sum(un_loss_uv) un_loss_uv," +
                        "       sum(loss_rate) loss_rate," +
                        "       sum(h1_rate) h1_rate," +
                        "       sum(top5_regional_rate) top5_regional_rate," +
                        "       sum(valid_uv) valid_uv," +
                        "       sum(new_uv) new_uv," +
                        "       sum(un_order_uv) un_order_uv," +
                        "       sum(valid_age_uv) valid_age_uv," +
                        "       sum(male_uv) male_uv," +
                        "       sum(valid_gender_uv) valid_gender_uv," +
                        "       sum(top5_regional_uv) top5_regional_uv," +
                        "       sum(valid_message_uv) valid_message_uv," +
                        "       sum(active_uv) active_uv," +
                        "       sum(repeat_visit_uv) repeat_visit_uv," +
                        "       sum(valid_uv_h1) valid_uv_h1," +
                        "       sum(valid_uv_h2) valid_uv_h2," +
                        "       sum(male_rate) male_rate," +
                        "       sum(valid_age_rate) valid_age_rate," +
                        "       sum(new_uv_rate) new_uv_rate," +
                        "       sum(repeat_visit_rate) repeat_visit_rate," +
                        "       sum(active_rate) active_rate").
                lambda()
                .eq(WholeLinkFlowDO::getCountDay, start)
                .eq(StringUtils.isNotBlank(scene), WholeLinkFlowDO::getScene, scene)
                .last("group by count_hour");
        return list(queryWrapper);
    }

    @Override
    public List<WholeLinkFlowDO> getSceneList() {
        QueryWrapper<WholeLinkFlowDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("scene , sum(whole_uv) as whole_uv").
                lambda()
                .ne(WholeLinkFlowDO::getScene, "")
                .groupBy(WholeLinkFlowDO::getScene)
                .last(" order by sum(whole_uv) desc limit 15");
        return list(queryWrapper);
    }

    @Override
    public List<WholeFlowHealthMongoDO> getListFromMongo(LocalDate time, Integer miniType, String scene, String name) {
        Query query = new Query();
        Criteria criteria = Criteria.where("countDay").is(time)
                .and("key").is(name)
                .and("scene").is(scene == null ? "" : scene);
        if (miniType == null && StringUtils.isBlank(scene)) {
            criteria.and("miniType").is(MiniTypeEnum.TOTAl.getCode());
        }
        query.addCriteria(criteria);
        List<WholeFlowHealthMongoDO> list = mongoTemplate.find(query, WholeFlowHealthMongoDO.class);
        Map<Integer, List<WholeFlowHealthMongoDO>> hour2List = list.stream()
                .collect(Collectors.groupingBy(WholeFlowHealthMongoDO::getHour));
        List<WholeFlowHealthMongoDO> objects = Lists.newArrayList();
        for (Map.Entry<Integer, List<WholeFlowHealthMongoDO>> entry : hour2List.entrySet()) {
            WholeFlowHealthMongoDO wholeFlowHealthMongoDO = new WholeFlowHealthMongoDO();
            objects.add(wholeFlowHealthMongoDO);
            Integer hour = entry.getKey();
            List<WholeFlowHealthMongoDO> value = entry.getValue();
            BigDecimal sum = ObjectUtils.getSUM(value, WholeFlowHealthMongoDO::getValue);
            wholeFlowHealthMongoDO.setHour(hour);
            wholeFlowHealthMongoDO.setValue(sum.toString());
        }
        return objects;
    }

    @Override
    public void save2Mongo(List<WholeFlowHealthMongoDO> list) {
        mongoTemplate.insertAll(list);
    }

    @Override
    public void removeMongoByTime(LocalDate start) {

        Query query = new Query(Criteria.where("countDay").is(start));
        mongoTemplate.findAllAndRemove(query, WholeFlowHealthMongoDO.class);
    }
}
