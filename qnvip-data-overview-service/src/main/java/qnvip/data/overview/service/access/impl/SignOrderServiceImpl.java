package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.SignOrderDO;
import qnvip.data.overview.mapper.order.SignOrderMapper;
import qnvip.data.overview.service.access.SignOrderService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

@Service
public class SignOrderServiceImpl extends BaseServiceImpl<SignOrderMapper, SignOrderDO> implements SignOrderService {



    @Override
    public boolean saveOrUpdate(SignOrderDO domain){
        Integer count = this.lambdaQuery()
                .eq(SignOrderDO::getOrderId, domain.getOrderId())
                .eq(SignOrderDO::getType,domain.getType()).count();
        if (count==0){
            save(domain);
        }else {
            UpdateWrapper<SignOrderDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(SignOrderDO::getCountDay, domain.getCountDay())
                    .eq(SignOrderDO::getOrderId, domain.getOrderId());
            update(domain,updateWrapper);
        }
        return true;
    }






    @Override
    public List<SignOrderDO> listByTime(LocalDateTime startTime, LocalDateTime endTime) {
        return this.lambdaQuery().ge(SignOrderDO::getCountDay, startTime).le(SignOrderDO::getCountDay, endTime)
                .orderByDesc(SignOrderDO::getId).list();
    }
}
