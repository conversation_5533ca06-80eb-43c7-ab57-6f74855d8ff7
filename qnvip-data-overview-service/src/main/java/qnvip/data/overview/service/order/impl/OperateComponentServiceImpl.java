package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateComponentDO;
import qnvip.data.overview.domain.order.OperateComponentDO;
import qnvip.data.overview.domain.order.OperateScreenRiskDO;
import qnvip.data.overview.mapper.access.OperateAccessCoreMapper;
import qnvip.data.overview.mapper.order.OperateComponentMapper;
import qnvip.data.overview.service.order.OperateComponentService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-23
 */
@Slf4j
@Service
public class OperateComponentServiceImpl extends BaseServiceImpl<OperateComponentMapper, OperateComponentDO> implements OperateComponentService {
    @Autowired
    private OperateComponentMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateComponentDO> getList(String fromTime, String toTime) {
        QueryWrapper<OperateComponentDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateComponentDO::getMerchantId, "100")
                .eq(OperateComponentDO::getMiniType, -1)
                .between(OperateComponentDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public boolean saveOrUpdate(OperateComponentDO domian) {
        QueryWrapper<OperateComponentDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateComponentDO::getCountDay, domian.getCountDay())
                .eq(OperateComponentDO::getMiniType, domian.getMiniType())
                .eq(OperateComponentDO::getName, domian.getName())
                .eq(OperateComponentDO::getMerchantId, domian.getMerchantId());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateComponentDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateComponentDO::getCountDay, domian.getCountDay())
                    .eq(OperateComponentDO::getMiniType, domian.getMiniType())
                    .eq(OperateComponentDO::getName, domian.getName())
                    .eq(OperateComponentDO::getMerchantId, domian.getMerchantId());
            update(domian, up);
        }
        return true;
    }

}
