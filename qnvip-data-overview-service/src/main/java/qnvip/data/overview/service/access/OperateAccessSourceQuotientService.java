package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.OperateAccessSourceDO;
import qnvip.data.overview.domain.access.OperateAccessSourceQuotientDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2022-01-10
 */
public interface OperateAccessSourceQuotientService extends BaseService<OperateAccessSourceQuotientDO> {
    public void removeDataByTime(LocalDateTime countDay);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateAccessSourceQuotientDO> getList(String fromTime, String toTime);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public MultiResult<OperateAccessSourceQuotientDO> getPageList(String fromTime,
                                                                  String toTime,
                                                                  Integer pageNo,
                                                                  Integer pageSize);

}
