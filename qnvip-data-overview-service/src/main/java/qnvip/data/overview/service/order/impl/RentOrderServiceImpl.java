package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import qnvip.data.overview.domain.order.RentOrderDO;
import qnvip.data.overview.domain.risk.*;
import qnvip.data.overview.enums.StrategyEnum;
import qnvip.data.overview.mapper.order.OperateRecoveriesMapper;
import qnvip.data.overview.mapper.order.RentOrderMapper;
import qnvip.data.overview.mapper.risk.DataviewRiskMerchantOrderInfoMapper;
import qnvip.data.overview.param.job.JobParam;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.DataviewRiskMerchantOrderInfoService;
import qnvip.data.overview.strategy.Client;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhengtong on 2021-12-03
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RentOrderServiceImpl extends BaseServiceImpl<RentOrderMapper, RentOrderDO> implements RentOrderService {

    private final Client client;

    private final RentOrderMapper mapper;

    private final DataviewRiskMerchantOrderInfoMapper dataviewRiskMerchantOrderInfoMapper;

    private final DataviewRiskMerchantOrderInfoService riskMerchantOrderInfoService;

    @Override
   // @DS("slave")
    public Map<String, Object> selectPredictedValue(StrategyEnum strategy) {
        return client.getData(strategy);
    }

    @Override
    //@DS("slave")
    public Map<String, Object> selectActualValue() {
        return mapper.selectRealCount();
    }

    @Override
    //@DS("slave")
    public BigDecimal selectOrderUvCount() {
        Map<String, Object> key2value = mapper.selectOrderUvCount();
        Object orderUvCount = key2value.get("orderUvCount");
        return CalculateUtil.toDecimal(orderUvCount);
    }

    @Override
    @DS("slave2")
    public List<GeneralRecord> getAllGeneralData(Integer startPage,Integer pageSize,Integer overdue) {
        try{
            List<GeneralRecord> res = mapper.getAllGeneralData(startPage,pageSize,overdue);
            return res;
        }catch (Exception e){
            log.error("生产环境-大盘通用查询数据报错:","startPage=",startPage,"pageSize",pageSize,e);
            throw e;
        }
    }

    @Override
    @DS("slave2")
    public Integer getGeneralCount() {
        try{
            Integer resCnt=mapper.getGeneralCount();
            return resCnt;
        }
        catch (Exception e){
            log.error("生产环境-大盘通用查询总条数报错:",e);
            throw e;
        }
    }

    @Override
    @DS("slave2")
    public Integer getRenewCountTidb() {
        try {
            Integer resCnt=mapper.getRenewCountTidb();
            return resCnt;
        }catch (Exception e){
            log.error("生产环境-大盘通用查询续租总条数报错:",e);
            throw e;
        }

    }

    @Override
    @DS("slave2")
    public List<GeneralRenewRecord> getAllGeneralRenewData(int startPage, Integer pageSize, Integer overdueDay) {
        try{
            List<GeneralRenewRecord> res = mapper.getAllGeneralRenewData(startPage,pageSize,overdueDay);
            return res;
        }catch (Exception e){
            log.error("生产环境-大盘通用查询续租报错:","startPage=",startPage,"pageSize",pageSize,e);
            throw e;
        }

    }

    @Override
    @DS("slave2")
    public Integer getShCountTidb() {
        try {
            Integer resCnt = mapper.getShCountTidb();
            return resCnt;
        }catch (Exception e){
            log.error("大盘分期查询总条数报错:",e);
            throw e;
        }

    }

    @Override
    @DS("slave2")
    public List<ShRecordData> getShAllData(int startPage, Integer pageSize, Integer overdueDay) {
        try{
            List<ShRecordData> res = mapper.getShAllData(startPage, pageSize, overdueDay);
            return res;
        }catch (Exception e){
            log.error("生产环境-大盘分期查询数据报错:","startPage=",startPage,"pageSize",pageSize,e);
            throw e;
        }
    }

    @Override
    @DS("slave2")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public List<Map<String,String>> getGeneralCommonSql(String sql) {
        List<Map<String,String>> res = mapper.getGeneralCommonSql(sql);
        return res;
    }

    @Override
    @DS("slave2")
    @Transactional(propagation = Propagation.REQUIRES_NEW, rollbackFor = Exception.class)
    public List<ShOrderInfo> getShOrderInfo(String pageSql) {
        List<ShOrderInfo> res = mapper.getShOrderInfo(pageSql);
        return res;
    }

    @Override
    @DS("slave2")
    public List<RiskNewRentOrderInfoVo> getNewRentOrderInfo(String pageSql) {
        List<RiskNewRentOrderInfoVo> res = mapper.getNewRentOrderInfo(pageSql);
        return res;
    }

    @Override
    @DS("slave2")
    public List<RiskRentInfoVo> getRiskRentInfo(String sql) {
        List<RiskRentInfoVo> res = mapper.getRiskRentInfo(sql);
        return res;
    }

    @Override
    @Transactional(propagation = Propagation.REQUIRED,rollbackFor = Exception.class)
    public void batchSave(List<DataviewRiskMerchantOrderInfoDO> list) {
        log.info("batchSave事务：{}", TransactionSynchronizationManager.getCurrentTransactionName());
        List<List<DataviewRiskMerchantOrderInfoDO>> partition = Lists.partition(list, 2000);
        log.info("批量插入风控-分期订单明细分区数 num={}", partition.size());
        for (List<DataviewRiskMerchantOrderInfoDO> infoDOS : partition) {
            if (CollUtil.isEmpty(infoDOS)) {
                continue;
            }
            try {
                List<String> noList = infoDOS.stream().map(DataviewRiskMerchantOrderInfoDO::getOrderNo).collect(Collectors.toList());
                // 删除
                dataviewRiskMerchantOrderInfoMapper.deleteByOrderNo(noList.stream().collect(Collectors.joining("','","'","'")));
            } catch (Exception e) {
                log.error("批量删除风控-分期订单明细异常", e);
            }

        }
        for (List<DataviewRiskMerchantOrderInfoDO> infoDOS : partition) {
            if (CollUtil.isEmpty(infoDOS)) {
                continue;
            }
            try {
                // 插入
                riskMerchantOrderInfoService.saveOrUpdateBatch(infoDOS, 2500);
                // dataviewRiskMerchantOrderInfoMapper.updateBatch(infoDOS);
                log.info("批量插入风控-分期订单明细size={}", infoDOS.size());
            } catch (Exception e) {
                log.error("批量插入风控-分期订单明细异常", e);
            }

        }
    }

    @Override
    @DS("slave2")
    public Integer getRenewCountDistributionTidb() {
        try{
            Integer resCnt=mapper.getRenewCountDistributionTidb();
            return resCnt;
        }catch (Exception e){
            log.error("生产环境-大盘商户续租查询总数据量报错:",e);
            throw e;
        }

    }

    @Override
    @DS("slave2")
    public List<GeneralRenewRecord> getAllDistributionRenewData(int id, Integer pageSize, Integer overdueDay) {
        try {
            List<GeneralRenewRecord> res = mapper.getAllDistributionRenewData(id, pageSize, overdueDay);
            return res;
        }catch (Exception e){
            log.error("生产环境-大盘商户续租查询数据报错:startPage {} pageSize{} ", id, pageSize, e);
            throw e;
        }
    }

}
