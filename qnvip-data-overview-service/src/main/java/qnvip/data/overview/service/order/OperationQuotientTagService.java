package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperationQuotientTagDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;
/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2022-11-11
*/
public interface OperationQuotientTagService extends BaseService<OperationQuotientTagDO>{
    public void deleteAll();

    public List<OperationQuotientTagDO> getTagList(String time,
                                                   Integer countType,
                                                   List<Integer> miniTypeList,
                                                   List<String> platformList,
                                                   List<String> quotientList,
                                                   List<String> sceneList,
                                                   List<String> riskStrategyList,
                                                   List<String> stage);
}
