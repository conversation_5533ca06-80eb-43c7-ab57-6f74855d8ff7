package qnvip.data.overview.service.finance.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.AppBiRentOrderLabelDO;
import qnvip.data.overview.mapper.finance.AppBiRentOrderLabelMapper;
import qnvip.data.overview.service.finance.AppBiRentOrderLabelService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * Created by zhang<PERSON><PERSON> on 2023-08-30
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AppBiRentOrderLabelServiceImpl extends BaseServiceImpl<AppBiRentOrderLabelMapper, AppBiRentOrderLabelDO> implements AppBiRentOrderLabelService {

    @Autowired
    private AppBiRentOrderLabelMapper appBiRentOrderLabelMapper;

    @Override
    public Map<String, AppBiRentOrderLabelDO> getMapByOrderNos(Set<String> orderNoList) {
        Map<String, AppBiRentOrderLabelDO> map = new HashMap<>();
        if (CollectionUtils.isEmpty(orderNoList)) {
            return map;
        }
        QueryWrapper<AppBiRentOrderLabelDO> wrapper = new QueryWrapper<>();
        wrapper.lambda()
                .select(AppBiRentOrderLabelDO::getOrderNo, AppBiRentOrderLabelDO::getQuotientName, AppBiRentOrderLabelDO::getSecondOrderBelonging)
                .in(AppBiRentOrderLabelDO::getOrderNo, orderNoList);
        List<AppBiRentOrderLabelDO> list = appBiRentOrderLabelMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(list)) {
            return map;
        }
        return list.stream().collect(Collectors.toMap(AppBiRentOrderLabelDO::getOrderNo, Function.identity(), (v1, v2) -> v1));
    }
}
