package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.TerminationReasonDO;
import qnvip.data.overview.mapper.order.TerminationReasonMapper;
import qnvip.data.overview.service.order.TerminationReasonService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.util.AssertUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 6:49 下午
 */
@Service
public class TerminationReasonServiceImpl extends BaseServiceImpl<TerminationReasonMapper,TerminationReasonDO> implements TerminationReasonService {

    @Override
    public boolean saveOrUpdate(TerminationReasonDO terminationReasonDO) {
        QueryWrapper<TerminationReasonDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(TerminationReasonDO::getTerminationDay,terminationReasonDO.getTerminationDay())
                .eq(TerminationReasonDO::getReason,terminationReasonDO.getReason());
        int count = count(queryWrapper);
        if(count==0){
            save(terminationReasonDO);
        }else {
            UpdateWrapper<TerminationReasonDO> up = new UpdateWrapper<>();
            up.lambda().eq( TerminationReasonDO::getTerminationDay, terminationReasonDO.getTerminationDay())
                    .eq(TerminationReasonDO::getReason,terminationReasonDO.getReason());
            update(terminationReasonDO,up);
        }
        return true;
    }

    @Override
    public List<TerminationReasonDO> getListByTime(LocalDateTime startTime, LocalDateTime endTime) {
        AssertUtil.checkNotNull(startTime, "startTime 不能为空");
        AssertUtil.checkNotNull(endTime, "endTime 不能为空");

        QueryWrapper<TerminationReasonDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("termination_day,reason,sum(total_count) as total_count");
        queryWrapper.lambda().ge(TerminationReasonDO::getTerminationDay, startTime)
                .lt(TerminationReasonDO::getTerminationDay, endTime)
                .groupBy(TerminationReasonDO::getTerminationDay,TerminationReasonDO::getReason);
        return list(queryWrapper);
    }

}