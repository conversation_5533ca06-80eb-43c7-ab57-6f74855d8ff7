package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.access.AccessTotalDO;
import qnvip.data.overview.mapper.access.AccessTotalMapper;
import qnvip.data.overview.service.access.AccessTotalService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2021-10-13
 */
@Slf4j
@Service
public class AccessTotalServiceImpl extends BaseServiceImpl<AccessTotalMapper, AccessTotalDO> implements AccessTotalService {


    @Override
    public boolean saveOrUpdate(AccessTotalDO domian) {
        QueryWrapper<AccessTotalDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(AccessTotalDO::getCountDay, domian.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<AccessTotalDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(AccessTotalDO::getCountDay, domian.getCountDay());
            update(domian, up);
        }
        return true;
    }

    @Override
    public AccessTotalDO getOne(LocalDateTime time) {
        QueryWrapper<AccessTotalDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                eq(AccessTotalDO::getCountDay, time);
        return getOne(queryWrapper);
    }

    @Override
    public List<AccessTotalDO> getList(LocalDateTime sTime, LocalDateTime eTime) {
        QueryWrapper<AccessTotalDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                between(AccessTotalDO::getCountDay, sTime, eTime);
        return list(queryWrapper);
    }

}
