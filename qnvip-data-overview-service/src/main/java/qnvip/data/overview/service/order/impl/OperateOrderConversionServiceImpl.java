package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OperateOrderConversionDO;
import qnvip.data.overview.mapper.order.OperateOrderConversionMapper;
import qnvip.data.overview.service.order.OperateOrderConversionService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-29
 */
@Slf4j
@Service
public class OperateOrderConversionServiceImpl extends BaseServiceImpl<OperateOrderConversionMapper, OperateOrderConversionDO> implements OperateOrderConversionService {

    @Autowired
    private OperateOrderConversionMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay, Integer miniType) {
        mapper.deleteByCountDay(countDay, miniType);
    }

    @Override
    public List<OperateOrderConversionDO> getList(String fromTime, String toTime, Integer miniType, Integer type) {
        QueryWrapper<OperateOrderConversionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateOrderConversionDO::getMiniType, miniType)
                .eq(OperateOrderConversionDO::getMerchantId, "100")
                .eq(type != null, OperateOrderConversionDO::getType, type)
                .between(OperateOrderConversionDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<OperateOrderConversionDO> getListByMiniTypeList(String fromTime, String toTime, List<Integer> miniType,
                                                                Integer type) {
        QueryWrapper<OperateOrderConversionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                in(CollUtil.isNotEmpty(miniType), OperateOrderConversionDO::getMiniType, miniType)
                .eq(OperateOrderConversionDO::getMerchantId, "100")
                .eq(type != null, OperateOrderConversionDO::getType, type)
                .between(OperateOrderConversionDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public boolean saveOrUpdate(OperateOrderConversionDO domian) {
        QueryWrapper<OperateOrderConversionDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateOrderConversionDO::getCountDay, domian.getCountDay())
                .eq(OperateOrderConversionDO::getMiniType, domian.getMiniType())
                .eq(OperateOrderConversionDO::getType, domian.getType())
                .eq(OperateOrderConversionDO::getMerchantId, domian.getMerchantId());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateOrderConversionDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateOrderConversionDO::getCountDay, domian.getCountDay())
                    .eq(OperateOrderConversionDO::getMiniType, domian.getMiniType())
                    .eq(OperateOrderConversionDO::getType, domian.getType())
                    .eq(OperateOrderConversionDO::getMerchantId, domian.getMerchantId());
            update(domian, up);
        }
        return true;
    }
}
