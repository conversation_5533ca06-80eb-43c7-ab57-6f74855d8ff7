package qnvip.data.overview.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceMerchantOrderDetailDO;
import qnvip.data.overview.mapper.finance.FinanceMerchantOrderDetailMapper;
import qnvip.data.overview.service.finance.FinanceMerchantOrderDetailService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * create by gw on 2022/3/22
 */
@Service
@RequiredArgsConstructor
public class FinanceMerchantOrderDetailServiceImpl extends BaseServiceImpl<FinanceMerchantOrderDetailMapper, FinanceMerchantOrderDetailDO>
        implements FinanceMerchantOrderDetailService {

    @Autowired
    private FinanceMerchantOrderDetailMapper mapper;


    @Override
    public List<FinanceMerchantOrderDetailDO> getList(String sTime,
                                                      String eTime,
                                                      String sEndTime, String eEndTime,
                                                      List<Integer> miniTypeList,
                                                      List<Integer> financeTypeList) {

        // 查询t-1分区数据
        String dsStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        QueryWrapper<FinanceMerchantOrderDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(StringUtils.isNotBlank(sTime), FinanceMerchantOrderDetailDO::getRentStartDate, sTime)
                .le(StringUtils.isNotBlank(eTime), FinanceMerchantOrderDetailDO::getRentStartDate, eTime)
                .ge(StringUtils.isNotBlank(sEndTime), FinanceMerchantOrderDetailDO::getRentEndDate, sEndTime)
                .le(StringUtils.isNotBlank(eEndTime), FinanceMerchantOrderDetailDO::getRentEndDate, eEndTime)
                .in(CollUtil.isNotEmpty(miniTypeList), FinanceMerchantOrderDetailDO::getMiniType, miniTypeList)
                .eq(FinanceMerchantOrderDetailDO::getDs, dsStr)
                .in(CollUtil.isNotEmpty(financeTypeList), FinanceMerchantOrderDetailDO::getFinanceType, financeTypeList)
                .orderByDesc(FinanceMerchantOrderDetailDO::getRentStartDate);
        return list(queryWrapper);
    }


    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }

}