package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.domain.order.OperateScreenRiskDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-25
*/
public interface OperateScreenRiskService extends BaseService<OperateScreenRiskDO>{
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateScreenRiskDO> getList(String fromTime, String toTime, Integer miniType);


    public void removeDataByTime(LocalDateTime countDay);


}
