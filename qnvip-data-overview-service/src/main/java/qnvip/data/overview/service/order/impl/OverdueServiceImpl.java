package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OverdueDO;
import qnvip.data.overview.mapper.order.OverdueMapper;
import qnvip.data.overview.service.order.OverdueService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.util.AssertUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 6:35 下午
 */
@Service
public class OverdueServiceImpl extends BaseServiceImpl<OverdueMapper, OverdueDO> implements OverdueService {

    @Override
    public boolean saveOrUpdate(OverdueDO overdueDO) {
        QueryWrapper<OverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OverdueDO::getMiniType, overdueDO.getMiniType())
                .eq(OverdueDO::getCountDay, overdueDO.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(overdueDO);
        } else {
            UpdateWrapper<OverdueDO> up = new UpdateWrapper<>();
            up.lambda().eq(OverdueDO::getMiniType, overdueDO.getMiniType())
                    .eq(OverdueDO::getCountDay, overdueDO.getCountDay());
            update(overdueDO, up);
        }
        return true;
    }


    @Override
    public List<OverdueDO> getOverdueListByDate(LocalDateTime countDate,Integer miniType){
        AssertUtil.checkNotNull(countDate,"countDate 不能为空");

        QueryWrapper<OverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(OverdueDO::getCountDay,countDate)
                .eq(miniType!=null,OverdueDO::getMiniType,miniType);
        return list(queryWrapper);
    }

    @Override
    public List<OverdueDO> getOverdueListByTime(LocalDateTime startTime,LocalDateTime endTime){
        AssertUtil.checkNotNull(startTime,"startTime 不能为空");
        AssertUtil.checkNotNull(startTime,"endTime 不能为空");
        QueryWrapper<OverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(OverdueDO::getCountDay,startTime)
                .lt(OverdueDO::getCountDay,endTime);
        return list(queryWrapper);
    }

}