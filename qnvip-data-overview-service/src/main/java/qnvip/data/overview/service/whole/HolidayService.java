package qnvip.data.overview.service.whole;

import qnvip.data.overview.domain.whole.HolidayDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2022-07-26
 */
public interface HolidayService extends BaseService<HolidayDO> {

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<HolidayDO> getList(LocalDate fromTime, LocalDate toTime);

    public int getCount(LocalDate time);
}
