package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.ScreenRiskDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/10 3:16 下午
 */
public interface ScreenRiskService extends BaseService<ScreenRiskDO> {

    List<ScreenRiskDO> getListByTime(LocalDateTime startTime, LocalDateTime endTime);

}
