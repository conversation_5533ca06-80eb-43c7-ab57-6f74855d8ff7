package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.ComponentDO;
import qnvip.data.overview.mapper.order.ComponentMapper;
import qnvip.data.overview.service.order.ComponentService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 9:34 下午
 */
@Service
public class ComponentServiceImpl extends BaseServiceImpl<ComponentMapper,ComponentDO> implements ComponentService {

    @Override
    public boolean saveOrUpdate(ComponentDO componentDO) {
        QueryWrapper<ComponentDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ComponentDO::getCountDay, componentDO.getCountDay());
        int count = count(queryWrapper);
        if(count==0){
            save(componentDO);
        }else {
            UpdateWrapper<ComponentDO> up = new UpdateWrapper<>();
            up.lambda().eq( ComponentDO::getCountDay, componentDO.getCountDay());
            update(componentDO,up);
        }
        return true;
    }


    @Override
    public List<ComponentDO> getListByTime(LocalDateTime startTime, LocalDateTime endTime){
        QueryWrapper<ComponentDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(ComponentDO::getCountDay, startTime)
                .lt(ComponentDO::getCountDay, endTime);
        return list(queryWrapper);
    }

}