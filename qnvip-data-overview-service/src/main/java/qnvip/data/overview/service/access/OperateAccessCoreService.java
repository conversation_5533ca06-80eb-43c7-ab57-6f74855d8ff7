package qnvip.data.overview.service.access;

import com.baomidou.dynamic.datasource.annotation.DS;
import qnvip.data.overview.domain.access.AccessTotalDO;
import qnvip.data.overview.domain.access.OperateAccessCoreDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON> on 2021-11-15
 */
public interface OperateAccessCoreService extends BaseService<OperateAccessCoreDO> {


    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateAccessCoreDO> getList(String fromTime, String toTime,Integer miniType);

    public void removeDataByTime(LocalDateTime countDay);

}
