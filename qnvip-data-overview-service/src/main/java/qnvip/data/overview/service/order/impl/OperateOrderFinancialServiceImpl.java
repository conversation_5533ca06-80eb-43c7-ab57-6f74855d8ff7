package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateOrderFinancialDO;
import qnvip.data.overview.mapper.order.OperateOrderFinancialMapper;
import qnvip.data.overview.service.order.OperateOrderFinancialService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-12-09
 */
@Slf4j
@Service
public class OperateOrderFinancialServiceImpl extends BaseServiceImpl<OperateOrderFinancialMapper, OperateOrderFinancialDO> implements OperateOrderFinancialService {

    @Autowired
    private OperateOrderFinancialMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateOrderFinancialDO> getList(String fromTime,
                                                 String toTime,
                                                 Integer miniType) {
        QueryWrapper<OperateOrderFinancialDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateOrderFinancialDO::getMiniType, miniType)
                .between(OperateOrderFinancialDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }
}
