package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.ComponentDO;
import qnvip.data.overview.domain.order.ComponentDetailDO;
import qnvip.data.overview.domain.order.GoodsDO;
import qnvip.data.overview.mapper.order.ComponentDetailMapper;
import qnvip.data.overview.service.order.ComponentDetailService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;
import qnvip.rent.common.util.AssertUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/13 1:28 下午
 */
@Service
public class ComponentDetailServiceImpl extends BaseServiceImpl<ComponentDetailMapper, ComponentDetailDO> implements ComponentDetailService {
    @Override
    public boolean saveOrUpdate(ComponentDetailDO detailDO) {
        QueryWrapper<ComponentDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(ComponentDetailDO::getCountDay, detailDO.getCountDay())
                .eq(ComponentDetailDO::getItemId,detailDO.getCountDay());
        int count = count(queryWrapper);
        if(count==0){
            save(detailDO);
        }else {
            UpdateWrapper<ComponentDetailDO> up = new UpdateWrapper<>();
            up.lambda().eq( ComponentDetailDO::getCountDay, detailDO.getCountDay())
                    .eq(ComponentDetailDO::getItemId,detailDO.getItemId());
            update(detailDO,up);
        }
        return true;
    }

    @Override
    public MultiResult<ComponentDetailDO> getGroupPageByDate(LocalDateTime startTime, LocalDateTime endTime, Integer pageNo, Integer pageSize){
        AssertUtil.checkNotNull(startTime, "startTime 不能为空");
        AssertUtil.checkNotNull(endTime, "endTime 不能为空");
        pageNo = pageNo==null?1:pageNo;
        pageSize = pageSize==null?10:pageSize;

        QueryWrapper<ComponentDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" item_id,item_name,sum(gmv) as gmv, sum(pay_count) as pay_count,sum(order_count) as order_count ");
        queryWrapper.lambda().ge(ComponentDetailDO::getCountDay, startTime)
                .lt(ComponentDetailDO::getCountDay, endTime)
                .groupBy(ComponentDetailDO::getItemId)
                .orderByDesc(ComponentDetailDO::getPayCount);
        Page<ComponentDetailDO> page = new Page<>(pageNo, pageSize);
        Page<ComponentDetailDO> detailPage = page(page, queryWrapper);
        return MultiResult.of(detailPage.getRecords(), detailPage.getTotal());
    }

}