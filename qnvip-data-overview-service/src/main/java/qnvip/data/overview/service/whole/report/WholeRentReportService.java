package qnvip.data.overview.service.whole.report;

import qnvip.data.overview.domain.whole.report.WholeRentReportDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON>gtong on 2023-02-09
 */
public interface WholeRentReportService extends BaseService<WholeRentReportDO> {
    public void removeDataByDs(String ds);

    /**
     * getList by condition
     *
     * @return
     */
    public List<WholeRentReportDO> getList(LocalDate fromTime, LocalDate toTime, String ds);
}
