package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateOrderStatusDO;
import qnvip.data.overview.domain.order.OperateOrderStatusDO;
import qnvip.data.overview.mapper.order.OperateOrderStatusMapper;
import qnvip.data.overview.service.order.OperateOrderStatusService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by zhen<PERSON>ong on 2021-12-03
*/
@Slf4j
@Service
public class OperateOrderStatusServiceImpl extends BaseServiceImpl<OperateOrderStatusMapper, OperateOrderStatusDO> implements OperateOrderStatusService {

        @Autowired
        private OperateOrderStatusMapper mapper;

        @Override
        public void removeDataByTime(LocalDateTime countDay) {
            mapper.deleteByCountDay(countDay);
        }

        @Override
        public List<OperateOrderStatusDO> getList(String fromTime, String toTime, Integer miniType) {
                QueryWrapper<OperateOrderStatusDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.
                        lambda().
                        eq(miniType != null, OperateOrderStatusDO::getMiniType, miniType)
                        .eq(OperateOrderStatusDO::getMerchantId, "100")
                        .between(OperateOrderStatusDO::getCountDay, fromTime, toTime);
                return list(queryWrapper);
        }
}
