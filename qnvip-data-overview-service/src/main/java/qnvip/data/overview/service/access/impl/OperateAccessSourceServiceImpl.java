package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.OperateAccessSourceDO;
import qnvip.data.overview.mapper.access.OperateAccessSourceMapper;
import qnvip.data.overview.service.access.OperateAccessSourceService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-12-13
 */
@Slf4j
@Service
public class OperateAccessSourceServiceImpl extends BaseServiceImpl<OperateAccessSourceMapper, OperateAccessSourceDO> implements OperateAccessSourceService {

    @Autowired
    private OperateAccessSourceMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public MultiResult<OperateAccessSourceDO> getPageList(String fromTime,
                                                          String toTime,
                                                          String scene,
                                                          Integer pageNo,
                                                          Integer pageSize,
                                                          Integer type) {
        QueryWrapper<OperateAccessSourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(scene != null, OperateAccessSourceDO::getScene, scene)
                .eq(type != null, OperateAccessSourceDO::getType, type)
                .between(OperateAccessSourceDO::getCountDay, fromTime, toTime)
                .orderByDesc(OperateAccessSourceDO::getOrderCount);
        Page<OperateAccessSourceDO> page = new Page<>(pageNo, pageSize);
        Page<OperateAccessSourceDO> accessSourcePage = page(page, queryWrapper);
        return MultiResult.of(accessSourcePage.getRecords(), accessSourcePage.getTotal());
    }

    @Override
    public MultiResult<OperateAccessSourceDO> getPageListByMiniType(String fromTime,
                                                                    String toTime,
                                                                    String scene,
                                                                    Integer pageNo,
                                                                    Integer pageSize,
                                                                    Integer type,
                                                                    List<Integer> miniType) {
        QueryWrapper<OperateAccessSourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(scene != null, OperateAccessSourceDO::getScene, scene)
                .eq(type != null, OperateAccessSourceDO::getType, type)
                .in(CollectionUtils.isNotEmpty(miniType), OperateAccessSourceDO::getMiniType, miniType)
                .between(OperateAccessSourceDO::getCountDay, fromTime, toTime)
                .orderByDesc(OperateAccessSourceDO::getUv);
        Page<OperateAccessSourceDO> page = new Page<>(pageNo, pageSize);
        Page<OperateAccessSourceDO> accessSourcePage = page(page, queryWrapper);
        return MultiResult.of(accessSourcePage.getRecords(), accessSourcePage.getTotal());
    }

    @Override
    public List<OperateAccessSourceDO> getList(String fromTime,
                                               String toTime,
                                               String scene,
                                               Integer type,
                                               List<Integer> miniType) {
        QueryWrapper<OperateAccessSourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(scene != null, OperateAccessSourceDO::getScene, scene)
                .eq(type != null, OperateAccessSourceDO::getType, type)
                .in(CollectionUtils.isNotEmpty(miniType), OperateAccessSourceDO::getMiniType, miniType)
                .ge(OperateAccessSourceDO::getCountDay, fromTime)
                .le(OperateAccessSourceDO::getCountDay, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<OperateAccessSourceDO> getSceneList(String fromTime,
                                                    String toTime,
                                                    Integer type) {
        QueryWrapper<OperateAccessSourceDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(type != null, OperateAccessSourceDO::getType, type)
                .select(OperateAccessSourceDO::getScene, OperateAccessSourceDO::getSceneName)
                .ge(OperateAccessSourceDO::getCountDay, fromTime)
                .le(OperateAccessSourceDO::getCountDay, toTime);
        return list(queryWrapper);
    }
}
