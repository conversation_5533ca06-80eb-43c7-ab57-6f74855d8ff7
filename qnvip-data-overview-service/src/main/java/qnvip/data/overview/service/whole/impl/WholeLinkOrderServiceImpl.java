package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkOrderDO;
import qnvip.data.overview.mapper.whole.WholeLinkOrderMapper;
import qnvip.data.overview.service.whole.WholeLinkOrderService;
import qnvip.rent.common.base.BaseServiceImpl;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.List;

/**
* Created by z<PERSON><PERSON><PERSON> on 2022-07-25
*/
@Slf4j
@Service
public class WholeLinkOrderServiceImpl extends BaseServiceImpl<WholeLinkOrderMapper, WholeLinkOrderDO> implements WholeLinkOrderService {

        @Resource
        private WholeLinkOrderMapper mapper;

        @Override
        public void removeByHour(Integer hour, LocalDate now) {
                mapper.removeByHour(now, hour);
        }

        @Override
        public List<WholeLinkOrderDO> getListByHour(LocalDate fromTime, LocalDate toTime, Integer hour) {
                QueryWrapper<WholeLinkOrderDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.
                        lambda()
                        .eq(WholeLinkOrderDO::getCountHour, hour)
                        .between(WholeLinkOrderDO::getCountDay, fromTime, toTime);
                return list(queryWrapper);
        }

        @Override
        public WholeLinkOrderDO getOneByMiniType(LocalDate time, Integer miniType) {
                QueryWrapper<WholeLinkOrderDO> queryWrapper = new QueryWrapper<>();
                queryWrapper.
                        lambda()
                        .eq(WholeLinkOrderDO::getMiniType, miniType)
                        .eq(WholeLinkOrderDO::getCountDay, time)
                        .orderByDesc(WholeLinkOrderDO::getCountHour);
                return getOne(queryWrapper, false);
        }
}
