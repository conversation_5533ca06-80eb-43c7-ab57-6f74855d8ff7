package qnvip.data.overview.service.order.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.GoodsDO;
import qnvip.data.overview.mapper.order.GoodsMapper;
import qnvip.data.overview.service.order.GoodsService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;
import qnvip.rent.common.util.AssertUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 8:55 下午
 */
@Service
public class GoodsServiceImpl extends BaseServiceImpl<GoodsMapper, GoodsDO> implements GoodsService {
    @Override
    public boolean saveOrUpdate(GoodsDO goodsDO) {
        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(GoodsDO::getCountDay, goodsDO.getCountDay())
                .eq(GoodsDO::getItemId, goodsDO.getItemId())
                .eq(GoodsDO::getModel,goodsDO.getModel());
        int count = count(queryWrapper);
        if (count == 0) {
            save(goodsDO);
        } else {
            UpdateWrapper<GoodsDO> up = new UpdateWrapper<>();
            up.lambda().eq(GoodsDO::getCountDay, goodsDO.getCountDay())
                    .eq(GoodsDO::getItemId, goodsDO.getItemId())
                    .eq(GoodsDO::getModel,goodsDO.getModel());
            update(goodsDO, up);
        }
        return true;
    }

    @Override
    public List<GoodsDO> getGroupListByDate(LocalDateTime startTime, LocalDateTime endTime, Integer pageNo, Integer pageSize) {
        AssertUtil.checkNotNull(startTime, "startTime 不能为空");
        AssertUtil.checkNotNull(endTime, "endTime 不能为空");
        pageNo = pageNo == null ? 1 : pageNo;
        pageSize = pageSize == null ? 10 : pageSize;

        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" count_day,first_category_ids,first_category_names,second_category_names,second_category_ids,item_id,goods_name," +
                "model,sum(total_worth) as total_worth, sum(pay_count) as pay_count,sum(total_term) as total_term,sum(total_supply_price) as " +
                " total_supply_price,sum(total_buy_price) as total_buy_price ");
        queryWrapper.lambda().ge(GoodsDO::getCountDay, startTime)
                .lt(GoodsDO::getCountDay, endTime)
                .groupBy(GoodsDO::getItemId,GoodsDO::getModel)
                .orderByDesc(GoodsDO::getPayCount)
                .last(" limit " + (pageNo - 1) * pageSize + "," + pageSize);
        return list(queryWrapper);
    }


    @Override
    public MultiResult<GoodsDO> getGroupPageByDate(LocalDateTime startTime, LocalDateTime endTime, String mainCategory, Integer pageNo, Integer pageSize) {
        AssertUtil.checkNotNull(startTime, "startTime 不能为空");
        AssertUtil.checkNotNull(endTime, "endTime 不能为空");
        pageNo = pageNo == null ? 1 : pageNo;
        pageSize = pageSize == null ? 10 : pageSize;

        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" item_id,goods_name,model,sum(total_worth)" +
                " as total_worth, sum(pay_count) as pay_count,sum(total_term) as total_term,sum(total_supply_price) as " +
                " total_supply_price,sum(total_buy_price) as total_buy_price ");
        queryWrapper.lambda().ge(GoodsDO::getCountDay, startTime)
                .le(GoodsDO::getCountDay, endTime)
                .eq(GoodsDO::getMainCategory, mainCategory)
                .groupBy(GoodsDO::getItemId,GoodsDO::getModel)
                .orderByDesc(GoodsDO::getPayCount);
        Page<GoodsDO> page = new Page<>(pageNo, pageSize);
        Page<GoodsDO> goodsPage = page(page, queryWrapper);
        return MultiResult.of(goodsPage.getRecords(), goodsPage.getTotal());
    }


    @Override
    public GoodsDO getTotalData(LocalDateTime startTime, LocalDateTime endTime) {
        AssertUtil.checkNotNull(startTime, "startTime 不能为空");
        AssertUtil.checkNotNull(endTime, "endTime 不能为空");

        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select(" sum(total_worth) as total_worth, sum(pay_count) as pay_count,sum(total_term) as total_term,sum(total_supply_price) as " +
                " total_supply_price,sum(total_buy_price) as total_buy_price ");
        queryWrapper.lambda().ge(GoodsDO::getCountDay, startTime)
                .le(GoodsDO::getCountDay, endTime);
        return getOne(queryWrapper);
    }

    @Override
    public List<GoodsDO> listByTime(LocalDateTime sTime, LocalDateTime eTime) {
        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(GoodsDO::getCountDay, sTime)
                .le(GoodsDO::getCountDay, eTime);
        return list(queryWrapper);
    }

    @Override
    public MultiResult<GoodsDO> page(String goodsName, LocalDateTime startTime, LocalDateTime endTime, int current, int pageSize) {
        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().like(StrUtil.isNotBlank(goodsName), GoodsDO::getGoodsName, goodsName)
                .ge(GoodsDO::getCountDay, startTime)
                .le(GoodsDO::getCountDay, endTime);
        Page<GoodsDO> page = new Page<>(current, pageSize);
        Page<GoodsDO> goodsPage = page(page, queryWrapper);
        return MultiResult.of(goodsPage.getRecords(), goodsPage.getTotal());
    }

    @Override
    public List<GoodsDO> list(String goodsName, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .like(StrUtil.isNotBlank(goodsName), GoodsDO::getGoodsName, goodsName)
                .ge(GoodsDO::getCountDay, startTime)
                .le(GoodsDO::getCountDay, endTime)
                .orderByDesc(GoodsDO::getPayCount);
        return list(queryWrapper);
    }


    @Override
    public List<GoodsDO> getByIdListAndTime(List<Object> idList, LocalDateTime startTime, LocalDateTime endTime) {
        QueryWrapper<GoodsDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .in(!CollectionUtils.isEmpty(idList), GoodsDO::getItemId, idList)
                .ge(GoodsDO::getCountDay, startTime)
                .le(GoodsDO::getCountDay, endTime);
        return list(queryWrapper);
    }

}