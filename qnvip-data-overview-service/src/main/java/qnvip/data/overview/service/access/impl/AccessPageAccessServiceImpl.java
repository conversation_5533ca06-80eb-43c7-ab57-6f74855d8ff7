package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.access.AccessPageAccessDO;
import qnvip.data.overview.mapper.access.AccessPageAccessMapper;
import qnvip.data.overview.service.access.AccessPageAccessService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
 * Created by zhengtong on 2021-10-09
 */
@Slf4j
@Service
public class AccessPageAccessServiceImpl extends BaseServiceImpl<AccessPageAccessMapper, AccessPageAccessDO> implements AccessPageAccessService {
    @Override
    public boolean saveOrUpdate(AccessPageAccessDO accessPageAccessDO) {
        QueryWrapper<AccessPageAccessDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().
                eq(AccessPageAccessDO::getMiniType, accessPageAccessDO.getMiniType()).
                eq(AccessPageAccessDO::getEnterPageCode, accessPageAccessDO.getEnterPageCode()).
                eq(AccessPageAccessDO::getCountDay, accessPageAccessDO.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(accessPageAccessDO);
        } else {
            UpdateWrapper<AccessPageAccessDO> up = new UpdateWrapper<>();
            up.lambda().eq(AccessPageAccessDO::getMiniType, accessPageAccessDO.getMiniType()).
                    eq(AccessPageAccessDO::getEnterPageCode, accessPageAccessDO.getEnterPageCode())
                    .eq(AccessPageAccessDO::getCountDay, accessPageAccessDO.getCountDay());
            update(accessPageAccessDO, up);
        }
        return true;
    }

    @Override
    public List<AccessPageAccessDO> getList(Integer miniType, String fromTime, String toTime) {
        QueryWrapper<AccessPageAccessDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                eq(miniType != null, AccessPageAccessDO::getMiniType, miniType).
                ge(AccessPageAccessDO::getCountDay, fromTime).
                le(AccessPageAccessDO::getCountDay, toTime);

        return list(queryWrapper);
    }

    @Override
    public MultiResult<AccessPageAccessDO> page(Integer miniType,
                                                String startTime,
                                                String endTime,
                                                int current,
                                                int pageSize) {
        QueryWrapper<AccessPageAccessDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().
                eq(miniType != null, AccessPageAccessDO::getMiniType, miniType)
                .ge(AccessPageAccessDO::getCountDay, startTime)
                .le(AccessPageAccessDO::getCountDay, endTime);
        Page<AccessPageAccessDO> page = new Page<>(current, pageSize);
        Page<AccessPageAccessDO> doPage = page(page, queryWrapper);
        return MultiResult.of(doPage.getRecords(), doPage.getTotal());
    }
}
