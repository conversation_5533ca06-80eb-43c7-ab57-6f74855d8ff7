package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.access.OperateAccessCoreDO;
import qnvip.data.overview.domain.access.OperateHourDO;
import qnvip.data.overview.mapper.access.OperateHourMapper;
import qnvip.data.overview.mapper.access.OperateMinuteMapper;
import qnvip.data.overview.service.access.OperateHourService;
import qnvip.data.overview.service.access.OperateMinuteService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2021-11-18
 */
@Slf4j
@Service
public class OperateHourServiceImpl extends BaseServiceImpl<OperateHourMapper, OperateHourDO> implements OperateHourService {

    @Autowired
    private  OperateHourMapper hourMapper;

    @Override
    public List<OperateHourDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateHourDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateHourDO::getMiniType, miniType)
                .ge(OperateHourDO::getCountDay, fromTime)
                .le(OperateHourDO::getCountDay, toTime);
        return list(queryWrapper);
    }

    @Override
    public boolean saveOrUpdate(OperateHourDO domian) {
        QueryWrapper<OperateHourDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateHourDO::getCountDay, domian.getCountDay())
                .eq(OperateHourDO::getCountTime, domian.getCountTime())
                .eq(OperateHourDO::getMiniType, domian.getMiniType());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateHourDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateHourDO::getCountDay, domian.getCountDay())
                    .eq(OperateHourDO::getCountTime, domian.getCountTime())
                    .eq(OperateHourDO::getMiniType, domian.getMiniType());
            update(domian, up);
        }
        return true;
    }

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        hourMapper.deleteByCountDay(countDay);
    }
}
