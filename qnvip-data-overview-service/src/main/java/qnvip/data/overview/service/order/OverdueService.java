package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OverdueDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/9 6:34 下午
 */
public interface OverdueService extends BaseService<OverdueDO> {

    List<OverdueDO> getOverdueListByDate(LocalDateTime countDate, Integer miniType);

    List<OverdueDO> getOverdueListByTime(LocalDateTime startTime,LocalDateTime endTime);

}
