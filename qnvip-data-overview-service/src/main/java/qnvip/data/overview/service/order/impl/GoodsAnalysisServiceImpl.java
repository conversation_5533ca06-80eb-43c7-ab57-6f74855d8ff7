package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.GoodsAnalysisDO;
import qnvip.data.overview.mapper.order.GoodsAnalysisMapper;
import qnvip.data.overview.service.order.GoodsAnalysisService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
 * Created by zhengtong on 2021-10-13
 */
@Slf4j
@Service
public class GoodsAnalysisServiceImpl extends BaseServiceImpl<GoodsAnalysisMapper, GoodsAnalysisDO> implements GoodsAnalysisService {

    @Override
    public boolean saveOrUpdate(GoodsAnalysisDO domain) {
        QueryWrapper<GoodsAnalysisDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(GoodsAnalysisDO::getItemId, domain.getItemId())
                .eq(GoodsAnalysisDO::getName, domain.getName())
                .eq(GoodsAnalysisDO::getCountDay, domain.getCountDay());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domain);
        } else {
            UpdateWrapper<GoodsAnalysisDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(GoodsAnalysisDO::getItemId, domain.getItemId())
                    .eq(GoodsAnalysisDO::getName, domain.getName())
                    .eq(GoodsAnalysisDO::getCountDay, domain.getCountDay());
            update(domain, up);
        }
        return true;
    }

    @Override
    public GoodsAnalysisDO getOne(
            String fromTime,
            String toTime) {
        QueryWrapper<GoodsAnalysisDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                ge(GoodsAnalysisDO::getCountDay, fromTime).
                lt(GoodsAnalysisDO::getCountDay, toTime);

        return getOne(queryWrapper);
    }

    @Override
    public MultiResult<GoodsAnalysisDO> page(String fromTime, String toTime, Integer pageNo, Integer pageSize) {
        QueryWrapper<GoodsAnalysisDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                ge(GoodsAnalysisDO::getCountDay, fromTime).
                lt(GoodsAnalysisDO::getCountDay, toTime).
                orderByDesc(GoodsAnalysisDO::getUv);
        Page<GoodsAnalysisDO> page = new Page<>(pageNo, pageSize);
        Page<GoodsAnalysisDO> goodsPage = page(page, queryWrapper);

        return MultiResult.of(goodsPage.getRecords(), goodsPage.getTotal());
    }

    @Override
    public List<GoodsAnalysisDO> getList(String fromTime, String toTime) {
        QueryWrapper<GoodsAnalysisDO> queryWrapper = new QueryWrapper<>();

        queryWrapper.
                lambda().
                ge(GoodsAnalysisDO::getCreateTime, fromTime).
                lt(GoodsAnalysisDO::getCreateTime, toTime);
        return list(queryWrapper);
    }
}
