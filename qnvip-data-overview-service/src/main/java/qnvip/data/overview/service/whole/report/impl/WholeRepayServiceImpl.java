package qnvip.data.overview.service.whole.report.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.mongo.WholeRepayMongoDO;
import qnvip.data.overview.domain.whole.WholeRepayDO;
import qnvip.data.overview.mapper.whole.report.WholeRepayMapper;
import qnvip.data.overview.param.WholeRepayParam;
import qnvip.data.overview.service.whole.report.WholeRepayService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.util.CopierUtil;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by zhengtong on 2023-03-06
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeRepayServiceImpl extends BaseServiceImpl<WholeRepayMapper, WholeRepayDO> implements WholeRepayService {
    private final MongoTemplate mongoTemplate;
    private final WholeRepayMapper mapper;

    @Override
    public void removeMongoDO() {
        mongoTemplate.dropCollection(WholeRepayMongoDO.class);
    }

    @Override
    public void deleteAll() {
        mapper.deleteByOrderType();
    }

    @Override
    public void save2Mongo(List<WholeRepayDO> list) {
        List<WholeRepayMongoDO> wholeRepayMongoDOS = CopierUtil.copyList(list, WholeRepayMongoDO.class);
        mongoTemplate.insertAll(wholeRepayMongoDOS);
    }

    @Override
    public List<WholeRepayDO> getList(WholeRepayParam param, Integer repayType) {
        QueryWrapper<WholeRepayDO> queryWrapper = new QueryWrapper<>();
        Object miniType = param.getMiniType();
        queryWrapper.
                lambda()
                .select(WholeRepayDO.class, i -> i.getFieldFill() == FieldFill.DEFAULT)
                .eq(WholeRepayDO::getCountMonth, param.getCountMonth())
                .eq(WholeRepayDO::getRepayType, repayType)
                .eq(ObjectUtil.isNotNull(param.getBusinessType()), WholeRepayDO::getBusinessType, param.getBusinessType())
                .eq(ObjectUtil.isNotNull(param.getOrderType()), WholeRepayDO::getOrderType, param.getOrderType())
                .eq(ObjectUtil.isNotNull(miniType) && miniType instanceof Integer, WholeRepayDO::getMiniType,
                        miniType)
                .eq(ObjectUtil.isNotNull(miniType) && miniType instanceof String, WholeRepayDO::getPlatform, miniType)
                .eq(ObjectUtil.isNotNull(param.getQuotientName()), WholeRepayDO::getQuotientName, param.getQuotientName())
        ;
        return list(queryWrapper);
    }

    @Override
    public List<WholeRepayDO> getSceneList() {
        QueryWrapper<WholeRepayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(WholeRepayDO::getQuotientName, WholeRepayDO::getMiniType)
                .groupBy(WholeRepayDO::getQuotientName, WholeRepayDO::getMiniType)
        ;
        return list(queryWrapper);
    }

    @Override
    public List<WholeRepayDO> getListFromMongo(Integer repayType, LocalDate from, LocalDate to) {
        Query query = new Query();
        query.fields().include("countMonth");
        query.fields().include("countDay");
        query.fields().include("t0Repay");
        query.fields().include("aheadWithhold");
        query.fields().include("aheadActive");
        query.fields().include("repay");
        query.fields().include("t7Repay");
        query.fields().include("t28Repay");
        query.fields().include("m2Repay");
        query.fields().include("quotientName");
        query.fields().include("platform");
        query.fields().include("orderType");
        query.fields().include("miniType");
        query.fields().include("businessType");
        Criteria criteria = Criteria.where("countDay").gte(from).lte(to).and("repayType").is(repayType);
        query.addCriteria(criteria);
        List<WholeRepayMongoDO> wholeRepayMongoDOS = mongoTemplate.find(query, WholeRepayMongoDO.class);
        return CopierUtil.copyList(wholeRepayMongoDOS, WholeRepayDO.class);
    }

}
