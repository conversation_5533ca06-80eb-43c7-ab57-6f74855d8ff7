package qnvip.data.overview.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceMerchantReportDO;
import qnvip.data.overview.mapper.finance.FinanceMerchantReportMapper;
import qnvip.data.overview.service.finance.FinanceMerchantReportService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.List;

/**
 * Created by zhengtong on 2022-10-17
 */
@Slf4j
@Service
public class FinanceMerchantReportServiceImpl extends BaseServiceImpl<FinanceMerchantReportMapper, FinanceMerchantReportDO> implements FinanceMerchantReportService {

    @Autowired
    private FinanceMerchantReportMapper mapper;

    @Override
    public boolean saveOrUpdate(FinanceMerchantReportDO riskPayDO) {
        QueryWrapper<FinanceMerchantReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(FinanceMerchantReportDO::getCountDay, riskPayDO.getCountDay())
                .eq(FinanceMerchantReportDO::getMiniType, riskPayDO.getMiniType())
                .eq(FinanceMerchantReportDO::getFinanceType, riskPayDO.getFinanceType());
        if (!exist(queryWrapper)) {
            save(riskPayDO);
        } else {
            UpdateWrapper<FinanceMerchantReportDO> up = new UpdateWrapper<>();
            up.lambda().eq(FinanceMerchantReportDO::getCountDay, riskPayDO.getCountDay())
                    .eq(FinanceMerchantReportDO::getMiniType, riskPayDO.getMiniType())
                    .eq(FinanceMerchantReportDO::getFinanceType, riskPayDO.getFinanceType());
            update(riskPayDO, up);
        }
        return true;
    }

    @Override
    public List<FinanceMerchantReportDO> getList(String sTime, String eTime, String sParentTime, String eParentTime,
                                         List<Integer> miniTypeList, List<Integer> financeTypeList) {

        QueryWrapper<FinanceMerchantReportDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(StringUtils.isNotBlank(sTime), FinanceMerchantReportDO::getCountDay, sTime)
                .le(StringUtils.isNotBlank(eTime), FinanceMerchantReportDO::getCountDay, eTime)
                .ge(StringUtils.isNotBlank(sParentTime), FinanceMerchantReportDO::getRentStartDate, sParentTime)
                .le(StringUtils.isNotBlank(eParentTime), FinanceMerchantReportDO::getRentStartDate, eParentTime)
                .in(CollUtil.isNotEmpty(miniTypeList), FinanceMerchantReportDO::getMiniType, miniTypeList)
                .in(CollUtil.isNotEmpty(financeTypeList), FinanceMerchantReportDO::getFinanceType, financeTypeList);
        return list(queryWrapper);
    }



    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }

}
