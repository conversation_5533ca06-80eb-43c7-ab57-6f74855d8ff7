package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.RentBusinessPlateDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;
/**
* Created by z<PERSON><PERSON>ong on 2022-12-01
*/
public interface RentBusinessPlateService extends BaseService<RentBusinessPlateDO>{
    /**
    * getList by condition
    *
    * @return
    */
    List<RentBusinessPlateDO> getList(String fromTime,List<Integer> miniType);

    List<RentBusinessPlateDO> getList(String fromTime, String endTime, List<Integer> miniType);
}
