package qnvip.data.overview.service.order.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.order.OperateMarginDO;
import qnvip.data.overview.domain.order.OperateMarginDO;
import qnvip.data.overview.mapper.order.OperateGoodsMapper;
import qnvip.data.overview.mapper.order.OperateMarginMapper;
import qnvip.data.overview.service.order.OperateMarginService;
import qnvip.rent.common.base.BaseServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-22
 */
@Slf4j
@Service
public class OperateMarginServiceImpl extends BaseServiceImpl<OperateMarginMapper, OperateMarginDO> implements OperateMarginService {

    @Autowired
    private OperateMarginMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }
    @Override
    public List<OperateMarginDO> getList(String fromTime, String toTime) {
        QueryWrapper<OperateMarginDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(OperateMarginDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public boolean saveOrUpdate(OperateMarginDO domian) {
        QueryWrapper<OperateMarginDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateMarginDO::getCountDay, domian.getCountDay())
                .eq(OperateMarginDO::getLevel, domian.getLevel());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateMarginDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateMarginDO::getCountDay, domian.getCountDay())
                    .eq(OperateMarginDO::getLevel, domian.getLevel());
            update(domian, up);
        }
        return true;
    }
}
