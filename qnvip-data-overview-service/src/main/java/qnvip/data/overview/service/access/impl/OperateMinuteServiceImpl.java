package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.OperateHourDO;
import qnvip.data.overview.domain.access.OperateMinuteDO;
import qnvip.data.overview.domain.access.OperateMinuteDO;
import qnvip.data.overview.mapper.access.OperateHourMapper;
import qnvip.data.overview.mapper.access.OperateMinuteMapper;
import qnvip.data.overview.service.access.OperateMinuteService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-18
 */
@Slf4j
@Service
public class OperateMinuteServiceImpl extends BaseServiceImpl<OperateMinuteMapper, OperateMinuteDO> implements OperateMinuteService {
    @Autowired
    private OperateMinuteMapper minuteMapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        minuteMapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateMinuteDO> getList(String fromTime, String toTime, Integer miniType) {
        QueryWrapper<OperateMinuteDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda().
                eq(miniType != null, OperateMinuteDO::getMiniType, miniType)
                .ge(OperateMinuteDO::getCountDay, fromTime)
                .le(OperateMinuteDO::getCountDay, toTime);
        return list(queryWrapper);
    }

    @Override
    public boolean saveOrUpdate(OperateMinuteDO domian) {
        QueryWrapper<OperateMinuteDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(OperateMinuteDO::getCountDay, domian.getCountDay())
                .eq(OperateMinuteDO::getCountTime, domian.getCountTime())
                .eq(OperateMinuteDO::getMiniType, domian.getMiniType());
        int count = count(queryWrapper);
        if (count == 0) {
            save(domian);
        } else {
            UpdateWrapper<OperateMinuteDO> up = new UpdateWrapper<>();
            up.lambda()
                    .eq(OperateMinuteDO::getCountDay, domian.getCountDay())
                    .eq(OperateMinuteDO::getCountTime, domian.getCountTime())
                    .eq(OperateMinuteDO::getMiniType, domian.getMiniType());
            update(domian, up);
        }
        return true;
    }

}
