package qnvip.data.overview.service.whole.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.HolidayDO;
import qnvip.data.overview.mapper.whole.HolidayMapper;
import qnvip.data.overview.service.whole.HolidayService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.util.List;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022-07-26
 */
@Slf4j
@Service
public class HolidayServiceImpl extends BaseServiceImpl<HolidayMapper, HolidayDO> implements HolidayService {


    @Override
    public List<HolidayDO> getList(LocalDate fromTime, LocalDate toTime) {
        QueryWrapper<HolidayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(HolidayDO::getCountDay, HolidayDO::getType)
                .between(HolidayDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public int getCount(LocalDate time) {
        QueryWrapper<HolidayDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .eq(HolidayDO::getCountDay, time);
        return count(queryWrapper);
    }
}
