package qnvip.data.overview.service.finance;

import qnvip.data.overview.domain.finance.FinanceOrderDetailDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * create by gw on 2022/3/22
 */
public interface FinanceOrderDetailService extends BaseService<FinanceOrderDetailDO> {

    List<FinanceOrderDetailDO> getList(String sTime, String eTime,
                                       String sEndTime, String eEndTime,
                                       String sParentTime, String eParentTime,
                                       String sParentEndTime, String eParentEndTime,
                                       Integer businessType,
                                       List<Integer> miniTypeList, List<Integer> financeTypeList, Integer pageNo, Integer pageSize, Boolean renewFlag);

    void deleteAll();

}
