package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OperationQuotientDO;
import qnvip.data.overview.mapper.order.OperationQuotientMapper;
import qnvip.data.overview.service.order.OperationQuotientService;
import qnvip.rent.common.base.BaseServiceImpl;
import qnvip.rent.common.base.MultiResult;

import java.util.List;
import java.util.Map;

/**
 * Created by zhen<PERSON>ong on 2022-11-08
 */
@Slf4j
@Service
public class OperationQuotientServiceImpl extends BaseServiceImpl<OperationQuotientMapper, OperationQuotientDO> implements OperationQuotientService {

    @Autowired
    private OperationQuotientMapper mapper;

    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }


    @Override
    public List<OperationQuotientDO> getSelectionList(String fromTime,
                                                      String toTime,
                                                      Integer countType) {
        QueryWrapper<OperationQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct count_day,scene,platform,mini_type,quotient_name").
                lambda().
                between(OperationQuotientDO::getCountDay, fromTime, toTime).
                eq(OperationQuotientDO::getCountType, countType);
        return list(queryWrapper);
    }

    @Override
    public MultiResult<OperationQuotientDO> getPageList(String fromTime,
                                                        String toTime,
                                                        List<Integer> miniType,
                                                        List<String> platformList,
                                                        List<String> sceneList,
                                                        List<String> quotientNameList,
                                                        Integer countType,
                                                        Integer pageSize,
                                                        Integer pageNo) {

        QueryWrapper<OperationQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(OperationQuotientDO.class, info -> !info.getColumn().equals("tag")).
                between(OperationQuotientDO::getCountDay, fromTime, toTime).
                eq(OperationQuotientDO::getCountType, countType).
                in(CollUtil.isNotEmpty(miniType), OperationQuotientDO::getMiniType, miniType).
                in(CollUtil.isNotEmpty(platformList), OperationQuotientDO::getPlatform, platformList).
                in(CollUtil.isNotEmpty(sceneList), OperationQuotientDO::getScene, sceneList).
                in(CollUtil.isNotEmpty(quotientNameList), OperationQuotientDO::getQuotientName, quotientNameList)
                .orderByDesc(OperationQuotientDO::getCountDay)
                .orderByDesc(CollUtil.isEmpty(miniType), OperationQuotientDO::getPlatform)
                .orderByDesc(CollUtil.isEmpty(quotientNameList), OperationQuotientDO::getMiniType)
                .orderByDesc(CollUtil.isEmpty(sceneList), OperationQuotientDO::getQuotientName)
                .orderByDesc(CollUtil.isNotEmpty(sceneList), OperationQuotientDO::getScene)
                .orderByDesc(OperationQuotientDO::getAuditPassCnt);
        ;

        Page<OperationQuotientDO> page = new Page<>(pageNo, pageSize);
        Page<OperationQuotientDO> goodsPage = page(page, queryWrapper);
        return MultiResult.of(goodsPage.getRecords(), goodsPage.getTotal());
    }

    @Override
    public List<OperationQuotientDO> getList(String fromTime,
                                             String toTime,
                                             List<Integer> miniType,
                                             List<String> platformList,
                                             List<String> sceneList,
                                             List<String> quotientNameList,
                                             Integer countType) {

        QueryWrapper<OperationQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(OperationQuotientDO.class, info -> !info.getColumn().equals("tag")).
                between(OperationQuotientDO::getCountDay, fromTime, toTime).
                eq(OperationQuotientDO::getCountType, countType).

                in(CollUtil.isNotEmpty(platformList), OperationQuotientDO::getPlatform, platformList).
                in(CollUtil.isNotEmpty(miniType), OperationQuotientDO::getMiniType, miniType).
                in(CollUtil.isNotEmpty(sceneList), OperationQuotientDO::getScene, sceneList).
                in(CollUtil.isNotEmpty(quotientNameList), OperationQuotientDO::getQuotientName, quotientNameList);


        return list(queryWrapper);
    }

    @Override
    public Map<String, Object> getMap(String fromTime,
                                      String toTime,
                                      Integer countType) {

        QueryWrapper<OperationQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .select(OperationQuotientDO.class, info -> !info.getColumn().equals("tag")).
                between(OperationQuotientDO::getCountDay, fromTime, toTime).
                eq(OperationQuotientDO::getCountType, countType);
        return getMap(queryWrapper);
    }


    @Override
    public List<OperationQuotientDO> getTagList(String time,
                                                Integer countType,
                                                List<Integer> miniTypeList,
                                                List<String> platformList,
                                                List<String> quotientList,
                                                List<String> sceneList,
                                                List<String> riskStrategyList,
                                                List<String> stage) {
        QueryWrapper<OperationQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("tag,risk_strategy").
                lambda().
                eq(OperationQuotientDO::getCountDay, time).
                eq(OperationQuotientDO::getCountType, countType).
                in(CollUtil.isNotEmpty(miniTypeList), OperationQuotientDO::getMiniType, miniTypeList).
                in(CollUtil.isNotEmpty(platformList), OperationQuotientDO::getPlatform, platformList).
                in(CollUtil.isNotEmpty(sceneList), OperationQuotientDO::getScene, sceneList).
                in(CollUtil.isNotEmpty(quotientList), OperationQuotientDO::getQuotientName, quotientList);
        return list(queryWrapper);
    }

}
