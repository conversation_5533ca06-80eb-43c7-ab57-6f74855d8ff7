package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.AccessPageAccessDO;
import qnvip.rent.common.base.BaseService;
import qnvip.rent.common.base.MultiResult;

import java.util.List;

/**
* Created by z<PERSON><PERSON>ong on 2021-10-09
*/
public interface AccessPageAccessService extends BaseService<AccessPageAccessDO>{

    /**
     * getListByHour by condition
     *
     * @param miniType
     * @param fromTime
     * @param toTime
     * @return
     */
    public List<AccessPageAccessDO> getList(Integer miniType,
                                            String fromTime,
                                            String toTime);

    public MultiResult<AccessPageAccessDO> page(Integer miniType,
                                                String startTime,
                                                String endTime,
                                                int current,
                                                int pageSize);

}
