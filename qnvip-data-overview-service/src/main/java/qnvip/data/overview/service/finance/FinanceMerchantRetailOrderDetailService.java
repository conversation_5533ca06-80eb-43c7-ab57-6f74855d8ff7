package qnvip.data.overview.service.finance;

import qnvip.data.overview.domain.finance.FinanceMerchantRetailOrderDetailDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * Created by zhengtong on 2023-06-25
 */
public interface FinanceMerchantRetailOrderDetailService extends BaseService<FinanceMerchantRetailOrderDetailDO> {
    public void removeAll();

    /**
     * getList by condition
     *
     * @return
     */
    public List<FinanceMerchantRetailOrderDetailDO> getList(String fromTime, String toTime, Integer countType,
                                                            List<Integer> miniTypes);
}
