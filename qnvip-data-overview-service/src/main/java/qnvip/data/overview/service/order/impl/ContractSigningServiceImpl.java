package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.ContractSigningDO;
import qnvip.data.overview.domain.order.SignOrderDO;
import qnvip.data.overview.dto.ContractSigningDTO;
import qnvip.data.overview.enums.ContractTypeEnum;
import qnvip.data.overview.mapper.order.ContractSigningMapper;
import qnvip.data.overview.service.access.SignOrderService;
import qnvip.data.overview.service.order.ContractSigningService;
import qnvip.data.overview.util.DateUtils;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContractSigningServiceImpl extends BaseServiceImpl<ContractSigningMapper, ContractSigningDO> implements ContractSigningService {

    private final SignOrderService signOrderService;


    @Override
    public boolean saveOrUpdate(ContractSigningDO entity) {
        Integer count = this.lambdaQuery().eq(ContractSigningDO::getOrderId, entity.getOrderId())
                .eq(ContractSigningDO::getCountDay, entity.getCountDay())
                .eq(ContractSigningDO::getType, entity.getType()).count();
        if (count == 0) {
            save(entity);
        } else {
            UpdateWrapper<ContractSigningDO> updateWrapper = new UpdateWrapper<>();
            updateWrapper.lambda().eq(ContractSigningDO::getOrderId, entity.getOrderId())
                    .eq(ContractSigningDO::getType, entity.getType());
            update(entity, updateWrapper);
        }
        return true;
    }

    @Override
    public ContractSigningDTO listByTime(LocalDateTime startTime, LocalDateTime endTime) {
        List<ContractSigningDO> list = this.lambdaQuery().ge(ContractSigningDO::getCountDay, startTime)
                .le(ContractSigningDO::getCountDay, endTime).orderByDesc(ContractSigningDO::getId).list();
        //已生成合同
        Map<LocalDateTime, List<ContractSigningDO>> contractSigningMap = list.stream()
                .collect(Collectors.groupingBy(ContractSigningDO::getCountDay));

        List<SignOrderDO> signOrderList = signOrderService.listByTime(startTime, endTime);
        //签收订单
        Map<LocalDateTime, List<SignOrderDO>> signOrderMap = signOrderList.stream()
                .collect(Collectors.groupingBy(SignOrderDO::getCountDay));

        ContractSigningDTO contractSigningDto = new ContractSigningDTO();
        List<ContractSigningDTO.BuildFailedInfo> buildFailedInfos = new ArrayList<>();
        List<ContractSigningDTO.ContractSigningChartDTO> chartList = DateUtils.getDateTimeList(startTime, endTime)
                .stream().map(countDay -> {
                    ContractSigningDTO.ContractSigningChartDTO contractSigningChart =
                            new ContractSigningDTO.ContractSigningChartDTO();
                    contractSigningChart.setCountDay(countDay);
                    List<ContractSigningDO> contractSigningList = contractSigningMap.get(countDay);
                    if (CollUtil.isNotEmpty(contractSigningList)) {
                        Long leaseContractCount = getContractCount(contractSigningList, ContractTypeEnum.ORDER_SIGN_FIRST);
                        contractSigningChart.setLeaseContractCount(leaseContractCount);
                        Long shortLeaseContractCount = getContractCount(contractSigningList, ContractTypeEnum.ORDER_SIGN_EIGHT);
                        contractSigningChart.setShortLeaseContractCount(shortLeaseContractCount);
                    }
                    List<SignOrderDO> signOrderDoList = signOrderMap.get(countDay);
                    if (CollUtil.isNotEmpty(signOrderDoList)) {
                        contractSigningChart.setSignCount((long) signOrderDoList.size());
                    }
                    buildFailedInfos.add(getBuildFailedInfo(contractSigningList, signOrderDoList, countDay));
                    return contractSigningChart;
                }).collect(Collectors.toList());
        contractSigningDto.setContractSigningChart(chartList);
        contractSigningDto.setBuildFailedInfo(buildFailedInfos);
        return contractSigningDto;
    }

    private ContractSigningDTO.BuildFailedInfo getBuildFailedInfo(List<ContractSigningDO> contractSigningList,
                                                                  List<SignOrderDO> signOrderDoList,
                                                                  LocalDateTime countDay) {
        ContractSigningDTO.BuildFailedInfo buildFailedInfo = new ContractSigningDTO.BuildFailedInfo();
        buildFailedInfo.setCountDay(countDay);
        if (CollUtil.isNotEmpty(contractSigningList) && CollUtil.isNotEmpty(signOrderDoList)) {
            List<Long> orderIds = contractSigningList.stream().map(ContractSigningDO::getOrderId)
                    .collect(Collectors.toList());
            //未生成的合同数
            String orderNo = signOrderDoList.stream().filter(o -> !orderIds.contains(o.getOrderId()))
                    .map(SignOrderDO::getOrderNo).collect(Collectors.joining(","));
            buildFailedInfo.setOrderNo(orderNo);
        }
        return buildFailedInfo;
    }

    private Long getContractCount(List<ContractSigningDO> contractSigningList, ContractTypeEnum contractTypeEnum) {

        return contractSigningList.stream()
                .filter(o -> Objects.equals(o.getType(), contractTypeEnum.getCode())).count();
    }
}
