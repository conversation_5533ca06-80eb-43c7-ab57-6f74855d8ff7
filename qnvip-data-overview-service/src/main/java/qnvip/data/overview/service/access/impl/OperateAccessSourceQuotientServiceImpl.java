package qnvip.data.overview.service.access.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import qnvip.data.overview.domain.access.OperateAccessSourceQuotientDO;
import qnvip.data.overview.domain.access.OperateAccessSourceQuotientDO;
import qnvip.data.overview.mapper.access.OperateAccessSourceQuotientMapper;
import qnvip.data.overview.service.access.OperateAccessSourceQuotientService;
import qnvip.rent.common.base.BaseServiceImpl;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2022-01-10
 */
@Slf4j
@Service
public class OperateAccessSourceQuotientServiceImpl extends BaseServiceImpl<OperateAccessSourceQuotientMapper, OperateAccessSourceQuotientDO> implements OperateAccessSourceQuotientService {

    @Autowired
    private OperateAccessSourceQuotientMapper mapper;

    @Override
    public void removeDataByTime(LocalDateTime countDay) {
        mapper.deleteByCountDay(countDay);
    }

    @Override
    public List<OperateAccessSourceQuotientDO> getList(String fromTime, String toTime) {
        QueryWrapper<OperateAccessSourceQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(OperateAccessSourceQuotientDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }


    @Override
    public MultiResult<OperateAccessSourceQuotientDO> getPageList(String fromTime,
                                                          String toTime,
                                                          Integer pageNo,
                                                          Integer pageSize) {
        QueryWrapper<OperateAccessSourceQuotientDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                lambda()
                .between(OperateAccessSourceQuotientDO::getCountDay, fromTime, toTime)
                .orderByDesc(OperateAccessSourceQuotientDO::getClickCount);
        Page<OperateAccessSourceQuotientDO> page = new Page<>(pageNo, pageSize);
        Page<OperateAccessSourceQuotientDO> accessSourcePage = page(page, queryWrapper);
        return MultiResult.of(accessSourcePage.getRecords(), accessSourcePage.getTotal());
    }
}