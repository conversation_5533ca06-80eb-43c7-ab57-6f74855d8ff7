package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateMarginDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by zhengtong on 2021-11-22
 */
public interface OperateMarginService extends BaseService<OperateMarginDO> {
    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateMarginDO> getList(String fromTime, String toTime);

    public void removeDataByTime(LocalDateTime countDay);

}
