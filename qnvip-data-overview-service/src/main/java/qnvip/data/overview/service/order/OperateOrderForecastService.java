package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateOrderForecastDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
/**
* Created by <PERSON><PERSON><PERSON><PERSON> on 2022-03-08
*/
public interface OperateOrderForecastService extends BaseService<OperateOrderForecastDO>{
    public void removeDataByTime(LocalDateTime countDay);
    /**
    * getListByHour by condition
    *
    * @return
     * @param fromTime
     * @param toTime
    */
    public List<OperateOrderForecastDO> getList(LocalDate fromTime, LocalDate toTime);


    /**
     * getListByHour by condition
     *
     * @return
     * @param fromTime
     */
    public List<OperateOrderForecastDO> getList(LocalDateTime fromTime, Integer bizType, Integer countType);
}
