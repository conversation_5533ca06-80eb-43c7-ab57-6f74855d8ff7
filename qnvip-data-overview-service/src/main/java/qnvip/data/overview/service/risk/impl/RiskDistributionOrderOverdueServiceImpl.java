package qnvip.data.overview.service.risk.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RiskDistributionOrderOverdueDO;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.dto.RiskOverdueDTO;
import qnvip.data.overview.mapper.risk.RiskDistributionOrderOverdueMapper;
import qnvip.data.overview.service.risk.RiskDistributionOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Created by zhengtong on 2022-11-14
 */
@Slf4j
@Service
public class RiskDistributionOrderOverdueServiceImpl extends BaseServiceImpl<RiskDistributionOrderOverdueMapper, RiskDistributionOrderOverdueDO> implements RiskDistributionOrderOverdueService {

    @Autowired
    private RiskDistributionOrderOverdueMapper mapper;

    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }

    @Override
    public List<RiskOverdueDTO> getList(String fromTime, String toTime, Integer overdueDay) {
        QueryWrapper<RiskDistributionOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select(" date_format(count_day, '%Y-%m')                     count_day," +
                        "       GROUP_CONCAT(order_id) as order_ids," +
                        "       risk_level," +
                        "       risk_strategy," +
                        "         deposit_free_type," +
                        "         traffic_type," +
                        "         financial_solutions," +
                        "         application_name," +
                        "         equipment_state," +
                        "         supervised_machine," +
                        "         machine_type," +
                        "         audit_type," +
                        "         audit_type_name," +
                        "       mini_type," +
                        "       scene," +
                        "       quotient_name," +
                        "       max_term," +
                        "       finance_type," +
                        "       bond_rate, " +
                        "       platform, " +
                        "       shop_name, " +
                        "       zmf_level, " +
                        "       drainage_type,refuse_type,is_rent,is_mortgage, " +
                        "       sum(buyout_amt)                                     buyout_amt," +
                        "       sum(p_before_discount)                              before_discount," +
                        "       sum(total_discount)                                 discount_total," +
                        "       sum(surplus_bond_amt)                               surplus_bond_amt_total," +
                        "       sum(bond_rest_fund_amount)                          bond_rest_fund_amount_total," +
                        "       sum(diff_pricing_discount_amt)                      diff_pricing_discount_amt_total," +
                        "       sum(coupon_discount_amt)                            coupon_discount_amt_total," +
                        "       sum(IF(discount_pricing_mode=1,rent_total-diff_pricing_discount_amt,rent_total))   rent_total_inner," +
                        "       sum(IF(discount_pricing_mode=1,IF(child_no is null or child_no='',rent_total+buyout_amt,rent_total+renew_total_rent+buy_out_capital)-diff_pricing_discount_amt,IF(child_no is null or child_no='',rent_total+buyout_amt,rent_total+renew_total_rent+buy_out_capital)))    rent_total," +
                        "       sum(renew_total_rent) renew_total_rent,"+
                        "       count(order_id)                                     total_order_cnt," +
                        "       sum(if(is_overdue1 = 1, bond_amt, 0))               bond_amt1," +
                        "       sum(if(is_overdue1 = 1, buyout_amt, 0))             buyout_amt1," +
                        "       sum(if(is_overdue1 = 1, p_before_discount, 0))      p_before_discount1," +
                        "       sum(if(is_overdue1 = 1, p_discount_return_amt, 0))  p_discount_return_amt1," +
                        "       sum(if(is_overdue1 = 1, before_discount, 0))        before_discount1," +
                        "       sum(if(is_overdue1 = 1, discount_return_amt, 0))    discount_return_amt1," +
                        "       sum(if(is_overdue1 = 1, surplus_bond_amt, 0)) surplus_bond_amt1,"+
                        "       sum(if(is_overdue1 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount1, "+
                        "       sum(if(is_overdue1 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt1,"+
                        "       sum(if(is_overdue1 = 1, coupon_discount_amt, 0)) coupon_discount_amt1,"+
                        "       sum(if(is_overdue2 = 1, bond_amt, 0))               bond_amt2," +
                        "       sum(if(is_overdue2 = 1, buyout_amt, 0))             buyout_amt2," +
                        "       sum(if(is_overdue2 = 1, p_before_discount, 0))      p_before_discount2," +
                        "       sum(if(is_overdue2 = 1, p_discount_return_amt, 0))  p_discount_return_amt2," +
                        "       sum(if(is_overdue2 = 1, before_discount, 0))        before_discount2," +
                        "       sum(if(is_overdue2 = 1, discount_return_amt, 0))    discount_return_amt2," +
                        "       sum(if(is_overdue2 = 1, surplus_bond_amt, 0)) surplus_bond_amt2,"+
                        "       sum(if(is_overdue2 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount2,"+
                        "       sum(if(is_overdue2 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt2,"+
                        "       sum(if(is_overdue2 = 1, coupon_discount_amt, 0)) coupon_discount_amt2,"+
                        "       sum(if(is_overdue3 = 1, bond_amt, 0))               bond_amt3," +
                        "       sum(if(is_overdue3 = 1, buyout_amt, 0))             buyout_amt3," +
                        "       sum(if(is_overdue3 = 1, p_before_discount, 0))      p_before_discount3," +
                        "       sum(if(is_overdue3 = 1, p_discount_return_amt, 0))  p_discount_return_amt3," +
                        "       sum(if(is_overdue3 = 1, before_discount, 0))        before_discount3," +
                        "       sum(if(is_overdue3 = 1, discount_return_amt, 0))    discount_return_amt3," +
                        "       sum(if(is_overdue3 = 1, surplus_bond_amt, 0)) surplus_bond_amt3,"+
                        "       sum(if(is_overdue3 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount3,"+
                        "       sum(if(is_overdue3 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt3,"+
                        "       sum(if(is_overdue3 = 1, coupon_discount_amt, 0)) coupon_discount_amt3,"+
                        "       sum(if(is_overdue4 = 1, bond_amt, 0))               bond_amt4," +
                        "       sum(if(is_overdue4 = 1, buyout_amt, 0))             buyout_amt4," +
                        "       sum(if(is_overdue4 = 1, p_before_discount, 0))      p_before_discount4," +
                        "       sum(if(is_overdue4 = 1, p_discount_return_amt, 0))  p_discount_return_amt4," +
                        "       sum(if(is_overdue4 = 1, before_discount, 0))        before_discount4," +
                        "       sum(if(is_overdue4 = 1, discount_return_amt, 0))    discount_return_amt4," +
                        "       sum(if(is_overdue4 = 1, surplus_bond_amt, 0)) surplus_bond_amt4,"+
                        "       sum(if(is_overdue4 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount4,"+
                        "       sum(if(is_overdue4 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt4,"+
                        "       sum(if(is_overdue4 = 1, coupon_discount_amt, 0)) coupon_discount_amt4,"+
                        "       sum(if(is_overdue5 = 1, bond_amt, 0))               bond_amt5," +
                        "       sum(if(is_overdue5 = 1, buyout_amt, 0))             buyout_amt5," +
                        "       sum(if(is_overdue5 = 1, p_before_discount, 0))      p_before_discount5," +
                        "       sum(if(is_overdue5 = 1, p_discount_return_amt, 0))  p_discount_return_amt5," +
                        "       sum(if(is_overdue5 = 1, before_discount, 0))        before_discount5," +
                        "       sum(if(is_overdue5 = 1, discount_return_amt, 0))    discount_return_amt5," +
                        "       sum(if(is_overdue5 = 1, surplus_bond_amt, 0)) surplus_bond_amt5,"+
                        "       sum(if(is_overdue5 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount5,"+
                        "       sum(if(is_overdue5 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt5,"+
                        "       sum(if(is_overdue5 = 1, coupon_discount_amt, 0)) coupon_discount_amt5,"+
                        "       sum(if(is_overdue6 = 1, bond_amt, 0))               bond_amt6," +
                        "       sum(if(is_overdue6 = 1, buyout_amt, 0))             buyout_amt6," +
                        "       sum(if(is_overdue6 = 1, p_before_discount, 0))      p_before_discount6," +
                        "       sum(if(is_overdue6 = 1, p_discount_return_amt, 0))  p_discount_return_amt6," +
                        "       sum(if(is_overdue6 = 1, before_discount, 0))        before_discount6," +
                        "       sum(if(is_overdue6 = 1, discount_return_amt, 0))    discount_return_amt6," +
                        "       sum(if(is_overdue6 = 1, surplus_bond_amt, 0)) surplus_bond_amt6,"+
                        "       sum(if(is_overdue6 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount6,"+
                        "       sum(if(is_overdue6 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt6,"+
                        "       sum(if(is_overdue6 = 1, coupon_discount_amt, 0)) coupon_discount_amt6,"+
                        "       sum(if(is_overdue7 = 1, bond_amt, 0))               bond_amt7," +
                        "       sum(if(is_overdue7 = 1, buyout_amt, 0))             buyout_amt7," +
                        "       sum(if(is_overdue7 = 1, p_before_discount, 0))      p_before_discount7," +
                        "       sum(if(is_overdue7 = 1, p_discount_return_amt, 0))  p_discount_return_amt7," +
                        "       sum(if(is_overdue7 = 1, before_discount, 0))        before_discount7," +
                        "       sum(if(is_overdue7 = 1, discount_return_amt, 0))    discount_return_amt7," +
                        "       sum(if(is_overdue7 = 1, surplus_bond_amt, 0)) surplus_bond_amt7,"+
                        "       sum(if(is_overdue7 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount7,"+
                        "       sum(if(is_overdue7 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt7,"+
                        "       sum(if(is_overdue7 = 1, coupon_discount_amt, 0)) coupon_discount_amt7,"+
                        "       sum(if(is_overdue8 = 1, bond_amt, 0))               bond_amt8," +
                        "       sum(if(is_overdue8 = 1, buyout_amt, 0))             buyout_amt8," +
                        "       sum(if(is_overdue8 = 1, p_before_discount, 0))      p_before_discount8," +
                        "       sum(if(is_overdue8 = 1, p_discount_return_amt, 0))  p_discount_return_amt8," +
                        "       sum(if(is_overdue8 = 1, before_discount, 0))        before_discount8," +
                        "       sum(if(is_overdue8 = 1, discount_return_amt, 0))    discount_return_amt8," +
                        "       sum(if(is_overdue8 = 1, surplus_bond_amt, 0)) surplus_bond_amt8,"+
                        "       sum(if(is_overdue8 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount8,"+
                        "       sum(if(is_overdue8 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt8,"+
                        "       sum(if(is_overdue8 = 1, coupon_discount_amt, 0)) coupon_discount_amt8,"+
                        "       sum(if(is_overdue9 = 1, bond_amt, 0))               bond_amt9," +
                        "       sum(if(is_overdue9 = 1, buyout_amt, 0))             buyout_amt9," +
                        "       sum(if(is_overdue9 = 1, p_before_discount, 0))      p_before_discount9," +
                        "       sum(if(is_overdue9 = 1, p_discount_return_amt, 0))  p_discount_return_amt9," +
                        "       sum(if(is_overdue9 = 1, before_discount, 0))        before_discount9," +
                        "       sum(if(is_overdue9 = 1, discount_return_amt, 0))    discount_return_amt9," +
                        "       sum(if(is_overdue9 = 1, surplus_bond_amt, 0)) surplus_bond_amt9,"+
                        "       sum(if(is_overdue9 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount9,"+
                        "       sum(if(is_overdue9 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt9,"+
                        "       sum(if(is_overdue9 = 1, coupon_discount_amt, 0)) coupon_discount_amt9,"+
                        "       sum(if(is_overdue10 = 1, bond_amt, 0))              bond_amt10," +
                        "       sum(if(is_overdue10 = 1, buyout_amt, 0))            buyout_amt10," +
                        "       sum(if(is_overdue10 = 1, p_before_discount, 0))     p_before_discount10," +
                        "       sum(if(is_overdue10 = 1, p_discount_return_amt, 0)) p_discount_return_amt10," +
                        "       sum(if(is_overdue10 = 1, before_discount, 0))       before_discount10," +
                        "       sum(if(is_overdue10 = 1, discount_return_amt, 0))   discount_return_amt10," +
                        "       sum(if(is_overdue10 = 1, surplus_bond_amt, 0)) surplus_bond_amt10,"+
                        "       sum(if(is_overdue10 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount10,"+
                        "       sum(if(is_overdue10 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt10,"+
                        "       sum(if(is_overdue10 = 1, coupon_discount_amt, 0)) coupon_discount_amt10,"+
                        "       sum(if(is_overdue11 = 1, bond_amt, 0))              bond_amt11," +
                        "       sum(if(is_overdue11 = 1, buyout_amt, 0))            buyout_amt11," +
                        "       sum(if(is_overdue11 = 1, p_before_discount, 0))     p_before_discount11," +
                        "       sum(if(is_overdue11 = 1, p_discount_return_amt, 0)) p_discount_return_amt11," +
                        "       sum(if(is_overdue11 = 1, before_discount, 0))       before_discount11," +
                        "       sum(if(is_overdue11 = 1, discount_return_amt, 0))   discount_return_amt11," +
                        "       sum(if(is_overdue11 = 1, surplus_bond_amt, 0)) surplus_bond_amt11,"+
                        "       sum(if(is_overdue11 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount11,"+
                        "       sum(if(is_overdue11 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt11,"+
                        "       sum(if(is_overdue11 = 1, coupon_discount_amt, 0)) coupon_discount_amt11,"+
                        "       sum(if(is_overdue12 = 1, bond_amt, 0))              bond_amt12," +
                        "       sum(if(is_overdue12 = 1, buyout_amt, 0))            buyout_amt12," +
                        "       sum(if(is_overdue12 = 1, p_before_discount, 0))     p_before_discount12," +
                        "       sum(if(is_overdue12 = 1, p_discount_return_amt, 0)) p_discount_return_amt12," +
                        "       sum(if(is_overdue12 = 1, before_discount, 0))       before_discount12," +
                        "       sum(if(is_overdue12 = 1, discount_return_amt, 0))   discount_return_amt12," +
                        "       sum(if(is_overdue12 = 1, surplus_bond_amt, 0)) surplus_bond_amt12,"+
                        "       sum(if(is_overdue12 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount12,"+
                        "       sum(if(is_overdue12 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt12,"+
                        "       sum(if(is_overdue12 = 1, coupon_discount_amt, 0)) coupon_discount_amt12,"+
                        "       sum(if(is_overdue13 = 1, bond_amt, 0))              bond_amt13," +
                        "       sum(if(is_overdue13 = 1, buyout_amt, 0))            buyout_amt13," +
                        "       sum(if(is_overdue13 = 1, p_before_discount, 0))     p_before_discount13," +
                        "       sum(if(is_overdue13 = 1, p_discount_return_amt, 0)) p_discount_return_amt13," +
                        "       sum(if(is_overdue13 = 1, before_discount, 0))       before_discount13," +
                        "       sum(if(is_overdue13 = 1, discount_return_amt, 0))   discount_return_amt13," +
                        "       sum(if(is_overdue13 = 1, surplus_bond_amt, 0)) surplus_bond_amt13,"+
                        "       sum(if(is_overdue13 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount13,"+
                        "       sum(if(is_overdue13 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt13,"+
                        "       sum(if(is_overdue13 = 1, coupon_discount_amt, 0)) coupon_discount_amt13,"+
                        "       sum(if(is_overdue14 = 1, bond_amt, 0))              bond_amt14," +
                        "       sum(if(is_overdue14 = 1, buyout_amt, 0))            buyout_amt14," +
                        "       sum(if(is_overdue14 = 1, p_before_discount, 0))     p_before_discount14," +
                        "       sum(if(is_overdue14 = 1, p_discount_return_amt, 0)) p_discount_return_amt14," +
                        "       sum(if(is_overdue14 = 1, before_discount, 0))       before_discount14," +
                        "       sum(if(is_overdue14 = 1, discount_return_amt, 0))   discount_return_amt14," +
                        "       sum(if(is_overdue14 = 1, surplus_bond_amt, 0)) surplus_bond_amt14,"+
                        "       sum(if(is_overdue14 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount14,"+
                        "       sum(if(is_overdue14 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt14,"+
                        "       sum(if(is_overdue14 = 1, coupon_discount_amt, 0)) coupon_discount_amt14,"+
                        "       sum(if(is_overdue15 = 1, bond_amt, 0))              bond_amt15," +
                        "       sum(if(is_overdue15 = 1, buyout_amt, 0))            buyout_amt15," +
                        "       sum(if(is_overdue15 = 1, p_before_discount, 0))     p_before_discount15," +
                        "       sum(if(is_overdue15 = 1, p_discount_return_amt, 0)) p_discount_return_amt15," +
                        "       sum(if(is_overdue15 = 1, before_discount, 0))       before_discount15," +
                        "       sum(if(is_overdue15 = 1, discount_return_amt, 0))   discount_return_amt15," +
                        "   sum(if(is_overdue15 = 1, surplus_bond_amt, 0)) surplus_bond_amt15,"+
                        "sum(if(is_overdue15 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount15,"+
                        "  sum(if(is_overdue15 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt15,"+
                        " sum(if(is_overdue15 = 1, coupon_discount_amt, 0)) coupon_discount_amt15,"+
                        "       sum(if(is_overdue16 = 1, bond_amt, 0))              bond_amt16," +
                        "       sum(if(is_overdue16 = 1, buyout_amt, 0))            buyout_amt16," +
                        "       sum(if(is_overdue16 = 1, p_before_discount, 0))     p_before_discount16," +
                        "       sum(if(is_overdue16 = 1, p_discount_return_amt, 0)) p_discount_return_amt16," +
                        "       sum(if(is_overdue16 = 1, before_discount, 0))       before_discount16," +
                        "       sum(if(is_overdue16 = 1, discount_return_amt, 0))   discount_return_amt16," +
                        "sum(if(is_overdue16 = 1, surplus_bond_amt, 0)) surplus_bond_amt16,"+
                        "sum(if(is_overdue16 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount16,"+
                        "sum(if(is_overdue16 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt16,"+
                        "sum(if(is_overdue16 = 1, coupon_discount_amt, 0)) coupon_discount_amt16,"+
                        "       sum(if(is_overdue17 = 1, bond_amt, 0))              bond_amt17," +
                        "       sum(if(is_overdue17 = 1, buyout_amt, 0))            buyout_amt17," +
                        "       sum(if(is_overdue17 = 1, p_before_discount, 0))     p_before_discount17," +
                        "       sum(if(is_overdue17 = 1, p_discount_return_amt, 0)) p_discount_return_amt17," +
                        "       sum(if(is_overdue17 = 1, before_discount, 0))       before_discount17," +
                        "       sum(if(is_overdue17 = 1, discount_return_amt, 0))   discount_return_amt17," +
                        "sum(if(is_overdue17 = 1, surplus_bond_amt, 0)) surplus_bond_amt17,"+
                        "sum(if(is_overdue17 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount17,"+
                        "sum(if(is_overdue17 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt17,"+
                        "sum(if(is_overdue17 = 1, coupon_discount_amt, 0)) coupon_discount_amt17,"+
                        "       sum(if(is_overdue18 = 1, bond_amt, 0))              bond_amt18," +
                        "       sum(if(is_overdue18 = 1, buyout_amt, 0))            buyout_amt18," +
                        "       sum(if(is_overdue18 = 1, p_before_discount, 0))     p_before_discount18," +
                        "       sum(if(is_overdue18 = 1, p_discount_return_amt, 0)) p_discount_return_amt18," +
                        "       sum(if(is_overdue18 = 1, before_discount, 0))       before_discount18," +
                        "       sum(if(is_overdue18 = 1, discount_return_amt, 0))   discount_return_amt18," +
                        "sum(if(is_overdue18 = 1, surplus_bond_amt, 0)) surplus_bond_amt18,"+
                        "sum(if(is_overdue18 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount18,"+
                        "sum(if(is_overdue18 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt18,"+
                        "sum(if(is_overdue18 = 1, coupon_discount_amt, 0)) coupon_discount_amt18,"+
                        "       sum(if(is_overdue19 = 1, bond_amt, 0))              bond_amt19," +
                        "       sum(if(is_overdue19 = 1, buyout_amt, 0))            buyout_amt19," +
                        "       sum(if(is_overdue19 = 1, p_before_discount, 0))     p_before_discount19," +
                        "       sum(if(is_overdue19 = 1, p_discount_return_amt, 0)) p_discount_return_amt19," +
                        "       sum(if(is_overdue19 = 1, before_discount, 0))       before_discount19," +
                        "       sum(if(is_overdue19 = 1, discount_return_amt, 0))   discount_return_amt19," +
                        "sum(if(is_overdue19 = 1, surplus_bond_amt, 0)) surplus_bond_amt19,"+
                        "sum(if(is_overdue19 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount19,"+
                        "sum(if(is_overdue19 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt19,"+
                        "sum(if(is_overdue19 = 1, coupon_discount_amt, 0)) coupon_discount_amt19,"+
                        "       sum(if(is_overdue20 = 1, bond_amt, 0))              bond_amt20," +
                        "       sum(if(is_overdue20 = 1, buyout_amt, 0))            buyout_amt20," +
                        "       sum(if(is_overdue20 = 1, p_before_discount, 0))     p_before_discount20," +
                        "       sum(if(is_overdue20 = 1, p_discount_return_amt, 0)) p_discount_return_amt20," +
                        "       sum(if(is_overdue20 = 1, before_discount, 0))       before_discount20," +
                        "       sum(if(is_overdue20 = 1, discount_return_amt, 0))   discount_return_amt20," +
                        "sum(if(is_overdue20 = 1, surplus_bond_amt, 0)) surplus_bond_amt20,"+
                        "sum(if(is_overdue20 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount20,"+
                        "sum(if(is_overdue20 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt20,"+
                        "sum(if(is_overdue20 = 1, coupon_discount_amt, 0)) coupon_discount_amt20,"+
                        "       sum(if(is_overdue21 = 1, bond_amt, 0))              bond_amt21," +
                        "       sum(if(is_overdue21 = 1, buyout_amt, 0))            buyout_amt21," +
                        "       sum(if(is_overdue21 = 1, p_before_discount, 0))     p_before_discount21," +
                        "       sum(if(is_overdue21 = 1, p_discount_return_amt, 0)) p_discount_return_amt21," +
                        "       sum(if(is_overdue21 = 1, before_discount, 0))       before_discount21," +
                        "       sum(if(is_overdue21 = 1, discount_return_amt, 0))   discount_return_amt21," +
                        "sum(if(is_overdue21 = 1, surplus_bond_amt, 0)) surplus_bond_amt21,"+
                        "sum(if(is_overdue21 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount21,"+
                        "sum(if(is_overdue21 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt21,"+
                        "sum(if(is_overdue21 = 1, coupon_discount_amt, 0)) coupon_discount_amt21,"+
                        "       sum(if(is_overdue22 = 1, bond_amt, 0))              bond_amt22," +
                        "       sum(if(is_overdue22 = 1, buyout_amt, 0))            buyout_amt22," +
                        "       sum(if(is_overdue22 = 1, p_before_discount, 0))     p_before_discount22," +
                        "       sum(if(is_overdue22 = 1, p_discount_return_amt, 0)) p_discount_return_amt22," +
                        "       sum(if(is_overdue22 = 1, before_discount, 0))       before_discount22," +
                        "       sum(if(is_overdue22 = 1, discount_return_amt, 0))   discount_return_amt22," +
                        "sum(if(is_overdue22 = 1, surplus_bond_amt, 0)) surplus_bond_amt22,"+
                        "sum(if(is_overdue22 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount22,"+
                        "sum(if(is_overdue22 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt22,"+
                        "sum(if(is_overdue22 = 1, coupon_discount_amt, 0)) coupon_discount_amt22,"+
                        "       sum(if(is_overdue23 = 1, bond_amt, 0))              bond_amt23," +
                        "       sum(if(is_overdue23 = 1, buyout_amt, 0))            buyout_amt23," +
                        "       sum(if(is_overdue23 = 1, p_before_discount, 0))     p_before_discount23," +
                        "       sum(if(is_overdue23 = 1, p_discount_return_amt, 0)) p_discount_return_amt23," +
                        "       sum(if(is_overdue23 = 1, before_discount, 0))       before_discount23," +
                        "       sum(if(is_overdue23 = 1, discount_return_amt, 0))   discount_return_amt23," +
                        "sum(if(is_overdue23 = 1, surplus_bond_amt, 0)) surplus_bond_amt23,"+
                        "sum(if(is_overdue23 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount23,"+
                        "sum(if(is_overdue23 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt23,"+
                        "sum(if(is_overdue23 = 1, coupon_discount_amt, 0)) coupon_discount_amt23,"+
                        "       sum(if(is_overdue24 = 1, bond_amt, 0))              bond_amt24," +
                        "       sum(if(is_overdue24 = 1, buyout_amt, 0))            buyout_amt24," +
                        "       sum(if(is_overdue24 = 1, p_before_discount, 0))     p_before_discount24," +
                        "       sum(if(is_overdue24 = 1, p_discount_return_amt, 0)) p_discount_return_amt24," +
                        "       sum(if(is_overdue24 = 1, before_discount, 0))       before_discount24," +
                        "       sum(if(is_overdue24 = 1, discount_return_amt, 0))   discount_return_amt24," +
                        "sum(if(is_overdue24 = 1, surplus_bond_amt, 0)) surplus_bond_amt24,"+
                        "sum(if(is_overdue24 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount24,"+
                        "sum(if(is_overdue24 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt24,"+
                        "sum(if(is_overdue24 = 1, coupon_discount_amt, 0)) coupon_discount_amt24,"+
                        "       sum(if(is_overdue25 = 1, bond_amt, 0))              bond_amt25," +
                        "       sum(if(is_overdue25 = 1, buyout_amt, 0))            buyout_amt25," +
                        "       sum(if(is_overdue25 = 1, p_before_discount, 0))     p_before_discount25," +
                        "       sum(if(is_overdue25 = 1, p_discount_return_amt, 0)) p_discount_return_amt25," +
                        "       sum(if(is_overdue25 = 1, before_discount, 0))       before_discount25," +
                        "       sum(if(is_overdue25 = 1, discount_return_amt, 0))   discount_return_amt25," +
                        "sum(if(is_overdue25 = 1, surplus_bond_amt, 0)) surplus_bond_amt25,"+
                        "sum(if(is_overdue25 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount25,"+
                        "sum(if(is_overdue25 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt25,"+
                        "sum(if(is_overdue25 = 1, coupon_discount_amt, 0)) coupon_discount_amt25,"+
                        "       sum(if(is_overdue26 = 1, bond_amt, 0))              bond_amt26," +
                        "       sum(if(is_overdue26 = 1, buyout_amt, 0))            buyout_amt26," +
                        "       sum(if(is_overdue26 = 1, p_before_discount, 0))     p_before_discount26," +
                        "       sum(if(is_overdue26 = 1, p_discount_return_amt, 0)) p_discount_return_amt26," +
                        "       sum(if(is_overdue26 = 1, before_discount, 0))       before_discount26," +
                        "       sum(if(is_overdue26 = 1, discount_return_amt, 0))   discount_return_amt26," +
                        "sum(if(is_overdue26 = 1, surplus_bond_amt, 0)) surplus_bond_amt26,"+
                        "sum(if(is_overdue26 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount26,"+
                        "sum(if(is_overdue26 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt26,"+
                        "sum(if(is_overdue26 = 1, coupon_discount_amt, 0)) coupon_discount_amt26,"+
                        "       sum(if(is_overdue27 = 1, bond_amt, 0))              bond_amt27," +
                        "       sum(if(is_overdue27 = 1, buyout_amt, 0))            buyout_amt27," +
                        "       sum(if(is_overdue27 = 1, p_before_discount, 0))     p_before_discount27," +
                        "       sum(if(is_overdue27 = 1, p_discount_return_amt, 0)) p_discount_return_amt27," +
                        "       sum(if(is_overdue27 = 1, before_discount, 0))       before_discount27," +
                        "       sum(if(is_overdue27 = 1, discount_return_amt, 0))   discount_return_amt27," +
                        "sum(if(is_overdue27 = 1, surplus_bond_amt, 0)) surplus_bond_amt27,"+
                        "sum(if(is_overdue27 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount27,"+
                        "sum(if(is_overdue27 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt27,"+
                        "sum(if(is_overdue27 = 1, coupon_discount_amt, 0)) coupon_discount_amt27,"+
                        "       sum(if(is_overdue28 = 1, bond_amt, 0))              bond_amt28," +
                        "       sum(if(is_overdue28 = 1, buyout_amt, 0))            buyout_amt28," +
                        "       sum(if(is_overdue28 = 1, p_before_discount, 0))     p_before_discount28," +
                        "       sum(if(is_overdue28 = 1, p_discount_return_amt, 0)) p_discount_return_amt28," +
                        "       sum(if(is_overdue28 = 1, before_discount, 0))       before_discount28," +
                        "       sum(if(is_overdue28 = 1, discount_return_amt, 0))   discount_return_amt28," +
                        "sum(if(is_overdue28 = 1, surplus_bond_amt, 0)) surplus_bond_amt28,"+
                        "sum(if(is_overdue28 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount28,"+
                        "sum(if(is_overdue28 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt28,"+
                        "sum(if(is_overdue28 = 1, coupon_discount_amt, 0)) coupon_discount_amt28,"+
                        "       sum(if(is_overdue29 = 1, bond_amt, 0))              bond_amt29," +
                        "       sum(if(is_overdue29 = 1, buyout_amt, 0))            buyout_amt29," +
                        "       sum(if(is_overdue29 = 1, p_before_discount, 0))     p_before_discount29," +
                        "       sum(if(is_overdue29 = 1, p_discount_return_amt, 0)) p_discount_return_amt29," +
                        "       sum(if(is_overdue29 = 1, before_discount, 0))       before_discount29," +
                        "       sum(if(is_overdue29 = 1, discount_return_amt, 0))   discount_return_amt29," +
                        "sum(if(is_overdue29 = 1, surplus_bond_amt, 0)) surplus_bond_amt29,"+
                        "sum(if(is_overdue29 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount29,"+
                        "sum(if(is_overdue29 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt29,"+
                        "sum(if(is_overdue29 = 1, coupon_discount_amt, 0)) coupon_discount_amt29,"+
                        "       sum(if(is_overdue30 = 1, bond_amt, 0))              bond_amt30," +
                        "       sum(if(is_overdue30 = 1, buyout_amt, 0))            buyout_amt30," +
                        "       sum(if(is_overdue30 = 1, p_before_discount, 0))     p_before_discount30," +
                        "       sum(if(is_overdue30 = 1, p_discount_return_amt, 0)) p_discount_return_amt30," +
                        "       sum(if(is_overdue30 = 1, before_discount, 0))       before_discount30," +
                        "       sum(if(is_overdue30 = 1, discount_return_amt, 0))   discount_return_amt30," +
                        "sum(if(is_overdue30 = 1, surplus_bond_amt, 0)) surplus_bond_amt30,"+
                        "sum(if(is_overdue30 = 1, bond_rest_fund_amount, 0)) bond_rest_fund_amount30,"+
                        "sum(if(is_overdue30 = 1, diff_pricing_discount_amt, 0)) diff_pricing_discount_amt30,"+
                        "sum(if(is_overdue30 = 1, coupon_discount_amt, 0)) coupon_discount_amt30,"+
                        "       sum(if(is_overdue1 = 1, IF(discount_pricing_mode=1,term1-diff_pricing_discount_amt,term1)+discount_amt+buyout_amt, 0))                  not_pay_term1," +
                        "       sum(if(is_overdue1 = 1, overdue_fine1, 0))          overdue_fine1," +
                        "       sum(if(is_overdue1 = 1, renew_amt1, 0))             renew_amt1," +
                        "       sum(if(is_overdue1 = 1, 1, 0))                      count_term1," +
                        "       sum(if(is_overdue2 = 1, IF(discount_pricing_mode=1,term2-diff_pricing_discount_amt,term2)+discount_amt+buyout_amt, 0))                  not_pay_term2," +
                        "       sum(if(is_overdue2 = 1, overdue_fine2, 0))          overdue_fine2," +
                        "       sum(if(is_overdue2 = 1, renew_amt2, 0))             renew_amt2," +
                        "       sum(if(is_overdue2 = 1, 1, 0))                      count_term2," +
                        "       sum(if(is_overdue3 = 1, IF(discount_pricing_mode=1,term3-diff_pricing_discount_amt,term3)+discount_amt+buyout_amt, 0))                  not_pay_term3," +
                        "       sum(if(is_overdue3 = 1, overdue_fine3, 0))          overdue_fine3," +
                        "       sum(if(is_overdue3 = 1, renew_amt3, 0))             renew_amt3," +
                        "       sum(if(is_overdue3 = 1, 1, 0))                      count_term3," +
                        "       sum(if(is_overdue4 = 1, IF(discount_pricing_mode=1,term4-diff_pricing_discount_amt,term4)+discount_amt+buyout_amt, 0))                  not_pay_term4," +
                        "       sum(if(is_overdue4 = 1, overdue_fine4, 0))          overdue_fine4," +
                        "       sum(if(is_overdue4 = 1, renew_amt4, 0))             renew_amt4," +
                        "       sum(if(is_overdue4 = 1, 1, 0))                      count_term4," +
                        "       sum(if(is_overdue5 = 1, IF(discount_pricing_mode=1,term5-diff_pricing_discount_amt,term5)+discount_amt+buyout_amt, 0))                  not_pay_term5," +
                        "       sum(if(is_overdue5 = 1, overdue_fine5, 0))          overdue_fine5," +
                        "       sum(if(is_overdue5 = 1, renew_amt5, 0))             renew_amt5," +
                        "       sum(if(is_overdue5 = 1, 1, 0))                      count_term5," +
                        "       sum(if(is_overdue6 = 1, IF(discount_pricing_mode=1,term6-diff_pricing_discount_amt,term6)+discount_amt+buyout_amt, 0))                  not_pay_term6," +
                        "       sum(if(is_overdue6 = 1, overdue_fine6, 0))          overdue_fine6," +
                        "       sum(if(is_overdue6 = 1, renew_amt6, 0))             renew_amt6," +
                        "       sum(if(is_overdue6 = 1, 1, 0))                      count_term6," +
                        "       sum(if(is_overdue7 = 1, IF(discount_pricing_mode=1,term7-diff_pricing_discount_amt,term7)+discount_amt+buyout_amt, 0))                  not_pay_term7," +
                        "       sum(if(is_overdue7 = 1, overdue_fine7, 0))          overdue_fine7," +
                        "       sum(if(is_overdue7 = 1, renew_amt7, 0))             renew_amt7," +
                        "       sum(if(is_overdue7 = 1, 1, 0))                      count_term7," +
                        "       sum(if(is_overdue8 = 1, IF(discount_pricing_mode=1,term8-diff_pricing_discount_amt,term8)+discount_amt+buyout_amt, 0))                  not_pay_term8," +
                        "       sum(if(is_overdue8 = 1, overdue_fine8, 0))          overdue_fine8," +
                        "       sum(if(is_overdue8 = 1, renew_amt8, 0))             renew_amt8," +
                        "       sum(if(is_overdue8 = 1, 1, 0))                      count_term8," +
                        "       sum(if(is_overdue9 = 1, IF(discount_pricing_mode=1,term9-diff_pricing_discount_amt,term9)+discount_amt+buyout_amt, 0))                  not_pay_term9," +
                        "       sum(if(is_overdue9 = 1, overdue_fine9, 0))          overdue_fine9," +
                        "       sum(if(is_overdue9 = 1, renew_amt9, 0))             renew_amt9," +
                        "       sum(if(is_overdue9 = 1, 1, 0))                      count_term9," +
                        "       sum(if(is_overdue10 = 1, IF(discount_pricing_mode=1,term10-diff_pricing_discount_amt,term10)+discount_amt+buyout_amt, 0))                not_pay_term10," +
                        "       sum(if(is_overdue10 = 1, overdue_fine10, 0))        overdue_fine10," +
                        "       sum(if(is_overdue10 = 1, renew_amt10, 0))           renew_amt10," +
                        "       sum(if(is_overdue10 = 1, 1, 0))                     count_term10," +
                        "       sum(if(is_overdue11 = 1, IF(discount_pricing_mode=1,term11-diff_pricing_discount_amt,term11)+discount_amt+buyout_amt, 0))                not_pay_term11," +
                        "       sum(if(is_overdue11 = 1, overdue_fine11, 0))        overdue_fine11," +
                        "       sum(if(is_overdue11 = 1, renew_amt11, 0))           renew_amt11," +
                        "       sum(if(is_overdue11 = 1, 1, 0))                     count_term11," +
                        "       sum(if(is_overdue12 = 1, IF(discount_pricing_mode=1,term12-diff_pricing_discount_amt,term12)+discount_amt+buyout_amt, 0))                not_pay_term12," +
                        "       sum(if(is_overdue12 = 1, overdue_fine12, 0))        overdue_fine12," +
                        "       sum(if(is_overdue12 = 1, renew_amt12, 0))           renew_amt12," +
                        "       sum(if(is_overdue12 = 1, 1, 0))                     count_term12," +
                        "       sum(if(is_overdue13 = 1, IF(discount_pricing_mode=1,term13-diff_pricing_discount_amt,term13)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term13," +
                        "       sum(if(is_overdue13 = 1, overdue_fine13, 0))        overdue_fine13," +
                        "       sum(if(is_overdue13 = 1, renew_amt13, 0))           renew_amt13," +
                        "       sum(if(is_overdue13 = 1, 1, 0))                     count_term13," +
                        "       sum(if(is_overdue14 = 1, IF(discount_pricing_mode=1,term14-diff_pricing_discount_amt,term14)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term14," +
                        "       sum(if(is_overdue14 = 1, overdue_fine14, 0))        overdue_fine14," +
                        "       sum(if(is_overdue14 = 1, renew_amt14, 0))           renew_amt14," +
                        "       sum(if(is_overdue14 = 1, 1, 0))                     count_term14," +
                        "       sum(if(is_overdue15 = 1, IF(discount_pricing_mode=1,term15-diff_pricing_discount_amt,term15)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term15," +
                        "       sum(if(is_overdue15 = 1, overdue_fine15, 0))        overdue_fine15," +
                        "       sum(if(is_overdue15 = 1, renew_amt15, 0))           renew_amt15," +
                        "       sum(if(is_overdue15 = 1, 1, 0))                     count_term15," +
                        "       sum(if(is_overdue16 = 1, IF(discount_pricing_mode=1,term16-diff_pricing_discount_amt,term16)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term16," +
                        "       sum(if(is_overdue16 = 1, overdue_fine16, 0))        overdue_fine16," +
                        "       sum(if(is_overdue16 = 1, renew_amt16, 0))           renew_amt16," +
                        "       sum(if(is_overdue16 = 1, 1, 0))                     count_term16," +
                        "       sum(if(is_overdue17 = 1, IF(discount_pricing_mode=1,term17-diff_pricing_discount_amt,term17)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term17," +
                        "       sum(if(is_overdue17 = 1, overdue_fine17, 0))        overdue_fine17," +
                        "       sum(if(is_overdue17 = 1, renew_amt17, 0))           renew_amt17," +
                        "       sum(if(is_overdue17 = 1, 1, 0))                     count_term17," +
                        "       sum(if(is_overdue18 = 1, IF(discount_pricing_mode=18,term1-diff_pricing_discount_amt,term18)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term18," +
                        "       sum(if(is_overdue18 = 1, overdue_fine18, 0))        overdue_fine18," +
                        "       sum(if(is_overdue18 = 1, renew_amt18, 0))           renew_amt18," +
                        "       sum(if(is_overdue18 = 1, 1, 0))                     count_term18," +
                        "       sum(if(is_overdue19 = 1, IF(discount_pricing_mode=1,term19-diff_pricing_discount_amt,term19)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term19," +
                        "       sum(if(is_overdue19 = 1, overdue_fine19, 0))        overdue_fine19," +
                        "       sum(if(is_overdue19 = 1, renew_amt19, 0))           renew_amt19," +
                        "       sum(if(is_overdue19 = 1, 1, 0))                     count_term19," +
                        "       sum(if(is_overdue20 = 1, IF(discount_pricing_mode=1,term20-diff_pricing_discount_amt,term20)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term20," +
                        "       sum(if(is_overdue20 = 1, overdue_fine20, 0))        overdue_fine20," +
                        "       sum(if(is_overdue20 = 1, renew_amt20, 0))           renew_amt20," +
                        "       sum(if(is_overdue20 = 1, 1, 0))                     count_term20," +
                        "       sum(if(is_overdue21 = 1, IF(discount_pricing_mode=1,term21-diff_pricing_discount_amt,term21)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term21," +
                        "       sum(if(is_overdue21 = 1, overdue_fine21, 0))        overdue_fine21," +
                        "       sum(if(is_overdue21 = 1, renew_amt21, 0))           renew_amt21," +
                        "       sum(if(is_overdue21 = 1, 1, 0))                     count_term21," +
                        "       sum(if(is_overdue22 = 1, IF(discount_pricing_mode=1,term22-diff_pricing_discount_amt,term22)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term22," +
                        "       sum(if(is_overdue22 = 1, overdue_fine22, 0))        overdue_fine22," +
                        "       sum(if(is_overdue22 = 1, renew_amt22, 0))           renew_amt22," +
                        "       sum(if(is_overdue22 = 1, 1, 0))                     count_term22," +
                        "       sum(if(is_overdue23 = 1, IF(discount_pricing_mode=1,term23-diff_pricing_discount_amt,term23)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term23," +
                        "       sum(if(is_overdue23 = 1, overdue_fine23, 0))        overdue_fine23," +
                        "       sum(if(is_overdue23 = 1, renew_amt23, 0))           renew_amt23," +
                        "       sum(if(is_overdue23 = 1, 1, 0))                     count_term23," +
                        "       sum(if(is_overdue24 = 1, IF(discount_pricing_mode=1,term24-diff_pricing_discount_amt,term24)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term24," +
                        "       sum(if(is_overdue24 = 1, overdue_fine24, 0))        overdue_fine24," +
                        "       sum(if(is_overdue24 = 1, renew_amt24, 0))           renew_amt24," +
                        "       sum(if(is_overdue24 = 1, 1, 0))                     count_term24," +
                        "       sum(if(is_overdue25 = 1, IF(discount_pricing_mode=1,term25-diff_pricing_discount_amt,term25)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term25," +
                        "       sum(if(is_overdue25 = 1, overdue_fine25, 0))        overdue_fine25," +
                        "       sum(if(is_overdue25 = 1, renew_amt25, 0))           renew_amt25," +
                        "       sum(if(is_overdue25 = 1, 1, 0))                     count_term25," +
                        "       sum(if(is_overdue26 = 1, IF(discount_pricing_mode=1,term26-diff_pricing_discount_amt,term26)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term26," +
                        "       sum(if(is_overdue26 = 1, overdue_fine26, 0))        overdue_fine26," +
                        "       sum(if(is_overdue26 = 1, renew_amt26, 0))           renew_amt26," +
                        "       sum(if(is_overdue26 = 1, 1, 0))                     count_term26," +
                        "       sum(if(is_overdue27 = 1, IF(discount_pricing_mode=1,term27-diff_pricing_discount_amt,term27)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term27," +
                        "       sum(if(is_overdue27 = 1, overdue_fine27, 0))        overdue_fine27," +
                        "       sum(if(is_overdue27 = 1, renew_amt27, 0))           renew_amt27," +
                        "       sum(if(is_overdue27 = 1, 1, 0))                     count_term27," +
                        "       sum(if(is_overdue28 = 1, IF(discount_pricing_mode=1,term28-diff_pricing_discount_amt,term28)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term28," +
                        "       sum(if(is_overdue28 = 1, overdue_fine28, 0))        overdue_fine28," +
                        "       sum(if(is_overdue28 = 1, renew_amt28, 0))           renew_amt28," +
                        "       sum(if(is_overdue28 = 1, 1, 0))                     count_term28," +
                        "       sum(if(is_overdue29 = 1, IF(discount_pricing_mode=1,term29-diff_pricing_discount_amt,term29)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term29," +
                        "       sum(if(is_overdue29 = 1, overdue_fine29, 0))        overdue_fine29," +
                        "       sum(if(is_overdue29 = 1, renew_amt29, 0))           renew_amt29," +
                        "       sum(if(is_overdue29 = 1, 1, 0))                     count_term29," +
                        "       sum(if(is_overdue30 = 1, IF(discount_pricing_mode=1,term30-diff_pricing_discount_amt,term30)+discount_amt+IF(child_no is null or child_no = '',buyout_amt,IF(buy_out_capital - buy_out_real_repay_capital =0,buyout_amt,buy_out_capital - buy_out_real_repay_capital)), 0))                not_pay_term30," +
                        "       sum(if(is_overdue30 = 1, overdue_fine30, 0))        overdue_fine30," +
                        "       sum(if(is_overdue30 = 1, renew_amt30, 0))           renew_amt30," +
                        "       sum(if(is_overdue30 = 1, 1, 0))                     count_term30").
                lambda().
                eq(RiskDistributionOrderOverdueDO::getOverdueDay, overdueDay).
                ge(RiskDistributionOrderOverdueDO::getCountDay, fromTime).
                le(RiskDistributionOrderOverdueDO::getCountDay, toTime).
                last(" group by   count_day," +
                        "         risk_level," +
                        "         risk_strategy," +
                        "         deposit_free_type," +
                        "         traffic_type," +
                        "         financial_solutions," +
                        "         application_name," +
                        "         equipment_state," +
                        "         supervised_machine," +
                        "         machine_type," +
                        "         audit_type," +
                        "         audit_type_name," +
                        "         mini_type," +
                        "         max_term," +
                        "       scene," +
                        "       quotient_name," +
                        "         finance_type," +
                        "         bond_rate,drainage_type,refuse_type,is_rent,is_mortgage,platform,shop_name,zmf_level");
        List<Map<String, Object>> mapList = listMaps(queryWrapper);
        List<RiskOverdueDTO> list = mapList.stream().map(e -> {
            RiskOverdueDTO riskOverdueDTO = new RiskOverdueDTO();
            String countDayStr = String.valueOf(e.get("count_day"));
            riskOverdueDTO.setCountMonth(countDayStr);
            riskOverdueDTO.setOrderIds(String.valueOf(e.get("order_ids")));
            riskOverdueDTO.setMiniType(Integer.parseInt(String.valueOf(e.get("mini_type"))));
            riskOverdueDTO.setFinanceType(Integer.parseInt(String.valueOf(e.get("finance_type"))));
            riskOverdueDTO.setRiskLevel(String.valueOf(e.get("risk_level")));
            riskOverdueDTO.setScene(String.valueOf(e.get("scene")));
            riskOverdueDTO.setQuotientName(String.valueOf(e.get("quotient_name")));
            riskOverdueDTO.setRiskStrategy(String.valueOf(e.get("risk_strategy")));
            //免押类型
            riskOverdueDTO.setDepositFreeType(String.valueOf(e.get("deposit_free_type")));
            //流量类型
            riskOverdueDTO.setTrafficType(String.valueOf(e.get("traffic_type")));
            //金融方案
            riskOverdueDTO.setFinancialSolutions(String.valueOf(e.get("financial_solutions")));
            //应用
            riskOverdueDTO.setApplicationName(String.valueOf(e.get("application_name")));
            //新旧程度
            riskOverdueDTO.setEquipmentState(String.valueOf(e.get("equipment_state")));
            //是否监管
            riskOverdueDTO.setSupervisedMachine(String.valueOf(e.get("supervised_machine")));
            //机型
            riskOverdueDTO.setMachineType(String.valueOf(e.get("machine_type")));

            riskOverdueDTO.setPlatform(String.valueOf(e.get("platform")));
            riskOverdueDTO.setAuditType(String.valueOf(e.get("audit_type")));
            riskOverdueDTO.setAuditTypeName(String.valueOf(e.get("audit_type_name")));
            riskOverdueDTO.setShopName(String.valueOf(e.get("shop_name")));
            riskOverdueDTO.setIsMortgage(Integer.parseInt(String.valueOf(e.get("is_mortgage"))));
            riskOverdueDTO.setRentType((String.valueOf(e.get("is_rent"))));
            riskOverdueDTO.setIsRent((Integer.parseInt(String.valueOf(e.get("is_rent")))));
            riskOverdueDTO.setRefuseType(Integer.parseInt(String.valueOf(e.get("refuse_type"))));
            riskOverdueDTO.setDrainageType(Integer.parseInt(String.valueOf(e.get("drainage_type"))));
            riskOverdueDTO.setMaxTerm(Integer.parseInt(String.valueOf(e.get("max_term"))));
            riskOverdueDTO.setBondRateInterval(String.valueOf(e.get("bond_rate")));
            riskOverdueDTO.setBuyoutAmt(CalculateUtil.toDecimal(String.valueOf(e.get("buyout_amt"))));
            riskOverdueDTO.setBeforeDiscount(CalculateUtil.toDecimal(String.valueOf(e.get("before_discount"))));
            riskOverdueDTO.setRentTotal(CalculateUtil.toDecimal(String.valueOf(e.get("rent_total"))));
            riskOverdueDTO.setTotalOrderCnt(CalculateUtil.toDecimal(String.valueOf(e.get("total_order_cnt"))));
            // riskOverdueDTO.setScore(Integer.parseInt(String.valueOf(e.get("score"))));
            riskOverdueDTO.setOverdueDay(overdueDay);
            riskOverdueDTO.setBondAmt1(CalculateUtil.toDecimal(e.get("bond_amt1")));
            riskOverdueDTO.setBuyoutAmt1(CalculateUtil.toDecimal(e.get("buyout_amt1")));
            riskOverdueDTO.setDiscountReturnAmt1(CalculateUtil.toDecimal(e.get("discount_return_amt1")));
            riskOverdueDTO.setBeforeDiscount1(CalculateUtil.toDecimal(e.get("before_discount1")));
            riskOverdueDTO.setParentBeforeDiscount1(CalculateUtil.toDecimal(e.get("p_before_discount1")));
            riskOverdueDTO.setParentDiscountReturnAmt1(CalculateUtil.toDecimal(e.get("p_discount_return_amt1")));
            riskOverdueDTO.setBondAmt2(CalculateUtil.toDecimal(e.get("bond_amt2")));
            riskOverdueDTO.setBuyoutAmt2(CalculateUtil.toDecimal(e.get("buyout_amt2")));
            riskOverdueDTO.setDiscountReturnAmt2(CalculateUtil.toDecimal(e.get("discount_return_amt2")));
            riskOverdueDTO.setBeforeDiscount2(CalculateUtil.toDecimal(e.get("before_discount2")));
            riskOverdueDTO.setParentBeforeDiscount2(CalculateUtil.toDecimal(e.get("p_before_discount2")));
            riskOverdueDTO.setParentDiscountReturnAmt2(CalculateUtil.toDecimal(e.get("p_discount_return_amt2")));
            riskOverdueDTO.setBondAmt3(CalculateUtil.toDecimal(e.get("bond_amt3")));
            riskOverdueDTO.setBuyoutAmt3(CalculateUtil.toDecimal(e.get("buyout_amt3")));
            riskOverdueDTO.setDiscountReturnAmt3(CalculateUtil.toDecimal(e.get("discount_return_amt3")));
            riskOverdueDTO.setBeforeDiscount3(CalculateUtil.toDecimal(e.get("before_discount3")));
            riskOverdueDTO.setParentBeforeDiscount3(CalculateUtil.toDecimal(e.get("p_before_discount3")));
            riskOverdueDTO.setParentDiscountReturnAmt3(CalculateUtil.toDecimal(e.get("p_discount_return_amt3")));
            riskOverdueDTO.setBondAmt4(CalculateUtil.toDecimal(e.get("bond_amt4")));
            riskOverdueDTO.setBuyoutAmt4(CalculateUtil.toDecimal(e.get("buyout_amt4")));
            riskOverdueDTO.setDiscountReturnAmt4(CalculateUtil.toDecimal(e.get("discount_return_amt4")));
            riskOverdueDTO.setBeforeDiscount4(CalculateUtil.toDecimal(e.get("before_discount4")));
            riskOverdueDTO.setParentBeforeDiscount4(CalculateUtil.toDecimal(e.get("p_before_discount4")));
            riskOverdueDTO.setParentDiscountReturnAmt4(CalculateUtil.toDecimal(e.get("p_discount_return_amt4")));
            riskOverdueDTO.setBondAmt5(CalculateUtil.toDecimal(e.get("bond_amt5")));
            riskOverdueDTO.setBuyoutAmt5(CalculateUtil.toDecimal(e.get("buyout_amt5")));
            riskOverdueDTO.setDiscountReturnAmt5(CalculateUtil.toDecimal(e.get("discount_return_amt5")));
            riskOverdueDTO.setBeforeDiscount5(CalculateUtil.toDecimal(e.get("before_discount5")));
            riskOverdueDTO.setParentBeforeDiscount5(CalculateUtil.toDecimal(e.get("p_before_discount5")));
            riskOverdueDTO.setParentDiscountReturnAmt5(CalculateUtil.toDecimal(e.get("p_discount_return_amt5")));
            riskOverdueDTO.setBondAmt6(CalculateUtil.toDecimal(e.get("bond_amt6")));
            riskOverdueDTO.setBuyoutAmt6(CalculateUtil.toDecimal(e.get("buyout_amt6")));
            riskOverdueDTO.setDiscountReturnAmt6(CalculateUtil.toDecimal(e.get("discount_return_amt6")));
            riskOverdueDTO.setBeforeDiscount6(CalculateUtil.toDecimal(e.get("before_discount6")));
            riskOverdueDTO.setParentBeforeDiscount6(CalculateUtil.toDecimal(e.get("p_before_discount6")));
            riskOverdueDTO.setParentDiscountReturnAmt6(CalculateUtil.toDecimal(e.get("p_discount_return_amt6")));
            riskOverdueDTO.setBondAmt7(CalculateUtil.toDecimal(e.get("bond_amt7")));
            riskOverdueDTO.setBuyoutAmt7(CalculateUtil.toDecimal(e.get("buyout_amt7")));
            riskOverdueDTO.setDiscountReturnAmt7(CalculateUtil.toDecimal(e.get("discount_return_amt7")));
            riskOverdueDTO.setBeforeDiscount7(CalculateUtil.toDecimal(e.get("before_discount7")));
            riskOverdueDTO.setParentBeforeDiscount7(CalculateUtil.toDecimal(e.get("p_before_discount7")));
            riskOverdueDTO.setParentDiscountReturnAmt7(CalculateUtil.toDecimal(e.get("p_discount_return_amt7")));
            riskOverdueDTO.setBondAmt8(CalculateUtil.toDecimal(e.get("bond_amt8")));
            riskOverdueDTO.setBuyoutAmt8(CalculateUtil.toDecimal(e.get("buyout_amt8")));
            riskOverdueDTO.setDiscountReturnAmt8(CalculateUtil.toDecimal(e.get("discount_return_amt8")));
            riskOverdueDTO.setBeforeDiscount8(CalculateUtil.toDecimal(e.get("before_discount8")));
            riskOverdueDTO.setParentBeforeDiscount8(CalculateUtil.toDecimal(e.get("p_before_discount8")));
            riskOverdueDTO.setParentDiscountReturnAmt8(CalculateUtil.toDecimal(e.get("p_discount_return_amt8")));
            riskOverdueDTO.setBondAmt9(CalculateUtil.toDecimal(e.get("bond_amt9")));
            riskOverdueDTO.setBuyoutAmt9(CalculateUtil.toDecimal(e.get("buyout_amt9")));
            riskOverdueDTO.setDiscountReturnAmt9(CalculateUtil.toDecimal(e.get("discount_return_amt9")));
            riskOverdueDTO.setBeforeDiscount9(CalculateUtil.toDecimal(e.get("before_discount9")));
            riskOverdueDTO.setParentBeforeDiscount9(CalculateUtil.toDecimal(e.get("p_before_discount9")));
            riskOverdueDTO.setParentDiscountReturnAmt9(CalculateUtil.toDecimal(e.get("p_discount_return_amt9")));
            riskOverdueDTO.setBondAmt10(CalculateUtil.toDecimal(e.get("bond_amt10")));
            riskOverdueDTO.setBuyoutAmt10(CalculateUtil.toDecimal(e.get("buyout_amt10")));
            riskOverdueDTO.setDiscountReturnAmt10(CalculateUtil.toDecimal(e.get("discount_return_amt10")));
            riskOverdueDTO.setBeforeDiscount10(CalculateUtil.toDecimal(e.get("before_discount10")));
            riskOverdueDTO.setParentBeforeDiscount10(CalculateUtil.toDecimal(e.get("p_before_discount10")));
            riskOverdueDTO.setParentDiscountReturnAmt10(CalculateUtil.toDecimal(e.get("p_discount_return_amt10")));
            riskOverdueDTO.setBondAmt11(CalculateUtil.toDecimal(e.get("bond_amt11")));
            riskOverdueDTO.setBuyoutAmt11(CalculateUtil.toDecimal(e.get("buyout_amt11")));
            riskOverdueDTO.setDiscountReturnAmt11(CalculateUtil.toDecimal(e.get("discount_return_amt11")));
            riskOverdueDTO.setBeforeDiscount11(CalculateUtil.toDecimal(e.get("before_discount11")));
            riskOverdueDTO.setParentBeforeDiscount11(CalculateUtil.toDecimal(e.get("p_before_discount11")));
            riskOverdueDTO.setParentDiscountReturnAmt11(CalculateUtil.toDecimal(e.get("p_discount_return_amt11")));
            riskOverdueDTO.setBondAmt12(CalculateUtil.toDecimal(e.get("bond_amt12")));
            riskOverdueDTO.setBuyoutAmt12(CalculateUtil.toDecimal(e.get("buyout_amt12")));
            riskOverdueDTO.setDiscountReturnAmt12(CalculateUtil.toDecimal(e.get("discount_return_amt12")));
            riskOverdueDTO.setBeforeDiscount12(CalculateUtil.toDecimal(e.get("before_discount12")));
            riskOverdueDTO.setParentBeforeDiscount12(CalculateUtil.toDecimal(e.get("p_before_discount12")));
            riskOverdueDTO.setParentDiscountReturnAmt12(CalculateUtil.toDecimal(e.get("p_discount_return_amt12")));
            riskOverdueDTO.setBondAmt13(CalculateUtil.toDecimal(e.get("bond_amt13")));
            riskOverdueDTO.setBuyoutAmt13(CalculateUtil.toDecimal(e.get("buyout_amt13")));
            riskOverdueDTO.setDiscountReturnAmt13(CalculateUtil.toDecimal(e.get("discount_return_amt13")));
            riskOverdueDTO.setBeforeDiscount13(CalculateUtil.toDecimal(e.get("before_discount13")));
            riskOverdueDTO.setParentBeforeDiscount13(CalculateUtil.toDecimal(e.get("p_before_discount13")));
            riskOverdueDTO.setParentDiscountReturnAmt13(CalculateUtil.toDecimal(e.get("p_discount_return_amt13")));
            riskOverdueDTO.setBondAmt14(CalculateUtil.toDecimal(e.get("bond_amt14")));
            riskOverdueDTO.setBuyoutAmt14(CalculateUtil.toDecimal(e.get("buyout_amt14")));
            riskOverdueDTO.setDiscountReturnAmt14(CalculateUtil.toDecimal(e.get("discount_return_amt14")));
            riskOverdueDTO.setBeforeDiscount14(CalculateUtil.toDecimal(e.get("before_discount14")));
            riskOverdueDTO.setParentBeforeDiscount14(CalculateUtil.toDecimal(e.get("p_before_discount14")));
            riskOverdueDTO.setParentDiscountReturnAmt14(CalculateUtil.toDecimal(e.get("p_discount_return_amt14")));
            riskOverdueDTO.setBondAmt15(CalculateUtil.toDecimal(e.get("bond_amt15")));
            riskOverdueDTO.setBuyoutAmt15(CalculateUtil.toDecimal(e.get("buyout_amt15")));
            riskOverdueDTO.setDiscountReturnAmt15(CalculateUtil.toDecimal(e.get("discount_return_amt15")));
            riskOverdueDTO.setBeforeDiscount15(CalculateUtil.toDecimal(e.get("before_discount15")));
            riskOverdueDTO.setParentBeforeDiscount15(CalculateUtil.toDecimal(e.get("p_before_discount15")));
            riskOverdueDTO.setParentDiscountReturnAmt15(CalculateUtil.toDecimal(e.get("p_discount_return_amt15")));
            riskOverdueDTO.setBondAmt16(CalculateUtil.toDecimal(e.get("bond_amt16")));
            riskOverdueDTO.setBuyoutAmt16(CalculateUtil.toDecimal(e.get("buyout_amt16")));
            riskOverdueDTO.setDiscountReturnAmt16(CalculateUtil.toDecimal(e.get("discount_return_amt16")));
            riskOverdueDTO.setBeforeDiscount16(CalculateUtil.toDecimal(e.get("before_discount16")));
            riskOverdueDTO.setParentBeforeDiscount16(CalculateUtil.toDecimal(e.get("p_before_discount16")));
            riskOverdueDTO.setParentDiscountReturnAmt16(CalculateUtil.toDecimal(e.get("p_discount_return_amt16")));
            riskOverdueDTO.setBondAmt17(CalculateUtil.toDecimal(e.get("bond_amt17")));
            riskOverdueDTO.setBuyoutAmt17(CalculateUtil.toDecimal(e.get("buyout_amt17")));
            riskOverdueDTO.setDiscountReturnAmt17(CalculateUtil.toDecimal(e.get("discount_return_amt17")));
            riskOverdueDTO.setBeforeDiscount17(CalculateUtil.toDecimal(e.get("before_discount17")));
            riskOverdueDTO.setParentBeforeDiscount17(CalculateUtil.toDecimal(e.get("p_before_discount17")));
            riskOverdueDTO.setParentDiscountReturnAmt17(CalculateUtil.toDecimal(e.get("p_discount_return_amt17")));
            riskOverdueDTO.setBondAmt18(CalculateUtil.toDecimal(e.get("bond_amt18")));
            riskOverdueDTO.setBuyoutAmt18(CalculateUtil.toDecimal(e.get("buyout_amt18")));
            riskOverdueDTO.setDiscountReturnAmt18(CalculateUtil.toDecimal(e.get("discount_return_amt18")));
            riskOverdueDTO.setBeforeDiscount18(CalculateUtil.toDecimal(e.get("before_discount18")));
            riskOverdueDTO.setParentBeforeDiscount18(CalculateUtil.toDecimal(e.get("p_before_discount18")));
            riskOverdueDTO.setParentDiscountReturnAmt18(CalculateUtil.toDecimal(e.get("p_discount_return_amt18")));
            riskOverdueDTO.setBondAmt19(CalculateUtil.toDecimal(e.get("bond_amt19")));
            riskOverdueDTO.setBuyoutAmt19(CalculateUtil.toDecimal(e.get("buyout_amt19")));
            riskOverdueDTO.setDiscountReturnAmt19(CalculateUtil.toDecimal(e.get("discount_return_amt19")));
            riskOverdueDTO.setBeforeDiscount19(CalculateUtil.toDecimal(e.get("before_discount19")));
            riskOverdueDTO.setParentBeforeDiscount19(CalculateUtil.toDecimal(e.get("p_before_discount19")));
            riskOverdueDTO.setParentDiscountReturnAmt19(CalculateUtil.toDecimal(e.get("p_discount_return_amt19")));
            riskOverdueDTO.setBondAmt20(CalculateUtil.toDecimal(e.get("bond_amt20")));
            riskOverdueDTO.setBuyoutAmt20(CalculateUtil.toDecimal(e.get("buyout_amt20")));
            riskOverdueDTO.setDiscountReturnAmt20(CalculateUtil.toDecimal(e.get("discount_return_amt20")));
            riskOverdueDTO.setBeforeDiscount20(CalculateUtil.toDecimal(e.get("before_discount20")));
            riskOverdueDTO.setParentBeforeDiscount20(CalculateUtil.toDecimal(e.get("p_before_discount20")));
            riskOverdueDTO.setParentDiscountReturnAmt20(CalculateUtil.toDecimal(e.get("p_discount_return_amt20")));
            riskOverdueDTO.setBondAmt21(CalculateUtil.toDecimal(e.get("bond_amt21")));
            riskOverdueDTO.setBuyoutAmt21(CalculateUtil.toDecimal(e.get("buyout_amt21")));
            riskOverdueDTO.setDiscountReturnAmt21(CalculateUtil.toDecimal(e.get("discount_return_amt21")));
            riskOverdueDTO.setBeforeDiscount21(CalculateUtil.toDecimal(e.get("before_discount21")));
            riskOverdueDTO.setParentBeforeDiscount21(CalculateUtil.toDecimal(e.get("p_before_discount21")));
            riskOverdueDTO.setParentDiscountReturnAmt21(CalculateUtil.toDecimal(e.get("p_discount_return_amt21")));
            riskOverdueDTO.setBondAmt22(CalculateUtil.toDecimal(e.get("bond_amt22")));
            riskOverdueDTO.setBuyoutAmt22(CalculateUtil.toDecimal(e.get("buyout_amt22")));
            riskOverdueDTO.setDiscountReturnAmt22(CalculateUtil.toDecimal(e.get("discount_return_amt22")));
            riskOverdueDTO.setBeforeDiscount22(CalculateUtil.toDecimal(e.get("before_discount22")));
            riskOverdueDTO.setParentBeforeDiscount22(CalculateUtil.toDecimal(e.get("p_before_discount22")));
            riskOverdueDTO.setParentDiscountReturnAmt22(CalculateUtil.toDecimal(e.get("p_discount_return_amt22")));
            riskOverdueDTO.setBondAmt23(CalculateUtil.toDecimal(e.get("bond_amt23")));
            riskOverdueDTO.setBuyoutAmt23(CalculateUtil.toDecimal(e.get("buyout_amt23")));
            riskOverdueDTO.setDiscountReturnAmt23(CalculateUtil.toDecimal(e.get("discount_return_amt23")));
            riskOverdueDTO.setBeforeDiscount23(CalculateUtil.toDecimal(e.get("before_discount23")));
            riskOverdueDTO.setParentBeforeDiscount23(CalculateUtil.toDecimal(e.get("p_before_discount23")));
            riskOverdueDTO.setParentDiscountReturnAmt23(CalculateUtil.toDecimal(e.get("p_discount_return_amt23")));
            riskOverdueDTO.setBondAmt24(CalculateUtil.toDecimal(e.get("bond_amt24")));
            riskOverdueDTO.setBuyoutAmt24(CalculateUtil.toDecimal(e.get("buyout_amt24")));
            riskOverdueDTO.setDiscountReturnAmt24(CalculateUtil.toDecimal(e.get("discount_return_amt24")));
            riskOverdueDTO.setBeforeDiscount24(CalculateUtil.toDecimal(e.get("before_discount24")));
            riskOverdueDTO.setParentBeforeDiscount24(CalculateUtil.toDecimal(e.get("p_before_discount24")));
            riskOverdueDTO.setParentDiscountReturnAmt24(CalculateUtil.toDecimal(e.get("p_discount_return_amt24")));
            riskOverdueDTO.setBondAmt25(CalculateUtil.toDecimal(e.get("bond_amt25")));
            riskOverdueDTO.setBuyoutAmt25(CalculateUtil.toDecimal(e.get("buyout_amt25")));
            riskOverdueDTO.setDiscountReturnAmt25(CalculateUtil.toDecimal(e.get("discount_return_amt25")));
            riskOverdueDTO.setBeforeDiscount25(CalculateUtil.toDecimal(e.get("before_discount25")));
            riskOverdueDTO.setParentBeforeDiscount25(CalculateUtil.toDecimal(e.get("p_before_discount25")));
            riskOverdueDTO.setParentDiscountReturnAmt25(CalculateUtil.toDecimal(e.get("p_discount_return_amt25")));
            riskOverdueDTO.setBondAmt26(CalculateUtil.toDecimal(e.get("bond_amt26")));
            riskOverdueDTO.setBuyoutAmt26(CalculateUtil.toDecimal(e.get("buyout_amt26")));
            riskOverdueDTO.setDiscountReturnAmt26(CalculateUtil.toDecimal(e.get("discount_return_amt26")));
            riskOverdueDTO.setBeforeDiscount26(CalculateUtil.toDecimal(e.get("before_discount26")));
            riskOverdueDTO.setParentBeforeDiscount26(CalculateUtil.toDecimal(e.get("p_before_discount26")));
            riskOverdueDTO.setParentDiscountReturnAmt26(CalculateUtil.toDecimal(e.get("p_discount_return_amt26")));
            riskOverdueDTO.setBondAmt27(CalculateUtil.toDecimal(e.get("bond_amt27")));
            riskOverdueDTO.setBuyoutAmt27(CalculateUtil.toDecimal(e.get("buyout_amt27")));
            riskOverdueDTO.setDiscountReturnAmt27(CalculateUtil.toDecimal(e.get("discount_return_amt27")));
            riskOverdueDTO.setBeforeDiscount27(CalculateUtil.toDecimal(e.get("before_discount27")));
            riskOverdueDTO.setParentBeforeDiscount27(CalculateUtil.toDecimal(e.get("p_before_discount27")));
            riskOverdueDTO.setParentDiscountReturnAmt27(CalculateUtil.toDecimal(e.get("p_discount_return_amt27")));
            riskOverdueDTO.setBondAmt28(CalculateUtil.toDecimal(e.get("bond_amt28")));
            riskOverdueDTO.setBuyoutAmt28(CalculateUtil.toDecimal(e.get("buyout_amt28")));
            riskOverdueDTO.setDiscountReturnAmt28(CalculateUtil.toDecimal(e.get("discount_return_amt28")));
            riskOverdueDTO.setBeforeDiscount28(CalculateUtil.toDecimal(e.get("before_discount28")));
            riskOverdueDTO.setParentBeforeDiscount28(CalculateUtil.toDecimal(e.get("p_before_discount28")));
            riskOverdueDTO.setParentDiscountReturnAmt28(CalculateUtil.toDecimal(e.get("p_discount_return_amt28")));
            riskOverdueDTO.setBondAmt29(CalculateUtil.toDecimal(e.get("bond_amt29")));
            riskOverdueDTO.setBuyoutAmt29(CalculateUtil.toDecimal(e.get("buyout_amt29")));
            riskOverdueDTO.setDiscountReturnAmt29(CalculateUtil.toDecimal(e.get("discount_return_amt29")));
            riskOverdueDTO.setBeforeDiscount29(CalculateUtil.toDecimal(e.get("before_discount29")));
            riskOverdueDTO.setParentBeforeDiscount29(CalculateUtil.toDecimal(e.get("p_before_discount29")));
            riskOverdueDTO.setParentDiscountReturnAmt29(CalculateUtil.toDecimal(e.get("p_discount_return_amt29")));
            riskOverdueDTO.setBondAmt30(CalculateUtil.toDecimal(e.get("bond_amt30")));
            riskOverdueDTO.setBuyoutAmt30(CalculateUtil.toDecimal(e.get("buyout_amt30")));
            riskOverdueDTO.setDiscountReturnAmt30(CalculateUtil.toDecimal(e.get("discount_return_amt30")));
            riskOverdueDTO.setBeforeDiscount30(CalculateUtil.toDecimal(e.get("before_discount30")));
            riskOverdueDTO.setParentBeforeDiscount30(CalculateUtil.toDecimal(e.get("p_before_discount30")));
            riskOverdueDTO.setParentDiscountReturnAmt30(CalculateUtil.toDecimal(e.get("p_discount_return_amt30")));

            riskOverdueDTO.setNotPayTerm1(CalculateUtil.toDecimal(e.get("not_pay_term1")));
            riskOverdueDTO.setOverdueFine1(CalculateUtil.toDecimal(e.get("overdue_fine1")));
            riskOverdueDTO.setRenewAmt1(CalculateUtil.toDecimal(e.get("renew_amt1")));
            riskOverdueDTO.setCountTerm1(CalculateUtil.toDecimal(e.get("count_term1")));
            riskOverdueDTO.setNotPayTerm2(CalculateUtil.toDecimal(e.get("not_pay_term2")));
            riskOverdueDTO.setOverdueFine2(CalculateUtil.toDecimal(e.get("overdue_fine2")));
            riskOverdueDTO.setRenewAmt2(CalculateUtil.toDecimal(e.get("renew_amt2")));
            riskOverdueDTO.setCountTerm2(CalculateUtil.toDecimal(e.get("count_term2")));
            riskOverdueDTO.setNotPayTerm3(CalculateUtil.toDecimal(e.get("not_pay_term3")));
            riskOverdueDTO.setOverdueFine3(CalculateUtil.toDecimal(e.get("overdue_fine3")));
            riskOverdueDTO.setRenewAmt3(CalculateUtil.toDecimal(e.get("renew_amt3")));
            riskOverdueDTO.setCountTerm3(CalculateUtil.toDecimal(e.get("count_term3")));
            riskOverdueDTO.setNotPayTerm4(CalculateUtil.toDecimal(e.get("not_pay_term4")));
            riskOverdueDTO.setOverdueFine4(CalculateUtil.toDecimal(e.get("overdue_fine4")));
            riskOverdueDTO.setRenewAmt4(CalculateUtil.toDecimal(e.get("renew_amt4")));
            riskOverdueDTO.setCountTerm4(CalculateUtil.toDecimal(e.get("count_term4")));
            riskOverdueDTO.setNotPayTerm5(CalculateUtil.toDecimal(e.get("not_pay_term5")));
            riskOverdueDTO.setOverdueFine5(CalculateUtil.toDecimal(e.get("overdue_fine5")));
            riskOverdueDTO.setRenewAmt5(CalculateUtil.toDecimal(e.get("renew_amt5")));
            riskOverdueDTO.setCountTerm5(CalculateUtil.toDecimal(e.get("count_term5")));
            riskOverdueDTO.setNotPayTerm6(CalculateUtil.toDecimal(e.get("not_pay_term6")));
            riskOverdueDTO.setOverdueFine6(CalculateUtil.toDecimal(e.get("overdue_fine6")));
            riskOverdueDTO.setRenewAmt6(CalculateUtil.toDecimal(e.get("renew_amt6")));
            riskOverdueDTO.setCountTerm6(CalculateUtil.toDecimal(e.get("count_term6")));
            riskOverdueDTO.setNotPayTerm7(CalculateUtil.toDecimal(e.get("not_pay_term7")));
            riskOverdueDTO.setOverdueFine7(CalculateUtil.toDecimal(e.get("overdue_fine7")));
            riskOverdueDTO.setRenewAmt7(CalculateUtil.toDecimal(e.get("renew_amt7")));
            riskOverdueDTO.setCountTerm7(CalculateUtil.toDecimal(e.get("count_term7")));
            riskOverdueDTO.setNotPayTerm8(CalculateUtil.toDecimal(e.get("not_pay_term8")));
            riskOverdueDTO.setOverdueFine8(CalculateUtil.toDecimal(e.get("overdue_fine8")));
            riskOverdueDTO.setRenewAmt8(CalculateUtil.toDecimal(e.get("renew_amt8")));
            riskOverdueDTO.setCountTerm8(CalculateUtil.toDecimal(e.get("count_term8")));
            riskOverdueDTO.setNotPayTerm9(CalculateUtil.toDecimal(e.get("not_pay_term9")));
            riskOverdueDTO.setOverdueFine9(CalculateUtil.toDecimal(e.get("overdue_fine9")));
            riskOverdueDTO.setRenewAmt9(CalculateUtil.toDecimal(e.get("renew_amt9")));
            riskOverdueDTO.setCountTerm9(CalculateUtil.toDecimal(e.get("count_term9")));
            riskOverdueDTO.setNotPayTerm10(CalculateUtil.toDecimal(e.get("not_pay_term10")));
            riskOverdueDTO.setOverdueFine10(CalculateUtil.toDecimal(e.get("overdue_fine10")));
            riskOverdueDTO.setRenewAmt10(CalculateUtil.toDecimal(e.get("renew_amt10")));
            riskOverdueDTO.setCountTerm10(CalculateUtil.toDecimal(e.get("count_term10")));
            riskOverdueDTO.setNotPayTerm11(CalculateUtil.toDecimal(e.get("not_pay_term11")));
            riskOverdueDTO.setOverdueFine11(CalculateUtil.toDecimal(e.get("overdue_fine11")));
            riskOverdueDTO.setRenewAmt11(CalculateUtil.toDecimal(e.get("renew_amt11")));
            riskOverdueDTO.setCountTerm11(CalculateUtil.toDecimal(e.get("count_term11")));
            riskOverdueDTO.setNotPayTerm12(CalculateUtil.toDecimal(e.get("not_pay_term12")));
            riskOverdueDTO.setOverdueFine12(CalculateUtil.toDecimal(e.get("overdue_fine12")));
            riskOverdueDTO.setRenewAmt12(CalculateUtil.toDecimal(e.get("renew_amt12")));
            riskOverdueDTO.setCountTerm12(CalculateUtil.toDecimal(e.get("count_term12")));
            riskOverdueDTO.setNotPayTerm13(CalculateUtil.toDecimal(e.get("not_pay_term13")));
            riskOverdueDTO.setOverdueFine13(CalculateUtil.toDecimal(e.get("overdue_fine13")));
            riskOverdueDTO.setRenewAmt13(CalculateUtil.toDecimal(e.get("renew_amt13")));
            riskOverdueDTO.setCountTerm13(CalculateUtil.toDecimal(e.get("count_term13")));
            riskOverdueDTO.setNotPayTerm14(CalculateUtil.toDecimal(e.get("not_pay_term14")));
            riskOverdueDTO.setOverdueFine14(CalculateUtil.toDecimal(e.get("overdue_fine14")));
            riskOverdueDTO.setRenewAmt14(CalculateUtil.toDecimal(e.get("renew_amt14")));
            riskOverdueDTO.setCountTerm14(CalculateUtil.toDecimal(e.get("count_term14")));
            riskOverdueDTO.setNotPayTerm15(CalculateUtil.toDecimal(e.get("not_pay_term15")));
            riskOverdueDTO.setOverdueFine15(CalculateUtil.toDecimal(e.get("overdue_fine15")));
            riskOverdueDTO.setRenewAmt15(CalculateUtil.toDecimal(e.get("renew_amt15")));
            riskOverdueDTO.setCountTerm15(CalculateUtil.toDecimal(e.get("count_term15")));
            riskOverdueDTO.setNotPayTerm16(CalculateUtil.toDecimal(e.get("not_pay_term16")));
            riskOverdueDTO.setOverdueFine16(CalculateUtil.toDecimal(e.get("overdue_fine16")));
            riskOverdueDTO.setRenewAmt16(CalculateUtil.toDecimal(e.get("renew_amt16")));
            riskOverdueDTO.setCountTerm16(CalculateUtil.toDecimal(e.get("count_term16")));
            riskOverdueDTO.setNotPayTerm17(CalculateUtil.toDecimal(e.get("not_pay_term17")));
            riskOverdueDTO.setOverdueFine17(CalculateUtil.toDecimal(e.get("overdue_fine17")));
            riskOverdueDTO.setRenewAmt17(CalculateUtil.toDecimal(e.get("renew_amt17")));
            riskOverdueDTO.setCountTerm17(CalculateUtil.toDecimal(e.get("count_term17")));
            riskOverdueDTO.setNotPayTerm18(CalculateUtil.toDecimal(e.get("not_pay_term18")));
            riskOverdueDTO.setOverdueFine18(CalculateUtil.toDecimal(e.get("overdue_fine18")));
            riskOverdueDTO.setRenewAmt18(CalculateUtil.toDecimal(e.get("renew_amt18")));
            riskOverdueDTO.setCountTerm18(CalculateUtil.toDecimal(e.get("count_term18")));
            riskOverdueDTO.setNotPayTerm19(CalculateUtil.toDecimal(e.get("not_pay_term19")));
            riskOverdueDTO.setOverdueFine19(CalculateUtil.toDecimal(e.get("overdue_fine19")));
            riskOverdueDTO.setRenewAmt19(CalculateUtil.toDecimal(e.get("renew_amt19")));
            riskOverdueDTO.setCountTerm19(CalculateUtil.toDecimal(e.get("count_term19")));
            riskOverdueDTO.setNotPayTerm20(CalculateUtil.toDecimal(e.get("not_pay_term20")));
            riskOverdueDTO.setOverdueFine20(CalculateUtil.toDecimal(e.get("overdue_fine20")));
            riskOverdueDTO.setRenewAmt20(CalculateUtil.toDecimal(e.get("renew_amt20")));
            riskOverdueDTO.setCountTerm20(CalculateUtil.toDecimal(e.get("count_term20")));
            riskOverdueDTO.setNotPayTerm21(CalculateUtil.toDecimal(e.get("not_pay_term21")));
            riskOverdueDTO.setOverdueFine21(CalculateUtil.toDecimal(e.get("overdue_fine21")));
            riskOverdueDTO.setRenewAmt21(CalculateUtil.toDecimal(e.get("renew_amt21")));
            riskOverdueDTO.setCountTerm21(CalculateUtil.toDecimal(e.get("count_term21")));
            riskOverdueDTO.setNotPayTerm22(CalculateUtil.toDecimal(e.get("not_pay_term22")));
            riskOverdueDTO.setOverdueFine22(CalculateUtil.toDecimal(e.get("overdue_fine22")));
            riskOverdueDTO.setRenewAmt22(CalculateUtil.toDecimal(e.get("renew_amt22")));
            riskOverdueDTO.setCountTerm22(CalculateUtil.toDecimal(e.get("count_term22")));
            riskOverdueDTO.setNotPayTerm23(CalculateUtil.toDecimal(e.get("not_pay_term23")));
            riskOverdueDTO.setOverdueFine23(CalculateUtil.toDecimal(e.get("overdue_fine23")));
            riskOverdueDTO.setRenewAmt23(CalculateUtil.toDecimal(e.get("renew_amt23")));
            riskOverdueDTO.setCountTerm23(CalculateUtil.toDecimal(e.get("count_term23")));
            riskOverdueDTO.setNotPayTerm24(CalculateUtil.toDecimal(e.get("not_pay_term24")));
            riskOverdueDTO.setOverdueFine24(CalculateUtil.toDecimal(e.get("overdue_fine24")));
            riskOverdueDTO.setRenewAmt24(CalculateUtil.toDecimal(e.get("renew_amt24")));
            riskOverdueDTO.setCountTerm24(CalculateUtil.toDecimal(e.get("count_term24")));
            riskOverdueDTO.setNotPayTerm25(CalculateUtil.toDecimal(e.get("not_pay_term25")));
            riskOverdueDTO.setOverdueFine25(CalculateUtil.toDecimal(e.get("overdue_fine25")));
            riskOverdueDTO.setRenewAmt25(CalculateUtil.toDecimal(e.get("renew_amt25")));
            riskOverdueDTO.setCountTerm25(CalculateUtil.toDecimal(e.get("count_term25")));
            riskOverdueDTO.setNotPayTerm26(CalculateUtil.toDecimal(e.get("not_pay_term26")));
            riskOverdueDTO.setOverdueFine26(CalculateUtil.toDecimal(e.get("overdue_fine26")));
            riskOverdueDTO.setRenewAmt26(CalculateUtil.toDecimal(e.get("renew_amt26")));
            riskOverdueDTO.setCountTerm26(CalculateUtil.toDecimal(e.get("count_term26")));
            riskOverdueDTO.setNotPayTerm27(CalculateUtil.toDecimal(e.get("not_pay_term27")));
            riskOverdueDTO.setOverdueFine27(CalculateUtil.toDecimal(e.get("overdue_fine27")));
            riskOverdueDTO.setRenewAmt27(CalculateUtil.toDecimal(e.get("renew_amt27")));
            riskOverdueDTO.setCountTerm27(CalculateUtil.toDecimal(e.get("count_term27")));
            riskOverdueDTO.setNotPayTerm28(CalculateUtil.toDecimal(e.get("not_pay_term28")));
            riskOverdueDTO.setOverdueFine28(CalculateUtil.toDecimal(e.get("overdue_fine28")));
            riskOverdueDTO.setRenewAmt28(CalculateUtil.toDecimal(e.get("renew_amt28")));
            riskOverdueDTO.setCountTerm28(CalculateUtil.toDecimal(e.get("count_term28")));
            riskOverdueDTO.setNotPayTerm29(CalculateUtil.toDecimal(e.get("not_pay_term29")));
            riskOverdueDTO.setOverdueFine29(CalculateUtil.toDecimal(e.get("overdue_fine29")));
            riskOverdueDTO.setRenewAmt29(CalculateUtil.toDecimal(e.get("renew_amt29")));
            riskOverdueDTO.setCountTerm29(CalculateUtil.toDecimal(e.get("count_term29")));
            riskOverdueDTO.setNotPayTerm30(CalculateUtil.toDecimal(e.get("not_pay_term30")));
            riskOverdueDTO.setOverdueFine30(CalculateUtil.toDecimal(e.get("overdue_fine30")));
            riskOverdueDTO.setRenewAmt30(CalculateUtil.toDecimal(e.get("renew_amt30")));
            riskOverdueDTO.setCountTerm30(CalculateUtil.toDecimal(e.get("count_term30")));
            riskOverdueDTO.setZmfLevel((String.valueOf(e.get("zmf_level"))));


            /*新增*/
            riskOverdueDTO.setDiscountTotal(CalculateUtil.toDecimal(String.valueOf(e.get("discount_total"))));
            riskOverdueDTO.setSurplusBondAmtTotal(CalculateUtil.toDecimal(e.get("surplus_bond_amt_total")));
            riskOverdueDTO.setBondRestFundAmountTotal(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount_total")));
            riskOverdueDTO.setDiffPricingDiscountAmtTotal(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt_total")));
            riskOverdueDTO.setCouponDiscountAmtTotal(CalculateUtil.toDecimal(e.get("coupon_discount_amt_total")));
            riskOverdueDTO.setRenewTotalRent(CalculateUtil.toDecimal(e.get("renew_total_rent")));
            riskOverdueDTO.setRentTotalInner(CalculateUtil.toDecimal(e.get("rent_total_inner")));

            riskOverdueDTO.setSurplusBondAmt1(CalculateUtil.toDecimal(e.get("surplus_bond_amt1")));
            riskOverdueDTO.setSurplusBondAmt2(CalculateUtil.toDecimal(e.get("surplus_bond_amt2")));
            riskOverdueDTO.setSurplusBondAmt3(CalculateUtil.toDecimal(e.get("surplus_bond_amt3")));
            riskOverdueDTO.setSurplusBondAmt4(CalculateUtil.toDecimal(e.get("surplus_bond_amt4")));
            riskOverdueDTO.setSurplusBondAmt5(CalculateUtil.toDecimal(e.get("surplus_bond_amt5")));
            riskOverdueDTO.setSurplusBondAmt6(CalculateUtil.toDecimal(e.get("surplus_bond_amt6")));
            riskOverdueDTO.setSurplusBondAmt7(CalculateUtil.toDecimal(e.get("surplus_bond_amt7")));
            riskOverdueDTO.setSurplusBondAmt8(CalculateUtil.toDecimal(e.get("surplus_bond_amt8")));
            riskOverdueDTO.setSurplusBondAmt9(CalculateUtil.toDecimal(e.get("surplus_bond_amt9")));
            riskOverdueDTO.setSurplusBondAmt10(CalculateUtil.toDecimal(e.get("surplus_bond_amt10")));
            riskOverdueDTO.setSurplusBondAmt11(CalculateUtil.toDecimal(e.get("surplus_bond_amt11")));
            riskOverdueDTO.setSurplusBondAmt12(CalculateUtil.toDecimal(e.get("surplus_bond_amt12")));
            riskOverdueDTO.setSurplusBondAmt13(CalculateUtil.toDecimal(e.get("surplus_bond_amt13")));
            riskOverdueDTO.setSurplusBondAmt14(CalculateUtil.toDecimal(e.get("surplus_bond_amt14")));
            riskOverdueDTO.setSurplusBondAmt15(CalculateUtil.toDecimal(e.get("surplus_bond_amt15")));
            riskOverdueDTO.setSurplusBondAmt16(CalculateUtil.toDecimal(e.get("surplus_bond_amt16")));
            riskOverdueDTO.setSurplusBondAmt17(CalculateUtil.toDecimal(e.get("surplus_bond_amt17")));
            riskOverdueDTO.setSurplusBondAmt18(CalculateUtil.toDecimal(e.get("surplus_bond_amt18")));
            riskOverdueDTO.setSurplusBondAmt19(CalculateUtil.toDecimal(e.get("surplus_bond_amt19")));
            riskOverdueDTO.setSurplusBondAmt20(CalculateUtil.toDecimal(e.get("surplus_bond_amt20")));
            riskOverdueDTO.setSurplusBondAmt21(CalculateUtil.toDecimal(e.get("surplus_bond_amt21")));
            riskOverdueDTO.setSurplusBondAmt22(CalculateUtil.toDecimal(e.get("surplus_bond_amt22")));
            riskOverdueDTO.setSurplusBondAmt23(CalculateUtil.toDecimal(e.get("surplus_bond_amt23")));
            riskOverdueDTO.setSurplusBondAmt24(CalculateUtil.toDecimal(e.get("surplus_bond_amt24")));
            riskOverdueDTO.setSurplusBondAmt25(CalculateUtil.toDecimal(e.get("surplus_bond_amt25")));
            riskOverdueDTO.setSurplusBondAmt26(CalculateUtil.toDecimal(e.get("surplus_bond_amt26")));
            riskOverdueDTO.setSurplusBondAmt27(CalculateUtil.toDecimal(e.get("surplus_bond_amt27")));
            riskOverdueDTO.setSurplusBondAmt28(CalculateUtil.toDecimal(e.get("surplus_bond_amt28")));
            riskOverdueDTO.setSurplusBondAmt29(CalculateUtil.toDecimal(e.get("surplus_bond_amt29")));
            riskOverdueDTO.setSurplusBondAmt30(CalculateUtil.toDecimal(e.get("surplus_bond_amt30")));

            riskOverdueDTO.setBondRestFundAmount1(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount1")));
            riskOverdueDTO.setBondRestFundAmount2(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount2")));
            riskOverdueDTO.setBondRestFundAmount3(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount3")));
            riskOverdueDTO.setBondRestFundAmount4(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount4")));
            riskOverdueDTO.setBondRestFundAmount5(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount5")));
            riskOverdueDTO.setBondRestFundAmount6(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount6")));
            riskOverdueDTO.setBondRestFundAmount7(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount7")));
            riskOverdueDTO.setBondRestFundAmount8(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount8")));
            riskOverdueDTO.setBondRestFundAmount9(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount9")));
            riskOverdueDTO.setBondRestFundAmount10(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount10")));
            riskOverdueDTO.setBondRestFundAmount11(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount11")));
            riskOverdueDTO.setBondRestFundAmount12(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount12")));
            riskOverdueDTO.setBondRestFundAmount13(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount13")));
            riskOverdueDTO.setBondRestFundAmount14(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount14")));
            riskOverdueDTO.setBondRestFundAmount15(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount15")));
            riskOverdueDTO.setBondRestFundAmount16(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount16")));
            riskOverdueDTO.setBondRestFundAmount17(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount17")));
            riskOverdueDTO.setBondRestFundAmount18(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount18")));
            riskOverdueDTO.setBondRestFundAmount19(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount19")));
            riskOverdueDTO.setBondRestFundAmount20(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount20")));
            riskOverdueDTO.setBondRestFundAmount21(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount21")));
            riskOverdueDTO.setBondRestFundAmount22(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount22")));
            riskOverdueDTO.setBondRestFundAmount23(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount23")));
            riskOverdueDTO.setBondRestFundAmount24(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount24")));
            riskOverdueDTO.setBondRestFundAmount25(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount25")));
            riskOverdueDTO.setBondRestFundAmount26(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount26")));
            riskOverdueDTO.setBondRestFundAmount27(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount27")));
            riskOverdueDTO.setBondRestFundAmount28(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount28")));
            riskOverdueDTO.setBondRestFundAmount29(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount29")));
            riskOverdueDTO.setBondRestFundAmount30(CalculateUtil.toDecimal(e.get("bond_rest_fund_amount30")));

            riskOverdueDTO.setDiffPricingDiscountAmt1(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt1")));
            riskOverdueDTO.setDiffPricingDiscountAmt2(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt2")));
            riskOverdueDTO.setDiffPricingDiscountAmt3(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt3")));
            riskOverdueDTO.setDiffPricingDiscountAmt4(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt4")));
            riskOverdueDTO.setDiffPricingDiscountAmt5(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt5")));
            riskOverdueDTO.setDiffPricingDiscountAmt6(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt6")));
            riskOverdueDTO.setDiffPricingDiscountAmt7(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt7")));
            riskOverdueDTO.setDiffPricingDiscountAmt8(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt8")));
            riskOverdueDTO.setDiffPricingDiscountAmt9(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt9")));
            riskOverdueDTO.setDiffPricingDiscountAmt10(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt10")));
            riskOverdueDTO.setDiffPricingDiscountAmt11(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt11")));
            riskOverdueDTO.setDiffPricingDiscountAmt12(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt12")));
            riskOverdueDTO.setDiffPricingDiscountAmt13(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt13")));
            riskOverdueDTO.setDiffPricingDiscountAmt14(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt14")));
            riskOverdueDTO.setDiffPricingDiscountAmt15(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt15")));
            riskOverdueDTO.setDiffPricingDiscountAmt16(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt16")));
            riskOverdueDTO.setDiffPricingDiscountAmt17(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt17")));
            riskOverdueDTO.setDiffPricingDiscountAmt18(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt18")));
            riskOverdueDTO.setDiffPricingDiscountAmt19(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt19")));
            riskOverdueDTO.setDiffPricingDiscountAmt20(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt20")));
            riskOverdueDTO.setDiffPricingDiscountAmt21(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt21")));
            riskOverdueDTO.setDiffPricingDiscountAmt22(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt22")));
            riskOverdueDTO.setDiffPricingDiscountAmt23(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt23")));
            riskOverdueDTO.setDiffPricingDiscountAmt24(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt24")));
            riskOverdueDTO.setDiffPricingDiscountAmt25(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt25")));
            riskOverdueDTO.setDiffPricingDiscountAmt26(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt26")));
            riskOverdueDTO.setDiffPricingDiscountAmt27(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt27")));
            riskOverdueDTO.setDiffPricingDiscountAmt28(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt28")));
            riskOverdueDTO.setDiffPricingDiscountAmt29(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt29")));
            riskOverdueDTO.setDiffPricingDiscountAmt30(CalculateUtil.toDecimal(e.get("diff_pricing_discount_amt30")));

            riskOverdueDTO.setCouponDiscountAmt1(CalculateUtil.toDecimal(e.get("coupon_discount_amt1")));
            riskOverdueDTO.setCouponDiscountAmt2(CalculateUtil.toDecimal(e.get("coupon_discount_amt2")));
            riskOverdueDTO.setCouponDiscountAmt3(CalculateUtil.toDecimal(e.get("coupon_discount_amt3")));
            riskOverdueDTO.setCouponDiscountAmt4(CalculateUtil.toDecimal(e.get("coupon_discount_amt4")));
            riskOverdueDTO.setCouponDiscountAmt5(CalculateUtil.toDecimal(e.get("coupon_discount_amt5")));
            riskOverdueDTO.setCouponDiscountAmt6(CalculateUtil.toDecimal(e.get("coupon_discount_amt6")));
            riskOverdueDTO.setCouponDiscountAmt7(CalculateUtil.toDecimal(e.get("coupon_discount_amt7")));
            riskOverdueDTO.setCouponDiscountAmt8(CalculateUtil.toDecimal(e.get("coupon_discount_amt8")));
            riskOverdueDTO.setCouponDiscountAmt9(CalculateUtil.toDecimal(e.get("coupon_discount_amt9")));
            riskOverdueDTO.setCouponDiscountAmt10(CalculateUtil.toDecimal(e.get("coupon_discount_amt10")));
            riskOverdueDTO.setCouponDiscountAmt11(CalculateUtil.toDecimal(e.get("coupon_discount_amt11")));
            riskOverdueDTO.setCouponDiscountAmt12(CalculateUtil.toDecimal(e.get("coupon_discount_amt12")));
            riskOverdueDTO.setCouponDiscountAmt13(CalculateUtil.toDecimal(e.get("coupon_discount_amt13")));
            riskOverdueDTO.setCouponDiscountAmt14(CalculateUtil.toDecimal(e.get("coupon_discount_amt14")));
            riskOverdueDTO.setCouponDiscountAmt15(CalculateUtil.toDecimal(e.get("coupon_discount_amt15")));
            riskOverdueDTO.setCouponDiscountAmt16(CalculateUtil.toDecimal(e.get("coupon_discount_amt16")));
            riskOverdueDTO.setCouponDiscountAmt17(CalculateUtil.toDecimal(e.get("coupon_discount_amt17")));
            riskOverdueDTO.setCouponDiscountAmt18(CalculateUtil.toDecimal(e.get("coupon_discount_amt18")));
            riskOverdueDTO.setCouponDiscountAmt19(CalculateUtil.toDecimal(e.get("coupon_discount_amt19")));
            riskOverdueDTO.setCouponDiscountAmt20(CalculateUtil.toDecimal(e.get("coupon_discount_amt20")));
            riskOverdueDTO.setCouponDiscountAmt21(CalculateUtil.toDecimal(e.get("coupon_discount_amt21")));
            riskOverdueDTO.setCouponDiscountAmt22(CalculateUtil.toDecimal(e.get("coupon_discount_amt22")));
            riskOverdueDTO.setCouponDiscountAmt23(CalculateUtil.toDecimal(e.get("coupon_discount_amt23")));
            riskOverdueDTO.setCouponDiscountAmt24(CalculateUtil.toDecimal(e.get("coupon_discount_amt24")));
            riskOverdueDTO.setCouponDiscountAmt25(CalculateUtil.toDecimal(e.get("coupon_discount_amt25")));
            riskOverdueDTO.setCouponDiscountAmt26(CalculateUtil.toDecimal(e.get("coupon_discount_amt26")));
            riskOverdueDTO.setCouponDiscountAmt27(CalculateUtil.toDecimal(e.get("coupon_discount_amt27")));
            riskOverdueDTO.setCouponDiscountAmt28(CalculateUtil.toDecimal(e.get("coupon_discount_amt28")));
            riskOverdueDTO.setCouponDiscountAmt29(CalculateUtil.toDecimal(e.get("coupon_discount_amt29")));
            riskOverdueDTO.setCouponDiscountAmt30(CalculateUtil.toDecimal(e.get("coupon_discount_amt30")));

            return riskOverdueDTO;
        }).collect(Collectors.toList());
        mapList.clear();
        return list;
    }

    @Override
    public List<RiskDistributionOrderOverdueDO> getList(List<Long> ids, Integer overdueDay) {

        QueryWrapper<RiskDistributionOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskDistributionOrderOverdueDO::getOverdueDay, overdueDay).
                    in(CollUtil.isNotEmpty(ids), RiskDistributionOrderOverdueDO::getOrderId, ids);
        return this.list(queryWrapper);

    }

    @Override
    public List<RiskDistributionOrderOverdueDO> getAuditType(String fromTime, String toTime, List<Integer> miniType) {
        QueryWrapper<RiskDistributionOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct audit_type, risk_level, risk_strategy, finance_type,is_mortgage,is_rent,shop_name,scene,quotient_name,zmf_level").
                lambda().
                in(CollUtil.isNotEmpty(miniType), RiskDistributionOrderOverdueDO::getMiniType, miniType)
                .between(RiskDistributionOrderOverdueDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<RiskDistributionOrderOverdueDO> getAuditTypeNew(String fromTime, String toTime, List<Integer> miniType, Integer overdueDay) {
        QueryWrapper<RiskDistributionOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct deposit_free_type,traffic_type,financial_solutions,application_name,equipment_state" +
                        ",supervised_machine,machine_type,audit_type, risk_level, risk_strategy," +
                        " finance_type,is_mortgage,is_rent,shop_name,scene,quotient_name,zmf_level").
                lambda().
                eq(RiskDistributionOrderOverdueDO::getOverdueDay, overdueDay);
        return list(queryWrapper);
    }

    @Override
    public List<RiskDistributionOrderOverdueDO> getBondRate(String fromTime, String toTime, List<Integer> miniType, Integer overdueDay) {
        QueryWrapper<RiskDistributionOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct bond_rate").
                lambda().
                eq(RiskDistributionOrderOverdueDO::getOverdueDay, overdueDay).
                in(CollUtil.isNotEmpty(miniType), RiskDistributionOrderOverdueDO::getMiniType, miniType)
                .between(RiskDistributionOrderOverdueDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }
}
