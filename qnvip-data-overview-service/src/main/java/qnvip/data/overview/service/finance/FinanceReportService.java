package qnvip.data.overview.service.finance;

import qnvip.data.overview.domain.finance.FinanceReportDO;
import qnvip.data.overview.domain.web.DataCountOverviewDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * create by gw on 2022/2/24
 */
public interface FinanceReportService extends BaseService<FinanceReportDO> {

    List<FinanceReportDO> getList(String sTime, String eTime,
                                  String sEndTime, String eEndTime,
                                  String sParentTime, String eParentTime,
                                  String sParentEndTime, String eParentEndTime,
                                  Integer businessType,
                                  List<Integer> miniTypeList, List<Integer> financeTypeList, Integer renewFlag);

    void deleteAllWithBusinessType(Integer businessType);

    List<DataCountOverviewDO> getBusinessAmountByMonth(Integer type);
}