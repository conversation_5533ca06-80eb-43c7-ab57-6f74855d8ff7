package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OrderDayBillDO;
import qnvip.rent.common.base.BaseService;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2021/11/8 14:19
 */
public interface OrderDayBillService extends BaseService<OrderDayBillDO> {

    List<OrderDayBillDO> getList(LocalDateTime startTime, LocalDateTime endTime, List<Integer> minType);

    public List<OrderDayBillDO> getList(String fromTime);
    @Override
    boolean saveOrUpdate(OrderDayBillDO domain);

    public void removeDataByTime(LocalDateTime countDay);

    public void update(String fromTime, BigDecimal amount);
}
