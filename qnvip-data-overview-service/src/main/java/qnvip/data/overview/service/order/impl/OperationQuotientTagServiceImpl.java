package qnvip.data.overview.service.order.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OperationQuotientTagDO;
import qnvip.data.overview.domain.order.OperationQuotientTagDO;
import qnvip.data.overview.mapper.order.OperationQuotientTagMapper;
import qnvip.data.overview.service.order.OperationQuotientTagService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.List;

/**
 * Created by zhen<PERSON>ong on 2022-11-11
 */
@Slf4j
@Service
public class OperationQuotientTagServiceImpl extends BaseServiceImpl<OperationQuotientTagMapper, OperationQuotientTagDO> implements OperationQuotientTagService {

    @Autowired
    private OperationQuotientTagMapper mapper;

    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }


    @Override
    public List<OperationQuotientTagDO> getTagList(String time,
                                                Integer countType,
                                                List<Integer> miniTypeList,
                                                List<String> platformList,
                                                List<String> quotientList,
                                                List<String> sceneList,
                                                List<String> riskStrategyList,
                                                List<String> stage) {
        QueryWrapper<OperationQuotientTagDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("tag,risk_strategy").
                lambda().
                eq(OperationQuotientTagDO::getCountDay, time).
                eq(OperationQuotientTagDO::getCountType, countType).
                in(CollUtil.isNotEmpty(miniTypeList), OperationQuotientTagDO::getMiniType, miniTypeList).
                in(CollUtil.isNotEmpty(platformList), OperationQuotientTagDO::getPlatform, platformList).
                in(CollUtil.isNotEmpty(sceneList), OperationQuotientTagDO::getScene, sceneList).
                in(CollUtil.isNotEmpty(riskStrategyList), OperationQuotientTagDO::getRiskStrategy, riskStrategyList).
                in(CollUtil.isNotEmpty(quotientList), OperationQuotientTagDO::getQuotientName, quotientList);
        return list(queryWrapper);
    }

}
