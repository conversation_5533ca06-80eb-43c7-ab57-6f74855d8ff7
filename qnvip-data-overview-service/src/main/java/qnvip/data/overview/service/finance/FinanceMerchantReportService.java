package qnvip.data.overview.service.finance;

import qnvip.data.overview.domain.finance.FinanceMerchantReportDO;
import qnvip.rent.common.base.BaseService;

import java.util.List;

/**
 * Created by z<PERSON><PERSON>ong on 2022-10-17
 */
public interface FinanceMerchantReportService extends BaseService<FinanceMerchantReportDO> {

    List<FinanceMerchantReportDO> getList(String sTime, String eTime, String sParentTime, String eParentTime,
                                  List<Integer> miniTypeList, List<Integer> financeTypeList);

    void deleteAll();
}
