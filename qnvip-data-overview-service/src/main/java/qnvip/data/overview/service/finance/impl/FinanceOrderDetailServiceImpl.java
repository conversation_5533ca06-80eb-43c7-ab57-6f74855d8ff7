package qnvip.data.overview.service.finance.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceOrderDetailDO;
import qnvip.data.overview.mapper.finance.FinanceOrderDetailMapper;
import qnvip.data.overview.service.finance.FinanceOrderDetailService;
import qnvip.rent.common.base.BaseServiceImpl;

import java.util.ArrayList;
import java.util.List;

/**
 * create by gw on 2022/3/22
 */
@Service
@RequiredArgsConstructor
public class FinanceOrderDetailServiceImpl extends BaseServiceImpl<FinanceOrderDetailMapper, FinanceOrderDetailDO>
        implements FinanceOrderDetailService {


    private final FinanceOrderDetailMapper financeOrderDetailMapper;

    @Override
    public List<FinanceOrderDetailDO> getList(String sTime, String eTime,
                                              String sEndTime, String eEndTime,
                                              String sParentTime, String eParentTime,
                                              String sParentEndTime, String eParentEndTime,
                                              Integer businessType,
                                              List<Integer> miniTypeList, List<Integer> financeTypeList, Integer pageNo, Integer pageSize, Boolean renewFlag) {
        /**
         * 15:已签收 200:归还中 210:已归还 220:已买断 310:续租中 320:已续租 330:续租完成
         */
        List<Integer> EFFECTIVE_ORDER_STATUS = new ArrayList<>();
        String renewSql = " ";
        if (renewFlag) {
            // 续租
            renewSql = "parent_day is not null";
            EFFECTIVE_ORDER_STATUS = Lists.newArrayList(310, 330);
        } else {
            // 非续租
            renewSql = "parent_day is null";
            EFFECTIVE_ORDER_STATUS = Lists.newArrayList(15, 200, 210, 220, 320);
        }
        QueryWrapper<FinanceOrderDetailDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().ge(StringUtils.isNotBlank(sTime), FinanceOrderDetailDO::getRentStartDate, sTime)
                .le(StringUtils.isNotBlank(eTime), FinanceOrderDetailDO::getRentStartDate, eTime)
                .ge(StringUtils.isNotBlank(sEndTime), FinanceOrderDetailDO::getRentEndDate, sEndTime)
                .le(StringUtils.isNotBlank(eEndTime), FinanceOrderDetailDO::getRentEndDate, eEndTime)
                .ge(StringUtils.isNotBlank(sParentTime), FinanceOrderDetailDO::getParentDay, sParentTime)
                .le(StringUtils.isNotBlank(eParentTime), FinanceOrderDetailDO::getParentDay, eParentTime)
                .ge(StringUtils.isNotBlank(sParentEndTime), FinanceOrderDetailDO::getParentEndDate, sParentEndTime)
                .le(StringUtils.isNotBlank(eParentEndTime), FinanceOrderDetailDO::getParentEndDate, eParentEndTime)
                .eq(FinanceOrderDetailDO::getBusinessType, businessType)
                .in(FinanceOrderDetailDO::getOrderStatus, EFFECTIVE_ORDER_STATUS)
                .apply(renewSql) // 租赁子订单,和续租订单作区分
                .in(CollUtil.isNotEmpty(miniTypeList), FinanceOrderDetailDO::getMiniType, miniTypeList)
                .in(CollUtil.isNotEmpty(financeTypeList), FinanceOrderDetailDO::getFinanceType, financeTypeList)
                .orderByDesc(FinanceOrderDetailDO::getOrderNo);
        Page<FinanceOrderDetailDO> page = page(queryWrapper, pageNo, pageSize);
        if (page == null || CollUtil.isEmpty(page.getRecords())) {
            return Lists.newArrayList();
        }
        return page.getRecords();
    }


    @Override
    public void deleteAll() {
        financeOrderDetailMapper.deleteAll();
    }

}