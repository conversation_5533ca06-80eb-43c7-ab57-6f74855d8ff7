package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.OperateAccessSciDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON>gtong on 2022-01-10
 */
public interface OperateAccessSciService extends BaseService<OperateAccessSciDO> {
    public void removeDataByTime(LocalDateTime countDay);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<OperateAccessSciDO> getList(String fromTime, String toTime, Integer miniType);
}
