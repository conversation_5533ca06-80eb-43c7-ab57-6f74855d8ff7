package qnvip.data.overview.service.order;

import com.baomidou.dynamic.datasource.annotation.DS;
import qnvip.data.overview.domain.order.RentOrderDO;
import qnvip.data.overview.domain.risk.*;
import qnvip.data.overview.enums.StrategyEnum;
import qnvip.data.overview.param.job.JobParam;
import qnvip.rent.common.base.BaseService;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengtong on 2021-11-18
 *
 * <AUTHOR>
 */
public interface RentOrderService extends BaseService<RentOrderDO> {

    /**
     * 获取预测值
     *
     * @return
     */
    Map<String, Object> selectPredictedValue(StrategyEnum strategy);

    /**
     * 获取实际值
     *
     * @return
     */
    Map<String, Object> selectActualValue();


    public BigDecimal selectOrderUvCount();

    List<GeneralRecord> getAllGeneralData(Integer startPage,Integer pageSize,Integer overdue);

    Integer getGeneralCount();

    Integer getRenewCountTidb();

    List<GeneralRenewRecord> getAllGeneralRenewData(int startPage, Integer pageSize, Integer overdueDay);

    Integer getShCountTidb();

    List<ShRecordData> getShAllData(int startPage, Integer pageSize, Integer overdueDay);

    List<Map<String,String>> getGeneralCommonSql(String sql);

    List<ShOrderInfo> getShOrderInfo(String pageSql);

    List<RiskNewRentOrderInfoVo> getNewRentOrderInfo(String pageSql);

    List<RiskRentInfoVo> getRiskRentInfo(String sql);

    void batchSave(List<DataviewRiskMerchantOrderInfoDO> list);

    Integer getRenewCountDistributionTidb();

    List<GeneralRenewRecord> getAllDistributionRenewData(int id, Integer pageSize, Integer overdueDay);
}
