package qnvip.data.overview.service.whole.report;

import qnvip.data.overview.domain.mongo.ReportLogMongoDO;
import qnvip.data.overview.domain.whole.report.WholeReportCountDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * Created by zhengtong on 2023-02-13
 */
public interface WholeRentReportCountService extends BaseService<WholeReportCountDO> {

    /**
     * getList by condition
     *
     * @return
     */
    public List<WholeReportCountDO> getList(Integer countType, Integer model);


    <T> void saveWeekReport(String countDay,
                            Integer codemodel,
                            Integer type,
                            LocalDate day,
                            Map<String, WholeReportCountDO> map,
                            Map<String, T> key2enum);


    List<ReportLogMongoDO> getWeekList(Integer model, Integer type, String code);


    public void removeMongoDO(LocalDate time, Integer type, Integer model);

    public void removeMongoDO();


    void deleteByCondition(Integer type, Integer model);
}
