package qnvip.data.overview.service.goods;

import qnvip.data.overview.domain.goods.OperateGoodsParticularsDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;
/**
* Created by z<PERSON>gtong on 2021-12-20
*/
public interface OperateGoodsParticularsService extends BaseService<OperateGoodsParticularsDO>{
    public void removeDataByTime(LocalDateTime countDay);

    /**
    * getListByHour by condition
    *
    * @return
    */
    public List<OperateGoodsParticularsDO> getList(String fromTime, String toTime, Integer Type);
}
