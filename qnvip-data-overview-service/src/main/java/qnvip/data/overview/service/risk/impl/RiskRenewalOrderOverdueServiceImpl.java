package qnvip.data.overview.service.risk.impl;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RiskRenewalOrderOverdueDO;
import qnvip.data.overview.dto.RiskOverdueDTO;
import qnvip.data.overview.mapper.risk.RiskRenewalOrderOverdueMapper;
import qnvip.data.overview.service.risk.RiskRenewalOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.rent.common.base.BaseServiceImpl;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by z<PERSON><PERSON><PERSON> on 2022-05-24
 */
@Slf4j
@Service
public class RiskRenewalOrderOverdueServiceImpl extends BaseServiceImpl<RiskRenewalOrderOverdueMapper, RiskRenewalOrderOverdueDO> implements RiskRenewalOrderOverdueService {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Integer PAGE_SIZE = 5000;

    @Autowired
    private RiskRenewalOrderOverdueMapper mapper;


    @Override
    public Integer getCount(String fromTime,
                            String toTime,
                            List<Integer> miniTypeList,
                            Integer overdueDay,
                            Integer isSettle,
                            Integer renewWay) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda().eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                eq(Objects.nonNull(isSettle), RiskRenewalOrderOverdueDO::getIsSettle, isSettle).
                eq(Objects.nonNull(renewWay), RiskRenewalOrderOverdueDO::getRenewWay, renewWay).
                in(CollUtil.isNotEmpty(miniTypeList), RiskRenewalOrderOverdueDO::getMiniType, miniTypeList).
                ge(RiskRenewalOrderOverdueDO::getCountDay, fromTime).
                le(RiskRenewalOrderOverdueDO::getCountDay, toTime);

        return this.count(queryWrapper);
    }

    public Map<String, Object> getId(String fromTime,
                                     String toTime,
                                     List<Integer> miniTypeList,
                                     Integer overdueDay) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("max(id) maxId,min(id) minId").lambda().eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                in(CollUtil.isNotEmpty(miniTypeList), RiskRenewalOrderOverdueDO::getMiniType, miniTypeList).
                ge(RiskRenewalOrderOverdueDO::getCountDay, fromTime).
                le(RiskRenewalOrderOverdueDO::getCountDay, toTime);

        return this.getMap(queryWrapper);
    }

    @Override
    public List<RiskOverdueDTO> getList(String fromTime,
                                        String toTime,
                                        List<Integer> miniTypeList,
                                        Integer isSettle,
                                        Integer renewWay,
                                        Integer overdueDay,
                                        Integer startPage) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("date(count_day)                              count_day," +
                        "       risk_level," +
                        "       risk_strategy," +
                        "       audit_type," +
                        "       mini_type," +
                        "       platform," +
                        "       finance_type," +
                        "       bond_rate," +
                        "       rent_total                                   rent_total," +
                        "       max_day," +
                        "       if(is_overdue1 = 1, term1, 0)                not_pay_term1," +
                        "       if(is_overdue1 = 1, overdue_fine1, 0)        overdue_fine1," +
                        "       if(is_overdue1 = 1, bond_amt, 0)             bond_amt1," +
                        "       if(is_overdue1 = 1, before_discount, 0)      before_discount1," +
                        "       if(is_overdue1 = 1, discount_return_amt, 0)  discount_return_amt1," +
                        "       if(is_overdue1 = 1, 1, 0)                    count_term1," +
                        "       if(is_overdue2 = 1, term2, 0)                not_pay_term2," +
                        "       if(is_overdue2 = 1, overdue_fine2, 0)        overdue_fine2," +
                        "       if(is_overdue2 = 1, bond_amt, 0)             bond_amt2," +
                        "       if(is_overdue2 = 1, before_discount, 0)      before_discount2," +
                        "       if(is_overdue2 = 1, discount_return_amt, 0)  discount_return_amt2," +
                        "       if(is_overdue2 = 1, 1, 0)                    count_term2," +
                        "       if(is_overdue3 = 1, term3, 0)                not_pay_term3," +
                        "       if(is_overdue3 = 1, overdue_fine3, 0)        overdue_fine3," +
                        "       if(is_overdue3 = 1, bond_amt, 0)             bond_amt3," +
                        "       if(is_overdue3 = 1, before_discount, 0)      before_discount3," +
                        "       if(is_overdue3 = 1, discount_return_amt, 0)  discount_return_amt3," +
                        "       if(is_overdue3 = 1, 1, 0)                    count_term3," +
                        "       if(is_overdue4 = 1, term4, 0)                not_pay_term4," +
                        "       if(is_overdue4 = 1, overdue_fine4, 0)        overdue_fine4," +
                        "       if(is_overdue4 = 1, bond_amt, 0)             bond_amt4," +
                        "       if(is_overdue4 = 1, before_discount, 0)      before_discount4," +
                        "       if(is_overdue4 = 1, discount_return_amt, 0)  discount_return_amt4," +
                        "       if(is_overdue4 = 1, 1, 0)                    count_term4," +
                        "       if(is_overdue5 = 1, term5, 0)                not_pay_term5," +
                        "       if(is_overdue5 = 1, overdue_fine5, 0)        overdue_fine5," +
                        "       if(is_overdue5 = 1, bond_amt, 0)             bond_amt5," +
                        "       if(is_overdue5 = 1, before_discount, 0)      before_discount5," +
                        "       if(is_overdue5 = 1, discount_return_amt, 0)  discount_return_amt5," +
                        "       if(is_overdue5 = 1, 1, 0)                    count_term5," +
                        "       if(is_overdue6 = 1, term6, 0)                not_pay_term6," +
                        "       if(is_overdue6 = 1, overdue_fine6, 0)        overdue_fine6," +
                        "       if(is_overdue6 = 1, bond_amt, 0)             bond_amt6," +
                        "       if(is_overdue6 = 1, before_discount, 0)      before_discount6," +
                        "       if(is_overdue6 = 1, discount_return_amt, 0)  discount_return_amt6," +
                        "       if(is_overdue6 = 1, 1, 0)                    count_term6," +
                        "       if(is_overdue7 = 1, term7, 0)                not_pay_term7," +
                        "       if(is_overdue7 = 1, overdue_fine7, 0)        overdue_fine7," +
                        "       if(is_overdue7 = 1, bond_amt, 0)             bond_amt7," +
                        "       if(is_overdue7 = 1, before_discount, 0)      before_discount7," +
                        "       if(is_overdue7 = 1, discount_return_amt, 0)  discount_return_amt7," +
                        "       if(is_overdue7 = 1, 1, 0)                    count_term7," +
                        "       if(is_overdue8 = 1, term8, 0)                not_pay_term8," +
                        "       if(is_overdue8 = 1, overdue_fine8, 0)        overdue_fine8," +
                        "       if(is_overdue8 = 1, bond_amt, 0)             bond_amt8," +
                        "       if(is_overdue8 = 1, before_discount, 0)      before_discount8," +
                        "       if(is_overdue8 = 1, discount_return_amt, 0)  discount_return_amt8," +
                        "       if(is_overdue8 = 1, 1, 0)                    count_term8," +
                        "       if(is_overdue9 = 1, term9, 0)                not_pay_term9," +
                        "       if(is_overdue9 = 1, overdue_fine9, 0)        overdue_fine9," +
                        "       if(is_overdue9 = 1, bond_amt, 0)             bond_amt9," +
                        "       if(is_overdue9 = 1, before_discount, 0)      before_discount9," +
                        "       if(is_overdue9 = 1, discount_return_amt, 0)  discount_return_amt9," +
                        "       if(is_overdue9 = 1, 1, 0)                    count_term9," +
                        "       if(is_overdue10 = 1, term10, 0)              not_pay_term10," +
                        "       if(is_overdue10 = 1, overdue_fine10, 0)      overdue_fine10," +
                        "       if(is_overdue10 = 1, bond_amt, 0)            bond_amt10," +
                        "       if(is_overdue10 = 1, before_discount, 0)     before_discount10," +
                        "       if(is_overdue10 = 1, discount_return_amt, 0) discount_return_amt10," +
                        "       if(is_overdue10 = 1, 1, 0)                   count_term10," +
                        "       if(is_overdue11 = 1, term11, 0)              not_pay_term11," +
                        "       if(is_overdue11 = 1, 1, 0)                   count_term11," +
                        "       if(is_overdue11 = 1, overdue_fine11, 0)      overdue_fine11," +
                        "       if(is_overdue11 = 1, bond_amt, 0)            bond_amt11," +
                        "       if(is_overdue11 = 1, before_discount, 0)     before_discount11," +
                        "       if(is_overdue11 = 1, discount_return_amt, 0) discount_return_amt11," +
                        "       if(is_overdue12 = 1, term12, 0)              not_pay_term12," +
                        "       if(is_overdue12 = 1, overdue_fine12, 0)      overdue_fine12," +
                        "       if(is_overdue12 = 1, bond_amt, 0)            bond_amt12," +
                        "       if(is_overdue12 = 1, before_discount, 0)     before_discount12," +
                        "       if(is_overdue12 = 1, discount_return_amt, 0) discount_return_amt12," +
                        "       if(is_overdue12 = 1, 1, 0)                   count_term12," +
                        "       if(is_overdue13 = 1, term12, 0)              not_pay_term13," +
                        "       if(is_overdue13 = 1, overdue_fine12, 0)      overdue_fine13," +
                        "       if(is_overdue13 = 1, bond_amt, 0)            bond_amt13," +
                        "       if(is_overdue13 = 1, before_discount, 0)     before_discount13," +
                        "       if(is_overdue13 = 1, discount_return_amt, 0) discount_return_amt13," +
                        "       if(is_overdue13 = 1, 1, 0)                   count_term13," +
                        "       if(is_overdue14 = 1, term12, 0)              not_pay_term14," +
                        "       if(is_overdue14 = 1, overdue_fine12, 0)      overdue_fine14," +
                        "       if(is_overdue14 = 1, bond_amt, 0)            bond_amt14," +
                        "       if(is_overdue14 = 1, before_discount, 0)     before_discount14," +
                        "       if(is_overdue14 = 1, discount_return_amt, 0) discount_return_amt14," +
                        "       if(is_overdue14 = 1, 1, 0)                   count_term14," +
                        "       if(is_overdue15 = 1, term12, 0)              not_pay_term15," +
                        "       if(is_overdue15 = 1, overdue_fine12, 0)      overdue_fine15," +
                        "       if(is_overdue15 = 1, bond_amt, 0)            bond_amt15," +
                        "       if(is_overdue15 = 1, before_discount, 0)     before_discount15," +
                        "       if(is_overdue15 = 1, discount_return_amt, 0) discount_return_amt15," +
                        "       if(is_overdue15 = 1, 1, 0)                   count_term15," +
                        "       if(is_overdue16 = 1, term12, 0)              not_pay_term16," +
                        "       if(is_overdue16 = 1, overdue_fine12, 0)      overdue_fine16," +
                        "       if(is_overdue16 = 1, bond_amt, 0)            bond_amt16," +
                        "       if(is_overdue16 = 1, before_discount, 0)     before_discount16," +
                        "       if(is_overdue16 = 1, discount_return_amt, 0) discount_return_amt16," +
                        "       if(is_overdue16 = 1, 1, 0)                   count_term16," +
                        "       if(is_overdue17 = 1, term12, 0)              not_pay_term17," +
                        "       if(is_overdue17 = 1, overdue_fine12, 0)      overdue_fine17," +
                        "       if(is_overdue17 = 1, bond_amt, 0)            bond_amt17," +
                        "       if(is_overdue17 = 1, before_discount, 0)     before_discount17," +
                        "       if(is_overdue17 = 1, discount_return_amt, 0) discount_return_amt17," +
                        "       if(is_overdue17 = 1, 1, 0)                   count_term17," +
                        "       if(is_overdue18 = 1, term12, 0)              not_pay_term18," +
                        "       if(is_overdue18 = 1, overdue_fine12, 0)      overdue_fine18," +
                        "       if(is_overdue18 = 1, bond_amt, 0)            bond_amt18," +
                        "       if(is_overdue18 = 1, before_discount, 0)     before_discount18," +
                        "       if(is_overdue18 = 1, discount_return_amt, 0) discount_return_amt18," +
                        "       if(is_overdue18 = 1, 1, 0)                   count_term18"
                ).
                lambda().
                eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                eq(Objects.nonNull(isSettle), RiskRenewalOrderOverdueDO::getIsSettle, isSettle).
                eq(Objects.nonNull(renewWay), RiskRenewalOrderOverdueDO::getRenewWay, renewWay).
                eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                in(CollUtil.isNotEmpty(miniTypeList), RiskRenewalOrderOverdueDO::getMiniType, miniTypeList).
                ge(RiskRenewalOrderOverdueDO::getCountDay, fromTime).
                le(RiskRenewalOrderOverdueDO::getCountDay, toTime)
                .orderByDesc(RiskRenewalOrderOverdueDO::getId)
                .last("limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE);
        List<Map<String, Object>> mapList = listMaps(queryWrapper);
        return mapList.stream().map(e -> {
            RiskOverdueDTO riskOverdueDTO = new RiskOverdueDTO();
            String countDayStr = String.valueOf(e.get("count_day"));
            LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(countDayStr, dateFormatter), LocalTime.MIN);
            riskOverdueDTO.setCountDay(countDay);
            riskOverdueDTO.setMiniType(Integer.parseInt(String.valueOf(e.get("mini_type"))));
            riskOverdueDTO.setFinanceType(Integer.parseInt(String.valueOf(e.get("finance_type"))));
            riskOverdueDTO.setRiskLevel(String.valueOf(e.get("risk_level")));
            riskOverdueDTO.setRiskStrategy(String.valueOf(e.get("risk_strategy")));
            riskOverdueDTO.setPlatform(String.valueOf(e.get("platform")));
            riskOverdueDTO.setAuditType(String.valueOf(e.get("audit_type")));
            riskOverdueDTO.setRentTotal(CalculateUtil.toDecimal(e.get("rent_total")));
            riskOverdueDTO.setMaxTerm(Integer.parseInt(String.valueOf(e.get("max_day"))));
            riskOverdueDTO.setBondRate(Double.parseDouble(String.valueOf(e.get("bond_rate"))));
            riskOverdueDTO.setNotPayTerm1(CalculateUtil.toDecimal(e.get("not_pay_term1")));
            riskOverdueDTO.setOverdueFine1(CalculateUtil.toDecimal(e.get("overdue_fine1")));
            riskOverdueDTO.setCountTerm1(CalculateUtil.toDecimal(e.get("count_term1")));
            riskOverdueDTO.setNotPayTerm2(CalculateUtil.toDecimal(e.get("not_pay_term2")));
            riskOverdueDTO.setOverdueFine2(CalculateUtil.toDecimal(e.get("overdue_fine2")));
            riskOverdueDTO.setCountTerm2(CalculateUtil.toDecimal(e.get("count_term2")));
            riskOverdueDTO.setNotPayTerm3(CalculateUtil.toDecimal(e.get("not_pay_term3")));
            riskOverdueDTO.setOverdueFine3(CalculateUtil.toDecimal(e.get("overdue_fine3")));
            riskOverdueDTO.setCountTerm3(CalculateUtil.toDecimal(e.get("count_term3")));
            riskOverdueDTO.setNotPayTerm4(CalculateUtil.toDecimal(e.get("not_pay_term4")));
            riskOverdueDTO.setOverdueFine4(CalculateUtil.toDecimal(e.get("overdue_fine4")));
            riskOverdueDTO.setCountTerm4(CalculateUtil.toDecimal(e.get("count_term4")));
            riskOverdueDTO.setNotPayTerm5(CalculateUtil.toDecimal(e.get("not_pay_term5")));
            riskOverdueDTO.setOverdueFine5(CalculateUtil.toDecimal(e.get("overdue_fine5")));
            riskOverdueDTO.setCountTerm5(CalculateUtil.toDecimal(e.get("count_term5")));
            riskOverdueDTO.setNotPayTerm6(CalculateUtil.toDecimal(e.get("not_pay_term6")));
            riskOverdueDTO.setOverdueFine6(CalculateUtil.toDecimal(e.get("overdue_fine6")));
            riskOverdueDTO.setCountTerm6(CalculateUtil.toDecimal(e.get("count_term6")));
            riskOverdueDTO.setNotPayTerm7(CalculateUtil.toDecimal(e.get("not_pay_term7")));
            riskOverdueDTO.setOverdueFine7(CalculateUtil.toDecimal(e.get("overdue_fine7")));
            riskOverdueDTO.setCountTerm7(CalculateUtil.toDecimal(e.get("count_term7")));
            riskOverdueDTO.setNotPayTerm8(CalculateUtil.toDecimal(e.get("not_pay_term8")));
            riskOverdueDTO.setOverdueFine8(CalculateUtil.toDecimal(e.get("overdue_fine8")));
            riskOverdueDTO.setCountTerm8(CalculateUtil.toDecimal(e.get("count_term8")));
            riskOverdueDTO.setNotPayTerm9(CalculateUtil.toDecimal(e.get("not_pay_term9")));
            riskOverdueDTO.setOverdueFine9(CalculateUtil.toDecimal(e.get("overdue_fine9")));
            riskOverdueDTO.setCountTerm9(CalculateUtil.toDecimal(e.get("count_term9")));
            riskOverdueDTO.setNotPayTerm10(CalculateUtil.toDecimal(e.get("not_pay_term10")));
            riskOverdueDTO.setOverdueFine10(CalculateUtil.toDecimal(e.get("overdue_fine10")));
            riskOverdueDTO.setCountTerm10(CalculateUtil.toDecimal(e.get("count_term10")));
            riskOverdueDTO.setNotPayTerm11(CalculateUtil.toDecimal(e.get("not_pay_term11")));
            riskOverdueDTO.setOverdueFine11(CalculateUtil.toDecimal(e.get("overdue_fine11")));
            riskOverdueDTO.setCountTerm11(CalculateUtil.toDecimal(e.get("count_term11")));
            riskOverdueDTO.setNotPayTerm12(CalculateUtil.toDecimal(e.get("not_pay_term12")));
            riskOverdueDTO.setOverdueFine12(CalculateUtil.toDecimal(e.get("overdue_fine12")));
            riskOverdueDTO.setCountTerm12(CalculateUtil.toDecimal(e.get("count_term12")));

            riskOverdueDTO.setNotPayTerm13(CalculateUtil.toDecimal(e.get("not_pay_term13")));
            riskOverdueDTO.setOverdueFine13(CalculateUtil.toDecimal(e.get("overdue_fine13")));
            riskOverdueDTO.setCountTerm13(CalculateUtil.toDecimal(e.get("count_term13")));

            riskOverdueDTO.setNotPayTerm14(CalculateUtil.toDecimal(e.get("not_pay_term14")));
            riskOverdueDTO.setOverdueFine14(CalculateUtil.toDecimal(e.get("overdue_fine14")));
            riskOverdueDTO.setCountTerm14(CalculateUtil.toDecimal(e.get("count_term14")));

            riskOverdueDTO.setNotPayTerm15(CalculateUtil.toDecimal(e.get("not_pay_term15")));
            riskOverdueDTO.setOverdueFine15(CalculateUtil.toDecimal(e.get("overdue_fine15")));
            riskOverdueDTO.setCountTerm15(CalculateUtil.toDecimal(e.get("count_term15")));

            riskOverdueDTO.setNotPayTerm16(CalculateUtil.toDecimal(e.get("not_pay_term16")));
            riskOverdueDTO.setOverdueFine16(CalculateUtil.toDecimal(e.get("overdue_fine16")));
            riskOverdueDTO.setCountTerm16(CalculateUtil.toDecimal(e.get("count_term16")));

            riskOverdueDTO.setNotPayTerm17(CalculateUtil.toDecimal(e.get("not_pay_term17")));
            riskOverdueDTO.setOverdueFine17(CalculateUtil.toDecimal(e.get("overdue_fine17")));
            riskOverdueDTO.setCountTerm17(CalculateUtil.toDecimal(e.get("count_term17")));

            riskOverdueDTO.setNotPayTerm18(CalculateUtil.toDecimal(e.get("not_pay_term18")));
            riskOverdueDTO.setOverdueFine18(CalculateUtil.toDecimal(e.get("overdue_fine18")));
            riskOverdueDTO.setCountTerm18(CalculateUtil.toDecimal(e.get("count_term18")));


            riskOverdueDTO.setBondAmt1(CalculateUtil.toDecimal(e.get("bond_amt1")));
            riskOverdueDTO.setBeforeDiscount1(CalculateUtil.toDecimal(e.get("before_discount1")));
            riskOverdueDTO.setDiscountReturnAmt1(CalculateUtil.toDecimal(e.get("discount_return_amt1")));
            riskOverdueDTO.setBondAmt2(CalculateUtil.toDecimal(e.get("bond_amt2")));
            riskOverdueDTO.setBeforeDiscount2(CalculateUtil.toDecimal(e.get("before_discount2")));
            riskOverdueDTO.setDiscountReturnAmt2(CalculateUtil.toDecimal(e.get("discount_return_amt2")));
            riskOverdueDTO.setBondAmt3(CalculateUtil.toDecimal(e.get("bond_amt3")));
            riskOverdueDTO.setBeforeDiscount3(CalculateUtil.toDecimal(e.get("before_discount3")));
            riskOverdueDTO.setDiscountReturnAmt3(CalculateUtil.toDecimal(e.get("discount_return_amt3")));
            riskOverdueDTO.setBondAmt4(CalculateUtil.toDecimal(e.get("bond_amt4")));
            riskOverdueDTO.setBeforeDiscount4(CalculateUtil.toDecimal(e.get("before_discount4")));
            riskOverdueDTO.setDiscountReturnAmt4(CalculateUtil.toDecimal(e.get("discount_return_amt4")));
            riskOverdueDTO.setBondAmt5(CalculateUtil.toDecimal(e.get("bond_amt5")));
            riskOverdueDTO.setBeforeDiscount5(CalculateUtil.toDecimal(e.get("before_discount5")));
            riskOverdueDTO.setDiscountReturnAmt5(CalculateUtil.toDecimal(e.get("discount_return_amt5")));
            riskOverdueDTO.setBondAmt6(CalculateUtil.toDecimal(e.get("bond_amt6")));
            riskOverdueDTO.setBeforeDiscount6(CalculateUtil.toDecimal(e.get("before_discount6")));
            riskOverdueDTO.setDiscountReturnAmt6(CalculateUtil.toDecimal(e.get("discount_return_amt6")));
            riskOverdueDTO.setBondAmt7(CalculateUtil.toDecimal(e.get("bond_amt7")));
            riskOverdueDTO.setBeforeDiscount7(CalculateUtil.toDecimal(e.get("before_discount7")));
            riskOverdueDTO.setDiscountReturnAmt7(CalculateUtil.toDecimal(e.get("discount_return_amt7")));
            riskOverdueDTO.setBondAmt8(CalculateUtil.toDecimal(e.get("bond_amt8")));
            riskOverdueDTO.setBeforeDiscount8(CalculateUtil.toDecimal(e.get("before_discount8")));
            riskOverdueDTO.setDiscountReturnAmt8(CalculateUtil.toDecimal(e.get("discount_return_amt8")));
            riskOverdueDTO.setBondAmt9(CalculateUtil.toDecimal(e.get("bond_amt9")));
            riskOverdueDTO.setBeforeDiscount9(CalculateUtil.toDecimal(e.get("before_discount9")));
            riskOverdueDTO.setDiscountReturnAmt9(CalculateUtil.toDecimal(e.get("discount_return_amt9")));
            riskOverdueDTO.setBondAmt10(CalculateUtil.toDecimal(e.get("bond_amt10")));
            riskOverdueDTO.setBeforeDiscount10(CalculateUtil.toDecimal(e.get("before_discount10")));
            riskOverdueDTO.setDiscountReturnAmt10(CalculateUtil.toDecimal(e.get("discount_return_amt10")));
            riskOverdueDTO.setBondAmt11(CalculateUtil.toDecimal(e.get("bond_amt11")));
            riskOverdueDTO.setBeforeDiscount11(CalculateUtil.toDecimal(e.get("before_discount11")));
            riskOverdueDTO.setDiscountReturnAmt11(CalculateUtil.toDecimal(e.get("discount_return_amt11")));
            riskOverdueDTO.setBondAmt12(CalculateUtil.toDecimal(e.get("bond_amt12")));
            riskOverdueDTO.setBeforeDiscount12(CalculateUtil.toDecimal(e.get("before_discount12")));
            riskOverdueDTO.setDiscountReturnAmt12(CalculateUtil.toDecimal(e.get("discount_return_amt12")));

            riskOverdueDTO.setBondAmt13(CalculateUtil.toDecimal(e.get("bond_amt13")));
            riskOverdueDTO.setBeforeDiscount13(CalculateUtil.toDecimal(e.get("before_discount13")));
            riskOverdueDTO.setDiscountReturnAmt13(CalculateUtil.toDecimal(e.get("discount_return_amt13")));
            riskOverdueDTO.setBondAmt14(CalculateUtil.toDecimal(e.get("bond_amt14")));
            riskOverdueDTO.setBeforeDiscount14(CalculateUtil.toDecimal(e.get("before_discount14")));
            riskOverdueDTO.setDiscountReturnAmt14(CalculateUtil.toDecimal(e.get("discount_return_amt14")));
            riskOverdueDTO.setBondAmt15(CalculateUtil.toDecimal(e.get("bond_amt15")));
            riskOverdueDTO.setBeforeDiscount15(CalculateUtil.toDecimal(e.get("before_discount15")));
            riskOverdueDTO.setDiscountReturnAmt15(CalculateUtil.toDecimal(e.get("discount_return_amt15")));
            riskOverdueDTO.setBondAmt16(CalculateUtil.toDecimal(e.get("bond_amt16")));
            riskOverdueDTO.setBeforeDiscount16(CalculateUtil.toDecimal(e.get("before_discount16")));
            riskOverdueDTO.setDiscountReturnAmt16(CalculateUtil.toDecimal(e.get("discount_return_amt16")));
            riskOverdueDTO.setBondAmt17(CalculateUtil.toDecimal(e.get("bond_amt17")));
            riskOverdueDTO.setBeforeDiscount17(CalculateUtil.toDecimal(e.get("before_discount17")));
            riskOverdueDTO.setDiscountReturnAmt17(CalculateUtil.toDecimal(e.get("discount_return_amt17")));
            riskOverdueDTO.setBondAmt18(CalculateUtil.toDecimal(e.get("bond_amt18")));
            riskOverdueDTO.setBeforeDiscount18(CalculateUtil.toDecimal(e.get("before_discount18")));
            riskOverdueDTO.setDiscountReturnAmt18(CalculateUtil.toDecimal(e.get("discount_return_amt18")));
            return riskOverdueDTO;
        }).collect(Collectors.toList());
    }


    @Override
    public boolean saveOrUpdate(RiskRenewalOrderOverdueDO orderDO) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.lambda()
                .select(RiskRenewalOrderOverdueDO::getOrderId)
                .eq(RiskRenewalOrderOverdueDO::getOrderId, orderDO.getOrderId())
                .last("limit 1");
        int count = count(queryWrapper);
        if (count == 0) {
            save(orderDO);
        } else {
            UpdateWrapper<RiskRenewalOrderOverdueDO> up = new UpdateWrapper<>();
            up.lambda().eq(RiskRenewalOrderOverdueDO::getOrderId, orderDO.getOrderId());
            up.lambda().eq(RiskRenewalOrderOverdueDO::getOverdueDay, orderDO.getOverdueDay());
            update(orderDO, up);
        }
        return true;
    }

    @Override
    public void deleteAll() {
        mapper.deleteAll();
    }



    @Override
    public List<RiskRenewalOrderOverdueDO> getAuditType(String fromTime, String toTime, List<Integer> miniType, Integer overdueDay) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct audit_type, risk_level, risk_strategy, finance_type").
                lambda().
                eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                in(CollUtil.isNotEmpty(miniType), RiskRenewalOrderOverdueDO::getMiniType, miniType)
                .between(RiskRenewalOrderOverdueDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public List<RiskRenewalOrderOverdueDO> getBondRate(String fromTime, String toTime, List<Integer> miniType, Integer overdueDay) {
        QueryWrapper<RiskRenewalOrderOverdueDO> queryWrapper = new QueryWrapper<>();
        queryWrapper.
                select("distinct bond_rate").
                lambda().
                eq(RiskRenewalOrderOverdueDO::getOverdueDay, overdueDay).
                in(CollUtil.isNotEmpty(miniType), RiskRenewalOrderOverdueDO::getMiniType, miniType)
                .between(RiskRenewalOrderOverdueDO::getCountDay, fromTime, toTime);
        return list(queryWrapper);
    }

    @Override
    public void insertBatchSomeColumn(List<RiskRenewalOrderOverdueDO> list) {
        mapper.insertBatchSomeColumn(list);
    }
}
