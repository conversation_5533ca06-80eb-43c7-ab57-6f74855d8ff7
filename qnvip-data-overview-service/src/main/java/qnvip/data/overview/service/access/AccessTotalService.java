package qnvip.data.overview.service.access;

import qnvip.data.overview.domain.access.AccessTotalDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
 * Created by z<PERSON>gtong on 2021-10-13
 */
public interface AccessTotalService extends BaseService<AccessTotalDO> {
    /**
     * getOne by condition
     *
     * @return
     */
    public AccessTotalDO getOne(LocalDateTime Time);

    /**
     * getListByHour by condition
     *
     * @return
     */
    public List<AccessTotalDO> getList(LocalDateTime sTime,LocalDateTime eTime);
}
