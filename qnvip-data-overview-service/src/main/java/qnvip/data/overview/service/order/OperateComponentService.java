package qnvip.data.overview.service.order;

import qnvip.data.overview.domain.order.OperateComponentDO;
import qnvip.rent.common.base.BaseService;

import java.time.LocalDateTime;
import java.util.List;

/**
* Created by z<PERSON><PERSON>ong on 2021-11-23
*/
public interface OperateComponentService extends BaseService<OperateComponentDO>{

    public List<OperateComponentDO> getList(String fromTime, String toTime);

    public void removeDataByTime(LocalDateTime countDay);

}
