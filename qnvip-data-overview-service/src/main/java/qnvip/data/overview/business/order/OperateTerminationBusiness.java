package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateTerminationDO;
import qnvip.data.overview.domain.order.OperateTerminationDO;
import qnvip.data.overview.domain.order.OperateTerminationDO;
import qnvip.data.overview.service.order.OperateMarginService;
import qnvip.data.overview.service.order.OperateTerminationService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateTerminationBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateTerminationService terminationService;


    /**
     * 定时调度任务
     */
    public void runCore() {

        List<OperateTerminationDO> reasonCount = getReasonCount();
        // for (OperateTerminationDO operateTerminationDO : reasonCount) {
        //     terminationService.saveOrUpdate(operateTerminationDO);
        // }
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        terminationService.removeDataByTime(countDay);
        terminationService.saveBatch(reasonCount);
    }

    /**
     * 获取支付单数
     */
    private List<OperateTerminationDO> getReasonCount() {
        String sql =
                "select get_json_object(b.operate_note, '$.reason') as reason, count(1) as num," +
                        "       a.mini_type," +
                        "       a.merchant_id" +
                        " from rent_order a" +
                        "         left join rent_order_audit b on a.id = b.order_id" +
                        " where a.biz_type = 2" +
                        "  and a.termination = 5" +
                        "  and b.type = 1" +
                        "  and a.type = 1 " +
                        "  and a.merchant_id = 100 " +
                        "  and b.audit_status = 1" +
                        "  and get_json_object(b.operate_note, '$.reason') is not null" +
                        "  and a.is_deleted = 0" +
                        "   and a.ds = to_char(getdate(), 'yyyymmdd')" +
                        "   and b.ds = to_char(getdate(), 'yyyymmdd')" +
                        " and b.operate_time between ${fromTime} and ${toTime}" +
                        " group by get_json_object(b.operate_note, '$.reason'),a.mini_type,a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateTerminationDO domain = new OperateTerminationDO();
            String reason = record.getString("reason");
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderCount(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            domain.setReason(reason);
            domain.setCountDay(countDay);
            return domain;
        }).collect(Collectors.toList());
    }


}
