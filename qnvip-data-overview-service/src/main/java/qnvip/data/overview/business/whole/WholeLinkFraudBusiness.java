package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkFraudDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkFraudService;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkFraudBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkFraudService wholeLinkFraudService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkFraudBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkFraudDO> miniType2Map, WholeLinkFraudDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkFraudDO ac = new WholeLinkFraudDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkFraudDO> miniType2Map = new HashMap<>();

            CompletableFuture<List<WholeLinkFraudDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getHitValueByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFraudDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getHitValue(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFraudDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFraudDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFraudDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFraudDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);

            CompletableFuture.allOf(f3, f4, f5, f6, f7, f8).join();
            assembleValue(miniType2Map, f3.get(), "hitUv", "hitUvTop1", "hitUvTop2", "hitUvTop3", "hitUvTop4",
                    "hitUvTop5", "fraudAliUv", "fraudOtherUv", "fraudWechatUv");
            assembleValue(miniType2Map, f4.get(), "hitUv", "hitUvTop1", "hitUvTop2", "hitUvTop3", "hitUvTop4",
                    "hitUvTop5", "fraudAliUv", "fraudOtherUv", "fraudWechatUv");
            assembleValue(miniType2Map, f5.get(), "fraudTop5ChannelUv");
            assembleValue(miniType2Map, f6.get(), "fraudTop5ChannelUv");
            assembleValue(miniType2Map, f7.get(), "fraudTop5SceneUv");
            assembleValue(miniType2Map, f8.get(), "fraudTop5SceneUv");
            List<WholeLinkFraudDO> list = Lists.newArrayList(miniType2Map.values());
            LocalDate now = LocalDate.now();
            wholeLinkFraudService.removeByHour(hour, now);
            wholeLinkFraudService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkFraudDO> miniType2Map,
                               List<WholeLinkFraudDO> list,
                               String... fields) throws Exception {

        for (WholeLinkFraudDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkFraudDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 获取命中分数
     */
    private List<WholeLinkFraudDO> getHitValueByMiniType(String ds, Integer hour) {
        String sql = "SELECT" +
                "          ro.mini_type," +
                "          count(DISTINCT if(cl.opinionAmount <> '', idcardNo, null)) as                          hit_uv," +
                "          COUNT(DISTINCT if(rs.ruleCode = 'R5097' and rs.result = 5, idcardNo, null))               " +
                "   hit_uv_top1," +
                "             COUNT(DISTINCT if(rs.ruleCode = '8R018' and rs.result = 5, idcardNo, null))               " +
                "   hit_uv_top2," +
                "             COUNT(DISTINCT if(rs.ruleCode = '8R029' and rs.result = 5, idcardNo, null))               " +
                "   hit_uv_top3," +
                "             COUNT(DISTINCT if(rs.ruleCode = 'R4152' and rs.result = 5, idcardNo, null))               " +
                "   hit_uv_top4," +
                "             COUNT(DISTINCT if(rs.ruleCode = 'R5096' and rs.result = 5, idcardNo, null))               " +
                "   hit_uv_top5," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel in (11, 14, 25, 27, 29), " +
                " idcardNo, null)) fraud_ali_uv," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel = 23, idcardNo, null)) fraud_wechat_uv," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel in (1, 15), idcardNo, " +
                " null)) fraud_other_uv" +
                "   FROM cl_loan cl" +
                "            left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left JOIN rent_order ro ON ro.no = cl.loanno" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left JOIN rc_risk_access_rule_result_2023 rs ON rs.serialnoId = sn.id" +
                "            left JOIN rc_risk_strategy_rule_set s ON s.id = rs.ruleid" +
                "   WHERE cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)" +
                "     and cl.parentNo = ''" +
                "     and cl.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and rs.ds = " + ds +
                "     and s.ds = " + ds +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long hitUv = Long.parseLong(record.getString("hit_uv"));
            Long hitUvTop1 = Long.parseLong(record.getString("hit_uv_top1"));
            Long hitUvTop2 = Long.parseLong(record.getString("hit_uv_top2"));
            Long hitUvTop3 = Long.parseLong(record.getString("hit_uv_top3"));
            Long hitUvTop4 = Long.parseLong(record.getString("hit_uv_top4"));
            Long hitUvTop5 = Long.parseLong(record.getString("hit_uv_top5"));
            Long fraudWechatUv = Long.parseLong(record.getString("fraud_wechat_uv"));
            Long fraudAliUv = Long.parseLong(record.getString("fraud_ali_uv"));
            Long fraudOtherUv = Long.parseLong(record.getString("fraud_other_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setHitUv(hitUv);
            domain.setHitUvTop1(hitUvTop1);
            domain.setHitUvTop2(hitUvTop2);
            domain.setHitUvTop3(hitUvTop3);
            domain.setHitUvTop4(hitUvTop4);
            domain.setHitUvTop5(hitUvTop5);
            domain.setFraudAliUv(fraudAliUv);
            domain.setFraudOtherUv(fraudOtherUv);
            domain.setFraudWechatUv(fraudWechatUv);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取命中分数
     */
    private List<WholeLinkFraudDO> getHitValue(String ds, Integer hour) {
        String sql = "SELECT" +
                "          count(DISTINCT if(cl.opinionAmount <> '', idcardNo, null)) as                          hit_uv," +
                "          COUNT(DISTINCT if(rs.ruleCode = 'R5097' and rs.result = 5, idcardNo, null))               " +
                "hit_uv_top1," +
                "          COUNT(DISTINCT if(rs.ruleCode = '8R018' and rs.result = 5, idcardNo, null))               " +
                "hit_uv_top2," +
                "          COUNT(DISTINCT if(rs.ruleCode = '8R029' and rs.result = 5, idcardNo, null))               " +
                "hit_uv_top3," +
                "          COUNT(DISTINCT if(rs.ruleCode = 'R4152' and rs.result = 5, idcardNo, null))               " +
                "hit_uv_top4," +
                "          COUNT(DISTINCT if(rs.ruleCode = 'R5096' and rs.result = 5, idcardNo, null))               " +
                "hit_uv_top5," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel in (11, 14, 25, 27, 29), " +
                " idcardNo, null)) fraud_ali_uv," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel = 23, idcardNo, null)) fraud_wechat_uv," +
                "          count(DISTINCT if(cl.opinionAmount <> '' and cl.businesschannel in (1, 15), idcardNo, " +
                " null)) fraud_other_uv" +
                "   FROM cl_loan cl" +
                "            left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left JOIN rent_order ro ON ro.no = cl.loanno" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left JOIN rc_risk_access_rule_result_2023 rs ON rs.serialnoId = sn.id" +
                "            left JOIN rc_risk_strategy_rule_set s ON s.id = rs.ruleid" +
                "   WHERE cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)" +
                "     and cl.parentNo = ''" +
                "     and cl.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and rs.ds = " + ds +
                "     and s.ds = " + ds +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "  ;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long hitUv = Long.parseLong(record.getString("hit_uv"));
            Long hitUvTop1 = Long.parseLong(record.getString("hit_uv_top1"));
            Long hitUvTop2 = Long.parseLong(record.getString("hit_uv_top2"));
            Long hitUvTop3 = Long.parseLong(record.getString("hit_uv_top3"));
            Long hitUvTop4 = Long.parseLong(record.getString("hit_uv_top4"));
            Long hitUvTop5 = Long.parseLong(record.getString("hit_uv_top5"));
            Long fraudWechatUv = Long.parseLong(record.getString("fraud_wechat_uv"));
            Long fraudAliUv = Long.parseLong(record.getString("fraud_ali_uv"));
            Long fraudOtherUv = Long.parseLong(record.getString("fraud_other_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setHitUv(hitUv);
            domain.setHitUvTop1(hitUvTop1);
            domain.setHitUvTop2(hitUvTop2);
            domain.setHitUvTop3(hitUvTop3);
            domain.setHitUvTop4(hitUvTop4);
            domain.setHitUvTop5(hitUvTop5);
            domain.setFraudAliUv(fraudAliUv);
            domain.setFraudOtherUv(fraudOtherUv);
            domain.setFraudWechatUv(fraudWechatUv);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * top渠道
     */
    private List<WholeLinkFraudDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and sn.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.opinionAmount<>'' then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setFraudTop5ChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkFraudDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and sn.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.opinionAmount<>'' then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setFraudTop5ChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkFraudDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and sn.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.opinionAmount<>'' then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setFraudTop5SceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkFraudDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and sn.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.opinionAmount<>'' then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.opinionAmount<>'' then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFraudDO domain = new WholeLinkFraudDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setFraudTop5SceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }
}



