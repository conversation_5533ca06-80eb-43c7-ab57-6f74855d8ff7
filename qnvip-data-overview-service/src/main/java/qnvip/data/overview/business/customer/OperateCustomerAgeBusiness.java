package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerAgeDO;
import qnvip.data.overview.enums.CustomerGenderEnum;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerAgeService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerAgeBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerAgeService ageService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            ageService.removeDataByTime(countDay);
            placeTheOrder();
            riskPass();
            onPay();
            signIn();
        } catch (Exception e) {
            log.error("OperateCustomerAgeBusiness runCore error :{}", e.getMessage());
        }
    }


    /**
     * 下单口径
     */
    private void placeTheOrder() {
        String sql = "select b.gender," +
                "       b.age," +
                "       b.mini_type," +
                "       count(*) num" +
                " from (select (year(now()) - substr(b.id_card_no, 7, 4)) age," +
                "             b.gender," +
                "             a.mini_type" +
                "      from rent_order a" +
                "               inner join rent_customer b on a.customer_id = b.id" +
                "      where a.is_deleted = 0" +
                "        and a.type = 1" +
                "        and a.parent_id = 0" +
                "        and a.merchant_id = 100" +
                "        and b.gender is not null" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and a.create_time between ${fromTime} and ${toTime}" +
                "     ) b" +
                " group by b.mini_type, b.age, b.gender";

        assemble(sql, CustomerTypeEnum.PLACE_THE_ORDER.getTypeCode());
    }

    /**
     * 通审口径
     */
    private void riskPass() {
        String sql = "select b.gender," +
                "       b.age," +
                "       b.mini_type," +
                "       count(*) num" +
                " from (select (year(now()) - substr(b.id_card_no, 7, 4)) age," +
                "             b.gender," +
                "             a.mini_type" +
                "      from rent_order a" +
                "               inner join rent_customer b on a.customer_id = b.id" +
                "               inner join rent_order_audit roa on a.id = roa.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.type = 1" +
                "        and a.merchant_id = 100" +
                "        and roa.type = 2" +
                "        and roa.audit_status = 1" +
                "        and a.parent_id = 0" +
                "        and b.gender is not null" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and roa.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and roa.operate_time between ${fromTime} and ${toTime}" +
                "     ) b" +
                " group by b.mini_type, b.age, b.gender";

        assemble(sql, CustomerTypeEnum.RISK_PASS.getTypeCode());
    }

    /**
     * 支付
     */
    private void onPay() {
        String sql = "select b.gender," +
                "       b.age," +
                "       b.mini_type," +
                "       count(*) num" +
                " from (select (year(now()) - substr(b.id_card_no, 7, 4)) age," +
                "             b.gender," +
                "             a.mini_type" +
                "      from rent_order a" +
                "               inner join rent_customer b on a.customer_id = b.id" +
                "               inner join rent_order_audit roa on a.id = roa.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.type = 1" +
                "        and a.merchant_id = 100" +
                "        and roa.type = 2" +
                "        and roa.audit_status = 1" +
                "        and a.parent_id = 0" +
                "        and b.gender is not null" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and roa.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and a.payment_time between ${fromTime} and ${toTime}" +
                "     ) b" +
                " group by b.mini_type, b.age, b.gender";

        assemble(sql, CustomerTypeEnum.ON_PAY.getTypeCode());
    }

    /**
     * 签收
     */
    private void signIn() {
        String sql = "select b.gender," +
                "       b.age," +
                "       b.mini_type," +
                "       count(*) num" +
                " from (select (year(now()) - substr(b.id_card_no, 7, 4)) age," +
                "             b.gender," +
                "             a.mini_type" +
                "      from rent_order a" +
                "               inner join rent_customer b on a.customer_id = b.id" +
                "               inner join rent_order_logistics rol on a.id = rol.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.type = 1" +
                "        and a.merchant_id = 100" +
                "        and a.parent_id = 0" +
                "        and b.gender is not null" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and rol.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and rol.sign_time between ${fromTime} and ${toTime}" +
                "     ) b" +
                " group by b.mini_type, b.age, b.gender";

        assemble(sql, CustomerTypeEnum.SIGN_IN.getTypeCode());
    }


    private void assemble(String sql, Integer type) {

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OperateCustomerAgeDO> collect = records.stream().map(record -> {
            OperateCustomerAgeDO domain = new OperateCustomerAgeDO();
            String miniType = record.getString("mini_type");
            String gender = record.getString("gender");
            String age = record.getString("age");
            String num = record.getString("num");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setType(type);
            domain.setCount(Long.valueOf(num));
            domain.setGender(CustomerGenderEnum.getValueByCode(Integer.parseInt(gender)));
            domain.setAge(Double.valueOf(age));
            domain.setCountDay(countDay);
            return domain;
        }).collect(Collectors.toList());
        ageService.saveBatch(collect);
    }

}
