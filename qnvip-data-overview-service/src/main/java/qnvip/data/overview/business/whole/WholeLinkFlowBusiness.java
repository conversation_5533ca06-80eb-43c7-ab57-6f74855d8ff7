package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.business.whole.report.WholeLawReportBusiness;
import qnvip.data.overview.domain.whole.WholeLinkFlowDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.enums.SceneConfigEnum;
import qnvip.data.overview.service.whole.WholeLinkFlowService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkFlowBusiness {


    private final OdpsUtil odpsUtil;
    private final WholeLinkFlowService wholeLinkFlowService;
    private final String keyTemplate = "%s-%s-%s-%s";

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkFlowBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkFlowDO> miniType2Map, WholeLinkFlowDO domain) {
        String key = String.format(keyTemplate, domain.getCountDay(), domain.getCountHour(), domain.getMiniType()
                , domain.getScene());
        if (!miniType2Map.containsKey(key)) {
            WholeLinkFlowDO ac = new WholeLinkFlowDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            ac.setScene(domain.getScene());
            ac.setPlatform(domain.getPlatform());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkFlowDO> miniType2Map = new HashMap<>();
            String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);

            CompletableFuture<List<WholeLinkFlowDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getDiscountUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f2 =
                    CompletableFuture.supplyAsync(() -> getDiscountUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getUnLossUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getUnLossUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getNewUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getNewUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getValidUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getValidUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f9 =
                    CompletableFuture.supplyAsync(() -> getRepeatVisitUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f10 =
                    CompletableFuture.supplyAsync(() -> getRepeatVisitUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f11 =
                    CompletableFuture.supplyAsync(() -> getActiveUvByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f12 =
                    CompletableFuture.supplyAsync(() -> getActiveUv(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f13 =
                    CompletableFuture.supplyAsync(() -> get8mNoOrder(ds, hour, time), threadPoolExecutor);
            CompletableFuture<List<WholeLinkFlowDO>> f14 =
                    CompletableFuture.supplyAsync(() -> get8mNoOrderByMiniType(ds, hour, time), threadPoolExecutor);
            CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14).join();

            assembleValue(miniType2Map, f1.get(), "discountUv", "wholeUv");
            assembleValue(miniType2Map, f2.get(), "discountUv", "wholeUv");
            assembleValue(miniType2Map, f3.get(), "unLossUv");
            assembleValue(miniType2Map, f4.get(), "unLossUv");
            assembleValue(miniType2Map, f5.get(), "newUv");
            assembleValue(miniType2Map, f6.get(), "newUv");
            assembleValue(miniType2Map, f7.get(), "validMessageUv", "top5RegionalUv", "maleUv", "validGenderUv",
                    "validAgeUv", "validUv", "validUvH1", "validUvH2");
            assembleValue(miniType2Map, f8.get(), "validMessageUv", "top5RegionalUv", "maleUv", "validGenderUv",
                    "validAgeUv", "validUv", "validUvH1", "validUvH2");
            assembleValue(miniType2Map, f9.get(), "repeatVisitUv");
            assembleValue(miniType2Map, f10.get(), "repeatVisitUv");
            assembleValue(miniType2Map, f11.get(), "activeUv");
            assembleValue(miniType2Map, f12.get(), "activeUv");
            assembleValue(miniType2Map, f13.get(), "unOrderUv");
            assembleValue(miniType2Map, f14.get(), "unOrderUv");

            List<WholeLinkFlowDO> list = Lists.newArrayList(miniType2Map.values());
            List<WholeLinkFlowDO> collect = list.stream().peek(o -> {

                Long h1 = o.getValidUvH1();
                Long h2 = o.getValidUvH2();
                // （h1/h2 -1)
                BigDecimal h1Rate = CalculateUtil.sub(CalculateUtil.div(h1, h2), 1);
                o.setH1Rate(h1Rate);

                Long discountUv = o.getDiscountUv();
                Long unLossUv = o.getUnLossUv();
                // （未跳失）/全量
                BigDecimal lossUv = CalculateUtil.div(unLossUv, discountUv);
                o.setLossRate(lossUv);

                Long validMessageUv = o.getValidMessageUv();
                Long top5Uv = o.getTop5RegionalUv();
                BigDecimal top5RegionalRate = CalculateUtil.div(top5Uv, validMessageUv);
                o.setTop5RegionalRate(top5RegionalRate);

                Long validUV = o.getValidUv();
                Long validAgeUv = o.getValidAgeUv();
                o.setValidAgeRate(CalculateUtil.div(validAgeUv, validUV));

                Long newUv = o.getNewUv();
                o.setNewUvRate(CalculateUtil.div(newUv, validUV));

                Long repeatVisitUv = o.getRepeatVisitUv();
                o.setRepeatVisitRate(CalculateUtil.div(repeatVisitUv, validUV));

                Long activeUv = o.getActiveUv();
                o.setActiveRate(CalculateUtil.div(activeUv, validUV));

                Long validGenderUv = o.getValidGenderUv();
                Long maleUv = o.getMaleUv();
                o.setMaleRate(CalculateUtil.div(maleUv, validGenderUv));
            }).collect(Collectors.toList());
            LocalDate now = DateUtils.stringToLocateDate(time);
            wholeLinkFlowService.removeByHour(hour, now);
            wholeLinkFlowService.saveBatch(collect);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkFlowDO> miniType2Map,
                               List<WholeLinkFlowDO> list,
                               String... fields) throws Exception {
        for (WholeLinkFlowDO flowDO : list) {
            String key = String.format(keyTemplate, flowDO.getCountDay(), flowDO.getCountHour(), flowDO.getMiniType()
                    , flowDO.getScene());
            initMap(miniType2Map, flowDO);
            WholeLinkFlowDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = flowDO.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                Object source = declaredField.get(core);
                Object target = field.get(flowDO);
                BigDecimal add = CalculateUtil.add(source, target);
                declaredField.set(core, add.longValue());
            }
        }
    }

    /**
     * todo instr(ro.quotient_id, '_', 1, 1) - 1 业务那边可能改了规则,导致这部分逻辑不对
     * 用于体现全量的UV,扣除刷量后的UV，主要体现自然流量
     */
    private List<WholeLinkFlowDO> getDiscountUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(ro.report_time) time," +
                "          count(distinct ro.customer_third_id) uv," +
                "          ro.mini_type," +
                "          ro.scene," +
                "          count(distinct ro.customer_third_id) -" +
                "          count(distinct" +
                "                if(substr(ro.quotient_id, 1, instr(ro.quotient_id, '_', 1, 1) - 1) in(10000532,10000664), ro.customer_third_id," +
                "                   null))      discount_uv" +
                "   from dataview_track_enter_applets ro" +
                "            left join rent_quotient_scene rqs on substr(ro.quotient_id, instr(ro.quotient_id, '_') + 1) = rqs.scene" +
                "   where ro.ds = " + ds +
                "     and rqs.ds = " + ds +
                "     and hour(ro.report_time) <=" + hour +
                "     and date(ro.report_time) ='" + time + "' " +
                "   group by date(ro.report_time),ro.mini_type,ro.scene" +
                "   order by date(ro.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long uv = Long.parseLong(record.getString("uv"));
            Long discountUv = Long.parseLong(record.getString("discount_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setWholeUv(uv);
            flowDO.setDiscountUv(discountUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }


    /**
     * 用于体现全量的UV,扣除刷量后的UV，主要体现自然流量
     */
    private List<WholeLinkFlowDO> getDiscountUv(String ds, Integer hour, String time) {
        String sql = "select date(ro.report_time) time," +
                "          count(distinct ro.customer_third_id) uv," +
                "          count(distinct ro.customer_third_id) -" +
                "          count(distinct" +
                "                if(substr(ro.quotient_id, 1, instr(ro.quotient_id, '_', 1, 1) - 1) in(10000532,10000664), ro.customer_third_id," +
                "                   null))      discount_uv" +
                "   from dataview_track_enter_applets ro" +
                "            left join rent_quotient_scene rqs on substr(ro.quotient_id, instr(ro.quotient_id, '_') + 1) = rqs.scene" +
                "   where ro.ds = " + ds +
                "     and rqs.ds = " + ds +
                "     and date(ro.report_time) ='" + time + "' " +
                "     and hour(ro.report_time) <=" + hour +
                "   group by date(ro.report_time)" +
                "   order by date(ro.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long uv = Long.parseLong(record.getString("uv"));
            Long discountUv = Long.parseLong(record.getString("discount_uv"));
            flowDO.setWholeUv(uv);
            flowDO.setDiscountUv(discountUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 非跳失UV
     */
    private List<WholeLinkFlowDO> getUnLossUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(ro.report_time)                  time," +
                "           ro.mini_type," +
                "           ro.scene," +
                "          count(distinct if(ro.action_type = 2, ro.customer_third_id, null)) -" +
                "          count(distinct" +
                "                if(ro.action_type = 2 and substr(ro.quotient_id, 1, instr(ro.quotient_id, '_', 1, 1) - 1) in(10000532,10000664)," +
                "                   ro.customer_third_id, null)) un_loss_uv" +
                "   from dataview_track_enter_applets ro" +
                "            left join rent_quotient_scene rqs on substr(ro.quotient_id, instr(ro.quotient_id, '_') + 1) = rqs.scene" +
                "   where ro.ds = " + ds +
                "     and rqs.ds = " + ds +
                "     and date(ro.report_time) ='" + time + "' " +
                "     and hour(ro.report_time) <=" + hour +
                "   group by date(ro.report_time),ro.mini_type,ro.scene" +
                "   order by date(ro.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unLossUv = Long.parseLong(record.getString("un_loss_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));
            flowDO.setUnLossUv(unLossUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 非跳失UV
     */
    private List<WholeLinkFlowDO> getUnLossUv(String ds, Integer hour, String time) {
        String sql = "select date(ro.report_time)                  time," +
                "          count(distinct if(ro.action_type = 2, ro.customer_third_id, null)) -" +
                "          count(distinct" +
                "                if(ro.action_type = 2 and substr(ro.quotient_id, 1, instr(ro.quotient_id, '_', 1, 1) - 1) in(10000532,10000664)," +
                "                   ro.customer_third_id, null)) un_loss_uv" +
                "   from dataview_track_enter_applets ro" +
                "            left join rent_quotient_scene rqs on substr(ro.quotient_id, instr(ro.quotient_id, '_') + 1) = rqs.scene" +
                "   where ro.ds = " + ds +
                "     and rqs.ds = " + ds +
                "     and hour(ro.report_time) <=" + hour +
                "     and date(ro.report_time) ='" + time + "' " +
                "   group by date(ro.report_time)" +
                "   order by date(ro.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unLossUv = Long.parseLong(record.getString("un_loss_uv"));
            flowDO.setUnLossUv(unLossUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 新用户
     */
    private List<WholeLinkFlowDO> getNewUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time, count(distinct rc.id) new_uv," +
                "          dtpa.scene, dtpa.mini_type" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "            left join dataview_track_new_access dtna on dtpa.customer_third_id = dtna.customer_third_id" +
                "   where dtpa.ds = " + ds +
                "     and dtna.ds = " + ds +
                "     and rc.ds = " + ds +
                "     and dtna.customer_third_id is not null" +
                "     and rc.mobile is not null" +
                "     and hour(dtpa.report_time) <=" + hour +
                "     and date(dtpa.report_time) ='" + time + "' " +
                "   group by date(dtpa.report_time),dtpa.scene,dtpa.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long newUv = Long.parseLong(record.getString("new_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setNewUv(newUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 新用户
     */
    private List<WholeLinkFlowDO> getNewUv(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time, count(distinct rc.id) new_uv" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "            left join dataview_track_new_access dtna on dtpa.customer_third_id = dtna.customer_third_id" +
                "   where dtpa.ds = " + ds +
                "     and dtna.ds = " + ds +
                "     and rc.ds = " + ds +
                "     and dtna.customer_third_id is not null" +
                "     and rc.mobile is not null" +
                "     and hour(dtpa.report_time) <=" + hour +
                "     and date(dtpa.report_time) ='" + time + "' " +
                "   group by date(dtpa.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long newUv = Long.parseLong(record.getString("new_uv"));
            flowDO.setNewUv(newUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 有效UV,有效年龄,男性用户,地域top比
     */
    private List<WholeLinkFlowDO> getValidUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time)                                                                    " +
                "                            time,dtpa.scene,mini_type," +
                "                count(distinct (case when rc.gender is not null then  rc.id else null end))  gender_valid_uv," +
                "             count(distinct (case when rc.mobile is not null then  rc.id else null end))      valid_uv," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and hour(dtpa.report_time) <= " + (hour - 1) +
                "                      then rc.id else null end) )                                                                             valid_uv_h1," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and hour(dtpa.report_time) <=" + (hour - 2) +
                "                      then  rc.id else null end))                                                                              valid_uv_h2," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and rc.message_city is not null" +
                "                      then  rc.id else null end))                                                                              valid_message_uv," +
                "             count(distinct (case" +
                "                  when rc.gender is not null and  rc.gender = 1" +
                "                      then  rc.id else null end))                                                     male_uv," +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and substr(rc.birthday, 1, 4) between '1979' and '1996' then  rc.id else null end)) valid_age_uv," +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and rc.message_city is not null and rc.message_city in ('长沙市', '成都市', '徐州市', '深圳市', '广州市')" +
                "                  then  rc.id else null end))                                                                                          top5_regional_uv" +
                "      from dataview_track_enter_applets dtpa" +
                "               left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "      where dtpa.ds = " + ds +
                "        and rc.ds = " + ds +
                "        and date(dtpa.report_time) ='" + time + "' " +
                "        and hour(dtpa.report_time) <= " + hour +
                "      group by date(dtpa.report_time),dtpa.scene,mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long validUv = stringToLong(record.getString("valid_uv"));
            Long genderValidUv = stringToLong(record.getString("gender_valid_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Long validAgeUv = stringToLong(record.getString("valid_age_uv"));
            Long maleUv = stringToLong(record.getString("male_uv"));
            Long top5RegionalUv = stringToLong(record.getString("top5_regional_uv"));
            Long validMessageUv = stringToLong(record.getString("valid_message_uv"));
            Long validUvH1 = stringToLong(record.getString("valid_uv_h1"));
            Long validUvH2 = stringToLong(record.getString("valid_uv_h2"));

            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setValidUvH1(validUvH1);
            flowDO.setValidUvH2(validUvH2);
            flowDO.setValidMessageUv(validMessageUv);
            flowDO.setTop5RegionalUv(top5RegionalUv);
            flowDO.setValidAgeUv(validAgeUv);
            flowDO.setValidUv(validUv);
            flowDO.setValidGenderUv(genderValidUv);
            flowDO.setMaleUv(maleUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 有效UV,有效年龄,男性用户,地域top比
     */
    private List<WholeLinkFlowDO> getValidUv(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time)                                                                                                time," +
                "                count(distinct (case when rc.gender is not null then  rc.id else null end))  gender_valid_uv," +
                "             count(distinct (case when rc.mobile is not null then  rc.id else null end))      valid_uv," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and hour(dtpa.report_time) <= " + (hour - 1) +
                "                      then rc.id else null end) )                                                                             valid_uv_h1," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and hour(dtpa.report_time) <=" + (hour - 2) +
                "                      then  rc.id else null end))                                                                              valid_uv_h2," +
                "" +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and rc.message_city is not null" +
                "                      then  rc.id else null end))                                                                              valid_message_uv," +
                "             count(distinct (case" +
                "                  when rc.gender is not null and  rc.gender = 1" +
                "                      then  rc.id else null end))                                                     male_uv," +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and substr(rc.birthday, 1, 4) between '1979' and '1996' then  rc.id else null end)) valid_age_uv," +
                "             count(distinct (case" +
                "                  when rc.mobile is not null and rc.message_city is not null and rc.message_city in ('长沙市', '成都市', '徐州市', '深圳市', '广州市')" +
                "                  then  rc.id else null end))                                                                                          top5_regional_uv" +
                "      from dataview_track_enter_applets dtpa" +
                "               left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "      where dtpa.ds = " + ds +
                "        and rc.ds = " + ds +
                "        and date(dtpa.report_time) ='" + time + "' " +
                "        and hour(dtpa.report_time) <= " + hour +
                "      group by date(dtpa.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long validUv = stringToLong(record.getString("valid_uv"));
            Long genderValidUv = stringToLong(record.getString("gender_valid_uv"));
            Long validAgeUv = stringToLong(record.getString("valid_age_uv"));
            Long maleUv = stringToLong(record.getString("male_uv"));
            Long top5RegionalUv = stringToLong(record.getString("top5_regional_uv"));
            Long validMessageUv = stringToLong(record.getString("valid_message_uv"));
            Long validUvH1 = stringToLong(record.getString("valid_uv_h1"));
            Long validUvH2 = stringToLong(record.getString("valid_uv_h2"));
            flowDO.setValidAgeUv(validAgeUv);
            flowDO.setValidUv(validUv);
            flowDO.setValidGenderUv(genderValidUv);
            flowDO.setValidMessageUv(validMessageUv);
            flowDO.setTop5RegionalUv(top5RegionalUv);
            flowDO.setValidUvH1(validUvH1);
            flowDO.setValidUvH2(validUvH2);
            flowDO.setMaleUv(maleUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 复访用户
     */
    private List<WholeLinkFlowDO> getRepeatVisitUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time,dtpa.scene, " +
                "   mini_type," +
                "       count(distinct rc.id) repeat_visit_uv" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "   where dtpa.ds = " + ds +
                "     and rc.ds = " + ds +
                "     and rc.mobile is not null" +
                "     and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "     and rc.id in (select rc.id" +
                "                   from dataview_track_enter_applets dtpa" +
                "                            left join rent_customer rc" +
                "                                      on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "                   where dtpa.ds = " + ds +
                "                     and rc.ds = " + ds +
                "                     and date(dtpa.report_time) between date_sub('" + time + "'" + ", 35) and date_sub('" + time + "'" + ", 30)" +
                "                   group by rc.id)" +
                "   group by date(dtpa.report_time),dtpa.scene,mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long repeatVisitUv = Long.parseLong(record.getString("repeat_visit_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setRepeatVisitUv(repeatVisitUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 复访用户
     */
    private List<WholeLinkFlowDO> getRepeatVisitUv(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time, " +
                "       count(distinct rc.id) repeat_visit_uv" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "   where dtpa.ds = " + ds +
                "     and rc.ds = " + ds +
                "     and rc.mobile is not null" +
                "     and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "     and rc.id in (select rc.id" +
                "                   from dataview_track_enter_applets dtpa" +
                "                            left join rent_customer rc" +
                "                                      on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "                   where dtpa.ds = " + ds +
                "                     and rc.ds = " + ds +
                "                     and date(dtpa.report_time) between date_sub('" + time + "'" + ", 35) and date_sub('" + time + "'" + ", 30)" +
                "                   group by rc.id)" +
                "   group by date(dtpa.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long repeatVisitUv = Long.parseLong(record.getString("repeat_visit_uv"));
            flowDO.setRepeatVisitUv(repeatVisitUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 活跃用户
     */
    private List<WholeLinkFlowDO> getActiveUvByMiniType(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time,dtpa.scene, " +
                "   mini_type," +
                "       count(distinct rc.id) active_uv" +
                " from dataview_track_enter_applets dtpa" +
                "         left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                " where dtpa.ds = " + ds +
                "  and rc.ds = " + ds +
                "  and rc.mobile is not null" +
                "  and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "  and rc.id in (select rc.id" +
                "                from dataview_track_enter_applets dtpa" +
                "                         left join rent_customer rc" +
                "                                   on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "                where dtpa.ds = " + ds +
                "                  and rc.ds = " + ds +
                "                     and date(dtpa.report_time) between date_sub('" + time + "'" + ", 2)" +
                " and date_sub('" + time + "'" + ", 2)" +
                "                group by rc.id)" +
                " group by date(dtpa.report_time),dtpa.scene,mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long activeUv = Long.parseLong(record.getString("active_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setActiveUv(activeUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 活跃用户
     */
    private List<WholeLinkFlowDO> getActiveUv(String ds, Integer hour, String time) {
        String sql = "select date(dtpa.report_time) time, " +
                "       count(distinct rc.id) active_uv" +
                " from dataview_track_enter_applets dtpa" +
                "         left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                " where dtpa.ds = " + ds +
                "  and rc.ds = " + ds +
                "  and rc.mobile is not null" +
                "  and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "  and rc.id in (select rc.id" +
                "                from dataview_track_enter_applets dtpa" +
                "                         left join rent_customer rc" +
                "                                   on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "                where dtpa.ds = " + ds +
                "                  and rc.ds = " + ds +
                "                     and date(dtpa.report_time) between date_sub('" + time + "'" + ", 2)" +
                " and date_sub('" + time + "'" + ", 2)" +
                "                group by rc.id)" +
                " group by date(dtpa.report_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long activeUv = Long.parseLong(record.getString("active_uv"));
            flowDO.setActiveUv(activeUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }


    /**
     * 8分钟未下单用户
     */
    private List<WholeLinkFlowDO> get8mNoOrderByMiniType(String ds, Integer hour, String time) {
        String sql = "select time,scene,mini_type,count(distinct a.id) un_order_uv" +
                " from(" +
                "    select rc.id ,dtpa.scene,date(dtpa.report_time) time, dtpa.mini_type,sum(dtpa.keep_alive_time)/60000 " +
                " keeptime" +
                "    from dataview_track_enter_applets dtpa" +
                "    left join rent_customer rc on get_json_object(rc.bindinfo,'$[0].miniUserId') = dtpa.customer_third_id" +
                "    where dtpa.ds = " + ds +
                "    and rc.ds = " + ds +
                "    and rc.mobile is not null" +
                "    and dtpa.action_type = 2" +
                "    and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "    and rc.id not in (select rc.id" +
                "                    from dataview_track_enter_applets dtpa" +
                "                    left join rent_customer rc on get_json_object(rc.bindinfo,'$[0].miniUserId') = dtpa.customer_third_id" +
                "                    left join rent_order ro on ro.customer_id = rc.id" +
                "                    where dtpa.ds =" + ds +
                "                    and rc.ds =  " + ds +
                "                    and ro.ds = " + ds +
                "                    and rc.mobile is not null" +
                "                    and dtpa.action_type = 2" +
                "                    and ro.termination = 1" +
                "                    and date(dtpa.report_time) ='" + time + "' " +
                "    group by rc.id" +
                "    having sum(dtpa.keep_alive_time)/60000 >= 8" +
                "    order by sum(dtpa.keep_alive_time)/60000 desc)" +
                "    group by    dtpa.mini_type,dtpa.scene,rc.id,dtpa.report_time) a" +
                " group by mini_type,scene,time;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unOrderUv = Long.parseLong(record.getString("un_order_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            flowDO.setScene(SceneConfigEnum.SceneTypeEnum.getValueByCode(miniType, record.getString("scene")));

            flowDO.setUnOrderUv(unOrderUv);
            flowDO.setMiniType(miniType);
            flowDO.setPlatform(MiniTypeEnum.getPlatformByMiniType(miniType));
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    /**
     * 8分钟未下单用户
     */
    private List<WholeLinkFlowDO> get8mNoOrder(String ds, Integer hour, String time) {
        String sql = "select time,count(distinct a.id) un_order_uv" +
                " from(" +
                "    select rc.id ,date(dtpa.report_time) time, sum(dtpa.keep_alive_time)/60000 keeptime" +
                "    from dataview_track_enter_applets dtpa" +
                "    left join rent_customer rc on get_json_object(rc.bindinfo,'$[0].miniUserId') = dtpa.customer_third_id" +
                "    where dtpa.ds = " + ds +
                "    and rc.ds = " + ds +
                "    and rc.mobile is not null" +
                "    and dtpa.action_type = 2" +
                "    and date(dtpa.report_time) ='" + time + "' " +
                "     and hour(dtpa.report_time) <=" + hour +
                "    and rc.id not in (select rc.id" +
                "                    from dataview_track_enter_applets dtpa" +
                "                    left join rent_customer rc on get_json_object(rc.bindinfo,'$[0].miniUserId') = dtpa.customer_third_id" +
                "                    left join rent_order ro on ro.customer_id = rc.id" +
                "                    where dtpa.ds =  " + ds +
                "                    and rc.ds = " + ds +
                "                    and ro.ds = " + ds +
                "                    and rc.mobile is not null" +
                "                    and dtpa.action_type = 2" +
                "                    and ro.termination = 1" +
                "                    and date(dtpa.report_time) ='" + time + "' " +
                "    group by rc.id" +
                "    having sum(dtpa.keep_alive_time)/60000 >= 8" +
                "    order by sum(dtpa.keep_alive_time)/60000 desc) " +
                "    group by    dtpa.mini_type,rc.id,dtpa.report_time) a" +
                " group by time;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkFlowDO flowDO = new WholeLinkFlowDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unOrderUv = Long.parseLong(record.getString("un_order_uv"));
            flowDO.setUnOrderUv(unOrderUv);
            flowDO.setMiniType(MiniTypeEnum.TOTAl.getCode());
            flowDO.setCountDay(localDate);
            flowDO.setCountHour(hour);
            return flowDO;
        }).collect(Collectors.toList());
    }

    private Long stringToLong(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return Long.parseLong(val);
    }
}



