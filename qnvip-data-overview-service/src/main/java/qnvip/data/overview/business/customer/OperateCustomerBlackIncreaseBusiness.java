package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerBlackIndustryDO;
import qnvip.data.overview.domain.customer.OperateCustomerBlackIndustryDO;
import qnvip.data.overview.enums.CustomerGenderEnum;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerBlackIndustryService;
import qnvip.data.overview.service.customer.impl.OperateCustomerBlackIndustryServiceImpl;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerBlackIncreaseBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerBlackIndustryService blackIndustryService;

    /**
     * 定时调度任务
     */
    public Boolean runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        blackIndustryService.removeDataByTime(countDay);
        CompletableFuture<Boolean> f1 = CompletableFuture.supplyAsync(this::placeAnOrder);
        CompletableFuture<Boolean> f2 = CompletableFuture.supplyAsync(this::payOrder);
        CompletableFuture.allOf(f1,f2).join();
        try {
            return f1.get().equals(f2.get());
        } catch (Exception e) {
            return false;
        }
    }


    /**
     * 黑产增长情况，下单
     */
    private Boolean placeAnOrder() {
        String sql = "select c.receiver_mobile," +
                "       c.receiver_address," +
                "       c.receiver_name," +
                "       c.receiver_province," +
                "       c.receiver_city," +
                "       count(a.id)       order_count," +
                "       count(b.id)       uv," +
                "       sum(d.rent_total) gmv" +
                " from rent_order a" +
                "         inner join rent_customer b" +
                "                    on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                "         inner join rent_order_finance_detail d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.ds = to_char(getdate(), 'yyyymmdd')" +
                " group by c.receiver_mobile, c.receiver_address, c.receiver_name, c.receiver_province,c.receiver_city" +
                " having count(a.id) >= 2" +
                " order by  count(a.id) desc limit 500";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return true;
        }
        List<OperateCustomerBlackIndustryDO> collect = records.stream().map(record -> {
            OperateCustomerBlackIndustryDO domain = new OperateCustomerBlackIndustryDO();
            String receiverMobile = record.getString("receiver_mobile");
            String receiverAddress = record.getString("receiver_address");
            String receiverCity = record.getString("receiver_city");
            String receiverProvince = record.getString("receiver_province");
            String receiverName = record.getString("receiver_name");
            String orderCount = record.getString("order_count");
            String gmv = record.getString("gmv");
            String uv = record.getString("uv");
            domain.setName(receiverName);
            domain.setContact(receiverMobile);
            domain.setProvince(receiverProvince);
            domain.setCity(receiverCity);
            domain.setAddress(receiverProvince.concat(receiverCity).concat(receiverAddress));
            domain.setOrderCount(Long.parseLong(orderCount));
            domain.setUv(Long.parseLong(uv));
            domain.setGmv(new BigDecimal(gmv));
            domain.setCountDay(countDay);
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
        return blackIndustryService.saveBatch(collect);
    }

    /**
     * 黑产增长情况，付款
     */
    private Boolean payOrder() {
        String sql = "select c.receiver_mobile," +
                "       c.receiver_address," +
                "       c.receiver_name," +
                "       c.receiver_province," +
                "       c.receiver_city," +
                "       count(a.id)       order_count," +
                "       count(b.id)       uv," +
                "       sum(d.rent_total) gmv" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                "         inner join rent_order_finance_detail d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.status in (1, 5, 15, 60)" +
                "  and a.payment_time is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.ds = to_char(getdate(), 'yyyymmdd')" +
                " group by c.receiver_mobile, c.receiver_address, c.receiver_name, c.receiver_province,c.receiver_city" +
                " having count(a.id) >= 2" +
                " order by  count(a.id) desc limit 500;";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return true;
        }
        List<OperateCustomerBlackIndustryDO> collect = records.stream().map(record -> {
            OperateCustomerBlackIndustryDO domain = new OperateCustomerBlackIndustryDO();
            String receiverMobile = record.getString("receiver_mobile");
            String receiverAddress = record.getString("receiver_address");
            String receiverName = record.getString("receiver_name");
            String orderCount = record.getString("order_count");
            String receiverCity = record.getString("receiver_city");
            String receiverProvince = record.getString("receiver_province");
            String gmv = record.getString("gmv");
            String uv = record.getString("uv");
            domain.setName(receiverName);
            domain.setContact(receiverMobile);
            domain.setProvince(receiverProvince);
            domain.setAddress(receiverProvince.concat(receiverCity).concat(receiverAddress));
            domain.setOrderCount(Long.parseLong(orderCount));
            domain.setUv(Long.parseLong(uv));
            domain.setGmv(new BigDecimal(gmv));
            domain.setCountDay(countDay);
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());
        return blackIndustryService.saveBatch(collect);
    }
}
