package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPassQualityDO;
import qnvip.data.overview.domain.dataindicators.IndicatorRiskRefuseCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorRiskRefuseCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/2/14
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorRiskRefuseCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorRiskRefuseCustomerService indicatorRiskRefuseCustomerService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorRiskRefuseCustomerDO> collectMap = new HashMap<>();
        countRhExecRefuseCount(ds, sTime, eTime, collectMap);
        countLtNhRegisterCount(ds, sTime, eTime, collectMap);
        countRegisterNotOrder(ds, sTime, eTime, collectMap);
        countRiskApprovedNotPay(ds, sTime, eTime, collectMap);
        countServiceCount(ds, sTime, eTime, false, collectMap);
        countServiceCount(ds, sTime, eTime, true, collectMap);
        countLtNdRefusedCount(ds, sTime, eTime, collectMap);
        List<IndicatorRiskRefuseCustomerDO> list = new ArrayList<>(collectMap.values());
        if (CollUtil.isNotEmpty(list)) {
            indicatorRiskRefuseCustomerService.saveOrUpdateBatch(list, 2000);
        }
        // for (IndicatorRiskRefuseCustomerDO value : collectMap.values()) {
        //     //实际主动支付=主动支付-电销支付
        //     indicatorRiskRefuseCustomerService.saveOrUpdate(value);
        // }
    }


    /**
     * 人行成功执行人数中，风控拒绝人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRhExecRefuseCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select  mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rent_order_audit d on d.order_id = a.id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and c.rhtime is not null and c.rhtime != '' " +
                "  and d.type = 2   " +
                "  and d.audit_status=2   " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                " group by mini_type,to_char(a.create_time,'yyyy-mm-dd');";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRhExecRefuseCount(count);
        }
    }


    /**
     * 风控拒绝人数中N小时内注册人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                        inner join rent_customer c " +
                "                                   on get_json_object(c.bindinfo, '$[0].miniUserId') = a" +
                ".customer_third_id " +
                "                        inner join ( " +
                "                           select customer_id, to_char(create_time, 'yyyy-mm-dd') day, mini_type " +
                "                           from ( " +
                "                                    select a.customer_id, a.mini_type, a.create_time " +
                "                                    from    rent_order a inner   " +
                "                                    join    rent_order_audit b   " +
                "                                    on      a.id = b.order_id   " +
                "                                    where   a.is_deleted = 0   " +
                "                                    and     a.merchant_id = 100   " +
                "                                    and     a.parent_id = 0   " +
                "                                    and     a.type = 1   " +
                "                                    and     a.biz_type = 2   " +
                "                                    and     a.create_time between '" + sTime + "' and " +
                "'" + eTime + "' " +
                "                                    and     b.type = 2   " +
                "                                    and     b.audit_status=2   " +
                "                                    and     a.ds = " + ds +
                "                                    and     b.ds = " + ds +
                "                                    order by a.create_time " +
                "                                ) a " +
                "                           group by a.customer_id  " +
                "                                 ,a.mini_type   " +
                "                                 ,to_char(a.create_time,'yyyy-mm-dd') " +
                "                       ) b on a.mini_type = b.mini_type and c.id = b.customer_id " +
                "                           and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "                       where a.action_type = 1 " +
                "                         and a.ds = " + ds +
                "                         and c.ds = " + ds +
                "                         and a.report_time between '" + sTime + "' " +
                "                           and '" + eTime + "' " +
                "                       order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                " where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(count);
        }
    }


    /**
     * 风控拒绝人数中,已注册未下单人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRegisterNotOrder(String ds, String sTime, String eTime,
                                       Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct a.customer_id) " +
                "count " +
                "from ( " +
                "         select a.mini_type, a.customer_id, min(a.create_time) create_time " +
                "         from rent_order a " +
                "                  left join rent_order_audit b " +
                "                            on a.id = b.order_id " +
                "         where a.is_deleted = 0 " +
                "           and a.parent_id = 0 " +
                "           and a.type = 1 " +
                "           and a.merchant_id = 100 " +
                "           and a.biz_type = 2 " +
                "           and a.create_time between '" + sTime + "' " +
                "           and '" + eTime + "' " +
                "           and b.type = 2   " +
                "           and b.audit_status=2  " +
                "           and a.ds = " + ds +
                "           and b.ds = " + ds +
                "         group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "                , a.mini_type, a.customer_id " +
                ") a inner join rent_customer b on b.id = a.customer_id " +
                "    inner join ( " +
                "        select min(create_time) create_time,customer_id from ( " +
                "            select customer_id,create_time from rent_order where customer_id in ( " +
                "                select customer_id " +
                "                    from rent_order  " +
                "                    where is_deleted = 0 " +
                "                    and merchant_id=100 " +
                "                    and parent_id = 0 " +
                "                    and type = 1 " +
                "                    and biz_type = 2 " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "' " +
                "                    and ds = " + ds +
                "            ) " +
                "            and ds = " + ds + " order by create_time desc " +
                "        ) group by customer_id " +
                "    ) c on a.customer_id = c.customer_id and to_char(a.create_time,'yyyy-mm-dd') =  to_char(c" +
                ".create_time,'yyyy-mm-dd') " +
                "    where DATEDIFF(a.create_time,b.create_time)>1 and b.ds= " + ds +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterNotOrder(count);
        }
    }


    /**
     * 风控拒绝人数中N小时内注册人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRiskApprovedNotPay(String ds, String sTime, String eTime,
                                         Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select  A.day " +
                "        ,A.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  x.mini_type " +
                "                    ,to_char(x.create_time, 'yyyy-mm-dd') as day " +
                "                    ,x.customer_id " +
                "            from    rent_order x inner " +
                "            join    cl_loan y " +
                "            on      x.no = y.loanno " +
                "            inner join    rent_order_audit b   " +
                "            on      x.id = b.order_id  " +
                "            inner join ( " +
                "                           select  min(risktime) risktime " +
                "                                   ,customer_id " +
                "                           from    ( " +
                "                                       select  b.risktime " +
                "                                               ,a.customer_id " +
                "                                       from    rent_order a inner " +
                "                                       join    cl_loan b " +
                "                                       on      a.no = b.loanno " +
                "                                       where   a.is_deleted = 0 " +
                "                                       and     a.merchant_id = 100 " +
                "                                       and     a.parent_id = 0 " +
                "                                       and     a.type = 1 " +
                "                                       and     a.biz_type = 2 " +
                "                                       and     b.riskstatus = 20 " +
                "                                       and     a.payment_time is null " +
                "                                       and     a.ds = " + ds +
                "                                       and     b.ds = " + ds +
                "                                       order by b.risktime asc " +
                "                                   )  " +
                "                           group by customer_id " +
                "                       ) D " +
                "            on      x.customer_id = D.customer_id " +
                "            where   x.is_deleted = 0 " +
                "            and     x.merchant_id = 100 " +
                "            and     x.parent_id = 0 " +
                "            and     x.type = 1 " +
                "            and     x.biz_type = 2 " +
                "            and     x.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     b.type = 2   " +
                "            and     b.audit_status=2  " +
                "            and     y.riskstatus = 15 " +
                "            and     x.ds = " + ds +
                "            and     y.ds = " + ds +
                "            and     b.ds = " + ds +
                "            and     datediff(x.create_time,D.risktime) >  " + +IndicatorBeforeSaleBusiness.N_DAYS +
                "            group by x.mini_type " +
                "                     ,to_char(x.create_time, 'yyyy-mm-dd') " +
                "                     ,x.customer_id " +
                "        ) A " +
                "group by A.mini_type " +
                "         ,A.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskApprovedNotPay(count);
        }
    }


    /**
     * 风控拒绝人数中，服务中客户数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countServiceCount(String ds, String sTime, String eTime, Boolean inServiceFlag,
                                   Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select  mini_type " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "        ,count(distinct a.customer_id) count " +
                "from    ( " +
                "            select  mini_type " +
                "                    ,max(a.create_time) create_time " +
                "                    ,a.customer_id " +
                "            from    rent_order a " +
                "            left join rent_order_audit b " +
                "            on      a.id = b.order_id " +
                "            where   a.is_deleted = 0 " +
                "            and     a.parent_id = 0 " +
                "            and     a.type = 1 " +
                "            and     a.merchant_id = 100 " +
                "            and     a.biz_type = 2 " +
                "            and     a.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     b.type = 2 " +
                "            and     b.audit_status = 2 " +
                "            and     a.ds = " + ds +
                "            and     b.ds = " + ds +
                "            group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "                     ,a.mini_type,a.customer_id " +
                "        ) a inner " +
                "join    ( " +
                "            select  min(create_time) create_time " +
                "                    ,max(termination) termination " +
                "                    ,customer_id " +
                "                    ,max(status) status " +
                "            from    ( " +
                "                        select  termination " +
                "                                ,customer_id " +
                "                                ,create_time " +
                "                                ,status " +
                "                        from    rent_order " +
                "                        where   customer_id in ( select customer_id from rent_order where is_deleted" +
                " = 0 and merchant_id = 100 and parent_id = 0 and type = 1 and biz_type = 2 and payment_time is not " +
                "null and ds = " + ds + " ) " +
                "                        and     ds = " + ds +
                "                        order by create_time asc " +
                "                    )  " +
                "            group by customer_id " +
                "        ) c " +
                "on      a.customer_id = c.customer_id " +
                "and     to_char(a.create_time,'yyyy-mm-dd') = to_char(c.create_time,'yyyy-mm-dd') " +
                "where   c.termination != 5 " +
                "        and c.status " + (inServiceFlag ? "not" : "") + " in (220,330)" +
                "group by mini_type " +
                "         ,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (inServiceFlag) {
                updateDO.setInServiceCount(count);
            } else {
                updateDO.setOutServiceCount(count);
            }
        }
    }


    /**
     * 风控拒绝人数中，N天内被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNdRefusedCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorRiskRefuseCustomerDO> collectMap) {
        String sql = " select  a.mini_type " +
                "    ,to_char(e.create_time,'yyyy-mm-dd') day   " +
                "    ,count(distinct a.customer_id) count   " +
                "from  rent_order a  " +
                "inner join rent_order_audit b on a.id = b.order_id   " +
                "inner join ( " +
                "    select max(id) id,max(create_time) create_time,customer_id from ( " +
                "        select id,customer_id,create_time from rent_order where create_time between '" + sTime + "' " +
                " " +
                "        and '" + eTime + "' and ds = " + ds + " order by id asc " +
                "    ) group by customer_id,to_char(create_time,'yyyy-mm-dd') " +
                ") d on a.customer_id = d.customer_id and to_char(d.create_time,'yyyy-mm-dd') = to_char(a" +
                ".create_time,'yyyy-mm-dd') and a.id != d.id " +
                "inner join ( " +
                "    select max(create_time) create_time,customer_id from ( " +
                "        select customer_id,create_time from rent_order where create_time between '" + sTime + "'  " +
                "        and '" + eTime + "' and ds = " + ds + " order by id asc " +
                "    ) group by customer_id,to_char(create_time,'yyyy-mm-dd') " +
                ") e on a.customer_id = e.customer_id and to_char(e.create_time,'yyyy-mm-dd') = to_char(a" +
                ".create_time,'yyyy-mm-dd') " +
                "where   a.is_deleted = 0   " +
                "    and     a.merchant_id = 100   " +
                "    and     a.parent_id = 0   " +
                "    and     a.type = 1   " +
                "    and     a.biz_type = 2   " +
                "    and     b.type = 2   " +
                "    and     b.audit_status=2   " +
                "    and datediff(e.create_time, b.operate_time) < " + IndicatorBeforeSaleBusiness.N_DAYS +
                "    and     a.ds = " + ds +
                "    and     b.ds = " + ds +
                "    group by a.mini_type   " +
                "     ,to_char(e.create_time,'yyyy-mm-dd') ; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorRiskRefuseCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(count);
        }
    }


    private void initMap(Map<String, IndicatorRiskRefuseCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorRiskRefuseCustomerDO ivd = new IndicatorRiskRefuseCustomerDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setRhExecRefuseCount(0);
            ivd.setLtNhRegisterCount(0);
            ivd.setRegisterNotOrder(0);
            ivd.setRiskApprovedNotPay(0);
            ivd.setInServiceCount(0);
            ivd.setOutServiceCount(0);
            ivd.setLtNdRefusedCount(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}