package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkOrderDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkOrderService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkOrderBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkOrderService wholeLinkOrderService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkOrderBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkOrderDO> miniType2Map, WholeLinkOrderDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkOrderDO ac = new WholeLinkOrderDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkOrderDO> miniType2Map = new HashMap<>();

            CompletableFuture<List<WholeLinkOrderDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getOrderCntByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f2 =
                    CompletableFuture.supplyAsync(() -> getOrderCnt(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getNewUserByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getNewUser(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f9 =
                    CompletableFuture.supplyAsync(() -> getUnAuditCloseUvMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkOrderDO>> f10 =
                    CompletableFuture.supplyAsync(() -> getUnAuditCloseUv(ds, hour), threadPoolExecutor);

            CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10).join();

            assembleValue(miniType2Map, f1.get(), "orderUv", "orderCnt", "unDepositUv", "topSearchUv",
                    "noSceneUv", "orderIphoneOrderCnt", "orderIphone13pmOrderCnt", "orderTwelveOrderCnt"
                    , "orderIphoneOrderRate", "orderIphone13pmOrderRate", "orderTwelveOrderRate"
                    , "unDepositUvRate", "topSearchUvRate", "noSceneUvRate");
            assembleValue(miniType2Map, f2.get(), "orderUv", "orderCnt", "unDepositUv", "topSearchUv",
                    "noSceneUv", "orderIphoneOrderCnt", "orderIphone13pmOrderCnt", "orderTwelveOrderCnt"
                    , "orderIphoneOrderRate", "orderIphone13pmOrderRate", "orderTwelveOrderRate"
                    , "unDepositUvRate", "topSearchUvRate", "noSceneUvRate");
            assembleValue(miniType2Map, f3.get(), "newOrderUv");
            assembleValue(miniType2Map, f4.get(), "newOrderUv");
            assembleValue(miniType2Map, f5.get(), "orderTop5ChannelUv");
            assembleValue(miniType2Map, f6.get(), "orderTop5ChannelUv");
            assembleValue(miniType2Map, f7.get(), "orderTop5SceneUv");
            assembleValue(miniType2Map, f8.get(), "orderTop5SceneUv");
            assembleValue(miniType2Map, f9.get(), "unAuditCloseUv");
            assembleValue(miniType2Map, f10.get(), "unAuditCloseUv");

            List<WholeLinkOrderDO> list = Lists.newArrayList(miniType2Map.values());
            List<WholeLinkOrderDO> collect = list.stream().peek(o -> {

                Long orderUv = o.getOrderUv();
                Long newOrderUv = o.getNewOrderUv();
                Long orderTop5ChannelUv = o.getOrderTop5ChannelUv();
                Long orderTop5SceneUv = o.getOrderTop5SceneUv();
                o.setNewOrderUvRate(CalculateUtil.div(newOrderUv, orderUv));
                o.setOrderTop5ChannelUvRate(CalculateUtil.div(orderTop5ChannelUv, orderUv));
                o.setOrderTop5SceneUvRate(CalculateUtil.div(orderTop5SceneUv, orderUv));

            }).collect(Collectors.toList());
            LocalDate now = LocalDate.now();
            wholeLinkOrderService.removeByHour(hour, now);
            wholeLinkOrderService.saveBatch(collect);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkOrderDO> miniType2Map,
                               List<WholeLinkOrderDO> list,
                               String... fields) throws Exception {

        for (WholeLinkOrderDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkOrderDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 获取下单量
     */
    private List<WholeLinkOrderDO> getOrderCntByMiniType(String ds, Integer hour) {
        String sql = "select date(ro.create_time)                                                                             time," +
                "          ro.mini_type," +
                "          count(distinct ro.id)                                                                            order_cnt," +
                "          count(distinct ro.customer_id)                                                                   order_uv," +
                "          count(distinct if(rofd.bond_free_status != 0, ro.customer_id, null))                             un_deposit_uv," +
                "          count(distinct (case when ro.mini_type in (1, 4, 5, 7, 9) and ro.scene = '1005' then ro.id end)) top_search_uv," +
                "          count(distinct (case when ros.quotient_id = 0 then ro.id end))                                   no_scene_uv," +
                "          count(distinct IF(lower(t2.name) LIKE 'iphone%', ro.customer_id, null))                   " +
                "              order_iphone_order_cnt," +
                "          count(distinct" +
                "                IF(lower(t2.name) LIKE '%iphone14%', ro.customer_id, null))                        " +
                "        order_iphone13pm_order_cnt," +
                "          count(distinct IF(t3.repayment_term = 12, ro.customer_id, null))                                 order_twelve_order_cnt" +
                "   from rent_order ro" +
                "            left join rent_order_finance_detail rofd on ro.id = rofd.order_id" +
                "            left join rent_order_audit roa on ro.id = roa.order_id" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            LEFT JOIN rent_order_item t2 on ro.id = t2.order_id AND t2.item_type = 1" +
                "            left join rent_order_finance_detail t3 on ro.id = t3.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and rofd.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and roa.ds =" + ds +
                "     and ros.ds =" + ds +
                "     and t2.ds =" + ds +
                "     and t3.ds =" + ds +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <= " + hour +
                "   group by date(ro.create_time), ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderCnt = Long.parseLong(record.getString("order_cnt"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Long orderUv = Long.parseLong(record.getString("order_uv"));
            Long unDepositUv = Long.parseLong(record.getString("un_deposit_uv"));
            Long topSearchUv = Long.parseLong(record.getString("top_search_uv"));
            Long noSceneUv = Long.parseLong(record.getString("no_scene_uv"));
            Long orderIphoneOrderCnt = Long.parseLong(record.getString("order_iphone_order_cnt"));
            Long orderIphone13pmOrderCnt = Long.parseLong(record.getString("order_iphone13pm_order_cnt"));
            Long orderTwelveOrderCnt = Long.parseLong(record.getString("order_twelve_order_cnt"));
            domain.setOrderUv(orderUv);
            domain.setOrderCnt(orderCnt);
            domain.setMiniType(miniType);
            domain.setUnDepositUv(unDepositUv);
            domain.setTopSearchUv(topSearchUv);
            domain.setNoSceneUv(noSceneUv);
            domain.setOrderIphoneOrderCnt(orderIphoneOrderCnt);
            domain.setOrderIphone13pmOrderCnt(orderIphone13pmOrderCnt);
            domain.setOrderTwelveOrderCnt(orderTwelveOrderCnt);
            domain.setOrderIphoneOrderRate(CalculateUtil.div(orderIphoneOrderCnt, orderUv));
            domain.setOrderIphone13pmOrderRate(CalculateUtil.div(orderIphone13pmOrderCnt, orderUv));
            domain.setOrderTwelveOrderRate(CalculateUtil.div(orderTwelveOrderCnt, orderUv));
            domain.setNoSceneUvRate(CalculateUtil.div(noSceneUv, orderUv));
            domain.setTopSearchUvRate(CalculateUtil.div(topSearchUv, orderUv));
            domain.setUnDepositUvRate(CalculateUtil.div(unDepositUv, orderUv));
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取下单量
     */
    private List<WholeLinkOrderDO> getOrderCnt(String ds, Integer hour) {
        String sql = "select date(ro.create_time)                                                                             time," +
                "          count(distinct ro.id)                                                                            order_cnt," +
                "          count(distinct ro.customer_id)                                                                   order_uv," +
                "          count(distinct if(rofd.bond_free_status != 0, ro.customer_id, null))                             un_deposit_uv," +
                "          count(distinct (case when ro.mini_type in (1, 4, 5, 7, 9) and ro.scene = '1005' then ro.id end)) top_search_uv," +
                "          count(distinct (case when ros.quotient_id = 0 then ro.id end))                                   no_scene_uv," +
                "          count(distinct IF(lower(t2.name) LIKE '%iphone%', ro.customer_id, null))                  " +
                "               order_iphone_order_cnt," +
                "          count(distinct" +
                "                IF(lower(t2.name) LIKE '%iphone14%', ro.customer_id, null))                         " +
                " " +
                "      order_iphone13pm_order_cnt," +
                "          count(distinct IF(t3.repayment_term = 12, ro.customer_id, null))                                 order_twelve_order_cnt" +
                "   from rent_order ro" +
                "            left join rent_order_finance_detail rofd on ro.id = rofd.order_id" +
                "            left join rent_order_audit roa on ro.id = roa.order_id" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            LEFT JOIN rent_order_item t2 on ro.id = t2.order_id AND t2.item_type = 1" +
                "            left join rent_order_finance_detail t3 on ro.id = t3.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and rofd.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and roa.ds =" + ds +
                "     and ros.ds =" + ds +
                "     and t2.ds =" + ds +
                "     and t3.ds =" + ds +
                "     and date(ro.create_time) = date(getdate())" +
                "   group by date(ro.create_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderCnt = Long.parseLong(record.getString("order_cnt"));
            Long orderUv = Long.parseLong(record.getString("order_uv"));
            Long unDepositUv = Long.parseLong(record.getString("un_deposit_uv"));
            Long topSearchUv = Long.parseLong(record.getString("top_search_uv"));
            Long noSceneUv = Long.parseLong(record.getString("no_scene_uv"));
            Long orderIphoneOrderCnt = Long.parseLong(record.getString("order_iphone_order_cnt"));
            Long orderIphone13pmOrderCnt = Long.parseLong(record.getString("order_iphone13pm_order_cnt"));
            Long orderTwelveOrderCnt = Long.parseLong(record.getString("order_twelve_order_cnt"));
            domain.setOrderUv(orderUv);
            domain.setOrderCnt(orderCnt);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUnDepositUv(unDepositUv);
            domain.setTopSearchUv(topSearchUv);
            domain.setNoSceneUv(noSceneUv);
            domain.setOrderIphoneOrderCnt(orderIphoneOrderCnt);
            domain.setOrderIphone13pmOrderCnt(orderIphone13pmOrderCnt);
            domain.setOrderTwelveOrderCnt(orderTwelveOrderCnt);
            domain.setOrderIphoneOrderRate(CalculateUtil.div(orderIphoneOrderCnt, orderUv));
            domain.setOrderIphone13pmOrderRate(CalculateUtil.div(orderIphone13pmOrderCnt, orderUv));
            domain.setOrderTwelveOrderRate(CalculateUtil.div(orderTwelveOrderCnt, orderUv));
            domain.setNoSceneUvRate(CalculateUtil.div(noSceneUv, orderUv));
            domain.setTopSearchUvRate(CalculateUtil.div(topSearchUv, orderUv));
            domain.setUnDepositUvRate(CalculateUtil.div(unDepositUv, orderUv));
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取未审核关闭订单
     */
    private List<WholeLinkOrderDO> getUnAuditCloseUvMiniType(String ds, Integer hour) {
        String sql = "select\n" +
                "     date (roi.termination_time) time,\n" +
                "     ro.mini_type,"+
                "     count (distinct (case when ro.termination = 5 then ro.customer_id end)) un_audit_close_uv\n" +
                " from rent_order ro\n" +
                "     left join rent_order_infomore roi\n" +
                " on ro.id = roi.order_id\n" +
                "     left join (select customer_id, roa.id\n" +
                "     from rent_order ro\n" +
                "     left join rent_order_audit roa on ro.id = roa.order_id\n" +
                "     where roa.audit_status in (1, 2)\n" +
                "     and roa.ds = \n" + ds +
                "     and ro.ds = \n" + ds +
                "     ) a on ro.customer_id = a.customer_id\n" +
                " where ro.is_deleted = 0\n" +
                "   \n" +
                "   and ro.type = 1\n" +
                "   and ro.parent_id = 0\n" +
                "   and a.id is null\n" +
                "   and roi.ds = \n" + ds +
                "   and ro.ds = \n" + ds +
                "   and date (roi.termination_time) = date (getdate())\n" +
                " group by date (roi.termination_time), ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Long unAuditCloseUv = Long.parseLong(record.getString("un_audit_close_uv"));
            domain.setMiniType(miniType);
            domain.setUnAuditCloseUv(unAuditCloseUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取未审核关闭订单
     */
    private List<WholeLinkOrderDO> getUnAuditCloseUv(String ds, Integer hour) {
        String sql = "select\n" +
                "     date (roi.termination_time) time,\n" +
                "     count (distinct (case when ro.termination = 5 then ro.customer_id end)) un_audit_close_uv\n" +
                " from rent_order ro\n" +
                "     left join rent_order_infomore roi\n" +
                " on ro.id = roi.order_id\n" +
                "     left join (select customer_id, roa.id\n" +
                "     from rent_order ro\n" +
                "     left join rent_order_audit roa on ro.id = roa.order_id\n" +
                "     where roa.audit_status in (1, 2)\n" +
                "     and roa.ds = \n" + ds +
                "     and ro.ds = \n" + ds +
                "     ) a on ro.customer_id = a.customer_id\n" +
                " where ro.is_deleted = 0\n" +
                "   \n" +
                "   and ro.type = 1\n" +
                "   and ro.parent_id = 0\n" +
                "   and a.id is null\n" +
                "   and roi.ds = \n" + ds +
                "   and ro.ds = \n" + ds +
                "   and date (roi.termination_time) = date (getdate())\n" +
                " group by date (roi.termination_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unAuditCloseUv = Long.parseLong(record.getString("un_audit_close_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUnAuditCloseUv(unAuditCloseUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单新客
     */
    private List<WholeLinkOrderDO> getNewUserByMiniType(String ds, Integer hour) {
        String sql = "select date(ro.create_time) time," +
                "           ro.mini_type," +
                "          count(distinct rc.id) new_uv" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "            left join dataview_track_new_access dtna on dtpa.customer_third_id = dtna.customer_third_id" +
                "                left join rent_order ro on ro.customer_id = rc.id" +
                "   where dtpa.ds =" + ds +
                "     and dtna.ds =" + ds +
                "     and rc.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and dtna.customer_third_id is not null" +
                "     and rc.mobile is not null" +
                "     " +
                "     and ro.biz_type = 2" +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <=" + hour +
                "   group by date(ro.create_time),ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unLossUv = Long.parseLong(record.getString("new_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setNewOrderUv(unLossUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 新客下单
     */
    private List<WholeLinkOrderDO> getNewUser(String ds, Integer hour) {
        String sql = "select date(ro.create_time) time," +
                "          count(distinct rc.id) new_uv" +
                "   from dataview_track_enter_applets dtpa" +
                "            left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id" +
                "            left join dataview_track_new_access dtna on dtpa.customer_third_id = dtna.customer_third_id" +
                "                left join rent_order ro on ro.customer_id = rc.id" +
                "   where dtpa.ds =" + ds +
                "     and dtna.ds =" + ds +
                "     and rc.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and dtna.customer_third_id is not null" +
                "     and rc.mobile is not null" +
                "     " +
                "     and ro.biz_type = 2" +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <=" + hour +
                "   group by date(ro.create_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long unLossUv = Long.parseLong(record.getString("new_uv"));
            domain.setNewOrderUv(unLossUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkOrderDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.create_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(ro.create_time) time ,ro.mini_type, COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <= " + hour +
                "   GROUP BY date(ro.create_time),ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setOrderTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkOrderDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.create_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(ro.create_time) time , COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <= " + hour +
                "   GROUP BY date(ro.create_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setOrderTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkOrderDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.create_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 5)" +
                "   SELECT date(ro.create_time) time ,ro.mini_type, COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <= " + hour +
                "   GROUP BY date(ro.create_time),ro.mini_type" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setOrderTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkOrderDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.create_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 5)" +
                "   SELECT date(ro.create_time) time ,COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(ro.create_time) = date(getdate())" +
                "     and hour(ro.create_time) <= " + hour +
                "   GROUP BY date(ro.create_time)" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkOrderDO domain = new WholeLinkOrderDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            domain.setOrderTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    private Long stringToLong(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return Long.parseLong(val);
    }
}



