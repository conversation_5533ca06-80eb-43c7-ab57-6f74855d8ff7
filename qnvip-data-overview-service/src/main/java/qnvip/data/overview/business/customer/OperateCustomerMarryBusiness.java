package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerMarryDO;
import qnvip.data.overview.enums.CustomerGenderEnum;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerMarryService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;
import qnvip.rent.common.util.JsonUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerMarryBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerMarryService marryService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        try {
            List<OperateCustomerMarryDO> list1 = placeTheOrder();
            List<OperateCustomerMarryDO> list2 = riskPass();
            List<OperateCustomerMarryDO> list3 = onPay();
            List<OperateCustomerMarryDO> list4 = signIn();

            List<OperateCustomerMarryDO> list =
                    Stream.of(list1, list2, list3, list4).flatMap(Collection::stream).collect(Collectors.toList());
            // 写入mysql
            marryService.removeDataByTime(countDay);
            marryService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateCustomerMarryBusiness.runCore error:{}", e.getMessage());
        }
    }

    /**
     * 下单口径
     */
    private List<OperateCustomerMarryDO> placeTheOrder() {
        String sql = "select level," +
                "       sum(tal_cnt) as tal_cnt," +
                "       sum(cnt)     as cnt," +
                "       mini_type," +
                "       b.gender" +
                " from (" +
                "         select (" +
                "             case" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 24 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 20 then 1" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 29 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 25 then 2" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 34 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 30 then 3" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 39 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 35 then 4" +
                "                 when" +
                "                     (year(now()) - substr(b.id_card_no, 7, 4)) >= 40 then 5" +
                "                 end" +
                "             ) as                                                                                              level" +
                "              , a.mini_type" +
                "              , b.gender" +
                "              , count(DISTINCT (case when b.marriage_status is not null then a.customer_id else null end))     tal_cnt" +
                "              , count(DISTINCT (case when b.marriage_status in (10, 40, 90) then a.customer_id else null end)) cnt" +
                "         from rent_order a" +
                "                  inner join rent_customer b on a.customer_id = b.id" +
                "         where a.create_time >= ${fromTime}" +
                "           and a.create_time <= ${toTime}" +
                "           and a.is_deleted = 0" +
                "           and a.parent_id = 0" +
                "           and a.merchant_id = 100" +
                "           and b.gender is not null" +
                "           and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "         group by b.id_card_no, a.mini_type, b.gender" +
                "     ) b" +
                " group by mini_type, level, b.gender";
        return assemble(sql, CustomerTypeEnum.PLACE_THE_ORDER.getTypeCode());
    }

    /**
     * 通审口径
     */
    private List<OperateCustomerMarryDO> riskPass() {
        String sql = "select level," +
                "       sum(tal_cnt) as tal_cnt," +
                "       sum(cnt)     as cnt," +
                "       mini_type," +
                "       b.gender" +
                " from (" +
                "         select (" +
                "             case" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 24 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 20 then 1" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 29 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 25 then 2" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 34 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 30 then 3" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 39 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 35 then 4" +
                "                 when" +
                "                     (year(now()) - substr(b.id_card_no, 7, 4)) >= 40 then 5" +
                "                 end" +
                "             ) as                                                                                              level" +
                "              , a.mini_type" +
                "              , b.gender" +
                "              , count(DISTINCT (case when b.marriage_status is not null then a.customer_id else null end))     tal_cnt" +
                "              , count(DISTINCT (case when b.marriage_status in (10, 40, 90) then a.customer_id else null end)) cnt" +
                "         from rent_order a" +
                "                  inner join rent_customer b on a.customer_id = b.id" +
                "                  inner join rent_order_audit c on a.id = c.order_id" +
                "         where c.operate_time >= ${fromTime}" +
                "           and c.operate_time <= ${toTime}" +
                "           and a.is_deleted = 0" +
                "           and a.parent_id = 0" +
                "           and a.merchant_id = 100" +
                "           and c.type = 2" +
                "           and c.audit_status = 1" +
                "           and b.gender is not null" +
                "           and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "         group by b.id_card_no, a.mini_type, b.gender" +
                "     ) b" +
                " group by mini_type, level, b.gender";

        return assemble(sql, CustomerTypeEnum.RISK_PASS.getTypeCode());
    }

    /**
     * 支付
     */
    private List<OperateCustomerMarryDO> onPay() {
        String sql = "select level," +
                "       sum(tal_cnt) as tal_cnt," +
                "       sum(cnt)     as cnt," +
                "       mini_type," +
                "       b.gender" +
                " from (" +
                "         select (" +
                "             case" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 24 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 20 then 1" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 29 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 25 then 2" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 34 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 30 then 3" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 39 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 35 then 4" +
                "                 when" +
                "                     (year(now()) - substr(b.id_card_no, 7, 4)) >= 40 then 5" +
                "                 end" +
                "             ) as                                                                                              level" +
                "              , a.mini_type" +
                "              , b.gender" +
                "              , count(DISTINCT (case when b.marriage_status is not null then a.customer_id else null end))     tal_cnt" +
                "              , count(DISTINCT (case when b.marriage_status in (10, 40, 90) then a.customer_id else null end)) cnt" +
                "         from rent_order a" +
                "                  inner join rent_customer b on a.customer_id = b.id" +
                "                  inner join rent_order_audit c on a.id = c.order_id" +
                "         where a.payment_time >= ${fromTime}" +
                "           and a.payment_time <= ${toTime}" +
                "           and a.is_deleted = 0" +
                "           and a.parent_id = 0" +
                "           and a.merchant_id = 100" +
                "           and b.gender is not null" +
                "           and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "         group by b.id_card_no, a.mini_type, b.gender" +
                "     ) b" +
                " group by mini_type, level, b.gender";

        return assemble(sql, CustomerTypeEnum.ON_PAY.getTypeCode());
    }


    /**
     * 签收
     */
    private List<OperateCustomerMarryDO> signIn() {
        String sql = " select level," +
                "       sum(tal_cnt) as tal_cnt," +
                "       sum(cnt) as cnt," +
                "       mini_type," +
                "       b.gender" +
                " from (" +
                "         select (" +
                "             case" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 24 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 20 then 1" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 29 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 25 then 2" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 34 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 30 then 3" +
                "                 when (year(now()) - substr(b.id_card_no, 7, 4)) <= 39 and" +
                "                      (year(now()) - substr(b.id_card_no, 7, 4)) >= 35 then 4" +
                "                 when" +
                "                     (year(now()) - substr(b.id_card_no, 7, 4)) >= 40 then 5" +
                "                 end" +
                "             )           as level" +
                "              , a.mini_type" +
                "              , b.gender" +
                "              , count(DISTINCT (case when b.marriage_status is not null then a.customer_id else null end )) tal_cnt" +
                "              , count(DISTINCT (case when  b.marriage_status in (10,40,90) then a.customer_id else null end )) cnt" +
                "         from rent_order a" +
                "                  inner join rent_customer b on a.customer_id = b.id" +
                "                  inner join rent_order_logistics c on a.id = c.order_id" +
                "         where c.sign_time >= ${fromTime}" +
                "           and c.sign_time <= ${toTime}" +
                "           and a.is_deleted = 0" +
                "           and a.parent_id = 0" +
                "           and a.merchant_id = 100" +
                "           and b.gender is not null" +
                "           and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "           and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "         group by b.id_card_no, a.mini_type, b.gender" +
                "     ) b" +
                " group by mini_type, level, b.gender";

        return assemble(sql, CustomerTypeEnum.SIGN_IN.getTypeCode());
    }

    private List<OperateCustomerMarryDO> assemble(String sql, Integer type) {


        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        LinkedList<OperateCustomerMarryDO> list = Lists.newLinkedList();
        OperateCustomerMarryDO domain;
        String miniType;
        String level;
        String cnt;
        String talCnt;
        String gender;
        for (Record record : records) {
            domain = new OperateCustomerMarryDO();
            miniType = record.getString("mini_type");
            level = record.getString("level");
            if ("\\N".equals(level)) {
                continue;
            }
            cnt = record.getString("cnt");
            talCnt = record.getString("tal_cnt");
            gender = record.getString("gender");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setType(type);
            domain.setCount(Long.valueOf(cnt));
            domain.setTotalCount(Long.valueOf(talCnt));
            domain.setGender(CustomerGenderEnum.getValueByCode(Integer.parseInt(gender)));
            domain.setAge(Integer.parseInt(level));
            domain.setCountDay(countDay);
            list.add(domain);
        }
        return list;
    }


}
