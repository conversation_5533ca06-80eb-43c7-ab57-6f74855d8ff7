package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.AccessCoreDO;
import qnvip.data.overview.domain.customer.CustomerAnalysisBrandDO;
import qnvip.data.overview.domain.customer.CustomerAnalysisCityDO;
import qnvip.data.overview.domain.customer.CustomerAnalysisSynthesisDO;
import qnvip.data.overview.domain.customer.CustomerOverallDO;
import qnvip.data.overview.enums.CusetomerAnalysisEnum;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.service.customer.CustomerAnalysisBrandService;
import qnvip.data.overview.service.customer.CustomerAnalysisCityService;
import qnvip.data.overview.service.customer.CustomerAnalysisSynthesisService;
import qnvip.data.overview.service.customer.CustomerOverallService;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class CustomerAnalysisBusiness {

    private final OdpsUtil odpsUtil;

    private final CustomerOverallService customerOverallService;

    private final CustomerAnalysisBrandService customerAnalysisBrandService;
    private final CustomerAnalysisCityService customerAnalysisCityService;
    private final CustomerAnalysisSynthesisService customerAnalysisSynthesisService;

    /**
     * 用户分析
     */
    public void runOverall() {
        //  当前时间新访客
        CompletableFuture.runAsync(this::newCustomer);
        //  当前时间老访客
        CompletableFuture.runAsync(this::oldCustomer);
    }

    /**
     * 用户画像
     */
    public void runCustomerDraw() {
        // 用户价格偏好
        CompletableFuture.runAsync(this::rentAnalysis);
        // 品牌偏好
        CompletableFuture.runAsync(this::brandAnalysis);
        // 性别
        CompletableFuture.runAsync(this::genderAnalysis);
        //  年龄
        CompletableFuture.runAsync(this::ageAnalysis);
        // 收货城市
        CompletableFuture.runAsync(this::receiveCityAnalysis);
        // 用户城市
        CompletableFuture.runAsync(this::cityAnalysis);
    }

    /**
     * 年龄分析
     *
     * @return
     */
    private void ageAnalysis() {
        String sql = "select c.level," +
                "       c.cnt," +
                "       c.status" +
                " from (select level," +
                "             sum(cnt) as cnt" +
                "              ," +
                "             status" +
                "      from (" +
                "               select (" +
                "                   case" +
                "                       when (year(now()) - substr(b.id_card_no, 7, 4)) <= 25 and" +
                "                            (year(now()) - substr(b.id_card_no, 7, 4)) >= 18 then 1" +
                "                       when (year(now()) - substr(b.id_card_no, 7, 4)) <= 30 and" +
                "                            (year(now()) - substr(b.id_card_no, 7, 4)) >= 26 then 2" +
                "                       when (year(now()) - substr(b.id_card_no, 7, 4)) <= 35 and" +
                "                            (year(now()) - substr(b.id_card_no, 7, 4)) >= 31 then 3" +
                "                       when (year(now()) - substr(b.id_card_no, 7, 4)) <= 40 and" +
                "                            (year(now()) - substr(b.id_card_no, 7, 4)) >= 36 then 4" +
                "                       when (year(now()) - substr(b.id_card_no, 7, 4)) <= 50 and" +
                "                            (year(now()) - substr(b.id_card_no, 7, 4)) >= 41 then 5" +
                "                       else 6" +
                "                       end" +
                "                   )           as level" +
                "                    , a.status" +
                "                    , count(1) as cnt" +
                "               from rent_order a" +
                "                        inner join rent_customer b" +
                "                                   on a.customer_id = b.id" +
                "               where a.create_time >= ${fromTime}" +
                "                 and a.create_time <= ${toTime}" +
                "                 and a.is_deleted = 0" +
                "                 and b.is_deleted = 0" +
                "                 and a.ds = ${dsStr}" +
                "                 and b.ds = ${dsStr}" +
                "               group by b.id_card_no" +
                "                      , status" +
                "           ) b" +
                "      group by status, level) c";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisSynthesisDO domain = new CustomerAnalysisSynthesisDO();
            String cnt = record.getString("cnt");
            String status = record.getString("status");
            String level = record.getString("level");
            domain.setCount(new BigDecimal(cnt));
            domain.setCountDay(countDay);
            domain.setStatus(Integer.valueOf(status));
            domain.setName(level);
            domain.setType(CusetomerAnalysisEnum.AGE.getCode());
            customerAnalysisSynthesisService.saveOrUpdate(domain);
        }
    }

    private void receiveCityAnalysis() {
        String sql = "select b.receiver_province" +
                "     , a.status" +
                "     , count(1) cnt" +
                " from rent_order a" +
                "         inner join rent_order_receiver b" +
                "                    on a.id = b.order_id" +
                " where b.receiver_province is not null" +
                "  and b.receiver_province != ''" +
                "  and a.create_time >= ${fromTime}" +
                "  and a.create_time <= ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and b.is_deleted = 0" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                " group by receiver_province" +
                "       , status";

        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisCityDO domain = new CustomerAnalysisCityDO();
            String status = record.getString("status");
            String cnt = record.getString("cnt");
            String receiverCity = record.getString("receiver_province");
            domain.setCountDay(countDay);
            domain.setCount(BigDecimal.valueOf(Double.parseDouble(cnt)));
            domain.setStatus(Integer.valueOf(status));
            domain.setCity(receiverCity); //
            domain.setType(CusetomerAnalysisEnum.RECEIVING_CITY.getCode()); //
            customerAnalysisCityService.saveOrUpdate(domain);
        }
    }

    /**
     * 用户所在城市
     */
    private void cityAnalysis() {
        String sql = "select b.message_province" +
                "     , a.status" +
                "     , count(1) cnt" +
                " from rent_order a" +
                "         inner join rent_customer b" +
                "                    on a.customer_id = b.id" +
                " where b.message_province is not null" +
                "  and b.message_province != ''" +
                "  and a.create_time >= ${fromTime}" +
                "  and a.create_time <= ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and b.is_deleted = 0" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                " group by message_province" +
                "       , status";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisCityDO domain = new CustomerAnalysisCityDO();
            String cnt = record.getString("cnt");
            String status = record.getString("status");
            String unitCity = record.getString("message_province");
            domain.setCount(new BigDecimal(cnt));
            domain.setCountDay(countDay);
            domain.setStatus(Integer.valueOf(status));
            domain.setCity(unitCity); //
            domain.setType(CusetomerAnalysisEnum.USER_CITY.getCode());
            customerAnalysisCityService.saveOrUpdate(domain);
        }
    }

    /**
     * 性别分析
     */
    private void genderAnalysis() {
        String sql = "select b.gender" +
                "     , a.status" +
                "     , count(customer_id) cnt" +
                " from rent_order a" +
                "         inner join rent_customer b" +
                "                    on a.customer_id = b.id" +
                " where a.create_time >= ${fromTime}" +
                "  and a.create_time <= ${toTime}" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                "  and a.is_deleted = 0" +
                "  and b.is_deleted = 0" +
                "  and b.gender is not null" +
                " group by gender, a.status";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisSynthesisDO domain = new CustomerAnalysisSynthesisDO();
            String cnt = record.getString("cnt");
            String status = record.getString("status");
            String gender = record.getString("gender");
            domain.setCount(BigDecimal.valueOf(Double.parseDouble(cnt)));
            domain.setCountDay(countDay);
            domain.setStatus(Integer.valueOf(status));
            domain.setName(gender); //
            domain.setType(CusetomerAnalysisEnum.GENDER.getCode()); //  性别
            customerAnalysisSynthesisService.saveOrUpdate(domain);
        }

    }

    /**
     * 品牌分析
     */
    private void brandAnalysis() {
        String sql = "select b.brand" +
                "     , a.status" +
                "     , count(1) cnt" +
                " from rent_order a" +
                "         inner join rent_order_item b" +
                "                    on a.id = b.order_id" +
                " where b.brand is not null" +
                "  and b.brand != ''" +
                "  and a.create_time >= ${fromTime}" +
                "  and a.create_time <= ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and b.is_deleted = 0" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                " group by brand" +
                "       , status";

        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisBrandDO customerAnalysisBrandDO = new CustomerAnalysisBrandDO();
            String cnt = record.getString("cnt");
            String status = record.getString("status");
            String brand = record.getString("brand");
            customerAnalysisBrandDO.setCount(new BigDecimal(cnt));
            customerAnalysisBrandDO.setCountDay(countDay);
            customerAnalysisBrandDO.setStatus(Integer.valueOf(status));
            customerAnalysisBrandDO.setBrand(brand);
            customerAnalysisBrandService.saveOrUpdate(customerAnalysisBrandDO);
        }
        // String sql = "select a.status, c.second_category_ids" +
        //         "from rent_order a" +
        //         "         inner join rent_order_item b on a.id = b.order_id" +
        //         "         inner join rent_item c on c.id = b.item_id" +
        //         "where a.create_time >= ${fromTime}" +
        //         "  and a.create_time <= ${toTime}" +
        //         "  and a.ds = ${dsStr}" +
        //         "  and b.ds = ${dsStr}" +
        //         "  and c.ds = ${dsStr}" +
        //         "group by a.status, second_category_ids";
        //
        // HashMap<String, Object> key2value = Maps.newHashMap();
        // String dsStr = ThreadLocalCacheUtil.get("dsStr");
        // String fromTime = ThreadLocalCacheUtil.get("fromTime");
        // String toTime = ThreadLocalCacheUtil.get("toTime");
        // LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        // key2value.put("fromTime", fromTime);
        // key2value.put("toTime", toTime);
        // key2value.put("dsStr", dsStr);
        // String format = SqlUtils.processTemplate(sql, key2value);
        // List<Record> records = odpsUtil.querySql(format.concat(";"));
        // HashSet<Long> ids = Sets.newHashSet();
        // HashMap<Integer, Map<Long, Integer>> status2id2count = Maps.newHashMap();
        //
        // for (Record record : records) {
        //     String categoryIds = record.getString("second_category_ids");
        //     Integer status = Integer.valueOf(record.getString("status"));
        //     if (!status2id2count.containsKey(status)) {
        //        HashMap<Long, Integer> id2count = Maps.newHashMap();F
        //         //         status2id2count.put(status, id2count);
        //     }
        //  Map<Long, Integer> id2count = status2id2count.get(status);
        //         //         Arrays.stream(categoryIds.split(",")).
        //         //                 peek(v -> {
        //         //                     long id = Long.parseLong(v);
        //         //                     ids.add(id);
        //         //                     if (id2count.containsKey(id)) {
        //         //                         Integer count = id2count.get(id);
        //         //                         id2count.put(id, ++count);
        //         //                     } else {
        //         //                         id2count.put(id, 0);
        //         //                     }
        //         //                 });
        // }
        // SqlInfo sqlInfo2 = ZealotKhala.start().
        //         select("id , name").
        //         from("rent_category").
        //         where("ds =", dsStr).
        //         in("id", ids.toArray()).
        //         end();
        // String sql2 = SqlUtils.formart(sqlInfo2);
        // List<Record> categoryRecords = odpsUtil.querySql(sql2);
        //
        // HashMap<Long, CustomerAnalysisBrandDO> id2brandDO = Maps.newHashMap();
        // //TODO 查询分类信息
        // for (Record record : categoryRecords) {
        //     CustomerAnalysisBrandDO customerAnalysisBrandDO = new CustomerAnalysisBrandDO();
        //     Long id = Long.valueOf(record.getString("id"));
        //     String name = record.getString("name");
        //     customerAnalysisBrandDO.setBrand(name);
        //     id2brandDO.put(id, customerAnalysisBrandDO);
        // }
        // ArrayList<CustomerAnalysisBrandDO> list = Lists.newArrayList();
        // for (Map.Entry<Integer, Map<Long, Integer>> entry : status2id2count.entrySet()) {
        //     entry.getValue().forEach((id, count) -> {
        //         CustomerAnalysisBrandDO customerAnalysisBrandDO = id2brandDO.get(id);
        //         customerAnalysisBrandDO.setCount(BigDecimal.valueOf(count));
        //         customerAnalysisBrandDO.setStatus(entry.getKey());
        //         list.add(customerAnalysisBrandDO);
        //     });
        // }
        // for (CustomerAnalysisBrandDO customerAnalysisBrandDO : list) {
        //     customerAnalysisBrandService.saveOrUpdate(customerAnalysisBrandDO);
        // }
    }

    /**
     * 价格偏好
     */
    private void rentAnalysis() {
        String sql = "select status," +
                "       count(1) as num," +
                "       level" +
                " from (" +
                "         select b.status as status" +
                "              , (case" +
                "                     when a.rent_total < 1000 then 1" +
                "                     when a.rent_total < 2000 and a.rent_total >= 1000 then 2" +
                "                     when a.rent_total < 3000 and a.rent_total >= 2000 then 3" +
                "                     when a.rent_total < 4000 and a.rent_total >= 3000 then 4" +
                "                     else 5" +
                "             end)        as level" +
                "         from rent_order_finance_detail a" +
                "                  inner join rent_order b" +
                "                             on a.order_id = b.id" +
                "         where b.create_time >= ${fromTime}" +
                "           and b.create_time <= ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and b.is_deleted = 0" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                "     ) c" +
                " group by level, status";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);

        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        for (Record record : records) {
            CustomerAnalysisSynthesisDO domain = new CustomerAnalysisSynthesisDO();
            String num = record.getString("num");
            String status = record.getString("status");
            String level = record.getString("level");
            domain.setCountDay(countDay);
            domain.setStatus(Integer.valueOf(status));
            domain.setName(level);
            domain.setCount(BigDecimal.valueOf(Long.parseLong(num)));
            domain.setType(CusetomerAnalysisEnum.PRICE.getCode()); // 价格
            customerAnalysisSynthesisService.saveOrUpdate(domain);
        }
    }


    /**
     * 获取全站PV和UV
     *
     * @param fromTime
     * @param toTime
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2PvUv(LocalDateTime countDay, String fromTime, String toTime) {
        SqlInfo sqlInfo1 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();
        String sql1 = SqlUtils.formart(sqlInfo1);
        List<Record> records = odpsUtil.querySql(sql1);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String miniType = record.getString("mini_type");
            accessCoreDO.setUv(Long.parseLong(uv));
            accessCoreDO.setPv(Long.parseLong(pv));
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    /**
     * 处理老访客
     */
    private void oldCustomer() {

        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

            // 老访客人数、老访客浏览量
            CompletableFuture<Map<Integer, CustomerOverallDO>> f1 =
                    CompletableFuture.supplyAsync(this::oldUvAndPv);

            // 老访客跳失数,跳失表的UV
            CompletableFuture<Map<Integer, CustomerOverallDO>> f2 =
                    CompletableFuture.supplyAsync(this::oldJumpLossUv);
            // 老客总访问时长
            CompletableFuture<Map<Integer, CustomerOverallDO>> f3 =
                    CompletableFuture.supplyAsync(this::oldKeepTime);
            // 老访客总访问页数
            CompletableFuture<Map<Integer, CustomerOverallDO>> f4 =
                    CompletableFuture.supplyAsync(this::oldPageCount);


            CompletableFuture.allOf(f1, f2, f3, f4).join();
            /* 根据miniType合并处理 */
            Stream.of(f1.get(), f2.get(), f3.get(), f4.get()).
                    flatMap(x -> x.entrySet().stream()).
                    collect(Collectors.toMap(Map.Entry::getKey, value -> Lists.newArrayList(value.getValue()),
                            (List<CustomerOverallDO> newValueList, List<CustomerOverallDO> oldValueList) -> {
                                oldValueList.addAll(newValueList);
                                return oldValueList;
                            })).
                    forEach((key, value) -> {
                        CustomerOverallDO domain = new CustomerOverallDO();
                        value.forEach(v -> {
                            try {
                                ObjectUtils.merge(domain, v, ((field, pair) -> pair.getLeft() + "," + pair.getRight()));
                            } catch (IllegalAccessException | InvocationTargetException e) {
                                log.error("merge Do error {}", e.getMessage());
                            }
                        });

                        domain.setCountDay(countDay);
                        domain.setMiniType(key);
                        // 老访客
                        domain.setType(2);
                        customerOverallService.saveOrUpdate(domain);
                    });
        } catch (InterruptedException | ExecutionException e) {
            log.error("runOverall oldCustomer error {}", e.getMessage());
        }
    }

    /**
     * 处理新访客
     */
    private void newCustomer() {

        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            // 新访客id
            // 新访客人数/全站用户数
            // 新访客人数、新访客浏览量
            CompletableFuture<Map<Integer, CustomerOverallDO>> f1 =
                    CompletableFuture.supplyAsync(this::newUvAndPv);
            // 新访客跳失数,跳失表的UV
            CompletableFuture<Map<Integer, CustomerOverallDO>> f2 =
                    CompletableFuture.supplyAsync(this::newJumpLossUv);
            // 新客总访问时长
            CompletableFuture<Map<Integer, CustomerOverallDO>> f3 =
                    CompletableFuture.supplyAsync(this::newKeepTime);
            // 新访客总访问页数
            CompletableFuture<Map<Integer, CustomerOverallDO>> f4 =
                    CompletableFuture.supplyAsync(this::newPageCount);

            CompletableFuture.allOf(f1, f2, f3, f4).join();
            /* 根据miniType合并处理 */
            Stream.of(f1.get(), f2.get(), f3.get(), f4.get()).
                    flatMap(x -> x.entrySet().stream()).
                    collect(Collectors.toMap(Map.Entry::getKey, value -> Lists.newArrayList(value.getValue()),
                            (List<CustomerOverallDO> newValueList, List<CustomerOverallDO> oldValueList) -> {
                                oldValueList.addAll(newValueList);
                                return oldValueList;
                            })).
                    forEach((key, value) -> {
                        CustomerOverallDO domain = new CustomerOverallDO();
                        value.forEach(v -> {
                            try {
                                ObjectUtils.merge(domain, v, ((field, pair) -> pair.getLeft() + "," + pair.getRight()));
                            } catch (IllegalAccessException | InvocationTargetException e) {
                                log.error("merge Do error {}", e.getMessage());
                            }
                        });

                        domain.setCountDay(countDay);
                        domain.setMiniType(key);
                        // 新访客
                        domain.setType(1);
                        customerOverallService.saveOrUpdate(domain);
                    });
        } catch (InterruptedException | ExecutionException e) {
            log.error("runOverall newCustomer error {}", e.getMessage());
        }
    }

    /**
     * 新客总访问时长
     *
     * @return
     */
    private Map<Integer, CustomerOverallDO> newKeepTime() {
        String sql = "SELECT mini_type, sum(keep_alive_time) sumkeeptime" +
                " FROM DATAVIEW_TRACK_ENTER_APPLETS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM DATAVIEW_TRACK_NEW_ACCESS" +
                "                     WHERE ds = ${dsStr}" +
                "                       AND REPORT_TIME BETWEEN ${fromTime}" +
                "                         AND ${toTime}" +
                "                     GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE action_type = 2" +
                "  AND a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String sumkeeptime = record.getString("sumkeeptime");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalKeepTime(new BigDecimal(sumkeeptime));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));

    }

    /**
     * 老客总访问时长
     *
     * @return
     */
    private Map<Integer, CustomerOverallDO> oldKeepTime() {
        String sql = "SELECT mini_type, sum(keep_alive_time) sumkeeptime" +
                " FROM DATAVIEW_TRACK_ENTER_APPLETS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM (SELECT count(1) cnt, CUSTOMER_THIRD_ID" +
                "                           FROM DATAVIEW_TRACK_ENTER_APPLETS" +
                "                           WHERE action_type = 1" +
                "                             AND ds = ${dsStr}" +
                "                             AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                           GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) as tb" +
                "                     WHERE tb.cnt >= 2) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE action_type = 2" +
                "  AND a.ds = ${dsStr}" +
                "  and REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String sumkeeptime = record.getString("sumkeeptime");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalKeepTime(new BigDecimal(sumkeeptime));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));

    }

    /**
     * 新访客总访问页面数
     */
    private Map<Integer, CustomerOverallDO> newPageCount() {
        String sql = "SELECT mini_type, count(1) cnt" +
                " FROM DATAVIEW_TRACK_PAGE_STATISTICS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM DATAVIEW_TRACK_NEW_ACCESS" +
                "                     WHERE ds = ${dsStr}" +
                "                       AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                     GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE action_type = 1" +
                "  AND a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String cnt = record.getString("cnt");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalPageBrowsing(Long.parseLong(cnt));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));
    }

    /**
     * 老访客总访问页面数
     */
    private Map<Integer, CustomerOverallDO> oldPageCount() {
        String sql = "SELECT mini_type, count(1) cnt" +
                " FROM DATAVIEW_TRACK_PAGE_STATISTICS a" +
                "         LEFT JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                    FROM (SELECT count(1) cnt, CUSTOMER_THIRD_ID" +
                "                          FROM DATAVIEW_TRACK_ENTER_APPLETS" +
                "                          WHERE action_type = 1" +
                "                            AND ds = ${dsStr}" +
                "                            AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                          GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) as tb" +
                "                    WHERE tb.cnt >= 2) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE action_type = 1" +
                "  AND a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String cnt = record.getString("cnt");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalPageBrowsing(Long.parseLong(cnt));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));
    }

    /**
     * 新访客总跳失数
     */
    private Map<Integer, CustomerOverallDO> newJumpLossUv() {
        String sql = "SELECT mini_type, count(*) cnt" +
                " FROM DATAVIEW_TRACK_JUMP_LOSS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM DATAVIEW_TRACK_NEW_ACCESS" +
                "                     WHERE ds = ${dsStr}" +
                "                       AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                     GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE ds =${dsStr} " +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        HashMap<String, Object> key2value = Maps.newHashMap();
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String sql2 = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(sql2.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String cnt = record.getString("cnt");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalJumpLoss(Long.parseLong(cnt));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));
    }

    /**
     * 老访客总跳失数
     */
    private Map<Integer, CustomerOverallDO> oldJumpLossUv() {
        String sql = "SELECT mini_type, count(1) cnt" +
                " FROM DATAVIEW_TRACK_JUMP_LOSS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM (SELECT count(1) cnt, CUSTOMER_THIRD_ID" +
                "                           FROM DATAVIEW_TRACK_ENTER_APPLETS" +
                "                           WHERE action_type = 1" +
                "                                  AND ds = ${dsStr}" +
                "                             AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                           GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) as tb" +
                "                     WHERE tb.cnt >= 2) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String cnt = record.getString("cnt");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTotalJumpLoss(Long.parseLong(cnt));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));
    }


    /**
     * 新访客PVUV
     */
    private Map<Integer, CustomerOverallDO> newUvAndPv() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo1 =
                ZealotKhala.start()
                        .select(EventTrackSqlParamEnum.PV_AND_UV.getDesc())
                        .from(EventTrackSqlParamEnum.DATAVIEW_TRACK_NEW_ACCESS.name())
                        .where(EventTrackSqlParamEnum.CONDITION.getDesc())
                        .and("ds = " + dsStr)
                        .andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime)
                        .groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";"))
                        .end();
        String sql1 = SqlUtils.formart(sqlInfo1);
        List<Record> records = odpsUtil.querySql(sql1);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv");
            String pv = record.getString("pv");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUv(Long.parseLong(uv));
            domain.setPv(Long.parseLong(pv));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));

    }

    /**
     * 老访客PVUV
     */
    private Map<Integer, CustomerOverallDO> oldUvAndPv() {
        String sql = "SELECT mini_type, count(a.customer_third_id) pv, count(distinct a.customer_third_id) uv" +
                " FROM DATAVIEW_TRACK_ENTER_APPLETS a" +
                "         inner JOIN (SELECT CUSTOMER_THIRD_ID" +
                "                     FROM (SELECT count(1) cnt, CUSTOMER_THIRD_ID" +
                "                           FROM DATAVIEW_TRACK_ENTER_APPLETS" +
                "                           WHERE action_type = 1" +
                "                             AND ds = ${dsStr}" +
                "                             AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                "                           GROUP BY CUSTOMER_THIRD_ID, MINI_TYPE) as tb" +
                "                     WHERE tb.cnt >= 2) b ON a.customer_third_id = b.customer_third_id" +
                " WHERE action_type = 1" +
                "  AND a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY MINI_TYPE";
        HashMap<String, Object> key2value = Maps.newHashMap();
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);
        String newSql = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(newSql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            CustomerOverallDO domain = new CustomerOverallDO();

            String miniType = record.getString("mini_type");
            String uv = record.getString("uv");
            String pv = record.getString("pv");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUv(Long.parseLong(uv));
            domain.setPv(Long.parseLong(pv));
            return domain;
        }).collect(Collectors.toMap(CustomerOverallDO::getMiniType, Function.identity()));

    }
}
