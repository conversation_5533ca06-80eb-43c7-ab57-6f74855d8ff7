package qnvip.data.overview.business.web;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.web.LengthOfStayRentDO;
import qnvip.data.overview.service.web.LengthOfStayRentService;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * <私域外网接口>
 *
 * <AUTHOR>
 * @create 2022/7/7
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LengthOfStayBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");


    private final OdpsUtil odpsUtil;
    private final LengthOfStayRentService lengthOfStayRentService;


    public void execHistoryCount(String fromTime, String toTime) {
        try {
            List<LengthOfStayRentDO> list = countTask(fromTime, toTime);
            lengthOfStayRentService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    public void execCount() {
        try {
            List<LengthOfStayRentDO> list = countTask(1);
            lengthOfStayRentService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }
    /**
     * 风控导流商
     */
    private List<LengthOfStayRentDO> countTask(String fromTime, String toTime) {
        try {
            String sql = "select distinct count_day,mini_type,customer_id,length_of_stay from\n" +
                    "  (select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[0].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[1].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[2].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[3].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[4].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[5].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[6].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[7].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[8].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[9].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                       and dtpa.report_time >= " + fromTime +
                    "                       and dtpa.report_time <= " + toTime +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000)\n" +
                    " order by count_day     " +
                    ";\n";


            List<Record> records = odpsUtil.querySql(sql);
            List<LengthOfStayRentDO> resList = new ArrayList<>();
            records.forEach(k -> {
                LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("count_day"), dateFormatter),
                        LocalTime.MIN);
                Double lengthOfStay = Double.valueOf(k.getString("length_of_stay"));
                Integer miniType = Integer.valueOf(k.getString("mini_type"));
                Integer customerId = Integer.valueOf(k.getString("customer_id"));
                LengthOfStayRentDO repayDO = new LengthOfStayRentDO();
                repayDO.setMiniType(miniType);
                repayDO.setCountDay(countDay);
                repayDO.setCustomerId(customerId);
                repayDO.setLengthOfStay(lengthOfStay.longValue());
                resList.add(repayDO);
            });
            return resList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 风控导流商
     */
    private List<LengthOfStayRentDO> countTask(Integer day) {
        try {
            String sql = "select distinct count_day,mini_type,customer_id,length_of_stay from\n" +
                    "  (select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[0].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[1].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[2].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[3].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", " +
                    " 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[4].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), - " + day + ", " +
                    "'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[5].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[6].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[7].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[8].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000\n" +
                    " UNION ALL \n" +
                    "  select date (dtpa.report_time) count_day,mini_type, c.id customer_id, sum(dtpa.keep_alive_time)\n" +
                    "                     length_of_stay\n" +
                    "                     from dataview_track_enter_applets dtpa\n" +
                    "                              inner join rent_customer c on get_json_object(c.bindinfo, '$[9].miniUserId') =\n" +
                    "                     dtpa.customer_third_id\n" +
                    "                     where dtpa.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and c.ds = to_char(getdate(), 'yyyymmdd')\n" +
                    "                       and dtpa.action_type = 2\n" +
                    "                      \n" +
                    "                       and date (dtpa.report_time) = dateadd(date (getdate()), -" + day + ", 'dd')\n" +
                    "                       and date (dtpa.report_time) < date(getdate())\n" +
                    "                     group by date (dtpa.report_time), c.id, mini_type\n" +
                    "                     having sum (dtpa.keep_alive_time) >60000)\n" +
                    " order by count_day     " +
                    ";\n";


            List<Record> records = odpsUtil.querySql(sql);
            List<LengthOfStayRentDO> resList = new ArrayList<>();
            records.forEach(k -> {
                LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("count_day"), dateFormatter),
                        LocalTime.MIN);
                Double lengthOfStay = Double.valueOf(k.getString("length_of_stay"));
                Integer miniType = Integer.valueOf(k.getString("mini_type"));
                Integer customerId = Integer.valueOf(k.getString("customer_id"));
                LengthOfStayRentDO repayDO = new LengthOfStayRentDO();
                repayDO.setMiniType(miniType);
                repayDO.setCountDay(countDay);
                repayDO.setCustomerId(customerId);
                repayDO.setLengthOfStay(lengthOfStay.longValue());
                resList.add(repayDO);
            });
            return resList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }
}
