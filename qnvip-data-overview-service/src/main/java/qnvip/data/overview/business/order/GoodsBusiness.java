package qnvip.data.overview.business.order;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.GoodsDO;
import qnvip.data.overview.domain.order.UpGoodsDO;
import qnvip.data.overview.service.order.GoodsService;
import qnvip.data.overview.service.order.UpGoodsService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品销量分析
 * <AUTHOR>
 * @Date 2021/9/30 10:25 上午
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsBusiness {

    private final OdpsUtil odpsUtil;
    private final GoodsService goodsService;
    private final UpGoodsService upGoodsService;


    /**
     * 启动计算任务
     * @param sTime
     * @param eTime
     */
    public void runGoodsOrderCount(String dsStr,LocalDateTime countDay,String sTime, String eTime){
        List<GoodsDO> list = goodsOrderCount(dsStr,countDay,sTime, eTime);
        for (GoodsDO goodsDO : list) {
            goodsService.saveOrUpdate(goodsDO);
        }
    }

    /**
     * 启动在架商品数计算
     * @param dsStr
     * @param countDay
     */
    public void runUpStatusGoodsCount(String dsStr,LocalDateTime countDay){
        UpGoodsDO upGoodsDO = new UpGoodsDO();
        upGoodsDO.setCountDay(countDay);
        Long number = upStatusGoods(dsStr);
        upGoodsDO.setUpCount(Long.valueOf(number));
        upGoodsService.saveOrUpdate(upGoodsDO);
    }


    /**
     * 在架商品数量
     * @param dsStr
     * @return
     */
    public Long upStatusGoods(String dsStr){
        StringBuilder sb = new StringBuilder(" select count(1) as num from rent_item where status = 5 and is_deleted = 0 and ds = ");
        sb.append(dsStr).append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if(CollUtil.isEmpty(recordList)){
            return 0l;
        }
        return Long.valueOf(recordList.get(0).getString("num"));
    }


    /**
     * 商品销量分析、动销商品数,这个粒度的数据，在代码上做处理
     * 商品下单量分组
     * @param dsStr
     * @param sTime
     * @param eTime
     * @return
     */
    public List<GoodsDO> goodsOrderCount(String dsStr,LocalDateTime countDay,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select " +
                "c.first_category_ids as firstCategoryIds,c.second_category_ids as secondCategoryIds,b.item_id as itemId, " +
                "b.short_name as name,NVL(sum(b.actual_supply_price),0) as totalSupply,NVL(sum(b.operating_purchase_price),0) as totalBuy," +
                "NVL(sum(d.repayment_term),0) as totalTerm,NVL(sum(d.rent_total),0) as totalPrice1,NVL(sum(d.buyout_amt),0) as totalPrice2," +
                " concat(b.color,' ',b.size) as model," +
                "count(1) as num,c.main_category as main_category from rent_order a inner join rent_order_item b on a.id = b.order_id inner join rent_item c on " +
                "b.item_id = c.id inner join rent_order_finance_detail d on a.id = d.order_id where a.biz_type = 2 and " +
                "a.merchant_id = 100 and a.payment_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        sb.append(" and d.ds = ").append(dsStr);
        if(sTime!=null){
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if(eTime!=null){
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by b.item_id,b.short_name,b.color,b.size,c.main_category,c.first_category_ids,c.second_category_ids; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if(CollectionUtils.isEmpty(recordList)) return new ArrayList<>();
        //查询分类名称
        Map<String,String> categoryNameMap = new HashMap<>();
        String[] split;
        List<GoodsDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            GoodsDO goodsDO = new GoodsDO();
            goodsDO.setCountDay(countDay);
            goodsDO.setItemId(Long.parseLong(record.getString("itemid")));
            goodsDO.setGoodsName(record.getString("name"));
            goodsDO.setPayCount(Long.parseLong(record.getString("num")));
            goodsDO.setModel(record.getString("model"));
            goodsDO.setTotalBuyPrice(new BigDecimal(record.getString("totalbuy")));
            goodsDO.setTotalTerm(Long.parseLong(record.getString("totalterm")));
            goodsDO.setTotalWorth(new BigDecimal(record.getString("totalprice1")).add(new BigDecimal(record.getString("totalprice2"))));
            goodsDO.setTotalSupplyPrice(new BigDecimal(record.getString("totalsupply")));
            goodsDO.setFirstCategoryIds(record.getString("firstcategoryids"));
            goodsDO.setSecondCategoryIds(record.getString("secondcategoryids"));
            goodsDO.setMainCategory(record.getString("main_category"));

            if(!categoryNameMap.containsKey(goodsDO.getMainCategory())){
                //从odps上查分类名字
                String categoryName = getCategoryName(dsStr,goodsDO.getMainCategory());
                //找不到数据，忽略
                categoryNameMap.put(goodsDO.getMainCategory(),categoryName);
                if(categoryName==null){
                    log.error("mainCategoryId：{}找不到，忽略该数据",goodsDO.getMainCategory());
//                    continue;
                }
                goodsDO.setMainCategoryName(categoryName);
            }else{
                String categoryName = categoryNameMap.get(goodsDO.getMainCategory());
                if(categoryName==null){
                    log.error("mainCategoryId：{}找不到，忽略该数据",goodsDO.getMainCategory());
//                    continue;
                }
                goodsDO.setMainCategoryName(categoryName);
            }
            if(!categoryNameMap.containsKey(goodsDO.getFirstCategoryIds())){
                //从odps上查分类名字
                split = goodsDO.getFirstCategoryIds().split(",");

                StringBuilder csb = new StringBuilder();
                for (String s : split) {
                    if(!categoryNameMap.containsKey(s)){
                        //从odps上查分类名字
                        String categoryName = getCategoryName(dsStr,s);
                        csb.append(",").append(categoryName);
                        categoryNameMap.put(s,categoryName);
                    }else{
                        csb.append(",").append(categoryNameMap.get(s));
                    }
                }
                goodsDO.setFirstCategoryNames(csb.deleteCharAt(0).toString());
            }else{
                String categoryName = categoryNameMap.get(goodsDO.getFirstCategoryIds());
                goodsDO.setFirstCategoryNames(categoryName);
            }
            if(StringUtils.isNotEmpty(goodsDO.getSecondCategoryIds())){
                split = goodsDO.getSecondCategoryIds().split(",");
                StringBuilder csb = new StringBuilder();
                for (String s : split) {
                    if(!categoryNameMap.containsKey(s)){
                        //从odps上查分类名字
                        String categoryName = getCategoryName(dsStr,s);
                        csb.append(",").append(categoryName);
                        categoryNameMap.put(s,categoryName);
                    }else{
                        csb.append(",").append(categoryNameMap.get(s));
                    }
                }
                goodsDO.setSecondCategoryNames(csb.deleteCharAt(0).toString());
            }
            Long goodsOrderCount = getGoodsOrderCount(dsStr, goodsDO.getItemId(), sTime, eTime);
            goodsDO.setOrderCount(goodsOrderCount);
            Long goodsSignCount = getGoodsSignCount(dsStr, goodsDO.getItemId(), sTime, eTime);
            goodsDO.setSignCount(goodsSignCount);
            Long goodsApprovedCount = getGoodsApprovedCount(dsStr, goodsDO.getItemId(), sTime, eTime);
            goodsDO.setApprovedCount(goodsApprovedCount);

            list.add(goodsDO);
        }
        return list;
    }




    /**
     * 查询分类
     * @param id
     * @return
     */
    public String getCategoryName(String dsStr,String id){
        if(StringUtils.isBlank(id)){
            return null;
        }
        StringBuilder csb = new StringBuilder(" select name from rent_category where id = ");
        csb.append(id);
        csb.append(" and ds = ").append(dsStr);
        csb.append(";");
        List<Record> recordList = odpsUtil.querySql(csb.toString());
        if(CollectionUtils.isEmpty(recordList)){
            return null;
        }
        return recordList.get(0).getString("name");
    }


    /**
     * 查询商品下单人数
     */
    private Long getGoodsOrderCount(String dsStr,Long itemId,String sTime,String eTime){
        StringBuilder sb = new StringBuilder(" select count(1) as num from rent_order a inner join rent_order_item b on a.id = b.order_id " +
                "where a.is_deleted = 0 and b.item_id = ");
        sb.append(itemId);
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if(sTime!=null){
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if(eTime!=null){
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if(CollectionUtils.isEmpty(recordList)){
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }

    /**
     * 签收单数
     * @param dsStr
     * @param itemId
     * @param sTime
     * @param eTime
     * @return
     */
    private Long getGoodsSignCount(String dsStr,Long itemId,String sTime,String eTime){
        StringBuilder sb = new StringBuilder(" select count(1) as num from  rent_order a inner" +
                " join rent_order_item b on a.id =b.order_id and b.item_type = 1 inner join rent_order_logistics c on b.logistics_id = c.id where" +
                " a.biz_type = 2 and a.merchant_id = 100 and a.payment_time is not null and c.sign_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and c.sign_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and c.sign_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" and b.item_id = ").append(itemId);
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if(CollectionUtils.isEmpty(recordList)){
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }


    /**
     * 风控通过人数
     * @param dsStr
     * @param itemId
     * @param sTime
     * @param eTime
     * @return
     */
    private Long getGoodsApprovedCount(String dsStr,Long itemId,String sTime,String eTime){
        StringBuilder sb = new StringBuilder(" select " +
                " count(a.customer_id) as num from rent_order a left join rent_order_audit b on a.id = b.order_id left join" +
                " rent_order_item c on a.id = c.order_id where a.biz_type = 2 and a.merchant_id = 100 and a.parent_id = 0 and b.type = 2" +
                " and b.audit_status = 1 and a.is_deleted = 0 and c.item_id = ");
        sb.append(itemId);
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and b.update_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and b.update_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if(CollectionUtils.isEmpty(recordList)){
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }


}