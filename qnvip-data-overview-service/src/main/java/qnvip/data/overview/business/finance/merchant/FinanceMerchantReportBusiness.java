// package qnvip.data.overview.business.finance.merchant;
//
// import cn.hutool.core.collection.CollUtil;
// import com.aliyun.odps.data.Record;
// import lombok.RequiredArgsConstructor;
// import org.apache.commons.lang3.StringUtils;
// import org.springframework.stereotype.Service;
// import qnvip.data.overview.domain.finance.FinanceMerchantReportDO;
// import qnvip.data.overview.enums.finance.FinanceTypeEnum;
// import qnvip.data.overview.service.finance.FinanceMerchantOrderDetailService;
// import qnvip.data.overview.service.finance.FinanceMerchantReportService;
// import qnvip.data.overview.service.finance.FinanceReportService;
// import qnvip.data.overview.util.CalculateUtil;
// import qnvip.data.overview.util.DateUtils;
// import qnvip.data.overview.util.OdpsUtil;
//
// import java.math.BigDecimal;
// import java.math.RoundingMode;
// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.LocalTime;
// import java.time.format.DateTimeFormatter;
// import java.util.*;
// import java.util.stream.Collectors;
//
// /**
//  * create by gw on 2022/2/28
//  */
// @Service
// @RequiredArgsConstructor
// public class FinanceMerchantReportBusiness {
//
//     /**
//      * 15:已签收 200:归还中 210:已归还 220:已买断 310:租用中 320:已续租 330:续租完成
//      */
//     public static final String EFFECTIVE_ORDER_STATUS = "15,200,210,220,310,320,330";
//     public static final String EXCEPT_FINANCE_TYPE = "17,18";
//     public static final String EXCEPT_MINI_TYPE = "-1";
//
//     private final OdpsUtil odpsUtil;
//     private final FinanceMerchantReportService financeReportBusiness;
//
//     private static final DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//
//
//     public void execCount(String ds) {
//         Map<String, FinanceMerchantReportDO> collectMap = new HashMap<>();
//         String testerIds = getTesterIds(ds);
//         countTask1(ds, testerIds, collectMap);
//         countTask2(ds, testerIds, collectMap);
//         countTask3(ds, testerIds, collectMap);
//         countTask4(ds, testerIds, collectMap);
//         countTask5(ds, testerIds, collectMap);
//         countTask6(ds, testerIds, collectMap);
//         countTask7(ds, testerIds, collectMap);
//         countTask8(ds, testerIds, collectMap);
//         countTask9(ds, testerIds, collectMap);
//         countTask10(ds, testerIds, collectMap);
//         countTask11(ds, testerIds, collectMap);
//         countTask12(ds, testerIds, collectMap);
//         countTask13(ds, testerIds, collectMap);
//         countTask14(ds, testerIds, collectMap);
//         countTask15(ds, testerIds, collectMap);
//         countTask16(ds, testerIds, collectMap);
//         countTask17(ds, testerIds, collectMap);
//         countTask18(ds, testerIds, collectMap);
//         financeReportBusiness.deleteAll();
//         financeReportBusiness.saveBatch(collectMap.values());
//     }
//
//
//
//     /**
//      * 到期买断金(基础项),归还本金(已收买断金)
//      *
//      * @param ds
//      */
//     private void countTask1(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //3+N买断金为0
//         String sql = " select date(a.rent_start_date)                                     day,  " +
//                 "           date(e.rent_start_date)                                       parent_day," +
//                 "           a.mini_type,  " +
//                 "           c.rate_config_type,  " +
//                 "           IF(a.parent_id > 0, 1, 0)                                     isrenew,  " +
//                 "           IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')   forcedconversion,  " +
//                 "           sum(if(c.rate_config_type!=10 and a.status in (200,210) and a.parent_id = 0," +
//                 "                                       c.buyout_amt,0))                  return_capital," +
//                 "           sum(if(c.rate_config_type!=10 and a.status in (200,210) and a.parent_id = 0,1,0)) " +
//                 "                                                                         return_capital_count," +
//                 "           sum(if(c.rate_config_type!=10,c.buyout_amt,0))                buyout_amt, " +
//                 "           sum(if(c.rate_config_type!=10,1,0))                           count  " +
//                 "    from rent_order a  " +
//                 "             inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and " +
//                 "               b.type_class != 1  " +
//                 "             inner join rent_order_finance_detail c on a.id = c.order_id  " +
//                 "             left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) e on e.id = a.parent_id " +
//                 "    where a.merchant_id = 100  " +
//                 "      and a.payment_time is not null  " +
//                 "      and a.termination != 5  " +
//                 "      and a.biz_type = 2  " +
//                 "      and a.type = 1  " +
//                 "      and a.customer_id not in (  " +
//                 testerIds +
//                 "        )  " +
//                 "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ")  " +
//                 "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ")  " +
//                 "      and a.is_deleted = 0  " +
//                 "      and b.is_deleted = 0  " +
//                 "      and c.is_deleted = 0  " +
//                 "      and a.ds = " + ds +
//                 "      and b.ds = " + ds +
//                 "      and c.ds = " + ds +
//                 "    group by date(a.rent_start_date),  " +
//                 "             date(e.rent_start_date),  " +
//                 "             a.mini_type,  " +
//                 "             IF(a.parent_id > 0, 1, 0),  " +
//                 "             IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'),  " +
//                 "             c.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             BigDecimal returnCapital = stringToDecimal(record.getString("return_capital"));
//             Integer returnCount = Integer.valueOf(record.getString("return_capital_count"));
//             BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//
//             FinanceMerchantReportDO.setBaseBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getBaseBuyoutAmt(), buyoutAmt));
//             FinanceMerchantReportDO.setBaseBuyoutCount(FinanceMerchantReportDO.getBaseBuyoutCount() + count);
//
//             FinanceMerchantReportDO.setBuyoutReturnCapitalAmt(CalculateUtil.add(FinanceMerchantReportDO.getBuyoutReturnCapitalAmt(),
//                     returnCapital));
//             FinanceMerchantReportDO.setBuyoutReturnCapitalCount(FinanceMerchantReportDO.getBuyoutReturnCapitalCount() + returnCount);
//
//         }
//
//
//     }
//
//
//     /**
//      * 真实采购价(基础项) 收取保证金(基础项) 协议总租金(基础项)
//      *
//      * @param ds
//      */
//     private void countTask2(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //3+N总租金=12期租金之和
//         String sql = "   select date(a.rent_start_date)                                 day, " +
//                 "           date(k.rent_start_date)                                     parent_day," +
//                 "           a.mini_type, " +
//                 "           c.rate_config_type, " +
//                 "           IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "           IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')  forcedconversion, " +
//                 "           sum(b.actual_supply_price)                                  actual_supply_price, " +
//                 "           sum(if(c.act_bond_amt>0,c.act_bond_amt,if(f.act_bond_amt>0,f.act_bond_amt,e.act_bond_amt)" +
//                 "                   ))  bond_amt, " +
//                 "           sum(if(c.rate_config_type != 10,c.actual_financing_amt,d.capital*12))    rent_total, " +
//                 "           count(1)                                                    count " +
//                 "    from rent_order a " +
//                 "             inner join rent_order_item b on a.id = b.order_id " +
//                 "                                                 and b.is_deleted = 0 " +
//                 "                                                 and b.item_type = 1 " +
//                 "                                                 and b.type_class != 1 " +
//                 "                                                 and b.ds = " + ds +
//                 "             inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "                                                           and c.is_deleted = 0 " +
//                 "                                                           and c.ds = " + ds +
//                 "             inner join (select order_id,avg(capital) capital from rent_order_repayment_plan  " +
//                 "                                                 where is_deleted = 0 " +
//                 "                                                    and ds = " + ds + " group by order_id) d " +
//                 "                                                    on a.id = d.order_id" +
//                 "             left join rent_order_finance_detail f on a.parent_id = f.order_id and f.ds = " + ds +
//                 "             left join ( select x.parent_id, y.act_bond_amt " +
//                 "                           from rent_order x" +
//                 "                           inner join rent_order_finance_detail y on x.id = y.order_id" +
//                 "                           where x.ds = " + ds + " and y.ds=" + ds + " ) e on e.parent_id = a.id " +
//                 "             left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "    where a.merchant_id = 100 " +
//                 "      and a.payment_time is not null " +
//                 "      and a.termination != 5 " +
//                 "      and a.biz_type = 2 " +
//                 "      and a.type = 1 " +
//                 "      and a.customer_id not in (" + testerIds + ") " +
//                 "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "      and a.is_deleted = 0 " +
//                 "      and a.ds = " + ds +
//                 "    group by date(a.rent_start_date), a.mini_type, " +
//                 "             date(k.rent_start_date), " +
//                 "             IF(a.parent_id > 0, 1, 0), " +
//                 "             IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'), " +
//                 "             c.rate_config_type;  ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             BigDecimal actualSupplyPrice = stringToDecimal(record.getString("actual_supply_price"));
//             BigDecimal bondAmt = stringToDecimal(record.getString("bond_amt"));
//             BigDecimal rentTotal = stringToDecimal(record.getString("rent_total"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setBaseActSupplyPrice(CalculateUtil.add(FinanceMerchantReportDO.getBaseActSupplyPrice(),
//                     actualSupplyPrice));
//             FinanceMerchantReportDO.setBaseActSupplyPriceCount(FinanceMerchantReportDO.getBaseActSupplyPriceCount() + count);
//
//             FinanceMerchantReportDO.setBaseBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getBaseBondAmt(), bondAmt));
//             FinanceMerchantReportDO.setBaseBondCount(FinanceMerchantReportDO.getBaseBondCount() + count);
//
//             FinanceMerchantReportDO.setBaseRentAmt(CalculateUtil.add(FinanceMerchantReportDO.getBaseRentAmt(), rentTotal));
//             FinanceMerchantReportDO.setBaseRentCount(FinanceMerchantReportDO.getBaseRentCount() + count);
//
//         }
//
//     }
//
//
//     /**
//      * 已收月租(已收租金)
//      *
//      * @param ds
//      */
//     private void countTask3(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //12+N
//         String sql = " select date(a.rent_start_date)                                            day, " +
//                 "           date(k.rent_start_date)                                     parent_day," +
//                 "           a.mini_type, " +
//                 "           c.rate_config_type, " +
//                 "           IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "           IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')  forcedconversion, " +
//                 "           sum(b.real_repay_capital)                  real_repay_capital, " +
//                 //3+N买断时的已收买断金也算已收租金
//                 "           sum(if(c.rate_config_type=10 and a.status=220,c.actual_buyout_amt,0))   rent_buyout_amt," +
//                 "           sum(if(c.rate_config_type!=10 and a.status in (200,210)," +
//                 "                        c.actual_financing_amt-e.already_repay_capital,0)) " +
//                 "                                               rent_return_discount," +
//                 "           sum(if(c.rate_config_type!=10 and a.status in (200,210) " +
//                 "                   and c.actual_financing_amt>e.already_repay_capital,1,0))   " +
//                 "                                               rent_return_discount_count," +
//                 "           count(distinct a.id)                                        count " +
//                 "    from rent_order a " +
//                 "             inner join (select order_id,sum(real_repay_capital) real_repay_capital,max(term) term" +
//                 "                                        from rent_order_repayment_plan " +
//                 "                                                   where is_deleted = 0 " +
//                 "                                                   and repay_status = 5" +
//                 "                                                   and ds = " + ds +
//                 "                                   group by order_id) b on a.id = b.order_id" +
//                 "             inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "                                                           and c.is_deleted = 0" +
//                 "                                                           and c.ds = " + ds +
//                 "             inner join rent_order_item d on a.id = d.order_id  " +
//                 "                                                   and d.item_type = 1  " +
//                 "                                                   and d.is_deleted = 0  " +
//                 "                                                   and d.type_class != 1  " +
//                 "                                                   and d.ds = " + ds +
//                 "             left join (select order_id,sum(capital) already_repay_capital" +
//                 "                                        from rent_order_repayment_plan " +
//                 "                                                   where repay_status = 5" +
//                 "                                                   and is_deleted = 0" +
//                 "                                                   and ds = " + ds +
//                 "                                   group by order_id) e on a.id = e.order_id" +
//                 "             left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "    where a.is_deleted = 0 " +
//                 "      and a.merchant_id = 100 " +
//                 "      and a.payment_time is not null " +
//                 "      and a.termination != 5 " +
//                 "      and a.biz_type = 2 " +
//                 "      and a.type = 1 " +
//                 "      and a.customer_id not in (" + testerIds + ") " +
//                 "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "      and a.is_deleted = 0 " +
//                 "      and a.ds = " + ds +
//                 "    group by date(a.rent_start_date), a.mini_type, " +
//                 "             date(k.rent_start_date),  " +
//                 "             IF(a.parent_id > 0, 1, 0), " +
//                 "             IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'), " +
//                 "             c.rate_config_type;  ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal realRepayCapital = stringToDecimal(record.getString("real_repay_capital"));
//             BigDecimal rentBuyoutAmt = stringToDecimal(record.getString("rent_buyout_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             BigDecimal rentReturnDiscount = stringToDecimal(record.getString("rent_return_discount"));
//             Integer rentReturnDiscountCount = Integer.valueOf(record.getString("rent_return_discount_count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setRentReceiveRent(CalculateUtil.add(FinanceMerchantReportDO.getRentReceiveRent(),
//                     CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
//             FinanceMerchantReportDO.setRentReceiveRentCount(FinanceMerchantReportDO.getRentReceiveRentCount() + count);
//             //针对非3+X的订单，有的账单没有还完，用户要归还。对这种已归还的订单，就存在一部分未收租金，将该未收部分的租金统计为归还优惠
//             FinanceMerchantReportDO.setRentReturnDiscount(CalculateUtil.add(FinanceMerchantReportDO.getRentReturnDiscount(),
//                     rentReturnDiscount));
//             FinanceMerchantReportDO.setRentReturnDiscountCount(FinanceMerchantReportDO.getRentReturnDiscountCount() + rentReturnDiscountCount);
//         }
//
//
//     }
//
//
//     /**
//      * 续租本金(已收买断金或续租报表) 转续租(实收保证金)
//      *
//      * @param ds
//      */
//     private void countTask4(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //12+n
//         String sql1 = " select date(c.rent_start_date)                                                     day, " +
//                 "           date(a.rent_start_date)                                     parent_day," +
//                 "           c.mini_type, " +
//                 "           c.rate_config_type, " +
//                 "           IF(c.parent_id > 0, 1, 0) isrenew, " +
//                 "           IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')          forcedconversion, " +
//                 "           sum(b.buyout_amt)                                                           buyout_amt, " +
//                 "           sum(c.act_bond_amt)                                                     act_bond_amt," +
//                 "           count(1)                                                                    count " +
//                 "    from rent_order a " +
//                 "             inner join (select x.parent_id, " +
//                 "                                x.rent_start_date, " +
//                 "                                x.mini_type, " +
//                 "                                y.rate_config_type, " +
//                 "                                y.renew_term, " +
//                 "                                y.repayment_term, " +
//                 "                                y.ext_json, " +
//                 "                                y.act_bond_amt " +
//                 "                         from rent_order x " +
//                 "                                  inner join rent_order_finance_detail y on x.id = y.order_id " +
//                 "                         where x.merchant_id = 100 " +
//                 "                           and x.payment_time is not null " +
//                 "                           and x.termination != 5 " +
//                 "                           and x.biz_type = 2 " +
//                 "                           and x.type = 1 " +
//                 "                           and x.parent_id > 0 " +
//                 "                           and x.customer_id not in (" + testerIds + ") " +
//                 "                           and x.is_deleted = 0 " +
//                 "                           and x.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "                           and x.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "                           and x.ds = " + ds +
//                 "                           and y.ds = " + ds + ") c on a.id = c.parent_id " +
//                 "             inner join rent_order_finance_detail b on a.id = b.order_id " +
//                 "                                                           and b.is_deleted = 0 " +
//                 "                                                           and b.ds = " + ds +
//                 "             inner join rent_order_item d " +
//                 "                        on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 and " +
//                 "                           d.type_class != 1  " +
//                 "                           and d.ds = " + ds +
//                 "    where a.merchant_id = 100 " +
//                 "      and a.payment_time is not null " +
//                 "      and a.termination != 5 " +
//                 "      and a.biz_type = 2 " +
//                 "      and a.type = 1 " +
//                 "      and a.customer_id not in (" + testerIds + ") " +
//                 "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "      and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "      and b.rate_config_type != 10 " +
//                 "      and a.is_deleted = 0 " +
//                 "      and a.ds = " + ds +
//                 "    group by date(c.rent_start_date),date(a.rent_start_date), c.mini_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10,'1', '0'), IF(c.parent_id > 0, 1, 0)," +
//                 "        c.rate_config_type;  ";
//         List<Record> records1 = odpsUtil.querySql(sql1);
//         for (Record record : records1) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
//             BigDecimal actBondAmt = stringToDecimal(record.getString("act_bond_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, 1, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setBuyoutRenewPrincipal(CalculateUtil.add(buyoutAmt,
//                     FinanceMerchantReportDO.getBuyoutRenewPrincipal()));
//             FinanceMerchantReportDO.setBuyoutRenewPrincipalCount(count + FinanceMerchantReportDO.getBuyoutRenewPrincipalCount());
//
//             FinanceMerchantReportDO.setBondRenewAmt(CalculateUtil.add(actBondAmt, FinanceMerchantReportDO.getBondRenewAmt()));
//             FinanceMerchantReportDO.setBondRenewCount(count + FinanceMerchantReportDO.getBondRenewCount());
//
//             //给主订单续租本金赋值
//             Integer extraFinanceType = FinanceTypeEnum.reverseFinanceType(financeType);
//             if (extraFinanceType != null) {
//                 initMap(collectMap, miniType, extraFinanceType, parentDay, null);
//                 FinanceMerchantReportDO extraFinanceMerchantReportDO = collectMap.get(getMapKey(miniType, extraFinanceType, parentDay
//                         , null));
//                 extraFinanceMerchantReportDO.setBuyoutRenewPrincipal(CalculateUtil.add(extraFinanceMerchantReportDO.getBuyoutRenewPrincipal(), buyoutAmt));
//                 extraFinanceMerchantReportDO.setBuyoutRenewPrincipalCount(extraFinanceMerchantReportDO.getBuyoutRenewPrincipalCount() + count);
//                 extraFinanceMerchantReportDO.setBondRenewAmt(CalculateUtil.add(extraFinanceMerchantReportDO.getBondRenewAmt(),
//                         actBondAmt));
//                 extraFinanceMerchantReportDO.setBondRenewCount(extraFinanceMerchantReportDO.getBondRenewCount() + count);
//             }
//
//         }
//
//     }
//
//
//
//     /**
//      * 保证金抵扣买断金、保证金抵扣租金(已收买断金)
//      *
//      * @param ds
//      */
//     private void countTask5(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "       IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "       sum(if(plan_deduction_amount2 is null,plan_deduction_amount1,plan_deduction_amount2)) " +
//                 "           plan_deduction_amount1, " +
//                 "       sum(if(plan_deduction_amount1 is not null or plan_deduction_amount2 is not null,1,0))" +
//                 "           plan_count1, " +
//                 "       sum(if(c.rate_config_type = 10,if(buyout_deduction_amount2 is null,buyout_deduction_amount1," +
//                 "               buyout_deduction_amount2),0)) plan_deduction_amount2, " +
//                 "       sum(if(c.rate_config_type = 10,if(buyout_deduction_amount1 is not null or " +
//                 "               buyout_deduction_amount2 is not null,1,0),0)) plan_count2, " +
//                 "       sum(if(c.rate_config_type != 10,if(buyout_deduction_amount2 is null,buyout_deduction_amount1," +
//                 "               buyout_deduction_amount2),0)) buyout_deduction_amount, " +
//                 "       sum(if(c.rate_config_type != 10,if(buyout_deduction_amount1 is not null or " +
//                 "               buyout_deduction_amount2 is not null,1,0),0)) buyout_count, " +
//                 "       sum(return_damage_amt) return_damage_amt, " +
//                 "       sum(if(return_damage_amt>0,1,0)) return_damage_count " +
//                 " from rent_order a " +
//                 "       inner join (select sum(if(biz_type=3,get_json_object(ext,'$.planDeductionAmount'),null)) " +
//                 "                     plan_deduction_amount1," +
//                 "                   sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=1,flow_amt,null))" +
//                 "                     plan_deduction_amount2," +
//                 "                   sum(if(biz_type=3,get_json_object(ext," +
//                 "                           '$.buyoutDeductionAmount'),null)) buyout_deduction_amount1," +
//                 "                   sum(if(biz_type=23 and get_json_object(ext," +
//                 "                           '$.deductionType')=3,flow_amt,null))  buyout_deduction_amount2," +
//                 "                   sum(if(biz_type=2,flow_amt,0))  return_damage_amt," +
//                 "               order_id from rent_order_flow  " +
//                 "                                   where biz_type in (3,23) " +
//                 "                                   and pay_status = 10 " +
//                 "                                   and flow_type =1 " +
//                 "                                   and (mark_refund is null or mark_refund = 0) " +
//                 "                                   and refunded_amt = 0 " +
//                 "                                   and ext is not null " +
//                 "                                   and ext != '' " +
//                 "                                   and is_deleted = 0 " +
//                 "                                   and ds = " + ds +
//                 "                          group by order_id) b on a.id = b.order_id" +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                                   and d.type_class != 1 and d.ds = " + ds +
//                 "             left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( "
//                 + testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and c.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 "  and c.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')," +
//                 "         c.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal planDeductionAmount1 = stringToDecimal(record.getString("plan_deduction_amount1"));
//             Integer planCount1 = Integer.valueOf(record.getString("plan_count1"));
//             BigDecimal planDeductionAmount2 = stringToDecimal(record.getString("plan_deduction_amount2"));
//             Integer planCount2 = Integer.valueOf(record.getString("plan_count2"));
//             BigDecimal buyoutDeductionAmount = stringToDecimal(record.getString("buyout_deduction_amount"));
//             Integer buyoutCount = Integer.valueOf(record.getString("buyout_count"));
//             BigDecimal returnDamageAmt = stringToDecimal(record.getString("return_damage_amt"));
//             Integer returnDamageCount = Integer.valueOf(record.getString("return_damage_count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setRentDeductBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getRentDeductBondAmt(),
//                     CalculateUtil.add(planDeductionAmount1, planDeductionAmount2)));
//             FinanceMerchantReportDO.setRentDeductBondCount(FinanceMerchantReportDO.getRentDeductBondCount() + planCount1 + planCount2);
//
//             FinanceMerchantReportDO.setBuyoutDeductBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getBuyoutDeductBuyoutAmt(),
//                     buyoutDeductionAmount));
//             FinanceMerchantReportDO.setBuyoutDeductBuyoutCount(FinanceMerchantReportDO.getBuyoutDeductBuyoutCount() + buyoutCount);
//
//             FinanceMerchantReportDO.setEndReturnDamageAmt(CalculateUtil.add(FinanceMerchantReportDO.getEndReturnDamageAmt(),returnDamageAmt));
//             FinanceMerchantReportDO.setEndReturnDamageCount(FinanceMerchantReportDO.getEndReturnDamageCount()+returnDamageCount);
//         }
//
//     }
//
//
//     /**
//      * 逾期滞纳金(其他收入)
//      *
//      * @param ds
//      */
//     private void countTask6(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //12+N
//         String sql = " select date(a.rent_start_date)                                 day, " +
//                 "       date(k.rent_start_date)                                       parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "       IF(a.parent_id > 0, 1, 0)                                   isrenew, " +
//                 "       sum(b.real_overdue_fine) real_overdue_fine, " +
//                 "       count(distinct a.id)                                                      count " +
//                 "from rent_order a " +
//                 "         inner join rent_order_repayment_plan b on a.id = b.order_id " +
//                 "                                           and b.real_overdue_fine > 0 " +
//                 "                                           and b.is_deleted = 0 " +
//                 "                                           and b.ds = " + ds +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "                                           and c.is_deleted = 0 " +
//                 "                                           and c.ds = " + ds +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                                           and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( "
//                 + testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')," +
//                 "         c.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal realOverdueFine = stringToDecimal(record.getString("real_overdue_fine"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setOtherOverdueAmt(CalculateUtil.add(FinanceMerchantReportDO.getOtherOverdueAmt(),
//                     realOverdueFine));
//             FinanceMerchantReportDO.setOtherOverdueCount(FinanceMerchantReportDO.getOtherOverdueCount() + count);
//         }
//
//
//     }
//
//
//     /**
//      * 结清/归还退回(保证金)
//      *
//      * @param ds
//      */
//     private void countTask7(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                 day, " +
//                 "       date(k.rent_start_date)                                             parent_day," +
//                 "       a.mini_type, " +
//                 "       IF(a.status in (200,210),1,0) isreturn," +
//                 "       b.rate_config_type, " +
//                 "       IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "       IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "       sum(if(b.bond_refund_time is not null,b.bond_refund_amount,0)) bond_refund_amount, " +
//                 "       sum(if(b.bond_refund_time is not null,1,0))                   refund_count, " +
//                 "       sum(if(a.status in (200,210,220,330) and b.bond_refund_time is null,b.bond_refund_amount,0)) " +
//                 "                               surplus_refund_amount, " +
//                 "       sum(if(a.status in (200,210,220,330) and b.bond_refund_time is null and" +
//                 "                               b.bond_refund_amount>0,1,0)) surplus_count " +
//                 " from rent_order a " +
//                 "         inner join rent_order_finance_detail b on a.id = b.order_id " +
//                 "                                           and b.is_deleted = 0 " +
//                 "                                           and b.ds = " + ds +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                                           and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( "
//                 + testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and a.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0')," +
//                 //是否归还
//                 "         IF(a.status in (200,210),1,0), " +
//                 "         b.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             Integer isReturn = Integer.valueOf(record.getString("isreturn"));
//             BigDecimal bondRefundAmount = stringToDecimal(record.getString("bond_refund_amount"));
//             Integer refundCount = Integer.valueOf(record.getString("refund_count"));
//
//             BigDecimal surplusRefundAmount = stringToDecimal(record.getString("surplus_refund_amount"));
//             Integer surplusCount = Integer.valueOf(record.getString("surplus_count"));
//
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             if (isReturn == 1) {
//                 FinanceMerchantReportDO.setBondReturnBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getBondReturnBackBondAmt(),
//                         bondRefundAmount));
//                 FinanceMerchantReportDO.setBondReturnBackBondCount(FinanceMerchantReportDO.getBondReturnBackBondCount() + refundCount);
//             } else {
//                 FinanceMerchantReportDO.setBondBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getBondBackBondAmt(),
//                         bondRefundAmount));
//                 FinanceMerchantReportDO.setBondBackBondCount(FinanceMerchantReportDO.getBondBackBondCount() + refundCount);
//             }
//
//             //结清未退保证金
//             FinanceMerchantReportDO.setBondNotBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getBondNotBackBondAmt(),
//                     surplusRefundAmount));
//             FinanceMerchantReportDO.setBondNotBackBondCount(FinanceMerchantReportDO.getBondNotBackBondCount() + surplusCount);
//         }
//
//     }
//
//
//     /**
//      * 未退保证金(所有逾期)
//      *
//      * @param ds
//      */
//     private void countTask8(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select e.day, " +
//                 "       e.parent_day, " +
//                 "       e.level, " +
//                 "       e.mini_type, " +
//                 "       e.isrenew, " +
//                 "       e.forcedconversion," +
//                 "       e.rate_config_type, " +
//                 "       sum(e.bond_amt)       bond_amt, " +
//                 "       sum(e.count)          count " +
//                 "from ( " +
//                 "         select date(a.rent_start_date)   day, " +
//                 "                date(k.rent_start_date)   parent_day, " +
//                 "                a.mini_type, " +
//                 "                b.rate_config_type," +
//                 "                IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "                IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "                sum(b.surplus_bond_Amt)            bond_amt, " +
//                 "                sum(if(b.bond_refund_time is null and b.surplus_bond_Amt>0,1,0))       count, " +
//                 "                (case " +
//                 "                     when c.current_overdue_days = 0 then 0 " +
//                 "                     when c.current_overdue_days <= 7 then 1 " +
//                 "                     when c.current_overdue_days <= 31 then 2 " +
//                 "                     else 3 end) as       level " +
//                 "         from rent_order a " +
//                 "                  inner join rent_order_finance_detail b on a.id = b.order_id " +
//                 "                  inner join rent_order_overdue_stat c on a.id = c.order_id " +
//                 "                  inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and " +
//                 "                                     d.is_deleted = 0 and d.type_class != 1 and d.ds = " + ds +
//                 "                  left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "         where a.merchant_id = 100 " +
//                 "           and a.payment_time is not null " +
//                 "           and a.termination != 5 " +
//                 "           and a.biz_type = 2 " +
//                 "           and a.type = 1 " +
//                 "           and a.customer_id not in ( "
//                 + testerIds +
//                 "             ) " +
//                 "           and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "           and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "           and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "           and a.is_deleted = 0 " +
//                 "           and b.is_deleted = 0 " +
//                 "           and c.is_deleted = 0 " +
//                 "           and a.ds = " + ds +
//                 "           and b.ds = " + ds +
//                 "           and c.ds = " + ds +
//                 "         group by date(a.rent_start_date), " +
//                 "                  date(k.rent_start_date)," +
//                 "                  a.mini_type, " +
//                 "                  IF(a.parent_id > 0, 1, 0), " +
//                 "                  b.rate_config_type, " +
//                 "                  IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0')," +
//                 "                  c.current_overdue_days " +
//                 "     ) e " +
//                 " group by e.level, " +
//                 "         e.day, " +
//                 "         e.parent_day, " +
//                 "         e.mini_type, " +
//                 "         e.isrenew, " +
//                 "         e.forcedconversion," +
//                 "         e.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer level = Integer.valueOf(record.getString("level"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal bondAmt = stringToDecimal(record.getString("bond_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             switch (level) {
//                 case 0:
//                     FinanceMerchantReportDO.setOverZeroNotBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverZeroNotBackBondAmt(), bondAmt));
//                     FinanceMerchantReportDO.setOverZeroNotBackBondCount(FinanceMerchantReportDO.getOverZeroNotBackBondCount() + count);
//                     break;
//                 case 1:
//                     FinanceMerchantReportDO.setOverFirstNotBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverFirstNotBackBondAmt(), bondAmt));
//                     FinanceMerchantReportDO.setOverFirstNotBackBondCount(FinanceMerchantReportDO.getOverFirstNotBackBondCount() + count);
//                     break;
//                 case 2:
//                     FinanceMerchantReportDO.setOverSecondNotBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverSecondNotBackBondAmt(), bondAmt));
//                     FinanceMerchantReportDO.setOverSecondNotBackBondCount(FinanceMerchantReportDO.getOverSecondNotBackBondCount() + count);
//                     break;
//                 case 3:
//                     FinanceMerchantReportDO.setOverThirdNotBackBondAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverThirdNotBackBondAmt(), bondAmt));
//                     FinanceMerchantReportDO.setOverThirdNotBackBondCount(FinanceMerchantReportDO.getOverThirdNotBackBondCount() + count);
//                     break;
//                 default:
//                     break;
//             }
//         }
//
//     }
//
//
//
//     /**
//      * 到期买断金(所有逾期)
//      *
//      * @param ds
//      */
//     private void countTask9(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //12+N
//         String sql = " select e.day, " +
//                 "       e.parent_day, " +
//                 "       e.level, " +
//                 "       e.mini_type, " +
//                 "       e.isrenew, " +
//                 "       e.forcedconversion, " +
//                 "       e.rate_config_type, " +
//                 "       sum(e.buyout_amt) buyout_amt, " +
//                 "       sum(e.count)      count " +
//                 "  from (select date(a.rent_start_date)                                                     day, " +
//                 "             date(k.rent_start_date)                                                  parent_day," +
//                 "             a.mini_type, " +
//                 "             b.rate_config_type, " +
//                 "             IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0') forcedconversion, " +
//                 "             IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "             sum(if(b.rate_config_type != 10 and a.status in (15, 310) " +
//                 "                                       and j.parent_id is null, b.buyout_amt,0)) buyout_amt," +
//                 "             sum(if(b.rate_config_type != 10 and a.status in (15, 310) " +
//                 "                                       and j.parent_id is null, 1, 0)) count, " +
//                 "             (case " +
//                 "                  when c.current_overdue_days = 0 then 0 " +
//                 "                  when c.current_overdue_days <= 7 then 1 " +
//                 "                  when c.current_overdue_days <= 31 then 2 " +
//                 "                  else 3 end)                                           as               level " +
//                 "      from rent_order a " +
//                 "               inner join rent_order_finance_detail b on a.id = b.order_id " +
//                 "               inner join rent_order_overdue_stat c on a.id = c.order_id " +
//                 "               inner join rent_order_item h on a.id = h.order_id and h.item_type = 1 " +
//                 "                                                   and h.is_deleted = 0 and h.type_class != 1 " +
//                 "                                                   and h.ds = " + ds +
//                 "               left join ( select parent_id from rent_order " +
//                 "                           where ds = " + ds + " ) j on j.parent_id = a.id " +
//                 "               left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "      where a.merchant_id = 100 " +
//                 "        and a.payment_time is not null " +
//                 "        and a.termination != 5 " +
//                 "        and a.biz_type = 2 " +
//                 "        and a.type = 1 " +
//                 "        and a.customer_id not in (" + testerIds + ") " +
//                 "        and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "        and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "        and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "        and a.is_deleted = 0 " +
//                 "        and b.is_deleted = 0 " +
//                 "        and c.is_deleted = 0 " +
//                 "        and a.ds = " + ds +
//                 "        and b.ds = " + ds +
//                 "        and c.ds = " + ds +
//                 "      group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type," +
//                 "               IF(a.parent_id > 0, 1,0),b.rate_config_type, " +
//                 "               IF(!ISNULL(b.ext_json) and b.rate_config_type=10,'1', '0'), c.current_overdue_days) e" +
//                 "   group by e.level, e.day,e.parent_day, e.mini_type, e.isrenew," +
//                 "            e.forcedconversion, e.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer level = Integer.valueOf(record.getString("level"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             switch (level) {
//                 case 0:
//                     FinanceMerchantReportDO.setOverZeroBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverZeroBuyoutAmt(),
//                             buyoutAmt));
//                     FinanceMerchantReportDO.setOverZeroBuyoutCount(FinanceMerchantReportDO.getOverZeroBuyoutCount() + count);
//                     break;
//                 case 1:
//                     FinanceMerchantReportDO.setOverFirstBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverFirstBuyoutAmt(),
//                             buyoutAmt));
//                     FinanceMerchantReportDO.setOverFirstBuyoutCount(FinanceMerchantReportDO.getOverFirstBuyoutCount() + count);
//                     break;
//                 case 2:
//                     FinanceMerchantReportDO.setOverSecondBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverSecondBuyoutAmt()
//                             , buyoutAmt));
//                     FinanceMerchantReportDO.setOverSecondBuyoutCount(FinanceMerchantReportDO.getOverSecondBuyoutCount() + count);
//                     break;
//                 case 3:
//                     FinanceMerchantReportDO.setOverThirdBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverThirdBuyoutAmt(),
//                             buyoutAmt));
//                     FinanceMerchantReportDO.setOverThirdBuyoutCount(FinanceMerchantReportDO.getOverThirdBuyoutCount() + count);
//                     break;
//                 default:
//                     break;
//             }
//         }
//
//
//     }
//
//
//     /**
//      * 逾期滞纳金(所有逾期)
//      *
//      * @param ds
//      */
//     private void countTask10(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select e.day, " +
//                 "       e.parent_day, " +
//                 "       e.level, " +
//                 "       e.isrenew, " +
//                 "       e.mini_type, " +
//                 "       e.forcedconversion," +
//                 "       e.rate_config_type, " +
//                 "       sum(e.overdue_fine)     overdue_fine, " +
//                 "       sum(e.count)            count " +
//                 "from ( " +
//                 "         select date(a.rent_start_date)   day, " +
//                 "                date(k.rent_start_date)   parent_day," +
//                 "                a.mini_type, " +
//                 "                b.rate_config_type, " +
//                 "                IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "                IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "                sum(d.overdue_fine)       overdue_fine, " +
//                 "                count(distinct a.id)      count, " +
//                 "                (case " +
//                 "                     when c.current_overdue_days = 0 then 0 " +
//                 "                     when c.current_overdue_days <= 7 then 1 " +
//                 "                     when c.current_overdue_days <= 31 then 2 " +
//                 "                     else 3 end) as       level " +
//                 "         from rent_order a " +
//                 "                  inner join rent_order_finance_detail b on a.id = b.order_id " +
//                 "                  inner join rent_order_overdue_stat c on a.id = c.order_id " +
//                 "                  inner join rent_order_item j on a.id = j.order_id and j.item_type = 1 " +
//                 "                     and j.is_deleted = 0  and j.type_class != 1 and j.ds = " + ds +
//                 "                  inner join rent_order_repayment_plan d on a.id = d.order_id " +
//                 "                  left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "         where a.merchant_id = 100 " +
//                 "           and a.payment_time is not null " +
//                 "           and a.termination != 5 " +
//                 "           and a.biz_type = 2 " +
//                 "           and a.type = 1 " +
//                 "           and a.customer_id not in ( "
//                 + testerIds +
//                 "             ) " +
//                 "           and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "           and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "           and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "           and d.repay_status != 5 " +
//                 "           and a.is_deleted = 0 " +
//                 "           and b.is_deleted = 0 " +
//                 "           and c.is_deleted = 0 " +
//                 "           and d.is_deleted = 0 " +
//                 "           and a.ds = " + ds +
//                 "           and b.ds = " + ds +
//                 "           and c.ds = " + ds +
//                 "           and d.ds = " + ds +
//                 "         group by date(a.rent_start_date), " +
//                 "                  date(k.rent_start_date)," +
//                 "                  a.mini_type, " +
//                 "                  IF(a.parent_id > 0, 1, 0), " +
//                 "                  b.rate_config_type , " +
//                 "                  IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0')," +
//                 "                  c.current_overdue_days " +
//                 "     ) e " +
//                 "group by e.level, " +
//                 "         e.parent_day, " +
//                 "         e.day, " +
//                 "         e.mini_type, " +
//                 "         e.isrenew, " +
//                 "         e.forcedconversion," +
//                 "         e.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer level = Integer.valueOf(record.getString("level"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal overdueFine = stringToDecimal(record.getString("overdue_fine"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             switch (level) {
//                 case 1:
//                     FinanceMerchantReportDO.setOverFirstOverDueAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverFirstOverDueAmt()
//                             , overdueFine));
//                     FinanceMerchantReportDO.setOverFirstOverDueCount(FinanceMerchantReportDO.getOverFirstOverDueCount() + count);
//                     break;
//                 case 2:
//                     FinanceMerchantReportDO.setOverSecondOverDueAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverSecondOverDueAmt(), overdueFine));
//                     FinanceMerchantReportDO.setOverSecondOverDueCount(FinanceMerchantReportDO.getOverSecondOverDueCount() + count);
//                     break;
//                 case 3:
//                     FinanceMerchantReportDO.setOverThirdOverDueAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverThirdOverDueAmt()
//                             , overdueFine));
//                     FinanceMerchantReportDO.setOverThirdOverDueCount(FinanceMerchantReportDO.getOverThirdOverDueCount() + count);
//                     break;
//                 default:
//                     break;
//             }
//         }
//
//     }
//
//
//     /**
//      * 剩余总租金(所有逾期)
//      *
//      * @param ds
//      */
//     private void countTask11(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         //12+N
//         String sql = " select e.day, " +
//                 "       e.parent_day," +
//                 "       e.level, " +
//                 "       e.isrenew, " +
//                 "       e.mini_type, " +
//                 "       e.forcedconversion, " +
//                 "       e.rate_config_type, " +
//                 "       sum(e.capital) capital, " +
//                 "       sum(e.count)   count " +
//                 " from (select date(a.rent_start_date)                                                     day, " +
//                 "             date(k.rent_start_date)   parent_day," +
//                 "             a.mini_type, " +
//                 "             b.rate_config_type, " +
//                 "             IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "             IF(!ISNULL(b.ext_json) and b.rate_config_type=10, '1', '0')   forcedconversion, " +
//                 "             sum(if(a.status not in (200,210),d.capital,0))                capital," +
//                 "             count(distinct a.id)                                                        count, " +
//                 "             (case " +
//                 "                  when c.current_overdue_days = 0 then 0 " +
//                 "                  when c.current_overdue_days <= 7 then 1 " +
//                 "                  when c.current_overdue_days <= 31 then 2 " +
//                 "                  else 3 end)                                           as               level " +
//                 "      from rent_order a " +
//                 "               inner join rent_order_finance_detail b on a.id = b.order_id  " +
//                 "                                                             and b.is_deleted = 0 and b.ds = " + ds +
//                 "               inner join rent_order_overdue_stat c on a.id = c.order_id  " +
//                 "                                                           and c.is_deleted = 0 and c.ds = " + ds +
//                 "               inner join rent_order_item j on a.id = j.order_id  " +
//                 "                                                   and j.item_type = 1 and j.is_deleted = 0  " +
//                 "                                                   and j.type_class != 1 and j.ds = " + ds +
//                 "               left join rent_order_repayment_plan d on a.id = d.order_id  " +
//                 "                                                             and d.repay_status != 5  " +
//                 "                                                             and d.is_deleted = 0 and d.ds = " + ds +
//                 "               left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 "      where a.merchant_id = 100 " +
//                 "        and a.payment_time is not null " +
//                 "        and a.termination != 5 " +
//                 "        and a.biz_type = 2 " +
//                 "        and a.type = 1 " +
//                 "        and a.customer_id not in (" + testerIds + ") " +
//                 "        and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "        and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "        and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "        and a.is_deleted = 0 " +
//                 "        and a.ds = " + ds +
//                 "      group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
//                 "               IF(a.parent_id > 0, 1, 0), " +
//                 "               b.rate_config_type," +
//                 "               IF(!ISNULL(b.ext_json) and b.rate_config_type=10,'1', '0'), c.current_overdue_days) e" +
//                 " group by e.level, e.day,e.parent_day, e.mini_type, e.isrenew, " +
//                 "          e.forcedconversion, e.rate_config_type;  ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer level = Integer.valueOf(record.getString("level"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal capital = stringToDecimal(record.getString("capital"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             switch (level) {
//                 case 0:
//                     FinanceMerchantReportDO.setOverZeroSurplusRentAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverZeroSurplusRentAmt(), capital));
//                     FinanceMerchantReportDO.setOverZeroSurplusRentCount(FinanceMerchantReportDO.getOverZeroSurplusRentCount() + count);
//                     break;
//                 case 1:
//                     FinanceMerchantReportDO.setOverFirstSurplusRentAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverFirstSurplusRentAmt(), capital));
//                     FinanceMerchantReportDO.setOverFirstSurplusRentCount(FinanceMerchantReportDO.getOverFirstSurplusRentCount() + count);
//                     break;
//                 case 2:
//                     FinanceMerchantReportDO.setOverSecondSurplusRentAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverSecondSurplusRentAmt(), capital));
//                     FinanceMerchantReportDO.setOverSecondSurplusRentCount(FinanceMerchantReportDO.getOverSecondSurplusRentCount() + count);
//                     break;
//                 case 3:
//                     FinanceMerchantReportDO.setOverThirdSurplusRentAmt(CalculateUtil.add(FinanceMerchantReportDO.getOverThirdSurplusRentAmt(), capital));
//                     FinanceMerchantReportDO.setOverThirdSurplusRentCount(FinanceMerchantReportDO.getOverThirdSurplusRentCount() + count);
//                     break;
//                 default:
//                     break;
//             }
//         }
//
//
//     }
//
//
//     /**
//      * 售前优惠(基础项)
//      *
//      * @param ds
//      */
//     private void countTask12(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0') forcedconversion, " +
//                 "       IF(a.parent_id > 0, 1, 0)                                     isrenew, " +
//                 "       sum(b.write_off_amt)                                          write_off_amt, " +
//                 "       sum(count)                                                    count " +
//                 " from rent_order a " +
//                 "         inner join (select order_id, sum(if(isbefore=1,amt1 + amt2,0)) write_off_amt, " +
//                 "                      sum(if(isbefore=1,1,0)) count" +
//                 "                     from (select x.order_id, " +
//                 "                                  if(!isnull(min(x.bind_order_time)) and " +
//                 "                                   min(y.payment_time) >= min(x.bind_order_time),1,0)  isbefore, " +
//                 "                                  if(x.type in (1, 3), max(x.write_off_amt), 0) amt1, " +
//                 "                                  if(x.type in (2, 4), sum(x.write_off_amt), 0) amt2 " +
//                 "                           from rent_customer_coupon x inner join rent_order y on x.order_id = y.id " +
//                 "                           where x.order_id > 0 and x.scene = 1 " +
//                 "                             and x.is_deleted = 0 " +
//                 "                             and y.is_deleted = 0 " +
//                 "                             and x.ds = " + ds +
//                 "                             and y.ds = " + ds +
//                 "                           group by x.order_id, x.type) " +
//                 "                     group by order_id) b on a.id = b.order_id " +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "                       and c.is_deleted = 0 and c.ds = " + ds +
//                 "         inner join rent_order_item d " +
//                 "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0" +
//                 "                       and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in (" + testerIds + ") " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0'); ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal writeOffAmt = stringToDecimal(record.getString("write_off_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setBasePreDiscount(CalculateUtil.add(FinanceMerchantReportDO.getBasePreDiscount(), writeOffAmt));
//             FinanceMerchantReportDO.setBasePreDiscountCount(FinanceMerchantReportDO.getBasePreDiscountCount() + count);
//         }
//
//     }
//
//
//     /**
//      * 售后减免租金(已收租金)
//      *
//      * @param ds
//      */
//     private void countTask13(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0') as forcedconversion, " +
//                 "       IF(a.parent_id > 0, 1, 0)                     isrenew, " +
//                 "       sum(get_json_object(b.ext, '$.planCouponAmount'))        plan_coupon_amt, " +
//                 "       count(1)                                                      count " +
//                 "from rent_order a " +
//                 "         inner join rent_order_flow b on a.id = b.order_id " +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 " +
//                 "                     and d.is_deleted = 0  and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( " +
//                 testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and b.biz_type = 3 " +
//                 "  and b.pay_status = 10 " +
//                 "  and (b.mark_refund is null or b.mark_refund = 0) " +
//                 "  and b.flow_type = 1 " +
//                 "  and b.refunded_amt = 0 " +
//                 "  and b.ext is not null " +
//                 "  and b.ext != '' " +
//                 "  and get_json_object(b.ext, '$.planCouponAmount') is not null " +
//                 "  and get_json_object(b.ext, '$.planCouponAmount') >0 " +
//                 "  and a.is_deleted = 0 " +
//                 "  and b.is_deleted = 0 " +
//                 "  and c.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 "  and b.ds = " + ds +
//                 "  and c.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         c.rate_config_type, " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'); ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal planCouponAmt = stringToDecimal(record.getString("plan_coupon_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setRentDeductAfterRent(CalculateUtil.add(FinanceMerchantReportDO.getRentDeductAfterRent(),
//                     planCouponAmt));
//             FinanceMerchantReportDO.setRentDeductAfterRentCount(FinanceMerchantReportDO.getRentDeductAfterRentCount() + count);
//         }
//
//     }
//
//
//     /**
//      * 售前实收减免租金、运营实收减免租金(已收租金)、预计售前收入
//      *
//      * @param ds
//      */
//     private void countTask14(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0') forcedconversion, " +
//                 "       IF(a.parent_id > 0, 1, 0)                                     isrenew, " +
//                 "       sum(manage_write_off_amt)                                     manage_write_off_amt, " +
//                 "       sum(manage_count)                                             manage_count, " +
//                 "       sum(before_write_off_amt)                                     before_write_off_amt, " +
//                 "       sum(before_count)                                             before_count, " +
//                 "       sum(expected_income)                                          expected_income, " +
//                 "       sum(expected_income_count)                                    expected_income_count " +
//                 " from rent_order a " +
//                 "         inner join (select order_id, " +
//                 "                            sum(manage_write_off_amt)  manage_write_off_amt, " +
//                 "                            sum(manage_count)            manage_count, " +
//                 "                            sum(before_write_off_amt)  before_write_off_amt, " +
//                 "                            sum(before_count)            before_count, " +
//                 "                            sum(expected_income) expected_income, " +
//                 "                            sum(expected_income_count)           expected_income_count " +
//                 "                     from (select x.order_id, " +
//                 "                                  if(ISNULL(min(x.bind_order_time)) or " +
//                 "                                         min(y.payment_time) < min(x.bind_order_time), " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
//                 "                                            x.write_off_amt, 0)), 0), 0) manage_write_off_amt," +
//                 "                                  if((ISNULL(min(x.bind_order_time)) or " +
//                 "                                         min(y.payment_time) < min(x.bind_order_time)) and  " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
//                 "                                            x.write_off_amt, 0)), 0)>0,1, 0) manage_count, " +
//                 "                                  if(!ISNULL(min(x.bind_order_time)) and " +
//                 "                                         min(y.payment_time) >= min(x.bind_order_time), " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) = 1, " +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status = 1, " +
//                 "                                            x.write_off_amt, 0)), 0), 0) before_write_off_amt," +
//                 "                                  if(!ISNULL(min(x.bind_order_time)) and " +
//                 "                                         min(y.payment_time) >= min(x.bind_order_time) and  " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
//                 "                                            x.write_off_amt, 0)), 0)>0,1, 0) before_count, " +
//                 "                                  if(!ISNULL(min(x.bind_order_time)) and " +
//                 "                                         min(y.payment_time) >= min(x.bind_order_time), " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) != 1," +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status != 1," +
//                 "                                            x.write_off_amt, 0)), 0), 0) expected_income, " +
//                 "                                  if(!ISNULL(min(x.bind_order_time)) and " +
//                 "                                         min(y.payment_time) >= min(x.bind_order_time) and  " +
//                 "                                         if(x.type in (1, 3), if(min(x.use_status) != 1," +
//                 "                                            max(x.write_off_amt), 0), 0) + " +
//                 "                                         if(x.type in (2, 4), sum(if(x.use_status != 1," +
//                 "                                            x.write_off_amt, 0)), 0)>0,1, 0) expected_income_count " +
//                 "                           from rent_customer_coupon x " +
//                 "                                    inner join rent_order y on x.order_id = y.id " +
//                 "                           where x.order_id > 0 and x.scene = 1 " +
//                 "                             and x.is_deleted = 0 " +
//                 "                             and y.is_deleted = 0 " +
//                 "                             and x.ds = " + ds +
//                 "                             and y.ds = " + ds +
//                 "                           group by x.order_id, x.type) " +
//                 "                     group by order_id) b on a.id = b.order_id " +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "         inner join rent_order_item d " +
//                 "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                       and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in (" + testerIds + ") " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and c.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 "  and c.ds = " + ds +
//                 " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type," +
//                 "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0'); ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal manageWriteOffAmt = stringToDecimal(record.getString("manage_write_off_amt"));
//             BigDecimal beforeWriteOffAmt = stringToDecimal(record.getString("before_write_off_amt"));
//             BigDecimal expectedIncome = stringToDecimal(record.getString("expected_income"));
//             Integer manageCount = Integer.valueOf(record.getString("manage_count"));
//             Integer beforeCount = Integer.valueOf(record.getString("before_count"));
//             Integer expectedIncomeCount = Integer.valueOf(record.getString("expected_income_count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setRentDeductMarketing(CalculateUtil.add(FinanceMerchantReportDO.getRentDeductMarketing(),
//                     manageWriteOffAmt));
//             FinanceMerchantReportDO.setRentDeductMarketingCount(FinanceMerchantReportDO.getRentDeductMarketingCount() + manageCount);
//             FinanceMerchantReportDO.setBaseActBeforeRentDiscountAmt(CalculateUtil.add(FinanceMerchantReportDO.getBaseActBeforeRentDiscountAmt(),
//                     beforeWriteOffAmt));
//             FinanceMerchantReportDO.setBaseActBeforeRentDiscountCount(FinanceMerchantReportDO.getBaseActBeforeRentDiscountCount() + beforeCount);
//             FinanceMerchantReportDO.setExpectedIncomeAmt(CalculateUtil.add(FinanceMerchantReportDO.getExpectedIncomeAmt(),
//                     expectedIncome));
//             FinanceMerchantReportDO.setExpectedIncomeCount(FinanceMerchantReportDO.getExpectedIncomeCount() + expectedIncomeCount);
//         }
//     }
//
//
//     /**
//      * 售后减免买断金(已收买断金)、运营减免买断金、保证金抵扣买断、已收买断金
//      *
//      * @param ds
//      */
//     private void countTask15(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                          day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0') as forcedconversion, " +
//                 "       IF(a.parent_id > 0, 1, 0)                                        isrenew, " +
//                 "       sum(if(c.rate_config_type != 10,b.user_actual_buyout_amt,0))                          " +
//                 "                                                               user_actual_buyout_amt, " +
//                 "       sum(if(c.rate_config_type != 10 and !isnull(b.user_actual_buyout_amt) " +
//                 "                   and b.user_actual_buyout_amt>0,1,0))        user_actual_buyout_amt_count, " +
//                 "       sum(if(c.rate_config_type != 10,b.bond_deduct_buyout,0))                              " +
//                 "                                                              bond_deduct_buyout, " +
//                 "       sum(if(c.rate_config_type != 10 and !isnull(b.bond_deduct_buyout) and b.bond_deduct_buyout>0," +
//                 "                   1,0))                                      bond_deduct_buyout_count, " +
//                 "       sum(if(c.rate_config_type != 10,b.manage_buyout_discount,0))       " +
//                 "                                                              manage_buyout_discount, " +
//                 "       sum(if(c.rate_config_type != 10 and !isnull(b.manage_buyout_discount) and " +
//                 "                   b.manage_buyout_discount>0,1,0))           manage_buyout_discount_count, " +
//                 "       sum(if(c.rate_config_type != 10,b.offline_buyout_discount,0))              " +
//                 "                                                              offline_buyout_discount, " +
//                 "       sum(if(c.rate_config_type != 10 and !isnull(b.offline_buyout_discount) " +
//                 "                   and b.offline_buyout_discount>0,1,0))      offline_buyout_discount_count " +
//                 " from rent_order a " +
//                 "         inner join (select order_id, " +
//                 "                            sum(user_actual_buyout_amt)  user_actual_buyout_amt, " +
//                 "                            sum(buyout_deduction_amount) bond_deduct_buyout, " +
//                 "                            sum(buyout_coupon_amount) manage_buyout_discount, " +
//                 "                            sum(buyout_coupon_offline_amount) offline_buyout_discount " +
//                 "                     from rent_order_account_check " +
//                 "                     where is_deleted = 0 " +
//                 "                       and ds = " + ds +
//                 "                     group by order_id) b on a.id = b.order_id " +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 " +
//                 "                   and c.ds = " + ds +
//                 "         inner join rent_order_item d " +
//                 "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                   and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in (" + testerIds + ") " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type = 10, '1', '0'); ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             //用户实际支付买断金
//             BigDecimal userActualBuyoutAmt = stringToDecimal(record.getString("user_actual_buyout_amt"));
//             Integer userActualBuyoutAmtCount = Integer.valueOf(record.getString("user_actual_buyout_amt_count"));
//             //保证金抵扣买断金 暂时走流水表
//             BigDecimal bondDeductBuyout = stringToDecimal(record.getString("bond_deduct_buyout"));
//             Integer bondDeductBuyoutCount = Integer.valueOf(record.getString("bond_deduct_buyout_count"));
//             //运营减免买断金
//             BigDecimal manageBuyoutDiscount = stringToDecimal(record.getString("manage_buyout_discount"));
//             Integer manageBuyoutDiscountCount = Integer.valueOf(record.getString("manage_buyout_discount_count"));
//             //售后减免买断金
//             BigDecimal offlineBuyoutDiscount = stringToDecimal(record.getString("offline_buyout_discount"));
//             Integer offlineBuyoutDiscountCount = Integer.valueOf(record.getString("offline_buyout_discount_count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//
//             FinanceMerchantReportDO.setBuyoutReceiveBuyoutAmt(CalculateUtil.add(FinanceMerchantReportDO.getBuyoutReceiveBuyoutAmt(),
//                     userActualBuyoutAmt));
//             FinanceMerchantReportDO.setBuyoutReceiveBuyoutCount(FinanceMerchantReportDO.getBuyoutReceiveBuyoutCount() + userActualBuyoutAmtCount);
//
//             FinanceMerchantReportDO.setBuyoutReduceMarketingAmt(CalculateUtil.add(FinanceMerchantReportDO.getBuyoutReduceMarketingAmt(), manageBuyoutDiscount));
//             FinanceMerchantReportDO.setBuyoutReduceMarketingCount(FinanceMerchantReportDO.getBuyoutReduceMarketingCount() + manageBuyoutDiscountCount);
//
//             FinanceMerchantReportDO.setBuyoutReduceAfterAmt(CalculateUtil.add(FinanceMerchantReportDO.getBuyoutReduceAfterAmt(),
//                     offlineBuyoutDiscount));
//             FinanceMerchantReportDO.setBuyoutReduceAfterCount(FinanceMerchantReportDO.getBuyoutReduceAfterCount() + offlineBuyoutDiscountCount);
//         }
//     }
//
//
//     /**
//      * 其他收入:优惠返还
//      *
//      * @param ds
//      */
//     private void countTask16(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0') as forcedconversion, " +
//                 "       IF(a.parent_id > 0, 1, 0)                     isrenew, " +
//                 "       sum(get_json_object(ext, '$.discountReturnAmt'))        discount_return_amt, " +
//                 "       count(1)                                                      count " +
//                 "from rent_order a " +
//                 "         inner join rent_order_flow b on a.id = b.order_id " +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 " +
//                 "                     and d.is_deleted = 0  and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( " +
//                 testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and b.biz_type = 3 " +
//                 "  and b.pay_status = 10 " +
//                 "  and (b.mark_refund is null or b.mark_refund = 0) " +
//                 "  and b.flow_type = 1 " +
//                 "  and b.refunded_amt = 0 " +
//                 "  and b.ext is not null " +
//                 "  and b.ext != '' " +
//                 "  and get_json_object(ext, '$.discountReturnAmt') is not null " +
//                 "  and get_json_object(ext, '$.discountReturnAmt') >0 " +
//                 "  and a.is_deleted = 0 " +
//                 "  and b.is_deleted = 0 " +
//                 "  and c.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 "  and b.ds = " + ds +
//                 "  and c.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         c.rate_config_type, " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'); ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal discountReturnAmt = stringToDecimal(record.getString("discount_return_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setOtherDiscountReturnAmt(CalculateUtil.add(FinanceMerchantReportDO.getOtherDiscountReturnAmt(),
//                     discountReturnAmt));
//             FinanceMerchantReportDO.setOtherDiscountReturnCount(FinanceMerchantReportDO.getOtherDiscountReturnCount() + count);
//         }
//     }
//
//
//     /**
//      * 3+N已收总额
//      *
//      * @param ds
//      */
//     private void countTask17(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                                     day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       IF(a.status in (200,210),1,0)                       isreturn,"+
//                 "       a.mini_type, " +
//                 "       c.rate_config_type," +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')             forcedconversion," +
//                 "       IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "       sum(d.capital*12) rent_total," +
//                 "       sum(d.act_rent) act_rent," +
//                 "       sum(c.actual_buyout_amt) actual_buyout_amt, " +
//                 "       count(1)                                                                    count " +
//                 " from rent_order a " +
//                 "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 " +
//                 "                                             and b.type_class != 1 and b.is_deleted = 0 " +
//                 "                                             and b.ds = " + ds +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "                                                       and c.is_deleted = 0 and c.ds = " + ds +
//                 "         inner join (select order_id,avg(capital) capital," +
//                 "               sum(if(repay_status = 5,real_repay_capital,0)) act_rent" +
//                 "                                                from rent_order_repayment_plan  " +
//                 "                                                 where is_deleted = 0 " +
//                 "                                                    and ds = " + ds + " group by order_id) d " +
//                 "                                                    on a.id = d.order_id" +
//                 "         left join rent_order_flow g on a.id = g.order_id and g.biz_type = 3 " +
//                 "                and g.pay_status = 10 " +
//                 "                and g.flow_type = 1 " +
//                 "                and g.refunded_amt = 0 " +
//                 "                and g.ext is not null " +
//                 "                and g.ext != ''" +
//                 "                and (g.mark_refund is null or g.mark_refund = 0) " +
//                 "                and g.is_deleted = 0  " +
//                 "                and g.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in (" + testerIds + ") " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and c.rate_config_type = 10 " +
//                 "  and a.status in(200,210,220)" +
//                 "  and a.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'), " +
//                 "         IF(a.status in (200,210),1,0), " +
//                 "         c.rate_config_type;  ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer isReturn = Integer.valueOf(record.getString("isreturn"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal actualBuyoutAmt = stringToDecimal(record.getString("actual_buyout_amt"));
//             BigDecimal rentTotal = stringToDecimal(record.getString("rent_total"));
//             BigDecimal actRent = stringToDecimal(record.getString("act_rent"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             // 3+N结清优惠 协议总租金-实收总额-已收租金
//             if(isReturn==0){
//                 FinanceMerchantReportDO.setEndDiscountAmt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
//                         actualBuyoutAmt)));
//                 FinanceMerchantReportDO.setEndDiscountCount(FinanceMerchantReportDO.getEndDiscountCount() + count);
//             }else{
//                 FinanceMerchantReportDO.setEndReturnNotRepayAmt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
//                         actualBuyoutAmt)));
//                 FinanceMerchantReportDO.setEndReturnNotRepayCount(FinanceMerchantReportDO.getEndReturnNotRepayCount() + count);
//             }
//         }
//
//     }
//
//
//     /**
//      * 保证金抵扣赔偿金(已收买断金)
//      *
//      * @param ds
//      */
//     private void countTask18(String ds, String testerIds, Map<String, FinanceMerchantReportDO> collectMap) {
//         String sql = " select date(a.rent_start_date)                                       day, " +
//                 "       date(k.rent_start_date)                             parent_day," +
//                 "       a.mini_type, " +
//                 "       c.rate_config_type, " +
//                 "       IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0') as forcedconversion," +
//                 "       IF(a.parent_id > 0, 1, 0) isrenew, " +
//                 "       sum(b.bond_deduct_damage_amt)  bond_deduct_damage_amt, " +
//                 "       sum(if(b.bond_deduct_damage_amt is not null,1,0)) count " +
//                 " from rent_order a " +
//                 "       inner join (select sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=2,flow_amt," +
//                 "               null)) bond_deduct_damage_amt," +
//                 "               order_id from rent_order_flow  " +
//                 "                                   where biz_type in (23) " +
//                 "                                   and pay_status = 10 " +
//                 "                                   and flow_type =1 " +
//                 "                                   and (mark_refund is null or mark_refund = 0) " +
//                 "                                   and refunded_amt = 0 " +
//                 "                                   and ext is not null " +
//                 "                                   and ext != '' " +
//                 "                                   and is_deleted = 0 " +
//                 "                                   and ds = " + ds +
//                 "                          group by order_id) b on a.id = b.order_id" +
//                 "         inner join rent_order_finance_detail c on a.id = c.order_id " +
//                 "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
//                 "                                   and d.type_class != 1 and d.ds = " + ds +
//                 "         left join ( select id,rent_start_date " +
//                 "                           from rent_order " +
//                 "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
//                 " where a.merchant_id = 100 " +
//                 "  and a.payment_time is not null " +
//                 "  and a.termination != 5 " +
//                 "  and a.biz_type = 2 " +
//                 "  and a.type = 1 " +
//                 "  and a.customer_id not in ( "
//                 + testerIds +
//                 "    ) " +
//                 "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
//                 "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
//                 "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
//                 "  and a.is_deleted = 0 " +
//                 "  and c.is_deleted = 0 " +
//                 "  and a.ds = " + ds +
//                 "  and c.ds = " + ds +
//                 " group by date(a.rent_start_date), " +
//                 "         date(k.rent_start_date),  " +
//                 "         a.mini_type, " +
//                 "         IF(a.parent_id > 0, 1, 0), " +
//                 "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0')," +
//                 "         c.rate_config_type; ";
//         List<Record> records = odpsUtil.querySql(sql);
//         for (Record record : records) {
//             LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
//             LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
//             Integer miniType = Integer.valueOf(record.getString("mini_type"));
//             Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
//             Integer isRenew = Integer.valueOf(record.getString("isrenew"));
//             Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
//             BigDecimal bondDeductDamageAmt = stringToDecimal(record.getString("bond_deduct_damage_amt"));
//             Integer count = Integer.valueOf(record.getString("count"));
//             Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
//             initMap(collectMap, miniType, financeType, day, parentDay);
//             FinanceMerchantReportDO FinanceMerchantReportDO = collectMap.get(getMapKey(miniType, financeType, day, parentDay));
//             FinanceMerchantReportDO.setBondDeductDamageAmt(CalculateUtil.add(FinanceMerchantReportDO.getBondDeductDamageAmt(),
//                     bondDeductDamageAmt));
//             FinanceMerchantReportDO.setBondDeductDamageCount(FinanceMerchantReportDO.getBondDeductDamageCount() + count);
//         }
//
//     }
//
//
//     private void initMap(Map<String, FinanceMerchantReportDO> miniType2Map, Integer miniType, Integer financeType,
//                          LocalDateTime countDay, LocalDateTime parentDay) {
//         String key = getMapKey(miniType, financeType, countDay, parentDay);
//         if (!miniType2Map.containsKey(key)) {
//             FinanceMerchantReportDO ivd = new FinanceMerchantReportDO();
//             ivd.setMiniType(miniType);
//             ivd.setCountDay(countDay);
//             ivd.setParentDay(parentDay);
//             ivd.setFinanceType(financeType);
//             miniType2Map.put(key, ivd);
//         }
//     }
//
//     private String getMapKey(Integer miniType, Integer financeType, LocalDateTime countDay, LocalDateTime parentDay) {
//         String str = "";
//         if (parentDay != null) {
//             str = DateUtils.dateToString(parentDay);
//         }
//         return miniType + "_" + financeType + "_" + DateUtils.dateToString(countDay) + "_" + str;
//     }
//
//
//     private BigDecimal stringToDecimal(String val) {
//         if (val.equals("\\N")) {
//             val = "0";
//         }
//         return new BigDecimal(val).setScale(2, RoundingMode.HALF_UP);
//     }
//
//     private LocalDateTime stringTOLocalDate(String val) {
//         if (val.equals("\\N")) {
//             return null;
//         }
//         return LocalDateTime.of(LocalDate.parse(val), LocalTime.MIN);
//     }
//
// }