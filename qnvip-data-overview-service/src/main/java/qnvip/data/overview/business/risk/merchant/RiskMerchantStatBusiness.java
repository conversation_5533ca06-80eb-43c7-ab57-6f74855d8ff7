package qnvip.data.overview.business.risk.merchant;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import com.google.common.base.Joiner;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.aop.framework.AopContext;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.springframework.transaction.support.TransactionSynchronizationManager;
import qnvip.data.overview.domain.risk.DataviewRiskMerchantOrderInfoDO;
import qnvip.data.overview.domain.risk.DataviewRiskNewRentJobLogDO;
import qnvip.data.overview.domain.risk.DataviewRiskNewRentOrderInfoDO;
import qnvip.data.overview.domain.risk.ShOrderInfo;
import qnvip.data.overview.mapper.risk.DataviewRiskMerchantOrderInfoMapper;
import qnvip.data.overview.param.job.JobParam;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.order.impl.RentOrderServiceImpl;
import qnvip.data.overview.service.risk.DataviewRiskMerchantOrderInfoService;
import qnvip.data.overview.service.risk.DataviewRiskNewRentJobLogService;
import qnvip.data.overview.service.risk.DataviewRiskNewRentOrderInfoService;
import qnvip.data.overview.util.DingDingAlarmUtil;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.rent.common.enums.StatusEnum;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * All things are difficult before they are easy. by: zl
 * 风控大盘-分期明细表统计
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskMerchantStatBusiness {
    private final OdpsUtil odpsUtil;
    private final DingDingAlarmUtil dingDingAlarmUtil;

    private final DataviewRiskMerchantOrderInfoService riskMerchantOrderInfoService;

    private final DataviewRiskMerchantOrderInfoMapper dataviewRiskMerchantOrderInfoMapper;
    private final DataviewRiskNewRentJobLogService jobLogService;

    private final DateTimeFormatter FORMAT_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter FORMAT_TIME_TIDB = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    private final DateTimeFormatter FORMAT_TIME_TIDB2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS");
    private final DateTimeFormatter FORMAT_TIME_TIDB3 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private final DateTimeFormatter FORMAT_TIME_TIDB4 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSS");
    private final DateTimeFormatter FORMAT_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final RentOrderService rentOrderService;

    public void runRiskMerchantOrderSys(JobParam jobParam) {
        String errorInfo = "";
        DataviewRiskNewRentJobLogDO jobLogDO = new DataviewRiskNewRentJobLogDO();
        jobLogDO.setJobTime(jobParam.getJobTime());
        jobLogDO.setJobName(jobParam.getJobName());
        try {
            log.info("开始分期订单明细, param={}", JSONUtil.toJsonStr(jobParam));
            // 获取所有订单信息
            String countSql1 = synShOrderInfo(jobParam);
            // 获取进入风控的订单信息
            String countSql2 = synRiskOrderInfo(jobParam);
            jobLogDO.setStatSql(countSql1 + countSql2);
            jobLogDO.setStatus(StatusEnum.YES.getCode());
        } catch (Exception e) {
            errorInfo = e.getMessage();
            log.error("数据大盘生产环境-数据总览-分期报错:",jobParam.getJobName() , e);
        } finally {
            jobLogDO.setErrorInfo(errorInfo);
            jobLogService.save(jobLogDO);
            log.info(jobParam.getJobName() + " 结束");
        }
    }



    @Transactional
    public void runRiskMerchantOrderSysTidb(JobParam jobParam) {
        String errorInfo = "";
        DataviewRiskNewRentJobLogDO jobLogDO = new DataviewRiskNewRentJobLogDO();
        jobLogDO.setJobTime(jobParam.getJobTime());
        jobLogDO.setJobName(jobParam.getJobName());
        try {
            log.info("开始分期订单明细, param={}", JSONUtil.toJsonStr(jobParam));
            log.info("事务：{}", TransactionSynchronizationManager.getCurrentTransactionName());
            // 获取所有订单信息
            String countSql1 = synShOrderInfoTidb(jobParam);
            // 获取进入风控的订单信息
            String countSql2 = synRiskOrderInfoTidb(jobParam);
            jobLogDO.setStatSql(countSql1 + countSql2);
            jobLogDO.setStatus(StatusEnum.YES.getCode());
        } catch (Exception e) {
            errorInfo = e.getMessage();
            log.error(jobParam.getJobName() + " 异常 ", e);
        } finally {
            jobLogDO.setErrorInfo(errorInfo);
            log.info(jobParam.getJobName() + " 结束");
        }
    }

    private String synRiskOrderInfo(JobParam jobParam) {
        // 获取sql明细
        String prefix = "WITH dt AS( SELECT DATE_FORMAT(now(), 'yyyymmdd') AS ds ,to_char(dateadd(getdate(),-1,'dd'),'yyyymmdd') last_ds) ";
        String body = getSqlBody(jobParam);
        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        Integer size = getCount(countSql);
        log.info("{}, 分期进入风控总数={}", jobParam.getJobName(), size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }

        for (int startPage = 0; startPage < times; startPage++) {
            List<DataviewRiskMerchantOrderInfoDO> list = Lists.newArrayList();
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = prefix + body + suffix;
            log.info("synRiskOrderInfo sql: {}", pageSql);
            List<Record> records = odpsUtil.querySql(pageSql);
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            list.addAll(assemble(records));
            batchSave(list);
            // rentOrderInfoService.saveOrUpdateBatch(list,2000);
        }
        return countSql;
    }

    @Transactional
    public String synRiskOrderInfoTidb(JobParam jobParam) {
        log.info("synRiskOrderInfoTidb事务：{}", TransactionSynchronizationManager.getCurrentTransactionName());
        // 获取sql明细
        String prefix = " ";
        String body = getSqlBodyTidb(jobParam);
        String countSql = prefix + "select count(*) num from (" + body + ") a;";
        Integer size = getCountTidb(countSql);
        log.info("{}, 分期进入风控总数={}", jobParam.getJobName(), size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        for (int startPage = 0; startPage < times; startPage++) {
            List<DataviewRiskMerchantOrderInfoDO> list = Lists.newArrayList();
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = prefix + body + suffix;
            log.info("synRiskOrderInfo sql: {}", pageSql);
            List<Map<String, String>> records = rentOrderService.getGeneralCommonSql(pageSql);
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            list.addAll(assembleTidb(records));
//            batchSave(list);
            rentOrderService.batchSave(list);
            // rentOrderInfoService.saveOrUpdateBatch(list,2000);
        }
        return countSql;
    }

    private String synShOrderInfo(JobParam jobParam) {
        // 获取sql明细
        String prefix = "WITH dt AS( SELECT DATE_FORMAT(now(), 'yyyymmdd') AS ds,to_char(dateadd(getdate(),-1,'dd'),'yyyymmdd') last_ds) ";
        String body = getShOrderSqlBody(jobParam);
        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        Integer size = getCount(countSql);
        log.info("{}, 分期总数={}", jobParam.getJobName(), size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        for (int startPage = 0; startPage < times; startPage++) {
            List<DataviewRiskMerchantOrderInfoDO> list = Lists.newArrayList();
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = prefix + body + suffix;
            log.info("synShOrderInfo: {}", pageSql);
            List<Record> records = odpsUtil.querySql(pageSql);
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            list.addAll(assemble(records));
            batchSave(list);
            // rentOrderInfoService.saveOrUpdateBatch(list,2000);
        }
        return countSql;
    }
    @Transactional
    public String synShOrderInfoTidb(JobParam jobParam) {
        log.info("synShOrderInfoTidb事务：{}", TransactionSynchronizationManager.getCurrentTransactionName());
        // 获取sql明细
        String prefix = " ";
        String body = getShOrderSqlBodyTidb(jobParam);
        String countSql = prefix + "select count(*) num from (" + body + ") a;";
        Integer size = getCountTidb(countSql);
        log.info("{}, 分期总数={}", jobParam.getJobName(), size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }


        for (int startPage = 0; startPage < times; startPage++) {
            List<DataviewRiskMerchantOrderInfoDO> list = Lists.newArrayList();
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = prefix + body + suffix;
            log.info("synShOrderInfo: {}", pageSql);
            List<ShOrderInfo> records = rentOrderService.getShOrderInfo(pageSql);
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            list.addAll(assembleTidb2(records));
            rentOrderService.batchSave(list);
            // rentOrderInfoService.saveOrUpdateBatch(list,2000);
        }
        return countSql;
    }

    @Transactional
    public void batchSave(List<DataviewRiskMerchantOrderInfoDO> list) {
        log.info("batchSave1事务：{}", TransactionSynchronizationManager.getCurrentTransactionName());
        List<List<DataviewRiskMerchantOrderInfoDO>> partition = Lists.partition(list, 2000);
        log.info("批量插入风控-分期订单明细分区数 num={}", partition.size());
        for (List<DataviewRiskMerchantOrderInfoDO> infoDOS : partition) {
            if (CollUtil.isEmpty(infoDOS)) {
                continue;
            }
            try {
                List<String> noList = infoDOS.stream().map(DataviewRiskMerchantOrderInfoDO::getOrderNo).collect(Collectors.toList());
                // 删除
                dataviewRiskMerchantOrderInfoMapper.deleteByOrderNo(noList.stream().collect(Collectors.joining("','","'","'")));
            } catch (Exception e) {
                log.error("批量删除风控-分期订单明细异常", e);
//                dingDingAlarmUtil.dataViewProExceptionAlarm("批量删除风控-分期订单明细异常 ");
            }

        }
        for (List<DataviewRiskMerchantOrderInfoDO> infoDOS : partition) {
            if (CollUtil.isEmpty(infoDOS)) {
                continue;
            }
            try {
                // 插入
                riskMerchantOrderInfoService.saveOrUpdateBatch(infoDOS, 2500);
                // dataviewRiskMerchantOrderInfoMapper.updateBatch(infoDOS);
                log.info("批量插入风控-分期订单明细size={}", infoDOS.size());
            } catch (Exception e) {
                log.error("批量插入风控-分期订单明细异常", e);
//                dingDingAlarmUtil.dataViewProExceptionAlarm("批量插入风控-分期订单明细异常 ");
            }

        }
    }

    private String getShOrderSqlBody(JobParam jobParam) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT '' risk_strategy, sof.hb_period_no hb_period_no, so.order_extra_type ,NULL rh_time ,so.create_time sh_create_time ,NULL product ,so.update_time sh_update_time ,NULL risk_create_time ,NULL risk_update_time ,ol.send_time sh_send_time ,NULL risk_send_time ,so" +
                ".cash_deposit_pay_time ," +
                "NULL risk_pay_time ,so.order_uid order_no ,so.customer_id sh_customer_id ,-1 risk_customer_id ,so.merchant_uid sh_merchant_uid ,-1 risk_merchant_id ,so.status sh_order_status ,so.closing_time sh_closing_time ,-1 risk_close_status ,sof.credit_difference_amt ,sof.repayment_period_count ,-1 risk_business_channel ,so.mini_type sh_mini_type ,si.equipment_type sh_equipment_type ,-1 risk_supervisory_machine ,-1 risk_fico_status ,-1 risk_artificial_audit_status ,'' risk_opinion ,mc.platform_code sh_platform_code ,-1 front_pass_flag ,-1 anti_fraud_pass_flag ,-1 big_data_pass_flag,so.down_payment_flag FROM sh_order so LEFT JOIN sh_order_finance sof ON so.order_uid = sof.order_uid AND sof.is_deleted = 0 AND sof.ds = ( SELECT ds FROM dt ) LEFT JOIN sh_order_logistics ol ON so.order_uid = ol.order_uid AND ol.is_deleted = 0 AND ol.type = 10 AND ol.ds = ( SELECT ds FROM dt ) LEFT JOIN sh_order_item oi ON oi.ds = ( SELECT ds FROM dt ) AND oi.order_uid = so.order_uid AND oi.item_type = 10 AND oi.is_deleted = 0 LEFT JOIN sh_item si ON oi.item_uid = si.item_uid AND si.is_deleted = 0 AND si.ds = ( SELECT last_ds FROM dt ) LEFT JOIN sh_mini_config mc ON so.mini_type = mc.mini_type AND mc.is_deleted = 0 AND mc.ds = ( SELECT ds FROM dt )  ");
        builder.append(" WHERE so.ds =( SELECT ds FROM dt) and so.is_deleted = 0 AND so.parent_order_uid = '' AND so.test_role = 0 ");
        if (jobParam.getInitFlag()) {
            builder.append(" AND so.create_time >= '{startTime}' and so.create_time <= '{endTime}' ORDER BY so.id DESC ");
        } else {
            builder.append(" AND so.update_time >= '{startTime}' and so.update_time <= '{endTime}' ORDER BY so.id DESC ");
        }
        return builder.toString().replace("{startTime}", jobParam.getStartTime()).replace("{endTime}", jobParam.getEndTime());
    }

    private String getShOrderSqlBodyTidb(JobParam jobParam) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT '' risk_strategy, sof.hb_period_no hb_period_no, so.order_extra_type ,NULL rh_time ,so.create_time sh_create_time ,NULL product ,so.update_time sh_update_time ,NULL risk_create_time ,NULL risk_update_time ,ol.send_time sh_send_time ,NULL risk_send_time ,so" +
                ".cash_deposit_pay_time ," +
                "NULL risk_pay_time ,so.order_uid order_no ,so.customer_id sh_customer_id ,-1 risk_customer_id ,so.merchant_uid sh_merchant_uid ,-1 risk_merchant_id ,so.status sh_order_status ,so.closing_time sh_closing_time ,-1 risk_close_status ,sof.credit_difference_amt ,sof.repayment_period_count ,-1 risk_business_channel ,so.mini_type sh_mini_type ,si.equipment_type sh_equipment_type ,-1 risk_supervisory_machine ,-1 risk_fico_status ,-1 risk_artificial_audit_status ,'' risk_opinion ,mc.platform_code sh_platform_code ,-1 front_pass_flag ,-1 anti_fraud_pass_flag ,-1 big_data_pass_flag,so.down_payment_flag FROM qnvip_merchant.sh_order so LEFT JOIN qnvip_merchant.sh_order_finance sof ON so.order_uid = sof.order_uid AND sof.is_deleted = 0  LEFT JOIN qnvip_merchant.sh_order_logistics ol ON so.order_uid = ol.order_uid AND ol.is_deleted = 0 AND ol.type = 10  LEFT JOIN qnvip_merchant.sh_order_item oi ON  oi.order_uid = so.order_uid AND oi.item_type = 10 AND oi.is_deleted = 0 LEFT JOIN qnvip_merchant.sh_item si ON oi.item_uid = si.item_uid AND si.is_deleted = 0  LEFT JOIN qnvip_merchant.sh_mini_config mc ON so.mini_type = mc.mini_type AND mc.is_deleted = 0  ");
        builder.append(" WHERE  so.is_deleted = 0 AND so.parent_order_uid = '' AND so.test_role = 0 ");
        if (jobParam.getInitFlag()) {
            builder.append(" AND so.create_time >= '{startTime}' and so.create_time <= '{endTime}' ORDER BY so.id DESC ");
        } else {
            builder.append(" AND so.update_time >= '{startTime}' and so.update_time <= '{endTime}' ORDER BY so.id DESC ");
        }
        return builder.toString().replace("{startTime}", jobParam.getStartTime()).replace("{endTime}", jobParam.getEndTime());
    }

    public String getSqlBody(JobParam jobParam) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT sn.riskStrategy risk_strategy, sof.hb_period_no hb_period_no, so.order_extra_type ,sn.rhtime rh_time ,so.create_time sh_create_time ,cl.product ,so.update_time sh_update_time ,cl.createtime risk_create_time ,cl.updatetime risk_update_time ,ol.send_time sh_send_time" +
                " ,cl" +
                ".sendtime " +
                "risk_send_time ,so.cash_deposit_pay_time ,cl.paytime risk_pay_time ,cl.loanno order_no ,so.customer_id sh_customer_id ,cl.customerid risk_customer_id ,so.merchant_uid sh_merchant_uid ,cl.merchantid risk_merchant_id ,so.status sh_order_status ,so.closing_time sh_closing_time ,cl.closestatus risk_close_status ,sof.credit_difference_amt ,sof.repayment_period_count ,cl.businesschannel risk_business_channel ,so.mini_type sh_mini_type ,si.equipment_type sh_equipment_type ,cl.supervisorymachine risk_supervisory_machine ,cl.riskFicoStatus risk_fico_status ,cl.artificialauditstatus risk_artificial_audit_status ,cl.riskopinion risk_opinion ,mc.platform_code sh_platform_code ,IF(cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 20,1,0) front_pass_flag ,IF(cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 30,1,0) anti_fraud_pass_flag ,IF(cl.riskFicoStatus IS NOT NULL AND cl.artificialauditstatus IS NOT NULL AND(cl.riskFicoStatus > 60 OR (cl.riskFicoStatus = 60 AND cl.artificialauditstatus = 10)),1,0) big_data_pass_flag,so.down_payment_flag FROM cl_loan cl LEFT JOIN sh_order so ON cl.loanno = so.order_uid AND so.is_deleted = 0 AND so.test_role = 0 AND so.parent_order_uid = '' AND so.ds = ( SELECT ds FROM dt) LEFT JOIN sh_order_finance sof ON so.order_uid = sof.order_uid AND sof.is_deleted = 0 AND sof.ds = ( SELECT ds FROM dt ) LEFT JOIN sh_order_logistics ol ON so.order_uid = ol.order_uid AND ol.is_deleted = 0 AND ol.type = 10 AND ol.ds = ( SELECT ds FROM dt ) LEFT JOIN sh_order_item oi ON oi.ds = ( SELECT ds FROM dt ) AND oi.order_uid = so.order_uid AND oi.item_type = 10 AND oi.is_deleted = 0 LEFT JOIN sh_item si ON oi.item_uid = si.item_uid AND si.is_deleted = 0 AND si.ds = ( SELECT last_ds FROM dt ) LEFT JOIN sh_mini_config mc ON so.mini_type = mc.mini_type AND mc.is_deleted = 0 AND mc.ds = ( SELECT ds FROM dt ) LEFT JOIN serial_no sn ON sn.businessno = cl.loanno AND sn.ds = ( SELECT ds FROM dt ) ");
        builder.append(" WHERE cl.ds =( SELECT ds FROM dt) AND cl.parentno = '' AND cl.businesschannel IN (28,51,1013,1014,1015,1016,1017,1018,1019) AND cl.product NOT LIKE '%测试%' ");
        if (jobParam.getInitFlag()) {
            builder.append(" AND cl.createtime >= '{startTime}' and cl.createtime <= '{endTime}' ORDER BY cl.id DESC ");
        } else {
            builder.append(" AND cl.updatetime >= '{startTime}' and cl.updatetime <= '{endTime}' ORDER BY cl.id DESC ");
        }
        return builder.toString().replace("{startTime}", jobParam.getStartTime()).replace("{endTime}", jobParam.getEndTime());
    }

    public String getSqlBodyTidb(JobParam jobParam) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT sn.riskStrategy risk_strategy, sof.hb_period_no hb_period_no, so.order_extra_type ,sn.rhtime rh_time ,so.create_time sh_create_time ,cl.product ,so.update_time sh_update_time ,cl.createtime risk_create_time ,cl.updatetime risk_update_time ,ol.send_time sh_send_time" +
                " ,cl" +
                ".sendtime " +
                "risk_send_time ,so.cash_deposit_pay_time ,cl.paytime risk_pay_time ,cl.loanno order_no ,so.customer_id sh_customer_id ,cl.customerid risk_customer_id ,so.merchant_uid sh_merchant_uid ,cl.merchantid risk_merchant_id ,so.status sh_order_status ,so.closing_time sh_closing_time ,cl.closestatus risk_close_status ,sof.credit_difference_amt ,sof.repayment_period_count ,cl.businesschannel risk_business_channel ,so.mini_type sh_mini_type ,si.equipment_type sh_equipment_type ,cl.supervisorymachine risk_supervisory_machine ,cl.riskFicoStatus risk_fico_status ,cl.artificialauditstatus risk_artificial_audit_status ,cl.riskopinion risk_opinion ,mc.platform_code sh_platform_code ,IF(cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 20,1,0) front_pass_flag ,IF(cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 30,1,0) anti_fraud_pass_flag ,IF(cl.riskFicoStatus IS NOT NULL AND cl.artificialauditstatus IS NOT NULL AND(cl.riskFicoStatus > 60 OR (cl.riskFicoStatus = 60 AND cl.artificialauditstatus = 10)),1,0) big_data_pass_flag,so.down_payment_flag FROM alchemist.cl_loan cl LEFT JOIN qnvip_merchant.sh_order so ON cl.loanno = so.order_uid AND so.is_deleted = 0 AND so.test_role = 0 AND so.parent_order_uid = ''  LEFT JOIN qnvip_merchant.sh_order_finance sof ON so.order_uid = sof.order_uid AND sof.is_deleted = 0  LEFT JOIN qnvip_merchant.sh_order_logistics ol ON so.order_uid = ol.order_uid AND ol.is_deleted = 0 AND ol.type = 10  LEFT JOIN qnvip_merchant.sh_order_item oi ON   oi.order_uid = so.order_uid AND oi.item_type = 10 AND oi.is_deleted = 0 LEFT JOIN qnvip_merchant.sh_item si ON oi.item_uid = si.item_uid AND si.is_deleted = 0  LEFT JOIN qnvip_merchant.sh_mini_config mc ON so.mini_type = mc.mini_type AND mc.is_deleted = 0  LEFT JOIN alchemist.serial_no sn ON sn.businessno = cl.loanno  ");
        builder.append(" WHERE  cl.parentno = '' AND cl.businesschannel IN (28,51,1013,1014,1015,1016,1017,1018,1019) AND cl.product NOT LIKE '%测试%' ");
        if (jobParam.getInitFlag()) {
            builder.append(" AND cl.createtime >= '{startTime}' and cl.createtime <= '{endTime}' ORDER BY cl.id DESC ");
        } else {
            builder.append(" AND cl.updatetime >= '{startTime}' and cl.updatetime <= '{endTime}' ORDER BY cl.id DESC ");
        }
        return builder.toString().replace("{startTime}", jobParam.getStartTime()).replace("{endTime}", jobParam.getEndTime());
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String sql) {
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCountTidb(String sql) {
//        List<Record> records = odpsUtil.querySql(sql);
        List<Map<String, String>> records = rentOrderService.getGeneralCommonSql(sql);
        Object num = records.get(0).get("num");
        return ((Long) num).intValue();
    }

    /**
     * 排除了订单号为空的订单
     */
    private List<DataviewRiskMerchantOrderInfoDO> assemble(List<Record> records) {
        return records.stream().map(merged -> {
            DataviewRiskMerchantOrderInfoDO orderInfoDO = new DataviewRiskMerchantOrderInfoDO();
            String shCreateTime = merged.getString("sh_create_time");
            String shUpdateTime = merged.getString("sh_update_time");
            String riskCreateTime = merged.getString("risk_create_time");
            String riskUpdateTime = merged.getString("risk_update_time");
            String shSendTime = merged.getString("sh_send_time");
            String riskSendTime = merged.getString("risk_send_time");
            String cashDepositPayTime = merged.getString("cash_deposit_pay_time");
            String riskPayTime = merged.getString("risk_pay_time");
            String orderNo = merged.getString("order_no");
            String riskCustomerId = merged.getString("risk_customer_id");
            String shCustomerId = merged.getString("sh_customer_id");
            String shMerchantUid = merged.getString("sh_merchant_uid");
            String riskMerchantId = merged.getString("risk_merchant_id");
            String shOrderStatus = merged.getString("sh_order_status");
            String shClosingTime = merged.getString("sh_closing_time");
            String riskCloseStatus = merged.getString("risk_close_status");
            String creditDifferenceAmt = merged.getString("credit_difference_amt");
            String repaymentPeriodCount = merged.getString("repayment_period_count");
            String riskBusinessChannel = merged.getString("risk_business_channel");
            String shMiniType = merged.getString("sh_mini_type");
            String shEquipmentType = merged.getString("sh_equipment_type");
            String riskSupervisoryMachine = merged.getString("risk_supervisory_machine");
            String riskFicoStatus = merged.getString("risk_fico_status");
            String riskArtificialAuditStatus = merged.getString("risk_artificial_audit_status");
            String riskOpinion = merged.getString("risk_opinion");
            String shPlatformCode = merged.getString("sh_platform_code");
            String frontPassFlag = merged.getString("front_pass_flag");
            String antiFraudPassFlag = merged.getString("anti_fraud_pass_flag");
            String bigDataPassFlag = merged.getString("big_data_pass_flag");
            String product = merged.getString("product");
            String rhTime = merged.getString("rh_time");
            String orderExtraType = merged.getString("order_extra_type");
            String hbPeriodNo = merged.getString("hb_period_no");
            String riskStrategy = merged.getString("risk_strategy");
            String downPaymentFlag = merged.getString("down_payment_flag");
            orderInfoDO.setRiskStrategy(stringToObject(riskStrategy,String.class));
            orderInfoDO.setHbPeriodNo(stringToObject(hbPeriodNo,Long.class));
            orderInfoDO.setOrderExtraType(stringToObject(orderExtraType, Integer.class));
            orderInfoDO.setRhTime(stringToObject(rhTime, LocalDateTime.class));
            orderInfoDO.setShCreateTime(stringToObject(shCreateTime, LocalDateTime.class));
            orderInfoDO.setShUpdateTime(stringToObject(shUpdateTime, LocalDateTime.class));
            orderInfoDO.setRiskCreateTime(stringToObject(riskCreateTime, LocalDateTime.class));
            orderInfoDO.setRiskUpdateTime(stringToObject(riskUpdateTime, LocalDateTime.class));
            orderInfoDO.setShSendTime(stringToObject(shSendTime, LocalDateTime.class));
            orderInfoDO.setRiskSendTime(stringToObject(riskSendTime, String.class));
            orderInfoDO.setCashDepositPayTime(stringToObject(cashDepositPayTime, LocalDateTime.class));
            orderInfoDO.setRiskPayTime(stringToObject(riskPayTime, LocalDateTime.class));
            orderInfoDO.setOrderNo(stringToObject(orderNo, String.class));
            orderInfoDO.setRiskCustomerId(stringToObject(riskCustomerId, Long.class));
            orderInfoDO.setShCustomerId(stringToObject(shCustomerId, Long.class));
            orderInfoDO.setShMerchantUid(stringToObject(shMerchantUid, String.class));
            orderInfoDO.setRiskMerchantId(stringToObject(riskMerchantId, Long.class));
            orderInfoDO.setShOrderStatus(stringToObject(shOrderStatus, Integer.class));
            orderInfoDO.setShClosingTime(stringToObject(shClosingTime, LocalDateTime.class));
            orderInfoDO.setRiskCloseStatus(stringToObject(riskCloseStatus, Integer.class));
            orderInfoDO.setCreditDifferenceAmt(stringToObject(creditDifferenceAmt, BigDecimal.class));
            orderInfoDO.setRepaymentPeriodCount(stringToObject(repaymentPeriodCount, Integer.class));
            orderInfoDO.setRiskBusinessChannel(stringToObject(riskBusinessChannel, Integer.class));
            orderInfoDO.setShMiniType(stringToObject(shMiniType, Integer.class));
            orderInfoDO.setShEquipmentType(stringToObject(shEquipmentType, Integer.class));
            orderInfoDO.setRiskSupervisoryMachine(stringToObject(riskSupervisoryMachine, Integer.class));
            orderInfoDO.setRiskFicoStatus(stringToObject(riskFicoStatus, Integer.class));
            orderInfoDO.setRiskArtificialAuditStatus(stringToObject(riskArtificialAuditStatus, Integer.class));
            orderInfoDO.setRiskOpinion(stringToObject(riskOpinion, String.class));
            orderInfoDO.setShPlatformCode(stringToObject(shPlatformCode, String.class));
            orderInfoDO.setFrontPassFlag(stringToObject(frontPassFlag, Integer.class));
            orderInfoDO.setAntiFraudPassFlag(stringToObject(antiFraudPassFlag, Integer.class));
            orderInfoDO.setBigDataPassFlag(stringToObject(bigDataPassFlag, Integer.class));
            orderInfoDO.setProduct(stringToObject(product, String.class));
            orderInfoDO.setDownPaymentFlag(stringToObject(downPaymentFlag, Integer.class));
            return orderInfoDO;
        }).filter(info -> StringUtils.isNotBlank(info.getOrderNo())).collect(Collectors.toList());
    }

    /**
     * 排除了订单号为空的订单
     */
    private List<DataviewRiskMerchantOrderInfoDO> assembleTidb2(List<ShOrderInfo> records) {
        return records.stream().map(merged -> {
            DataviewRiskMerchantOrderInfoDO orderInfoDO = new DataviewRiskMerchantOrderInfoDO();
            String shCreateTime = merged.getShCreateTime();
            String shUpdateTime = merged.getShUpdateTime();
            String riskCreateTime = merged.getRiskCreateTime();
            String riskUpdateTime = merged.getRiskUpdateTime();
            String shSendTime = merged.getShSendTime();
            String riskSendTime = merged.getRiskSendTime();
            String cashDepositPayTime = merged.getCashDepositPayTime();
            String riskPayTime = merged.getRiskPayTime();
            String orderNo = merged.getOrderNo();
            String riskCustomerId = merged.getRiskCustomerId();
            String shCustomerId = merged.getShCustomerId();
            String shMerchantUid = merged.getShMerchantUid();
            String riskMerchantId = merged.getRiskMerchantId();
            String shOrderStatus = merged.getShOrderStatus();
            String shClosingTime = merged.getShClosingTime();
            String riskCloseStatus = merged.getRiskCloseStatus();
            String creditDifferenceAmt = merged.getCreditDifferenceAmt();
            String repaymentPeriodCount = merged.getRepaymentPeriodCount();
            String riskBusinessChannel = merged.getRiskBusinessChannel();
            String shMiniType = merged.getShMiniType();
            String shEquipmentType = merged.getShEquipmentType();
            String riskSupervisoryMachine = merged.getRiskSupervisoryMachine();
            String riskFicoStatus = merged.getRiskFicoStatus();
            String riskArtificialAuditStatus = merged.getRiskArtificialAuditStatus();
            String riskOpinion = merged.getRiskOpinion();
            String shPlatformCode = merged.getShPlatformCode();
            String frontPassFlag = merged.getFrontPassFlag();
            String antiFraudPassFlag = merged.getAntiFraudPassFlag();
            String bigDataPassFlag = merged.getBigDataPassFlag();
            String product = merged.getProduct();
            String rhTime = merged.getRhTime();
            String orderExtraType = merged.getOrderExtraType();
            String hbPeriodNo = merged.getHbPeriodNo();
            String riskStrategy = merged.getRiskStrategy();
            String downPaymentFlag = merged.getDownPaymentFlag();
            orderInfoDO.setRiskStrategy(stringToObject(riskStrategy,String.class));
            orderInfoDO.setHbPeriodNo(stringToObject(hbPeriodNo,Long.class));
            orderInfoDO.setOrderExtraType(stringToObject(orderExtraType, Integer.class));
            orderInfoDO.setRhTime(stringToObjectTidb(rhTime, LocalDateTime.class));
            orderInfoDO.setShCreateTime(stringToObjectTidb(shCreateTime, LocalDateTime.class));
            orderInfoDO.setShUpdateTime(stringToObjectTidb(shUpdateTime, LocalDateTime.class));
            orderInfoDO.setRiskCreateTime(stringToObjectTidb(riskCreateTime, LocalDateTime.class));
            orderInfoDO.setRiskUpdateTime(stringToObjectTidb(riskUpdateTime, LocalDateTime.class));
            orderInfoDO.setShSendTime(stringToObjectTidb(shSendTime, LocalDateTime.class));
            orderInfoDO.setRiskSendTime(stringToObject(riskSendTime, String.class));
            orderInfoDO.setCashDepositPayTime(stringToObjectTidb(cashDepositPayTime, LocalDateTime.class));
            orderInfoDO.setRiskPayTime(stringToObjectTidb(riskPayTime, LocalDateTime.class));
            orderInfoDO.setOrderNo(stringToObject(orderNo, String.class));
            orderInfoDO.setRiskCustomerId(stringToObject(riskCustomerId, Long.class));
            orderInfoDO.setShCustomerId(stringToObject(shCustomerId, Long.class));
            orderInfoDO.setShMerchantUid(stringToObject(shMerchantUid, String.class));
            orderInfoDO.setRiskMerchantId(stringToObject(riskMerchantId, Long.class));
            orderInfoDO.setShOrderStatus(stringToObject(shOrderStatus, Integer.class));
            orderInfoDO.setShClosingTime(stringToObjectTidb(shClosingTime, LocalDateTime.class));
            orderInfoDO.setRiskCloseStatus(stringToObject(riskCloseStatus, Integer.class));
            orderInfoDO.setCreditDifferenceAmt(stringToObject(creditDifferenceAmt, BigDecimal.class));
            orderInfoDO.setRepaymentPeriodCount(stringToObject(repaymentPeriodCount, Integer.class));
            orderInfoDO.setRiskBusinessChannel(stringToObject(riskBusinessChannel, Integer.class));
            orderInfoDO.setShMiniType(stringToObject(shMiniType, Integer.class));
            orderInfoDO.setShEquipmentType(stringToObject(shEquipmentType, Integer.class));
            orderInfoDO.setRiskSupervisoryMachine(stringToObject(riskSupervisoryMachine, Integer.class));
            orderInfoDO.setRiskFicoStatus(stringToObject(riskFicoStatus, Integer.class));
            orderInfoDO.setRiskArtificialAuditStatus(stringToObject(riskArtificialAuditStatus, Integer.class));
            orderInfoDO.setRiskOpinion(stringToObject(riskOpinion, String.class));
            orderInfoDO.setShPlatformCode(stringToObject(shPlatformCode, String.class));
            orderInfoDO.setFrontPassFlag(stringToObject(frontPassFlag, Integer.class));
            orderInfoDO.setAntiFraudPassFlag(stringToObject(antiFraudPassFlag, Integer.class));
            orderInfoDO.setBigDataPassFlag(stringToObject(bigDataPassFlag, Integer.class));
            orderInfoDO.setProduct(stringToObject(product, String.class));
            orderInfoDO.setDownPaymentFlag(stringToObject(downPaymentFlag, Integer.class));
            return orderInfoDO;
        }).filter(info -> StringUtils.isNotBlank(info.getOrderNo())).collect(Collectors.toList());
    }

    /**
     * 排除了订单号为空的订单
     */
    private List<DataviewRiskMerchantOrderInfoDO> assembleTidb(List<Map<String, String>> records) {
        return records.stream().map(merged -> {
            DataviewRiskMerchantOrderInfoDO orderInfoDO = new DataviewRiskMerchantOrderInfoDO();
            String shCreateTime = null==merged.get("sh_create_time") ? null : ((Object)merged.get("sh_create_time")).toString();
            String shUpdateTime = null==merged.get("sh_update_time") ? null : ((Object)merged.get("sh_update_time")).toString();
            String riskCreateTime = null==merged.get("risk_create_time") ? null : ((Object)merged.get("risk_create_time")).toString();
            String riskUpdateTime = null==merged.get("risk_update_time") ? null : ((Object)merged.get("risk_update_time")).toString();
            String shSendTime = null==merged.get("sh_send_time") ? null : ((Object)merged.get("sh_send_time")).toString();
            String riskSendTime = null==merged.get("risk_send_time") ? null : ((Object)merged.get("risk_send_time")).toString();
            String cashDepositPayTime = null==merged.get("cash_deposit_pay_time") ? null : ((Object)merged.get("cash_deposit_pay_time")).toString();
            String riskPayTime = null==merged.get("risk_pay_time") ? null :((Object)merged.get("risk_pay_time")).toString();
            String orderNo = null==merged.get("order_no") ? null :((Object)merged.get("order_no")).toString();
            String riskCustomerId = null==merged.get("risk_customer_id") ? null :((Object)merged.get("risk_customer_id")).toString();
            String shCustomerId = null==merged.get("sh_customer_id") ? null :((Object)merged.get("sh_customer_id")).toString();
            String shMerchantUid = null==merged.get("sh_merchant_uid") ? null :((Object)merged.get("sh_merchant_uid")).toString();
            String riskMerchantId = null==merged.get("risk_merchant_id") ? null :((Object)merged.get("risk_merchant_id")).toString();
            String shOrderStatus = null==merged.get("sh_order_status") ? null :((Object)merged.get("sh_order_status")).toString();
            String shClosingTime = null==merged.get("sh_closing_time") ? null :((Object)merged.get("sh_closing_time")).toString();
            String riskCloseStatus = null==merged.get("risk_close_status") ? null :((Object)merged.get("risk_close_status")).toString();
            String creditDifferenceAmt = null==merged.get("credit_difference_amt") ? null :((Object)merged.get("credit_difference_amt")).toString();
            String repaymentPeriodCount = null==merged.get("repayment_period_count") ? null :((Object)merged.get("repayment_period_count")).toString();
            String riskBusinessChannel = null==merged.get("risk_business_channel") ? null :((Object)merged.get("risk_business_channel")).toString();
            String shMiniType = null==merged.get("sh_mini_type") ? null :((Object)merged.get("sh_mini_type")).toString();
            String shEquipmentType = null==merged.get("sh_equipment_type") ? null :((Object)merged.get("sh_equipment_type")).toString();
            String riskSupervisoryMachine = null==merged.get("risk_supervisory_machine") ? null :((Object)merged.get("risk_supervisory_machine")).toString();
            String riskFicoStatus = null==merged.get("risk_fico_status") ? null :((Object)merged.get("risk_fico_status")).toString();
            String riskArtificialAuditStatus = null==merged.get("risk_artificial_audit_status") ? null :((Object)merged.get("risk_artificial_audit_status")).toString();
            String riskOpinion = null==merged.get("risk_opinion") ? null :((Object)merged.get("risk_opinion")).toString();
            String shPlatformCode = null==merged.get("sh_platform_code") ? null :((Object)merged.get("sh_platform_code")).toString();
            String frontPassFlag = null==merged.get("front_pass_flag") ? null :((Object)merged.get("front_pass_flag")).toString();
            String antiFraudPassFlag = null==merged.get("anti_fraud_pass_flag") ? null :((Object)merged.get("anti_fraud_pass_flag")).toString();
            String bigDataPassFlag = null==merged.get("big_data_pass_flag") ? null :((Object)merged.get("big_data_pass_flag")).toString();
            String product = null==merged.get("product") ? null :((Object)merged.get("product")).toString();
            String rhTime = null==merged.get("rh_time") ? null :((Object)merged.get("rh_time")).toString();
            String orderExtraType = null==merged.get("order_extra_type") ? null :((Object)merged.get("order_extra_type")).toString();
            String hbPeriodNo = null==merged.get("hb_period_no") ? null :((Object)merged.get("hb_period_no")).toString();
            String riskStrategy = null==merged.get("risk_strategy") ? null :((Object)merged.get("risk_strategy")).toString();
            String downPaymentFlag = null==merged.get("down_payment_flag") ? null :((Object)merged.get("down_payment_flag")).toString();
            orderInfoDO.setRiskStrategy(stringToObject(riskStrategy,String.class));
            orderInfoDO.setHbPeriodNo(stringToObject(hbPeriodNo,Long.class));
            orderInfoDO.setOrderExtraType(stringToObject(orderExtraType, Integer.class));
            orderInfoDO.setRhTime(stringToObject(rhTime, LocalDateTime.class));
            orderInfoDO.setShCreateTime(stringToObjectTidb(shCreateTime, LocalDateTime.class));
            orderInfoDO.setShUpdateTime(stringToObjectTidb(shUpdateTime, LocalDateTime.class));
            orderInfoDO.setRiskCreateTime(stringToObjectTidb(riskCreateTime, LocalDateTime.class));
            orderInfoDO.setRiskUpdateTime(stringToObjectTidb(riskUpdateTime, LocalDateTime.class));
            orderInfoDO.setShSendTime(stringToObjectTidb(shSendTime, LocalDateTime.class));
            orderInfoDO.setRiskSendTime(stringToObject(riskSendTime, String.class));
            orderInfoDO.setCashDepositPayTime(stringToObjectTidb(cashDepositPayTime, LocalDateTime.class));
            orderInfoDO.setRiskPayTime(stringToObjectTidb(riskPayTime, LocalDateTime.class));
            orderInfoDO.setOrderNo(stringToObject(orderNo, String.class));
            orderInfoDO.setRiskCustomerId(stringToObject(riskCustomerId, Long.class));
            orderInfoDO.setShCustomerId(stringToObject(shCustomerId, Long.class));
            orderInfoDO.setShMerchantUid(stringToObject(shMerchantUid, String.class));
            orderInfoDO.setRiskMerchantId(stringToObject(riskMerchantId, Long.class));
            orderInfoDO.setShOrderStatus(stringToObject(shOrderStatus, Integer.class));
            orderInfoDO.setShClosingTime(stringToObjectTidb(shClosingTime, LocalDateTime.class));
            orderInfoDO.setRiskCloseStatus(stringToObject(riskCloseStatus, Integer.class));
            orderInfoDO.setCreditDifferenceAmt(stringToObject(creditDifferenceAmt, BigDecimal.class));
            orderInfoDO.setRepaymentPeriodCount(stringToObject(repaymentPeriodCount, Integer.class));
            orderInfoDO.setRiskBusinessChannel(stringToObject(riskBusinessChannel, Integer.class));
            orderInfoDO.setShMiniType(stringToObject(shMiniType, Integer.class));
            orderInfoDO.setShEquipmentType(stringToObject(shEquipmentType, Integer.class));
            orderInfoDO.setRiskSupervisoryMachine(stringToObject(riskSupervisoryMachine, Integer.class));
            orderInfoDO.setRiskFicoStatus(stringToObject(riskFicoStatus, Integer.class));
            orderInfoDO.setRiskArtificialAuditStatus(stringToObject(riskArtificialAuditStatus, Integer.class));
            orderInfoDO.setRiskOpinion(stringToObject(riskOpinion, String.class));
            orderInfoDO.setShPlatformCode(stringToObject(shPlatformCode, String.class));
            orderInfoDO.setFrontPassFlag(stringToObject(frontPassFlag, Integer.class));
            orderInfoDO.setAntiFraudPassFlag(stringToObject(antiFraudPassFlag, Integer.class));
            orderInfoDO.setBigDataPassFlag(stringToObject(bigDataPassFlag, Integer.class));
            orderInfoDO.setProduct(stringToObject(product, String.class));
            orderInfoDO.setDownPaymentFlag(stringToObject(downPaymentFlag, Integer.class));
            return orderInfoDO;
        }).filter(info -> StringUtils.isNotBlank(info.getOrderNo())).collect(Collectors.toList());
    }


    public <T> T stringToObject(String str, Class<T> type) {
        if (StringUtils.isNotBlank(str) && !Objects.equals(str, "\\N") && !Objects.equals(str, "NaN") && !Objects.equals(str, "N")) {
            if (type == Integer.class) {
                return (T) Integer.valueOf(str);
            } else if (type == Long.class) {
                return (T) Long.valueOf(str);
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal(str);
            } else if (type == LocalDateTime.class) {
                return (T) LocalDateTime.parse(str, FORMAT_TIME);
            } else {
                log.error("类型转换异常");
                return null;
            }
        } else {
            if (type == Integer.class) {
                return (T) Integer.valueOf("-1");
            } else if (type == Long.class) {
                return (T) Long.valueOf("0");
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal("0");
            } else {
                return null;
            }
        }
    }

    public <T> T stringToObjectTidb(String str, Class<T> type) {
        if (StringUtils.isNotBlank(str) && !Objects.equals(str, "\\N") && !Objects.equals(str, "NaN") && !Objects.equals(str, "N")) {
            if (type == Integer.class) {
                return (T) Integer.valueOf(str);
            } else if (type == Long.class) {
                return (T) Long.valueOf(str);
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal(str);
            } else if (type == LocalDateTime.class) {
                if(str.contains(".")){
                    int index = str.indexOf(".");
                    String res = str.substring(index + 1);
                    if(res.length() == 1){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB);
                    }else if(res.length() == 2){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB2);
                    }
                    else if(res.length() == 3){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB3);
                    }
                    else if(res.length() == 4){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB4);
                    }
                    else {
                        return (T) LocalDateTime.parse(str, FORMAT_TIME);
                    }
                }
                return (T) LocalDateTime.parse(str, FORMAT_TIME);
            } else {
                log.error("类型转换异常");
                return null;
            }
        } else {
            if (type == Integer.class) {
                return (T) Integer.valueOf("-1");
            } else if (type == Long.class) {
                return (T) Long.valueOf("0");
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal("0");
            } else {
                return null;
            }
        }
    }

}
