package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPassCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorPassCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorPassCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorPassCustomerService indicatorPassCustomerService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorPassCustomerDO> collectMap = new HashMap<>();
        countLtNhRegisterCount(ds, sTime, eTime, collectMap);
        countRegisterNotOrder(ds, sTime, eTime, collectMap);
        countRiskPassOutServiceCount(ds, sTime, eTime, collectMap);
        countLtNdRefusedCount(ds, sTime, eTime, collectMap);
        countRiskPassPlanTotal(ds, sTime, eTime, collectMap);
        countPlanBondRate(ds, sTime, eTime, collectMap);
        ArrayList<IndicatorPassCustomerDO> list = new ArrayList<>(collectMap.values());
        if (CollUtil.isNotEmpty(list)) {
            indicatorPassCustomerService.saveOrUpdateBatch(list,2000);
        }
        // for (IndicatorPassCustomerDO value : collectMap.values()) {
        //     indicatorPassCustomerService.saveOrUpdate(value);
        // }
    }


    /**
     * N小时内注册人数 刷30天数据
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                    inner join rent_customer c " +
                "                    on get_json_object(c.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        inner join ( " +
                "                             select customer_id,to_char(create_time, 'yyyy-mm-dd') day,mini_type " +
                "from( " +
                "                                 select a.customer_id,a.mini_type,a.create_time  " +
                "                                    from rent_order a   " +
                "                                            inner join cl_loan b on a.no = b.loanno   " +
                "                                    where a.is_deleted = 0   " +
                "                                    and a.merchant_id = 100   " +
                "                                    and a.parent_id = 0   " +
                "                                    and a.type = 1   " +
                "                                    and a.biz_type = 2   " +
                "                                    and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "                                    and b.riskstatus = 20   " +
                "                                    and a.ds = " + ds +
                "                                    and b.ds = " + ds +
                "                                    order by a.create_time desc " +
                "                             ) group by customer_id " +
                "                                        ,mini_type " +
                "                                        ,to_char(create_time, 'yyyy-mm-dd') " +
                "               ) b on a.mini_type = b.mini_type and c.id = b.customer_id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and c.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                "where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 已注册未下单人数，刷当天数据
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRegisterNotOrder(String ds, String sTime, String eTime,
                                      Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count " +
                "from ( " +
                "    select customer_id,min(create_time) create_time,mini_type from( " +
                "        select a.customer_id,a.mini_type,a.create_time  " +
                "        from rent_order a   " +
                "                inner join cl_loan b on a.no = b.loanno   " +
                "        where a.is_deleted = 0   " +
                "        and a.merchant_id = 100   " +
                "        and a.parent_id = 0   " +
                "        and a.type = 1   " +
                "        and a.biz_type = 2   " +
                "        and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "        and b.riskstatus = 20   " +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "        order by a.create_time desc " +
                "    ) group by customer_id " +
                "            ,mini_type " +
                "            ,to_char(create_time, 'yyyy-mm-dd') " +
                ") a inner join rent_customer b on b.id = a.customer_id " +
                "    inner join ( " +
                "        select min(create_time) create_time,customer_id from ( " +
                "            select customer_id,create_time from rent_order where customer_id in ( " +
                "                select a.customer_id " +
                "                    from rent_order a   " +
                "                            inner join cl_loan b on a.no = b.loanno   " +
                "                    where a.is_deleted = 0   " +
                "                    and a.merchant_id = 100   " +
                "                    and a.parent_id = 0   " +
                "                    and a.type = 1   " +
                "                    and a.biz_type = 2   " +
                "                    and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "                    and b.riskstatus = 20   " +
                "                    and a.ds = " + ds +
                "                    and b.ds = " + ds +
                "                    order by a.create_time desc " +
                "            ) " +
                "            and ds = " + ds + " order by create_time desc " +
                "        ) group by customer_id " +
                "    ) c on a.customer_id = c.customer_id and to_char(a.create_time,'yyyy-mm-dd') =  to_char(c" +
                ".create_time,'yyyy-mm-dd') " +
                "    where DATEDIFF(a.create_time,b.create_time)>1 and b.ds= " + ds +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterNotOrder(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控通过人数中，服务已结束客户，刷30天数据
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRiskPassOutServiceCount(String ds, String sTime, String eTime,
                                             Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count  " +
                "from (   " +
                "    select a.mini_type,min(a.create_time) create_time,a.customer_id   " +
                "    from rent_order a  " +
                "        inner join cl_loan b on a.no = b.loanno   " +
                "    where a.is_deleted = 0   " +
                "        and a.merchant_id = 100   " +
                "        and a.parent_id = 0   " +
                "        and a.type = 1   " +
                "        and a.biz_type = 2   " +
                "        and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "        and b.riskstatus = 20   " +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "        group by a.mini_type,a.customer_id,to_char(a.create_time,'yyyy-mm-dd') " +
                ") a    " +
                "    inner join (   " +
                "        select min(create_time) create_time,max(termination) termination,max(status) status," +
                "           customer_id from (   " +
                "            select termination,customer_id,create_time,status from rent_order where customer_id in (" +
                "                select customer_id   " +
                "                    from rent_order    " +
                "                    where is_deleted = 0   " +
                "                    and merchant_id=100   " +
                "                    and parent_id = 0   " +
                "                    and type = 1   " +
                "                    and biz_type = 2   " +
                "                    and payment_time is not null   " +
                "                    and ds = " + ds +
                "            )   " +
                "            and ds = " + ds + " order by create_time asc   " +
                "        ) group by customer_id   " +
                "    ) c on a.customer_id = c.customer_id and  " +
                "    to_char(a.create_time,'yyyy-mm-dd') =  to_char(c.create_time,'yyyy-mm-dd')   " +
                "    where c.termination !=5 and c.status in (220,330) " +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd');  ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskPassOutServiceCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控通过,有被风控拒绝的订单，且最近风控时间距离本次提交订单N天以上客户数，刷30天数据
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countLtNdRefusedCount(String ds, String sTime, String eTime,
                                      Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select  A.day " +
                "        ,A.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  x.mini_type " +
                "                    ,to_char(x.create_time, 'yyyy-mm-dd') as day " +
                "                    ,x.customer_id " +
                "            from    （ " +
                "                select a.*   " +
                "                        from rent_order a  " +
                "                    inner join cl_loan b on a.no = b.loanno   " +
                "                    where a.is_deleted = 0   " +
                "                    and a.merchant_id = 100   " +
                "                    and a.parent_id = 0   " +
                "                    and a.type = 1   " +
                "                    and a.biz_type = 2   " +
                "                    and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "                    and b.riskstatus = 20   " +
                "                    and a.ds = " + ds +
                "                    and b.ds = " + ds +
                "            ） x " +
                "            inner join ( " +
                "                           select  min(risktime) risktime " +
                "                                   ,min(id) order_id " +
                "                                   ,customer_id " +
                "                           from    ( " +
                "                                       select  b.risktime " +
                "                                               ,a.customer_id " +
                "                                               ,a.id " +
                "                                       from    rent_order a inner " +
                "                                       join    cl_loan b " +
                "                                       on      a.no = b.loanno " +
                "                                       where   a.is_deleted = 0 " +
                "                                       and     a.merchant_id = 100 " +
                "                                       and     a.parent_id = 0 " +
                "                                       and     a.type = 1 " +
                "                                       and     a.biz_type = 2 " +
                "                                       and     b.riskstatus = 15 " +
                "                                       and     a.ds = " + ds +
                "                                       and     b.ds = " + ds +
                "                                       order by b.risktime asc " +
                "                                   )  " +
                "                           group by customer_id " +
                "                       ) D " +
                "            on      x.customer_id = D.customer_id and x.id != D.order_id " +
                "            where   x.is_deleted = 0 " +
                "            and     x.merchant_id = 100 " +
                "            and     x.parent_id = 0 " +
                "            and     x.type = 1 " +
                "            and     x.biz_type = 2 " +
                "            and     x.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     x.ds = " + ds +
                "            and     datediff(x.create_time,D.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "            group by x.mini_type " +
                "                     ,to_char(x.create_time, 'yyyy-mm-dd') " +
                "                     ,x.customer_id " +
                "        ) A " +
                "group by A.mini_type " +
                "         ,A.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控通过中,金融方案分组,加是否免押分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRiskPassPlanTotal(String ds, String sTime, String eTime,
                                       Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select a.mini_type,c.rate_config_type,c.bond_free_status, " +
                "    to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "    count(distinct a.customer_id) count   " +
                "from rent_order a   " +
                "     inner join cl_loan b on a.no = b.loanno   " +
                "     inner join rent_order_finance_detail c on a.id=c.order_id " +
                "where a.is_deleted = 0   " +
                "  and a.merchant_id = 100   " +
                "  and a.parent_id = 0   " +
                "  and a.type = 1   " +
                "  and a.biz_type = 2   " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "  and b.riskstatus = 20   " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'), " +
                "  c.bond_free_status,c.rate_config_type, " +
                "  IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer bondFreeStatus = Integer.valueOf(record.getString("bond_free_status"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                updateDO.setPlanThirdTotal(Optional.ofNullable(updateDO.getPlanThirdTotal()).orElse(0) + count);
            } else if (rateConfigType == 10) {
                updateDO.setPlanFirstTotal(Optional.ofNullable(updateDO.getPlanFirstTotal()).orElse(0) + count);
            } else {
                updateDO.setPlanSecondTotal(Optional.ofNullable(updateDO.getPlanSecondTotal()).orElse(0) + count);
            }
            if (rateConfigType == 10) {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanFirstFreeBond(Optional.ofNullable(updateDO.getPlanFirstFreeBond()).orElse(0) + count);
                } else {
                    updateDO.setPlanFirstNotFreeBond(Optional.ofNullable(updateDO.getPlanFirstNotFreeBond()).orElse(0) + count);
                }
            } else {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanSecondFreeBond(Optional.ofNullable(updateDO.getPlanSecondFreeBond()).orElse(0) + count);
                } else {
                    updateDO.setPlanSecondNotFreeBond(Optional.ofNullable(updateDO.getPlanSecondNotFreeBond()).orElse(0) + count);
                }
            }
        }
    }


    /**
     * 查询金融方案保证金期数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countPlanBondRate(String ds, String sTime, String eTime,
                                  Map<String, IndicatorPassCustomerDO> collectMap) {
        String sql = " select a.mini_type,c.rate_config_type, " +
                "       to_char(a.create_time,'yyyy-mm-dd') day, " +
                "       IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "       c.bond_rate, " +
                "       count(1) count " +
                "from rent_order a " +
                "    inner join cl_loan b on a.no = b.loanno " +
                "    inner join rent_order_finance_detail c on a.id=c.order_id " +
                "where a.is_deleted = 0 " +
                "    and a.merchant_id = 100 " +
                "    and a.parent_id = 0 " +
                "    and a.type = 1 " +
                "    and a.biz_type = 2 " +
                "    and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "    and b.riskstatus = 20 " +
                "    and a.ds = " + ds +
                "    and b.ds = " + ds +
                "    and c.ds = " + ds +
                "    group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "    c.bond_rate,IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorPassCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThirdOneTerm(Optional.ofNullable(updateDO.getPlanThirdOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanThirdTwoTerm(Optional.ofNullable(updateDO.getPlanThirdTwoTerm()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanFirstOneTerm(Optional.ofNullable(updateDO.getPlanFirstOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanFirstTwoTerm(Optional.ofNullable(updateDO.getPlanFirstTwoTerm()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanSecondOneTerm(Optional.ofNullable(updateDO.getPlanSecondOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanSecondTwoTerm(Optional.ofNullable(updateDO.getPlanSecondTwoTerm()).orElse(0) + count);
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorPassCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorPassCustomerDO iord = new IndicatorPassCustomerDO();
            iord.setMiniType(miniType);
            iord.setCountDay(countDay);
            iord.setRiskPassInServiceCount(0);
            iord.setLtNhRegisterCount(0);
            iord.setRegisterNotOrder(0);
            iord.setRiskPassOutServiceCount(0);
            iord.setLtNdRefusedCount(0);
            iord.setPlanFirstTotal(0);
            iord.setPlanFirstOneTerm(0);
            iord.setPlanFirstTwoTerm(0);
            iord.setPlanFirstFreeBond(0);
            iord.setPlanFirstNotFreeBond(0);
            iord.setPlanSecondTotal(0);
            iord.setPlanSecondOneTerm(0);
            iord.setPlanSecondTwoTerm(0);
            iord.setPlanSecondFreeBond(0);
            iord.setPlanSecondNotFreeBond(0);
            iord.setPlanThirdTotal(0);
            iord.setPlanThirdOneTerm(0);
            iord.setPlanThirdTwoTerm(0);
            miniType2Map.put(key, iord);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}