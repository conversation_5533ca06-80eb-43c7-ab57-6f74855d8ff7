package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderResideDO;
import qnvip.data.overview.service.dataindicators.IndicatorOrderResideService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/1/15
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorOrderResideBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorOrderResideService indicatorOrderResideService;


    /**
     * 启动计算任务
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorOrderResideDO> collectMap = new HashMap<>();
        countOrderResideLevel(ds, sTime, eTime, collectMap);
        for (IndicatorOrderResideDO value : collectMap.values()) {
            indicatorOrderResideService.saveOrUpdate(value);
        }
    }


    /**
     * 下单时长分组统计
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    private void countOrderResideLevel(String ds, String sTime, String eTime,
                                      Map<String, IndicatorOrderResideDO> collectMap) {
        String sql = " select level,mini_type,day,count(1) count from (  " +
                "        select (case when buy_time <19 then 0  " +
                "                    when buy_time <67 then 1  " +
                "                    when buy_time <211 then 2  " +
                "                    when buy_time <434 then 3   " +
                "                    else 4 end  " +
                "            ) level,mini_type,day from (  " +
                "            select  mini_type,to_char(report_time,'yyyy-mm-dd') day,  " +
                "               unix_timestamp(min(create_time))-unix_timestamp(min(report_time)) buy_time from (  " +
                "                    select A.mini_type,A.customer_id,A.create_time,B.report_time from (  " +
                "                        select customer_id,create_time,mini_type  " +
                "                        from rent_order   " +
                "                        where is_deleted = 0  " +
                "                        and merchant_id=100  " +
                "                        and parent_id = 0  " +
                "                        and type = 1  " +
                "                        and biz_type = 2  " +
                "                        and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                        and ds = " + ds +
                "                    ) A   " +
                "                    inner join (  " +
                "                        select mini_type,min(report_time) report_time,customer_id from (  " +
                "                                select  a.mini_type  " +
                "                                        ,a.report_time  " +
                "                                        , b.id customer_id  " +
                "                                    from dataview_track_enter_applets a   " +
                "                                    inner join rent_customer b on get_json_object(b.bindinfo, '$[0]" +
                ".miniUserId') = a.customer_third_id  " +
                "                                    where a.action_type = 1  " +
                "                                    and a.ds = " + ds +
                "                                    and b.ds = " + ds +
                "                                    and a.report_time between '" + sTime + "'  " +
                "                                        and '" + eTime + "'  " +
                "                                    order by a.report_time desc  " +
                "                            ) group by mini_type,customer_id,to_char(report_time, 'yyyy-mm-dd')  " +
                "                    ) B on A.customer_id = B.customer_id and A.mini_type = B.mini_type  " +
                "   and     to_char(A.create_time,'yyyy-mm-dd') = to_char(B.report_time,'yyyy-mm-dd')"+
                "                    order by A.create_time desc  " +
                "            ) group by customer_id,to_char(report_time,'yyyy-mm-dd'),mini_type  " +
                "        )  " +
                ")group by mini_type,day,level; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderResideDO updateDO = collectMap.get(getMapKey(miniType, day));
            Integer level = Integer.valueOf(record.getString("level"));
            Integer count = Integer.valueOf(record.getString("count"));
            switch (level) {
                case 0:
                    updateDO.setLevelZero(count);
                    break;
                case 1:
                    updateDO.setLevelOne(count);
                    break;
                case 2:
                    updateDO.setLevelTwo(count);
                    break;
                case 3:
                    updateDO.setLevelThree(count);
                    break;
                case 4:
                    updateDO.setLevelFour(count);
                    break;
                default:
                    break;
            }
        }
    }


    private void initMap(Map<String, IndicatorOrderResideDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorOrderResideDO iord = new IndicatorOrderResideDO();
            iord.setMiniType(miniType);
            iord.setCountDay(countDay);
            iord.setLevelZero(0);
            iord.setLevelOne(0);
            iord.setLevelTwo(0);
            iord.setLevelThree(0);
            iord.setLevelFour(0);
            miniType2Map.put(key, iord);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }
}