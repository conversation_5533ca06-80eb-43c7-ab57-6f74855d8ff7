package qnvip.data.overview.business.whole.report;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeMerchantReportDO;
import qnvip.data.overview.service.whole.report.WholeMerchantReportService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class WholeMerchantReportBusiness {

    private final OdpsUtil odpsUtil;
    private final WholeMerchantReportService wholeMerchantReportService;

    @Deprecated
    public void execData(String ds) {
        String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);
        if (StringUtils.isBlank(ds)) {
            ds = LocalDate.now().format(WholeLawReportBusiness.YYYYMMDD);
            time = LocalDate.now().format(WholeLawReportBusiness.YYYY_MM_DD);
        }
        time = "'" + time + "'";
        String sql = "with dt as (" +
                "       select " + ds + " as ds" +
                "   )" +
                "   select a.time       count_day," +
                "          nvl(a.订单量,0)        order_cnt," +
                "          nvl(a.下单人数,0)       order_uv," +
                "          nvl(b.通审人数,0)       audit_uv," +
                "          nvl(c.支付订单,0)       pay_cnt," +
                "          nvl(c.支付用户,0)       pay_uv," +
                "          nvl(c.最终支付用户,0)," +
                "          nvl(c.最终支付订单,0)," +
                "          nvl(d.发货订单,0)       send_cnt," +
                "          nvl(e.签收订单,0)," +
                "          nvl(c.待发货订单,0)," +
                "          nvl(b.待支付订单,0)," +
                "          nvl(b.未支付关闭,0)," +
                "          nvl(a.全额订单,0)       ," +
                "          nvl(a.差额订单,0)," +
                "          nvl(b.全额订单通审,0)     whole_audit_cnt," +
                "          nvl(b.差额订单通审,0)     margin_audit_cnt," +
                "          nvl(f.全款订单,0)," +
                "          nvl(f.全款支付,0)," +
                "          nvl(b.通审订单,0)        audit_cnt," +
                "          nvl(b.差额订单通审支付_人,0) margin_pay_uv," +
                "          nvl(b.差额订单通审支付_单,0) margin_pay_cnt," +
                "          nvl(b.全额订单通审支付_人,0) whole_pay_uv," +
                "          nvl(b.全额订单通审支付_单,0) whole_pay_cnt," +
                "          nvl(b.全额订单通审_人,0)   whole_audit_uv," +
                "          nvl(b.差额订单通审_人,0)   margin_audit_uv," +
                "          nvl(c.信审关闭订单,0)     credit_close," +
                "          nvl(d.48小时发货量,0)    hour48_send_cnt," +
                "          nvl(d.总租金,0)        finance_amt," +
                "          nvl(d.进货价 ,0)       purchase_price" +
                "   from (" +
                "            select date(so.create_time)                                              time," +
                "                   count(distinct so.order_uid)                                      订单量," +
                "                   count(distinct so.customer_id)                                    下单人数," +
                "                   count(distinct if(credit_difference_amt > 0, so.order_uid, null)) 差额订单," +
                "                   count(distinct if(credit_difference_amt = 0, so.order_uid, null)) 全额订单" +
                "            from sh_order so" +
                "                     left join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "            where so.is_deleted = 0" +
                "              and date(so.create_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "              and so.ds = (select ds from dt)" +
                "              and sof.ds = (select ds from dt)" +
                "            group by date(so.create_time)) a" +
                "            left join (" +
                "       select date(soar.create_time)                                                                            time," +
                "              count(distinct so.order_uid)                                                                      通审订单," +
                "              count(distinct so.customer_id)                                                                    通审人数," +
                "              count(distinct if(credit_difference_amt > 0, so.order_uid, null))                                 差额订单通审," +
                "              count(distinct if(credit_difference_amt > 0, so.customer_id, null))                               差额订单通审_人," +
                "              count(distinct" +
                "                    if(credit_difference_amt > 0 and so.cash_deposit_pay_time is not null, so.order_uid, null)) 差额订单通审支付_单," +
                "              count(distinct if(credit_difference_amt > 0 and so.cash_deposit_pay_time is not null, so.customer_id," +
                "                                null))                                                                          差额订单通审支付_人," +
                "              count(distinct if(credit_difference_amt = 0, so.order_uid, null))                                 全额订单通审," +
                "              count(distinct if(credit_difference_amt = 0, so.customer_id, null))                               全额订单通审_人," +
                "              count(distinct" +
                "                    if(credit_difference_amt = 0 and so.cash_deposit_pay_time is not null, so.order_uid, null)) 全额订单通审支付_单," +
                "              count(distinct if(credit_difference_amt = 0 and so.cash_deposit_pay_time is not null, so.customer_id," +
                "                                null))                                                                          全额订单通审支付_人," +
                "              count(distinct if(so.closing_time is not null, so.order_uid, null))                               未支付关闭," +
                "              count(distinct" +
                "                    if(so.cash_deposit_pay_time is null or (date(so.cash_deposit_pay_time) > date(soar.create_time) + 2)," +
                "                       so.customer_id, null))                                                                   待支付订单" +
                "       from sh_order so" +
                "                left join sh_risk_alchemist_result soar on so.order_uid = soar.order_uid" +
                "                left join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "       where so.is_deleted = 0" +
                "         and soar.audit_status = 10" +
                "         and repayment_period_count > 1" +
                "         and so.ds = (select ds from dt)" +
                "         and sof.ds = (select ds from dt)" +
                "         and soar.ds = (select ds from dt)" +
                "         and date(so.create_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "       group by date(soar.create_time)) b on a.time = b.time" +
                "            left join (" +
                "       select date(so.cash_deposit_pay_time)                                                                      time," +
                "              count(distinct so.order_uid)                                                                        支付订单," +
                "              count(distinct if(so.closing_time is null, so.order_uid, null))                                     最终支付订单," +
                "              count(distinct so.customer_id)                                                                      支付用户," +
                "              count(distinct if(so.closing_time is null, so.customer_id, null))                                   最终支付用户," +
                "              count(distinct" +
                "                    if(sol.send_time is null or date(sol.send_time) > (date(so.cash_deposit_pay_time) + 2), so.order_uid," +
                "                       null))                                                                                     待发货订单," +
                "              count(distinct if(so.credit_audit_status != 0 and so.closing_time is not null, so.order_uid, null)) 信审关闭订单" +
                "       from sh_order so" +
                "                left join (select order_uid,send_time" +
                "                                from sh_order_logistics" +
                "                                where ds= (select ds from dt)" +
                "                                group by order_uid,send_time) sol on sol.order_uid=so.order_uid" +
                "                left join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "       where so.is_deleted = 0" +
                "         and repayment_period_count > 1" +
                "         and so.ds = (select ds from dt)" +
                "         and sof.ds = (select ds from dt)" +
                "         and date(so.cash_deposit_pay_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "       group by date(so.cash_deposit_pay_time)) c on a.time = c.time" +
                "            left join (" +
                "       select date(sol.send_time)                                                                          time," +
                "              count(distinct sol.order_uid)                                                                发货订单," +
                "              count(distinct" +
                "                    if(datediff(sol.send_time, so.cash_deposit_pay_time, 'hh') <= 48, so.order_uid, null)) 48小时发货量," +
                "              sum(total_rent_amount)                                                                       总租金," +
                "              sum(cast(get_json_object(soi.spec_snapshot, '$.purchasePrice') as double))                   进货价" +
                "       from sh_order_logistics sol" +
                "                LEFT JOIN sh_order so on sol.order_uid = so.order_uid" +
                "                left join sh_order_finance sof on sof.order_uid = sol.order_uid and sof.is_deleted=0" +
                "                left join sh_order_item soi on so.order_uid = soi.order_uid and soi.is_deleted=0" +
                "       where sol.is_deleted = 0" +
                "         and date(sol.send_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "         and so.ds = (select ds from dt)" +
                "         and sof.ds = (select ds from dt)" +
                "         and sol.ds = (select ds from dt)" +
                "         and soi.ds = (select ds from dt)" +
                "       group by date(sol.send_time)) d on a.time = d.time" +
                "            left join (" +
                "       select date(sol.sign_time) time, count(distinct sol.order_uid) 签收订单" +
                "       from sh_order_logistics sol" +
                "       where sol.is_deleted = 0" +
                "         and sol.ds = (select ds from dt)" +
                "         and date(sol.sign_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "       group by date(sol.sign_time)) e on a.time = e.time" +
                "            left join (" +
                "       select date(so.create_time)                                                         time," +
                "              count(distinct so.order_uid)                                                 全款订单," +
                "              count(distinct if(so.cash_deposit_pay_time is not null, so.order_uid, null)) 全款支付" +
                "       from sh_order so" +
                "                left join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "       where so.is_deleted = 0" +
                "         and repayment_period_count = 1" +
                "         and so.ds = (select ds from dt)" +
                "         and sof.ds = (select ds from dt)" +
                "         and date(so.create_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "       group by date(so.create_time)) f on a.time = f.time" +
                "   ;";
        List<Record> records = odpsUtil.querySql(sql);
        String finalDs = ds;
        List<WholeMerchantReportDO> collect = records.stream().map((record) -> {
            LocalDate countDay = LocalDate.parse(record.getString("count_day"), WholeLawReportBusiness.YYYY_MM_DD);
            WholeMerchantReportDO reportDO = new WholeMerchantReportDO();
            reportDO.setCountDay(countDay);
            reportDO.setDs(finalDs);
            reportDO.setOrderCnt(Integer.valueOf(record.getString("order_cnt")));
            reportDO.setOrderUv(Integer.valueOf(record.getString("order_uv")));
            reportDO.setAuditUv(Integer.valueOf(record.getString("audit_uv")));
            reportDO.setAuditCnt(Integer.valueOf(record.getString("audit_cnt")));
            reportDO.setPayUv(Integer.valueOf(record.getString("pay_uv")));
            reportDO.setWholeAuditUv(Integer.valueOf(record.getString("whole_audit_uv")));
            reportDO.setWholeAuditCnt(Integer.valueOf(record.getString("whole_audit_cnt")));
            reportDO.setMarginAuditCnt(Integer.valueOf(record.getString("margin_audit_cnt")));
            reportDO.setMarginAuditUv(Integer.valueOf(record.getString("margin_audit_uv")));
            reportDO.setPayCnt(Integer.valueOf(record.getString("pay_cnt")));
            reportDO.setWholePayUv(Integer.valueOf(record.getString("whole_pay_uv")));
            reportDO.setWholePayCnt(Integer.valueOf(record.getString("whole_pay_cnt")));
            reportDO.setMarginPayUv(Integer.valueOf(record.getString("margin_pay_uv")));
            reportDO.setMarginPayCnt(Integer.valueOf(record.getString("margin_pay_cnt")));
            reportDO.setCreditClose(Integer.valueOf(record.getString("credit_close")));
            reportDO.setSendCnt(Integer.valueOf(record.getString("send_cnt")));
            reportDO.setFinanceAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("finance_amt"))));
            reportDO.setHour48SendCnt(Integer.valueOf(record.getString("hour48_send_cnt")));
            reportDO.setPurchasePrice(BigDecimal.valueOf(Double.parseDouble(record.getString("purchase_price"))));

            return reportDO;
        }).collect(Collectors.toList());
        wholeMerchantReportService.removeDataByDs(ds);
        wholeMerchantReportService.saveBatch(collect);
    }


}



