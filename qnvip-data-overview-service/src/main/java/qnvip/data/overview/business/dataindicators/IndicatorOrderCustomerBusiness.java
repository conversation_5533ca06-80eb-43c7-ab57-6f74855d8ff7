package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorOrderCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/1/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorOrderCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorOrderCustomerService indicatorOrderCustomerService;

    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorOrderCustomerDO> miniType2Map = new HashMap<>();
        countLtNhRegisterCount(ds, sTime, eTime, miniType2Map);
        countRegisterNotOrderCount(ds, sTime, eTime, miniType2Map);
        countOrderRiskApprovedNotPay(ds, sTime, eTime, miniType2Map);
        countOrderOutServiceCount(ds, sTime, eTime, miniType2Map);
        countLtNdRefusedCountCount(ds, sTime, eTime, miniType2Map);
        for (IndicatorOrderCustomerDO value : miniType2Map.values()) {
            indicatorOrderCustomerService.saveOrUpdate(value);
        }
    }

    /**
     * N小时内注册人数(下单成功人数中，当天Nh内注册人数)
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorOrderCustomerDO> collectMap) {
        String sql = "select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                    inner join rent_customer c " +
                "                    on get_json_object(c.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        inner join ( " +
                "                             select  customer_id " +
                "                                        ,to_char(create_time, 'yyyy-mm-dd') day " +
                "                                        ,mini_type " +
                "                                from    ( " +
                "                                            select  customer_id,create_time,mini_type " +
                "                                            from    rent_order " +
                "                                            where   is_deleted = 0 " +
                "                                            and     merchant_id = 100 " +
                "                                            and     parent_id = 0 " +
                "                                            and     type = 1 " +
                "                                            and     biz_type = 2 " +
                "                                            and     create_time between '" + sTime + "' " +
                "                                            and     '" + eTime + "' " +
                "                                            and     ds = " + ds +
                "                                            order by create_time desc " +
                "                                        )  " +
                "                                group by customer_id " +
                "                                        ,mini_type " +
                "                                        ,to_char(create_time, 'yyyy-mm-dd') " +
                "               ) b on a.mini_type = b.mini_type and c.id = b.customer_id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and c.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' " +
                "                   and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                " where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd');";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 已注册未下单人数(不是当天注册，且当天之前未下过单，一天天查)
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRegisterNotOrderCount(String ds, String sTime, String eTime,
                                            Map<String, IndicatorOrderCustomerDO> collectMap) {
        String sql = " select mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count " +
                "from ( " +
                "    select mini_type,customer_id,min(create_time) create_time from rent_order " +
                "    where is_deleted = 0 " +
                "    and merchant_id = 100 " +
                "    and parent_id = 0 " +
                "    and type = 1 " +
                "    and biz_type = 2 " +
                "    and create_time between '" + sTime + "' and '" + eTime + "' " +
                "    and ds = " + ds +
                "    group by mini_type,customer_id,to_char(create_time,'yyyy-mm-dd') " +
                ") a inner join rent_customer b on b.id = a.customer_id " +
                "    inner join ( " +
                "        select min(create_time) create_time,customer_id from ( " +
                "            select customer_id,create_time from rent_order where customer_id in ( " +
                "                select customer_id " +
                "                    from rent_order  " +
                "                    where is_deleted = 0 " +
                "                    and merchant_id=100 " +
                "                    and parent_id = 0 " +
                "                    and type = 1 " +
                "                    and biz_type = 2 " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "' " +
                "                    and ds = " + ds +
                "            ) " +
                "            and ds = " + ds + " order by create_time desc " +
                "        ) group by customer_id " +
                "    ) c on a.customer_id = c.customer_id and to_char(a.create_time,'yyyy-mm-dd') =  to_char(c" +
                ".create_time,'yyyy-mm-dd') " +
                "    where DATEDIFF(a.create_time,b.create_time)>1 and b.ds= " + ds +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterNotOrder(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 下单人数中，风控通过未付/风控N天之前被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countOrderRiskApprovedNotPay(String ds, String sTime, String eTime,
                                             Map<String, IndicatorOrderCustomerDO> collectMap) {
        String sql = " select  A.day " +
                "        ,A.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  x.mini_type " +
                "                    ,to_char(x.create_time, 'yyyy-mm-dd') as day " +
                "                    ,x.customer_id " +
                "            from    rent_order x inner " +
                "            join    cl_loan y " +
                "            on      x.no = y.loanno " +
                "            inner join ( " +
                "                           select  min(risktime) risktime " +
                "                                   ,customer_id " +
                "                           from    ( " +
                "                                       select  b.risktime " +
                "                                               ,a.customer_id " +
                "                                       from    rent_order a inner " +
                "                                       join    cl_loan b " +
                "                                       on      a.no = b.loanno " +
                "                                       where   a.is_deleted = 0 " +
                "                                       and     a.merchant_id = 100 " +
                "                                       and     a.parent_id = 0 " +
                "                                       and     a.type = 1 " +
                "                                       and     a.biz_type = 2 " +
                "                                       and     b.riskstatus = 20 " +
                "                                       and     a.payment_time is null " +
                "                                       and     a.ds = " + ds +
                "                                       and     b.ds = " + ds +
                "                                       order by b.risktime asc " +
                "                                   )  " +
                "                           group by customer_id " +
                "                       ) D " +
                "            on      x.customer_id = D.customer_id " +
                "            where   x.is_deleted = 0 " +
                "            and     x.merchant_id = 100 " +
                "            and     x.parent_id = 0 " +
                "            and     x.type = 1 " +
                "            and     x.biz_type = 2 " +
                "            and     x.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     y.riskstatus = 15 " +
                "            and     x.ds = " + ds +
                "            and     y.ds = " + ds +
                "            and     datediff(x.create_time,D.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "            group by x.mini_type " +
                "                     ,to_char(x.create_time, 'yyyy-mm-dd') " +
                "                     ,x.customer_id " +
                "        ) A " +
                " group by A.mini_type,A.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setOrderRiskApprovedNotPay(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 下单人数中，服务已结束客户
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countOrderOutServiceCount(String ds, String sTime, String eTime,
                                          Map<String, IndicatorOrderCustomerDO> collectMap) {
        String sql = " select mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count " +
                "from ( " +
                "    select mini_type,customer_id,min(create_time) create_time from rent_order " +
                "    where is_deleted = 0 " +
                "    and merchant_id = 100 " +
                "    and parent_id = 0 " +
                "    and type = 1 " +
                "    and biz_type = 2 " +
                "    and create_time between '" + sTime + "' and '" + eTime + "' " +
                "    and ds = " + ds +
                "    group by mini_type,customer_id,to_char(create_time,'yyyy-mm-dd') " +
                ") a  " +
                "    inner join ( " +
                "        select min(create_time) create_time,max(termination) termination,max(status) status," +
                "           customer_id from ( " +
                "            select termination,status,customer_id,create_time from rent_order where customer_id in " +
                "( " +
                "                select customer_id " +
                "                    from rent_order  " +
                "                    where is_deleted = 0 " +
                "                    and merchant_id=100 " +
                "                    and parent_id = 0 " +
                "                    and type = 1 " +
                "                    and biz_type = 2 " +
                "                    and payment_time is not null " +
                "                    and ds = " + ds +
                "            ) " +
                "            and ds = " + ds + " order by create_time asc " +
                "        ) group by customer_id " +
                "    ) c on a.customer_id = c.customer_id and to_char(a.create_time,'yyyy-mm-dd') =  to_char(c" +
                ".create_time,'yyyy-mm-dd') " +
                "    where c.termination !=5 and c.status in (220,330) " +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setOrderOutServiceCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 下单人数中，风控通过未付/N天内被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countLtNdRefusedCountCount(String ds, String sTime, String eTime,
                                           Map<String, IndicatorOrderCustomerDO> collectMap) {
        String sql = " select  A.day " +
                "        ,A.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  x.mini_type " +
                "                    ,to_char(x.create_time, 'yyyy-mm-dd') as day " +
                "                    ,x.customer_id " +
                "            from    rent_order x " +
                "            inner join ( " +
                "                           select  min(risktime) risktime " +
                "                                   ,min(id) order_id " +
                "                                   ,customer_id " +
                "                           from    ( " +
                "                                       select  b.risktime " +
                "                                               ,a.customer_id " +
                "                                               ,a.id " +
                "                                       from    rent_order a inner " +
                "                                       join    cl_loan b " +
                "                                       on      a.no = b.loanno " +
                "                                       where   a.is_deleted = 0 " +
                "                                       and     a.merchant_id = 100 " +
                "                                       and     a.parent_id = 0 " +
                "                                       and     a.type = 1 " +
                "                                       and     a.biz_type = 2 " +
                "                                       and     b.riskstatus in (15,25) " +
                "                                       and     a.ds = " + ds +
                "                                       and     b.ds = " + ds +
                "                                       order by b.risktime asc " +
                "                                   )  " +
                "                           group by customer_id " +
                "                       ) D " +
                "            on      x.customer_id = D.customer_id and x.id != D.order_id " +
                "            where   x.is_deleted = 0 " +
                "            and     x.merchant_id = 100 " +
                "            and     x.parent_id = 0 " +
                "            and     x.type = 1 " +
                "            and     x.biz_type = 2 " +
                "            and     x.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     x.ds = " + ds +
                "            and     datediff(x.create_time,D.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "            group by x.mini_type " +
                "                     ,to_char(x.create_time, 'yyyy-mm-dd') " +
                "                     ,x.customer_id " +
                "        ) A " +
                "group by A.mini_type ,A.day;  ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(Integer.valueOf(record.getString("count")));
        }
    }


    private void initMap(Map<String, IndicatorOrderCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorOrderCustomerDO iocd = new IndicatorOrderCustomerDO();
            iocd.setMiniType(miniType);
            iocd.setCountDay(countDay);
            iocd.setLtNhRegisterCount(0);
            iocd.setRegisterNotOrder(0);
            iocd.setOrderRiskApprovedNotPay(0);
            iocd.setOrderInServiceCount(0);
            iocd.setOrderOutServiceCount(0);
            iocd.setLtNdRefusedCount(0);
            miniType2Map.put(key, iocd);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}