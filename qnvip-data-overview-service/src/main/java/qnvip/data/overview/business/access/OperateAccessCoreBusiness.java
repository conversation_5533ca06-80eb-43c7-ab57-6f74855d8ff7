package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperateAccessCoreDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.access.OperateAccessCoreService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/15
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateAccessCoreBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateAccessCoreService accessCoreService;

    void initMap(Map<Integer, OperateAccessCoreDO> miniType2Map, OperateAccessCoreDO accessCoreD) {
        if (!miniType2Map.containsKey(accessCoreD.getMiniType())) {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            OperateAccessCoreDO ac = new OperateAccessCoreDO();
            ac.setMiniType(accessCoreD.getMiniType());
            ac.setCountDay(countDay);
            miniType2Map.put(accessCoreD.getMiniType(), ac);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        Map<Integer, OperateAccessCoreDO> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateAccessCoreDO>> f1 = CompletableFuture.supplyAsync(() -> getDauAndUvByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f2 =
                CompletableFuture.supplyAsync(() -> getUvNewValidByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f3 = CompletableFuture.supplyAsync(()->getUvNewNDACByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f4 = CompletableFuture.supplyAsync(()->getUvNewByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f5 = CompletableFuture.supplyAsync(()->getUvNewQualityByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f7 = CompletableFuture.supplyAsync(()->getPvByMiniType(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f14 = CompletableFuture.supplyAsync(()->getRuvMiniType(ds));
        // todo 留存、流失用户数，需要用到开窗函数
        // 计算全站的用户数
        CompletableFuture<List<OperateAccessCoreDO>> f8 = CompletableFuture.supplyAsync(()->getDauAndUv(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f9 = CompletableFuture.supplyAsync(()->getUvNewValid(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f10 = CompletableFuture.supplyAsync(()->getUvNewNDAC(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f11 = CompletableFuture.supplyAsync(()->getUvNew(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f12 = CompletableFuture.supplyAsync(()->getUvNewQuality(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f13 = CompletableFuture.supplyAsync(()->getPv(ds));
        CompletableFuture<List<OperateAccessCoreDO>> f15 = CompletableFuture.supplyAsync(()->getRUV(ds));
        CompletableFuture.allOf(f1, f2, f3, f4, f5, f7, f8, f9, f10, f11, f12, f13, f14, f15).join();
        try {
            List<OperateAccessCoreDO> pvAndUv = f1.get();
            List<OperateAccessCoreDO> uvNewValid = f2.get();
            List<OperateAccessCoreDO> uvNewNDAC = f3.get();
            List<OperateAccessCoreDO> uvNew = f4.get();
            List<OperateAccessCoreDO> uvNewQuality = f5.get();
            List<OperateAccessCoreDO> pv = f7.get();
            parseDauAndUv(miniType2Map, pvAndUv);
            parseUvNewValid(miniType2Map, uvNewValid);
            parseUvNewNDAC(miniType2Map, uvNewNDAC);
            parseUvNew(miniType2Map, uvNew);
            parseUvNewQuality(miniType2Map, uvNewQuality);
            parsePv(miniType2Map, pv);
            parseRuv(miniType2Map, f14.get());
            // 计算全站的用户数
            parseDauAndUv(miniType2Map, f8.get());
            parseUvNewValid(miniType2Map, f9.get());
            parseUvNewNDAC(miniType2Map, f10.get());
            parseUvNew(miniType2Map, f11.get());
            parseUvNewQuality(miniType2Map, f12.get());
            parsePv(miniType2Map, f13.get());
            parseRuv(miniType2Map, f15.get());

            // 写入mysql
            LinkedList<OperateAccessCoreDO> list = Lists.newLinkedList(miniType2Map.values());
            accessCoreService.removeDataByTime(countDay);
            accessCoreService.saveBatch(list);

        } catch (InterruptedException | ExecutionException e) {
            log.error("OperateAccessCoreBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void parseRuv(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> pv) {
        for (OperateAccessCoreDO coreDO : pv) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());
            core.setUvKeep(coreDO.getUvKeep());
        }
    }


    private void parsePv(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> pv) {
        for (OperateAccessCoreDO coreDO : pv) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());
            core.setPv(coreDO.getPv());
        }
    }

    private void parseUvNewQuality(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> uvNewQuality) {
        for (OperateAccessCoreDO coreDO : uvNewQuality) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());

            core.setUvNewQuality(coreDO.getUvNewQuality());
        }
    }

    private void parseUvNew(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> uvNew) {
        for (OperateAccessCoreDO coreDO : uvNew) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());

            core.setUvNew(coreDO.getUvNew());
        }
    }

    private void parseUvNewNDAC(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> uvNewNDAC) {
        for (OperateAccessCoreDO coreDO : uvNewNDAC) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());
            core.setDnpu(coreDO.getDnpu());
        }
    }

    private void parseUvNewValid(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> uvNewValid) {
        for (OperateAccessCoreDO coreDO : uvNewValid) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());

            core.setUvNewValid(coreDO.getUvNewValid());
        }
    }

    private void parseDauAndUv(Map<Integer, OperateAccessCoreDO> miniType2Map, List<OperateAccessCoreDO> pvAndUv) {
        for (OperateAccessCoreDO coreDO : pvAndUv) {
            initMap(miniType2Map, coreDO);
            OperateAccessCoreDO core = miniType2Map.get(coreDO.getMiniType());
            core.setUv(coreDO.getUv());
            core.setDau(coreDO.getDau());
        }
    }

    /**
     * 全站PV和UV
     */
    private List<OperateAccessCoreDO> getDauAndUvByMiniType(String ds) {
        String sql = "select count(customer_third_id)          dau," +
                "       count(distinct customer_third_id) uv," +
                "       mini_type" +
                " from dataview_track_enter_applets" +
                " where action_type = 1 " +
                "  and report_time between ${fromTime} and ${toTime}" +
                "  and ds = " + ds +
                " group by mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String dau = record.getString("dau");
            String uv = record.getString("uv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setDau(Long.valueOf(dau));
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 全站存续用户
     */
    private List<OperateAccessCoreDO> getRUV(String ds) {
        String sql = "select count(*) uv_keep" +
                " from (select id, count(*)" +
                "      from (select *, date (rTime)-cum as ruv" +
                "      from ( select *, row_number() over(PARTITION by id order by rTime) as cum" +
                "          from ( select DISTINCT date (report_time) as rTime, customer_third_id id" +
                "          from dataview_track_enter_applets" +
                "          where ds = " + ds +
                "          and action_type = 1" +
                "          and report_time between to_char(date_sub(${fromTime}, 2), 'yyyy-mm-dd hh:mi:ss') and ${toTime}" +
                "          ) a" +
                "          ) b" +
                "     ) c" +
                " GROUP BY id, ruv" +
                " having count(*) >= 3 )e";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String dau = record.getString("uv_keep");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvKeep(Long.valueOf(dau));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 全站PV和UV
     */
    private List<OperateAccessCoreDO> getDauAndUv(String ds) {
        String sql = "select count(customer_third_id)          dau," +
                "       count(distinct customer_third_id) uv" +
                " from dataview_track_enter_applets" +
                " where action_type = 1 " +
                "  and report_time between ${fromTime} and ${toTime}" +
                "  and ds = " + ds;

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String dau = record.getString("dau");
            String uv = record.getString("uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setDau(Long.valueOf(dau));
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 存续用户
     */
    private List<OperateAccessCoreDO> getRuvMiniType(String ds) {
        String sql = " select mini_type, count(*) uv_keep" +
                " from (select id, mini_type, count(*)" +
                "      from (select mini_type, id, date (rTime)-cum as num" +
                "      from ( select *, row_number() over(PARTITION by id order by rTime) as cum" +
                "          from ( select date (report_time) as rTime, customer_third_id id, mini_type" +
                "          from dataview_track_enter_applets" +
                "          where ds = " + ds +
                "          and action_type = 1" +
                "          and report_time between to_char(date_sub(${fromTime}, 2), 'yyyy-mm-dd hh:mi:ss') and ${toTime}" +
                "          group by mini_type, customer_third_id, date (report_time)" +
                "          ) a" +
                "          ) b" +
                "     ) c" +
                " GROUP BY id, mini_type, num" +
                " having count(*) >= 3 )e" +
                " group by e.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("uv_keep");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvKeep(Long.valueOf(pv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 全站PV
     */
    private List<OperateAccessCoreDO> getPvByMiniType(String ds) {
        String sql = " select count(enter_page_code) pv," +
                "       mini_type" +
                " from dataview_track_enter_applets" +
                " where action_type = 1" +
                "  and ds = " + ds +
                "  and report_time between ${fromTime} and ${toTime}" +
                " group by mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("pv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPv(Long.valueOf(pv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 全站PV
     */
    private List<OperateAccessCoreDO> getPv(String ds) {
        String sql = " select count(enter_page_code) pv" +
                " from dataview_track_enter_applets" +
                " where action_type = 1" +
                "  and ds = " + ds +
                "  and report_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String pv = record.getString("pv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPv(Long.valueOf(pv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 新用户数
     */
    private List<OperateAccessCoreDO> getUvNewByMiniType(String ds) {
        String sql = "select count(customer_third_id)          pv," +
                "       count(distinct customer_third_id) uv," +
                "       mini_type" +
                " from dataview_track_new_access" +
                " where report_time between ${fromTime} and ${toTime}" +
                "  and ds = " + ds +
                " group by mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvNew(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 新用户数
     */
    private List<OperateAccessCoreDO> getUvNew(String ds) {
        String sql = "select count(customer_third_id)          pv," +
                "       count(distinct customer_third_id) uv" +
                " from dataview_track_new_access" +
                " where report_time between ${fromTime} and ${toTime}" +
                "  and ds = " + ds;

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String uv = record.getString("uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvNew(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效新用户数
     */
    private List<OperateAccessCoreDO> getUvNewValidByMiniType(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv_new_valid, a.mini_type " +
                " from (select a.mini_type, " +
                "             a.customer_third_id " +
                "      from dataview_track_new_access a " +
                "               inner join dataview_track_page_statistics b on a.customer_third_id = b.customer_third_id " +
                "      where b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)  " +
                "        and b.action_type = 2 " +
                "        and a.ds = to_char(getdate(), 'yyyymmdd') " +
                "        and b.ds = to_char(getdate(), 'yyyymmdd') " +
                "        and a.report_time between ${fromTime} and ${toTime} " +
                "      group by a.mini_type, a.customer_third_id " +
                "      having sum(b.keep_alive_time) >= 19000 " +
                "     ) a " +
                " group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv_new_valid");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvNewValid(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效新用户数
     */
    private List<OperateAccessCoreDO> getUvNewValid(String ds) {
        String sql = "select count(distinct customer_third_id) uv_new_valid" +
                " from (select a.customer_third_id" +
                "      from dataview_track_new_access a" +
                "               left join dataview_track_page_statistics b on a.customer_third_id = b.customer_third_id" +
                "      where b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and b.action_type = 2" +
                "        and a.report_time between ${fromTime} and ${toTime}" +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "      group by a.customer_third_id" +
                "      having sum(b.keep_alive_time)  >= 19000" +
                "     ) a";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String uv = record.getString("uv_new_valid");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvNewValid(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 高质量新用户数
     */
    private List<OperateAccessCoreDO> getUvNewQualityByMiniType(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv_new_quality, a.mini_type " +
                " from (select a.mini_type, " +
                "             a.customer_third_id " +
                "      from dataview_track_new_access a " +
                "               inner join dataview_track_page_statistics b on a.customer_third_id = b.customer_third_id " +
                "      where b.enter_page_code in (10004,10009) " +
                "        and b.action_type = 2 " +
                "        and a.ds = to_char(getdate(), 'yyyymmdd') " +
                "        and b.ds = to_char(getdate(), 'yyyymmdd') " +
                "        and a.report_time between ${fromTime} and ${toTime} " +
                "      group by a.mini_type, a.customer_third_id " +
                "      having sum(b.keep_alive_time) >= 19000 " +
                "     ) a " +
                " group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv_new_quality");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvNewQuality(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 高质量新用户数
     */
    private List<OperateAccessCoreDO> getUvNewQuality(String ds) {
        String sql = "select count(distinct customer_third_id) uv_new_quality" +
                " from (select a.customer_third_id" +
                "      from dataview_track_new_access a" +
                "               left join dataview_track_page_statistics b on a.customer_third_id = b.customer_third_id" +
                "      where b.enter_page_code in (10004,10009)" +
                "        and b.action_type = 2" +
                "        and a.report_time between ${fromTime} and ${toTime}" +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "      group by a.customer_third_id" +
                "      having sum(b.keep_alive_time)  >= 67000" +
                "     ) a ";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String uv = record.getString("uv_new_quality");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvNewQuality(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 动销新用户
     */
    private List<OperateAccessCoreDO> getUvNewNDACByMiniType(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv_new_ndac," +
                "       a.mini_type" +
                " from dataview_track_new_access a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                " where get_json_object(b.bindinfo, '$[0].miniUserId') != ''" +
                "  and get_json_object(b.bindinfo, '$[0].miniUserId') is not null" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and c.is_deleted = 0" +
                "  and c.status in (1, 5, 15, 60)" +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                "  and c.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv_new_ndac");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setDnpu(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 动销新用户
     */
    private List<OperateAccessCoreDO> getUvNewNDAC(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv_new_ndac" +
                " from dataview_track_new_access a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                " where get_json_object(b.bindinfo, '$[0].miniUserId') != ''" +
                "  and get_json_object(b.bindinfo, '$[0].miniUserId') is not null" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and c.is_deleted = 0" +
                "  and c.status in (1, 5, 15, 60)" +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                "  and c.payment_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessCoreDO domain = new OperateAccessCoreDO();
            String uv = record.getString("uv_new_ndac");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setDnpu(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

}
