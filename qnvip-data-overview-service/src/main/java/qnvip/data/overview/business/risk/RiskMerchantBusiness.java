package qnvip.data.overview.business.risk;

import cn.hutool.core.convert.Convert;
import com.alibaba.ttl.TtlWrappers;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.risk.RiskMerchantSituationDO;
import qnvip.data.overview.domain.risk.RiskUserVo;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskMerchantSituationService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <风控大盘-分期>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskMerchantBusiness {

    private final OdpsUtil odpsUtil;

    private final RiskMerchantSituationService riskSituationService;

    private final RentOrderService rentOrderService;

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            List<Map<String, Object>> list = Lists.newArrayList();

            getRiskUserTotalByMiniType(list, ds);
            getBigDataPassTotalByMiniType(list, ds);
            getSendCountTotalByMiniType(list, ds);
            getMarginTotalByMiniType(list, ds);
            getInstallmentPayCountByMiniType(list, ds);

            // 计算全部平台
            getRiskUserTotal(list, ds);
            getBigDataPassTotal(list, ds);
            getSendCountTotal(list, ds);
            getMarginTotal(list, ds);
            getInstallmentPayCount(list, ds);

            List<CompletableFuture<List<Map<String, String>>>> futureList =
                    list.stream().map(this::getData).collect(Collectors.toList());
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<Map<String, String>> totalList = Lists.newArrayList();
            futureList.forEach(future -> {
                try {
                    totalList.addAll(future.get());
                } catch (Exception e) {
                    log.error("RiskSituationBusiness runCore error,{}", e.getMessage(), e);
                }
            });

            Map<String, List<Map<String, String>>> channel2list =
                    totalList.stream().collect(Collectors.groupingBy(map -> map.get("business_channel")));
            LinkedList<RiskMerchantSituationDO> dos = Lists.newLinkedList();
            channel2list.forEach((channel, channelList) -> {
                RiskMerchantSituationDO RiskMerchantSituationDO = new RiskMerchantSituationDO();
                Map<String, String> merged = new HashMap<>();
                channelList.forEach(merged::putAll);
                String bigDataPass = Optional.ofNullable(merged.get("big_data_pass")).orElse("0");
                String centralBankPass = Optional.ofNullable(merged.get("central_bank_pass")).orElse("0");
                String centralBankInquiry = Optional.ofNullable(merged.get("central_bank_inquiry")).orElse("0");
                String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
                String validUser = Optional.ofNullable(merged.get("valid_user")).orElse("0");
                String passUser = Optional.ofNullable(merged.get("pass_user")).orElse("0");
                String riskUser = Optional.ofNullable(merged.get("risk_user")).orElse("0");
                String prepositionRiskUser = Optional.ofNullable(merged.get("preposition_risk_user")).orElse("0");
                String antiFraudPassCnt = Optional.ofNullable(merged.get("anti_fraud_pass_cnt")).orElse("0");
                String margin = Optional.ofNullable(merged.get("margin")).orElse("0");
                String totalAmount = Optional.ofNullable(merged.get("total_amount")).orElse("0");
                String buyOutAmt = Optional.ofNullable(merged.get("buy_out_amt")).orElse("0");
                String sendCount = Optional.ofNullable(merged.get("send_count")).orElse("0");
                String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
                String curDayNum = Optional.ofNullable(merged.get("cur_day_num")).orElse("0");
                String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
                String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
                String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
                String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");
                String unDifferenceBigDataPass = Optional.ofNullable(merged.get("un_difference_big_data_pass")).orElse("0");
                String payCountWhole = Optional.ofNullable(merged.get("pay_count_whole")).orElse("0");
                String payCount = Optional.ofNullable(merged.get("pay_count")).orElse("0");

                totalAmount = "\\N".equals(totalAmount) ? "0" : totalAmount;
                buyOutAmt = "\\N".equals(buyOutAmt) ? "0" : buyOutAmt;
                margin = "\\N".equals(margin) ? "0" : margin;

                RiskMerchantSituationDO.setCountDay(countDay);

                RiskMerchantSituationDO.setTotalAmt(new BigDecimal(totalAmount));
                RiskMerchantSituationDO.setBuyOutAmt(new BigDecimal(buyOutAmt));
                RiskMerchantSituationDO.setMargin(new BigDecimal(margin));
                RiskMerchantSituationDO.setBusinessChannel(Integer.valueOf(channel));
                RiskMerchantSituationDO.setRiskUser(Long.valueOf(riskUser));
                RiskMerchantSituationDO.setValidUser(Long.valueOf(validUser));
                RiskMerchantSituationDO.setPassUser(Long.valueOf(passUser));
                RiskMerchantSituationDO.setCentralBankInquiry(Long.valueOf(centralBankInquiry));
                RiskMerchantSituationDO.setCentralBankPass(Long.valueOf(centralBankPass));
                RiskMerchantSituationDO.setBigDataPass(Long.valueOf(bigDataPass));
                RiskMerchantSituationDO.setPayUser(Long.valueOf(payUser));
                RiskMerchantSituationDO.setPayCount(Long.valueOf(payCount));
                RiskMerchantSituationDO.setPayUserWhole(Long.valueOf(payCountWhole));
                RiskMerchantSituationDO.setUnDifferenceBigDataPass(Long.valueOf(unDifferenceBigDataPass));
                // 差额=一级审批通过数-非差额
                RiskMerchantSituationDO.setDifferenceBigDataPass(Math.subtractExact(Long.parseLong(bigDataPass), Long.parseLong(unDifferenceBigDataPass)));
                RiskMerchantSituationDO.setSendCount(Long.valueOf(sendCount));
                RiskMerchantSituationDO.setPayUserClose(Long.valueOf(payUserClose));
                RiskMerchantSituationDO.setCurDayCount(Integer.valueOf(curDayNum));
                RiskMerchantSituationDO.setPrepositionRiskUser(Long.valueOf(prepositionRiskUser));
                RiskMerchantSituationDO.setAntiFraudPassCnt(Long.valueOf(antiFraudPassCnt));
                RiskMerchantSituationDO.setAliPay(Long.valueOf(aliPay));
                RiskMerchantSituationDO.setWeiXinPay(Long.valueOf(weixinPay));
                RiskMerchantSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
                RiskMerchantSituationDO.setOtherPay(Long.valueOf(otherPay));
                dos.add(RiskMerchantSituationDO);
            });
            riskSituationService.saveBatch(dos);
        } catch (Exception e) {
            log.error("================RiskSituationBusiness ERROR==================== error:{}", e.getMessage(), e);
        }

    }


    /**
     * 定时调度任务
     */
    public void runCoreTidb(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            List<Map<String, Object>> list = Lists.newArrayList();

            getRiskUserTotalByMiniTypeTidb(list, ds);
            getBigDataPassTotalByMiniTypeTidb(list, ds);
            getSendCountTotalByMiniTypeTidb(list, ds);
            getMarginTotalByMiniTypeTidb(list, ds);
            getInstallmentPayCountByMiniTypeTidb(list, ds);

            // 计算全部平台
            getRiskUserTotalTidb(list, ds);
            getBigDataPassTotalTidb(list, ds);
            getSendCountTotalTidb(list, ds);
            getMarginTotalTidb(list, ds);
            getInstallmentPayCountTidb(list, ds);

            List<CompletableFuture<List<Map<String, String>>>> futureList =
                    list.stream().map(this::getDataTidb).collect(Collectors.toList());
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<Map<String, String>> totalList = Lists.newArrayList();
            futureList.forEach(future -> {
                try {
                    totalList.addAll(future.get());
                } catch (Exception e) {
                    log.error("RiskSituationBusiness runCore error,{}", e.getMessage(), e);
                }
            });

            Map<String, List<Map<String, String>>> channel2list =
                    totalList.stream().collect(Collectors.groupingBy(map -> map.get("business_channel")));
            LinkedList<RiskMerchantSituationDO> dos = Lists.newLinkedList();
            channel2list.forEach((channel, channelList) -> {
                RiskMerchantSituationDO RiskMerchantSituationDO = new RiskMerchantSituationDO();
                Map<String, String> merged = new HashMap<>();
                channelList.forEach(merged::putAll);
                String bigDataPass = Optional.ofNullable(merged.get("big_data_pass")).orElse("0");
                String centralBankPass = Optional.ofNullable(merged.get("central_bank_pass")).orElse("0");
                String centralBankInquiry = Optional.ofNullable(merged.get("central_bank_inquiry")).orElse("0");
                String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
                String validUser = Optional.ofNullable(merged.get("valid_user")).orElse("0");
                String passUser = Optional.ofNullable(merged.get("pass_user")).orElse("0");
                String riskUser = Optional.ofNullable(merged.get("risk_user")).orElse("0");
                String prepositionRiskUser = Optional.ofNullable(merged.get("preposition_risk_user")).orElse("0");
                String antiFraudPassCnt = Optional.ofNullable(merged.get("anti_fraud_pass_cnt")).orElse("0");
                String margin = Optional.ofNullable(merged.get("margin")).orElse("0");
                String totalAmount = Optional.ofNullable(merged.get("total_amount")).orElse("0");
                String buyOutAmt = Optional.ofNullable(merged.get("buy_out_amt")).orElse("0");
                String sendCount = Optional.ofNullable(merged.get("send_count")).orElse("0");
                String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
                String curDayNum = Optional.ofNullable(merged.get("cur_day_num")).orElse("0");
                String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
                String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
                String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
                String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");
                String unDifferenceBigDataPass = Optional.ofNullable(merged.get("un_difference_big_data_pass")).orElse("0");
                String payCountWhole = Optional.ofNullable(merged.get("pay_count_whole")).orElse("0");
                String payCount = Optional.ofNullable(merged.get("pay_count")).orElse("0");

                totalAmount = "\\N".equals(totalAmount) ? "0" : totalAmount;
                buyOutAmt = "\\N".equals(buyOutAmt) ? "0" : buyOutAmt;
                margin = "\\N".equals(margin) ? "0" : margin;

                RiskMerchantSituationDO.setCountDay(countDay);

                RiskMerchantSituationDO.setTotalAmt(new BigDecimal(totalAmount));
                RiskMerchantSituationDO.setBuyOutAmt(new BigDecimal(buyOutAmt));
                RiskMerchantSituationDO.setMargin(new BigDecimal(margin));
                RiskMerchantSituationDO.setBusinessChannel(Integer.valueOf(channel));
                RiskMerchantSituationDO.setRiskUser(Long.valueOf(riskUser));
                RiskMerchantSituationDO.setValidUser(Long.valueOf(validUser));
                RiskMerchantSituationDO.setPassUser(Long.valueOf(passUser));
                RiskMerchantSituationDO.setCentralBankInquiry(Long.valueOf(centralBankInquiry));
                RiskMerchantSituationDO.setCentralBankPass(Long.valueOf(centralBankPass));
                RiskMerchantSituationDO.setBigDataPass(Long.valueOf(bigDataPass));
                RiskMerchantSituationDO.setPayUser(Long.valueOf(payUser));
                RiskMerchantSituationDO.setPayCount(Long.valueOf(payCount));
                RiskMerchantSituationDO.setPayUserWhole(Long.valueOf(payCountWhole));
                RiskMerchantSituationDO.setUnDifferenceBigDataPass(Long.valueOf(unDifferenceBigDataPass));
                // 差额=一级审批通过数-非差额
                RiskMerchantSituationDO.setDifferenceBigDataPass(Math.subtractExact(Long.parseLong(bigDataPass), Long.parseLong(unDifferenceBigDataPass)));
                RiskMerchantSituationDO.setSendCount(Long.valueOf(sendCount));
                RiskMerchantSituationDO.setPayUserClose(Long.valueOf(payUserClose));
                RiskMerchantSituationDO.setCurDayCount(Integer.valueOf(curDayNum));
                RiskMerchantSituationDO.setPrepositionRiskUser(Long.valueOf(prepositionRiskUser));
                RiskMerchantSituationDO.setAntiFraudPassCnt(Long.valueOf(antiFraudPassCnt));
                RiskMerchantSituationDO.setAliPay(Long.valueOf(aliPay));
                RiskMerchantSituationDO.setWeiXinPay(Long.valueOf(weixinPay));
                RiskMerchantSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
                RiskMerchantSituationDO.setOtherPay(Long.valueOf(otherPay));
                dos.add(RiskMerchantSituationDO);
            });
            riskSituationService.saveBatch(dos);
        } catch (Exception e) {
            log.error("================RiskSituationBusiness ERROR==================== error:{}", e.getMessage(), e);
        }

    }

    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCount(List<Map<String, Object>> list, String ds) {
        String sql = "select" +
                "   count(DISTINCT if(so.closing_time is null, customer_id, null))                                           AS                             pay_user," +
                "   count(if(so.closing_time is null, so.order_uid, null))                                                   AS                             pay_count," +
                "   count(DISTINCT if(sof.repayment_period_count = 1 and so.closing_time is null, customer_id, null)) AS                             pay_count_whole," +
                "   count(DISTINCT (if(smf.platform_code = 'ALIPAY'  and so.closing_time is null, customer_id," +
                "                      null)))                                                                           ali_pay," +
                "   count(DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
                "                      null)))                                                                           weixin_pay," +
                "   count(DISTINCT" +
                "         (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) other_pay," +
                "   count(DISTINCT" +
                "         (if(so.closing_time is not null, customer_id, null)))                                          pay_user_close," +
                "                  count(DISTINCT ( " +
                "                              case " +
                "                                  when so.credit_audit_status =30 and so.closing_time is not null then customer_id" +
                "                                  else null end " +
                "                              )) as pay_credit_audit_close " +
                "   " +
                "   from sh_order so" +
                "            inner join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "            inner join sh_mini_config smf on so.mini_type = smf.mini_type" +
                "   where so.ds =" + ds +
                "     AND sof.ds = " + ds +
                "     AND smf.ds = " + ds +
                "     and (so.test_role=0 or so.test_role is null) " +
                "     AND so.cash_deposit_pay_time >=${fromTime}" +
                "     AND so.cash_deposit_pay_time <= ${toTime};";
        list.add(packageParam(sql, "pay_user", "pay_count_whole", "pay_user_close", "pay_credit_audit_close", "pay_count", "weixin_pay",
                "ali_pay", "other_pay"));
    }


    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCountTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select" +
                "   count(DISTINCT if(so.closing_time is null, customer_id, null))                                           AS                             pay_user," +
                "   count(if(so.closing_time is null, so.order_uid, null))                                                   AS                             pay_count," +
                "   count(DISTINCT if(sof.repayment_period_count = 1 and so.closing_time is null, customer_id, null)) AS                             pay_count_whole," +
                "   count(DISTINCT (if(smf.platform_code = 'ALIPAY'  and so.closing_time is null, customer_id," +
                "                      null)))                                                                           ali_pay," +
                "   count(DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
                "                      null)))                                                                           weixin_pay," +
                "   count(DISTINCT" +
                "         (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) other_pay," +
                "   count(DISTINCT" +
                "         (if(so.closing_time is not null, customer_id, null)))                                          pay_user_close," +
                "                  count(DISTINCT ( " +
                "                              case " +
                "                                  when so.credit_audit_status =30 and so.closing_time is not null then customer_id" +
                "                                  else null end " +
                "                              )) as pay_credit_audit_close " +
                "   " +
                "   from qnvip_merchant.sh_order so" +
                "            inner join qnvip_merchant.sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "            inner join qnvip_merchant.sh_mini_config smf on so.mini_type = smf.mini_type" +
                "   where (so.test_role=0 or so.test_role is null) " +
                "     AND so.cash_deposit_pay_time >=${fromTime}" +
                "     AND so.cash_deposit_pay_time <= ${toTime};";
        list.add(packageParam(sql, "pay_user", "pay_count_whole", "pay_user_close", "pay_credit_audit_close", "pay_count", "weixin_pay",
                "ali_pay", "other_pay"));
    }
    /**
     * 风控用户 anti_fraud_pass_cnt,前置风控用户数
     *
     * @param list
     */
    private void getRiskUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT count(DISTINCT sh.customer_id)       risk_user," +
                "       count(DISTINCT cl.customerId)       preposition_risk_user," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 30), sh.customer_id, 0)) " +
                "   anti_fraud_pass_cnt," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 20), sh.customer_id, 0)) pass_user" +
                " from cl_loan cl" +
                "  left join sh_order sh on cl.loanno = sh.order_uid and sh.is_deleted=0 and sh.ds=" + ds +
                " WHERE  cl.ds = " + ds +
                "  and cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and cl.parentNo = '';";
        list.add(packageParam(sql, "risk_user", "pass_user", "preposition_risk_user", "anti_fraud_pass_cnt"));
    }

    /**
     * 风控用户 anti_fraud_pass_cnt,前置风控用户数
     *
     * @param list
     */
    private void getRiskUserTotalTidb(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT count(DISTINCT sh.customer_id)       risk_user," +
                "       count(DISTINCT cl.customerId)       preposition_risk_user," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 30), sh.customer_id, 0)) " +
                "   anti_fraud_pass_cnt," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 20), sh.customer_id, 0)) pass_user" +
                " from alchemist.cl_loan cl" +
                "  left join qnvip_merchant.sh_order sh on cl.loanno = sh.order_uid and sh.is_deleted=0 " +
                " WHERE  cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and cl.parentNo = '';";
        list.add(packageParam(sql, "risk_user", "pass_user", "preposition_risk_user", "anti_fraud_pass_cnt"));
    }

    /**
     * 有效用户
     *
     * @param list
     *//*
    private void getValidUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = " select count(DISTINCT(s.userId)) valid_user" +
                " from serial_no s" +
                " where s.userId not in (" +
                "    select userId from serial_no where riskStrategy !=''" +
                "        and createTime >= to_char(date_sub( ${fromTime}, 30),'yyyy-mm-dd')" +
                "        and createTime <= ${fromTime}" +
                "        and ds = to_char(getdate(), 'yyyymmdd')" +
                "        )" +
                " and s.createTime >= ${fromTime}" +
                " and s.createTime <= ${toTime}" +
                "  and s.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                " and s.ds = " + ds;
        list.add(packageParam(sql.concat(";"), "valid_user"));
    }*/

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT if(l.businesschannel in(28,1013,1014,1016,1018,1019,1015,1017) and l.sendStatus = 5, l.customerId, null))  " +
                "send_count" +
                " from cl_loan l" +
                " where l.parentNo = ''" +
                "  and ds = " + ds +
                "  and l.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and date(sendtime) = ${sendTime} ;";
        list.add(packageParam(sql, "send_count"));
    }

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotalTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT if(l.businesschannel in(28,1013,1014,1016,1018,1019,1015,1017) and l.sendStatus = 5, l.customerId, null))  " +
                "send_count" +
                " from alchemist.cl_loan l" +
                " where l.parentNo = ''" +
                "  and l.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and date(sendtime) = ${sendTime} ;";
        list.add(packageParam(sql, "send_count"));
    }

    /**
     * 人行风控执行总数\人行风控通过数\一级审批通过数\人行风控当日订单\差额\非差额
     *
     * @param list
     */
    private void getBigDataPassTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select " +
                "       count(DISTINCT  if(artificialauditstatus in (10, 15), a.customer_id, null))  as central_bank_inquiry," +
                "       count(DISTINCT if(\n" +
                "                     date(a.create_time) = to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                             artificialauditstatus in (10, 15), a.customer_id,\n" +
                "                     null)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as central_bank_pass," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt =0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as un_difference_big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt >0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as difference_big_data_pass" +
                " from cl_loan cl" +
                "         inner join serial_no sn" +
                "                    on sn.businessNo = cl.loanNo" + "  and sn.ds = " + ds +
                "                       and sn.rhtime >=  ${fromTime} \n" +
                "                       and sn.rhtime <= ${toTime}" +
                "         inner JOIN sh_order a on      cl.loanno = a.order_uid and a.test_role=0 and a.is_deleted=0 and a.ds = " + ds +
                "                   inner join sh_order_finance sof on a.order_uid = sof.order_uid and sof.is_deleted=0 and sof.ds=" + ds +
                " where  cl.parentNo = ''" +
                "  and cl.ds =" + ds +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017);";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "cur_day_num", "un_difference_big_data_pass", "difference_big_data_pass"));
    }

    /**
     * 人行风控执行总数\人行风控通过数\一级审批通过数\人行风控当日订单\差额\非差额
     *
     * @param list
     */
    private void getBigDataPassTotalTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select " +
                "       count(DISTINCT  if(artificialauditstatus in (10, 15), a.customer_id, null))  as central_bank_inquiry," +
                "       count(DISTINCT if(\n" +
                "                     date(a.create_time) = DATE(sn.`rhTime`) and\n" +
                "                             artificialauditstatus in (10, 15), a.customer_id,\n" +
                "                     null)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as central_bank_pass," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt =0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as un_difference_big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt >0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as difference_big_data_pass" +
                " from alchemist.cl_loan cl" +
                "         inner join alchemist.serial_no sn" +
                "                    on sn.businessNo = cl.loanNo"  +
                "                       and sn.rhtime >=  ${fromTime} \n" +
                "                       and sn.rhtime <= ${toTime}" +
                "         inner JOIN qnvip_merchant.sh_order a on      cl.loanno = a.order_uid and a.test_role=0 and a.is_deleted=0 " +
                "                   inner join qnvip_merchant.sh_order_finance sof on a.order_uid = sof.order_uid and sof.is_deleted=0 " +
                " where  cl.parentNo = ''" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017);";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "cur_day_num", "un_difference_big_data_pass", "difference_big_data_pass"));
    }

    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt" +
                " FROM cl_loan cl" +
                "    INNER JOIN rc_assess_record rr ON rr.loanId=cl.id" +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                " and cl.ds =" + ds +
                " and rr.ds = " + ds;
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt"));
    }


    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotalTidb(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt" +
                " FROM alchemist.cl_loan cl" +
                "    INNER JOIN alchemist.rc_assess_record rr ON rr.loanId=cl.id" +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)";
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt"));
    }
    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCountByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "select    so.mini_type business_channel," +
                "   count(DISTINCT if(so.closing_time is null, customer_id, null))                                           AS                             pay_user," +
                "   count(if(so.closing_time is null, so.order_uid, null))                                                   AS                             pay_count," +
                "   count(DISTINCT if(sof.repayment_period_count = 1 and so.closing_time is null, customer_id, null)) AS                             pay_count_whole," +
                "   count(DISTINCT (if(smf.platform_code = 'ALIPAY'  and so.closing_time is null, customer_id," +
                "                      null)))                                                                           ali_pay," +
                "   count(DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
                "                      null)))                                                                           weixin_pay," +
                "   count(DISTINCT" +
                "         (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) other_pay," +
                "   count(DISTINCT" +
                "         (if(so.closing_time is not null, customer_id, null)))                                          pay_user_close," +
                "                  count(DISTINCT ( " +
                "                              case " +
                "                                  when so.credit_audit_status =30 and so.closing_time is not null then customer_id" +
                "                                  else null end " +
                "                              )) as pay_credit_audit_close " +
                "   " +
                "   from sh_order so" +
                "            inner join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "            inner join sh_mini_config smf on so.mini_type = smf.mini_type" +
                "   where so.ds =" + ds +
                "     AND sof.ds = " + ds +
                "     AND smf.ds = " + ds +
                "     and (so.test_role=0 or so.test_role is null) " +
                "     AND so.cash_deposit_pay_time >=${fromTime}" +
                "     AND so.cash_deposit_pay_time <= ${toTime}" +
                "     group by so.mini_type;";
        list.add(packageParam(sql, "pay_user", "pay_count_whole", "pay_user_close", "pay_credit_audit_close", "pay_count", "weixin_pay",
                "ali_pay", "other_pay", "business_channel"));
    }


    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCountByMiniTypeTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select    so.mini_type business_channel," +
                "   count(DISTINCT if(so.closing_time is null, customer_id, null))                                           AS                             pay_user," +
                "   count(if(so.closing_time is null, so.order_uid, null))                                                   AS                             pay_count," +
                "   count(DISTINCT if(sof.repayment_period_count = 1 and so.closing_time is null, customer_id, null)) AS                             pay_count_whole," +
                "   count(DISTINCT (if(smf.platform_code = 'ALIPAY'  and so.closing_time is null, customer_id," +
                "                      null)))                                                                           ali_pay," +
                "   count(DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
                "                      null)))                                                                           weixin_pay," +
                "   count(DISTINCT" +
                "         (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) other_pay," +
                "   count(DISTINCT" +
                "         (if(so.closing_time is not null, customer_id, null)))                                          pay_user_close," +
                "                  count(DISTINCT ( " +
                "                              case " +
                "                                  when so.credit_audit_status =30 and so.closing_time is not null then customer_id" +
                "                                  else null end " +
                "                              )) as pay_credit_audit_close " +
                "   " +
                "   from qnvip_merchant.sh_order so" +
                "            inner join qnvip_merchant.sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0" +
                "            inner join qnvip_merchant.sh_mini_config smf on so.mini_type = smf.mini_type" +
                "   where (so.test_role=0 or so.test_role is null) " +
                "     AND so.cash_deposit_pay_time >=${fromTime}" +
                "     AND so.cash_deposit_pay_time <= ${toTime}" +
                "     group by so.mini_type;";
        list.add(packageParam(sql, "pay_user", "pay_count_whole", "pay_user_close", "pay_credit_audit_close", "pay_count", "weixin_pay",
                "ali_pay", "other_pay", "business_channel"));
    }

    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUserTotalByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT nvl(sh.mini_type,cl.businessChannel) business_channel,count(DISTINCT sh.customer_id)     " +
                "  risk_user," +
                "       count(DISTINCT cl.customerId)       preposition_risk_user," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 30), sh.customer_id, 0)) " +
                "   anti_fraud_pass_cnt," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 20), sh.customer_id, 0)) pass_user" +
                " from cl_loan cl" +
                "  left join sh_order sh on cl.loanno = sh.order_uid and sh.is_deleted=0 and sh.ds=" + ds +
                " WHERE  cl.ds = " + ds +
                "  and cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and cl.parentNo = ''  group by sh.mini_type,cl.businessChannel;";
        list.add(packageParam(sql, "risk_user", "pass_user", "preposition_risk_user", "anti_fraud_pass_cnt", "business_channel"));
    }

    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUserTotalByMiniTypeTidb(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT IFNULL(sh.mini_type,cl.businessChannel) business_channel,count(DISTINCT sh.customer_id)     " +
                "  risk_user," +
                "       count(DISTINCT cl.customerId)       preposition_risk_user," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 30), sh.customer_id, 0)) " +
                "   anti_fraud_pass_cnt," +
                "       COUNT(DISTINCT if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or\n" +
                "                                              (riskFicoStatus > 20), sh.customer_id, 0)) pass_user" +
                " from alchemist.cl_loan cl" +
                "  left join qnvip_merchant.sh_order sh on cl.loanno = sh.order_uid and sh.is_deleted=0 "+
                " WHERE  cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and cl.parentNo = ''  group by sh.mini_type,cl.businessChannel;";
        list.add(packageParam(sql, "risk_user", "pass_user", "preposition_risk_user", "anti_fraud_pass_cnt", "business_channel"));
    }

    /**
     * 有效用户
     *
     * @param list
     */
    /*private void getValidUserTotalByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = " select a.mini_type business_channel,count(DISTINCT(s.userId)) valid_user" +
                " from serial_no s" +
                " inner JOIN sh_order a on      s.businessNo = a.order_uid" + "  and a.ds = " + ds +
                " where s.userId not in (" +
                "    select userId from serial_no where riskStrategy !=''" +
                "        and createTime >= to_char(date_sub( ${fromTime}, 30),'yyyy-mm-dd')" +
                "        and createTime <= ${fromTime}" +
                "        and ds = to_char(getdate(), 'yyyymmdd')" +
                "        )" +
                " and s.createTime >= ${fromTime}" +
                " and s.createTime <= ${toTime}" +
                "  and s.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                " and s.ds = " + ds +
                "   group by a.mini_type;";
        ;
        list.add(packageParam(sql.concat(";"), "valid_user", "business_channel"));
    }*/

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotalByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "select a.mini_type business_channel,COUNT(DISTINCT if(l.businesschannel in(28,1013,1014,1016,1018,1019,1015,1017) and l.sendStatus = 5, l" +
                ".customerId, null))  " +
                "send_count" +
                " from cl_loan l" +
                " inner JOIN sh_order a on      l.loanno = a.order_uid" + "  and a.ds = " + ds +
                " where l.parentNo = ''" +
                "  and l.ds = " + ds +
                "  and l.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and date(sendtime) = ${sendTime} " +
                "   group by a.mini_type;";
        list.add(packageParam(sql, "send_count", "business_channel"));
    }

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotalByMiniTypeTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select a.mini_type business_channel,COUNT(DISTINCT if(l.businesschannel in(28,1013,1014,1016,1018,1019,1015,1017) and l.sendStatus = 5, l" +
                ".customerId, null))  " +
                "send_count" +
                " from alchemist.cl_loan l" +
                " inner JOIN qnvip_merchant.sh_order a on      l.loanno = a.order_uid " +
                " where l.parentNo = ''" +
                "  and l.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and date(sendtime) = ${sendTime} " +
                "   group by a.mini_type;";
        list.add(packageParam(sql, "send_count", "business_channel"));
    }

    /**
     * 人行风控执行总数\人行风控通过数\一级审批通过数\人行风控当日订单\差额\非差额
     *
     * @param list
     */
    private void getBigDataPassTotalByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "select a.mini_type business_channel," +
                "       count(DISTINCT  if(artificialauditstatus in (10, 15), a.customer_id, null))  as central_bank_inquiry," +
                "       count(DISTINCT if(\n" +
                "                     date(a.create_time) = to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                             artificialauditstatus in (10, 15), a.customer_id,\n" +
                "                     null)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as central_bank_pass," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt =0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as un_difference_big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt >0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as difference_big_data_pass" +
                " from cl_loan cl" +
                "         inner join serial_no sn" +
                "                    on sn.businessNo = cl.loanNo" + "  and sn.ds = " + ds +
                "                       and sn.rhtime >=  ${fromTime} \n" +
                "                       and sn.rhtime <= ${toTime}" +
                "         inner JOIN sh_order a on      cl.loanno = a.order_uid and a.test_role=0 and a.is_deleted=0 and a.ds = " + ds +
                "                   inner join sh_order_finance sof on a.order_uid = sof.order_uid and sof.is_deleted=0 and sof.ds=" + ds +
                " where  cl.parentNo = ''" +
                "  and cl.ds =" + ds +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)  group by a.mini_type";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "cur_day_num", "un_difference_big_data_pass", "difference_big_data_pass", "business_channel"));
    }

    private void getBigDataPassTotalByMiniTypeTidb(List<Map<String, Object>> list, String ds) {
        String sql = "select a.mini_type business_channel," +
                "       count(DISTINCT  if(artificialauditstatus in (10, 15), a.customer_id, null))  as central_bank_inquiry," +
                "       count(DISTINCT if(\n" +
                "                     date(a.create_time) = DATE(sn.`rhTime`) and\n" +
                "                             artificialauditstatus in (10, 15), a.customer_id,\n" +
                "                     null)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as central_bank_pass," +
                "       count(DISTINCT (if((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60),\n" +
                "                                a.customer_id,\n" +
                "                                null))) as big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt =0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as un_difference_big_data_pass," +
                "       count(DISTINCT (if(((cl.riskFicoStatus = 60 and cl.artificialauditstatus = 10) or (riskFicoStatus > 60))  and sof.credit_difference_amt >0,\n" +
                "                                a.customer_id,\n" +
                "                                null))) as difference_big_data_pass" +
                " from alchemist.cl_loan cl" +
                "         inner join alchemist.serial_no sn" +
                "                    on sn.businessNo = cl.loanNo" +
                "                       and sn.rhtime >=  ${fromTime} \n" +
                "                       and sn.rhtime <= ${toTime}" +
                "         inner JOIN qnvip_merchant.sh_order a on      cl.loanno = a.order_uid and a.test_role=0 and a.is_deleted=0  "+
                "                   inner join qnvip_merchant.sh_order_finance sof on a.order_uid = sof.order_uid and sof.is_deleted=0 "+
                " where  cl.parentNo = ''" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)  group by a.mini_type";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "cur_day_num", "un_difference_big_data_pass", "difference_big_data_pass", "business_channel"));
    }


    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotalByMiniTypeTidb(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type business_channel,SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt" +
                " FROM alchemist.cl_loan cl" +
                "    INNER JOIN alchemist.rc_assess_record rr ON rr.loanId=cl.id" +
                " inner JOIN qnvip_merchant.sh_order a on      cl.loanno = a.order_uid " +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "   group by a.mini_type;";
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt", "business_channel"));
    }

    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotalByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type business_channel,SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt" +
                " FROM cl_loan cl" +
                "    INNER JOIN rc_assess_record rr ON rr.loanId=cl.id" +
                " inner JOIN sh_order a on      cl.loanno = a.order_uid" + "  and a.ds = " + ds +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                " and cl.ds =" + ds +
                " and rr.ds = " + ds +
                "   group by a.mini_type;";
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt", "business_channel"));
    }

    private CompletableFuture<List<Map<String, String>>> getData(Map<String, Object> param2condition) {

        return CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);

            List<Record> records = odpsUtil.querySql(format.concat(";"));

            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            return records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = record.getString(column);
                    column2value.put(column, value);
                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
        }));
    }

    private CompletableFuture<List<Map<String, String>>> getDataTidb(Map<String, Object> param2condition) {

        return CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);

            List<Map<String,String>> riskUserVos=rentOrderService.getGeneralCommonSql(format);


            if (CollectionUtils.isEmpty(riskUserVos)) {
                return Lists.newArrayList();
            }
            return riskUserVos.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = null;
                    if(null == record){
                        column2value.put(column, "0");
                    }
                    else {
                        value = String.valueOf(record.get(column) == null ? "0":record.get(column));
                        column2value.put(column, value);
                    }

                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
        }));
    }

    private Map<String, Object> packageParam(String sql, String... columns) {
        HashMap<String, Object> param2condition = Maps.newHashMap();
        param2condition.put("sql", sql);
        param2condition.put("column", columns);
        return param2condition;
    }
}
