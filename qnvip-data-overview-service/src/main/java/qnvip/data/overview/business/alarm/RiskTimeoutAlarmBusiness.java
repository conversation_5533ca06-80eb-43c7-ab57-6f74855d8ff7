package qnvip.data.overview.business.alarm;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.util.DingDingAlarmUtil;
import qnvip.data.overview.util.OdpsUtil;

import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * create by gw on 2022/3/4
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskTimeoutAlarmBusiness {

    private static DateTimeFormatter dayDF = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final OdpsUtil odpsUtil;
    private final DingDingAlarmUtil dingDingAlarmUtil;


    public void queryRiskTimeoutAndAlarm(String ds, String currentTime) {
        String sql = " select count(distinct a.id) count, " +
                "       if(dateadd(c.id_card_ocr_time, 48, 'HH') <= '" + currentTime + "' " +
                "              and dateadd(a.create_time, 48, 'HH') <= '" + currentTime + "', 1, 0) over_two_days " +
                "from rent_order a " +
                "         inner join rent_order_audit b on a.id = b.order_id " +
                "         inner join rent_customer_auth c on a.customer_id = c.customer_id " +
                "where c.id_card_ocr_time is not null " +
                "  and dateadd(c.id_card_ocr_time, 12, 'HH') <= '" + currentTime + "' " +
                "  and dateadd(a.create_time, 12, 'HH') <= '" + currentTime + "' " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.termination != 5 " +
                "  and b.type = 2 " +
                "  and b.audit_status = 0 " +
                "  and a.is_deleted = 0 " +
                "  and b.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by if(dateadd(c.id_card_ocr_time, 48, 'HH') <= '" + currentTime + "' " +
                "                and dateadd(a.create_time, 48, 'HH') <= '" + currentTime + "', 1, 0); ";
        List<Record> records = odpsUtil.querySql(sql);
        if (CollUtil.isEmpty(records)) {
            return;
        }
        Integer totalCount = 0;
        Integer overTwoDaysCount = 0;
        for (Record record : records) {
            String overTwoDays = record.getString("over_two_days");
            Integer count = Integer.valueOf(record.getString("count"));
            if(overTwoDays.equals("1")){
                overTwoDaysCount += count;
            }
            totalCount+=count;
        }
        String sql2 = " select count(distinct a.id) count " +
                "from rent_order a " +
                "         inner join rent_order_audit b on a.id = b.order_id " +
                "         inner join rent_customer_auth c on a.customer_id = c.customer_id " +
                "         inner join cl_loan d on a.no = d.loanno and d.artificialauditstatus = 1 " +
                "where c.id_card_ocr_time is not null " +
                "  and dateadd(c.id_card_ocr_time, 12, 'HH') <= '"+currentTime+"' " +
                "  and dateadd(a.create_time, 12, 'HH') <= '"+currentTime+"' " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.termination != 5 " +
                "  and b.type = 2 " +
                "  and b.audit_status = 0 " +
                "  and a.is_deleted = 0 " +
                "  and b.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +"; ";
        List<Record> records2 = odpsUtil.querySql(sql2);
        String artificialAuditCount = records2.get(0).getString("count");
        String errorMsg = "【订单审核超时】 当前总量："+totalCount+"笔；超过48小时量："+overTwoDaysCount+"笔，人审量："+artificialAuditCount+"笔";
        //查询人审量
        dingDingAlarmUtil.marketingExceptionAlarm(errorMsg);
    }


}