package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorLossOrderStayDO;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderCustomerDO;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderItemDO;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderResideDO;
import qnvip.data.overview.service.dataindicators.IndicatorLossOrderStayService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/2/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLossOrderStayBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorLossOrderStayService indicatorLossOrderStayService;


    /**
     * 启动计算任务
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorLossOrderStayDO> miniType2Map = new HashMap<>();
        countIntentionStayTimeCount(ds,sTime,eTime,miniType2Map);
        for (IndicatorLossOrderStayDO value : miniType2Map.values()) {
            indicatorLossOrderStayService.saveOrUpdate(value);
        }
    }



    /**
     * 下单意愿人数中分区间统计
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countIntentionStayTimeCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorLossOrderStayDO> collectMap) {
        String sql = " select  a.day,a.mini_type,a.staytime,count(1) as count  " +
                "from    (  " +
                "            select  min(b.mini_type) mini_type,to_char(b.report_time, 'yyyy-mm-dd') as day " +
                "            , ( " +
                "               case when sum(b.keep_alive_time) <19000 then 0 " +
                "                                when sum(b.keep_alive_time) <67000 then 1 " +
                "                                when sum(b.keep_alive_time) <211000 then 2 " +
                "                                when sum(b.keep_alive_time) <434000 then 3 " +
                "                                else 4  " +
                "                        end " +
                "            ) staytime " +
                "            from    dataview_track_enter_applets b " +
                "            inner join rent_customer c    " +
                "                on get_json_object(c.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "            where   b.action_type = 2  " +
                "            and b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "            and     b.ds = " + ds +
                "            and     c.ds = " + ds +
                "            and     b.report_time between '"+sTime+"' " +
                "            and '"+eTime+"' " +
                "            group by to_char(b.report_time, 'yyyy-mm-dd')  " +
                "                    ,c.id  " +
                "            having  sum(b.keep_alive_time) > " + IndicatorBeforeSaleBusiness.WANT_N_MILL_SECOND +
                "        ) a  " +
                "        group by a.mini_type,a.day,a.staytime; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossOrderStayDO updateDO = collectMap.get(getMapKey(miniType, day));
            Integer stayTime = Integer.valueOf(record.getString("staytime"));
            Integer count = Integer.valueOf(record.getString("count"));
            switch (stayTime) {
                case 0:
                    updateDO.setLevelOne(count);
                    break;
                case 1:
                    updateDO.setLevelTwo(count);
                    break;
                case 2:
                    updateDO.setLevelThree(count);
                    break;
                case 3:
                    updateDO.setLevelFour(count);
                    break;
                case 4:
                    updateDO.setLevelFive(count);
                    break;
                default:
                    break;
            }
        }
    }




    private void initMap(Map<String, IndicatorLossOrderStayDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorLossOrderStayDO ioid = new IndicatorLossOrderStayDO();
            ioid.setMiniType(miniType);
            ioid.setCountDay(countDay);
            ioid.setLevelOne(0);
            ioid.setLevelTwo(0);
            ioid.setLevelThree(0);
            ioid.setLevelFive(0);
            ioid.setLevelFour(0);
            miniType2Map.put(key, ioid);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}