package qnvip.data.overview.business.risk;

import com.google.common.collect.Range;
import com.google.common.collect.RangeMap;
import com.google.common.collect.TreeRangeMap;
import com.qnvip.base.vo.req.JobTaskReq;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;
import qnvip.data.overview.business.rpc.JobApiService;
import qnvip.data.overview.domain.risk.RiskDistributionBaseDO;
import qnvip.data.overview.domain.risk.RiskDistributionOrderOverdueDO;
import qnvip.data.overview.domain.risk.RiskOrderOverdueDO;
import qnvip.data.overview.service.risk.RiskDistributionBaseService;
import qnvip.data.overview.service.risk.RiskDistributionOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.rent.common.base.MultiResult;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 风控大盘-vintage-商户长租
 * create by gw on 2022/3/24
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskDistributionBusiness {
    private static final Integer PAGE_SIZE = 5000;
    private static AtomicInteger atomicInteger = new AtomicInteger();
    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });
    @Qualifier("businessJobApiService")
    private final JobApiService jobApiService;
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final RiskDistributionBaseService riskDistributionBaseService;
    private final RiskDistributionOrderOverdueService riskDistributionOrderOverdueService;

    public void execOldData(int... overdueDays) {
        // order总记录数
        riskDistributionOrderOverdueService.deleteAll();

        for (int overdueDay : overdueDays) {
            Integer size = riskDistributionBaseService.getNum(overdueDay);
            log.info(" 数仓 dataview_risk_distribution_base2 {} ", size);
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }

            // 创建CountDownLatch来等待所有任务完成
            CountDownLatch latch = new CountDownLatch(times);

            int startPage = 1;
            for (int i = 0; i < times; i++) {
                int p = startPage;
                threadPoolExecutor.execute(() -> {
                    try {
                        oldRepayTask(p, overdueDay);
                    } finally {
                        latch.countDown(); // 任务完成后计数减1
                    }
                });
                startPage++;
            }

            try {
                // 等待所有任务完成
                latch.await();
                log.info("商户长租明细表 逾期天数 {} 的所有异步任务已完成，共执行 {} 个任务", overdueDay, times);

                // 创建任务记录
                JobTaskReq jobTaskReq = new JobTaskReq();
                jobTaskReq.setTaskCode("DATAVIEW_RISK_DISTRIBUTION_ORDER_OVERDUE");
                jobTaskReq.setBizType(0);

                // 设置任务时间 - 使用当前时间
                LocalDateTime now = LocalDateTime.now();
                // 尝试使用ISO格式
                String taskSuccessTime = now.format(dateTimeFormatter);
                jobTaskReq.setTaskSuccessTime(taskSuccessTime);
                log.info("准备插入任务记录: taskCode={}, bizType={}, taskSuccessTime={}",
                    jobTaskReq.getTaskCode(), jobTaskReq.getBizType(), jobTaskReq.getTaskSuccessTime());

                try {
                    Object result = jobApiService.insertJobTask(jobTaskReq);
                    log.info("任务记录插入成功: {}", result);
                } catch (Exception e) {
                    log.error("任务记录插入失败", e);
                }
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待异步任务完成时被中断", e);
            }
        }
    }

    // 数据量上来后会有深翻页问题
    private void oldRepayTask(int startPage, Integer overdueDay) {
        MultiResult<RiskDistributionBaseDO> result = riskDistributionBaseService.getList(overdueDay, PAGE_SIZE,
                startPage);
        List<Long> collect =
                result.getData().stream().map(RiskDistributionBaseDO::getOrderId).collect(Collectors.toList());
        List<RiskDistributionBaseDO> list = riskDistributionBaseService.getListByOrderId(overdueDay, collect);
        saveData(list, overdueDay);
    }


    private void saveData(List<RiskDistributionBaseDO> records, Integer overdueDay) {
        List<RiskDistributionOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay)
                .stream()
                .peek(o -> {
                    //续租买断更新13期之后的数据
//                    if (o.getRenewStatus() == 1) {
                    //订单最大期数
                    Integer maxTerm = o.getMaxTerm();
                    //可续租期数
                    Integer renewTerm = o.getRenewTerm();
                    //续租时间间隔
                    Integer renewDay = o.getRenewDay();
                    //最后一期未还金额
                    BigDecimal parentNotReturnAmt = getParentNotReturnAmt(o, maxTerm);
                    // 最大期数+续租期数
                    int newMaxTerm = maxTerm + renewTerm;
                    o.setMaxTerm(newMaxTerm);
                    for (int i = 1; i <= renewTerm; i++) {
                        // 将假续租订单的逾期未还金额设置为,租后待买断订单的最后一期逾期未还金额
                        setTermNotRepayVal(o, maxTerm + i, parentNotReturnAmt);
                        // 设置续租总租金
                        setRenewAmt(o, maxTerm + i, o.getRenewAmt1());
//                      setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
                    }
                    // 把逾期未还进行扩展到30期
                    //找打最大期数的金额(父订单期数+子订单期数的上一个金额)
                    BigDecimal parentNotReturnAmt2 = getParentNotReturnAmt(o, newMaxTerm);
                    if (parentNotReturnAmt2.compareTo(BigDecimal.ZERO) > 0) {
                        for (int i = newMaxTerm; i <= 30; i++) {
                            setTermNotRepayVal(o, i + 1, parentNotReturnAmt2);
                            setOverdueVal(o, i + 1, 1, BigDecimal.ZERO);
                        }
                    }
                    //有逾期金额才更新为已逾期
                    if (parentNotReturnAmt.compareTo(BigDecimal.ZERO) > 0) {
                        // 设置逾期情况,判断续租待买断订单的逾期时间
                        for (int i = 1, day = renewDay + 1; i <= day && maxTerm + i <= newMaxTerm; i++) {
                            setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
                        }
                    }
//                    }
                }).collect(Collectors.toList());
        riskDistributionOrderOverdueService.saveBatch(generalList);
    }


    private RiskDistributionBaseDO getPlanDo(List<RiskDistributionBaseDO> value, Integer maxTerm, Map<Integer,
            List<RiskDistributionBaseDO>> term2PlanDo) {
        List<RiskDistributionBaseDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
        RiskDistributionBaseDO planDO = repaymentPlanDOList.get(0);
        if (value.size() > 1) {
            for (RiskDistributionBaseDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        return planDO;
    }


    private List<RiskDistributionOrderOverdueDO> fetchGeneralList(List<RiskDistributionBaseDO> records,
                                                                  Integer overdueDay) {
        List<RiskDistributionOrderOverdueDO> resList = new ArrayList<>();
        // 获取每笔订单对应的1~12期还款情况
        Map<Long, List<RiskDistributionBaseDO>> orderId2List =
                records.stream().collect(Collectors.groupingBy(RiskDistributionBaseDO::getOrderId));

        // 把租后待买断的订单先当作续租处理,如果真买断了,就走原来的逻辑,如果一直未买断,也可以按照续租的方式计算逾期率
        for (Map.Entry<Long, List<RiskDistributionBaseDO>> entry : orderId2List.entrySet()) {
            // 获取订单id
            Long orderId = entry.getKey();
            // 获取订单还款计划
            List<RiskDistributionBaseDO> value = entry.getValue();
            //使用之后的优惠券金额
            BigDecimal discountAmt = ObjectUtils.getSum(value, RiskDistributionBaseDO::getDiscountAmt);
            RiskDistributionOrderOverdueDO riskOrderOverdueDO = null;
            // 获取订单最大期数 todo 具体做啥用后面补充
            Integer maxTerm = value.stream().max(Comparator.comparing(RiskDistributionBaseDO::getTerm)).get().getTerm();
            // 获取每一期的还款计划,这边对应的是list是因为有的一期有两条数据,应该是还款计划没有过滤is_deleted
            Map<Integer, List<RiskDistributionBaseDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RiskDistributionBaseDO::getTerm));
            // 循环遍历每一期还款计划
            for (RiskDistributionBaseDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskDistributionOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setShopName(repaymentPlanDO.getShopName());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setAuditTypeName(repaymentPlanDO.getAuditTypeName());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    String riskLevel = repaymentPlanDO.getRiskLevel();
                    if (riskLevel.contains("风控等级")) {
                        // 将风控等级4.0和风控等级4之类的进行合并
                        String[] split = riskLevel.split("\\.");
                        riskLevel = split[0];
                    }
                    riskOrderOverdueDO.setRiskLevel(riskLevel);
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setPDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setPBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    // 这里还涉及一个保证金区间的逻辑
                    BigDecimal mul = CalculateUtil.mul(BigDecimal.valueOf(repaymentPlanDO.getBondRate()).setScale(2,
                            RoundingMode.HALF_UP), 100);
                    String bondRateStr = fetchInterval(mul.doubleValue());
                    riskOrderOverdueDO.setBondRate(bondRateStr);

                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                    riskOrderOverdueDO.setPMaxTerm(maxTerm);
                    riskOrderOverdueDO.setMaxTerm(maxTerm);
                    riskOrderOverdueDO.setRenewDay(repaymentPlanDO.getRenewDay());
                    riskOrderOverdueDO.setRenewStatus(repaymentPlanDO.getRenewStatus());
                    riskOrderOverdueDO.setRenewTerm(repaymentPlanDO.getRenewTerm());
                    riskOrderOverdueDO.setNo(repaymentPlanDO.getOrderNo());
                    riskOrderOverdueDO.setIsMortgage(repaymentPlanDO.getIsMortgage());
                    riskOrderOverdueDO.setIsRent(repaymentPlanDO.getIsRent());
                    riskOrderOverdueDO.setRefuseType(repaymentPlanDO.getRefuseType());
                    riskOrderOverdueDO.setDrainageType(repaymentPlanDO.getDrainageType());
                    riskOrderOverdueDO.setZmfLevel(repaymentPlanDO.getZmfLevel());
                    //之前没有
                    riskOrderOverdueDO.setTotalDiscount(repaymentPlanDO.getTotalDiscount());
                    riskOrderOverdueDO.setSurplusBondAmt(repaymentPlanDO.getSurplusBondAmt());
                    riskOrderOverdueDO.setBondRestFundAmount(repaymentPlanDO.getBondRestFundAmount());
                    riskOrderOverdueDO.setDiffPricingDiscountAmt(repaymentPlanDO.getDiffPricingDiscountAmt());
                    riskOrderOverdueDO.setCouponDiscountAmt(repaymentPlanDO.getCouponDiscountAmt());
                    riskOrderOverdueDO.setDiscountPricingMode(repaymentPlanDO.getDiscountPricingMode());
                    riskOrderOverdueDO.setChildNo(repaymentPlanDO.getChildNo());
                    riskOrderOverdueDO.setChildOrderId(repaymentPlanDO.getChildOrderId());
                    riskOrderOverdueDO.setRenewTotalRent(repaymentPlanDO.getRenewTotalRent());
                    riskOrderOverdueDO.setDiscountAmt(discountAmt);
                    riskOrderOverdueDO.setBuyOutCapital(repaymentPlanDO.getBuyOutCapital());
                    riskOrderOverdueDO.setBuyOutRealRepayCapital(repaymentPlanDO.getBuyOutRealRepayCapital());

                    //免押类型
                    riskOrderOverdueDO.setDepositFreeType(repaymentPlanDO.getDepositFreeType());
                    //流量类型
                    riskOrderOverdueDO.setTrafficType(repaymentPlanDO.getTrafficType());
                    //金融方案
                    riskOrderOverdueDO.setFinancialSolutions(repaymentPlanDO.getFinancialSolutions());
                    //应用
                    riskOrderOverdueDO.setApplicationName(repaymentPlanDO.getApplicationName());
                    //新旧程度
                    riskOrderOverdueDO.setEquipmentState(repaymentPlanDO.getEquipmentState());
                    //是否监管
                    riskOrderOverdueDO.setSupervisedMachine(repaymentPlanDO.getSupervisedMachine());
                    //机型
                    riskOrderOverdueDO.setMachineType(repaymentPlanDO.getMachineType());
                }
                // 获取当前期的逾期状态
                int isOverdue = repaymentPlanDO.getIsOverdue();
                // 获取当前期的期数
                int term = repaymentPlanDO.getTerm();
                //针对一些期数需要根据上一期的还款表现来判断当前逾期状态,对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(isOverdue);
                    repaymentPlanDO.setOverdue(5);
                    //    repaymentPlanDO.setIsOverdue(1);
                }

//                if(isOverdue!=1){ //如果当前是未逾期,就继续找上一期是否逾期
//                    int tmp_term = term;
//                    while (tmp_term>1){
//                        //获取上一期的对象
//                        List<RiskDistributionBaseDO> lastTermDo = term2PlanDo.get(tmp_term);
//                        RiskDistributionBaseDO riskDistributionBaseDO = lastTermDo.get(0);
//                        if(riskDistributionBaseDO.getIsOverdue().equals(1)){
//                            repaymentPlanDO.setIsOverdue(1);
//                            break;
//                        }
//                        else{
//                            tmp_term=tmp_term-1;
//                        }
//                    }
//                }

//                isOverdue = repaymentPlanDO.getIsOverdue(); //重新获取更新后的逾期状态
                // 获取当前订单起租日
                LocalDateTime rentStartDay = repaymentPlanDO.getCountDay();
                riskOrderOverdueDO.setCountDay(rentStartDay);
                // 设置订单当前期的逾期状态和滞纳金
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                // 设置订单的续租金额,也就是说 renewAmt1~renewAmt1这些字段都要设置成一样的续租总租金
                setRenewAmt(riskOrderOverdueDO, term, repaymentPlanDO.getRenewTotalRent());
                // 这一步就是为了拿最后一期订单,todo 具体干啥用的待定
                RiskDistributionBaseDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    // 获取当前期还款时间
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                    // 获取当前期封帐日,这个是加上了宽限期的
                    LocalDate localDate =
                            LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    // 封账时间改成下个月3号,小于4号就没到封账期
//                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(4));

                    // 获取最后一期的封帐日
                    LocalDate maxRepayDate =
                            LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                    LocalDate maxRepayDate =
//                            LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(4));

                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);

                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
//                    log.info("fetchCapital  resMap.get(capital)==>"+decimal+"期数="+term);
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }


    /**
     * 判断区间
     *
     * @param d
     * @return
     */
    private String fetchInterval(Double d) {
        RangeMap<Double, String> level = TreeRangeMap.create();
        level.put(Range.closed(0D, 5D), "(0%-5%],1");
        level.put(Range.openClosed(5D, 10D), "(5%-10%],2");
        level.put(Range.openClosed(10D, 15D), "(10%-15%],3");
        level.put(Range.openClosed(15D, 20D), "(15%-20%],4");
        level.put(Range.openClosed(20D, 25D), "(20%-25%],5");
        level.put(Range.openClosed(25D, 30D), "(25%-30%],6");
        level.put(Range.openClosed(30D, 35D), "(30%-35%],7");
        level.put(Range.openClosed(35D, 40D), "(35%-40%],8");
        level.put(Range.openClosed(40D, 45D), "(40%-45%],9");
        level.put(Range.openClosed(45D, 50D), "(45%-50%],10");
        level.put(Range.openClosed(50D, 55D), "(50%-55%],11");
        level.put(Range.openClosed(55D, 60D), "(55%-60%],12");
        level.put(Range.openClosed(60D, 65D), "(60%-65%],13");
        level.put(Range.openClosed(65D, 70D), "(65%-70%],14");
        level.put(Range.openClosed(70D, 75D), "(70%-75%],15");
        level.put(Range.openClosed(75D, 80D), "(75%-80%],16");
        level.put(Range.openClosed(80D, 85D), "(80%-85%],17");
        level.put(Range.openClosed(85D, 90D), "(85%-90%],18");
        level.put(Range.openClosed(90D, 95D), "(90%-95%],19");
        level.put(Range.openClosed(95D, 100D), "(95%-100%],20");

        return level.get(d);
    }


    private int updateOverdueStatus(Map<Integer, List<RiskDistributionBaseDO>> term2PlanDo, int term) {
        // 如果当前期逾期,不管有没有到封账日,都算逾期
        List<RiskDistributionBaseDO> riskDistributionBaseDOS = term2PlanDo.get(term);
        RiskDistributionBaseDO riskDistributionBaseDO = riskDistributionBaseDOS.get(0);
        if ((riskDistributionBaseDO.getRealRepayTime() == null ||
                riskDistributionBaseDO.getRealRepayTime().isAfter(riskDistributionBaseDO.getRepayDate()))
                && riskDistributionBaseDO.getRepayDate().isBefore(LocalDateTime.now().minusDays(1))
                && riskDistributionBaseDO.getRepayStatus() != 5) {
            //当前期未还且逾期
            return 1;
        }

        // 当前实际还款实际为空，且当前时间大于应还时间，当期为逾期
        List<RiskDistributionBaseDO> repaymentPlanDOList = term2PlanDo.get(--term);
        RiskDistributionBaseDO planDO = repaymentPlanDOList.get(0);
        if (repaymentPlanDOList.size() > 1) {
            for (RiskDistributionBaseDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        int isOverdue = planDO.getIsOverdue();
        // 上一期逾期并且未还款,这一期即使还没到,算逾期
        // 上一期逾期并且还款,这一期还没到,不算逾期
        // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期

        int repayStatus = planDO.getRepayStatus();
        return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
    }


    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }


    /**
     * 递归查找，最早的的逾期期数
     *
     * @param map term为key，还款计划为val
     * @param
     */
    public void fetchTerm(Map<Integer, List<RiskDistributionBaseDO>> map, Map<String, Object> resMap) {
        int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        List<RiskDistributionBaseDO> planDOList = map.get(key);
        RiskDistributionBaseDO planDO = planDOList.get(0);
        if (planDOList.size() > 1) {
            for (RiskDistributionBaseDO repaymentPlanDO : planDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        if (planDO != null) {
            // 当前期逾期状态
            Integer isOverdue = planDO.getIsOverdue();
            // 还款计划还款状态
            Integer repayStatus = planDO.getRepayStatus();
            // 还款计划逾期状态(还款计划是严格按照应还日期来判断逾期状态的)
            Integer overdue = planDO.getOverdue();
            LocalDateTime realRepayTime = planDO.getRealRepayTime();
            // 获取当前期数
            BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));

            // //  本期逾期了判断上一期是否逾期,上一期未逾期，那么逾期期数就是本期
            // // 本期逾期了判断上一期是否逾期,上一期逾期，并且未还,继续向上判断,直到找到已还款的那一期,那一期+1就是逾期未还金额
            // // 本期逾期了判断上一期是否逾期,上一期逾期并且已经还款,逾期金额用本期算
            // 封账日
            LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                    dateFormatter), LocalTime.MAX);

            if (Integer.valueOf(1).equals(isOverdue)) {
                //先判断本期的状态是否 已逾期未还款
                if (repayStatus == 1 && overdue == 5) {
                    if (term.compareTo(new BigDecimal(1)) == 0) {
                        resMap.put("term", term);
                    } else {
                        // 递归判断判断上一期的订单是否逾期
                        term = term.subtract(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                        fetchTerm(map, resMap);
                    }
                } else {
                    // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
                    // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
                    // 再递归往上判断
                    if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
                        if (term.compareTo(new BigDecimal(1)) == 0) {
                            resMap.put("term", term);
                        } else {
                            term = term.subtract(BigDecimal.valueOf(1));
                            resMap.put("term", term);
                            fetchTerm(map, resMap);
                        }
                    } else {
                        term = term.add(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                    }
                }

            } else {
                term = term.add(BigDecimal.valueOf(1));
                resMap.put("term", term);
            }


        }

    }

    public void fetchCapital(Map<Integer, List<RiskDistributionBaseDO>> map, Map<String, Object> resMap) {
        // 获取当前期数
        int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        // 获取封帐日
        LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                dateFormatter), LocalTime.MAX);
        resMap.put("level", 1);
        // 只有第一期不参与递归
        if (sourceTerm > 1) {
            fetchTerm(map, resMap);
        }
        int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        // 循环每一期,逻辑就是把每一期的未还金额加起来,就是对应那一期的总未还金额
        for (Map.Entry<Integer, List<RiskDistributionBaseDO>> entry : map.entrySet()) {
            // 获取对应还款计划
            List<RiskDistributionBaseDO> value = entry.getValue();
            RiskDistributionBaseDO planDO = value.get(0);
            if (value.size() > 1) {
                for (RiskDistributionBaseDO repaymentPlanDO : value) {
                    if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                        planDO = repaymentPlanDO;
                    }
                }
            }
            if (entry.getKey() >= term) {
                BigDecimal capital;
                // 如果是第一期
                if (term == entry.getKey()) {
                    // 实际还款时间大于这一期封账日,那么这一期将当没还
                    if (planDO.getRealRepayTime() == null) {
                        capital = planDO.getCapital();
                    } else if (planDO.getRealRepayTime() != null && planDO.getRealRepayTime().compareTo(endTime) > 0) {
                        capital = planDO.getCapital();
                    } else {
                        capital = planDO.getRealCapital();
                    }
                } else {
                    capital = planDO.getCapital();
                }
                if (planDO.getRenewTime() == null && "2".equals(planDO.getStageNo())) {
                    capital = BigDecimal.ZERO;
                }
//                log.info("*************resMap.get(capital)*************"+capital);
                resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
            }
        }
    }


    private void setOverdueVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        }
    }

    private void setRenewAmt(RiskDistributionOrderOverdueDO riskOrderOverdueDO,
                             int term,
                             BigDecimal renewAmt) {
        if (term == 1) {
            riskOrderOverdueDO.setRenewAmt1(renewAmt);
        } else if (term == 2) {
            riskOrderOverdueDO.setRenewAmt2(renewAmt);
        } else if (term == 3) {
            riskOrderOverdueDO.setRenewAmt3(renewAmt);
        } else if (term == 4) {
            riskOrderOverdueDO.setRenewAmt4(renewAmt);
        } else if (term == 5) {
            riskOrderOverdueDO.setRenewAmt5(renewAmt);
        } else if (term == 6) {
            riskOrderOverdueDO.setRenewAmt6(renewAmt);
        } else if (term == 7) {
            riskOrderOverdueDO.setRenewAmt7(renewAmt);
        } else if (term == 8) {
            riskOrderOverdueDO.setRenewAmt8(renewAmt);
        } else if (term == 9) {
            riskOrderOverdueDO.setRenewAmt9(renewAmt);
        } else if (term == 10) {
            riskOrderOverdueDO.setRenewAmt10(renewAmt);
        } else if (term == 11) {
            riskOrderOverdueDO.setRenewAmt11(renewAmt);
        } else if (term == 12) {
            riskOrderOverdueDO.setRenewAmt12(renewAmt);
        } else if (term == 13) {
            riskOrderOverdueDO.setRenewAmt13(renewAmt);
        } else if (term == 14) {
            riskOrderOverdueDO.setRenewAmt14(renewAmt);
        } else if (term == 15) {
            riskOrderOverdueDO.setRenewAmt15(renewAmt);
        } else if (term == 16) {
            riskOrderOverdueDO.setRenewAmt16(renewAmt);
        } else if (term == 17) {
            riskOrderOverdueDO.setRenewAmt17(renewAmt);
        } else if (term == 18) {
            riskOrderOverdueDO.setRenewAmt18(renewAmt);
        } else if (term == 19) {
            riskOrderOverdueDO.setRenewAmt19(renewAmt);
        } else if (term == 20) {
            riskOrderOverdueDO.setRenewAmt20(renewAmt);
        } else if (term == 21) {
            riskOrderOverdueDO.setRenewAmt21(renewAmt);
        } else if (term == 22) {
            riskOrderOverdueDO.setRenewAmt22(renewAmt);
        } else if (term == 23) {
            riskOrderOverdueDO.setRenewAmt23(renewAmt);
        } else if (term == 24) {
            riskOrderOverdueDO.setRenewAmt24(renewAmt);
        } else if (term == 25) {
            riskOrderOverdueDO.setRenewAmt25(renewAmt);
        } else if (term == 26) {
            riskOrderOverdueDO.setRenewAmt26(renewAmt);
        } else if (term == 27) {
            riskOrderOverdueDO.setRenewAmt27(renewAmt);
        } else if (term == 28) {
            riskOrderOverdueDO.setRenewAmt28(renewAmt);
        } else if (term == 29) {
            riskOrderOverdueDO.setRenewAmt29(renewAmt);
        } else if (term == 30) {
            riskOrderOverdueDO.setRenewAmt30(renewAmt);
        }
    }

    private void setOverdueVal(RiskDistributionOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        } else if (term == 13) {
            riskOrderOverdueDO.setIsOverdue13(isOverdue);
            riskOrderOverdueDO.setOverdueFine13(overdueFine);
        } else if (term == 14) {
            riskOrderOverdueDO.setIsOverdue14(isOverdue);
            riskOrderOverdueDO.setOverdueFine14(overdueFine);
        } else if (term == 15) {
            riskOrderOverdueDO.setIsOverdue15(isOverdue);
            riskOrderOverdueDO.setOverdueFine15(overdueFine);
        } else if (term == 16) {
            riskOrderOverdueDO.setIsOverdue16(isOverdue);
            riskOrderOverdueDO.setOverdueFine16(overdueFine);
        } else if (term == 17) {
            riskOrderOverdueDO.setIsOverdue17(isOverdue);
            riskOrderOverdueDO.setOverdueFine17(overdueFine);
        } else if (term == 18) {
            riskOrderOverdueDO.setIsOverdue18(isOverdue);
            riskOrderOverdueDO.setOverdueFine18(overdueFine);
        } else if (term == 19) {
            riskOrderOverdueDO.setIsOverdue19(isOverdue);
            riskOrderOverdueDO.setOverdueFine19(overdueFine);
        } else if (term == 20) {
            riskOrderOverdueDO.setIsOverdue20(isOverdue);
            riskOrderOverdueDO.setOverdueFine20(overdueFine);
        } else if (term == 21) {
            riskOrderOverdueDO.setIsOverdue21(isOverdue);
            riskOrderOverdueDO.setOverdueFine21(overdueFine);
        } else if (term == 22) {
            riskOrderOverdueDO.setIsOverdue22(isOverdue);
            riskOrderOverdueDO.setOverdueFine22(overdueFine);
        } else if (term == 23) {
            riskOrderOverdueDO.setIsOverdue23(isOverdue);
            riskOrderOverdueDO.setOverdueFine23(overdueFine);
        } else if (term == 24) {
            riskOrderOverdueDO.setIsOverdue24(isOverdue);
            riskOrderOverdueDO.setOverdueFine24(overdueFine);
        } else if (term == 25) {
            riskOrderOverdueDO.setIsOverdue25(isOverdue);
            riskOrderOverdueDO.setOverdueFine25(overdueFine);
        } else if (term == 26) {
            riskOrderOverdueDO.setIsOverdue26(isOverdue);
            riskOrderOverdueDO.setOverdueFine26(overdueFine);
        } else if (term == 27) {
            riskOrderOverdueDO.setIsOverdue27(isOverdue);
            riskOrderOverdueDO.setOverdueFine27(overdueFine);
        } else if (term == 28) {
            riskOrderOverdueDO.setIsOverdue28(isOverdue);
            riskOrderOverdueDO.setOverdueFine28(overdueFine);
        } else if (term == 29) {
            riskOrderOverdueDO.setIsOverdue29(isOverdue);
            riskOrderOverdueDO.setOverdueFine29(overdueFine);
        } else if (term == 30) {
            riskOrderOverdueDO.setIsOverdue30(isOverdue);
            riskOrderOverdueDO.setOverdueFine30(overdueFine);
        }
    }

    private void setTermNotRepayVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        }
    }

    private void setTermNotRepayVal(RiskDistributionOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setTerm13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setTerm14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setTerm15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setTerm16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setTerm17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setTerm18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setTerm19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setTerm20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setTerm21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setTerm22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setTerm23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setTerm24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setTerm25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setTerm26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setTerm27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setTerm28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setTerm29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setTerm30(decimal);
        }
    }

    private BigDecimal getParentNotReturnAmt(RiskDistributionOrderOverdueDO parentDO, Integer term) {
        BigDecimal amt = BigDecimal.ZERO;
        if (term == 1) {
            amt = parentDO.getTerm1();
        } else if (term == 2) {
            amt = parentDO.getTerm2();
        } else if (term == 3) {
            amt = parentDO.getTerm3();
        } else if (term == 4) {
            amt = parentDO.getTerm4();
        } else if (term == 5) {
            amt = parentDO.getTerm5();
        } else if (term == 6) {
            amt = parentDO.getTerm6();
        } else if (term == 7) {
            amt = parentDO.getTerm7();
        } else if (term == 8) {
            amt = parentDO.getTerm8();
        } else if (term == 9) {
            amt = parentDO.getTerm9();
        } else if (term == 10) {
            amt = parentDO.getTerm10();
        } else if (term == 11) {
            amt = parentDO.getTerm11();
        } else if (term == 12) {
            amt = parentDO.getTerm12();
        } else if (term == 13) {
            amt = parentDO.getTerm13();
        } else if (term == 14) {
            amt = parentDO.getTerm14();
        } else if (term == 15) {
            amt = parentDO.getTerm15();
        } else if (term == 16) {
            amt = parentDO.getTerm16();
        } else if (term == 17) {
            amt = parentDO.getTerm17();
        } else if (term == 18) {
            amt = parentDO.getTerm18();
        } else if (term == 19) {
            amt = parentDO.getTerm19();
        } else if (term == 20) {
            amt = parentDO.getTerm20();
        } else if (term == 21) {
            amt = parentDO.getTerm21();
        } else if (term == 22) {
            amt = parentDO.getTerm22();
        } else if (term == 23) {
            amt = parentDO.getTerm23();
        } else if (term == 24) {
            amt = parentDO.getTerm24();
        } else if (term == 25) {
            amt = parentDO.getTerm25();
        } else if (term == 26) {
            amt = parentDO.getTerm26();
        } else if (term == 27) {
            amt = parentDO.getTerm27();
        } else if (term == 28) {
            amt = parentDO.getTerm28();
        } else if (term == 29) {
            amt = parentDO.getTerm29();
        } else if (term == 30) {
            amt = parentDO.getTerm30();
        }

        return amt;
    }
}



