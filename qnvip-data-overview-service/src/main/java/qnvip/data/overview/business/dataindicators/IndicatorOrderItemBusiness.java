package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorOrderItemDO;
import qnvip.data.overview.service.dataindicators.IndicatorOrderItemService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/1/16
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorOrderItemBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorOrderItemService indicatorOrderItemService;

    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorOrderItemDO> collectMap = new HashMap<>();
        countPlanTotal(ds, sTime, eTime, collectMap);
        countPlanWithService(ds, sTime, eTime, collectMap);
        countPlanByFreeBond(ds, sTime, eTime, collectMap);
        for (IndicatorOrderItemDO value : collectMap.values()) {
            value.setPlanOneNotService(value.getPlanOneTotal() - value.getPlanOneWithService());
            value.setPlanTwoNotService(value.getPlanTwoTotal() - value.getPlanTwoWithService());
            indicatorOrderItemService.saveOrUpdate(value);
        }
    }

    /**
     * 下单金融方案分组总数
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanTotal(String ds, String sTime, String eTime,
                               Map<String, IndicatorOrderItemDO> collectMap) {
        String sql = " select b.mini_type " +
                "     , to_char(b.create_time, 'yyyy-mm-dd') day " +
                "     , a.rate_config_type " +
                "     , count(1)                             count " +
                " from rent_order_finance_detail a " +
                "         inner join rent_order b " +
                "                    on b.id = a.order_id " +
                " where b.is_deleted = 0 " +
                "  and b.merchant_id = 100 " +
                "  and b.parent_id = 0 " +
                "  and b.type = 1 " +
                "  and b.biz_type = 2 " +
                "  and b.create_time between '"+sTime+"' and '"+eTime+"' " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                " group by b.mini_type " +
                "       , to_char(b.create_time, 'yyyy-mm-dd') " +
                "       , a.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            initMap(collectMap, miniType, day);
            IndicatorOrderItemDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                updateDO.setPlanOneTotal(updateDO.getPlanOneTotal()+count);
            } else {
                updateDO.setPlanTwoTotal(updateDO.getPlanTwoTotal()+count);
            }
        }
    }


    /**
     * 下单金融方案分组且有增值服务
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanWithService(String ds, String sTime, String eTime,
                                     Map<String, IndicatorOrderItemDO> collectMap) {
        String sql = " select b.mini_type,to_char(b.create_time,'yyyy-mm-dd') day,a.rate_config_type,count(distinct " +
                " a.order_id) count " +
                " from rent_order_finance_detail a inner join rent_order b " +
                " on a.order_id = b.id " +
                " inner join ( " +
                "    select distinct order_id from ( " +
                "            select b.order_id from rent_order a  inner join  rent_order_item b " +
                "                on a.id=b.order_id " +
                "                inner join ( " +
                "                    select id " +
                "                        from rent_order  " +
                "                        where is_deleted = 0 " +
                "                        and merchant_id=100 " +
                "                        and parent_id = 0 " +
                "                        and type = 1 " +
                "                        and biz_type = 2 " +
                "                        and create_time between '"+sTime+"' and '"+eTime+"' " +
                "                        and ds = " + ds +
                "                ) c on a.id = c.id " +
                "                where a.ds = " + ds +
                "                and b.ds = " + ds +
                "                group by b.order_id having count(1)>1 " +
                "            union all  " +
                "            select a.order_id from rent_order_insurance a inner join rent_order b " +
                "                on a.order_id = b.id  " +
                "                inner join ( " +
                "                    select id id " +
                "                        from rent_order  " +
                "                        where is_deleted = 0 " +
                "                        and merchant_id=100 " +
                "                        and parent_id = 0 " +
                "                        and type = 1 " +
                "                        and biz_type = 2 " +
                "                        and create_time between '"+sTime+"' and '"+eTime+"' " +
                "                        and ds = " + ds +
                "                ) c on a.id = c.id " +
                "                where a.ds = " + ds +
                "                and b.ds = " + ds +
                "    ) " +
                " ) c on b.id = c.order_id " +
                " where  a.ds = " + ds +
                " and b.ds = " + ds +
                " group by b.mini_type,to_char(b.create_time,'yyyy-mm-dd'),a.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorOrderItemDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                updateDO.setPlanOneWithService(updateDO.getPlanOneWithService()+count);
            } else {
                updateDO.setPlanTwoWithService(updateDO.getPlanTwoWithService()+count);
            }
        }
    }


    /**
     * 下单是否免押分组
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanByFreeBond(String ds, String sTime, String eTime,
                                      Map<String, IndicatorOrderItemDO> collectMap) {
        String sql = " select a.mini_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , b.rate_config_type " +
                "     , b.bond_free_status " +
                "     , count(1)                             count " +
                "from rent_order a " +
                "         inner join rent_order_finance_detail b " +
                "                    on a.id = b.order_id " +
                "where a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '"+sTime+"' and '"+eTime+"' " +
                " group by a.mini_type " +
                "       , to_char(a.create_time, 'yyyy-mm-dd') " +
                "       , b.bond_free_status " +
                "       , b.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            initMap(collectMap, miniType, day);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer bondFreeStatus = Integer.valueOf(record.getString("bond_free_status"));
            IndicatorOrderItemDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                if(bondFreeStatus==0){
                    updateDO.setPlanOneFreeBond(updateDO.getPlanOneFreeBond()+count);
                }else{
                    updateDO.setPlanOneNotFreeBond(updateDO.getPlanOneNotFreeBond()+count);
                }
            } else {
                if(bondFreeStatus==0){
                    updateDO.setPlanTwoFreeBond(updateDO.getPlanTwoFreeBond()+count);
                }else{
                    updateDO.setPlanTwoNotFreeBond(updateDO.getPlanTwoNotFreeBond()+count);
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorOrderItemDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorOrderItemDO ioid = new IndicatorOrderItemDO();
            ioid.setMiniType(miniType);
            ioid.setCountDay(countDay);
            ioid.setPlanOneTotal(0);
            ioid.setPlanOneWithService(0);
            ioid.setPlanOneNotService(0);
            ioid.setPlanOneFreeBond(0);
            ioid.setPlanOneNotFreeBond(0);
            ioid.setPlanTwoTotal(0);
            ioid.setPlanTwoWithService(0);
            ioid.setPlanTwoNotService(0);
            ioid.setPlanTwoFreeBond(0);
            ioid.setPlanTwoNotFreeBond(0);
            miniType2Map.put(key, ioid);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}