package qnvip.data.overview.business.alarm;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.alarm.AlarmRecordDO;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.domain.order.OrderDO;
import qnvip.data.overview.domain.pobc.CreditQueriesInfoDO;
import qnvip.data.overview.domain.risk.RiskSituationDO;
import qnvip.data.overview.dto.AlarmIntervalConfigDTO;
import qnvip.data.overview.dto.BuyoutRateExceptionDTO;
import qnvip.data.overview.dto.ProfitsExceptionDTO;
import qnvip.data.overview.enums.AlarmConstantEnum;
import qnvip.data.overview.service.alarm.AlarmConfigService;
import qnvip.data.overview.service.alarm.AlarmRecordService;
import qnvip.data.overview.service.order.OperateRecoveriesService;
import qnvip.data.overview.service.order.OrderService;
import qnvip.data.overview.service.pobc.CreditQueriesInfoService;
import qnvip.data.overview.service.risk.RiskSituationService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.DingDingAlarmUtil;
import qnvip.rent.common.util.JsonUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2021/12/23
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ExceptionAlarmBusiness {


    private final OrderService orderService;
    private final RiskSituationService riskSituationService;
    private final CreditQueriesInfoService creditQueriesInfoService;
    private final AlarmConfigService alarmConfigService;
    private final OperateRecoveriesService operateRecoveriesService;
    private final AlarmRecordService alarmRecordService;
    private final ProfitsQueryBusiness profitsQueryBusiness;
    private final DingDingAlarmUtil dingDingAlarmUtil;


    public void hoursAlarmTask() {
        LocalDate now = LocalDate.now();
        LocalDateTime nowTime = LocalDateTime.now();
        String yyyyMMdd = now.format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        LocalDateTime startTime = LocalDateTime.of(now, LocalTime.MIN);
        LocalDateTime endTime = now.atTime(LocalTime.now());
        int hour = endTime.getHour();

        LocalDateTime ratePeriodStartTime = nowTime.withMinute(0).withSecond(0).minusHours(1);
        LocalDateTime ratePeriodEndTime = nowTime.withMinute(59).withSecond(59).minusHours(1);

        orderInfoAlarm(startTime, endTime, hour);
        bigDataInfoAlarm(startTime, endTime, hour);
        creditInfoAlarm(startTime, endTime, hour);
        //回款金额一天统计一次，小时告警没有意义
        //receivableAmountAlarm(startTime, endTime, hour);

        buyOutRateAlarm(yyyyMMdd, ratePeriodStartTime, ratePeriodEndTime);
        profitsRateAlarm(yyyyMMdd, ratePeriodStartTime, ratePeriodEndTime);
    }


    public void daysAlarmTask() {
        LocalDate now = LocalDate.now();
        LocalDate yesterday = now.minusDays(1);
        LocalDateTime startTime = LocalDateTime.of(yesterday, LocalTime.MIN);
        LocalDateTime endTime = LocalDateTime.of(yesterday, LocalTime.MAX);
        orderInfoAlarm(startTime, endTime, -1);
        bigDataInfoAlarm(startTime, endTime, -1);
        creditInfoAlarm(startTime, endTime, -1);
        receivableAmountAlarm(startTime, endTime, -1);
    }


    private void orderInfoAlarm(LocalDateTime startTime, LocalDateTime endTime, int hour) {
        List<OrderDO> list = orderService.getOrderListDataByDate(startTime, endTime, null);
        //下单人数
        int orderCount = 0;
        //支付人数
        int payCount = 0;
        if (CollUtil.isNotEmpty(list)) {
            for (OrderDO order : list) {
                if (order.getMiniType() == -5) {
                    orderCount += order.getOrderUvCount();
                    payCount += order.getPayCount();
                }
            }
        }
        compareAndAlarm(AlarmConstantEnum.ORDER_COUNT_ALARM, hour, new BigDecimal(String.valueOf(orderCount)));
        compareAndAlarm(AlarmConstantEnum.PAY_COUNT_ALARM, hour, new BigDecimal(String.valueOf(payCount)));
    }


    private void bigDataInfoAlarm(LocalDateTime startTime, LocalDateTime endTime, int hour) {
        List<RiskSituationDO> list = riskSituationService.getList(DateUtils.dateToString(startTime),
                DateUtils.dateToString(endTime), Collections.singletonList(-5));
        //基础通过人数
        int baseCount = 0;
        //大数据通过人数
        int passCount = 0;
        if (CollUtil.isNotEmpty(list)) {
            list.sort(Comparator.comparing(RiskSituationDO::getCreateTime).reversed());
            RiskSituationDO risk = list.get(0);
            baseCount += risk.getPassUser();
            passCount += risk.getBigDataPass();
        }
        compareAndAlarm(AlarmConstantEnum.BIG_DATA_BASE_PASS_ALARM, hour, new BigDecimal(String.valueOf(baseCount)));
        compareAndAlarm(AlarmConstantEnum.BIG_DATA_SUCCESS_ALARM, hour, new BigDecimal(String.valueOf(passCount)));
    }


    private void creditInfoAlarm(LocalDateTime startTime, LocalDateTime endTime, int hour) {
        List<CreditQueriesInfoDO> list = creditQueriesInfoService.getListByTime(startTime, endTime, null);
        //央行查询数量
        int creditSubmitCount = 0;
        if (CollUtil.isNotEmpty(list)) {
            for (CreditQueriesInfoDO credit : list) {
                creditSubmitCount += credit.getSubmitCount();
            }
        }
        compareAndAlarm(AlarmConstantEnum.BANK_SUBMIT_ALARM, hour, new BigDecimal(String.valueOf(creditSubmitCount)));
    }


    private void buyOutRateAlarm(String ds, LocalDateTime startTime, LocalDateTime endTime) {
        String alarmConfig = alarmConfigService.getStringByKey(AlarmConstantEnum.BUY_OUT_RATE_ALARM.getConstantKey());
        if (alarmConfig == null) {
            String errorMsg = "【" + AlarmConstantEnum.BUY_OUT_RATE_ALARM.getConstantDesc() + "】告警区间未配置，不比对数据";
            log.error(errorMsg);
            sendAlarmMsg(errorMsg);
            return;
        }
        AlarmIntervalConfigDTO alarmIntervalConfig = JSONUtil.toBean(alarmConfig, AlarmIntervalConfigDTO.class);
        List<BuyoutRateExceptionDTO> list = profitsQueryBusiness.queryAndCompareBuyoutRate(ds,
                DateUtils.dateToString(startTime), DateUtils.dateToString(endTime),
                alarmIntervalConfig.getStartValue(), alarmIntervalConfig.getEndValue());
        if (CollUtil.isNotEmpty(list)) {
            String alarmMsg = "以下订单买断金占比异常,正常值:【" + alarmIntervalConfig.getStartValue() + "," +
                    alarmIntervalConfig.getEndValue() + "】，异常信息:【" + JsonUtil.toJsonStr(list) + "】";
            log.error(alarmMsg);
         //   dingDingAlarmUtil.marketingExceptionAlarmExtendMall(alarmMsg);
        }
    }


    private void profitsRateAlarm(String ds, LocalDateTime startTime, LocalDateTime endTime) {
        String alarmConfig = alarmConfigService.getStringByKey(AlarmConstantEnum.PROFITS_ITEM_ALARM.getConstantKey());
        if (alarmConfig == null) {
            String errorMsg = "【" + AlarmConstantEnum.PROFITS_ITEM_ALARM.getConstantDesc() + "】告警区间未配置，不比对数据";
            log.error(errorMsg);
            sendAlarmMsg(errorMsg);
            return;
        }
        AlarmIntervalConfigDTO alarmIntervalConfig = JSONUtil.toBean(alarmConfig, AlarmIntervalConfigDTO.class);
        List<ProfitsExceptionDTO> list = profitsQueryBusiness.queryAndCompareProfitsRate(ds,
                DateUtils.dateToString(startTime), DateUtils.dateToString(endTime),
                alarmIntervalConfig.getStartValue(), alarmIntervalConfig.getEndValue());
        if (CollUtil.isNotEmpty(list)) {
            String alarmMsg = "以下订单毛利率异常,正常值:【" + alarmIntervalConfig.getStartValue() + "," +
                    alarmIntervalConfig.getEndValue() + "】，异常信息:【" + JsonUtil.toJsonStr(list) + "】";
            log.error(alarmMsg);
            dingDingAlarmUtil.marketingExceptionAlarmExtendMall(alarmMsg);
        }
    }


    private void receivableAmountAlarm(LocalDateTime startTime, LocalDateTime endTime, int hour) {
        List<OperateRecoveriesDO> list = operateRecoveriesService.getList(DateUtils.dateToString(startTime),
                DateUtils.dateToString(endTime), null);
        BigDecimal receivableAmount = BigDecimal.ZERO;
        if (CollUtil.isNotEmpty(list)) {
            for (OperateRecoveriesDO recoveriesDO : list) {
                receivableAmount = CalculateUtil.add(receivableAmount, recoveriesDO.getAlreadyAmt());
            }
        }
        compareAndAlarm(AlarmConstantEnum.RECEIVABLE_AMOUNT_ALARM, hour, receivableAmount);
    }


    private void compareAndAlarm(AlarmConstantEnum constantEnum, Integer presentHour, BigDecimal presentCount) {
        Map<Integer, AlarmIntervalConfigDTO> alarmConfigMap =
                alarmConfigService.getAlarmIntervalConfigByKey(constantEnum.getConstantKey());
        AlarmRecordDO alarmRecordDO = new AlarmRecordDO();
        alarmRecordDO.setConstKey(constantEnum.getConstantKey());
        alarmRecordDO.setActVal(presentCount);
        alarmRecordDO.setCountHour(presentHour);
        alarmRecordDO.setResult(1);
        String alarmDesc = constantEnum.getConstantDesc();
        if (alarmConfigMap == null) {
            alarmRecordDO.setNormalInterval("【未配置】");
            alarmRecordDO.setResult(0);
            alarmRecordService.save(alarmRecordDO);
            String errorMsg = "【" + alarmDesc + "】告警区间未配置，不比对数据";
            log.error(errorMsg);
            sendAlarmMsg(errorMsg);
            return;
        }
        String timeDesc = "今日";
        if (presentHour == -1) {
            timeDesc = "昨天全天";
        }
        AlarmIntervalConfigDTO alarmIntervalConfig = alarmConfigMap.get(presentHour);
        if (alarmIntervalConfig == null) {
            alarmRecordDO.setNormalInterval("【未配置】");
            alarmRecordDO.setResult(0);
            alarmRecordService.save(alarmRecordDO);
            String errorMsg = "【" + alarmDesc + "】" + timeDesc + "告警区间未配置，不比对数据";
            log.error(errorMsg);
            sendAlarmMsg(errorMsg);
            return;
        }
        boolean isNormal = true;
        String normalInterval =
                "【" + alarmIntervalConfig.getStartValue() + "," + alarmIntervalConfig.getEndValue() + "】";
        alarmRecordDO.setNormalInterval(normalInterval);
        if (presentCount.compareTo(alarmIntervalConfig.getStartValue()) < 0) {
            alarmRecordDO.setResult(2);
            isNormal = false;
        } else if (presentCount.compareTo(alarmIntervalConfig.getEndValue()) > 0) {
            alarmRecordDO.setResult(3);
            isNormal = false;
        }
        if (!isNormal) {
            String alarmMsg = "【" + timeDesc + alarmDesc + "】: " + presentCount + ",正常区间: " + normalInterval;
            log.error(alarmMsg);
            sendAlarmMsg(alarmMsg);
        }
        alarmRecordService.save(alarmRecordDO);
    }


    private void sendAlarmMsg(String alarmMsg) {
        dingDingAlarmUtil.dataViewExceptionAlarm(alarmMsg);
    }


}