package qnvip.data.overview.business.goods;

import cn.hutool.core.convert.Convert;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.goods.OperateGoodsCoreDO;
import qnvip.data.overview.service.goods.OperateGoodsCoreService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateGoodsCoreBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateGoodsCoreService goodsCoreService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        List<Map<String, Object>> list = Lists.newArrayList();
        getOnSaleSku(list);
        getShelvesSku(list);
        getUpdateSku(list);
        getQualityMoveOffSku(list);
        getMoveOffSku(list);
        getSoldOutSku(list);

        getOnSaleComponent(list);
        getShelvesComponent(list);
        getUpdateComponent(list);
        getQualityMoveOffComponent(list);
        getMoveOffComponent(list);
        getSoldOutComponent(list);
        List<CompletableFuture<List<Map<String, String>>>> futureList =
                list.stream().map(this::getData).collect(Collectors.toList());
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        List<Map<String, String>> totalList = Lists.newArrayList();
        futureList.forEach(future -> {
            try {
                totalList.addAll(future.get());
            } catch (Exception e) {
                log.error("OperateGoodsCoreBusiness runCore error,{}", e.getMessage());
            }
        });
        LinkedList<OperateGoodsCoreDO> dos = Lists.newLinkedList();
        OperateGoodsCoreDO domain = new OperateGoodsCoreDO();

        Map<String, String> merged = new HashMap<>();
        totalList.forEach(merged::putAll);
        String sku = Optional.ofNullable(merged.get("shelves_sku")).orElse("0");
        String newSku = Optional.ofNullable(merged.get("update_sku")).orElse("0");
        String onSaleSku = Optional.ofNullable(merged.get("on_sale_sku")).orElse("0");
        String soldOutSku = Optional.ofNullable(merged.get("sold_out_sku")).orElse("0");
        String moveOffSku = Optional.ofNullable(merged.get("move_off_sku")).orElse("0");
        String qualityMoveOffSku = Optional.ofNullable(merged.get("quality_move_off_sku")).orElse("0");
        String shelvesComponent = Optional.ofNullable(merged.get("shelves_component")).orElse("0");
        String updateComponent = Optional.ofNullable(merged.get("update_component")).orElse("0");
        String onSaleComponent = Optional.ofNullable(merged.get("on_sale_component")).orElse("0");
        String soldOutComponent = Optional.ofNullable(merged.get("sold_out_component")).orElse("0");
        String moveOffComponent = Optional.ofNullable(merged.get("move_off_component")).orElse("0");
        String qualityMoveOffComponent = Optional.ofNullable(merged.get("quality_move_off_component")).orElse("0");


        domain.setCountDay(countDay);
        domain.setShelvesSku(Long.valueOf(sku));
        domain.setUpdateSku(Long.valueOf(newSku));
        domain.setOnSaleSku(Long.valueOf(onSaleSku));
        domain.setSoldOutSku(Long.valueOf(soldOutSku));
        domain.setMoveOffSku(Long.valueOf(moveOffSku));
        domain.setQualityMoveOffSku(Long.valueOf(qualityMoveOffSku));
        domain.setShelvesComponent(Long.valueOf(shelvesComponent));
        domain.setUpdateComponent(Long.valueOf(updateComponent));
        domain.setOnSaleComponent(Long.valueOf(onSaleComponent));
        domain.setSoldOutComponent(Long.valueOf(soldOutComponent));
        domain.setMoveOffComponent(Long.valueOf(moveOffComponent));
        domain.setQualityMoveOffComponent(Long.valueOf(qualityMoveOffComponent));
        dos.add(domain);
        goodsCoreService.removeDataByTime(countDay);
        goodsCoreService.saveBatch(dos);
    }

    /**
     * 在售sku
     *
     * @param list
     */
    private void getOnSaleSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) on_sale_sku" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 5" +
                "  and ri.main_category = 2" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd')";
        list.add(packageParam(sql, "on_sale_sku"));
    }

    /**
     * 上架sku
     *
     * @param list
     */
    private void getShelvesSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) shelves_sku" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 5" +
                "  and ri.main_category = 2" +
                "  and ri.create_time between ${monthFromTime} and ${toTime}" +
                "  and ri.shelves_time between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd');";
        list.add(packageParam(sql, "shelves_sku"));
    }

    /**
     * 更新sku
     *
     * @param list
     */
    private void getUpdateSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) update_sku" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status in (5)" +
                "  and ri.main_category = 2" +
                "  and ri.create_time not between ${monthFromTime} and ${toTime}" +
                "  and ri.shelves_time between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd')";
        list.add(packageParam(sql, "update_sku"));
    }

    /**
     * 下架sku
     *
     * @param list
     */
    private void getSoldOutSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) sold_out_sku" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 6" +
                "  and ri.main_category = 2" +
                "  and ri.update_time between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd');";
        list.add(packageParam(sql, "sold_out_sku"));
    }

    /**
     * 动销sku
     *
     * @param list
     */
    private void getMoveOffSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct a.id) move_off_sku" +
                " from rent_item a" +
                "         inner join rent_order_item b on a.id = b.item_id" +
                "         inner join rent_order c on b.order_id = c.id" +
                " where a.status = 5" +
                "  and a.main_category = 2" +
                "  and c.parent_id =0" +
                "  and c.type =1" +
                "  and c.merchant_id =100" +
                "  and c.is_deleted = 0" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.create_time between ${monthFromTime} and ${toTime};";
        list.add(packageParam(sql, "move_off_sku"));
    }
    /**
     * 高质量动销sku
     *
     * @param list
     */
    private void getQualityMoveOffSku(List<Map<String, Object>> list) {
        String sql = "select count(distinct a.id) quality_move_off_sku" +
                " from rent_item a" +
                "         inner join rent_order_item b on a.id = b.item_id" +
                "         inner join rent_order c on b.order_id = c.id" +
                " where a.status = 5" +
                "  and a.main_category = 2" +
                "  and c.parent_id =0" +
                "  and c.type =1" +
                "  and c.merchant_id =100" +
                "  and c.is_deleted = 0" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.create_time between ${monthFromTime} and ${toTime}" +
                " having count(distinct c.id) > 5;";
        list.add(packageParam(sql, "quality_move_off_sku"));
    }
    /**
     * 在售配件
     *
     * @param list
     */
    private void getOnSaleComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) on_sale_component" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 5" +
                "  and ri.main_category =11 " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd')";
        list.add(packageParam(sql, "on_sale_component"));
    }

    /**
     * 上架配件
     *
     * @param list
     */
    private void getShelvesComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) shelves_component" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 5" +
                "  and ri.main_category =11 " +
                "  and ri.create_time between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd');";
        list.add(packageParam(sql, "shelves_component"));
    }

    /**
     * 更新配件
     *
     * @param list
     */
    private void getUpdateComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) update_component" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status in (5)" +
                "  and ri.main_category =11 " +
                "  and ri.create_time not between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd')";
        list.add(packageParam(sql, "update_component"));
    }

    /**
     * 下架配件
     *
     * @param list
     */
    private void getSoldOutComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct ri.id) sold_out_component" +
                " from rent_item ri" +
                " where ri.is_deleted = 0" +
                "  and ri.status = 6" +
                "  and ri.main_category =11 " +
                "  and ri.update_time between ${monthFromTime} and ${toTime}" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd');";
        list.add(packageParam(sql, "sold_out_component"));
    }

    /**
     * 动销配件
     *
     * @param list
     */
    private void getMoveOffComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct a.id) move_off_component" +
                " from rent_item a" +
                "         inner join rent_order_item b on a.id = b.item_id" +
                "         inner join rent_order c on b.order_id = c.id" +
                " where a.status = 5" +
                "  and a.main_category =11 " +
                "  and c.parent_id =0" +
                "  and c.type =1" +
                "  and c.merchant_id =100" +
                "  and c.is_deleted = 0" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.create_time between ${monthFromTime} and ${toTime};";
        list.add(packageParam(sql, "move_off_component"));
    }
    /**
     * 高质量动销配件
     *
     * @param list
     */
    private void getQualityMoveOffComponent(List<Map<String, Object>> list) {
        String sql = "select count(distinct a.id) quality_move_off_component" +
                " from rent_item a" +
                "         inner join rent_order_item b on a.id = b.item_id" +
                "         inner join rent_order c on b.order_id = c.id" +
                " where a.status = 5" +
                "  and a.main_category =11 " +
                "  and c.parent_id =0" +
                "  and c.type =1" +
                "  and c.merchant_id =100" +
                "  and c.is_deleted = 0" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.create_time between ${monthFromTime} and ${toTime}" +
                " having count(distinct c.id) > 5;";
        list.add(packageParam(sql, "quality_move_off_component"));
    }

    private CompletableFuture<List<Map<String, String>>> getData(Map<String, Object> param2condition) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);
            List<Record> records = odpsUtil.querySql(format.concat(";"));
            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            return records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = record.getString(column);
                    column2value.put(column, value);
                }
                return column2value;
            }).collect(Collectors.toList());
        });
    }

    private Map<String, Object> packageParam(String sql, String... columns) {
        HashMap<String, Object> param2condition = Maps.newHashMap();
        param2condition.put("sql", sql);
        param2condition.put("column", columns);
        return param2condition;
    }
}
