package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.*;
import qnvip.data.overview.service.order.MarginService;
import qnvip.data.overview.service.order.OrderService;
import qnvip.data.overview.service.order.OverdueService;
import qnvip.data.overview.service.order.TerminationReasonService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/9/29 10:55 上午
 */
@Component
@RequiredArgsConstructor
public class OrderBusiness {

    private final OdpsUtil odpsUtil;
    private final OrderService orderService;
    private final OverdueService overdueService;
    private final TerminationReasonService terminationReasonService;
    private final MarginService marginService;


    /**
     * 启动订单数据计算
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void runOrderCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        Map<Integer, OrderDO> map = new HashMap<>();
        List<OrderDO> list = rentGMVAndOrderCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setRentTotalGmv(orderDO.getRentTotalGmv());
            updateDO.setOrderCount(orderDO.getOrderCount());
        }
        list = rentOrderUvCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setOrderUvCount(orderDO.getOrderUvCount());
        }
        list = signOrderCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setSignCount(orderDO.getSignCount());
        }
        list = rentPayCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setPayCount(orderDO.getPayCount());
        }
        list = orderMarginTotal(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setMarginTotal(orderDO.getMarginTotal());
        }
        list = orderInsuranceAmt(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setInsuranceAmt(orderDO.getInsuranceAmt());
        }
        list = orderComponentPrice(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setComponentPrice(orderDO.getComponentPrice());
        }
        list = deliveryCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setDeliveryCount(orderDO.getDeliveryCount());
        }
        list = terminationCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setTerminationCount(orderDO.getTerminationCount());
        }
        list = auditApprovalCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setRiskPassCount(orderDO.getRiskPassCount());
            updateDO.setRiskPassOrderCount(orderDO.getRiskPassOrderCount());
        }
        list = auditApprovalTotalCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setRiskPassCount(orderDO.getRiskPassCount());
            updateDO.setRiskPassOrderCount(orderDO.getRiskPassOrderCount());
        }
        list = deliveryTotalCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setDeliveryCount(orderDO.getDeliveryCount());
        }
        list = rentOrderUvTotalCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setOrderUvCount(orderDO.getOrderUvCount());
        }
        list = rentPayTotalCount(dsStr, sTime, eTime);
        for (OrderDO orderDO : list) {
            initMap(map, orderDO, countDay);
            OrderDO updateDO = map.get(orderDO.getMiniType());
            updateDO.setPayCount(orderDO.getPayCount());
        }
        // 写入mysql
        for (Map.Entry<Integer, OrderDO> entry : map.entrySet()) {
            OrderDO orderDO = entry.getValue();
            orderService.saveOrUpdate(orderDO);
        }
    }


    void initMap(Map<Integer, OrderDO> map, OrderDO orderDO, LocalDateTime countDay) {
        if (!map.containsKey(orderDO.getMiniType())) {
            OrderDO ac = new OrderDO();
            ac.setMiniType(orderDO.getMiniType());
            ac.setCountDay(countDay);
            ac.setRentTotalGmv(BigDecimal.ZERO);
            ac.setOrderCount(0l);
            ac.setPayCount(0l);
            ac.setSignCount(0l);
            ac.setMarginTotal(BigDecimal.ZERO);
            ac.setInsuranceAmt(BigDecimal.ZERO);
            ac.setComponentPrice(BigDecimal.ZERO);
            ac.setDeliveryCount(0l);
            ac.setTerminationCount(0l);
            ac.setRiskPassCount(0l);
            ac.setRiskPassOrderCount(0l);
            ac.setSignCount(0l);
            map.put(orderDO.getMiniType(), ac);
        }
    }

    /**
     * 启动逾期订单数据计算
     *
     * @return
     */
    public void runOverdueCount(String dsStr, LocalDateTime countDay) {
        Map<Integer, OverdueDO> map = new HashMap<>();
        List<OverdueDO> list = currentOverdueCount(dsStr);
        for (OverdueDO overdueDO : list) {
            if (!map.containsKey(overdueDO.getMiniType())) {
                OverdueDO ac = new OverdueDO();
                ac.setCountDay(countDay);
                ac.setMiniType(overdueDO.getMiniType());
                ac.setCurrentOverdue(0l);
                ac.setMaxOverdue(0l);
                map.put(overdueDO.getMiniType(), ac);
            }
            OverdueDO updateDO = map.get(overdueDO.getMiniType());
            updateDO.setCurrentOverdue(overdueDO.getCurrentOverdue());
        }
        list = maxOverdueCount(dsStr);
        for (OverdueDO overdueDO : list) {
            if (!map.containsKey(overdueDO.getMiniType())) {
                OverdueDO ac = new OverdueDO();
                ac.setCountDay(countDay);
                ac.setMiniType(overdueDO.getMiniType());
                ac.setCurrentOverdue(0l);
                ac.setMaxOverdue(0l);
                map.put(overdueDO.getMiniType(), ac);
            }
            OverdueDO updateDO = map.get(overdueDO.getMiniType());
            updateDO.setMaxOverdue(overdueDO.getMaxOverdue());
        }
        // 写入mysql
        for (Map.Entry<Integer, OverdueDO> entry : map.entrySet()) {
            OverdueDO overdueDO = entry.getValue();
            overdueService.saveOrUpdate(overdueDO);
        }
    }

    /**
     * 启动订单关闭原因计算
     *
     * @return
     */
    public void runTerminationReasonCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        List<TerminationReasonDO> list = terminationReason(dsStr, countDay, sTime, eTime);
        // 写入mysql
        for (TerminationReasonDO reasonDO : list) {
            terminationReasonService.saveOrUpdate(reasonDO);
        }
    }


    /**
     * 启动保证金区间分析统计
     *
     * @param countDay
     * @param sTime
     * @param eTime
     */
    public void runMarginCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        Map<Integer, MarginDO> map = new HashMap<>();
        List<MarginDO> marginDOS = marginOrderCount(dsStr, countDay, sTime, eTime);
        for (MarginDO marginDO : marginDOS) {
            initMargiMap(map, marginDO, countDay);
            MarginDO updateDO = map.get(marginDO.getLevel());
            updateDO.setOrderCount(marginDO.getOrderCount());
        }
        marginDOS = marginPayCount(dsStr, countDay, sTime, eTime);
        for (MarginDO marginDO : marginDOS) {
            initMargiMap(map, marginDO, countDay);
            MarginDO updateDO = map.get(marginDO.getLevel());
            updateDO.setPayCount(marginDO.getPayCount());
        }
        // 写入mysql
        for (Map.Entry<Integer, MarginDO> entry : map.entrySet()) {
            MarginDO marginDO = entry.getValue();
            marginService.saveOrUpdate(marginDO);
        }
    }


    void initMargiMap(Map<Integer, MarginDO> map, MarginDO marginDO, LocalDateTime countDay) {
        if (!map.containsKey(marginDO.getLevel())) {
            MarginDO ac = new MarginDO();
            ac.setLevel(marginDO.getLevel());
            ac.setCountDay(countDay);
            ac.setOrderCount(0l);
            ac.setPayCount(0l);
            map.put(marginDO.getLevel(), ac);
        }
    }


    /**
     * 计算订单总租金GMV、订单量
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> rentGMVAndOrderCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,NVL(sum(b.rent_total),0) as renttotal," +
                " count(1) as num from rent_order a inner join rent_order_finance_detail b on a.id = b.order_id where " +
                " a.biz_type = 2 and a.type = 1 and a.is_deleted = 0 and a.merchant_id = 100 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setRentTotalGmv(new BigDecimal(record.getString("renttotal")));
            orderDO.setOrderCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }
        return list;
    }


    /**
     * 计算订单下单人数
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> rentOrderUvCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder("select  b.minitype as minitype" +
                "        ,count(distinct b.customer_id) uv" +
                " from    (" +
                "            select  a.mini_type as minitype" +
                "                    ,a.customer_id" +
                "            from    rent_order a inner" +
                "            join    rent_order_finance_detail b" +
                "            on      a.id = b.order_id" +
                "            where   a.type=1" +
                "            and     a.biz_type = 2 " +
                "            and     a.merchant_id = 100" +
                "            and     a.is_deleted = 0" +
                "            and     a.ds = to_char(getdate(), 'yyyymmdd')" +
                "            and     b.ds = to_char(getdate(), 'yyyymmdd')");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type,a.customer_id ) b group by b.minitype;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setOrderUvCount(Long.parseLong(record.getString("uv")));
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 计算订单下单人数 总和
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> rentOrderUvTotalCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(
                "            select  count(distinct a.customer_id) uv" +
                        "            from    rent_order a inner" +
                        "            join    rent_order_audit b" +
                        "            on      a.id = b.order_id" +
                        "            where   a.type=1" +
                        "            and     a.biz_type = 2 " +
                        "            and     a.merchant_id = 100" +
                        "            and     a.is_deleted = 0" +
                        "            and     a.parent_id = 0" +
                        "            and     b.id is not null" +
                        "            and     a.ds = to_char(getdate(), 'yyyymmdd')" +
                        "            and     b.ds = to_char(getdate(), 'yyyymmdd')");
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        List<Record> recordList = odpsUtil.querySql(sb.toString().concat(";"));
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(-5);
            orderDO.setOrderUvCount(Long.parseLong(record.getString("uv")));
            list.add(orderDO);
        }
        return list;
    }


    /**
     * 成功签收数量
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> signOrderCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from  rent_order a " +
                " inner join rent_order_item b on a.id =b.order_id and b.item_type = 1 inner join " +
                " rent_order_logistics c on b.logistics_id = c.id where a.biz_type = 2 and a.type = 1 and " +
                " a.merchant_id = 100 and a.payment_time is not null and c.sign_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and c.sign_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and c.sign_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setSignCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }
        return list;
    }


    /**
     * 支付订单量（待发货）
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> rentPayCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from rent_order a where" +
                " a.merchant_id = 100 and a.biz_type = 2 and a.type = 1 " +
                " and a.is_deleted = 0 and status in (1, 5, 15, 60) ");
        sb.append(" and a.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setPayCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 支付订单量（待发货）总和
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> rentPayTotalCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder("select count(distinct customer_id) uv" +
                " from rent_order" +
                " where merchant_id = 100" +
                "  and biz_type = 2 " +
                "  and type = 1 " +
                "  and is_deleted = 0" +
                "  and parent_id = 0" +
                "  and payment_time IS NOT NULL" +
                "  and termination != 5" +
                "  and is_deleted = 0");
        sb.append(" and ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and payment_time <= ").append("'").append(eTime).append("'");
        }
        List<Record> recordList = odpsUtil.querySql(sb.toString().concat(";"));
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setPayCount(Long.parseLong(record.getString("uv")));
            orderDO.setMiniType(-5);
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 保证金支付金额
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> orderMarginTotal(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,NVL(sum(b.act_bond_amt),0) as bondamt " +
                " from rent_order a inner join rent_order_finance_detail b on a.id = b.order_id inner join " +
                " rent_order_item c on b.order_id = c.order_id and c.item_type = 1 " +
                " where a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.payment_time is not " +
                " null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setMarginTotal(new BigDecimal(record.getString("bondamt")));
            list.add(orderDO);
        }

        return list;
    }

    /**
     * 碎屏险支付金额
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> orderInsuranceAmt(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,NVL(sum(b.insurance_amt),0) insuranceamt " +
                " from rent_order a inner join rent_order_insurance b on a.id = b.order_id where a.biz_type = 2" +
                " and a.type = 1 and a.merchant_id = 100 and a.payment_time is not null and a.is_screen_risk_payed = 1" +
                " and b.giving_flag = 0 and a.is_deleted = 0 and b.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setInsuranceAmt(new BigDecimal(record.getString("insuranceamt")));
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 配件支付金额
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> orderComponentPrice(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,NVL(sum(d.operating_purchase_price),0) " +
                " as componentprice,count(1) as num from rent_order a inner join rent_order_item d on a.id = d.order_id" +
                " and d.item_type=10 where a.biz_type = 2 and a.type = 1 and" +
                " a.merchant_id = 100 and a.payment_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and d.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setComponentPrice(new BigDecimal(record.getString("componentprice")));
            list.add(orderDO);
        }
        return list;
    }


    /**
     * 发货数量
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> deliveryCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from  rent_order a " +
                "inner join rent_order_item" +
                " b on a.id =b.order_id and b.item_type = 1 inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.biz_type = 2 and a.type = 1 and " +
                " a.merchant_id = 100 and a.payment_time is not null and c.send_time is not null and a.is_deleted = 0 " +
                " ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and c.send_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and c.send_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setDeliveryCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }

        return list;
    }

    /**
     * 发货数量总和
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> deliveryTotalCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder("select count(distinct a.no) as num" +
                " from rent_order a" +
                "        inner join rent_order_logistics c on a.id = c.order_id" +
                " where a.merchant_id = 100" +
                "  and a.biz_type = 2 and a.type = 1" +
                "  and a.is_deleted = 0");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and c.send_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and c.send_time <= ").append("'").append(eTime).append("'");
        }
        List<Record> recordList = odpsUtil.querySql(sb.toString().concat(";"));
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(-5);
                orderDO.setDeliveryCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }

        return list;
    }

    /**
     * 订单关闭数量
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OrderDO> terminationCount(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder("select a.mini_type as minitype,count(1) as num from rent_order a inner " +
                " join rent_order_infomore b on a.id=b.order_id where a.biz_type = 2 and a.type = 1 and " +
                " a.merchant_id = 100 and a.termination = 5 and  b.termination_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and b.termination_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and b.termination_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by a.mini_type;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setTerminationCount(Long.parseLong(record.getString("num")));
            list.add(orderDO);
        }

        return list;
    }


    /**
     * 风控通过人数（待付款人数）
     *
     * @return
     */
    public List<OrderDO> auditApprovalCount(String dsStr, String sTime, String eTime) {
        String sql = "select  a.minitype" +
                "        ,count(a.customer_id) uv" +
                " from    (" +
                "            select  ro.customer_id" +
                "                    ,ro.mini_type as minitype" +
                "            from    rent_order ro" +
                "            left join rent_order_audit roa" +
                "            on      ro.id = roa.order_id" +
                "            where   ro.biz_type = 2" +
                "            and     ro.type = 1 " +
                "            and     ro.merchant_id = 100" +
                "            and     ro.parent_id = 0" +
                "            and     roa.type = 2" +
                "            and     ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "            and     roa.ds = to_char(getdate(), 'yyyymmdd')" +
                "            and     roa.audit_status = 1" +
                "            and     ro.is_deleted = 0" +
                "            and     roa.operate_time between ${fromTime} and ${toTime}" +
                "            group by ro.customer_id" +
                "                     ,ro.mini_type" +
                "        ) a" +
                " group by a.minitype;";

        HashMap<String, Object> key2value = new HashMap<>();
        key2value.put("fromTime", "'".concat(sTime).concat("'"));
        key2value.put("toTime", "'".concat(eTime).concat("'"));
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> recordList = odpsUtil.querySql(format.concat(";"));
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            orderDO.setRiskPassCount(Long.parseLong(record.getString("uv")));
            orderDO.setRiskPassOrderCount(Long.parseLong(record.getString("uv")));
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 风控通过人数（待付款人数）总数
     *
     * @return
     */
    public List<OrderDO> auditApprovalTotalCount(String dsStr, String sTime, String eTime) {
        String sql = "select count(distinct ro.customer_id) uv" +
                " from rent_order ro" +
                "         left join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.merchant_id = 100" +
                "  and ro.biz_type = 2 and ro.type = 1 " +
                "  and roa.type = 2" +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and roa.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and roa.audit_status = 1" +
                "  and ro.is_deleted = 0" +
                "  and roa.operate_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = new HashMap<>();
        key2value.put("fromTime", "'".concat(sTime).concat("'"));
        key2value.put("toTime", "'".concat(eTime).concat("'"));
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> recordList = odpsUtil.querySql(format.concat(";"));
        List<OrderDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OrderDO orderDO = new OrderDO();
            orderDO.setRiskPassCount(Long.parseLong(record.getString("uv")));
            orderDO.setRiskPassOrderCount(Long.parseLong(record.getString("uv")));
            orderDO.setMiniType(-5);
            list.add(orderDO);
        }
        return list;
    }

    /**
     * 当前逾期订单数
     *
     * @return
     */
    public List<OverdueDO> currentOverdueCount(String dsStr) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from rent_order a " +
                " left join rent_order_overdue_stat b on a.id = b.order_id " +
                " where a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and b.current_overdue_days > 0 " +
                " and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" group by a.mini_type; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OverdueDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OverdueDO overdueDO = new OverdueDO();
            overdueDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            overdueDO.setCurrentOverdue(Long.parseLong(record.getString("num")));
            list.add(overdueDO);
        }
        return list;
    }


    /**
     * 累计逾期订单数
     *
     * @return
     */
    public List<OverdueDO> maxOverdueCount(String dsStr) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from rent_order a left " +
                " join rent_order_overdue_stat b on a.id = b.order_id where a.biz_type = 2 and a.type = 1 " +
                " and a.merchant_id = 100 and b.max_history_overdue_days > 0 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" group by a.mini_type; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<OverdueDO> list = new ArrayList<>();
        for (Record record : recordList) {
            OverdueDO overdueDO = new OverdueDO();
            overdueDO.setMiniType(Integer.parseInt(record.getString("minitype")));
            overdueDO.setMaxOverdue(Long.parseLong(record.getString("num")));
            list.add(overdueDO);
        }
        return list;
    }


    /**
     * 订单关闭原因
     *
     * @return
     */
    public List<TerminationReasonDO> terminationReason(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select get_json_object(b.operate_note,'$.reason') as reason,count(1) as " +
                "  num from rent_order a left join rent_order_audit b on a.id = b.order_id " +
                "  where a.biz_type = 2 and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.termination = 5 " +
                "  and b.type = 1 " +
                "  and b.audit_status = 1 " +
                "  and get_json_object(b.operate_note,'$.reason') is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and b.operate_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append("  and b.operate_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(" group by get_json_object(b.operate_note,'$.reason');  ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<TerminationReasonDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            TerminationReasonDO reasonDO = new TerminationReasonDO();
            reasonDO.setTerminationDay(countDay);
            reasonDO.setReason(record.getString("reason"));
            reasonDO.setTotalCount(Long.parseLong(record.getString("num")));
            list.add(reasonDO);
        }
        return list;
    }


    /**
     * 保证金支付区间分析(支付量)
     *
     * @return
     */
    public List<MarginDO> marginPayCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select c.level as level,count(1) as num from (  " +
                "                select (case  " +
                "      when b.bond_amt < 1000 THEN 1  " +
                "      when b.bond_amt < 1500 THEN 2  " +
                "      when b.bond_amt < 2300 THEN 3  " +
                "      when b.bond_amt < 3000 THEN 4  " +
                "      when b.bond_amt < 3500 THEN 5  " +
                "      ELSE 6 end) as level  " +
                "      from rent_order a inner join rent_order_finance_detail b on a.id = b.order_id  " +
                "      where a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.payment_time is not null " +
                "       and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append("  and a.payment_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(" ) c group by c.level;  ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<MarginDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            MarginDO marginDO = new MarginDO();
            marginDO.setCountDay(countDay);
            marginDO.setLevel(Integer.parseInt(record.getString("level")));
            marginDO.setPayCount(Long.parseLong(record.getString("num")));
            list.add(marginDO);
        }
        return list;
    }

    /**
     * 保证金支付区间分析(下单数)
     *
     * @return
     */
    public List<MarginDO> marginOrderCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select c.level as level,count(1) as num from (  " +
                "                select (case  " +
                "      when b.bond_amt < 1000 THEN 1  " +
                "      when b.bond_amt < 1500 THEN 2  " +
                "      when b.bond_amt < 2300 THEN 3  " +
                "      when b.bond_amt < 3000 THEN 4  " +
                "      when b.bond_amt < 3500 THEN 5  " +
                "      ELSE 6 end) as level  " +
                "      from rent_order a inner join rent_order_finance_detail b on a.id = b.order_id  " +
                "      where a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append("  and a.create_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(" ) c group by c.level;  ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<MarginDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            MarginDO marginDO = new MarginDO();
            marginDO.setCountDay(countDay);
            marginDO.setLevel(Integer.parseInt(record.getString("level")));
            marginDO.setOrderCount(Long.parseLong(record.getString("num")));
            list.add(marginDO);
        }
        return list;
    }

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder(" select a.mini_type as minitype,count(1) as num from  rent_order a " +
                "inner join rent_order_item" +
                " b on a.id =b.order_id and b.item_type = 1 inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.payment_time is not null " +
                " and c.send_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(2);
        sb.append(" and b.ds = ").append(2);
        sb.append(" and c.ds = ").append(2);
        sb.append(" and c.send_time >= ").append("'").append(2).append("'");
        sb.append(" and c.send_time <= ").append("'").append(2).append("'");
        sb.append(" group by a.mini_type;");
        System.out.println(sb.toString());
    }

}