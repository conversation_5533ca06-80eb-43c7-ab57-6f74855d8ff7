package qnvip.data.overview.business.order;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.ContractSigningDO;
import qnvip.data.overview.service.order.ContractSigningService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class ContractSigningBusiness {

    private final OdpsUtil odpsUtil;

    private final ContractSigningService contractSigningService;


    /**
     * 获取订单租赁合同签订成功数
     */
    public void getContract() {
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String sql = "select order_id, type" +
                " from rent_order_agreement" +
                " where type in (200, 207)" +
                "  and capital_id = 0" +
                "  and is_deleted = 0" +
                "  and create_time <= ${fromTime}" +
                "  and create_time >= ${toTime}" +
                " and ds = to_char(getdate(), 'yyyymmdd') " +
                " group by order_id";
        String sql2 = SqlUtils.processTemplate(sql, key2value);
        List<Record> alipayList = odpsUtil.querySql(sql2.concat(";"));

        if (CollUtil.isEmpty(alipayList)) {
            return;
        }
        alipayList.forEach(record -> {
            Long orderId = record.getBigint("order_id");
            String type = record.getString("type");
            ContractSigningDO contractSigningDO = new ContractSigningDO();
            contractSigningDO.setCountDay(countDay);
            contractSigningDO.setOrderId(orderId);
            contractSigningDO.setType(Integer.parseInt(type));
            contractSigningService.saveOrUpdate(contractSigningDO);
        });

    }


}
