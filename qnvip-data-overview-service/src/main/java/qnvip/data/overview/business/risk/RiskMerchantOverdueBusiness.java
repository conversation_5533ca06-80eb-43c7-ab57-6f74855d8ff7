package qnvip.data.overview.business.risk;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RepaymentPlanDO;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.domain.risk.RiskMerchantOrderOverdueDO;
import qnvip.data.overview.domain.risk.ShRecordData;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskMerchantOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 风控大盘-vintage-分期
 * create by gw on 2022/3/24
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskMerchantOverdueBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final Integer PAGE_SIZE = 10000;
    private final Integer PAGE_SIZE_MERCHANT = 5000;
    private final int[] OVERDUE_DAY = {3};

    private final OdpsUtil odpsUtil;
    private final RiskMerchantOrderOverdueService riskStageOrderOverdueService;

    private final RentOrderService rentOrderService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskMerchantOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    private ThreadPoolExecutor threadPoolExecutorTidb = new ThreadPoolExecutor(1, 2, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskMerchantOverdueBusinessTiDB-" + atomicInteger.incrementAndGet());
        return t;
    });

    /**
     * @param ds
     */
    public void execOldData(String ds) {
        // order总记录数
        riskStageOrderOverdueService.deleteAll();
        Integer size = getCount(ds);
        for (int overdueDay : OVERDUE_DAY) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutor.execute(() -> {
                    oldRepayTask(ds, p, overdueDay);
                });
                startPage++;
            }
        }
    }

    /**
     * tidb版本
     * @param ds
     */
    public void execOldDataTidb(String ds) {
        // order总记录数
        riskStageOrderOverdueService.deleteAll();
        Integer size = rentOrderService.getShCountTidb();
        for (int overdueDay : OVERDUE_DAY) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutorTidb.execute(() -> {
                    oldRepayTaskTidb(ds, p, overdueDay);
                });
                startPage++;
            }
        }
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String ds) {
        String sql = "select count(1) num" +
                " from sh_order so " +
                "          left JOIN sh_order_logistics sol ON sol.order_uid = so.order_uid and confirm_sign = 20 " +
                " where so.ds =" + ds +
                "   AND sol.ds =" + ds +
                "                       AND so.is_deleted=0\n" +
                "                       and so.closing_time is null" +
                ";";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }


    private void oldRepayTask(String ds, int startPage, Integer overdueDay) {
        String sql = "with dt as (\n" +
                "    select date_format(now() , 'yyyymmdd') as ds\n" +
                "),\n" +
                "     new_dt as (\n" +
                "         select date_format(now() - interval '1' day, 'yyyymmdd') as ds\n" +
                "     )\n" +
                "\n" +
                "SELECT date(rent_start_time)                                                                           rent_start_date,\n" +
                "       so.order_uid                                                                                    no,\n" +
                "       so.id                                                                                           order_id,\n" +
                "       cl.riskOpinion                                                                                  risk_level,\n" +
                "       if(cl.scene ='','未标记上场景值',cl.scene)                                                        scene, " +
                "       if(cl.quotientname ='','未标记上导流商',cl.quotientname)                                          quotient_name,"+
                "       sn.riskStrategy                                                                                 risk_strategy,\n" +
                "       artificialAuditorId                                                                             audit_type,\n" +
                "        nvl(s_c.name, '')                                                                             first_category,\n" +
                "        nvl(s_c_new.name, '')                                                                         second_category,\n" +
                "       if(gg.paytime is null or date(so.create_time) <= gg.paytime, 0, 1)                              customer_type,\n" +
                "       (CASE\n" +
                "            WHEN rmc.platform_code = 'ALIPAY' THEN '支付宝'\n" +
                "            WHEN rmc.platform_code = 'WECHAT' THEN '微信'\n" +
                "            WHEN rmc.platform_code = 'BYTEDANCE' THEN '字节跳动'\n" +
                "            WHEN rmc.platform_code = 'APP' THEN 'APP'\n" +
                "            WHEN rmc.platform_code = 'KUAISHOU' THEN '快手'\n" +
                "            else so.mini_type END)                                                                     platform,\n" +
                "       (case\n" +
                "            when add_months(date(normal_repay_date) - day(normal_repay_date) + 2, 1) > date(getdate()) THEN (case\n" +
                "                                                                                                                 when year(normal_repay_date) > year(getdate())\n" +
                "                                                                                                                     then '0'\n" +
                "                                                                                                                 when year(normal_repay_date) =\n" +
                "                                                                                                                      year(getdate()) and\n" +
                "                                                                                                                      month(normal_repay_date) >\n" +
                "                                                                                                                      month(getdate())\n" +
                "                                                                                                                     then '0'\n" +
                "                                                                                                                 when period_no = 1\n" +
                "                                                                                                                     then if(\n" +
                "                                                                                                                             date(normal_repay_date) >=\n" +
                "                                                                                                                             date(getdate()),\n" +
                "                                                                                                                             '0',\n" +
                "                                                                                                                             if(srp.status <> 10 and overdue_day_count > 0, '1', '0'))\n" +
                "                                                                                                                 else IF(\n" +
                "                                                                                                                                 srp.status <>\n" +
                "                                                                                                                                 10 and\n" +
                "                                                                                                                                 overdue_day_count >\n" +
                "                                                                                                                                 0,\n" +
                "                                                                                                                                 '1',\n" +
                "                                                                                                                                 if(srp.status = 10, '0', '2')) end)\n" +
                "            else (case\n" +
                "                      when date(NVL(real_repay_date, getdate())) <=\n" +
                "                           add_months(date(normal_repay_date) - day(normal_repay_date) + 2, 1)\n" +
                "                          then if(srp.status = 10, '0', '1')\n" +
                "                      else IF(datediff(date(NVL(real_repay_date, getdate())),\n" +
                "                                       date(add_months(date(normal_repay_date) - day(normal_repay_date) + 2, 1)),\n" +
                "                                       'dd') >  " + overdueDay + ", '1',\n" +
                "                              IF(real_repay_date is not null and srp.status = 10, '0', '1')) end) end) is_overdue,\n" +
                "       total_rent_amount                                                                               rent_total,\n" +
                "       real_repay_date                                                                                 real_repay_time,\n" +
                "       date(normal_repay_date)                                                                         repay_date,\n" +
                "       period_no                                                                                       term,\n" +
                "       payable_amount                                                                                  capital,\n" +
                "       real_repay_amount                                                                               real_capital,\n" +
                "       srp.status                                                                                      repay_status,\n" +
                "       so.mini_type,\n" +
                "       repayment_period_count                                                                          max_term,\n" +
                "       overdue_retention_fine                                                                          overdue_fine,\n" +
                "       sof.coupon_discount_money + reduce_overdue_fine                                                 discount_amt,\n" +
                "       total_cash_deposit                                                                              bond_amt,\n" +
                "       nvl(aa.hitValue, '')                                                                            hit_value,\n" +
                "       srp.is_deleted,\n" +
                "       overdue_day_count                                                                               overdue\n" +
                "FROM sh_order so\n" +
                "         left join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted = 0\n" +
                "         left join sh_repayment_plan srp on so.order_uid = srp.order_uid and srp.is_deleted = 0\n" +
                "         left join sh_customer sc on so.customer_id = sc.id and sc.is_deleted = 0\n" +
                "         left JOIN cl_loan cl ON cl.loanno = so.order_uid \n" +
                "         left JOIN serial_no sn ON sn.businessno = so.order_uid\n" +
                "         left join sh_mini_config rmc on so.mini_type = rmc.mini_type and rmc.is_deleted = 0\n" +
                "         inner join sh_order_item soi\n" +
                "                on so.order_uid = soi.order_uid and soi.is_deleted = 0 and soi.ds = (select ds from dt)\n" +
                "         left join sh_item si\n" +
                "                on si.item_uid = soi.item_uid  and si.ds = (select ds from new_dt) and si.is_deleted = 0\n" +
                "         left join sh_category s_c\n" +
                "                   on si.first_category = s_c.id  and s_c.ds = (select ds from new_dt) and s_c.is_deleted = 0\n" +
                "         left join sh_category s_c_new\n" +
                "                   on si.second_category = s_c_new.id  and s_c_new.ds = (select ds from new_dt)  and s_c_new.is_deleted = 0\n" +
                "         left join (SELECT rc.hitValue, sn.businessNo\n" +
                "                    FROM serial_no sn\n" +
                "                             INNER JOIN rc_risk_access_rule_result_2023 rc on sn.id = rc.serialnoId\n" +
                "                             INNER JOIN rc_risk_strategy_rule_set rr on rr.id = rc.ruleId\n" +
                "                    WHERE rr.scene = 3\n" +
                "                      and rc.hitValue != ''\n" +
                "                      and rr.masterModel = 1\n" +
                "                      and rc.ds = (select ds from dt)\n" +
                "                      and rr.ds = (select ds from dt)\n" +
                "                      and sn.ds = (select ds from dt)) aa on so.order_uid = aa.businessNo\n" +
                "         left join (select so.customer_id, min(date(to_date(substring(paytime, 1, 10), 'yyyy-mm-dd'))) paytime\n" +
                "                    from sh_order so\n" +
                "                             left join sh_customer sc on so.customer_id = sc.id\n" +
                "                             left join rc_user ru on sc.id_card_no = ru.idcardno\n" +
                "                             left join cl_loan cl on cl.customerid = ru.id\n" +
                "                    where so.ds = to_char(getdate(), 'yyyymmdd')\n" +
                "                      and sc.ds = (select ds from dt)\n" +
                "                      and cl.ds = (select ds from dt)\n" +
                "                      and ru.ds = (select ds from dt)\n" +
                "                    group by so.customer_id) gg on gg.customer_id = so.customer_id\n" +
                "         inner join (select so.order_uid\n" +
                "                     from sh_order so\n" +
                "                              left JOIN sh_order_logistics sol ON sol.order_uid = so.order_uid and confirm_sign = 20 and sol.is_deleted = 0\n" +
                "                     where so.ds = (select ds from dt)\n" +
                "                       AND sol.ds = (select ds from dt)\n" +
                "                       AND so.is_deleted = 0\n" +
                "                       and so.closing_time is null\n" +
                "                     order by order_uid limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ") cc on cc.order_uid = so.order_uid\n" +
                "WHERE so.ds = (select ds from dt)\n" +
                "  AND sof.ds = (select ds from dt)\n" +
                "  AND srp.ds = (select ds from dt)\n" +
                "  AND sc.ds = (select ds from dt)\n" +
                "  AND cl.ds = (select ds from dt)\n" +
                "  AND sn.ds = (select ds from dt)\n" +
                "  AND rmc.ds = (select ds from dt)\n" +
                "ORDER BY so.order_uid, period_no;";
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        saveData(records, overdueDay);
    }


    private void oldRepayTaskTidb(String ds, int startPage, Integer overdueDay) {
        startPage = startPage * PAGE_SIZE;
        List<ShRecordData> ShRecordData = rentOrderService.getShAllData(startPage, PAGE_SIZE, overdueDay);
        log.info("大盘分期(赊销自营)模块查询明细表表跑出来的数据量大小为:ds=" + ds + "  总条数为: " + ShRecordData.size());
        saveDataTidb(ShRecordData, overdueDay);
        log.info("大盘分期(赊销自营)模块查询明细表表跑出来的数据插入成功");
    }

    private void saveData(List<Record> records, Integer overdueDay) {
        try {
            List<RiskMerchantOrderOverdueDO> resList = fetchList(records, overdueDay);
            riskStageOrderOverdueService.saveBatch(resList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void saveDataTidb(List<ShRecordData> records, Integer overdueDay) {
        try {
            List<RiskMerchantOrderOverdueDO> resList = fetchListTidb(records, overdueDay);
            riskStageOrderOverdueService.saveBatch(resList, PAGE_SIZE_MERCHANT);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private List<RiskMerchantOrderOverdueDO> fetchList(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);

        List<RiskMerchantOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            RiskMerchantOrderOverdueDO riskStageOrderOverdueDO = null;
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
            for (RepaymentPlanDO repaymentPlanDO : value) {
                if (riskStageOrderOverdueDO == null) {
                    riskStageOrderOverdueDO = new RiskMerchantOrderOverdueDO();
                    resList.add(riskStageOrderOverdueDO);
                    riskStageOrderOverdueDO.setOrderId(orderId);
                    riskStageOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskStageOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskStageOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskStageOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskStageOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    riskStageOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskStageOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskStageOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskStageOrderOverdueDO.setOverdueDay(overdueDay);
                    riskStageOrderOverdueDO.setCustomerType(repaymentPlanDO.getCustomerType());
                    riskStageOrderOverdueDO.setDiscountAmt(repaymentPlanDO.getTotalDiscount());
                    riskStageOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskStageOrderOverdueDO.setScore(repaymentPlanDO.getScore());
                    riskStageOrderOverdueDO.setFirstCategory(repaymentPlanDO.getFirstCategory());
                    riskStageOrderOverdueDO.setSecondCategory(repaymentPlanDO.getSecondCategory());
                }
                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                //对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskStageOrderOverdueDO.setCountDay(rentStartDay);
                riskStageOrderOverdueDO.setTermType(String.valueOf(maxTerm));
                riskStageOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                setOverdueVal(riskStageOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);

                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();

                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    LocalDate maxRepayDate =
                            LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));

                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);

                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    setTermNotRepayVal(riskStageOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskStageOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private List<RiskMerchantOrderOverdueDO> fetchListTidb(List<ShRecordData> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = getRepaymentPlanDOSTidb(records);
        records = null;
        List<RiskMerchantOrderOverdueDO> resList = new CopyOnWriteArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));
        planList = null;
        // 创建一个固定大小的线程池，线程数为5
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        // 将订单ID和还款计划列表拆分成任务
        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            executorService.submit(() -> processSingleOrder(orderId, value, resList, overdueDay));
        }

        // 关闭线程池，等待所有任务完成
        executorService.shutdown();
        try {
            // 等待所有任务完成，设置合理超时时间
            executorService.awaitTermination(1, TimeUnit.HOURS);
        } catch (InterruptedException e) {
            // 恢复中断状态
            Thread.currentThread().interrupt();
            e.printStackTrace();
        }
        return resList;
    }

    private void processSingleOrder(Long orderId, List<RepaymentPlanDO> value, List<RiskMerchantOrderOverdueDO> resList, Integer overdueDay) {
        RiskMerchantOrderOverdueDO riskStageOrderOverdueDO = null;
        Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
        Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
        for (RepaymentPlanDO repaymentPlanDO : value) {
            if (riskStageOrderOverdueDO == null) {
                riskStageOrderOverdueDO = new RiskMerchantOrderOverdueDO();
                resList.add(riskStageOrderOverdueDO);
                riskStageOrderOverdueDO.setOrderId(orderId);
                riskStageOrderOverdueDO.setBizType(repaymentPlanDO.getBizType());
                riskStageOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                riskStageOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                riskStageOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                riskStageOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                riskStageOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                riskStageOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                riskStageOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                riskStageOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                riskStageOrderOverdueDO.setOverdueDay(overdueDay);
                riskStageOrderOverdueDO.setCustomerType(repaymentPlanDO.getCustomerType());
                riskStageOrderOverdueDO.setDiscountAmt(repaymentPlanDO.getTotalDiscount());
                riskStageOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                riskStageOrderOverdueDO.setScore(repaymentPlanDO.getScore());
                riskStageOrderOverdueDO.setFirstCategory(repaymentPlanDO.getFirstCategory());
                riskStageOrderOverdueDO.setSecondCategory(repaymentPlanDO.getSecondCategory());

                riskStageOrderOverdueDO.setProductType(repaymentPlanDO.getProductType());
                riskStageOrderOverdueDO.setGlodType(repaymentPlanDO.getGlodType());
                riskStageOrderOverdueDO.setApplicationName(repaymentPlanDO.getApplicationName());
                riskStageOrderOverdueDO.setSupervisedMachine(repaymentPlanDO.getSupervisedMachine());

            }
            int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
            int term = repaymentPlanDO.getTerm();
            //对当前期逾期情况做修正
            if (isOverdue == 2) {
                isOverdue = updateOverdueStatus(term2PlanDo, term);
                repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                repaymentPlanDO.setOverdue(5);
            }
            LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
            riskStageOrderOverdueDO.setCountDay(rentStartDay);
            riskStageOrderOverdueDO.setTermType(String.valueOf(maxTerm));
            riskStageOrderOverdueDO.setNo(repaymentPlanDO.getNo());
            //每期的逾期状态
            setOverdueVal(riskStageOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
            RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);

            if (isOverdue == 1) {
                Map<String, Object> resMap = new HashMap<>();
                resMap.put("term", BigDecimal.valueOf(term));
                resMap.put("capital", BigDecimal.ZERO);
                LocalDateTime repayDate = repaymentPlanDO.getRepayDate();

                LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                LocalDate maxRepayDate =
                        LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));

                resMap.put("endTime", localDate);
                resMap.put("maxRepayDate", maxRepayDate);

                // 获取逾期未还金额
                fetchCapital(term2PlanDo, resMap);
                // 设置对应期数未还金额
                BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                setTermNotRepayVal(riskStageOrderOverdueDO, term, decimal);
            } else {
                // 未逾期，逾期未还金额为0
                setTermNotRepayVal(riskStageOrderOverdueDO, term, BigDecimal.ZERO);
            }
        }
    }




    private RepaymentPlanDO getPlanDo(List<RepaymentPlanDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanDO>> term2PlanDo) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (value.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        return planDO;
    }

    private List<RepaymentPlanDO> getRepaymentPlanDOS(List<Record> records) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Integer term = Integer.valueOf(domain.getString("term"));
                Integer isDeleted = Integer.valueOf(domain.getString("is_deleted"));
                Integer miniType = Integer.valueOf(domain.getString("mini_type"));
                Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
//                Integer customerType = Integer.valueOf(domain.getString("customer_type"));
                String platform = domain.getString("platform");
                String no = domain.getString("no");
                Integer overdue = Integer.parseInt(domain.getString("overdue")) > 0 ? 5 : 1;
//                String riskLevel = domain.getString("risk_level");
//                if(riskLevel.contains("风控等级")){
//                    // 将风控等级4.0和风控等级4之类的进行合并
//                    String[] split = riskLevel.split("\\.");
//                    riskLevel = split[0];
//                }
                String riskStrategy = domain.getString("risk_strategy");
         //       String auditType = domain.getString("audit_type");
                // 获取分数
                String hitValue = domain.getString("hit_value");
                String secondCategory = domain.getString("second_category");
                String firstCategory = domain.getString("first_category");
//                String scene = domain.getString("scene");
//                String quotientName = domain.getString("quotient_name");
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100018");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                String isOverdue = domain.getString("is_overdue");
                BigDecimal capital = stringToDecimal(domain.getString("capital"));
                BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
                Integer maxTerm = Integer.valueOf(domain.getString("max_term"));


                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("rent_start_date"),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
                        dateFormatter), LocalTime.MIN);

                LocalDateTime realRepayTime = null;
                if (!domain.getString("real_repay_time").equals("\\N")) {
                    realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);
                }

                BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));
                BigDecimal discountAmt = stringToDecimal(domain.getString("discount_amt"));
                BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
                BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setMaxTerm(maxTerm);
                repaymentPlanDO.setScore(score);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setTermType(String.valueOf(term));
                repaymentPlanDO.setTotalDiscount(discountAmt);
                // todo 参数设置
                // repaymentPlanDO.setBondTermType(no);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setTerm(term);
//                repaymentPlanDO.setScene(scene);
//                repaymentPlanDO.setQuotientName(quotientName);
//                repaymentPlanDO.setCustomerType(customerType);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setFirstCategory(firstCategory);
                repaymentPlanDO.setSecondCategory(secondCategory);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
              // repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
               // repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        return planList;
    }

    private List<RepaymentPlanDO> getRepaymentPlanDOSTidb(List<ShRecordData> records) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getOrderId());
                Integer term = (null == domain.getTerm()) ? 0 : Integer.valueOf(domain.getTerm());
                Integer isDeleted = Integer.valueOf(domain.getIsDeleted());
                Integer miniType = Integer.valueOf(domain.getMiniType());
                Integer repayStatus = Integer.valueOf(domain.getRepayStatus());
               // Integer customerType = Integer.valueOf(domain.getCustomerType());
                String platform = domain.getPlatform();
                String no = domain.getNo();
                Integer overdue = Integer.parseInt(domain.getOverdue()) > 0 ? 5 : 1;
               String riskLevel = "0";
//                if(riskLevel.contains("风控等级")){
//                    // 将风控等级4.0和风控等级4之类的进行合并
//                    String[] split = riskLevel.split("\\.");
//                    riskLevel = split[0];
//                }
                String riskStrategy = domain.getRiskStrategy();
               String auditType = "0";
                // 获取分数
                String hitValue = domain.getHitValue();
                String secondCategory = domain.getSecondCategory();
                String firstCategory = domain.getFirstCategory();
                String scene = "0";
                String quotientName = "0";
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100018");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                String isOverdue = domain.getIsOverdue();
                BigDecimal capital = stringToDecimal(domain.getCapital());
                BigDecimal realCapital = stringToDecimal(domain.getRealCapital());
                Integer maxTerm = Integer.valueOf(domain.getMaxTerm());

                String productType=domain.getProductType();
                String glodType=domain.getGlodType();
                String applicationName=domain.getApplicationName();
                String supervisoryMachine=domain.getSupervisedMachine();
                repaymentPlanDO.setBizType(Integer.valueOf(domain.getBizType()));
                repaymentPlanDO.setProductType(productType);
                repaymentPlanDO.setGlodType(glodType);
                repaymentPlanDO.setApplicationName(applicationName);
                repaymentPlanDO.setSupervisedMachine(supervisoryMachine);

                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getRentStartDate(),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getRepayDate(),
                        dateFormatter), LocalTime.MIN);

                LocalDateTime realRepayTime = null;
                if (null != domain.getRealRepayTime() && !domain.getRealRepayTime().equals("\\N")) {
                    realRepayTime = LocalDateTime.parse(domain.getRealRepayTime(), YYYY_MM_DD_HH_MM_SS);
                }

                BigDecimal rentTotal = stringToDecimal(domain.getRentTotal());
                BigDecimal discountAmt = stringToDecimal(domain.getDiscountAmt());
                BigDecimal overdueFine = stringToDecimal(domain.getOverdueFine());
                BigDecimal bondAmt = stringToDecimal(domain.getBondAmt());
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setMaxTerm(maxTerm);
                repaymentPlanDO.setScore(score);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setTermType(String.valueOf(term));
                repaymentPlanDO.setTotalDiscount(discountAmt);
                // todo 参数设置
                // repaymentPlanDO.setBondTermType(no);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setCustomerType(0);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setFirstCategory(firstCategory);
                repaymentPlanDO.setSecondCategory(secondCategory);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
               repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        return planList;
    }

    private int updateOverdueStatus(Map<Integer, List<RepaymentPlanDO>> term2PlanDo, int term) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(--term);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (repaymentPlanDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        int isOverdue = Integer.parseInt(planDO.getIsOverdue());
        // 上一期逾期并且未还款,这一期即使还没到,算逾期
        // 上一期逾期并且还款,这一期还没到,不算逾期
        // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
        // 如果当前期逾期,不管有没有到封账日,都算逾期
        int repayStatus = planDO.getRepayStatus();
        return isOverdue == 1 ? (repayStatus == 10 ? 0 : 1) : 0;
    }


    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }


    /**
     * 递归查找，最早的的逾期期数
     *
     * @param map term为key，还款计划为val
     * @param
     */
    public void fetchTerm(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        List<RepaymentPlanDO> planDOList = map.get(key);
        RepaymentPlanDO planDO = planDOList.get(0);
        if (planDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : planDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        if (planDO != null) {
            String isOverdue = planDO.getIsOverdue();
            Integer repayStatus = planDO.getRepayStatus();
            Integer overdue = planDO.getOverdue();
            LocalDateTime realRepayTime = planDO.getRealRepayTime();
            BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));

            // //  本期逾期了判断上一期是否逾期,上一期未逾期，那么逾期期数就是本期
            // // 本期逾期了判断上一期是否逾期,上一期逾期，并且未还,继续向上判断,直到找到已还款的那一期,那一期+1就是逾期未还金额
            // // 本期逾期了判断上一期是否逾期,上一期逾期并且已经还款,逾期金额用本期算
            LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                    dateFormatter), LocalTime.MAX);

            if ("1".equals(isOverdue)) {
                //先判断本期的状态是否已逾期未还款
                if (repayStatus != 10 && overdue == 5) {
                    if (term.compareTo(new BigDecimal(1)) == 0) {
                        resMap.put("term", term);
                    } else {
                        // 递归判断判断上一期的订单是否逾期
                        term = term.subtract(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                        fetchTerm(map, resMap);
                    }
                } else {
                    // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
                    // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
                    // 再递归往上判断
                    if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
                        if (term.compareTo(new BigDecimal(1)) == 0) {
                            resMap.put("term", term);
                        } else {
                            term = term.subtract(BigDecimal.valueOf(1));
                            resMap.put("term", term);
                            fetchTerm(map, resMap);
                        }
                    } else {
                        term = term.add(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                    }
                }

            } else {
                term = term.add(BigDecimal.valueOf(1));
                resMap.put("term", term);
            }


        }

    }

    public void fetchCapital(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                dateFormatter), LocalTime.MAX);
        resMap.put("level", 1);
        if (sourceTerm > 1) {
            fetchTerm(map, resMap);
        }
        int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        for (Map.Entry<Integer, List<RepaymentPlanDO>> entry : map.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            RepaymentPlanDO planDO = value.get(0);
            if (value.size() > 1) {
                for (RepaymentPlanDO repaymentPlanDO : value) {
                    if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                        planDO = repaymentPlanDO;
                    }
                }
            }
            if (entry.getKey() >= term) {
                BigDecimal capital;
                if (term == entry.getKey()) {
                    // 实际还款时间大于这一期封账日,那么这一期将当没
                    if (planDO.getRealRepayTime() == null) {
                        capital = planDO.getCapital();
                    } else if (planDO.getRealRepayTime().compareTo(endTime) > 0) {
                        capital = planDO.getCapital();
                    } else {
                        capital = planDO.getRealCapital();
                    }
                } else {
                    capital = planDO.getCapital();
                }
                resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
            }
        }
    }


    private void setOverdueVal(RiskMerchantOrderOverdueDO RiskStageOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            RiskStageOrderOverdueDO.setIsOverdue1(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            RiskStageOrderOverdueDO.setIsOverdue2(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            RiskStageOrderOverdueDO.setIsOverdue3(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            RiskStageOrderOverdueDO.setIsOverdue4(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            RiskStageOrderOverdueDO.setIsOverdue5(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            RiskStageOrderOverdueDO.setIsOverdue6(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            RiskStageOrderOverdueDO.setIsOverdue7(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            RiskStageOrderOverdueDO.setIsOverdue8(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            RiskStageOrderOverdueDO.setIsOverdue9(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            RiskStageOrderOverdueDO.setIsOverdue10(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            RiskStageOrderOverdueDO.setIsOverdue11(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            RiskStageOrderOverdueDO.setIsOverdue12(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine12(overdueFine);
        }
    }

    private void setRenewAmt(RiskGeneralOrderOverdueDO RiskStageOrderOverdueDO,
                             int term,
                             BigDecimal renewAmt) {
        if (term == 1) {
            RiskStageOrderOverdueDO.setRenewAmt1(renewAmt);
        } else if (term == 2) {
            RiskStageOrderOverdueDO.setRenewAmt2(renewAmt);
        } else if (term == 3) {
            RiskStageOrderOverdueDO.setRenewAmt3(renewAmt);
        } else if (term == 4) {
            RiskStageOrderOverdueDO.setRenewAmt4(renewAmt);
        } else if (term == 5) {
            RiskStageOrderOverdueDO.setRenewAmt5(renewAmt);
        } else if (term == 6) {
            RiskStageOrderOverdueDO.setRenewAmt6(renewAmt);
        } else if (term == 7) {
            RiskStageOrderOverdueDO.setRenewAmt7(renewAmt);
        } else if (term == 8) {
            RiskStageOrderOverdueDO.setRenewAmt8(renewAmt);
        } else if (term == 9) {
            RiskStageOrderOverdueDO.setRenewAmt9(renewAmt);
        } else if (term == 10) {
            RiskStageOrderOverdueDO.setRenewAmt10(renewAmt);
        } else if (term == 11) {
            RiskStageOrderOverdueDO.setRenewAmt11(renewAmt);
        } else if (term == 12) {
            RiskStageOrderOverdueDO.setRenewAmt12(renewAmt);
        }
    }

    private void setOverdueVal(RiskGeneralOrderOverdueDO RiskStageOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            RiskStageOrderOverdueDO.setIsOverdue1(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            RiskStageOrderOverdueDO.setIsOverdue2(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            RiskStageOrderOverdueDO.setIsOverdue3(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            RiskStageOrderOverdueDO.setIsOverdue4(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            RiskStageOrderOverdueDO.setIsOverdue5(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            RiskStageOrderOverdueDO.setIsOverdue6(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            RiskStageOrderOverdueDO.setIsOverdue7(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            RiskStageOrderOverdueDO.setIsOverdue8(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            RiskStageOrderOverdueDO.setIsOverdue9(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            RiskStageOrderOverdueDO.setIsOverdue10(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            RiskStageOrderOverdueDO.setIsOverdue11(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            RiskStageOrderOverdueDO.setIsOverdue12(isOverdue);
            RiskStageOrderOverdueDO.setOverdueFine12(overdueFine);
        }
    }

    private void setTermNotRepayVal(RiskMerchantOrderOverdueDO RiskStageOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            RiskStageOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            RiskStageOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            RiskStageOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            RiskStageOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            RiskStageOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            RiskStageOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            RiskStageOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            RiskStageOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            RiskStageOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            RiskStageOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            RiskStageOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            RiskStageOrderOverdueDO.setTerm12(decimal);
        }
    }

    private void setTermNotRepayVal(RiskGeneralOrderOverdueDO RiskStageOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            RiskStageOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            RiskStageOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            RiskStageOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            RiskStageOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            RiskStageOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            RiskStageOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            RiskStageOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            RiskStageOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            RiskStageOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            RiskStageOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            RiskStageOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            RiskStageOrderOverdueDO.setTerm12(decimal);
        }
    }
}



