package qnvip.data.overview.business.overdue;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.business.whole.report.WholeLawReportBusiness;
import qnvip.data.overview.domain.overdue.RepayPerformanceBuyoutRepayDO;
import qnvip.data.overview.service.overdue.RepayPerformanceBuyoutRepayService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RepayPerformanceBuyoutBusiness {

    private final OdpsUtil odpsUtil;

    private final RepayPerformanceBuyoutRepayService service;

    public void excute(String ds) {
        String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);
        String sql = "with dt as (select " + ds + " as ds) ," +
                "  self_order AS (SELECT id,`no` order_no,parent_id,settle_date FROM rent_order\n" +
                "                    WHERE ds=(select ds from dt) AND is_deleted=0 AND merchant_id IN (100,10000107))\n" +
                "\n" +
                "    ,calc_buyout_repay_detail AS (SELECT send.order_no AS main_order_no\n" +
                "                                        ,send.buyout_amt\n" +
                "                                        ,extend_order.order_no AS extend_no\n" +
                "                                        ,extend_order.settle_date\n" +
                "                                        ,send.extend_first_repay_date\n" +
                "                                         -- 主订单买断 或 续租订单买断 或 主订单非归还的结清\n" +
                "                                        ,COALESCE(main_infomore.buyout_time,extend_infomore.buyout_time,IF(NULLIF(TRIM(extend_infomore.return_time),'') IS NULL, NULLIF(TRIM(extend_order.settle_date),''), NULL)) AS total_buyout_date\n" +
                "                                        ,IF(SUBSTRING(send.extend_first_repay_date,1,10)>'" + time + "',1,0) AS is_one_month_after\n" +
                "                                        ,IF(send.current_overdue_days=0\n" +
                "                                              AND SUBSTRING(send.rent_end_date2,1,10) BETWEEN DATE_ADD('" + time + "',1) AND DATE_ADD('" + time + "',15), 1, 0) AS is_pre_half_month\n" +
                "                                  -- 主要使用发货表的前一天的ds，可以共用该表数据\n" +
                "                                  FROM (SELECT * FROM dataview_repay_ontime_order_detail\n" +
                "                                        WHERE ds=(select ds from dt) " +
                "                                        AND finance_title!='3+X' AND finance_title NOT LIKE '%6+6%'\n" +
                "                                        AND total_settle_status=1) send\n" +
                "                                  JOIN self_order main_order ON main_order.parent_id=0 AND send.order_no=main_order.order_no\n" +
                "                                  LEFT JOIN self_order extend_order ON extend_order.parent_id<>0 AND send.extend_no=extend_order.order_no\n" +
                "                                  LEFT JOIN (SELECT order_id\n" +
                "                                                   ,NULLIF(TRIM(buyout_time),'') AS buyout_time\n" +
                "                                             FROM rent_order_infomore\n" +
                "                                             WHERE ds=(select ds from dt) AND is_deleted=0) main_infomore\n" +
                "                                  ON main_order.id=main_infomore.order_id\n" +
                "                                  LEFT JOIN (SELECT order_id\n" +
                "                                                   ,NULLIF(TRIM(buyout_time),'') AS buyout_time\n" +
                "                                                   ,NULLIF(TRIM(return_time),'') AS return_time\n" +
                "                                             FROM rent_order_infomore\n" +
                "                                             WHERE ds=(select ds from dt) AND is_deleted=0) extend_infomore\n" +
                "                                  ON extend_order.id=extend_infomore.order_id)\n" +
                "SELECT 0 AS stat_time_zone\n" +
                "      ,stat_type    \n" +
                "      ,buyout_stage\n" +
                "      ,collect_buyout\n" +
                "      ,repaied_buyout \n" +
                "      ,if(collect_buyout=0,0,repaied_buyout / collect_buyout) AS buyout_ratio \n" +
                "FROM\n" +
                "(SELECT 0 AS stat_type  \n" +
                "      ,1 AS buyout_stage  \n" +
                "      ,SUM(buyout_amt) AS collect_buyout\n" +
                "      ,SUM(IF(SUBSTRING(total_buyout_date,1,10)='" + time + "',buyout_amt,0)) AS repaied_buyout\n" +
                "FROM calc_buyout_repay_detail\n" +
                "WHERE is_pre_half_month=1\n" +
                "UNION ALL\n" +
                "SELECT 0 AS stat_type  \n" +
                "      ,2 AS buyout_stage  \n" +
                "      ,SUM(buyout_amt) AS collect_buyout\n" +
                "      ,SUM(IF(SUBSTRING(total_buyout_date,1,10)='" + time + "',buyout_amt,0)) AS repaied_buyouy\n" +
                "FROM calc_buyout_repay_detail\n" +
                "WHERE is_one_month_after=1\n" +
                "UNION ALL\n" +
                "SELECT 1 AS stat_type \n" +
                "      ,1 AS buyout_stage  \n" +
                "      ,COUNT(DISTINCT main_order_no) AS collect_buyout\n" +
                "      ,COUNT(DISTINCT IF(SUBSTRING(total_buyout_date,1,10)='" + time + "', main_order_no,NULL)) AS repaied_buyout\n" +
                "FROM calc_buyout_repay_detail\n" +
                "WHERE is_pre_half_month=1\n" +
                "UNION ALL\n" +
                "SELECT 1 AS stat_type \n" +
                "      ,2 AS buyout_stage  \n" +
                "      ,COUNT(DISTINCT IF(extend_no IS NOT NULL,main_order_no,NULL)) AS collect_buyout\n" +
                "      ,COUNT(DISTINCT IF(SUBSTRING(total_buyout_date,1,10)='" + time + "', IF(extend_no IS NOT NULL,main_order_no,NULL),NULL)) AS repaied_buyout\n" +
                "FROM calc_buyout_repay_detail\n" +
                "WHERE is_one_month_after=1) tmp;";

        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        LocalDate localDate = DateUtils.stringToLocateDate(time);
        List<RepayPerformanceBuyoutRepayDO> collect = records.stream().map(o -> {
            RepayPerformanceBuyoutRepayDO domain = new RepayPerformanceBuyoutRepayDO();
            domain.setStatTimeZone(Integer.valueOf(o.getString("stat_time_zone")));
            domain.setStatType(Integer.valueOf(o.getString("stat_type")));
            domain.setBuyoutStage(Integer.valueOf(o.getString("buyout_stage")));
            domain.setCollectBuyout(new BigDecimal(o.getString("collect_buyout")));
            domain.setRepaiedBuyout(new BigDecimal(o.getString("repaied_buyout")));
            domain.setBuyoutRatio(new BigDecimal(o.getString("buyout_ratio")));
            domain.setCountDay(time);
            return domain;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            service.removeDataByTime(localDate);
            service.saveBatch(collect);
        }
    }

}
