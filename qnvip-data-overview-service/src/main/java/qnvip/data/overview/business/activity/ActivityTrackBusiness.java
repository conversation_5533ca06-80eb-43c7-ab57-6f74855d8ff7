package qnvip.data.overview.business.activity;

import com.aliyun.odps.data.Record;
import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ActivityTrackBusiness {

    private final OdpsUtil odpsUtil;


}
