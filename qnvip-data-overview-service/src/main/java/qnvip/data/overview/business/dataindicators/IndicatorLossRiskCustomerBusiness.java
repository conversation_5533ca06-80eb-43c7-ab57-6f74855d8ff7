package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorLossRiskCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorLossRiskCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/2/10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLossRiskCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorLossRiskCustomerService indicatorLossRiskCustomerService;


    /**
     * 启动计算任务（计算30天的数据）
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorLossRiskCustomerDO> miniType2Map = new HashMap<>();
        List<LocalDateTime> dateTimeList = DateUtils.getDateTimeList(DateUtils.stringToDate(sTime),
                DateUtils.stringToDate(eTime));
        for (LocalDateTime time : dateTimeList) {
            LocalDateTime startTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MAX);
            countRegisterAndNotOrder(ds, DateUtils.dateToString(startTime), DateUtils.dateToString(endTime),
                    miniType2Map);
        }
        countCancelCount(ds, sTime, eTime, miniType2Map);
        countLtNhRegisterCount(ds, sTime, eTime, miniType2Map);
        countServiceOrder(ds, sTime, eTime, true, miniType2Map);
        countServiceOrder(ds, sTime, eTime, false, miniType2Map);
        countLtNdRefusedCount(ds, sTime, eTime, miniType2Map);

        for (IndicatorLossRiskCustomerDO value : miniType2Map.values()) {
            value.setTotalCancelCount(value.getFirstCancelCount() + value.getSecondCancelCount() + value.getThirdCancelCount());
            indicatorLossRiskCustomerService.saveOrUpdate(value);
        }
    }


    /**
     * 下单取消人数分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countCancelCount(String ds, String sTime, String eTime,
                                  Map<String, IndicatorLossRiskCustomerDO> collectMap) {
        String sql = " select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, " +
                "        f.rate_config_type, " +
                "        IF(ISNOTNULL(f.ext_json) and f.repayment_term=3,'1','0') as forcedconversion, " +
                "        count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "         inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "         inner join rent_order_finance_detail f on a.id=f.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and d.result = 10 " +
                "  and e.rulestage = 1 " +
                "  and a.id not in ( " +
                "    select a.id  " +
                "    from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "    where a.is_deleted = 0 " +
                "      and a.merchant_id = 100 " +
                "      and a.parent_id = 0 " +
                "      and a.type = 1 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "      and c.rhtime is not null and c.rhtime != '' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                "      and c.ds = " + ds +
                "  ) " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and e.ds = " + ds +
                "  and f.ds = " + ds +
                " group by mini_type, to_char(a.create_time, 'yyyy-mm-dd'), " +
                "    IF(ISNOTNULL(f.ext_json) and f.repayment_term=3,'1','0'), " +
                "    f.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            initMap(collectMap, miniType, day);
            IndicatorLossRiskCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                updateDO.setThirdCancelCount(Optional.ofNullable(updateDO.getThirdCancelCount()).orElse(0) + count);
            } else if (rateConfigType == 10) {
                updateDO.setFirstCancelCount(Optional.ofNullable(updateDO.getFirstCancelCount()).orElse(0) + count);
            } else {
                updateDO.setSecondCancelCount(Optional.ofNullable(updateDO.getSecondCancelCount()).orElse(0) + count);
            }
        }
    }


    /**
     * 下单取消人数中 N小时内注册人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorLossRiskCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                        inner join rent_customer c " +
                "                                   on get_json_object(c.bindinfo, '$[0].miniUserId') = a" +
                ".customer_third_id " +
                "                        inner join ( " +
                "                            select a.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day,a" +
                ".customer_id " +
                "                            from rent_order a " +
                "                                     inner join cl_loan b on a.no = b.loanno " +
                "                                     inner join serial_no c on b.loanno = c.businessNo " +
                "                                     inner join rc_risk_access_rule_result_2023 d on c.id = d" +
                ".serialnoId " +
                "                                     inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "                            where a.is_deleted = 0 " +
                "                              and a.merchant_id = 100 " +
                "                              and a.parent_id = 0 " +
                "                              and a.type = 1 " +
                "                              and a.biz_type = 2 " +
                "                              and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "                              and d.result = 10 " +
                "                              and e.rulestage = 1 " +
                "                              and a.id not in ( " +
                "                                select a.id  " +
                "                                from rent_order a " +
                "                                     inner join cl_loan b on a.no = b.loanno " +
                "                                     inner join serial_no c on b.loanno = c.businessNo " +
                "                                where a.is_deleted = 0 " +
                "                                  and a.merchant_id = 100 " +
                "                                  and a.parent_id = 0 " +
                "                                  and a.type = 1 " +
                "                                  and a.biz_type = 2 " +
                "                                  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "                                  and c.rhtime is not null and c.rhtime != '' " +
                "                                  and a.ds = " + ds +
                "                                  and b.ds = " + ds +
                "                                  and c.ds = " + ds +
                "                              ) " +
                "                              and a.ds = " + ds +
                "                              and b.ds = " + ds +
                "                              and c.ds = " + ds +
                "                              and d.ds = " + ds +
                "                              and e.ds = " + ds +
                "                             group by a.mini_type, to_char(a.create_time, 'yyyy-mm-dd'),a" +
                ".customer_id " +
                "               ) b on a.mini_type = b.mini_type and c.id = b.customer_id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and c.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' " +
                "                   and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                "where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorLossRiskCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(count);
        }
    }


    /**
     * 下单取消人数中 已注册未下单(一天天查询)
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRegisterAndNotOrder(String ds, String sTime, String eTime,
                                          Map<String, IndicatorLossRiskCustomerDO> collectMap) {
        String sql = " select b.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct a.id) count " +
                "from rent_customer a " +
                "         inner join dataview_track_enter_applets b " +
                "                    on get_json_object(a.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "         inner join ( " +
                "          select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day,a.customer_id " +
                "   from rent_order a " +
                "            inner join cl_loan b on a.no = b.loanno " +
                "            inner join serial_no c on b.loanno = c.businessNo " +
                "            inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "            inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "   where a.is_deleted = 0 " +
                "     and a.merchant_id = 100 " +
                "     and a.parent_id = 0 " +
                "     and a.type = 1 " +
                "     and a.biz_type = 2 " +
                "     and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "     and d.result = 10 " +
                "     and e.rulestage = 1 " +
                "     and a.id not in ( " +
                "       select a.id  " +
                "       from rent_order a " +
                "            inner join cl_loan b on a.no = b.loanno " +
                "            inner join serial_no c on b.loanno = c.businessNo " +
                "       where a.is_deleted = 0 " +
                "         and a.merchant_id = 100 " +
                "         and a.parent_id = 0 " +
                "         and a.type = 1 " +
                "         and a.biz_type = 2 " +
                "         and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "         and c.rhtime is not null and c.rhtime != '' " +
                "         and a.ds = " + ds +
                "         and b.ds = " + ds +
                "         and c.ds = " + ds +
                "     ) " +
                "     and a.ds = " + ds +
                "     and b.ds = " + ds +
                "     and c.ds = " + ds +
                "     and d.ds = " + ds +
                "     and e.ds = " + ds +
                "    group by mini_type, to_char(a.create_time, 'yyyy-mm-dd'),a.customer_id " +
                "         ) c on a.id = c.customer_id " +
                "where a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                " and a.id not in ( " +
                "     select customer_id " +
                "     from rent_order " +
                "     where create_time between '" + sTime + "' " +
                "         and '" + eTime + "' " +
                "       and ds = " + ds +
                " ) " +
                "   and a.ds = " + ds +
                "   and b.ds = " + ds +
                " group by b.mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossRiskCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterAndNotOrder(count);
        }
    }


    /**
     * 下单取消人数中 服务中/结束客户数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countServiceOrder(String ds, String sTime, String eTime, Boolean inServiceFlag,
                                   Map<String, IndicatorLossRiskCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, " +
                "        count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "         inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "         inner join ( " +
                "                 select min(mini_type) mini_type,customer_id,to_char(create_time,'yyyy-mm-dd') day " +
                "from rent_order  " +
                "                    where is_deleted = 0  " +
                "                    and type = 1  " +
                "                    and parent_id = 0  " +
                "                    and biz_type = 2  " +
                "                    and merchant_id = 100  " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                    and ds = " + ds +
                "                    group by customer_id,to_char(create_time,'yyyy-mm-dd') " +
                "                ) f " +
                "          on f.day = to_char(a.create_time, 'yyyy-mm-dd') and f.customer_id = a.customer_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.status " + (inServiceFlag ? "not" : "") + " in (220,330)" +
                "  and d.result = 10 " +
                "  and e.rulestage = 1 " +
                "  and a.id not in ( " +
                "    select a.id  " +
                "    from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "    where a.is_deleted = 0 " +
                "      and a.merchant_id = 100 " +
                "      and a.parent_id = 0 " +
                "      and a.type = 1 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "      and c.rhtime is not null and c.rhtime != '' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                "      and c.ds = " + ds +
                "  ) " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and e.ds = " + ds +
                " group by a.mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossRiskCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (inServiceFlag) {
                updateDO.setInServiceCount(count);
            } else {
                updateDO.setOutServiceCount(count);
            }
        }
    }


    /**
     * 下单取消人数中 风控通过未付/N天内被拒
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNdRefusedCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorLossRiskCustomerDO> collectMap) {
        String sql = " select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, " +
                "        count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "         inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "         inner join ( " +
                "                select max(risktime) risktime , max(id) order_id, customer_id " +
                "                from ( " +
                "                         select b.risktime , a.customer_id , a.id " +
                "                         from rent_order a " +
                "                                  inner join cl_loan b on a.no = b.loanno " +
                "                         where a.is_deleted = 0 " +
                "                           and a.merchant_id = 100 " +
                "                           and a.parent_id = 0 " +
                "                           and a.type = 1 " +
                "                           and a.biz_type = 2 " +
                "                           and b.riskstatus in (15, 25) " +
                "                           and a.ds = " + ds +
                "                           and b.ds = " + ds +
                "                         order by b.risktime asc " +
                "                     ) " +
                "                group by customer_id " +
                "            ) f on a.customer_id = f.customer_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and d.result = 10 " +
                "  and e.rulestage = 1 " +
                "  and a.id not in ( " +
                "    select a.id  " +
                "    from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "    where a.is_deleted = 0 " +
                "      and a.merchant_id = 100 " +
                "      and a.parent_id = 0 " +
                "      and a.type = 1 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "      and c.rhtime is not null and c.rhtime != '' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                "      and c.ds = " + ds +
                "  ) " +
                "  and datediff(a.create_time, f.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and e.ds = " + ds +
                " group by mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossRiskCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(count);
        }
    }


    private void initMap(Map<String, IndicatorLossRiskCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorLossRiskCustomerDO ioid = new IndicatorLossRiskCustomerDO();
            ioid.setMiniType(miniType);
            ioid.setCountDay(countDay);
            ioid.setTotalCancelCount(0);
            ioid.setFirstCancelCount(0);
            ioid.setSecondCancelCount(0);
            ioid.setThirdCancelCount(0);
            ioid.setLtNhRegisterCount(0);
            ioid.setRegisterAndNotOrder(0);
            ioid.setInServiceCount(0);
            ioid.setOutServiceCount(0);
            ioid.setLtNdRefusedCount(0);
            miniType2Map.put(key, ioid);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}