package qnvip.data.overview.business.risk;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RiskBusinessDO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.risk.RiskBusinessService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控大盘-总览
 * create by gw on 2022/3/24
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Integer PAGE_SIZE = 10000;


    private final OdpsUtil odpsUtil;
    private final RiskBusinessService riskBusinessService;



    // public void execCount(String ds) {
    //
    //     try {
    //         riskBusinessService.deleteAll();
    //         Integer size = getCount(ds);
    //         int times = size / PAGE_SIZE;
    //         if (size % PAGE_SIZE != 0) {
    //             times += 1;
    //         }
    //         int startPage = 0;
    //         for (int i = 0; i < times; i++) {
    //             List<RiskBusinessDO> list = countRepayTask(ds, startPage);
    //             riskBusinessService.saveBatch(list);
    //             startPage++;
    //         }
    //     } catch (Exception e) {
    //         log.error(e.getMessage(), e);
    //     }
    // }



    /**
     * 风控经营
     *
     * @param ds
     */
    // private List<RiskBusinessDO> countRepayTask(String ds, int startPage) {
    //
    //     String sql = "select date(a.rent_start_date)                                                   count_day," +
    //             "       a.mini_type," +
    //             "       (CASE" +
    //             "            WHEN a.mini_type in (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
    //             "            WHEN a.mini_type in (6, 8, 10, 11) THEN '微信'" +
    //             "            WHEN a.mini_type = 2 THEN '字节跳动'" +
    //             "            WHEN a.mini_type = 3 THEN 'app' END)                                 platform," +
    //             "        c.rate_config_type                                                        finance_type," +
    //             "        cl.riskOpinion                                                            risk_level," +
    //             "        sn.riskStrategy                                                           risk_strategy," +
    //             "        IF(artificialAuditorId=1, '自动风控','人工审核')                             audit_type     ," +
    //             "        IF(ext_json is not null and c.rate_config_type = 10, '1', '0') as         forcedconversion," +
    //             "        nvl(count(no), 0)                                                         total_order_cnt," +
    //             "        sum(if(c.rate_config_type != 10,c.actual_financing_amt,d.capital*12))     rent_total," +
    //             "        sum(if(c.rate_config_type!=10,c.buyout_amt,0))                            buyout_amt_total," +
    //             "        nvl(sum(discounts_total), 0)                                              discounts_total," +
    //             "        nvl(sum(bond_amt), 0)                                                     bond_amt_total" +
    //             "  from rent_order a" +
    //             "           left join (select min(ext) ext, order_id" +
    //             "                      from rent_order_flow b" +
    //             "                      where b.ds = " + ds + "" +
    //             "                        and b.biz_type = 3" +
    //             "                        and b.is_deleted = 0" +
    //             "                        and b.pay_status = 10" +
    //             "                        and (b.mark_refund is null or b.mark_refund = 0)" +
    //             "                        and b.flow_type = 1" +
    //             "                        and b.refunded_amt = 0" +
    //             "                        and b.ext is not null" +
    //             "                        and b.ext != ''" +
    //             "                      GROUP by order_id) g ON a.id = g.order_id" +
    //             "           inner join rent_order_finance_detail c on a.id = c.order_id" +
    //             "           inner join rent_order_logistics rol on a.id = rol.order_id" +
    //             "           inner join (select order_id, avg(capital) capital" +
    //             "                          from rent_order_repayment_plan" +
    //             "                          where is_deleted = 0" +
    //             "                          and ds = " + ds + "" +
    //             "                          group by order_id) d on a.id = d.order_id" +
    //             "           left join (select order_id,sum(if(isbefore=1,amt1+amt2,0)) discounts_total from(" +
    //             "             select x.order_id," +
    //             "                           if(min(x.bind_order_time) is not null and min(y.payment_time) >= " +
    //             "                               min(x.bind_order_time), 1,0)              isbefore," +
    //             "                           if(x.type in (1, 3), max(x.write_off_amt), 0) amt1," +
    //             "                           if(x.type in (2, 4), sum(x.write_off_amt), 0) amt2" +
    //             "                    from rent_customer_coupon x" +
    //             "                             inner join rent_order y on x.order_id = y.id" +
    //             "                    where x.order_id > 0" +
    //             "                      and x.scene = 1" +
    //             "                      and x.is_deleted = 0" +
    //             "                      and y.is_deleted = 0" +
    //             "                      and x.ds = " + ds +
    //             "                      and y.ds = " + ds +
    //             "                    group by x.order_id, x.type" +
    //             "               ) group by order_id) x on a.id = x.order_id " +
    //             "           inner join cl_loan cl on cl.loanno = a.no" +
    //             "           inner join serial_no sn on sn.businessno = a.no" +
    //             "  where a.merchant_id = 100" +
    //             "    and a.termination != 5" +
    //             "    and a.biz_type = 2" +
    //             "    and a.type = 1" +
    //             "    and a.parent_id = 0" +
    //             "    and a.is_deleted = 0" +
    //             "    and c.is_deleted = 0" +
    //             "    and a.ds =" + ds + "" +
    //             "    and c.ds = " + ds + "" +
    //             "    and cl.ds = " + ds + "" +
    //             "    and sn.ds = " + ds + "" +
    //             "    and rol.ds = " + ds + "" +
    //             "    and rol.sign_time is not null" +
    //             "    AND date(a.rent_start_date) >= '2020-10-01'" +
    //             "  group by date(a.rent_start_date), a.mini_type, c.rate_config_type," +
    //             "          IF(ext_json is not null and c.rate_config_type = 10, '1', '0'), cl.riskOpinion, sn" +
    //             ".riskStrategy," +
    //             "          cl.artificialAuditorId " +
    //             "         ,(" +
    //             "             c.bond_amt/(" +
    //             "                 if(" +
    //             "                     c.rate_config_type != 10" +
    //             "                     ,c.actual_financing_amt" +
    //             "                     ,d.capital*12" +
    //             "                 ) + if(c.rate_config_type != 10, c.buyout_amt, 0)" +
    //             "             ) - x.discounts_total" +
    //             "         )" +
    //             " order by date(a.rent_start_date)" +
    //             " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";
    //
    //     List<Record> records = odpsUtil.querySql(sql);
    //     List<RiskBusinessDO> resList = new ArrayList<>();
    //         records.forEach(k -> {
    //         LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("count_day"), dateFormatter),
    //                 LocalTime.MIN);
    //         Integer miniType = Integer.valueOf(k.getString("mini_type"));
    //         String platform = k.getString("platform");
    //         Integer financeType = Integer.valueOf(k.getString("finance_type"));
    //         String riskLevel = k.getString("risk_level");
    //         String riskStrategy = k.getString("risk_strategy");
    //         String auditType = k.getString("audit_type");
    //         Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
    //         Integer totalOrderCnt = Integer.valueOf(k.getString("total_order_cnt"));
    //         BigDecimal rentTotal = stringToDecimal(k.getString("rent_total"));
    //         BigDecimal buyoutAmtTotal = stringToDecimal(k.getString("buyout_amt_total"));
    //         BigDecimal discountsTotal = stringToDecimal(k.getString("discounts_total"));
    //         BigDecimal bondAmtTotal = stringToDecimal(k.getString("bond_amt_total"));
    //
    //         RiskBusinessDO repayDO = new RiskBusinessDO();
    //         repayDO.setCountDay(countDay);
    //         repayDO.setMiniType(miniType);
    //         repayDO.setPlatform(platform);
    //         repayDO.setRiskLevel(riskLevel);
    //         repayDO.setRiskStrategy(riskStrategy);
    //         repayDO.setAuditType(auditType);
    //         repayDO.setTotalOrderCnt(totalOrderCnt);
    //         repayDO.setRentTotal(rentTotal);
    //         repayDO.setBuyoutAmtTotal(buyoutAmtTotal);
    //         repayDO.setDiscountsTotal(discountsTotal);
    //         repayDO.setBondAmtTotal(bondAmtTotal);
    //         repayDO.setFinanceType(FinanceTypeEnum.getFinanceType(financeType, 0, forcedConversion));
    //         resList.add(repayDO);
    //     });
    //     return resList;
    // }
    //
    // /**
    //  * 风控经营
    //  *
    //  * @param ds
    //  */
    // private Integer getCount(String ds) {
    //
    //     String sql = "select count(*) num from (select date(a.rent_start_date)                                       " +
    //             "            count_day," +
    //             "       a.mini_type," +
    //             "       (CASE" +
    //             "            WHEN a.mini_type in (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
    //             "            WHEN a.mini_type in (6, 8, 10, 11) THEN '微信'" +
    //             "            WHEN a.mini_type = 2 THEN '字节跳动'" +
    //             "            WHEN a.mini_type = 3 THEN 'app' END)                                 platform," +
    //             "        c.rate_config_type                                                        finance_type," +
    //             "        cl.riskOpinion                                                            risk_level," +
    //             "        sn.riskStrategy                                                           risk_strategy," +
    //             "        IF(artificialAuditorId=1, '自动风控','人工审核')                             audit_type," +
    //             "        IF(ext_json is not null and c.rate_config_type = 10, '1', '0') as         forcedconversion," +
    //             "        nvl(count(no), 0)                                                         total_order_cnt," +
    //             "        sum(if(c.rate_config_type != 10,c.actual_financing_amt,d.capital*12))     rent_total," +
    //             "        sum(if(c.rate_config_type!=10,c.buyout_amt,0))                            buyout_amt_total," +
    //             "        nvl(sum(discounts_total), 0)                                             discounts_total," +
    //             "        nvl(sum(bond_amt), 0)                                                     bond_amt_total" +
    //             "  from rent_order a" +
    //             "           left join (select min(ext) ext, order_id" +
    //             "                      from rent_order_flow b" +
    //             "                      where b.ds = " + ds + "" +
    //             "                        and b.biz_type = 3" +
    //             "                        and b.is_deleted = 0" +
    //             "                        and b.pay_status = 10" +
    //             "                        and (b.mark_refund is null or b.mark_refund = 0)" +
    //             "                        and b.flow_type = 1" +
    //             "                        and b.refunded_amt = 0" +
    //             "                        and b.ext is not null" +
    //             "                        and b.ext != ''" +
    //             "                      GROUP by order_id) g ON a.id = g.order_id" +
    //             "           inner join rent_order_finance_detail c on a.id = c.order_id" +
    //             "           inner join rent_order_logistics rol on a.id = rol.order_id" +
    //             "           inner join (select order_id, avg(capital) capital" +
    //             "                          from rent_order_repayment_plan" +
    //             "                          where is_deleted = 0" +
    //             "                          and ds = " + ds + "" +
    //             "                          group by order_id) d on a.id = d.order_id" +
    //             "           left join (select order_id,sum(if(isbefore=1,amt1+amt2,0)) discounts_total from(" +
    //             "             select x.order_id," +
    //             "                           if(min(x.bind_order_time) is not null and min(y.payment_time) >= " +
    //             "                               min(x.bind_order_time), 1,0)              isbefore," +
    //             "                           if(x.type in (1, 3), max(x.write_off_amt), 0) amt1," +
    //             "                           if(x.type in (2, 4), sum(x.write_off_amt), 0) amt2" +
    //             "                    from rent_customer_coupon x" +
    //             "                             inner join rent_order y on x.order_id = y.id" +
    //             "                    where x.order_id > 0" +
    //             "                      and x.scene = 1" +
    //             "                      and x.is_deleted = 0" +
    //             "                      and y.is_deleted = 0" +
    //             "                      and x.ds = " + ds +
    //             "                      and y.ds = " + ds +
    //             "                    group by x.order_id, x.type" +
    //             "               ) group by order_id) x on a.id = x.order_id " +
    //             "           inner join cl_loan cl on cl.loanno = a.no" +
    //             "           inner join serial_no sn on sn.businessno = a.no" +
    //             "  where a.merchant_id = 100" +
    //             "    and a.payment_time is not null" +
    //             "    and a.termination != 5" +
    //             "    and a.biz_type = 2" +
    //             "    and a.type = 1" +
    //             "    and a.parent_id = 0" +
    //             "    and a.is_deleted = 0" +
    //             "    and c.is_deleted = 0" +
    //             "    and a.ds =" + ds + "" +
    //             "    and c.ds = " + ds + "" +
    //             "    and cl.ds = " + ds + "" +
    //             "    and sn.ds = " + ds + "" +
    //             "    and rol.ds = " + ds + "" +
    //             "    and rol.sign_time is not null" +
    //             "    AND date(a.rent_start_date) >= '2020-10-01'" +
    //             "  group by date(a.rent_start_date), a.mini_type, c.rate_config_type," +
    //             "          IF(ext_json is not null and c.rate_config_type = 10, '1', '0'), cl.riskOpinion, sn" +
    //             ".riskStrategy," +
    //             "          cl.artificialAuditorId" +
    //             "         ,(" +
    //             "             c.bond_amt/(" +
    //             "                 if(" +
    //             "                     c.rate_config_type != 10" +
    //             "                     ,c.actual_financing_amt" +
    //             "                     ,d.capital*12" +
    //             "                 ) + if(c.rate_config_type != 10, c.buyout_amt, 0)" +
    //             "             ) - x.discounts_total" +
    //             "         )" +
    //             " order by date(a.rent_start_date));";
    //     List<Record> records = odpsUtil.querySql(sql);
    //     return Integer.valueOf(records.get(0).getString("num"));
    // }
    //
    // private BigDecimal stringToDecimal(String val) {
    //     if ("\\N".equals(val)) {
    //         val = "0";
    //     }
    //     return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    // }

}


