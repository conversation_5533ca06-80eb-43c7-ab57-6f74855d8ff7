package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleDO;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorBeforeSaleBusiness {

    /**
     * 驻留N秒
     */
    public static final int RESIDE_N_MILL_SECOND = 19000;
    /**
     * 下单意愿人数N秒
     */
    public static final int WANT_N_MILL_SECOND = 67000;
    /**
     * N分钟内注册(暂定6分钟)
     */
    public static final int N_MINUTES = 6;
    /**
     * 风控N天以上
     */
    public static final int N_DAYS = 30;
    /**
     * 下单意愿强烈人数的pageCode
     */
    private static final String WANT_PAGE_CODE = "10001,10002,10003,10004,10009,10010,10011,10012,10014";

    private final OdpsUtil odpsUtil;
    private final IndicatorBeforeSaleService indicatorBeforeSaleService;


    //不用重复执行的定时任务
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorBeforeSaleDO> collectMap = new HashMap<>();
        countPvAndUv(ds, sTime, eTime, collectMap);
        countMoreThanNCount(ds, sTime, eTime, collectMap);
        countLtNhRegisterCount(ds, sTime, eTime, collectMap);
        countOrderIntentionNumber(ds, sTime, eTime, collectMap);
        countOrderCount(ds, sTime, eTime, collectMap);
        //一天天计算取消下单人数
        List<LocalDateTime> dateTimeList = DateUtils.getDateTimeList(DateUtils.stringToDate(sTime),
                DateUtils.stringToDate(eTime));
        for (LocalDateTime time : dateTimeList) {
            LocalDateTime startTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MAX);
            countCancelOrderCount(ds, DateUtils.dateToString(startTime), DateUtils.dateToString(endTime), collectMap);
            countRegisterAndNotOrder(ds, DateUtils.dateToString(startTime), DateUtils.dateToString(endTime),
                    collectMap);
        }
        countServiceCount(ds, sTime, eTime, true, collectMap);
        countServiceCount(ds, sTime, eTime, false, collectMap);
        countRiskAllowRefusedCount(ds, sTime, eTime, collectMap);
        countRiskAllowPassCount(ds, sTime, eTime, collectMap);
        countGtNseAndRiskRefuseCount(ds, sTime, eTime, collectMap);
        countRiskApproveAndNotPay(ds, sTime, eTime, collectMap);
        for (IndicatorBeforeSaleDO value : collectMap.values()) {
            value.setRiskExecCount(value.getRiskExecCountOne() + value.getRiskExecCountTwo() + value.getRiskExecCountThree());
            indicatorBeforeSaleService.saveOrUpdate(value);
        }
    }


    /**
     * 计算pv uv
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void countPvAndUv(String ds, String sTime, String eTime, Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = "select  mini_type,count(1)           pv, " +
                "        count(distinct customer_third_id) uv, " +
                "        to_char(report_time,'yyyy-mm-dd') day " +
                " from dataview_track_enter_applets " +
                " where action_type = 1 " +
                "  and report_time between '" + sTime + "' and '" + eTime + "' " +
                " and ds = " + ds +
                " group by mini_type,to_char(report_time,'yyyy-mm-dd');";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setPvCount(Integer.valueOf(record.getString("pv")));
            updateDO.setUvCount(Integer.valueOf(record.getString("uv")));
        }
    }


    /**
     * 统计停留时长大于N秒的用户数（去重）
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void countMoreThanNCount(String ds, String sTime, String eTime,
                                    Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select  a.day " +
                "        ,a.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  mini_type,to_char(report_time, 'yyyy-mm-dd') as day " +
                "                    ,customer_third_id " +
                "            from    dataview_track_enter_applets " +
                "            where   action_type = 2 " +
                "            and     enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "            and     ds = " + ds +
                "            and     report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            group by mini_type,to_char(report_time, 'yyyy-mm-dd') " +
                "                     ,customer_third_id " +
                "            having  sum(keep_alive_time) > " + RESIDE_N_MILL_SECOND +
                "        ) a " +
                " group by a.mini_type " +
                "         ,a.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setGtNseCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 统计驻留N小时内注册用户数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                        inner join ( " +
                "                   select customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd') day " +
                "                   from dataview_track_enter_applets " +
                "                   where action_type = 2 " +
                "            and     enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "                     and ds = " + ds +
                "                     and report_time between '" + sTime + "' " +
                "                       and '" + eTime + "' " +
                "                   group by mini_type, to_char(report_time, 'yyyy-mm-dd') " +
                "                          , customer_third_id " +
                "                   having sum(keep_alive_time) > " + RESIDE_N_MILL_SECOND +
                "               ) b on a.mini_type = b.mini_type and a.customer_third_id = b.customer_third_id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' " +
                "                   and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                " 'yyyy-mm-dd') " +
                " where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + N_MINUTES * 60 +
                "  and b.ds =  " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 统计已注册用户中今天未下单人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void countRegisterAndNotOrder(String ds, String sTime, String eTime,
                                         Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select b.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct a.id) count " +
                "from rent_customer a " +
                "         inner join dataview_track_enter_applets b " +
                "                    on get_json_object(a.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                " where a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.id not in ( " +
                "    select customer_id " +
                "    from rent_order " +
                "    where create_time between '" + sTime + "' " +
                "        and '" + eTime + "' " +
                "      and ds = " + ds +
                ") " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                " group by b.mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterAndNotOrder(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 统计下单意愿强烈人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countOrderIntentionNumber(String ds, String sTime, String eTime, Map<String,
            IndicatorBeforeSaleDO> collectMap) {
        String sql = "select  a.day,a.mini_type,count(1) as count  " +
                "                 from    (  " +
                "                             select  min(b.mini_type) mini_type,to_char(b.report_time, 'yyyy-mm-dd')" +
                " as day  " +
                "                             from    dataview_track_enter_applets b " +
                "                             inner join rent_customer c    " +
                "                                  on get_json_object(c.bindinfo, '$[0].miniUserId') = b" +
                ".customer_third_id " +
                "                             where   b.action_type = 2  " +
                "                             and b.enter_page_code in (" + WANT_PAGE_CODE + ") " +
                "                             and     b.ds = " + ds +
                "                             and     c.ds = " + ds +
                "                             and     b.report_time between '" + sTime + "' " +
                "                                and '" + eTime + "' " +
                "                             group by to_char(b.report_time, 'yyyy-mm-dd')  " +
                "                                      ,c.id  " +
                "                             having  sum(b.keep_alive_time) >  " + WANT_N_MILL_SECOND +
                "                         ) a  " +
                "                         group by a.mini_type,a.day;";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            //判断map中是否存在
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setIntentionCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 下单人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countOrderCount(String ds, String sTime, String eTime, Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select mini_type,day,count(1) count from ( " +
                "    select min(mini_type) mini_type,to_char(create_time,'yyyy-mm-dd') day " +
                "        from rent_order  " +
                "        where is_deleted = 0 " +
                "        and merchant_id=100 " +
                "        and parent_id = 0 " +
                "        and type = 1 " +
                "        and biz_type = 2 " +
                "        and create_time between '" + sTime + "' and '" + eTime + "' " +
                "        and ds = " + ds +
                "        group by customer_id,to_char(create_time,'yyyy-mm-dd') " +
                ") group by mini_type,day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setOrderCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 下单意愿强烈的人数当中未下单人数（这个只能一天天查）
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countCancelOrderCount(String ds, String sTime, String eTime,
                                      Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select  a.day " +
                "        ,a.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  b.mini_type " +
                "                    ,to_char(report_time, 'yyyy-mm-dd') as day " +
                "            from    dataview_track_enter_applets b inner " +
                "            join    rent_customer c " +
                "            on      get_json_object(c.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "            where   b.action_type = 2 " +
                "            and     c.id not in ( " +
                "                select customer_id from rent_order  " +
                "                    where is_deleted = 0  " +
                "                    and type = 1  " +
                "                    and parent_id = 0  " +
                "                    and biz_type = 2  " +
                "                    and merchant_id = 100  " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                    and ds = " + ds +
                "                    group by customer_id " +
                "            ) " +
                "            and     b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "            and     b.ds = " + ds +
                "            and     c.ds = " + ds +
                "            and     b.report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            group by b.mini_type " +
                "                     ,to_char(b.report_time, 'yyyy-mm-dd') " +
                "                     ,c.id " +
                "            having  sum(b.keep_alive_time) > " + WANT_N_MILL_SECOND +
                "        ) a " +
                "group by a.mini_type " +
                "         ,a.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setCancelCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控准入被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countRiskAllowRefusedCount(String ds, String sTime, String eTime,
                                           Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select  mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) " +
                "count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "         inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and d.result = 5 " +
                "  and d.type = 2 " +
                "  and e.rulestage = 1 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and e.ds = " + ds +
                "  group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskRefuseCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控准入通过人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countRiskAllowPassCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    b.rate_config_type, " +
                "    IF(ISNOTNULL(b.ext_json) and b.repayment_term=3,'1','0') as forcedconversion, " +
                "        count(distinct a.customer_id) count " +
                "  from rent_order a " +
                "       inner join rent_order_finance_detail b on a.id=b.order_id " +
                " where a.id not in ( " +
                "     select a.id " +
                "        from rent_order a " +
                "                inner join cl_loan b on a.no = b.loanno " +
                "                inner join serial_no c on b.loanno = c.businessNo " +
                "                inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "                inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "        where a.is_deleted = 0 " +
                "        and a.merchant_id = 100 " +
                "        and a.parent_id = 0 " +
                "        and a.type = 1 " +
                "        and a.biz_type = 2 " +
                "        and a.create_time between '" + sTime + "' and  '" + eTime + "' " +
                "        and d.result = 5 " +
                "        and d.type = 2 " +
                "        and e.rulestage = 1 " +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "        and c.ds = " + ds +
                "        and d.ds = " + ds +
                "        and e.ds = " + ds +
                " ) " +
                " and a.ds = " + ds +
                " and b.ds = " + ds +
                " and a.create_time between '" + sTime + "' and  '" + eTime + "' " +
                " group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd')," +
                "    IF(ISNOTNULL(b.ext_json) and b.repayment_term=3,'1','0'), " +
                "    b.rate_config_type;";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                updateDO.setRiskExecCountThree(Optional.ofNullable(updateDO.getRiskExecCountThree()).orElse(0) + count);
            } else if (rateConfigType == 10) {
                updateDO.setRiskExecCountOne(Optional.ofNullable(updateDO.getRiskExecCountOne()).orElse(0) + count);
            } else {
                updateDO.setRiskExecCountTwo(Optional.ofNullable(updateDO.getRiskExecCountTwo()).orElse(0) + count);
            }
        }
    }


    /**
     * 驻留N+人数中，风控准入被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countGtNseAndRiskRefuseCount(String ds, String sTime, String eTime,
                                             Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select A.day, A.mini_type, count(1) as count " +
                "from ( " +
                "         select B.mini_type " +
                "              , to_char(B.report_time, 'yyyy-mm-dd') as day " +
                "              , B.customer_third_id " +
                "         from dataview_track_enter_applets B inner join rent_customer C " +
                "                             on get_json_object(C.bindinfo, '$[0].miniUserId') = B" +
                ".customer_third_id " +
                "         where B.action_type = 2 " +
                "            and     enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "           and B.ds = " + ds +
                "           and C.ds = " + ds +
                "           and c.id in  ( " +
                "                    select  d.customer_id  " +
                "                    from rent_order d " +
                "                            inner join cl_loan e on d.no = e.loanno " +
                "                            inner join serial_no f on e.loanno = f.businessNo " +
                "                            inner join rc_risk_access_rule_result_2023 g on f.id = g.serialnoId " +
                "                            inner join rc_risk_strategy_rule_set h on g.ruleid = h.id " +
                "                    where d.is_deleted = 0 " +
                "                    and d.merchant_id = 100 " +
                "                    and d.parent_id = 0 " +
                "                    and d.type = 1 " +
                "                    and d.biz_type = 2 " +
                "                    and d.create_time between '" + sTime + "' and '" + eTime + "' " +
                "                    and g.result = 5 " +
                "                    and g.type = 2 " +
                "                    and h.rulestage = 1 " +
                "                    and d.ds = " + ds +
                "                    and e.ds = " + ds +
                "                    and f.ds = " + ds +
                "                    and g.ds = " + ds +
                "                    and h.ds = " + ds +
                "           ) " +
                "           and B.report_time between '" + sTime + "' " +
                "             and '" + eTime + "' " +
                "         group by B.mini_type, to_char(B.report_time, 'yyyy-mm-dd') " +
                "                , B.customer_third_id " +
                "         having sum(B.keep_alive_time) > " + RESIDE_N_MILL_SECOND +
                "     ) A " +
                " group by A.mini_type, A.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setGtNseAndRiskRefuseCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 驻留N+人数中，服务中客户
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @return
     */
    public void countServiceCount(String ds, String sTime, String eTime, Boolean inServiceFlag,
                                  Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select a.day, a.mini_type, count(1) as count " +
                "from ( " +
                "         select b.mini_type " +
                "              , to_char(report_time, 'yyyy-mm-dd') as day " +
                "         from dataview_track_enter_applets b " +
                "                  inner join rent_customer c " +
                "                             on get_json_object(c.bindinfo, '$[0].miniUserId') = b" +
                ".customer_third_id " +
                "                  inner join rent_order d on c.id = d.customer_id " +
                "         where b.action_type = 2  and  enter_page_code in (10001,10002,10003,10004,10009,10010," +
                "10011,10012,10014) " +
                "            and b.ds = " + ds +
                "            and c.ds = " + ds +
                "            and d.ds = " + ds +
                "            and b.report_time between '" + sTime + "' " +
                "            and '" + eTime + "' " +
                "            and d.merchant_id = 100 " +
                "            and d.type = 1 " +
                "            and d.biz_type = 2 " +
                "            and d.is_deleted = 0 " +
                "            and d.payment_time is not null " +
                "            and d.termination != 5 " +
                "            and d.status " + (inServiceFlag ? "not" : "") + " in (220,330)" +
                "         group by b.mini_type, to_char(b.report_time, 'yyyy-mm-dd') " +
                "                , b.customer_third_id " +
                "         having sum(b.keep_alive_time) > " + RESIDE_N_MILL_SECOND +
                "     ) a " +
                "group by a.mini_type, a.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (inServiceFlag) {
                updateDO.setResideInServiceCount(Integer.valueOf(record.getString("count")));
            } else {
                updateDO.setResideOutServiceCount(Integer.valueOf(record.getString("count")));
            }
        }
    }

    /**
     * 驻留N+人数中，风控通过未付/风控N天之前被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRiskApproveAndNotPay(String ds, String sTime, String eTime,
                                           Map<String, IndicatorBeforeSaleDO> collectMap) {
        String sql = " select  A.day " +
                "        ,A.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  B.mini_type " +
                "                    ,to_char(B.report_time, 'yyyy-mm-dd') as day " +
                "                    ,B.customer_third_id " +
                "            from    dataview_track_enter_applets B inner " +
                "            join    rent_customer C " +
                "            on      get_json_object(C.bindinfo, '$[0].miniUserId') = B.customer_third_id " +
                "            inner join ( " +
                "                           select  a.customer_id " +
                "                                   ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "                                   ,a.create_time " +
                "                           from    rent_order a inner " +
                "                           join    cl_loan b " +
                "                           on      a.no = b.loanno " +
                "                           where   a.is_deleted = 0 " +
                "                           and     a.merchant_id = 100 " +
                "                           and     a.parent_id = 0 " +
                "                           and     a.type = 1 " +
                "                           and     a.biz_type = 2 " +
                "                           and     a.create_time between '" + sTime + "' " +
                "                           and     '" + eTime + "' " +
                "                           and     b.riskstatus = 15 " +
                "                           and     a.ds = " + ds +
                "                           and     b.ds = " + ds +
                "                       ) D " +
                "            on      D.customer_id = C.id " +
                "            and     D.day = to_char(B.report_time, 'yyyy-mm-dd') inner " +
                "            join    ( " +
                "                       select min(risktime) risktime,customer_id from ( " +
                "                            select  b.risktime " +
                "                                ,a.customer_id " +
                "                                from    rent_order a inner " +
                "                                join    cl_loan b " +
                "                                on      a.no = b.loanno " +
                "                                where   a.is_deleted = 0 " +
                "                                and     a.merchant_id = 100 " +
                "                                and     a.parent_id = 0 " +
                "                                and     a.type = 1 " +
                "                                and     a.biz_type = 2 " +
                "                                and     b.riskstatus = 20 " +
                "                                and     a.payment_time is null " +
                "                                and     a.ds = " + ds +
                "                                and     b.ds = " + ds +
                "                                order by b.risktime asc " +
                "                       ) group by customer_id " +
                "                    ) E " +
                "            on      C.id = E.customer_id " +
                "            where   B.action_type = 2 " +
                "            and     enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "            and     B.ds = " + ds +
                "            and     C.ds = " + ds +
                "            and     datediff(D.create_time,E.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "            and     B.report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            group by B.mini_type " +
                "                     ,to_char(B.report_time, 'yyyy-mm-dd') " +
                "                     ,B.customer_third_id " +
                "            having  sum(B.keep_alive_time) > " + IndicatorBeforeSaleBusiness.RESIDE_N_MILL_SECOND +
                "        ) A " +
                "group by A.mini_type " +
                "         ,A.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setResideRiskApprovedNotPay(Integer.valueOf(record.getString("count")));
        }
    }


    private void initMap(Map<String, IndicatorBeforeSaleDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorBeforeSaleDO ivd = new IndicatorBeforeSaleDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setPvCount(0);
            ivd.setUvCount(0);
            ivd.setGtNseCount(0);
            ivd.setLtNhRegisterCount(0);
            ivd.setRegisterAndNotOrder(0);
            ivd.setIntentionCount(0);
            ivd.setOrderCount(0);
            ivd.setCancelCount(0);
            ivd.setRiskRefuseCount(0);
            ivd.setRiskExecCount(0);
            ivd.setRiskExecCountOne(0);
            ivd.setRiskExecCountTwo(0);
            ivd.setRiskExecCountThree(0);
            ivd.setResideRiskApprovedNotPay(0);
            ivd.setResideInServiceCount(0);
            ivd.setResideOutServiceCount(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }


}