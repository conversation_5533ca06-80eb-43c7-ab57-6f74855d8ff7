package qnvip.data.overview.business.goods;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.util.OdpsUtil;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsInsightBusiness {

    private final OdpsUtil odpsUtil;

    /**
     * 商品洞察
     * @param fromTime
     * @param toTime
     */
    public void goodsOverallDataFormat(String fromTime,
                                       String toTime) {

    }
}
