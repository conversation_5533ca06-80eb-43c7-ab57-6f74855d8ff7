package qnvip.data.overview.business.oa;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.oa.OAWarnAdvanceStatisticsDO;
import qnvip.data.overview.domain.oa.OAWarnCurrentStatisticsDO;
import qnvip.data.overview.domain.oa.OAWarnOverdueStatisticsDO;
import qnvip.data.overview.domain.oa.OAWarnRepayStatisticsDO;
import qnvip.data.overview.service.oa.OAWarnAdvanceStatisticsService;
import qnvip.data.overview.service.oa.OAWarnCurrentStatisticsService;
import qnvip.data.overview.service.oa.OAWarnOverdueStatisticsService;
import qnvip.data.overview.service.oa.OAWarnRepayStatisticsService;
import qnvip.data.overview.util.JdbcUtil;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.rent.common.config.alarm.DingDingRobot;

import java.math.BigDecimal;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 还款提醒统计
 *
 * <AUTHOR>
 * @time 2021年12月28日 11:05 上午
 */
@Component
@RequiredArgsConstructor
@Slf4j
public class OAWarnStatisticsBusiness {

    private final OdpsUtil odpsUtil;
    private final OAWarnRepayStatisticsService oaWarnRepayStatisticsService;
    private final OAWarnOverdueStatisticsService oaWarnOverdueStatisticsService;
    private final OAWarnCurrentStatisticsService oaWarnCurrentStatisticsService;
    private final OAWarnAdvanceStatisticsService oaWarnAdvanceStatisticsService;
    @Autowired
    private RedisTemplate redisTemplate;
    private final DingDingRobot dingDingRobot;

    private final String HARMFUL_ORDERS = "harmful.orders.";
    private final String BI_WARN_SERVICE = "oauth2:52:dept";
    private final String HARMFUL_SERVICE = "oauth2:54:dept";

    @Value("${overdue.remindDay:30}")
    private String overdueDays;

    /**
     * 回款数据统计
     *
     * @return
     */
    public void repayStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String oids = null;
            if (!CollectionUtils.isEmpty(harmfulOrders)) {
                List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                        Collectors.toList());
                oids = StringUtils.join(harmfulOIds, ",");
            }
            String uids = StringUtils.join(userIds, ",");
            try {
                StringBuilder sb = new StringBuilder("select  '").append(processDate).append("' as date\n"
                        + "        ,o.mini_type\n"
                        + "        ,ifnull(al.collection_personnel_id,0) as service_id\n"
                        + "        ,ifnull(al.collection_personnel_name,'') as service_name\n"
                        + "        ,count(case when ff.order_id is not null then 1 end) as repay_count\n"
                        + "        ,round(sum(ff.flow_amt),2) as repay_amt\n"
                        + "        ,count(case when ff.withhold > 0 then 1 end) as withhold_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when ff.withhold > 0 then ff.withhold_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as withhold_amt\n"
                        + "        ,count(case when o.status = 220 and o.parent_id =0 then 1 end) as buyout_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when o.status = 220 and o.parent_id = 0 then (case when ff.buyout_amt >= 0 then ff.buyout_amt else d.buyout_amt end) end),0)\n"
                        + "            ,2\n"
                        + "        ) as buyout_amt\n"
                        + "        ,count(case when fd.order_id is not null then 1 end) as bond_deduc_count\n"
                        + "        ,round(ifnull(sum(fd.deduc_bond),0),2) as bond_deduc_amt\n"
                        + "        ,round(ifnull(sum(case when o.status=220  then d.surplus_bond_amt end),0),2) as surplus_bond_amt\n"
                        + "        ,round(ifnull(sum(ff.flow_overdue_fine),0),2) as flow_overdue_fine\n"
                        + "        ,round(ifnull(sum(pp.reduce_amt),0),2) as reduce_amt\n"
                        + "        ,round(ifnull(sum(ff.discount_amt),0),2) as discount_amt\n"
                        + "        ,count(case when rco1.order_id is not null then 1 end) as called_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when rco1.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as called_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_un_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is not null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as called_un_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_amt\n"
                        + "from    rent_order o\n"
                        + "left join rent_order_finance_detail d\n"
                        + "on      d.order_id = o.id left\n"
                        + "join    rent_order_infomore oi\n"
                        + "on      oi.order_id = o.id\n"
                        + "inner join (\n"
                        + "               select  order_id\n"
                        + "                       ,round(\n"
                        + "                           sum(\n"
                        + "                               case    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount'  \n"
                        + "                                       else flow_amt \n"
                        + "                               end\n"
                        + "                           )\n"
                        + "                           ,2\n"
                        + "                       ) as flow_amt\n"
                        + "                       ,sum(discount_amt) as discount_amt\n"
                        + "                       ,round(\n"
                        + "                           sum(\n"
                        + "                               case    when ext <> '' and ext -> '$.overdueFine' > 0 then ext -> '$.overdueFine' \n"
                        + "                               end\n"
                        + "                           )\n"
                        + "                           ,2\n"
                        + "                       ) as flow_overdue_fine\n"
                        + "                       ,count(case when alipay_pay_type not in (0,1) then 1 end) as withhold\n"
                        + "                       ,round(\n"
                        + "                           sum(\n"
                        + "                               case    when alipay_pay_type not in (0,1) then (case\n"
                        + "                                       when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                       else flow_amt \n"
                        + "                               end) end\n"
                        + "                           )\n"
                        + "                           ,2\n"
                        + "                       ) as withhold_amt\n"
                        + "               ,(case when ext <> '' and biz_type = 3 then ifnull(ext -> '$.buyoutActAmt',0) + ifnull(ext -> '$.buyoutDeductionAmount',0) + ifnull(ext -> '$.buyoutCouponAmount',0) + ifnull(ext -> '$.discountReturnAmt',0) end) as buyout_amt\n"
                        + "               from    rent_order_flow\n"
                        + "               where   flow_type = 1\n"
                        + "               and     parent_uid = ''\n"
                        + "               and     pay_status = 10\n"
                        + "               and     is_deleted = 0\n"
                        + "               and     biz_type in (1,3,10,24,25)\n"
                        + "               and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "               group by order_id\n"
                        + "           ) ff\n"
                        + "on      ff.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "                      ,sum(flow_amt) as deduc_bond\n"
                        + "              from    rent_order_flow\n"
                        + "              where   flow_type = 1\n"
                        + "              and     parent_uid = ''\n"
                        + "              and     biz_type = 23\n"
                        + "              and     is_deleted = 0\n"
                        + "              and     pay_status = 10\n"
                        + "              group by order_id\n"
                        + "          ) fd\n"
                        + "on      fd.order_id = o.id  left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "                    ,sum(case when repay_status = 5 then reduce_amt end) as reduce_amt\n"
                        + "            from    rent_order_repayment_plan\n"
                        + "            where   date_format(real_repay_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            group by order_id\n"
                        + "        ) pp\n"
                        + "on      pp.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  a.order_id\n"
                        + "                      ,collection_personnel_id\n"
                        + "                      ,collection_personnel_name\n"
                        + "              from    rent_order_repayment_plan_allot a\n"
                        + "              left join rent_order_repayment_plan p\n"
                        + "              on      p.id = a.repayment_plan_id\n"
                        + "              where   a.id in (select max(id) from rent_order_repayment_plan_allot where date_format(update_time, '%Y-%m-%d') <='").append(processDate).append("'  group by repayment_plan_id)\n"
                        + "              and date_format(p.real_repay_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and a.collection_personnel_id in (").append(uids).append(")\n"
                        + "              group by a.order_id\n"
                        + "                       ,a.collection_personnel_id\n"
                        + "                       ,a.collection_personnel_name\n"
                        + "          ) al\n"
                        + "on      al.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "            from    rent_call_record c\n"
                        + "            left join rent_order o\n"
                        + "            on      o.id = c.order_id\n"
                        + "            where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            and     user_id in (").append(uids).append(")\n"
                        + "            and     call_type = 'dialout'\n"
                        + "            and     order_id > 0\n"
                        + "            and     state = 'dealing'\n"
                        + "            and     call_state in ('Unlink','Hangup')\n"
                        + "            group by order_id\n"
                        + "        ) rco1\n"
                        + "on      rco1.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "              from    rent_call_record c\n"
                        + "              left join rent_order o\n"
                        + "              on      o.id = c.order_id\n"
                        + "              where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and     user_id in (").append(uids).append(")\n"
                        + "              and     call_type = 'dialout'\n"
                        + "              and     order_id > 0\n"
                        + "              and     (state <> 'dealing' or call_state not in ('Unlink','Hangup'))\n"
                        + "              group by order_id\n"
                        + "          ) rco2\n"
                        + "on      rco2.order_id = o.id\n"
                        + "where   oi.`lawsuit_status` = 1\n"
                        + "and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "and     o.type = 1\n"
                        + "and     o.biz_type = 2\n"
                        + "and     o.merchant_id = 100\n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.id not in (").append(oids).append(")\n");
                    sb.append(" and o.parent_id not in (").append(oids).append(")\n");
                }
                sb.append(" group by o.mini_type\n"
                        + "         ,al.collection_personnel_id\n"
                        + "         ,al.collection_personnel_name\n"
                        + ";\n");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("回款数据统计sql：repayStatistics= {}", sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.info("===查询完毕===");
                if (rs == null) {
                    return;
                }
                List<OAWarnRepayStatisticsDO> list = new ArrayList<>();
                while (rs.next()) {
                    OAWarnRepayStatisticsDO st = new OAWarnRepayStatisticsDO();
                    st.setStatisticsDate(rs.getString("date"));
                    st.setMiniType(Integer.valueOf(rs.getString("mini_type")));
                    st.setServiceId(Integer.valueOf(rs.getString("service_id")));
                    st.setServiceName(rs.getString("service_name"));
                    st.setRepayCount(Integer.valueOf(rs.getString("repay_count")));
                    st.setRepayAmt(new BigDecimal(rs.getString("repay_amt")));
                    st.setWithholdCount(Integer.valueOf(rs.getString("withhold_count")));
                    st.setWithholdAmt(new BigDecimal(rs.getString("withhold_amt")));
                    st.setBuyoutCount(Integer.valueOf(rs.getString("buyout_count")));
                    st.setBuyoutAmt(new BigDecimal(rs.getString("buyout_amt")));
                    st.setDeducBondCount(Integer.valueOf(rs.getString("bond_deduc_count")));
                    st.setDeducBondAmt(new BigDecimal(rs.getString("bond_deduc_amt")));
                    st.setSurplusBondAmt(new BigDecimal(rs.getString("surplus_bond_amt")));
                    st.setOverdueFine(new BigDecimal(rs.getString("flow_overdue_fine")));
                    st.setReduceAmt(new BigDecimal(rs.getString("reduce_amt")));
                    st.setDiscountAmt(new BigDecimal(rs.getString("discount_amt")));
                    st.setCalledDealCount(Integer.valueOf(rs.getString("called_deal_count")));
                    st.setCalledDealAmt(new BigDecimal(rs.getString("called_deal_amt")));
                    st.setCalledUnDealCount(Integer.valueOf(rs.getString("called_un_deal_count")));
                    st.setCalledUnDealAmt(new BigDecimal(rs.getString("called_un_deal_amt")));
                    st.setUnCalledCount(Integer.valueOf(rs.getString("un_called_count")));
                    st.setUnCalledAmt(new BigDecimal(rs.getString("un_called_amt")));
                    list.add(st);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    oaWarnRepayStatisticsService.saveBatch(list);
                }
            } catch (Exception e) {
                log.error("还款提醒-回款统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("还款提醒-回款统计SQL异常！" + e.getMessage());
            }
        }
    }

    /**
     * 提前提醒数据统计
     *
     * @return
     */
    public void advanceStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        String oids = null;
        if (!CollectionUtils.isEmpty(harmfulOrders)) {
            List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                    Collectors.toList());
            oids = StringUtils.join(harmfulOIds, ",");
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String uids = StringUtils.join(userIds, ",");
            try {
                StringBuilder sb = new StringBuilder("select  '").append(processDate).append("' as date\n"
                        + "        ,o.mini_type\n"
                        + "        ,ifnull(al.collection_personnel_id,0) as service_id\n"
                        + "        ,ifnull(al.collection_personnel_name,'') as service_name\n"
                        + "        ,count(o.id) as total_count\n"
                        + "        ,round(ifnull(sum(pp.total_amt),0),2) as total_amt\n"
                        + "        ,count(case when ff.order_id is not null then 1 end) as pay_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when ff.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as pay_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is not null or rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_count\n"
                        + "        ,count(case when rco1.order_id is not null then 1 end) as called_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when rco1.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as called_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_un_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is not null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as called_un_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_count\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null and c.hq_flag= 0 then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_un_hq_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null and c.hq_flag = 0 then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_un_hq_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null and c.hq_flag= 1 then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_hq_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null and c.hq_flag = 1 then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_hq_amt\n"
                        + "        ,count(case when o.status = 220 and o.parent_id =0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then 1 end) as buyout_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when o.status = 220 and o.parent_id = 0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then (case when ff.buyout_amt >=0 then ff.buyout_amt else d.buyout_amt end) end ),0)\n"
                        + "            ,2\n"
                        + "        ) as buyout_amt\n"
                        + "        ,round(ifnull(sum(fd.deduc_bond),0),2) as deduc_bond_amt\n"
                        + "        ,round(ifnull(sum(ff.discount_amt),0),2) as discount_amt\n"
                        + "from    rent_order o\n"
                        + "left join rent_order_finance_detail d\n"
                        + "on      d.order_id = o.id left\n"
                        + "join    rent_order_infomore oi\n"
                        + "on      oi.order_id = o.id\n"
                        + "left join rent_customer c\n"
                        + "on      c.id = o.customer_id\n"
                        + "inner join (\n"
                        + "               select  p.order_id\n"
                        + "                       ,sum(p.capital) as `total_amt`\n"
                        + "                       ,sum(\n"
                        + "                           case    when p.repay_status = 5 then p.`real_repay_capital` \n"
                        + "                           end\n"
                        + "                       ) as real_repay_amt\n"
                        + "               from    rent_order_repayment_plan p\n"
                        + "               where   p.is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(LocalDate.parse(processDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).toString()).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "               group by p.order_id\n"
                        + "           ) pp\n"
                        + "on      pp.order_id = o.id  left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_amt\n"
                        + "                    ,sum(discount_amt) as discount_amt,(case when ext <> '' and biz_type = 3 then ifnull(ext -> '$.buyoutActAmt',0) + ifnull(ext -> '$.buyoutDeductionAmount',0) + ifnull(ext -> '$.buyoutCouponAmount',0) + ifnull(ext -> '$.discountReturnAmt',0) end) as buyout_amt\n"
                        + "            from    rent_order_flow\n"
                        + "            where   flow_type = 1\n"
                        + "            and     parent_uid = ''\n"
                        + "            and     pay_status = 10\n"
                        + "            and     is_deleted = 0\n"
                        + "            and     biz_type in (1,3,10,24,25)\n"
                        + "            and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            group by order_id\n"
                        + "        ) ff\n"
                        + "on      ff.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "                      ,sum(flow_amt) as deduc_bond\n"
                        + "              from    rent_order_flow\n"
                        + "              where   flow_type = 1\n"
                        + "              and     parent_uid = ''\n"
                        + "              and     biz_type = 23\n"
                        + "              and     is_deleted = 0\n"
                        + "              and     pay_status = 10\n"
                        + "              and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              group by order_id\n"
                        + "          ) fd\n"
                        + "on      fd.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "            from    rent_call_record c\n"
                        + "            left join rent_order o\n"
                        + "            on      o.id = c.order_id\n"
                        + "            where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            and     user_id in (").append(uids).append(")\n"
                        + "            and     call_type = 'dialout'\n"
                        + "            and     order_id > 0\n"
                        + "            and     state = 'dealing'\n"
                        + "            and     call_state in ('Unlink','Hangup')\n"
                        + "            group by order_id\n"
                        + "        ) rco1\n"
                        + "on      rco1.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "              from    rent_call_record c\n"
                        + "              left join rent_order o\n"
                        + "              on      o.id = c.order_id\n"
                        + "              where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and     user_id in (").append(uids).append(")\n"
                        + "              and     call_type = 'dialout'\n"
                        + "              and     order_id > 0\n"
                        + "              and     (state <> 'dealing' or call_state not in ('Unlink','Hangup'))\n"
                        + "              group by order_id\n"
                        + "          ) rco2\n"
                        + "on      rco2.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  a.order_id\n"
                        + "                    ,collection_personnel_id\n"
                        + "                    ,collection_personnel_name\n"
                        + "            from    rent_order_repayment_plan_allot a\n"
                        + "            left join rent_order_repayment_plan p\n"
                        + "            on      p.id = a.repayment_plan_id\n"
                        + "            where   a.id in (select max(id) from rent_order_repayment_plan_allot where date_format(update_time, '%Y-%m-%d') ='").append(processDate).append("'  group by repayment_plan_id)\n"
                        + "            and date_format(p.repay_date, '%Y-%m-%d') = '").append(LocalDate.parse(processDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).toString()).append("'\n"
                        + "               and     (\n"
                        + "                               (p.repay_status = 5 and date_format(p.real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  p.repay_status = 1\n"
                        + "                       )\n"
                        + "              and a.collection_personnel_id in (").append(uids).append(")\n"
                        + "            group by a.order_id\n"
                        + "                     ,a.collection_personnel_id\n"
                        + "                     ,a.collection_personnel_name\n"
                        + "        ) al\n"
                        + "on      al.order_id = o.id\n"
                        + "where   o.is_deleted = 0 and oi.`lawsuit_status` = 1\n"
                        + "and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "and     o.type = 1\n"
                        + "and     o.biz_type = 2\n"
                        + "and     o.merchant_id = 100\n"
                        + "and     o.id not in (select order_id from rent_order_repayment_plan where (repay_status = 1 or (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')) and overdue_days > 0  group by order_id)\n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.parent_id not in (").append(oids).append(")\n");
                }

                sb.append("group by o.mini_type\n"
                        + "         ,al.collection_personnel_id\n"
                        + "         ,al.collection_personnel_name\n"
                        + ";\n");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return;
                }
                List<OAWarnAdvanceStatisticsDO> list = new ArrayList<>();
                while (rs.next()) {
                    OAWarnAdvanceStatisticsDO st = new OAWarnAdvanceStatisticsDO();
                    st.setStatisticsDate(rs.getString("date"));
                    st.setMiniType(Integer.valueOf(rs.getString("mini_type")));
                    st.setServiceId(Integer.valueOf(rs.getString("service_id")));
                    st.setServiceName(rs.getString("service_name"));
                    st.setTotalCount(Integer.valueOf(rs.getString("total_count")));
                    if (st.getServiceId() == 0) {
                        st.setTotalAllotCount(st.getTotalCount());
                    }
                    st.setTotalAmt(new BigDecimal(rs.getString("total_amt")));
                    st.setPayCount(Integer.valueOf(rs.getString("pay_count")));
                    st.setPayAmt(new BigDecimal(rs.getString("pay_amt")));
                    st.setCalledCount(Integer.valueOf(rs.getString("called_count")));
                    st.setCalledDealCount(Integer.valueOf(rs.getString("called_deal_count")));
                    st.setCalledDealAmt(new BigDecimal(rs.getString("called_deal_amt")));
                    st.setCalledUnDealCount(Integer.valueOf(rs.getString("called_un_deal_count")));
                    st.setCalledUnDealAmt(new BigDecimal(rs.getString("called_un_deal_amt")));
                    st.setUnCalledCount(Integer.valueOf(rs.getString("un_called_count")));
                    st.setUnCalledUnHqCount(Integer.valueOf(rs.getString("un_called_un_hq_count")));
                    st.setUnCalledUnHqAmt(new BigDecimal(rs.getString("un_called_un_hq_amt")));
                    st.setUnCalledHqCount(Integer.valueOf(rs.getString("un_called_hq_count")));
                    st.setUnCalledHqAmt(new BigDecimal(rs.getString("un_called_hq_amt")));
                    st.setBuyoutCount(Integer.valueOf(rs.getString("buyout_count")));
                    st.setBuyoutAmt(new BigDecimal(rs.getString("buyout_amt")));
                    st.setDeducBondAmt(new BigDecimal(rs.getString("deduc_bond_amt")));
                    st.setDiscountAmt(new BigDecimal(rs.getString("discount_amt")));
                    list.add(st);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    oaWarnAdvanceStatisticsService.saveBatch(list);
                }
            } catch (Exception e) {
                log.error("还款提醒-提前统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("还款提醒-提前统计SQL异常！" + e.getMessage());
            }

        }
    }

    /**
     * 到期提醒数据统计
     *
     * @return
     */
    public void currentStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        String oids = null;
        if (!CollectionUtils.isEmpty(harmfulOrders)) {
            List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                    Collectors.toList());
            oids = StringUtils.join(harmfulOIds, ",");
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String uids = StringUtils.join(userIds, ",");
            try {
                StringBuilder sb = new StringBuilder("select  '").append(processDate).append("' as date\n"
                        + "        ,o.mini_type\n"
                        + "        ,ifnull(al.collection_personnel_id,0) as service_id\n"
                        + "        ,ifnull(al.collection_personnel_name,'') as service_name\n"
                        + "        ,count(o.id) as total_count\n"
                        + "        ,round(ifnull(sum(pp.total_amt),0),2) as total_amt\n"
                        + "        ,count(case when ff.order_id is not null then 1 end) as pay_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when ff.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as pay_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is not null or rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_count\n"
                        + "        ,count(case when rco1.order_id is not null then 1 end) as called_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when rco1.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as called_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_un_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is not null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as called_un_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_amt\n"
                        + "        ,count(case when o.status = 220 and o.parent_id =0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then 1 end) as buyout_count\n"
                        + "        ,ifnull(\n"
                        + "            round(sum(case when o.status = 220 and o.parent_id = 0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then (case when ff.buyout_amt >=0 then ff.buyout_amt else d.buyout_amt end) end),2)\n"
                        + "            ,0\n"
                        + "        ) as buyout_amt\n"
                        + "        ,count(case when ff.withhold > 0 then 1 end) as withhold_count\n"
                        + "        ,round(ifnull(sum(ff.withhold_amt),0),2) as withhold_amt\n"
                        + "        ,ifnull(round(sum(fd.deduc_bond),2),0) as deduc_bond_amt\n"
                        + "        ,ifnull(round(sum(case when o.status = 220 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then d.surplus_bond_amt end),2),0) as surplus_bond_amt\n"
                        + "        ,ifnull(round(sum(ff.discount_amt),2),0) as discount_amt\n"
                        + "from    rent_order o\n"
                        + "left join rent_order_finance_detail d\n"
                        + "on      d.order_id = o.id left\n"
                        + "join    rent_order_infomore oi\n"
                        + "on      oi.order_id = o.id\n"
                        + "left join rent_customer c\n"
                        + "on      c.id = o.customer_id\n"
                        + "inner join (\n"
                        + "               select  p.order_id\n"
                        + "                       ,sum(p.capital) as `total_amt`\n"
                        + "                       ,sum(\n"
                        + "                           case    when p.repay_status in (5, 10) then p.`real_repay_capital` \n"
                        + "                           end\n"
                        + "                       ) as real_repay_amt\n"
                        + "               from    rent_order_repayment_plan p\n"
                        + "               where   p.is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "               group by p.order_id\n"
                        + "           ) pp\n"
                        + "on      pp.order_id = o.id  left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_amt\n"
                        + "                    ,sum(discount_amt) as discount_amt\n"
                        + "                    ,count(case when alipay_pay_type not in (0,1) then 1 end) as withhold\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when alipay_pay_type not in (0,1) then (case\n"
                        + "                                    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end) end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as withhold_amt,(case when ext <> '' and biz_type = 3 then ifnull(ext -> '$.buyoutActAmt',0) + ifnull(ext -> '$.buyoutDeductionAmount',0) + ifnull(ext -> '$.buyoutCouponAmount',0) + ifnull(ext -> '$.discountReturnAmt',0) end) as buyout_amt\n"
                        + "            from    rent_order_flow\n"
                        + "            where   flow_type = 1\n"
                        + "            and     parent_uid = ''\n"
                        + "            and     pay_status = 10\n"
                        + "            and     is_deleted = 0\n"
                        + "            and     biz_type in (1,3,10,24,25)\n"
                        + "            and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            group by order_id\n"
                        + "        ) ff\n"
                        + "on      ff.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "                      ,sum(flow_amt) as deduc_bond\n"
                        + "              from    rent_order_flow\n"
                        + "              where   flow_type = 1\n"
                        + "              and     parent_uid = ''\n"
                        + "              and     biz_type = 23\n"
                        + "              and     is_deleted = 0\n"
                        + "              and     pay_status = 10\n"
                        + "              and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              group by order_id\n"
                        + "          ) fd\n"
                        + "on      fd.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "            from    rent_call_record c\n"
                        + "            left join rent_order o\n"
                        + "            on      o.id = c.order_id\n"
                        + "            where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            and     user_id in (").append(uids).append(")\n"
                        + "            and     call_type = 'dialout'\n"
                        + "            and     order_id > 0\n"
                        + "            and     state = 'dealing'\n"
                        + "            and     call_state in ('Unlink','Hangup')\n"
                        + "            group by order_id\n"
                        + "        ) rco1\n"
                        + "on      rco1.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "              from    rent_call_record c\n"
                        + "              left join rent_order o\n"
                        + "              on      o.id = c.order_id\n"
                        + "              where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and     user_id in (").append(uids).append(")\n"
                        + "              and     call_type = 'dialout'\n"
                        + "              and     order_id > 0\n"
                        + "              and     (state <> 'dealing' or call_state not in ('Unlink','Hangup'))\n"
                        + "              group by order_id\n"
                        + "          ) rco2\n"
                        + "on      rco2.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  a.order_id\n"
                        + "                    ,collection_personnel_id\n"
                        + "                    ,collection_personnel_name\n"
                        + "            from    rent_order_repayment_plan_allot a\n"
                        + "            left join rent_order_repayment_plan p\n"
                        + "            on      p.id = a.repayment_plan_id\n"
                        + "            where   a.id in (select max(id) from rent_order_repayment_plan_allot where date_format(update_time, '%Y-%m-%d') ='").append(processDate).append("'  group by repayment_plan_id)\n"
                        + "            and date_format(p.repay_date, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (p.repay_status in (5, 10) and date_format(p.real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  p.repay_status = 1\n"
                        + "                       )\n"
                        + "              and a.collection_personnel_id in (").append(uids).append(")\n"
                        + "            group by a.order_id\n"
                        + "                     ,a.collection_personnel_id\n"
                        + "                     ,a.collection_personnel_name\n"
                        + "        ) al\n"
                        + "on      al.order_id = o.id\n"
                        + "where   o.is_deleted = 0 and oi.`lawsuit_status` = 1\n"
                        + "and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "and     o.type = 1\n"
                        + "and     o.biz_type = 2\n"
                        + "and     o.merchant_id = 100\n"
                        + "and     o.id not in (select order_id from rent_order_repayment_plan where (repay_status = 1 or (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')) and overdue_days > 1  group by order_id)\n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.parent_id not in (").append(oids).append(")\n");
                }

                sb.append(" group by o.mini_type\n"
                        + "         ,al.collection_personnel_id\n"
                        + "         ,al.collection_personnel_name\n"
                        + ";\n");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return;
                }
                List<OAWarnCurrentStatisticsDO> list = new ArrayList<>();
                while (rs.next()) {
                    OAWarnCurrentStatisticsDO st = new OAWarnCurrentStatisticsDO();
                    st.setStatisticsDate(rs.getString("date"));
                    st.setMiniType(Integer.valueOf(rs.getString("mini_type")));
                    st.setServiceId(Integer.valueOf(rs.getString("service_id")));
                    st.setServiceName(rs.getString("service_name"));
                    st.setTotalCount(Integer.valueOf(rs.getString("total_count")));
                    if (st.getServiceId() == 0) {
                        st.setTotalAllotCount(st.getTotalCount());
                    }
                    st.setTotalAmt(new BigDecimal(rs.getString("total_amt")));
                    st.setPayCount(Integer.valueOf(rs.getString("pay_count")));
                    st.setPayAmt(new BigDecimal(rs.getString("pay_amt")));
                    st.setCalledCount(Integer.valueOf(rs.getString("called_count")));
                    st.setCalledDealCount(Integer.valueOf(rs.getString("called_deal_count")));
                    st.setCalledDealAmt(new BigDecimal(rs.getString("called_deal_amt")));
                    st.setCalledUnDealCount(Integer.valueOf(rs.getString("called_un_deal_count")));
                    st.setCalledUnDealAmt(new BigDecimal(rs.getString("called_un_deal_amt")));
                    st.setUnCalledCount(Integer.valueOf(rs.getString("un_called_count")));
                    st.setUnCalledAmt(new BigDecimal(rs.getString("un_called_amt")));
                    st.setBuyoutCount(Integer.valueOf(rs.getString("buyout_count")));
                    st.setBuyoutAmt(new BigDecimal(rs.getString("buyout_amt")));
                    st.setWithholdCount(Integer.valueOf(rs.getString("withhold_count")));
                    st.setWithholdAmt(new BigDecimal(rs.getString("withhold_amt")));
                    st.setDeducBondAmt(new BigDecimal(rs.getString("deduc_bond_amt")));
                    st.setDiscountAmt(new BigDecimal(rs.getString("discount_amt")));
                    st.setSurplusBondAmt(new BigDecimal(rs.getString("surplus_bond_amt")));
                    list.add(st);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    oaWarnCurrentStatisticsService.saveBatch(list);
                }
            } catch (Exception e) {
                log.error("到期提醒数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("到期提醒数据统计SQL异常！" + e.getMessage());
            }

        }
    }

    /**
     * 逾期提醒数据统计 1-7天
     *
     * @return
     */
    public void overdueStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String oids = null;
            if (!CollectionUtils.isEmpty(harmfulOrders)) {
                List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                        Collectors.toList());
                oids = StringUtils.join(harmfulOIds, ",");
            }
            String uids = StringUtils.join(userIds, ",");
            try {
                StringBuilder sb = new StringBuilder("select  '").append(processDate).append("' as date\n"
                        + "        ,o.mini_type\n"
                        + "        ,ifnull(al.collection_personnel_id,0) as service_id\n"
                        + "        ,ifnull(al.collection_personnel_name,'') as service_name\n"
                        + "        ,count(o.id) as total_count\n"
                        + "        ,round(ifnull(sum(pp.total_amt),0),2) as total_amt\n"
                        + "        ,round(\n"
                        + "            sum(ifnull(pp.overdue_fine,0) + ifnull(ff.flow_overdue_fine,0))\n"
                        + "            ,2\n"
                        + "        ) as overdue_fine\n"
                        + "        ,count(case when ff.order_id is not null then 1 end) as pay_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when ff.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as pay_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is not null or rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_count\n"
                        + "        ,count(case when rco1.order_id is not null then 1 end) as called_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when rco1.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as called_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_un_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is not null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as called_un_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_amt\n"
                        + "        ,count(case when ff.withhold > 0 then 1 end) as withhold_count\n"
                        + "        ,round(ifnull(sum(ff.withhold_amt),0),2) as withhold_amt\n"
                        + "        ,count(case when o.status = 220 and o.parent_id =0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then 1 end) as buyout_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when o.status = 220 and o.parent_id = 0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then (case when ff.buyout_amt >=0 then ff.buyout_amt else d.buyout_amt end) end),0)\n"
                        + "            ,2\n"
                        + "        ) as buyout_amt\n"
                        + "        ,round(ifnull(sum(fd.deduc_bond),0),2) as deduc_bond_amt\n"
                        + "        ,ifnull(round(sum(case when o.status = 220 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then d.surplus_bond_amt end),2),0) as surplus_bond_amt\n"
                        + "        ,round(ifnull(sum(ff.discount_amt),0),2) as discount_amt\n"
                        + "        ,round(sum(ifnull(ff.flow_overdue_fine,0)),2) as flow_overdue_fine\n"
                        + "        ,round(ifnull(sum(pp.reduce_amt),0),2) as reduce_amt\n"
                        + "from    rent_order o\n"
                        + "left join rent_order_finance_detail d\n"
                        + "on      d.order_id = o.id left\n"
                        + "join    rent_order_infomore oi\n"
                        + "on      oi.order_id = o.id\n"
                        + "left join rent_customer c\n"
                        + "on      c.id = o.customer_id\n"
                        + "inner join (\n"
                        + "               select  p.order_id\n"
                        + "                       ,sum(p.capital) as `total_amt`\n"
                        + "                       ,sum(\n"
                        + "                           case    when p.repay_status in (5, 10) then p.`real_repay_capital` \n"
                        + "                           end\n"
                        + "                       ) as real_repay_amt\n"
                        + "                       ,sum(p.`overdue_fine` - p.`real_overdue_fine`) as overdue_fine\n"
                        + "                       ,sum(case when p.repay_status in (5, 10) then p.`reduce_amt` end) as reduce_amt\n"
                        + "               from    rent_order_repayment_plan p\n"
                        + "               where   p.is_deleted = 0 \n"
                        + "               and   ((p.repay_status in (5, 10) and overdue_days > 0) or  (p.repay_status = 1 and (overdue_days-1) > 0)) and  (overdue_days-1) < 8 \n"
                        + "               and     date_format(repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "               group by p.order_id\n"
                        + "           ) pp\n"
                        + "on      pp.order_id = o.id  left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_amt\n"
                        + "                    ,sum(discount_amt) as discount_amt\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.overdueFine' > 0 then ext -> '$.overdueFine' \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_overdue_fine\n"
                        + "                    ,count(case when alipay_pay_type  not in (0,1) then 1 end) as withhold\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when alipay_pay_type not in (0,1) then (case\n"
                        + "                                    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end) end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as withhold_amt,(case when ext <> '' and biz_type = 3 then ifnull(ext -> '$.buyoutActAmt',0) + ifnull(ext -> '$.buyoutDeductionAmount',0) + ifnull(ext -> '$.buyoutCouponAmount',0) + ifnull(ext -> '$.discountReturnAmt',0) end) as buyout_amt\n"
                        + "            from    rent_order_flow\n"
                        + "            where   flow_type = 1\n"
                        + "            and     parent_uid = ''\n"
                        + "            and     pay_status = 10\n"
                        + "            and     is_deleted = 0\n"
                        + "            and     biz_type in (1,3,10,24,25)\n"
                        + "            and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            group by order_id\n"
                        + "        ) ff\n"
                        + "on      ff.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "                      ,sum(flow_amt) as deduc_bond\n"
                        + "              from    rent_order_flow\n"
                        + "              where   flow_type = 1\n"
                        + "              and     parent_uid = ''\n"
                        + "              and     biz_type = 23\n"
                        + "              and     is_deleted = 0\n"
                        + "              and     pay_status = 10\n"
                        + "              and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              group by order_id\n"
                        + "          ) fd\n"
                        + "on      fd.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "            from    rent_call_record c\n"
                        + "            left join rent_order o\n"
                        + "            on      o.id = c.order_id\n"
                        + "            where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            and     user_id in (").append(uids).append(")\n"
                        + "            and     call_type = 'dialout'\n"
                        + "            and     order_id > 0\n"
                        + "            and     state = 'dealing'\n"
                        + "            and     call_state in ('Unlink','Hangup')\n"
                        + "            group by order_id\n"
                        + "        ) rco1\n"
                        + "on      rco1.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "              from    rent_call_record c\n"
                        + "              left join rent_order o\n"
                        + "              on      o.id = c.order_id\n"
                        + "              where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and     user_id in (").append(uids).append(")\n"
                        + "              and     call_type = 'dialout'\n"
                        + "              and     order_id > 0\n"
                        + "              and     (state <> 'dealing' or call_state not in ('Unlink','Hangup'))\n"
                        + "              group by order_id\n"
                        + "          ) rco2\n"
                        + "on      rco2.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  a.order_id\n"
                        + "                    ,collection_personnel_id\n"
                        + "                    ,collection_personnel_name\n"
                        + "            from    rent_order_repayment_plan_allot a\n"
                        + "            left join rent_order_repayment_plan p\n"
                        + "            on      p.id = a.repayment_plan_id\n"
                        + "            where   a.id in (select max(id) from rent_order_repayment_plan_allot where date_format(update_time, '%Y-%m-%d') <='").append(processDate).append("'  group by repayment_plan_id)\n"
                        + "               and     date_format(p.repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (p.repay_status in (5, 10) and date_format(p.real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  p.repay_status = 1\n"
                        + "                       )\n"
                        + "              and a.collection_personnel_id in (").append(uids).append(")\n"
                        + "            group by a.order_id\n"
                        + "                     ,a.collection_personnel_id\n"
                        + "                     ,a.collection_personnel_name\n"
                        + "        ) al\n"
                        + "on      al.order_id = o.id\n"
                        + "where   1=1 and o.is_deleted = 0 \n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.id not in (").append(oids).append(") \n");
                    sb.append(" and o.parent_id not in (").append(oids).append(") \n");
                }
                sb.append(" and     oi.`lawsuit_status` = 1\n"
                        + "and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "and     o.type = 1\n"
                        + "and     o.biz_type = 2\n"
                        + "and     o.merchant_id = 100\n"
                        + "group by o.mini_type\n"
                        + "         ,al.collection_personnel_id\n"
                        + "         ,al.collection_personnel_name\n"
                        + ";\n");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sb.toString());
                log.info("===查询完毕===");
                if (rs == null) {
                    return;
                }
                List<OAWarnOverdueStatisticsDO> list = new ArrayList<>();
                while (rs.next()) {
                    OAWarnOverdueStatisticsDO st = new OAWarnOverdueStatisticsDO();
                    st.setType(10);
                    st.setStatisticsDate(rs.getString("date"));
                    st.setMiniType(Integer.valueOf(rs.getString("mini_type")));
                    st.setServiceId(Integer.valueOf(rs.getString("service_id")));
                    st.setServiceName(rs.getString("service_name"));
                    st.setTotalCount(Integer.valueOf(rs.getString("total_count")));
                    st.setTotalAllotCount(st.getTotalCount());
                    if (st.getServiceId() == 0) {
                        st.setTotalAllotCount(st.getTotalCount());
                    }
                    st.setTotalAmt(new BigDecimal(rs.getString("total_amt")));
                    st.setTotalOverdueFine(new BigDecimal(rs.getString("overdue_fine")));
                    st.setPayCount(Integer.valueOf(rs.getString("pay_count")));
                    st.setPayAmt(new BigDecimal(rs.getString("pay_amt")));
                    st.setCalledCount(Integer.valueOf(rs.getString("called_count")));
                    st.setCalledDealCount(Integer.valueOf(rs.getString("called_deal_count")));
                    st.setCalledDealAmt(new BigDecimal(rs.getString("called_deal_amt")));
                    st.setCalledUnDealCount(Integer.valueOf(rs.getString("called_un_deal_count")));
                    st.setCalledUnDealAmt(new BigDecimal(rs.getString("called_un_deal_amt")));
                    st.setUnCalledCount(Integer.valueOf(rs.getString("un_called_count")));
                    st.setUnCalledAmt(new BigDecimal(rs.getString("un_called_amt")));
                    st.setBuyoutCount(Integer.valueOf(rs.getString("buyout_count")));
                    st.setBuyoutAmt(new BigDecimal(rs.getString("buyout_amt")));
                    st.setWithholdCount(Integer.valueOf(rs.getString("withhold_count")));
                    st.setWithholdAmt(new BigDecimal(rs.getString("withhold_amt")));
                    st.setDeducBondAmt(new BigDecimal(rs.getString("deduc_bond_amt")));
                    st.setDiscountAmt(new BigDecimal(rs.getString("discount_amt")));
                    st.setSurplusBondAmt(new BigDecimal(rs.getString("surplus_bond_amt")));
                    st.setOverdueFine(new BigDecimal(rs.getString("flow_overdue_fine")));
                    st.setReduceAmt(new BigDecimal(rs.getString("reduce_amt")));
                    list.add(st);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    oaWarnOverdueStatisticsService.saveBatch(list);
                }
            } catch (Exception e) {
                log.error("逾期提醒数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("逾期提醒数据统计SQL异常！" + e.getMessage());
            }

        }
    }


    /**
     * 逾期提醒数据统计8-30天
     *
     * @return
     */
    public void overdueStatistics8(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String oids = null;
            if (!CollectionUtils.isEmpty(harmfulOrders)) {
                List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                        Collectors.toList());
                oids = StringUtils.join(harmfulOIds, ",");
            }
            String uids = StringUtils.join(userIds, ",");
            try {
                StringBuilder sb = new StringBuilder("select  '").append(processDate).append("' as date\n"
                        + "        ,o.mini_type\n"
                        + "        ,ifnull(al.collection_personnel_id,0) as service_id\n"
                        + "        ,ifnull(al.collection_personnel_name,'') as service_name\n"
                        + "        ,count(o.id) as total_count\n"
                        + "        ,round(ifnull(sum(pp.total_amt),0),2) as total_amt\n"
                        + "        ,round(\n"
                        + "            sum(ifnull(pp.overdue_fine,0) + ifnull(ff.flow_overdue_fine,0))\n"
                        + "            ,2\n"
                        + "        ) as overdue_fine\n"
                        + "        ,count(case when ff.order_id is not null then 1 end) as pay_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when ff.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as pay_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is not null or rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_count\n"
                        + "        ,count(case when rco1.order_id is not null then 1 end) as called_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when rco1.order_id is not null then ff.flow_amt end),0)\n"
                        + "            ,2\n"
                        + "        ) as called_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is not null then 1 \n"
                        + "            end\n"
                        + "        ) as called_un_deal_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is not null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as called_un_deal_amt\n"
                        + "        ,count(\n"
                        + "            case    when rco1.order_id is null and rco2.order_id is null then 1 \n"
                        + "            end\n"
                        + "        ) as un_called_count\n"
                        + "        ,round(\n"
                        + "            ifnull(\n"
                        + "                sum(\n"
                        + "                    case    when rco1.order_id is null and rco2.order_id is null then ff.flow_amt \n"
                        + "                    end\n"
                        + "                )\n"
                        + "                ,0\n"
                        + "            )\n"
                        + "            ,2\n"
                        + "        ) as un_called_amt\n"
                        + "        ,count(case when ff.withhold > 0 then 1 end) as withhold_count\n"
                        + "        ,round(ifnull(sum(ff.withhold_amt),0),2) as withhold_amt\n"
                        + "        ,count(case when o.status = 220 and o.parent_id =0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then 1 end) as buyout_count\n"
                        + "        ,round(\n"
                        + "            ifnull(sum(case when o.status = 220 and o.parent_id = 0 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then (case when ff.buyout_amt >=0 then ff.buyout_amt else d.buyout_amt end) end),0)\n"
                        + "            ,2\n"
                        + "        ) as buyout_amt\n"
                        + "        ,round(ifnull(sum(fd.deduc_bond),0),2) as deduc_bond_amt\n"
                        + "        ,ifnull(round(sum(case when o.status = 220 and date_format(oi.buyout_time, '%Y-%m-%d')='").append(processDate).append("' then d.surplus_bond_amt end),2),0) as surplus_bond_amt\n"
                        + "        ,round(ifnull(sum(ff.discount_amt),0),2) as discount_amt\n"
                        + "        ,round(sum(ifnull(ff.flow_overdue_fine,0)),2) as flow_overdue_fine\n"
                        + "        ,round(ifnull(sum(pp.reduce_amt),0),2) as reduce_amt\n"
                        + "from    rent_order o\n"
                        + "left join rent_order_finance_detail d\n"
                        + "on      d.order_id = o.id left\n"
                        + "join    rent_order_infomore oi\n"
                        + "on      oi.order_id = o.id\n"
                        + "left join rent_customer c\n"
                        + "on      c.id = o.customer_id\n"
                        + "inner join (\n"
                        + "               select  p.order_id\n"
                        + "                       ,sum(p.capital) as `total_amt`\n"
                        + "                       ,sum(\n"
                        + "                           case    when p.repay_status in (5, 10) then p.`real_repay_capital` \n"
                        + "                           end\n"
                        + "                       ) as real_repay_amt\n"
                        + "                       ,sum(p.`overdue_fine` - p.`real_overdue_fine`) as overdue_fine\n"
                        + "                       ,sum(case when p.repay_status in (5, 10) then p.`reduce_amt` end) as reduce_amt\n"
                        + "               from    rent_order_repayment_plan p\n"
                        + "               where   p.is_deleted = 0 \n"
                        + "               and (overdue_days-1) >= 8 and (overdue_days-1) <= " + overdueDays + "\n"
                        + "               and     date_format(repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "               group by p.order_id\n"
                        + "           ) pp\n"
                        + "on      pp.order_id = o.id  left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_amt\n"
                        + "                    ,sum(discount_amt) as discount_amt\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when ext <> '' and ext -> '$.overdueFine' > 0 then ext -> '$.overdueFine' \n"
                        + "                            end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as flow_overdue_fine\n"
                        + "                    ,count(case when alipay_pay_type  not in (0,1) then 1 end) as withhold\n"
                        + "                    ,round(\n"
                        + "                        sum(\n"
                        + "                            case    when alipay_pay_type not in (0,1) then (case\n"
                        + "                                    when ext <> '' and ext -> '$.originPayAmt' > 0 then ext -> '$.originPayAmt'  when ext <> '' and ext -> '$.offlineFinance' =true then ext -> '$.realPayAmount' \n"
                        + "                                    else flow_amt \n"
                        + "                            end) end\n"
                        + "                        )\n"
                        + "                        ,2\n"
                        + "                    ) as withhold_amt,(case when ext <> '' and biz_type = 3 then ifnull(ext -> '$.buyoutActAmt',0) + ifnull(ext -> '$.buyoutDeductionAmount',0) + ifnull(ext -> '$.buyoutCouponAmount',0) + ifnull(ext -> '$.discountReturnAmt',0) end) as buyout_amt\n"
                        + "            from    rent_order_flow\n"
                        + "            where   flow_type = 1\n"
                        + "            and     parent_uid = ''\n"
                        + "            and     pay_status = 10\n"
                        + "            and     is_deleted = 0\n"
                        + "            and     biz_type in (1,3,10,24,25)\n"
                        + "            and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            group by order_id\n"
                        + "        ) ff\n"
                        + "on      ff.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "                      ,sum(flow_amt) as deduc_bond\n"
                        + "              from    rent_order_flow\n"
                        + "              where   flow_type = 1\n"
                        + "              and     parent_uid = ''\n"
                        + "              and     biz_type = 23\n"
                        + "              and     is_deleted = 0\n"
                        + "              and     pay_status = 10\n"
                        + "              and     date_format(flow_time,'%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              group by order_id\n"
                        + "          ) fd\n"
                        + "on      fd.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  order_id\n"
                        + "            from    rent_call_record c\n"
                        + "            left join rent_order o\n"
                        + "            on      o.id = c.order_id\n"
                        + "            where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "            and     user_id in (").append(uids).append(")\n"
                        + "            and     call_type = 'dialout'\n"
                        + "            and     order_id > 0\n"
                        + "            and     state = 'dealing'\n"
                        + "            and     call_state in ('Unlink','Hangup')\n"
                        + "            group by order_id\n"
                        + "        ) rco1\n"
                        + "on      rco1.order_id = o.id\n"
                        + "left join (\n"
                        + "              select  order_id\n"
                        + "              from    rent_call_record c\n"
                        + "              left join rent_order o\n"
                        + "              on      o.id = c.order_id\n"
                        + "              where   date_format(c.create_time, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "              and     user_id in (").append(uids).append(")\n"
                        + "              and     call_type = 'dialout'\n"
                        + "              and     order_id > 0\n"
                        + "              and     (state <> 'dealing' or call_state not in ('Unlink','Hangup'))\n"
                        + "              group by order_id\n"
                        + "          ) rco2\n"
                        + "on      rco2.order_id = o.id   left\n"
                        + "join    (\n"
                        + "            select  a.order_id\n"
                        + "                    ,collection_personnel_id\n"
                        + "                    ,collection_personnel_name\n"
                        + "            from    rent_order_repayment_plan_allot a\n"
                        + "            left join rent_order_repayment_plan p\n"
                        + "            on      p.id = a.repayment_plan_id\n"
                        + "            where   a.id in (select max(id) from rent_order_repayment_plan_allot where date_format(update_time, '%Y-%m-%d') <='").append(processDate).append("'  group by repayment_plan_id)\n"
                        + "               and     date_format(p.repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (p.repay_status in (5, 10) and date_format(p.real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  p.repay_status = 1\n"
                        + "                       )\n"
                        + "              and a.collection_personnel_id in (").append(uids).append(")\n"
                        + "            group by a.order_id\n"
                        + "                     ,a.collection_personnel_id\n"
                        + "                     ,a.collection_personnel_name\n"
                        + "        ) al\n"
                        + "on      al.order_id = o.id\n"
                        + "where   1=1 and o.is_deleted = 0 \n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.id not in (").append(oids).append(") \n");
                    sb.append(" and o.parent_id not in (").append(oids).append(") \n");
                }
                sb.append(" and     oi.`lawsuit_status` = 1\n"
                        + "and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "and     o.type = 1\n"
                        + "and     o.biz_type = 2\n"
                        + "and     o.merchant_id = 100\n"
                        + "group by o.mini_type\n"
                        + "         ,al.collection_personnel_id\n"
                        + "         ,al.collection_personnel_name\n"
                        + ";\n");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                ResultSet rs = stmt.executeQuery(sb.toString());
                log.info("===查询完毕===");
                if (rs == null) {
                    return;
                }
                List<OAWarnOverdueStatisticsDO> list = new ArrayList<>();
                while (rs.next()) {
                    OAWarnOverdueStatisticsDO st = new OAWarnOverdueStatisticsDO();
                    st.setType(20);
                    st.setStatisticsDate(rs.getString("date"));
                    st.setMiniType(Integer.valueOf(rs.getString("mini_type")));
                    st.setServiceId(Integer.valueOf(rs.getString("service_id")));
                    st.setServiceName(rs.getString("service_name"));
                    st.setTotalCount(Integer.valueOf(rs.getString("total_count")));
                    st.setTotalAllotCount(st.getTotalCount());
                    if (st.getServiceId() == 0) {
                        st.setTotalAllotCount(st.getTotalCount());
                    }
                    st.setTotalAmt(new BigDecimal(rs.getString("total_amt")));
                    st.setTotalOverdueFine(new BigDecimal(rs.getString("overdue_fine")));
                    st.setPayCount(Integer.valueOf(rs.getString("pay_count")));
                    st.setPayAmt(new BigDecimal(rs.getString("pay_amt")));
                    st.setCalledCount(Integer.valueOf(rs.getString("called_count")));
                    st.setCalledDealCount(Integer.valueOf(rs.getString("called_deal_count")));
                    st.setCalledDealAmt(new BigDecimal(rs.getString("called_deal_amt")));
                    st.setCalledUnDealCount(Integer.valueOf(rs.getString("called_un_deal_count")));
                    st.setCalledUnDealAmt(new BigDecimal(rs.getString("called_un_deal_amt")));
                    st.setUnCalledCount(Integer.valueOf(rs.getString("un_called_count")));
                    st.setUnCalledAmt(new BigDecimal(rs.getString("un_called_amt")));
                    st.setBuyoutCount(Integer.valueOf(rs.getString("buyout_count")));
                    st.setBuyoutAmt(new BigDecimal(rs.getString("buyout_amt")));
                    st.setWithholdCount(Integer.valueOf(rs.getString("withhold_count")));
                    st.setWithholdAmt(new BigDecimal(rs.getString("withhold_amt")));
                    st.setDeducBondAmt(new BigDecimal(rs.getString("deduc_bond_amt")));
                    st.setDiscountAmt(new BigDecimal(rs.getString("discount_amt")));
                    st.setSurplusBondAmt(new BigDecimal(rs.getString("surplus_bond_amt")));
                    st.setOverdueFine(new BigDecimal(rs.getString("flow_overdue_fine")));
                    st.setReduceAmt(new BigDecimal(rs.getString("reduce_amt")));
                    list.add(st);
                }
                if (!CollectionUtils.isEmpty(list)) {
                    oaWarnOverdueStatisticsService.saveBatch(list);
                }
            } catch (Exception e) {
                log.error("逾期提醒数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("逾期提醒数据统计SQL异常！" + e.getMessage());
            }

        }
    }


    /**
     * 提前提醒总分配数据统计
     *
     * @return
     */
    public void advanceTotalAllotStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String uids = StringUtils.join(userIds, ",");
            String allotIds = this.advanceAllotIds(date);
            if (StringUtils.isEmpty(allotIds)) {
                return;
            }
            try {
                StringBuilder sb = new StringBuilder("select '").append(processDate).append("' as date,collection_personnel_id,collection_personnel_name,o.mini_type,count(DISTINCT o.id) as allot_count \n"
                        + "from rent_order_repayment_plan_allot a\n"
                        + "left join rent_order_repayment_plan p on p.id = a.`repayment_plan_id`\n"
                        + "left join rent_order o on o.id = p.order_id\n"
                        + "left join rent_order_infomore oi on oi.order_id = p.order_id\n"
                        + "where a.id in (").append(allotIds).append(") \n"
                        + "      and a.collection_personnel_id in (").append(uids).append(")  \n"
                        + "      and     oi.`lawsuit_status` = 1\n"
                        + "      and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "      and     o.type = 1\n"
                        + "      and     o.biz_type = 2\n"
                        + "      and     o.merchant_id = 100\n"
                        + "      and o.id not in (select order_id from rent_order_repayment_plan where (repay_status = 1 or (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')) and overdue_days > 0  group by order_id)\n"
                        + "    group by a.`collection_personnel_id`,o.mini_type");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return;
                }
                while (rs.next()) {
                    String sDate = rs.getString("date");
                    Integer miniType = Integer.valueOf(rs.getString("mini_type"));
                    Integer serviceId = Integer.valueOf(rs.getString("collection_personnel_id"));
                    String serviceName = rs.getString("collection_personnel_name");
                    Integer allotCount = Integer.valueOf(rs.getString("allot_count"));

                    OAWarnAdvanceStatisticsDO statisticsDO = oaWarnAdvanceStatisticsService.getByQuery(sDate, miniType, serviceId);
                    if (statisticsDO == null) {
                        statisticsDO = new OAWarnAdvanceStatisticsDO();
                        statisticsDO.setTotalAllotCount(allotCount);
                        statisticsDO.setStatisticsDate(sDate);
                        statisticsDO.setMiniType(miniType);
                        statisticsDO.setServiceName(serviceName);
                        statisticsDO.setServiceId(serviceId);
                        oaWarnAdvanceStatisticsService.save(statisticsDO);
                    } else {
                        OAWarnAdvanceStatisticsDO updateDO = new OAWarnAdvanceStatisticsDO();
                        updateDO.setId(statisticsDO.getId());
                        updateDO.setTotalAllotCount(allotCount);
                        oaWarnAdvanceStatisticsService.updateById(updateDO);
                    }
                }
            } catch (Exception e) {
                log.error("提前提醒总分配数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("提前提醒总分配数据统计SQL异常！" + e.getMessage());
            }

        }
    }

    /**
     * 当期提醒总分配数据统计
     *
     * @return
     */
    public void currentTotalAllotStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String uids = StringUtils.join(userIds, ",");
            String allotIds = this.currentAllotIds(date);
            if (StringUtils.isEmpty(allotIds)) {
                return;
            }
            try {
                StringBuilder sb = new StringBuilder("select '").append(processDate).append("' as date,collection_personnel_id,collection_personnel_name,o.mini_type,count(DISTINCT o.id) as allot_count \n"
                        + "from rent_order_repayment_plan_allot a\n"
                        + "left join rent_order_repayment_plan p on p.id = a.`repayment_plan_id`\n"
                        + "left join rent_order o on o.id = p.order_id\n"
                        + "left join rent_order_infomore oi on oi.order_id = p.order_id\n"
                        + "where a.id in (").append(allotIds).append(") \n"
                        + "      and a.collection_personnel_id in (").append(uids).append(")  \n"
                        + "      and     oi.`lawsuit_status` = 1\n"
                        + "      and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "      and     o.type = 1\n"
                        + "      and     o.biz_type = 2\n"
                        + "      and     o.merchant_id = 100\n"
                        + "      and o.id not in (select order_id from rent_order_repayment_plan where (repay_status = 1 or (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')) and overdue_days > 1  group by order_id)\n"
                        + "    group by a.`collection_personnel_id`,o.mini_type");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return;
                }
                while (rs.next()) {
                    String sDate = rs.getString("date");
                    Integer miniType = Integer.valueOf(rs.getString("mini_type"));
                    Integer serviceId = Integer.valueOf(rs.getString("collection_personnel_id"));
                    String serviceName = rs.getString("collection_personnel_name");
                    Integer allotCount = rs.getInt("allot_count");

                    OAWarnCurrentStatisticsDO statisticsDO = oaWarnCurrentStatisticsService.getByQuery(sDate, miniType, serviceId);
                    if (statisticsDO == null) {
                        statisticsDO = new OAWarnCurrentStatisticsDO();
                        statisticsDO.setTotalAllotCount(allotCount);
                        statisticsDO.setStatisticsDate(sDate);
                        statisticsDO.setMiniType(miniType);
                        statisticsDO.setServiceName(serviceName);
                        statisticsDO.setServiceId(serviceId);
                        oaWarnCurrentStatisticsService.save(statisticsDO);
                    } else {
                        OAWarnCurrentStatisticsDO updateDO = new OAWarnCurrentStatisticsDO();
                        updateDO.setId(statisticsDO.getId());
                        updateDO.setTotalAllotCount(allotCount);
                        oaWarnCurrentStatisticsService.updateById(updateDO);
                    }
                }
            } catch (Exception e) {
                log.error("提前提醒总分配数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("提前提醒总分配数据统计SQL异常！" + e.getMessage());
            }

        }
    }

    /**
     * 逾期提醒总分配数据统计
     *
     * @return
     */
    public void overdueTotalAllotStatistics(String date) {
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        Set<String> harmfulOrders = redisTemplate.opsForSet().members(HARMFUL_ORDERS + processDate);
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            String oids = null;
            if (!CollectionUtils.isEmpty(harmfulOrders)) {
                List<Long> harmfulOIds = harmfulOrders.stream().map(s -> Long.valueOf(s)).collect(
                        Collectors.toList());
                oids = StringUtils.join(harmfulOIds, ",");
            }
            String uids = StringUtils.join(userIds, ",");
            String allotIds = this.overdueAllotIds(date);
            if (StringUtils.isEmpty(allotIds)) {
                return;
            }
            try {
                StringBuilder sb = new StringBuilder("select '").append(processDate).append("' as date,collection_personnel_id,collection_personnel_name,o.mini_type,count(DISTINCT o.id) as allot_count \n"
                        + "from rent_order_repayment_plan_allot a\n"
                        + "left join rent_order_repayment_plan p on p.id = a.`repayment_plan_id`\n"
                        + "left join rent_order o on o.id = p.order_id\n"
                        + "left join rent_order_infomore oi on oi.order_id = p.order_id\n"
                        + "where a.id in (").append(allotIds).append(") \n"
                        + "      and a.collection_personnel_id in (").append(uids).append(")  \n");

                if (StringUtils.isNotEmpty(oids)) {
                    sb.append(" and o.id not in (").append(oids).append(") \n");
                }
                sb.append(" and     oi.`lawsuit_status` = 1\n"
                        + "       and     o.termination <> 5  and o.is_deleted = 0\n"
                        + "       and     o.type = 1\n"
                        + "       and     o.biz_type = 2\n"
                        + "       and     o.merchant_id = 100\n"
                        + "    group by a.`collection_personnel_id`,o.mini_type");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return;
                }
                while (rs.next()) {
                    String sDate = rs.getString("date");
                    Integer miniType = Integer.valueOf(rs.getString("mini_type"));
                    Integer serviceId = Integer.valueOf(rs.getString("collection_personnel_id"));
                    String serviceName = rs.getString("collection_personnel_name");
                    Integer allotCount = Integer.valueOf(rs.getString("allot_count"));

                    OAWarnOverdueStatisticsDO statisticsDO = oaWarnOverdueStatisticsService.getByQuery(sDate, miniType, serviceId);
                    if (statisticsDO == null) {
                        statisticsDO = new OAWarnOverdueStatisticsDO();
                        statisticsDO.setType(10);
                        statisticsDO.setTotalAllotCount(allotCount);
                        statisticsDO.setStatisticsDate(sDate);
                        statisticsDO.setMiniType(miniType);
                        statisticsDO.setServiceName(serviceName);
                        statisticsDO.setServiceId(serviceId);
                        oaWarnOverdueStatisticsService.save(statisticsDO);
                    } else {
                        OAWarnOverdueStatisticsDO updateDO = new OAWarnOverdueStatisticsDO();
                        updateDO.setId(statisticsDO.getId());
                        updateDO.setTotalAllotCount(allotCount);
                        oaWarnOverdueStatisticsService.updateById(updateDO);
                    }
                }

            } catch (Exception e) {
                log.error("逾期提醒数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("逾期提醒数据统计SQL异常！" + e.getMessage());
            }

        }
    }


    /**
     * 提前提醒分配记录IDS
     *
     * @return
     */
    public String advanceAllotIds(String date) {
        String result = null;
        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            Set<Long> ids = new HashSet<>();
            try {
                StringBuilder sb = new StringBuilder("select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 06:00:00' and update_time <='").append(processDate).append(" 11:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where   is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(LocalDate.parse(processDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).toString()).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id\n"
                        + "    \n"
                        + "    union all (\n"
                        + "    \n"
                        + "    select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 17:00:00' and update_time <='").append(processDate).append(" 19:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where  is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(LocalDate.parse(processDate, DateTimeFormatter.ofPattern("yyyy-MM-dd")).plusDays(1).toString()).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id)");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return null;
                }
                while (rs.next()) {
                    ids.add(rs.getLong("id"));
                }
                if (!CollectionUtils.isEmpty(ids)) {
                    result = StringUtils.join(ids, ",");
                }
            } catch (Exception e) {
                log.error("提前提醒分配记录IDS SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("提前提醒分配记录IDS SQL异常！" + e.getMessage());
            }

        }

        return result;
    }

    /**
     * 当期提醒分配记录IDS
     *
     * @return
     */
    public String currentAllotIds(String date) {
        String result = null;

        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            try {
                StringBuilder sb = new StringBuilder("select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 06:00:00' and update_time <='").append(processDate).append(" 11:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where  is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id\n"
                        + "    \n"
                        + "    union all (\n"
                        + "    \n"
                        + "    select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 17:00:00' and update_time <='").append(processDate).append(" 19:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where  is_deleted = 0 and date_format(repay_date, '%Y-%m-%d') = '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status in (5, 10) and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id)");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return null;
                }
                Set<Long> ids = new HashSet<>();
                while (rs.next()) {
                    ids.add(rs.getLong("id"));
                }
                if (!CollectionUtils.isEmpty(ids)) {
                    result = StringUtils.join(ids, ",");
                }
            } catch (Exception e) {
                log.error("当期提醒分配记录IDS SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("当期提醒分配记录IDS SQL异常！" + e.getMessage());
            }

        }
        return result;
    }

    /**
     * 逾期提醒分配记录IDS
     *
     * @return
     */
    public String overdueAllotIds(String date) {
        String result = null;

        String processDate = LocalDate.now().minusDays(1).toString();
        if (StringUtils.isNotEmpty(date)) {
            processDate = date;
        }
        // 还款提醒人员
        Set<Integer> userIds = redisTemplate.opsForSet().members(BI_WARN_SERVICE);
        if (!CollectionUtils.isEmpty(userIds)) {
            try {
                StringBuilder sb = new StringBuilder("select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 06:00:00' and update_time <='").append(processDate).append(" 11:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where  is_deleted = 0 and overdue_days > 0 and overdue_days <= " + overdueDays + " and " +
                        "date_format(repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id\n"
                        + "    \n"
                        + "    union all (\n"
                        + "    \n"
                        + "    select max(id) as id from rent_order_repayment_plan_allot where update_time >='").append(processDate).append(" 17:00:00' and update_time <='").append(processDate).append(" 19:00:00'  and repayment_plan_id in (\n"
                        + "  select  id  from    rent_order_repayment_plan \n"
                        + "               where  is_deleted = 0 and overdue_days > 0 and overdue_days <= " + overdueDays + " and " +
                        "date_format(repay_date, '%Y-%m-%d') < '").append(processDate).append("'\n"
                        + "               and     (\n"
                        + "                               (repay_status = 5 and date_format(real_repay_time,'%Y-%m-%d') >= '").append(processDate).append("')\n"
                        + "                           or  repay_status = 1\n"
                        + "                       )\n"
                        + "                       \n"
                        + "    ) group by repayment_plan_id)");
                Connection conn = JdbcUtil.getConnection();
                Statement stmt = conn.createStatement();
                // log.error("sql：" + sb.toString());
                ResultSet rs = stmt.executeQuery(sb.toString());
                // log.error("===查询完毕===");
                if (rs == null) {
                    return null;
                }
                Set<Long> ids = new HashSet<>();
                while (rs.next()) {
                    ids.add(rs.getLong("id"));
                }
                if (!CollectionUtils.isEmpty(ids)) {
                    result = StringUtils.join(ids, ",");
                }
            } catch (Exception e) {
                log.error("逾期提醒数据统计SQL异常！" + e.getMessage(), e);
                dingDingRobot.exceptionAlarm("逾期提醒数据统计SQL异常！" + e.getMessage());
            }

        }
        return result;
    }
}
