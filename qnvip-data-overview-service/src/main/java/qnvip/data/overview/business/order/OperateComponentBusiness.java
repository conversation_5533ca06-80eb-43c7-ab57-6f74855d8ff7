package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateComponentDO;
import qnvip.data.overview.service.order.OperateComponentService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateComponentBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateComponentService componentService;

    void initMap(Map<Integer, Map<String, Map<String, OperateComponentDO>>> miniType2Map, OperateComponentDO orderDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, Map<String, OperateComponentDO>> merchantId2Do = Maps.newHashMap();
        Map<String, OperateComponentDO> name2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(orderDO.getMiniType())) {
            OperateComponentDO domain = new OperateComponentDO();
            domain.setCountDay(countDay);
            domain.setMiniType(orderDO.getMiniType());
            domain.setMerchantId(orderDO.getMerchantId());
            domain.setName(orderDO.getName());
            name2Do.put(orderDO.getName(), domain);
            merchantId2Do.put(orderDO.getMerchantId(), name2Do);
        } else {
            merchantId2Do = miniType2Map.get(orderDO.getMiniType());
            if (!merchantId2Do.containsKey(orderDO.getMerchantId())) {
                OperateComponentDO domain = new OperateComponentDO();
                domain.setCountDay(countDay);
                domain.setMiniType(orderDO.getMiniType());
                domain.setMerchantId(orderDO.getMerchantId());
                domain.setName(orderDO.getName());
                name2Do.put(orderDO.getName(), domain);
                merchantId2Do.put(orderDO.getMerchantId(), name2Do);
            } else {
                name2Do = merchantId2Do.get(orderDO.getMerchantId());
                if (!name2Do.containsKey(orderDO.getName())) {
                    OperateComponentDO domain = new OperateComponentDO();
                    domain.setCountDay(countDay);
                    domain.setMiniType(orderDO.getMiniType());
                    domain.setMerchantId(orderDO.getMerchantId());
                    domain.setName(orderDO.getName());
                    name2Do.put(orderDO.getName(), domain);
                    merchantId2Do.put(orderDO.getMerchantId(), name2Do);
                }
            }
        }
        miniType2Map.put(orderDO.getMiniType(), merchantId2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, Map<String, OperateComponentDO>>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateComponentDO>> f1 = CompletableFuture.supplyAsync(()->getTotalGMV(ds));
        CompletableFuture<List<OperateComponentDO>> f2 = CompletableFuture.supplyAsync(()->getTotalNMV(ds));
        CompletableFuture<List<OperateComponentDO>> f3 = CompletableFuture.supplyAsync(()->getGMV(ds));
        CompletableFuture<List<OperateComponentDO>> f4 = CompletableFuture.supplyAsync(()->getNMV(ds));
        CompletableFuture.allOf(f1, f2, f3, f4).join();
        try {
            List<OperateComponentDO> totalGmv = f1.get();
            List<OperateComponentDO> totalNmv = f2.get();
            List<OperateComponentDO> gmv = f3.get();
            List<OperateComponentDO> nmv = f4.get();

            for (OperateComponentDO coreDO : totalGmv) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<String, OperateComponentDO>> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                Map<String, OperateComponentDO> name2Do = merchantId2Do.get(coreDO.getMerchantId());
                OperateComponentDO operateComponentDO = name2Do.get(coreDO.getName());
                operateComponentDO.setGmv(coreDO.getGmv());
                operateComponentDO.setOrderCount(coreDO.getOrderCount());
                operateComponentDO.setOrderUvCount(coreDO.getOrderUvCount());
            }
            for (OperateComponentDO coreDO : gmv) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<String, OperateComponentDO>> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                Map<String, OperateComponentDO> name2Do = merchantId2Do.get(coreDO.getMerchantId());
                OperateComponentDO operateComponentDO = name2Do.get(coreDO.getName());
                operateComponentDO.setGmv(coreDO.getGmv());
                operateComponentDO.setOrderCount(coreDO.getOrderCount());
                operateComponentDO.setOrderUvCount(coreDO.getOrderUvCount());
            }
            for (OperateComponentDO coreDO : totalNmv) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<String, OperateComponentDO>> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                Map<String, OperateComponentDO> name2Do = merchantId2Do.get(coreDO.getMerchantId());
                OperateComponentDO operateComponentDO = name2Do.get(coreDO.getName());
                operateComponentDO.setNmv(coreDO.getNmv());
                operateComponentDO.setOrderPayCount(coreDO.getOrderPayCount());
                operateComponentDO.setOrderPayUvCount(coreDO.getOrderPayUvCount());
            }
            for (OperateComponentDO coreDO : nmv) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<String, OperateComponentDO>> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                Map<String, OperateComponentDO> name2Do = merchantId2Do.get(coreDO.getMerchantId());
                OperateComponentDO operateComponentDO = name2Do.get(coreDO.getName());
                operateComponentDO.setNmv(coreDO.getNmv());
                operateComponentDO.setOrderPayCount(coreDO.getOrderPayCount());
                operateComponentDO.setOrderPayUvCount(coreDO.getOrderPayUvCount());
            }
            LinkedList<OperateComponentDO> list = Lists.newLinkedList();
            // 写入mysql
            for (Map.Entry<Integer, Map<String, Map<String, OperateComponentDO>>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, Map<String, OperateComponentDO>> merchantId2Do : entry.getValue().entrySet()) {
                    for (Map.Entry<String, OperateComponentDO> name2Do : merchantId2Do.getValue().entrySet()) {
                        OperateComponentDO value = name2Do.getValue();
                        list.add(value);
                    }
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            componentService.removeDataByTime(countDay);
            componentService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateComponentBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 获取总加购配件订单信息
     */
    private List<OperateComponentDO> getTotalGMV(String ds) {
        String sql = "select count(a.id)                     as num," +
                "       count(distinct (a.customer_id)) as uv," +
                "       sum(b.operating_purchase_price) as GMV," +
                "       b.name" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id" +
                " where a.merchant_id = 100" +
                "  and b.item_type = 10" +
                "  and a.is_deleted = 0" +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds ="+ds +
                "  and b.ds ="+ds +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                "  group by b.name";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateComponentDO domain = new OperateComponentDO();
            String nmv = record.getString("gmv");
            String num = record.getString("num");
            String uv = record.getString("uv");
            String name = record.getString("name");
            domain.setMiniType(-1);
            domain.setMerchantId("100");
            domain.setOrderCount(Long.parseLong(num));
            domain.setOrderUvCount(Long.parseLong(uv));
            domain.setGmv(new BigDecimal(nmv));
            domain.setName(name);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取总加购配件支付订单
     */
    private List<OperateComponentDO> getTotalNMV(String ds) {
        String sql = "select count(a.id)                     as num," +
                "       count(distinct (a.customer_id)) as uv," +
                "       sum(b.operating_purchase_price) as nmv," +
                "       b.name" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id" +
                " where a.merchant_id = 100" +
                "  and b.item_type = 10" +
                "  and a.is_deleted = 0" +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds ="+ds +
                "  and b.ds ="+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                "  group by b.name";


        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateComponentDO domain = new OperateComponentDO();
            String nmv = record.getString("nmv");
            String num = record.getString("num");
            String uv = record.getString("uv");
            String name = record.getString("name");
            domain.setMiniType(-1);
            domain.setMerchantId("100");
            domain.setOrderPayCount(Long.parseLong(num));
            domain.setOrderPayUvCount(Long.parseLong(uv));
            domain.setNmv(new BigDecimal(nmv));
            domain.setName(name);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取加购配件订单信息 分组
     */
    private List<OperateComponentDO> getGMV(String ds) {
        String sql = "select count(a.id)                     as num," +
                "       count(distinct (a.customer_id)) as uv," +
                "       sum(b.operating_purchase_price) as GMV," +
                "       a.mini_type," +
                "       a.merchant_id," +
                "       b.name" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id" +
                " where a.merchant_id = 100" +
                "  and b.item_type = 10" +
                "  and a.is_deleted = 0" +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.ds ="+ds +
                "  and b.ds ="+ds +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id,b.name";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateComponentDO domain = new OperateComponentDO();
            String nmv = record.getString("gmv");
            String num = record.getString("num");
            String uv = record.getString("uv");
            String name = record.getString("name");
            String miniType = record.getString("mini_type");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setMerchantId(merchantId);
            domain.setOrderCount(Long.parseLong(num));
            domain.setOrderUvCount(Long.parseLong(uv));
            domain.setGmv(new BigDecimal(nmv));
            domain.setName(name);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取加购配件支付订单 分组
     */
    private List<OperateComponentDO> getNMV(String ds) {
        String sql = "select count(a.id)                     as num," +
                "       count(distinct (a.customer_id)) as uv," +
                "       sum(b.operating_purchase_price) as nmv," +
                "       a.mini_type," +
                "       a.merchant_id," +
                "       b.name" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id" +
                " where a.merchant_id = 100" +
                "  and b.item_type = 10" +
                "  and a.parent_id = 0 " +
                "  and a.is_deleted = 0" +
                "  and a.type = 1 " +
                "  and a.ds ="+ds +
                "  and b.ds ="+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id,b.name";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateComponentDO domain = new OperateComponentDO();
            String nmv = record.getString("nmv");
            String num = record.getString("num");
            String uv = record.getString("uv");
            String name = record.getString("name");
            String miniType = record.getString("mini_type");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setMerchantId(merchantId);
            domain.setOrderPayCount(Long.parseLong(num));
            domain.setOrderPayUvCount(Long.parseLong(uv));
            domain.setNmv(new BigDecimal(nmv));
            domain.setName(name);
            return domain;
        }).collect(Collectors.toList());
    }
}
