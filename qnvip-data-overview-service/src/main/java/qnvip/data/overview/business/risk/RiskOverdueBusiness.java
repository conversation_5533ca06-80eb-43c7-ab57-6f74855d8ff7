package qnvip.data.overview.business.risk;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.google.common.collect.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.datacheck.DataCheckAfterRentOverviewDO;
import qnvip.data.overview.domain.risk.*;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.datacheck.DataCheckAfterRentOverviewService;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
import qnvip.data.overview.service.risk.RiskOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 风控大盘-经营/通用
 * create by gw on 2022/3/24
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskOverdueBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final Integer PAGE_SIZE = 10000;

    private static final Integer PAGE_SIZE_TIDB = 10000;

    private final OdpsUtil odpsUtil;
    private final RiskOrderOverdueService riskOrderOverdueService;
    private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
    private final DataCheckAfterRentOverviewService dataCheckAfterRentOverviewService;

    private final RentOrderService rentOrderService;



    private static AtomicInteger atomicInteger = new AtomicInteger();

    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    private static ThreadPoolExecutor threadPoolExecutorTidb = new ThreadPoolExecutor(1, 2, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    /**
     * @param ds
     */
    public void execOldData(String ds, int... overdueDays) {
        // order总记录数
//        riskOrderOverdueService.deleteAll();
//        riskGeneralOrderOverdueService.deleteAll();
//        dataCheckAfterRentOverviewService.deleteAll();
        Integer size = getCount(ds);
        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutor.execute(() -> {
                    oldRepayTask(ds, p, overdueDay);
                });
                startPage++;
            }
        }
    }

    /**
     * @param ds
     */
    public void execOldData_new(String ds, int... overdueDays) {
        // order总记录数
        riskOrderOverdueService.deleteAll();
        riskGeneralOrderOverdueService.deleteAll();
        dataCheckAfterRentOverviewService.deleteAll();

        Integer size = getCount(ds);
        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutor.execute(() -> {
                    oldRepayTask_new(ds, p, overdueDay);
                });
                startPage++;
            }
        }
    }


    /**
     * @param ds
     */
    public void execOldData_tidb(String ds, int... overdueDays) {
        // order总记录数
        riskGeneralOrderOverdueService.deleteAll();
        Integer size = getCountTidb(ds);

        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutorTidb.execute(() -> {
                    oldRepayTask_tidb(ds, p, overdueDay);
                });
                startPage++;
            }
        }
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String ds) {
        String sql = "select count(*) num" +
                " from rent_order ro" +
                "           inner join rent_order_logistics rol on ro.id = rol.order_id" +
                "    WHERE ro.ds =  " + ds +
                "     and rol.ds = " + ds + "" +
                "     and rol.sign_time is not null" +
                " and ro.merchant_id in (100,10000107)" +
                "  and ro.termination != 5" +
                "  and ro.biz_type = 2" +
                "  and ro.type = 1" +
                "  and ro.parent_id = 0" +
                "  AND date(ro.rent_start_date) >= '2020-10-01' " +
                ";";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    private Integer getCountTidb(String ds) {
        Integer cnt=rentOrderService.getGeneralCount();
        return cnt;
    }


    private void oldRepayTask(String ds, int startPage, Integer overdueDay) {

        String sql = "with dt as (\n" +
                "           select date_format(now(), 'yyyymmdd') as ds\n" +
                "   )\n" +
                "   SELECT date(ro.rent_start_date)                                                                        count_day,\n" +
                "          rorp.order_id,\n" +
                "          nvl(min(aa.hitValue), '')                                                                       hit_value,\n" +
                "          ro.no                                                                                           order_no,\n" +
                "          ro.mini_type,\n" +
                "          date(repay_date)                                                                                repay_date,\n" +
                "          real_repay_time                                                                                 real_repay_time,\n" +
                "          cc.zmf_level                                                                                     zmf_level,\n" +
                "          nvl(ff.is_rent,'非租物')                                                                            is_rent,\n" +
                "          (CASE\n" +
                "               WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'\n" +
                "               WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信'\n" +
                "               WHEN ro.mini_type = 2 THEN '字节跳动'\n" +
                "               WHEN ro.mini_type = 3 THEN 'app' END)                                                      platform,\n" +
                "          rate_config_type                                                                                finance_type,\n" +
                "          rorp.term                                                                                       term,\n" +
                "          cl.riskOpinion                                                                                  risk_level,\n" +
                "           if(cl.scene ='','未标记上场景值',cl.scene)                                                        scene,\n" +
                "           if(cl.quotientname ='','未标记上导流商',cl.quotientname)                                          quotient_name," +
                "          sn.riskStrategy                                                                                 risk_strategy,\n" +
                "          artificialAuditorId                                                                             audit_type,\n" +
                "          (case\n" +
                "               when\n" +
                "                       add_months(date(repay_date) - day(repay_date) + 2, 1) > date(getdate())\n" +
                "                   THEN (case\n" +
                "                             when year(repay_date)=year(getdate()) and month(repay_date) > month" +
                " (getdate()) then '0'\n" +
                "                             when term = 1\n" +
                "                                 then if(date(repay_date) >= date(getdate()), '0',\n" +
                "                                         if(min(repay_status) = 1 and min(overdue) = 5, '1', '0'))\n" +
                "                             else IF(min(repay_status) = 1 and min(overdue) = 5, '1', if(min(repay_status) =5, '0', '2'))\n" +
                "                   end\n" +
                "                   )\n" +
                "               else\n" +
                "                   (case\n" +
                "                        when\n" +
                "                                date(NVL(real_repay_time, getdate())) <= add_months(date(repay_date)" +
                " -\n" +
                "                                                                                   day(repay_date) + 2, 1)\n" +
                "                            then if( min(repay_status) = 5 ,'0','1')\n" +
                "                        else IF(datediff(date(NVL(real_repay_time, getdate())),\n" +
                "                                         date(add_months(date(repay_date) - day(repay_date) + 2, 1))" +
                ", 'dd') > " + overdueDay + " , '1',\n" +
                "                                IF(real_repay_time is not null and min(repay_status) = 5 , '0', '1'))\n" +
                "                       end)\n" +
                "              end\n" +
                "              )                                                                                           is_overdue,\n" +
                "          nvl(if(min(g.ext) != '', get_json_object(min(g.ext), '$.discountReturnAmt'), '0'),\n" +
                "              '0')                                                                                        discount_return_amt,\n" +
                "          if(c.rate_config_type not in( 10,30), min(c.actual_financing_amt), avg(rorp.capital) * 12)               rent_total,\n" +
                "          if(c.rate_config_type not in( 10,30) , max(c.buyout_amt), 0)                              " +
                "                buyout_amt,\n" +
                "          if(c.rate_config_type != 10, max(h.user_actual_buyout_amt), 0)                                  act_buyout_amt,\n" +
                "          if(c.rate_config_type != 10, max(h.manage_buyout_discount) + max(h.offline_buyout_discount), 0) buyout_discount,\n" +
                "          nvl(min(rorp.overdue_fine), 0)                                                                  overdue_fine,\n" +
                "          nvl(min(c.bond_amt), 0)                                                                         bond_amt,\n" +
                "          nvl(min(x.before_discount), 0)                                                                  before_discount,\n" +
                "          nvl(min(x.total_discount), 0)                                                                   total_discount,\n" +
                "          if(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0')                                    forced_conversion,\n" +
                "          round(nvl(min(c.bond_amt) / ((if(c.rate_config_type != 10, min(c.actual_financing_amt), avg(rorp\n" +
                "              .capital) * 12) + if(c.rate_config_type != 10, min(c.buyout_amt), 0)) - nvl(min(x.total_discount), 0)),\n" +
                "                    0), 2)\n" +
                "                                                                                                          bond_rate,\n" +
                "          (case\n" +
                "               when min(repay_status) = 5 then avg(rorp.capital)\n" +
                "               when min(repay_status) = 1 then (avg(capital) - avg(real_repay_capital)) end)\n" +
                "                                                                                                          real_capital,\n" +
                "          avg(rorp.capital)                                                                          capital,\n" +
                "          min(rorp.is_deleted)                                                                            deleted,\n" +
                "          min(repay_status)                                                                               repay_status,\n" +
                "          min(overdue)                                                                                    overdue,\n" +
                "          if(real_repay_time is null, 1, 0)                                                               real_repay_time_status,\n" +
                "          min(c.renew_total_rent)                                                                         renew_total_rent,\n" +
                "          max(repayment_term)                                                                                  max_term,\n" +
                "          min(renew_term)                                                                                 renew_term,\n" +
                "          if(min(ro.status) = 15 and datediff(date(getdate()), date(min(ro.rent_end_date))) > 7, 1, 0)\n" +
                "                                                                                                          renew_status,\n" +
                "          datediff(date(getdate()), date(min(ro.rent_end_date)), 'mm')                                    renew_day,\n" +
                "          min(surplus_bond_amt)                                                                           surplus_bond_amt,\n" +
                "          min(roi.actual_supply_price)                                                                    actual_supply_price,\n" +
                "          min(roos.current_overdue_days)                                                                  current_overdue_days,\n" +
                "          min(rorp.overdue_days)                                                                          term_overdue_days,\n" +
                "          if(min(ro.settle_date) is not null, 1, 0)                                                       is_settle,\n" +
                "          min(ro.status)                                                                                  order_status,\n" +
                "          sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0))      surplus_amt,\n" +
                "          sum(if(repay_status = 5,real_repay_capital,0))      already_pay,\n" +
                "          if(min(y.id) is not null and min(k.lawsuit_status) not in (2, 5, 9), 1, 0)                      renew_type\n" +
                "   FROM rent_order ro\n" +
                "            INNER JOIN rent_order_repayment_plan rorp\n" +
                "                       ON ro.id = rorp.order_id AND rorp.ds = (select ds from dt) AND rorp.is_deleted = 0\n" +
                "            INNER JOIN rent_order_item roi\n" +
                "                       ON ro.id = roi.order_id AND roi.ds = (select ds from dt) AND roi.is_deleted = 0\n" +
                "            INNER JOIN rent_order_overdue_stat roos\n" +
                "                       ON ro.id = roos.order_id AND roos.ds = (select ds from dt) AND roos.is_deleted = 0\n" +
                "            LEFT join (SELECT min(ext) ext, order_id\n" +
                "                       FROM rent_order_flow b\n" +
                "                       WHERE b.ds = (select ds from dt)\n" +
                "                         AND b.biz_type = 3\n" +
                "                         AND b.is_deleted = 0\n" +
                "                         AND b.pay_status = 10\n" +
                "                         and (b.mark_refund IS NULL OR b.mark_refund = 0)\n" +
                "                         AND b.flow_type = 1\n" +
                "                         AND b.refunded_amt = 0\n" +
                "                         AND b.ext IS NOT NULL\n" +
                "                         AND b.ext != ''\n" +
                "                       GROUP BY order_id) g ON ro.id = g.order_id\n" +
                "            INNER JOIN rent_order_finance_detail c ON ro.id = c.order_id AND c.ds = (select ds from dt)  AND c.is_deleted = 0\n" +
                "         left join (select order_id, " +
                "                            sum(manage_write_off_amt)  manage_write_off_amt, " +
                "                            sum(before_write_off_amt)  before_discount, " +
                "                            min(total_before_write_off_amt) total_discount" +
                "                     from (select x.order_id, " +
                "                                  if(ISNULL(min(x.bind_order_time)) or " +
                "                                         min(y.payment_time) < min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0), 0) manage_write_off_amt," +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1, " +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1, " +
                "                                            x.write_off_amt, 0)), 0), 0) before_write_off_amt," +
                "                                  min(z.total_before_write_off_amt)  total_before_write_off_amt " +
                "                           from rent_customer_coupon x " +
                "                                    inner join rent_order y on x.order_id = y.id " +
                "                                    inner join (select x.order_id,sum(write_off_amt) total_before_write_off_amt\n" +
                "                                        from (select order_id\n" +
                "                                                   , nvl(x.write_off_amt, 0)                                                                                               write_off_amt\n" +
                "                                                   , if(x.type in (2, 4), 1, row_number() over( partition by x.order_id,x.use_term order by nvl(x.write_off_amt,0) desc )) rowid\n" +
                "                                              from rent_customer_coupon x\n" +
                "                                                       inner join rent_order y on x.order_id = y.id\n" +
                "                                              where x.order_id > 0\n" +
                "                                                and x.scene = 1\n" +
                "                                                and x.is_deleted = 0\n" +
                "                                                and x.ds = " + ds +
                "                                                and x.bind_order_time is not null\n" +
                "                                                and y.payment_time >= x.bind_order_time\n" +
                "                                                and y.ds = " + ds +
                "                                                and y.is_deleted = 0) x\n" +
                "                                        where x.rowid=1\n" +
                "                                        group by x.order_id) z on z.order_id= y.id" +
                "                           where x.order_id > 0 and x.scene = 1 " +
                "                             and x.is_deleted = 0 " +
                "                             and y.is_deleted = 0 " +
                "                             and x.ds = " + ds +
                "                             and y.ds = " + ds +
                "                           group by x.order_id, x.type) " +
                "                     group by order_id) x on ro.id = x.order_id " +
                "            INNER JOIN cl_loan cl ON cl.loanno = ro.no AND cl.ds = (select ds from dt)\n" +
                "            INNER JOIN serial_no sn ON sn.businessno = ro.no AND sn.ds = (select ds from dt)\n" +
                "            left join rent_order_infomore k on ro.id = k.order_id and k.ds = (select ds from dt) AND   k.is_deleted = 0\n" +
                "            left join (select order_id,\n" +
                "                              sum(user_actual_buyout_amt)       user_actual_buyout_amt,\n" +
                "                              sum(buyout_coupon_amount)         manage_buyout_discount,\n" +
                "                              sum(buyout_coupon_offline_amount) offline_buyout_discount\n" +
                "                       from rent_order_account_check\n" +
                "                       where is_deleted = 0\n" +
                "                         and ds = (select ds from dt)\n" +
                "                       group by order_id) h on ro.id = h.order_id\n" +
                "            left join (select id, parent_id\n" +
                "                       from rent_order\n" +
                "                       where is_deleted = 0\n" +
                "                         and ds = (select ds from dt)) y on ro.id = y.parent_id\n" +
                "            INNER join (SELECT ro.id\n" +
                "                        FROM rent_order ro\n" +
                "                                 INNER JOIN rent_order_logistics rol ON ro.id = rol.order_id\n" +
                "                        WHERE ro.ds = (select ds from dt)\n" +
                "                          AND rol.ds = (select ds from dt)\n" +
                "                          AND ro.merchant_id in (100,10000107)\n" +
                "                          AND rol.sign_time IS NOT NULL\n" +
                "                          AND ro.parent_id = 0\n" +
                "                          AND ro.termination != 5\n" +
                "                          AND ro.is_deleted = 0\n" +
                "                          AND ro.type = 1\n" +
                "                          AND date(ro.rent_start_date) >= date'2020-10-01'\n" +
                "                        ORDER BY ro.id\n" +
                "                     limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ") z ON ro.id = z.id " +
                "            left join (SELECT rc.hitValue, sn.businessNo\n" +
                "                       FROM serial_no sn\n" +
                "                                INNER JOIN\n" +
                "                            rc_risk_access_rule_result_2023 rc on sn.id = rc.serialnoId\n" +
                "                                INNER JOIN rc_risk_strategy_rule_set rr on rr.id = rc.ruleId\n" +
                "                       WHERE rr.scene = 3\n" +
                "                         and rc.hitValue != ''\n" +
                "                         and rr.masterModel = 1\n" +
                "                         and sn.riskStrategy = 'qnvip_d'\n" +
                "                         and rc.ds = (select ds from dt)\n" +
                "                         and rr.ds = (select ds from dt)\n" +
                "                         and sn.ds = (select ds from dt)\n" +
                "   ) aa on ro.no = aa.businessNo\n" +
                "   left join (" +
                "         select order_id,\n" +
                "             (CASE\n" +
                "                  WHEN freeze_amt = 0 AND deposit_free_amt > 0 THEN '750分+'\n" +
                "                  WHEN freeze_amt = 1 AND deposit_free_amt > 0 THEN '700-750分'\n" +
                "                  WHEN freeze_amt = 2 AND deposit_free_amt > 0 THEN '650-700分'\n" +
                "                  WHEN freeze_amt = 3 AND deposit_free_amt > 0 THEN '600-650分'\n" +
                "                  WHEN freeze_amt > 3 AND deposit_free_amt > 0 THEN '600分以下'\n" +
                "                  WHEN freeze_amt = 0 AND deposit_free_amt = 0 THEN '非免押用户'\n" +
                "                END)\n" +
                "                 AS zmf_level\n" +
                "       from (select order_id,\n" +
                "                   if(finance.bond_free_status = 0, 0, finance.frozen_amt - finance.bond_free_credit_amt) freeze_amt,\n" +
                "                   if(finance.bond_free_status = 0, 0, finance.bond_free_credit_amt) as                   deposit_free_amt\n" +
                "            from rent_order_finance_detail finance\n" +
                "            where ds = (select ds from dt)\n" +
                "              and is_deleted = 0\n" +
                "           ) ) cc on cc.order_id=ro.id " +
                "     left join (\n" +
                "    select type, if(remark like '%租物%', '租物', '非租物') is_rent\n" +
                "      from rent_finance_template where  status=1 AND is_deleted = 0\n" +
                "    ) ff on ff.type = c.rate_config_type   " +
                "   WHERE ro.ds = (select ds from dt)\n" +
                "   GROUP BY date(ro.rent_start_date), ro.mini_type, rorp.order_id, ro.no, cl.scene, cl.quotientname,rate_config_type,\n" +
                "            IF(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0'), rorp.term, cl.riskOpinion,\n" +
                "            sn.riskStrategy, cl.artificialAuditorId,\n" +
                "            repay_date,\n" +
                "            is_rent,\n" +
                "            zmf_level,\n" +
                "            real_repay_time,\n" +
                "            (c.bond_amt / (if(c.rate_config_type != 10, c.actual_financing_amt,\n" +
                "                              rorp.capital * 12) + if(c.rate_config_type != 10, c.buyout_amt, 0)) - x.total_discount)\n" +
                "   ORDER BY date(ro.rent_start_date);";
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        saveData(records, overdueDay);
    }

    private void oldRepayTask_new(String ds, int startPage, Integer overdueDay) {

        String sql = "with dt as (\n" +
                "           select date_format(now(), 'yyyymmdd') as ds\n" +
                "   )\n" +
                "   SELECT date(ro.rent_start_date)                                                                        count_day,\n" +
                "          rorp.order_id,\n" +
                "          nvl(min(aa.hitValue), '')                                                                       hit_value,\n" +
                "          ro.no                                                                                           order_no,\n" +
                "          ro.mini_type,\n" +
                "          date(repay_date)                                                                                repay_date,\n" +
                "          real_repay_time                                                                                 real_repay_time,\n" +
                "          cc.zmf_level                                                                                     zmf_level,\n" +
                "          nvl(ff.is_rent,'非租物')                                                                            is_rent,\n" +
                "          (CASE\n" +
                "               WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'\n" +
                "               WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信'\n" +
                "               WHEN ro.mini_type = 2 THEN '字节跳动'\n" +
                "               WHEN ro.mini_type = 3 THEN 'app' END)                                                      platform,\n" +
                "          rate_config_type                                                                                finance_type,\n" +
                "          rorp.term                                                                                       term,\n" +
                "          cl.riskOpinion                                                                                  risk_level,\n" +
                "           if(cl.scene ='','未标记上场景值',cl.scene)                                                        scene,\n" +
                "           if(cl.quotientname ='','未标记上导流商',cl.quotientname)                                          quotient_name," +
                "          sn.riskStrategy                                                                                 risk_strategy,\n" +
                "          artificialAuditorId                                                                             audit_type,\n" +
                "          (case\n" +
                "               when\n" +
                "                       add_months(date(repay_date) - day(repay_date) + 2, 1) > date(getdate())\n" +
                "                   THEN (case\n" +
                "                             when year(repay_date)=year(getdate()) and month(repay_date) > month" +
                " (getdate()) then '0'\n" +
                "                             when term = 1\n" +
                "                                 then if(date(repay_date) >= date(getdate()), '0',\n" +
                "                                         if(min(repay_status) = 1 and min(overdue) = 5, '1', '0'))\n" +
                "                             else IF(min(repay_status) = 1 and min(overdue) = 5, '1', if(min(repay_status) =5, '0', '2'))\n" +
                "                   end\n" +
                "                   )\n" +
                "               else\n" +
                "                   (case\n" +
                "                        when\n" +
                "                                date(NVL(real_repay_time, getdate())) <= add_months(date(repay_date)" +
                " -\n" +
                "                                                                                   day(repay_date) + 2, 1)\n" +
                "                            then if( min(repay_status) = 5 ,'0','1')\n" +
                "                        else IF(datediff(date(NVL(real_repay_time, getdate())),\n" +
                "                                         date(add_months(date(repay_date) - day(repay_date) + 2, 1))" +
                ", 'dd') > " + overdueDay + " , '1',\n" +
//                ", 'dd') > 0 "  + " , '1',\n" +
                "                                IF(real_repay_time is not null and min(repay_status) = 5 , '0', '1'))\n" +
                "                       end)\n" +
                "              end\n" +
                "              )                                                                                           is_overdue,\n" +
                "          nvl(if(min(g.ext) != '', get_json_object(min(g.ext), '$.discountReturnAmt'), '0'),\n" +
                "              '0')                                                                                        discount_return_amt,\n" +
                "          if(min(c.renew_type) != 2, min(c.actual_financing_amt), avg(rorp.capital) * 12)     rent_total,\n" +
                "          if(min(c.renew_type) != 2 , max(c.buyout_amt),0)                            " +
                "                buyout_amt,\n" +
                "          nvl(min(rorp.discount_amt),0)       discount_amt,\n"+
                "          if(c.rate_config_type != 10, max(h.user_actual_buyout_amt), 0)                                  act_buyout_amt,\n" +
                "          if(c.rate_config_type != 10, max(h.manage_buyout_discount) + max(h.offline_buyout_discount), 0) buyout_discount,\n" +
                "          nvl(min(rorp.overdue_fine), 0)                                                                  overdue_fine,\n" +
                "          nvl(min(c.bond_amt), 0)                                                                         bond_amt,\n" +
                "          nvl(min(x.before_discount), 0)                                                                  before_discount,\n" +
                "          nvl(min(c.diff_pricing_discount_amt),0) + nvl(min(c.coupon_discount_amt),0)                     total_discount,\n" +
                "          if(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0')                                    forced_conversion,\n" +
                "          round(nvl(min(c.bond_amt) / ((if(c.rate_config_type != 10, min(c.actual_financing_amt), avg(rorp\n" +
                "              .capital) * 12) + if(c.rate_config_type != 10, min(c.buyout_amt), 0)) - nvl(min(x.total_discount), 0)),\n" +
                "                    0), 2)\n" +
                "                                                                                                          bond_rate,\n" +
                "          (case\n" +
                "               when min(repay_status) = 5 then avg(rorp.capital)  \n" +
                "               when min(repay_status) = 1 then avg(capital) - (avg(real_repay_capital) - avg(discount_amt)) end)\n" +
                "                                                                                                          real_capital,\n" +
                "          avg(rorp.capital)                                                                               capital,\n" +
                "          min(rorp.is_deleted)                                                                            deleted,\n" +
                "          min(repay_status)                                                                               repay_status,\n" +
                "          min(overdue)                                                                                    overdue,\n" +
                "          if(real_repay_time is null, 1, 0)                                                               real_repay_time_status,\n" +
                "          min(c.renew_total_rent)                                                                         renew_total_rent,\n" +
                "          min(IF(c.discount_pricing_mode IS NULL,0,c.discount_pricing_mode))                              discount_pricing_mode,\n"+
                "          max(repayment_term)                                                                                  max_term,\n" +
                "          min(renew_term)                                                                                 renew_term,\n" +
                "          if(min(ro.status) = 15 and datediff(date(getdate()), date(min(ro.rent_end_date))) > 7, 1, 0)\n" +
                "                                                                                                          renew_status,\n" +
                "          datediff(date(getdate()), date(min(ro.rent_end_date)), 'mm')                                    renew_day,\n" +
                "           nvl(min(c.bond_rest_fund_amount),0)     bond_rest_fund_amount,\n"+
                "           nvl(min(c.diff_pricing_discount_amt),0)     diff_pricing_discount_amt,\n"+
                "           nvl(min(c.coupon_discount_amt),0)     coupon_discount_amt,\n"+
                "          nvl(min(c.surplus_bond_amt),0)                                                                  surplus_bond_amt,\n" +
                "          min(roi.actual_supply_price)                                                                    actual_supply_price,\n" +
                "          min(roos.current_overdue_days)                                                                  current_overdue_days,\n" +
                "          min(rorp.overdue_days)                                                                          term_overdue_days,\n" +
                "          if(min(ro.settle_date) is not null, 1, 0)                                                       is_settle,\n" +
                "          min(ro.status)                                                                                  order_status,\n" +
                "          sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0))      surplus_amt,\n" +
                "          sum(if(repay_status = 5,real_repay_capital,0))      already_pay,\n" +
                "          if(min(y.id) is not null and min(k.lawsuit_status) not in (2, 5, 9), 1, 0)                      renew_type\n" +
                "   FROM rent_order ro\n" +
                "            INNER JOIN rent_order_repayment_plan rorp\n" +
                "                       ON ro.id = rorp.order_id AND rorp.ds = (select ds from dt) AND rorp.is_deleted = 0\n" +
                "            INNER JOIN rent_order_item roi\n" +
                "                       ON ro.id = roi.order_id AND roi.ds = (select ds from dt) AND roi.is_deleted = 0\n" +
                "            INNER JOIN rent_order_overdue_stat roos\n" +
                "                       ON ro.id = roos.order_id AND roos.ds = (select ds from dt) AND roos.is_deleted = 0\n" +
                "            LEFT join (SELECT min(ext) ext, order_id\n" +
                "                       FROM rent_order_flow b\n" +
                "                       WHERE b.ds = (select ds from dt)\n" +
                "                         AND b.biz_type = 3\n" +
                "                         AND b.is_deleted = 0\n" +
                "                         AND b.pay_status = 10\n" +
                "                         and (b.mark_refund IS NULL OR b.mark_refund = 0)\n" +
                "                         AND b.flow_type = 1\n" +
                "                         AND b.refunded_amt = 0\n" +
                "                         AND b.ext IS NOT NULL\n" +
                "                         AND b.ext != ''\n" +
                "                       GROUP BY order_id) g ON ro.id = g.order_id\n" +
                "            INNER JOIN rent_order_finance_detail c ON ro.id = c.order_id AND c.ds = (select ds from dt)  AND c.is_deleted = 0\n" +
                "         left join (select order_id, " +
                "                            sum(manage_write_off_amt)  manage_write_off_amt, " +
                "                            sum(before_write_off_amt)  before_discount, " +
                "                            min(total_before_write_off_amt) total_discount" +
                "                     from (select x.order_id, " +
                "                                  if(ISNULL(min(x.bind_order_time)) or " +
                "                                         min(y.payment_time) < min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0), 0) manage_write_off_amt," +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1, " +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1, " +
                "                                            x.write_off_amt, 0)), 0), 0) before_write_off_amt," +
                "                                  min(z.total_before_write_off_amt)  total_before_write_off_amt " +
                "                           from rent_customer_coupon x " +
                "                                    inner join rent_order y on x.order_id = y.id " +
                "                                    inner join (select x.order_id,sum(write_off_amt) total_before_write_off_amt\n" +
                "                                        from (select order_id\n" +
                "                                                   , nvl(x.write_off_amt, 0)                                                                                               write_off_amt\n" +
                "                                                   , if(x.type in (2, 4), 1, row_number() over( partition by x.order_id,x.use_term order by nvl(x.write_off_amt,0) desc )) rowid\n" +
                "                                              from rent_customer_coupon x\n" +
                "                                                       inner join rent_order y on x.order_id = y.id\n" +
                "                                              where x.order_id > 0\n" +
                "                                                and x.scene = 1\n" +
                "                                                and x.is_deleted = 0\n" +
                "                                                and x.ds = " + ds +
                "                                                and x.bind_order_time is not null\n" +
                "                                                and y.payment_time >= x.bind_order_time\n" +
                "                                                and y.ds = " + ds +
                "                                                and y.is_deleted = 0) x\n" +
                "                                        where x.rowid=1\n" +
                "                                        group by x.order_id) z on z.order_id= y.id" +
                "                           where x.order_id > 0 and x.scene = 1 " +
                "                             and x.is_deleted = 0 " +
                "                             and y.is_deleted = 0 " +
                "                             and x.ds = " + ds +
                "                             and y.ds = " + ds +
                "                           group by x.order_id, x.type) " +
                "                     group by order_id) x on ro.id = x.order_id " +
                "            INNER JOIN cl_loan cl ON cl.loanno = ro.no AND cl.ds = (select ds from dt)\n" +
                "            INNER JOIN serial_no sn ON sn.businessno = ro.no AND sn.ds = (select ds from dt)\n" +
                "            left join rent_order_infomore k on ro.id = k.order_id and k.ds = (select ds from dt) AND   k.is_deleted = 0\n" +
                "            left join (select order_id,\n" +
                "                              sum(user_actual_buyout_amt)       user_actual_buyout_amt,\n" +
                "                              sum(buyout_coupon_amount)         manage_buyout_discount,\n" +
                "                              sum(buyout_coupon_offline_amount) offline_buyout_discount\n" +
                "                       from rent_order_account_check\n" +
                "                       where is_deleted = 0\n" +
                "                         and ds = (select ds from dt)\n" +
                "                       group by order_id) h on ro.id = h.order_id\n" +
                "            left join (select id, parent_id,id AS n_id \n" +
                "                       from rent_order\n" +
                "                       where is_deleted = 0\n" +
                "                         and ds = (select ds from dt)) y on ro.id = y.parent_id\n" +
                "            INNER join (SELECT ro.id\n" +
                "                        FROM rent_order ro\n" +
                "                                 INNER JOIN rent_order_logistics rol ON ro.id = rol.order_id\n" +
                "                        WHERE ro.ds = (select ds from dt)\n" +
                "                          AND rol.ds = (select ds from dt)\n" +
                "                          AND ro.merchant_id in (100,10000107)\n" +
                "                          AND rol.sign_time IS NOT NULL\n" +
                "                          AND ro.parent_id = 0\n" +
                "                          AND ro.termination != 5\n" +
                "                          AND ro.is_deleted = 0\n" +
                "                          AND ro.type = 1\n" +
                "                          AND date(ro.rent_start_date) >= date'2020-10-01'\n" +
                "                        ORDER BY ro.id\n" +
                "                     limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ") z ON ro.id = z.id " +
                "            left join (SELECT rc.hitValue, sn.businessNo\n" +
                "                       FROM serial_no sn\n" +
                "                                INNER JOIN\n" +
                "                            rc_risk_access_rule_result_2023 rc on sn.id = rc.serialnoId\n" +
                "                                INNER JOIN rc_risk_strategy_rule_set rr on rr.id = rc.ruleId\n" +
                "                       WHERE rr.scene = 3\n" +
                "                         and rc.hitValue != ''\n" +
                "                         and rr.masterModel = 1\n" +
                "                         and sn.riskStrategy = 'qnvip_d'\n" +
                "                         and rc.ds = (select ds from dt)\n" +
                "                         and rr.ds = (select ds from dt)\n" +
                "                         and sn.ds = (select ds from dt)\n" +
                "   ) aa on ro.no = aa.businessNo\n" +
                "   left join (" +
                "         select order_id,\n" +
                "             (CASE\n" +
                "                  WHEN freeze_amt = 0 AND deposit_free_amt > 0 THEN '750分+'\n" +
                "                  WHEN freeze_amt = 1 AND deposit_free_amt > 0 THEN '700-750分'\n" +
                "                  WHEN freeze_amt = 2 AND deposit_free_amt > 0 THEN '650-700分'\n" +
                "                  WHEN freeze_amt = 3 AND deposit_free_amt > 0 THEN '600-650分'\n" +
                "                  WHEN freeze_amt > 3 AND deposit_free_amt > 0 THEN '600分以下'\n" +
                "                  WHEN freeze_amt = 0 AND deposit_free_amt = 0 THEN '非免押用户'\n" +
                "                END)\n" +
                "                 AS zmf_level\n" +
                "       from (select order_id,\n" +
                "                   if(finance.bond_free_status = 0, 0, finance.frozen_amt - finance.bond_free_credit_amt) freeze_amt,\n" +
                "                   if(finance.bond_free_status = 0, 0, finance.bond_free_credit_amt) as                   deposit_free_amt\n" +
                "            from rent_order_finance_detail finance\n" +
                "            where ds = (select ds from dt)\n" +
                "              and is_deleted = 0\n" +
                "           ) ) cc on cc.order_id=ro.id " +
                "     left join (\n" +
                "    select type, if(remark like '%租物%', '租物', '非租物') is_rent\n" +
                "      from rent_finance_template where  status=1 AND is_deleted = 0\n" +
                "    ) ff on ff.type = c.rate_config_type   " +
                "   WHERE ro.ds = (select ds from dt)\n" +
                "   GROUP BY date(ro.rent_start_date), ro.mini_type, rorp.order_id, ro.no, cl.scene, cl.quotientname,rate_config_type,\n" +
                "            IF(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0'), rorp.term, cl.riskOpinion,\n" +
                "            sn.riskStrategy, cl.artificialAuditorId,\n" +
                "            repay_date,\n" +
                "            is_rent,\n" +
                "            zmf_level,\n" +
                "            real_repay_time,\n" +
                "            (c.bond_amt / (if(c.rate_config_type != 10, c.actual_financing_amt,\n" +
                "                              rorp.capital * 12) + if(c.rate_config_type != 10, c.buyout_amt, 0)) - nvl(c.diff_pricing_discount_amt,0)-nvl(c.coupon_discount_amt,0))\n" +
                "   ORDER BY date(ro.rent_start_date);";
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        log.info("大盘通用模块查询原始表跑出来的数据量大小为:ds="+ds+"  总条数为: "+records.size());
        saveData_new(records, overdueDay);
    }


    private void oldRepayTask_tidb(String ds, int startPage, Integer overdueDay) {
        startPage = startPage * PAGE_SIZE;
        List<GeneralRecord> generalRecords = rentOrderService.getAllGeneralData(startPage,PAGE_SIZE,overdueDay);
        log.info("tidb-生产环境-大盘通用模块查询原始表跑出来的数据量大小为:ds="+ds+"  总条数为: "+generalRecords.size()+"开始页startPage="+startPage+" PAGE_SIZE="+PAGE_SIZE);
        saveData_tidb(generalRecords, overdueDay);
    }

    public void updateScore(String ds) {
        Integer pageSize = 10000;
        Integer pageNo = 0;
        String prefix = "select count(*) num from ";
        String sql = "SELECT rc.hitvalue hit_value, ro.id\n" +
                " FROM cl_loan cl\n" +
                "          inner join rent_order ro on ro.no = cl.loanno\n" +
                "         INNER JOIN serial_no sn ON cl.loanno = sn.businessNo\n" +
                "         INNER JOIN rc_risk_access_rule_result_2021 rc on sn.id = rc.serialnoId\n" +
                "         INNER JOIN rc_risk_strategy_rule_set rr on rr.ruleId = rc.ruleId\n" +
                " WHERE rr.scene = 201\n" +
                "  and rc.hitValue != ''\n" +
                // "  and sn.riskStrategy = 'qnvip_d'\n" +
                "  and sn.ds = " + ds +
                "  and ro.ds = " + ds +
                "  and rr.ds = " + ds +
                "  and cl.ds = " + ds +
                "  and cl.createTime >= '2021-10-01 00:00:00'\n" +
                "  and cl.createTime < '2022-01-01 00:00:00'\n" +
                " group by ro.id, rc.hitvalue order by ro.id";

        String countSql = prefix + "(" + sql + ");";
        List<Record> countRecords = odpsUtil.querySql(countSql);
        Integer num = Integer.valueOf(countRecords.get(0).getString("num"));
        int times = num / pageSize;
        if (num % pageSize != 0) {
            times += 1;
        }
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = " limit " + startPage * pageSize + "," + pageSize + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql.concat(";"));
            if (CollUtil.isEmpty(records)) {
                continue;
            }
            Map<Long, Integer> no2score = Maps.newHashMap();
            for (Record record : records) {
                String hitValue = record.getString("hit_value");
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100015");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                no2score.put(Long.parseLong(record.getString("id")), score);
            }
            List<Long> collect = new ArrayList<>(no2score.keySet());
            List<RiskGeneralOrderOverdueDO> list = riskGeneralOrderOverdueService.getList(collect);
            List<RiskGeneralOrderOverdueDO> finalList = list.stream().peek(o -> {
                Long orderId = o.getOrderId();
                Integer score = no2score.get(orderId);
                o.setScore(score);
            }).collect(Collectors.toList());
            riskGeneralOrderOverdueService.updateBatchById(finalList);
        }
    }

    private void saveData(List<Record> records, Integer overdueDay) {
        List<RiskOrderOverdueDO> resList = fetchList(records, overdueDay);
        riskOrderOverdueService.saveBatch(resList);
        //  通用列表
        // List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay);
        //  当前时间>最后还款时间并且status = 15,为租后待买断订单,按照续租处理,生成假续租订单
        //  取出最大续租期数以及续租总租金
        //  只要更新续租订单的renewAmt字段为续租总租金,以及更新Pmaxterm就行
        List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay)
                .stream()
                .peek(o -> {
                    if (o.getRenewStatus() == 1) {
                        Integer maxTerm = o.getMaxTerm();
                        Integer renewTerm = o.getRenewTerm();
                        Integer renewDay = o.getRenewDay();
                        BigDecimal parentNotReturnAmt = getParentNotReturnAmt(o, maxTerm);
                        int newMaxTerm = maxTerm + renewTerm;
                        o.setMaxTerm(newMaxTerm);

                        for (int i = 1; i <= renewTerm; i++) {
                            // 将假续租订单的逾期未还金额设置为,租后待买断订单的最后一期逾期未还金额
                            setTermNotRepayVal(o, maxTerm + i, parentNotReturnAmt);
                            // 设置续租总租金
                            setRenewAmt(o, maxTerm + i, o.getRenewAmt1());
                        }
                        // 设置逾期情况,判断续租待买断订单的逾期时间
                        for (int i = 1, day = renewDay + 1; i <= day && maxTerm + i <= newMaxTerm; i++) {
                            setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
                        }
                    }
                }).collect(Collectors.toList());
        riskGeneralOrderOverdueService.saveBatch(generalList);

        List<DataCheckAfterRentOverviewDO> checkList = fetchDataCheckList(records);
        dataCheckAfterRentOverviewService.saveBatch(checkList);

    }

    private void saveData_new(List<Record> records, Integer overdueDay) {
//        List<RiskOrderOverdueDO> resList = fetchList(records, overdueDay);
//        riskOrderOverdueService.saveBatch(resList);
        List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList_new(records, overdueDay)
                .stream()
                .peek(o -> {
                    if (o.getRenewStatus() == 1) {
                        Integer maxTerm = o.getMaxTerm();
                        Integer renewTerm = o.getRenewTerm();
                        Integer renewDay = o.getRenewDay();
                        BigDecimal parentNotReturnAmt = getParentNotReturnAmt(o, maxTerm);
                        int newMaxTerm = maxTerm + renewTerm;
                        o.setMaxTerm(newMaxTerm);

                        for (int i = 1; i <= renewTerm; i++) {
                            // 将假续租订单的逾期未还金额设置为,租后待买断订单的最后一期逾期未还金额
                            setTermNotRepayVal(o, maxTerm + i, parentNotReturnAmt);
                            // 设置续租总租金
                            setRenewAmt(o, maxTerm + i, o.getRenewAmt1());
                        }
                        // 设置逾期情况,判断续租待买断订单的逾期时间
                        for (int i = 1, day = renewDay + 1; i <= day && maxTerm + i <= newMaxTerm; i++) {
                            setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
                        }
                    }
                }).collect(Collectors.toList());
        riskGeneralOrderOverdueService.saveBatch(generalList);
//        List<DataCheckAfterRentOverviewDO> checkList = fetchDataCheckList(records);
//        dataCheckAfterRentOverviewService.saveBatch(checkList);
    }


    private void saveData_tidb(List<GeneralRecord> records, Integer overdueDay) {
        // 经营看板使用,目前已经下线
//        List<RiskOrderOverdueDO> resList = fetchList(records, overdueDay);
//        riskOrderOverdueService.saveBatch(resList);
        List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList_tidb(records, overdueDay)
                .stream()
                .peek(o -> {
                    if (o.getRenewStatus() == 1) {
                        Integer maxTerm = o.getMaxTerm();
                        Integer renewTerm = o.getRenewTerm();
                        Integer renewDay = o.getRenewDay();
                        BigDecimal parentNotReturnAmt = getParentNotReturnAmt(o, maxTerm);
                        int newMaxTerm = maxTerm + renewTerm;
                        o.setMaxTerm(newMaxTerm);

                        for (int i = 1; i <= renewTerm; i++) {
                            // 将假续租订单的逾期未还金额设置为,租后待买断订单的最后一期逾期未还金额
                            setTermNotRepayVal(o, maxTerm + i, parentNotReturnAmt);
                            // 设置续租总租金
                            setRenewAmt(o, maxTerm + i, o.getRenewAmt1());
                        }

                        // 把逾期未还进行扩展到30期
                        //找打最大期数的金额(父订单期数+子订单期数的上一个金额)
                        BigDecimal parentNotReturnAmt2 = getParentNotReturnAmt(o, newMaxTerm);
                        for(int i=newMaxTerm;i<=30;i++){
                            setTermNotRepayVal(o,  i+1, parentNotReturnAmt2);
                            setOverdueVal(o, i+1, 1, BigDecimal.ZERO);
                        }

                        // 设置逾期情况,判断续租待买断订单的逾期时间
                        for (int i = 1, day = renewDay + 1; i <= day && maxTerm + i <= newMaxTerm; i++) {
                            setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
                        }
                    }
                }).collect(Collectors.toList());
        riskGeneralOrderOverdueService.saveBatch(generalList);
//        List<DataCheckAfterRentOverviewDO> checkList = fetchDataCheckList(records);
//        dataCheckAfterRentOverviewService.saveBatch(checkList);
    }

    private List<DataCheckAfterRentOverviewDO> fetchDataCheckList(List<Record> records) {
        List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);

        List<DataCheckAfterRentOverviewDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            DataCheckAfterRentOverviewDO riskOrderOverdueDO = null;
            BigDecimal sum = ObjectUtils.getSum(value, RepaymentPlanDO::getSurplusAmt);
            BigDecimal sumPay = ObjectUtils.getSum(value, RepaymentPlanDO::getAlreadyPay);
            for (RepaymentPlanDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new DataCheckAfterRentOverviewDO();

                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getSurplusBondAmt());
                    // 未使用售前优惠
                    riskOrderOverdueDO.setUnusedDiscount(repaymentPlanDO.getBeforeDiscount());
                    // 总售前优惠金额
                    riskOrderOverdueDO.setTotalDiscount(repaymentPlanDO.getTotalDiscount());
                    riskOrderOverdueDO.setOverdueDay(repaymentPlanDO.getCurrentOverdueDays());
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                    riskOrderOverdueDO.setIsSettle(repaymentPlanDO.getIsSettle());
                    riskOrderOverdueDO.setStatus(repaymentPlanDO.getOrderStatus());
                    riskOrderOverdueDO.setActualSupplyPrice(repaymentPlanDO.getActualSupplyPrice());
                    riskOrderOverdueDO.setSurplusAmt(sum);
                    riskOrderOverdueDO.setAlreadyPay(sumPay);
                }
                riskOrderOverdueDO.setCountDay(repaymentPlanDO.getRentStartDate());
                riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                riskOrderOverdueDO.setOrderId(repaymentPlanDO.getOrderId());
            }
        }
        return resList;
    }

    private List<RiskOrderOverdueDO> fetchList(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);

        List<RiskOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            RiskOrderOverdueDO riskOrderOverdueDO = null;
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
            for (RepaymentPlanDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    // 未使用售前优惠
                    riskOrderOverdueDO.setBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    // 总售前优惠金额
                    riskOrderOverdueDO.setTotalDiscount(repaymentPlanDO.getTotalDiscount());
                    riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRate());
                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                }

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                // 对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskOrderOverdueDO.setCountDay(rentStartDay);
                riskOrderOverdueDO.setMaxDay(maxTerm);
                riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);

                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();

                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    LocalDate maxRepayDate =
                            LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));

                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);

                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private RepaymentPlanDO getPlanDo(List<RepaymentPlanDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanDO>> term2PlanDo) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (value.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        return planDO;
    }

    private RepaymentPlanNewDO getPlanDoNew(List<RepaymentPlanNewDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanNewDO>> term2PlanDo) {
        List<RepaymentPlanNewDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
        RepaymentPlanNewDO planDO = repaymentPlanDOList.get(0);
        if (value.size() > 1) {
            for (RepaymentPlanNewDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        return planDO;
    }


    private List<RiskGeneralOrderOverdueDO> fetchGeneralList(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);

        List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        // 把租后待买断的订单先当作续租处理,如果真买断了,就走原来的逻辑,如果一直未买断,也可以按照续租的方式计算逾期率
        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            RiskGeneralOrderOverdueDO riskOrderOverdueDO = null;
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));

            for (RepaymentPlanDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskGeneralOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setScore(repaymentPlanDO.getScore());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setPDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setPBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRateStr());
                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                    riskOrderOverdueDO.setPMaxTerm(maxTerm);
                    riskOrderOverdueDO.setIsRent(repaymentPlanDO.getRentType());
                    riskOrderOverdueDO.setZmfLevel(repaymentPlanDO.getZmfLevel());
                    riskOrderOverdueDO.setMaxTerm(maxTerm);
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setRenewDay(repaymentPlanDO.getRenewDay());
                    riskOrderOverdueDO.setRenewStatus(repaymentPlanDO.getRenewStatus());
                    riskOrderOverdueDO.setRenewTerm(repaymentPlanDO.getRenewTerm());
                    riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                    riskOrderOverdueDO.setDiscountTotal(repaymentPlanDO.getTotalDiscount());
                }

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                // 对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskOrderOverdueDO.setCountDay(rentStartDay);

                //***重要**** 设置每期是否逾期和逾期滞纳金
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                //***重要**** 设置每期的续租总租金
                setRenewAmt(riskOrderOverdueDO, term, repaymentPlanDO.getRenewTotalRent());
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
                // 如果已经逾期
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));

                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                    // 封账日期
                    // 封账日期  = repayDate 应还日期+下个月2号+宽限日期
                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    resMap.put("endTime", localDate);
                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // ***重要****
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private List<RiskGeneralOrderOverdueDO> fetchGeneralList_new(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanNewDO> planList = getRepaymentPlanDOS_new(records);


        List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanNewDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanNewDO::getOrderId));

        // 把租后待买断的订单先当作续租处理,如果真买断了,就走原来的逻辑,如果一直未买断,也可以按照续租的方式计算逾期率
        for (Map.Entry<Long, List<RepaymentPlanNewDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanNewDO> value = entry.getValue();
            //使用之后的优惠券金额
            BigDecimal discountAmt = ObjectUtils.getSum(value, RepaymentPlanNewDO::getDiscountAmt);
            RiskGeneralOrderOverdueDO riskOrderOverdueDO = null;
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanNewDO::getTerm)).get().getTerm();
            Map<Integer, List<RepaymentPlanNewDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanNewDO::getTerm));

            /*类似于行专列，一行一行的分期数据,转换成一列一列*/
            for (RepaymentPlanNewDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskGeneralOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setScore(repaymentPlanDO.getScore());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setPDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setPBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRateStr());
                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                    riskOrderOverdueDO.setPMaxTerm(maxTerm);
                    riskOrderOverdueDO.setIsRent(repaymentPlanDO.getRentType());
                    riskOrderOverdueDO.setZmfLevel(repaymentPlanDO.getZmfLevel());
                    riskOrderOverdueDO.setMaxTerm(maxTerm);
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setRenewDay(repaymentPlanDO.getRenewDay());
                    riskOrderOverdueDO.setRenewStatus(repaymentPlanDO.getRenewStatus());
                    riskOrderOverdueDO.setRenewTerm(repaymentPlanDO.getRenewTerm());
                    riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                    riskOrderOverdueDO.setDiscountTotal(repaymentPlanDO.getTotalDiscount());
                    riskOrderOverdueDO.setSurplusBondAmt(repaymentPlanDO.getSurplusBondAmt());
                    riskOrderOverdueDO.setBondRestFundAmount(repaymentPlanDO.getBondRestFundAmount());
                    riskOrderOverdueDO.setDiffPricingDiscountAmt(repaymentPlanDO.getDiffPricingDiscountAmt());
                    riskOrderOverdueDO.setCouponDiscountAmt(repaymentPlanDO.getCouponDiscountAmt());
                    riskOrderOverdueDO.setRenewTotalRent(repaymentPlanDO.getRenewTotalRent());
                    riskOrderOverdueDO.setDiscountPricingMode(repaymentPlanDO.getDiscountPricingMode());
                    riskOrderOverdueDO.setDiscountAmt(discountAmt);
                }

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                // 对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatusNew(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskOrderOverdueDO.setCountDay(rentStartDay);
                //***重要**** 设置每期是否逾期和逾期滞纳金
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                //***重要**** 设置每期的续租总租金
                setRenewAmt(riskOrderOverdueDO, term, repaymentPlanDO.getRenewTotalRent());
                RepaymentPlanNewDO maxTerPlanDO = getPlanDoNew(value, maxTerm, term2PlanDo);
                // 如果已经逾期
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));

                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                    // 封账日期
                    // 封账日期  = repayDate 应还日期+下个月2号+宽限日期
                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    // 封账时间改成下个月3号,小于4号就没到封账
//                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(4));
                    resMap.put("endTime", localDate);
                    // 获取逾期未还金额
                    fetchCapitalNew(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // ***重要****
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private List<RiskGeneralOrderOverdueDO> fetchGeneralList_tidb(List<GeneralRecord> records, Integer overdueDay) {
        List<RepaymentPlanNewDO> planList = getRepaymentPlanDOS_tidb(records);
        List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanNewDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanNewDO::getOrderId));

        // 把租后待买断的订单先当作续租处理,如果真买断了,就走原来的逻辑,如果一直未买断,也可以按照续租的方式计算逾期率
        for (Map.Entry<Long, List<RepaymentPlanNewDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanNewDO> value = entry.getValue();
            //使用之后的优惠券金额
            BigDecimal discountAmt = ObjectUtils.getSum(value, RepaymentPlanNewDO::getDiscountAmt);
            RiskGeneralOrderOverdueDO riskOrderOverdueDO = null;
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanNewDO::getTerm)).get().getTerm();
            Map<Integer, List<RepaymentPlanNewDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanNewDO::getTerm));

            /*类似于行专列，一行一行的分期数据,转换成一列一列*/
            for (RepaymentPlanNewDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskGeneralOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setScore(repaymentPlanDO.getScore());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setPDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setPBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRateStr());
                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
                    riskOrderOverdueDO.setPMaxTerm(maxTerm);
                    riskOrderOverdueDO.setIsRent(repaymentPlanDO.getRentType());
                    riskOrderOverdueDO.setZmfLevel(repaymentPlanDO.getZmfLevel());
                    riskOrderOverdueDO.setMaxTerm(maxTerm);
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setRenewDay(repaymentPlanDO.getRenewDay());
                    riskOrderOverdueDO.setRenewStatus(repaymentPlanDO.getRenewStatus());
                    riskOrderOverdueDO.setRenewTerm(repaymentPlanDO.getRenewTerm());
                    riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                    riskOrderOverdueDO.setDiscountTotal(repaymentPlanDO.getTotalDiscount());
                    riskOrderOverdueDO.setSurplusBondAmt(repaymentPlanDO.getSurplusBondAmt());
                    riskOrderOverdueDO.setBondRestFundAmount(repaymentPlanDO.getBondRestFundAmount());
                    riskOrderOverdueDO.setDiffPricingDiscountAmt(repaymentPlanDO.getDiffPricingDiscountAmt());
                    riskOrderOverdueDO.setCouponDiscountAmt(repaymentPlanDO.getCouponDiscountAmt());
                    riskOrderOverdueDO.setRenewTotalRent(repaymentPlanDO.getRenewTotalRent());
                    riskOrderOverdueDO.setDiscountPricingMode(repaymentPlanDO.getDiscountPricingMode());
                    riskOrderOverdueDO.setDiscountAmt(discountAmt);
                    riskOrderOverdueDO.setBuyOutCapital(repaymentPlanDO.getBuyOutCapital());
                    riskOrderOverdueDO.setBuyOutRealRepayCapital(repaymentPlanDO.getBuyOutRealRepayCapital());
                    riskOrderOverdueDO.setAuditTypeName(repaymentPlanDO.getAuditTypeName());
                    //免押类型
                    riskOrderOverdueDO.setDepositFreeType(repaymentPlanDO.getDepositFreeType());
                    //流量类型
                    riskOrderOverdueDO.setTrafficType(repaymentPlanDO.getTrafficType());
                    //金融方案
                    riskOrderOverdueDO.setFinancialSolutions(repaymentPlanDO.getFinancialSolutions());
                    //应用
                    riskOrderOverdueDO.setApplicationName(repaymentPlanDO.getApplicationName());
                    //新旧程度
                    riskOrderOverdueDO.setEquipmentState(repaymentPlanDO.getEquipmentState());
                    //是否监管
                    riskOrderOverdueDO.setSupervisedMachine(repaymentPlanDO.getSupervisedMachine());
                    //机型
                    riskOrderOverdueDO.setMachineType(repaymentPlanDO.getMachineType());
                }

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                // 对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatusNew(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskOrderOverdueDO.setCountDay(rentStartDay);
                //***重要**** 设置每期是否逾期和逾期滞纳金
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                //***重要**** 设置每期的续租总租金
                setRenewAmt(riskOrderOverdueDO, term, repaymentPlanDO.getRenewTotalRent());
                RepaymentPlanNewDO maxTerPlanDO = getPlanDoNew(value, maxTerm, term2PlanDo);
                // 如果已经逾期
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));

                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                    // 封账日期
                    // 封账日期  = repayDate 应还日期+下个月2号+宽限日期
                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    // 封账时间改成下个月3号,小于4号就没到封账
//                    LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(4));
                    resMap.put("endTime", localDate);
                    // 获取逾期未还金额
                    fetchCapitalNew(term2PlanDo, resMap);
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // 设置对应期数未还金额
                    // ***重要****
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private List<RepaymentPlanDO> getRepaymentPlanDOS(List<Record> records) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Integer term = Integer.valueOf(domain.getString("term"));
                Integer isDeleted = Integer.valueOf(domain.getString("deleted"));
                Integer miniType = Integer.valueOf(domain.getString("mini_type"));
                Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
                Integer renewStatus = Integer.valueOf(domain.getString("renew_status"));
                Integer renewTerm = Integer.valueOf(domain.getString("renew_term"));
                Integer renewDay = Integer.valueOf(domain.getString("renew_day"));
                // 根据冻结金额计算出的评分级别 750+，700-750,650-700,600-650分,600分以下,非免押用户
                String zmfLevel = domain.getString("zmf_level");
                // '租物', '非租物'
                String isRent = domain.getString("is_rent");
                // 实际还款日期
                Integer realRepayTimeStatus = Integer.valueOf(domain.getString("real_repay_time_status"));
                String platform = domain.getString("platform");
                String no = domain.getString("order_no");
                // overdue 逾期1=未逾期 5=已逾期
                Integer overdue = Integer.valueOf(domain.getString("overdue"));
                // 金融方案类型 0:正常；1:差异化
                Integer financeType = Integer.valueOf(domain.getString("finance_type"));
                // 自动风控意见
                String riskLevel = domain.getString("risk_level");
                if (riskLevel.contains("风控等级")) {
                    // 将风控等级4.0和风控等级4之类的进行合并
                    String[] split = riskLevel.split("\\.");
                    riskLevel = split[0];
                }
                String riskStrategy = domain.getString("risk_strategy");
                String auditType = domain.getString("audit_type");
                String scene = domain.getString("scene");
                String quotientName = domain.getString("quotient_name");
                // 获取分数
                String hitValue = domain.getString("hit_value");
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100018");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                Integer forcedConversion = Integer.valueOf(domain.getString("forced_conversion"));
                // 是否逾期,只有 0,1
                String isOverdue = domain.getString("is_overdue");
                // 应还款本金
                BigDecimal capital = stringToDecimal(domain.getString("capital"));
                // (case
                //               when min(repay_status) = 5 then avg(rorp.capital)
                //               when min(repay_status) = 1 then (avg(capital) - avg(real_repay_capital) - avg(reduce_amt)) end)
                //                                                                                                          real_capital
                //应还款本金-实际还款本金-减免金额
                BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
                // 还款状态1=未还款 2=还款中 5=已还款  10-已展期
                // sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0))
                // 实际还款金额
                BigDecimal surplusAmt = stringToDecimal(domain.getString("surplus_amt"));
                // 实际供货价（手机销量统计使用）
                BigDecimal actualSupplyPrice = stringToDecimal(domain.getString("actual_supply_price"));
                //剩余可用履约保证金
                BigDecimal surplusBondAmt = stringToDecimal(domain.getString("surplus_bond_amt"));
                // 用户实际支付买断金 = 实际支付买断金 -折扣返还金额
                BigDecimal actBuyoutAmt = stringToDecimal(domain.getString("act_buyout_amt"));
                // if(c.rate_config_type != 10, max(h.manage_buyout_discount) + max(h.offline_buyout_discount), 0) buyout_discount
                //manage_buyout_discount买断优惠金额总金额+offline_buyout_discount买断金线下优惠券金额总额
                BigDecimal buyoutDiscount = stringToDecimal(domain.getString("buyout_discount"));
                //  sum(if(repay_status = 5,real_repay_capital,0))      already_pay,
                //  实际还款本金 总金额
                BigDecimal alreadyPay = stringToDecimal(domain.getString("already_pay"));
                // 订单金融方案模式: 1-租完买断/续租/归还; 2-租完买断/续租/归还(不生成子订单) 3-租完即送
                Integer renewType = Integer.valueOf(domain.getString("renew_type"));
                Integer orderStatus = Integer.valueOf(domain.getString("order_status"));
                // if(min(ro.settle_date) is not null, 1, 0)  是否结清
                Integer isSettle = Integer.valueOf(domain.getString("is_settle"));
                // min(rorp.overdue_days) 逾期天数
                Integer termOverdueDays = Integer.valueOf(domain.getString("term_overdue_days"));
                // current_overdue_days:当前逾期天数
                Integer currentOverdueDays = Integer.valueOf(domain.getString("current_overdue_days"));
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setSurplusAmt(surplusAmt);
                repaymentPlanDO.setActualSupplyPrice(actualSupplyPrice);
                repaymentPlanDO.setSurplusBondAmt(surplusBondAmt);
                repaymentPlanDO.setActBuyoutAmt(actBuyoutAmt);
                repaymentPlanDO.setBuyoutDiscount(buyoutDiscount);
                repaymentPlanDO.setOrderStatus(orderStatus);
                repaymentPlanDO.setAlreadyPay(alreadyPay);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setZmfLevel(zmfLevel);
                repaymentPlanDO.setRentType(isRent);
                repaymentPlanDO.setTermOverdueDays(termOverdueDays);
                repaymentPlanDO.setCurrentOverdueDays(currentOverdueDays);
                repaymentPlanDO.setRenewType(renewType);
                // 续租总租金
                BigDecimal renewTotalRent = stringToDecimal(domain.getString("renew_total_rent"));
                // max(repayment_term)  还款期数
                Integer maxTerm = Integer.valueOf(domain.getString("max_term"));

                // count_day 订单起租时间
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("count_day"),
                        dateFormatter), LocalTime.MIN);
                // repay_date 应还日期
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
                        dateFormatter), LocalTime.MIN);

                // real_repay_time 实际还款日期
                LocalDateTime realRepayTime = null;
                if (!domain.getString("real_repay_time").equals("\\N")) {
                    realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);
                }

                // rent_order_flow.ext 折扣返还金额 discount_return_amt
                BigDecimal discountReturnAmt = stringToDecimal(domain.getString("discount_return_amt"));
                // if(c.rate_config_type not in( 10,30), min(c.actual_financing_amt), avg(rorp.capital) * 12)
                // actual_financing_amt 实际融资金额
                // capital 应还款本金
                BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));
                // buyout_amt 买断金
                BigDecimal buyoutAmt = stringToDecimal(domain.getString("buyout_amt"));
                //overdue_fine 逾期滞纳金
                BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
                // bond_amt 保证金
                BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
                //  round(nvl(min(c.bond_amt) / ((if(c.rate_config_type != 10, min(c.actual_financing_amt), avg(rorp
                //              .capital) * 12) + if(c.rate_config_type != 10, min(c.buyout_amt), 0)) - nvl(min(x.total_discount), 0)),
                //                    0), 2)
                double bondRate = Double.parseDouble(domain.getString("bond_rate"));
                BigDecimal mul = CalculateUtil.mul(new BigDecimal(bondRate).setScale(2, BigDecimal.ROUND_HALF_UP), 100);

                //***重要*** 获取判断区间
                String bondRateStr = fetchInterval(mul.doubleValue());

                // before_discount 使用中的核销总金额
                BigDecimal beforeDiscount = stringToDecimal(domain.getString("before_discount"));
                //核销总金额 total_discount
                BigDecimal totalDiscount = stringToDecimal(domain.getString("total_discount"));
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setMaxTerm(maxTerm);
                repaymentPlanDO.setScore(score);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setRenewDay(renewDay);
                repaymentPlanDO.setBondRateStr(bondRateStr);
                repaymentPlanDO.setRenewTotalRent(renewTotalRent);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBuyoutAmt(buyoutAmt);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRenewStatus(renewStatus);
                repaymentPlanDO.setRenewTerm(renewTerm);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setTotalDiscount(totalDiscount);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        return planList;
    }

    private List<RepaymentPlanNewDO> getRepaymentPlanDOS_new(List<Record> records) {
        List<RepaymentPlanNewDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanNewDO repaymentPlanDO = new RepaymentPlanNewDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Integer term = Integer.valueOf(domain.getString("term"));
                Integer isDeleted = Integer.valueOf(domain.getString("deleted"));
                Integer miniType = Integer.valueOf(domain.getString("mini_type"));
                Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
                Integer renewStatus = Integer.valueOf(domain.getString("renew_status"));
                Integer renewTerm = Integer.valueOf(domain.getString("renew_term"));
                Integer renewDay = Integer.valueOf(domain.getString("renew_day"));
                // 根据冻结金额计算出的评分级别 750+，700-750,650-700,600-650分,600分以下,非免押用户
                String zmfLevel = domain.getString("zmf_level");
                // '租物', '非租物'
                String isRent = domain.getString("is_rent");
                // 实际还款日期
                Integer realRepayTimeStatus = Integer.valueOf(domain.getString("real_repay_time_status"));
                String platform = domain.getString("platform");
                String no = domain.getString("order_no");
                // overdue 逾期1=未逾期 5=已逾期
                Integer overdue = Integer.valueOf(domain.getString("overdue"));
                // 金融方案类型 0:正常；1:差异化
                Integer financeType = Integer.valueOf(domain.getString("finance_type"));
                // 自动风控意见
                String riskLevel = domain.getString("risk_level");
                if (riskLevel.contains("风控等级")) {
                    // 将风控等级4.0和风控等级4之类的进行合并
                    String[] split = riskLevel.split("\\.");
                    riskLevel = split[0];
                }
                String riskStrategy = domain.getString("risk_strategy");
                String auditType = domain.getString("audit_type");
                String scene = domain.getString("scene");
                String quotientName = domain.getString("quotient_name");
                // 获取分数
                String hitValue = domain.getString("hit_value");
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100018");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                Integer forcedConversion = Integer.valueOf(domain.getString("forced_conversion"));
                // 是否逾期,只有 0,1
                String isOverdue = domain.getString("is_overdue");
                // 应还款本金
                BigDecimal capital = stringToDecimal(domain.getString("capital"));
                // (case
                //               when min(repay_status) = 5 then avg(rorp.capital)
                //               when min(repay_status) = 1 then (avg(capital) - avg(real_repay_capital) - avg(reduce_amt)) end)
                //                                                                                                          real_capital
                //应还款本金-实际还款本金-减免金额
                BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
                // 还款状态1=未还款 2=还款中 5=已还款  10-已展期
                // sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0))
                // 实际还款金额
                BigDecimal surplusAmt = stringToDecimal(domain.getString("surplus_amt"));
                // 实际供货价（手机销量统计使用）
                BigDecimal actualSupplyPrice = stringToDecimal(domain.getString("actual_supply_price"));
                //剩余可用履约保证金
                BigDecimal surplusBondAmt = stringToDecimal(domain.getString("surplus_bond_amt"));
                // 剩余可用预授权实付押金
                BigDecimal bondRestFundAmount=stringToDecimal(domain.getString("bond_rest_fund_amount"));
                // 差异化优惠金额
                BigDecimal  diffPricingDiscountAmt=stringToDecimal(domain.getString("diff_pricing_discount_amt"));
                // 优惠券金额
                BigDecimal  couponDiscountAmt=stringToDecimal(domain.getString("coupon_discount_amt"));
                // 用户实际支付买断金 = 实际支付买断金 -折扣返还金额
                BigDecimal actBuyoutAmt = stringToDecimal(domain.getString("act_buyout_amt"));
                // if(c.rate_config_type != 10, max(h.manage_buyout_discount) + max(h.offline_buyout_discount), 0) buyout_discount
                //manage_buyout_discount买断优惠金额总金额+offline_buyout_discount买断金线下优惠券金额总额
                BigDecimal buyoutDiscount = stringToDecimal(domain.getString("buyout_discount"));
                //  sum(if(repay_status = 5,real_repay_capital,0))      already_pay,
                //  实际还款本金 总金额
                BigDecimal alreadyPay = stringToDecimal(domain.getString("already_pay"));
                // 订单金融方案模式: 1-租完买断/续租/归还; 2-租完买断/续租/归还(不生成子订单) 3-租完即送
                Integer renewType = Integer.valueOf(domain.getString("renew_type"));
                Integer orderStatus = Integer.valueOf(domain.getString("order_status"));
                // if(min(ro.settle_date) is not null, 1, 0)  是否结清
                Integer isSettle = Integer.valueOf(domain.getString("is_settle"));
                // min(rorp.overdue_days) 逾期天数
                Integer termOverdueDays = Integer.valueOf(domain.getString("term_overdue_days"));
                // current_overdue_days:当前逾期天数
                Integer currentOverdueDays = Integer.valueOf(domain.getString("current_overdue_days"));
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setSurplusAmt(surplusAmt);
                repaymentPlanDO.setActualSupplyPrice(actualSupplyPrice);
                repaymentPlanDO.setSurplusBondAmt(surplusBondAmt);
                repaymentPlanDO.setBondRestFundAmount(bondRestFundAmount);
                repaymentPlanDO.setDiffPricingDiscountAmt(diffPricingDiscountAmt);
                repaymentPlanDO.setCouponDiscountAmt(couponDiscountAmt);
                repaymentPlanDO.setActBuyoutAmt(actBuyoutAmt);
                repaymentPlanDO.setBuyoutDiscount(buyoutDiscount);
                repaymentPlanDO.setOrderStatus(orderStatus);
                repaymentPlanDO.setAlreadyPay(alreadyPay);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setZmfLevel(zmfLevel);
                repaymentPlanDO.setRentType(isRent);
                repaymentPlanDO.setTermOverdueDays(termOverdueDays);
                repaymentPlanDO.setCurrentOverdueDays(currentOverdueDays);
                repaymentPlanDO.setRenewType(renewType);
                // 续租总租金
                BigDecimal renewTotalRent = stringToDecimal(domain.getString("renew_total_rent"));

                // 优惠定价模型 0:普通 1:先优惠后差异化
                Integer discountPricingMode = Integer.valueOf(domain.getString("discount_pricing_mode"));
                repaymentPlanDO.setDiscountPricingMode(discountPricingMode);
                // max(repayment_term)  还款期数
                Integer maxTerm = Integer.valueOf(domain.getString("max_term"));

                // count_day 订单起租时间
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("count_day"),
                        dateFormatter), LocalTime.MIN);
                // repay_date 应还日期
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
                        dateFormatter), LocalTime.MIN);

                // real_repay_time 实际还款日期
                LocalDateTime realRepayTime = null;
                if (!domain.getString("real_repay_time").equals("\\N")) {
                    realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);
                }

                // rent_order_flow.ext 折扣返还金额 discount_return_amt
                BigDecimal discountReturnAmt = stringToDecimal(domain.getString("discount_return_amt"));
                // if(c.rate_config_type not in( 10,30), min(c.actual_financing_amt), avg(rorp.capital) * 12)

                // actual_financing_amt 实际融资金额
                // capital 应还款本金
                BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));

                // 使用之后的优惠金额
                BigDecimal discountAmt = stringToDecimal(domain.getString("discount_amt"));
                // buyout_amt 买断金
                BigDecimal buyoutAmt = stringToDecimal(domain.getString("buyout_amt"));
                //overdue_fine 逾期滞纳金
                BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
                // bond_amt 保证金
                BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
                //  round(nvl(min(c.bond_amt) / ((if(c.rate_config_type != 10, min(c.actual_financing_amt), avg(rorp
                //              .capital) * 12) + if(c.rate_config_type != 10, min(c.buyout_amt), 0)) - nvl(min(x.total_discount), 0)),
                //                    0), 2)
                double bondRate = Double.parseDouble(domain.getString("bond_rate"));
                BigDecimal mul = CalculateUtil.mul(new BigDecimal(bondRate).setScale(2, BigDecimal.ROUND_HALF_UP), 100);

                //***重要*** 获取判断区间
                String bondRateStr = fetchInterval(mul.doubleValue());

                // before_discount 使用中的核销总金额
                BigDecimal beforeDiscount = stringToDecimal(domain.getString("before_discount"));
                //核销总金额 total_discount = diff_pricing_discount_amt(优惠券金额)+diff_pricing_discount_amt(差异化优惠金额)
                BigDecimal totalDiscount = stringToDecimal(domain.getString("total_discount"));
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setMaxTerm(maxTerm);
                repaymentPlanDO.setScore(score);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setRenewDay(renewDay);
                repaymentPlanDO.setBondRateStr(bondRateStr);
                repaymentPlanDO.setRenewTotalRent(renewTotalRent);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBuyoutAmt(buyoutAmt);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRenewStatus(renewStatus);
                repaymentPlanDO.setRenewTerm(renewTerm);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setTotalDiscount(totalDiscount);
                repaymentPlanDO.setDiscountAmt(discountAmt);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        return planList;
    }


    private List<RepaymentPlanNewDO> getRepaymentPlanDOS_tidb(List<GeneralRecord> records) {
        List<RepaymentPlanNewDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanNewDO repaymentPlanDO = new RepaymentPlanNewDO();
                Long orderId = Long.valueOf(domain.getOrderid());
                Integer term = Integer.valueOf(domain.getTerm());
                Integer isDeleted = Integer.valueOf(domain.getDeleted());
                Integer miniType = Integer.valueOf(domain.getMiniType());
                Integer repayStatus = Integer.valueOf(domain.getRepayStatus());
                Integer renewStatus = Integer.valueOf(domain.getRenewStatus());
                Integer renewTerm = Integer.valueOf(domain.getRenewTerm());
                Integer renewDay = Integer.valueOf(domain.getRenewDay());
                // 根据冻结金额计算出的评分级别 750+，700-750,650-700,600-650分,600分以下,非免押用户
                String zmfLevel = domain.getZmfLevel();
                // '租物', '非租物'
                String isRent = domain.getIsRent();
                // 实际还款日期
                Integer realRepayTimeStatus = Integer.valueOf(domain.getRealRepayTimeStatus());
                String platform = domain.getPlatform();
                String no = domain.getOrderNo();
                // overdue 逾期1=未逾期 5=已逾期
                Integer overdue = Integer.valueOf(domain.getOverdue());
                // 金融方案类型 0:正常；1:差异化
                Integer financeType = Integer.valueOf(domain.getFinanceType());
                // 自动风控意见
                String riskLevel = domain.getRiskLevel();
                if (riskLevel.contains("风控等级")) {
                    // 将风控等级4.0和风控等级4之类的进行合并
                    String[] split = riskLevel.split("\\.");
                    riskLevel = split[0];
                }
                String riskStrategy = domain.getRiskStrategy();
                String auditType = domain.getAuditType();
                String auditTypeName = domain.getAuditTypeName();
                String scene = domain.getScene();
                String quotientName = domain.getQuotientName();
                // 获取分数
                String hitValue = domain.getHitValue();
                Integer score = -1;
                if (StringUtils.isNotBlank(hitValue)) {
                    if (hitValue.startsWith("{")) {
                        JSONObject jsonObject = JSONUtil.parseObj(hitValue);
                        score = jsonObject.get("score", Integer.class);
                    } else {
                        List<Map> maps = JSONUtil.toList(hitValue, Map.class);
                        Map map = maps.get(0);
                        Object str = map.get("Y100018");
                        JSONObject jsonObject = JSONUtil.parseObj(str);
                        score = jsonObject.get("score", Integer.class);
                    }
                }
                Integer forcedConversion = Integer.valueOf(domain.getForcedConversion());
                // 是否逾期,只有 0,1
                String isOverdue = domain.getIsOverdue();
                // 应还款本金
                BigDecimal capital = stringToDecimal(domain.getCapital());
                // (case
                //               when min(repay_status) = 5 then avg(rorp.capital)
                //               when min(repay_status) = 1 then (avg(capital) - avg(real_repay_capital) - avg(reduce_amt)) end)
                //                                                                                                          real_capital
                //应还款本金-实际还款本金-减免金额
                BigDecimal realCapital = stringToDecimal(domain.getRealCapital());
                // 还款状态1=未还款 2=还款中 5=已还款  10-已展期
                // sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0))
                // 实际还款金额
                BigDecimal surplusAmt = stringToDecimal(domain.getSurplusAmt());
                // 实际供货价（手机销量统计使用）
                BigDecimal actualSupplyPrice = stringToDecimal(domain.getActualSupplyPrice());
                //剩余可用履约保证金
                BigDecimal surplusBondAmt = stringToDecimal(domain.getSurplusBondAmt());
                // 剩余可用预授权实付押金
                BigDecimal bondRestFundAmount=stringToDecimal(domain.getBondRestFundAmount());
                // 差异化优惠金额
                BigDecimal  diffPricingDiscountAmt=stringToDecimal(domain.getDiffPricingDiscountAmt());
                // 优惠券金额
                BigDecimal  couponDiscountAmt=stringToDecimal(domain.getCouponDiscountAmt());
                // 用户实际支付买断金 = 实际支付买断金 -折扣返还金额
                BigDecimal actBuyoutAmt = stringToDecimal(domain.getActBuyoutAmt());
                // if(c.rate_config_type != 10, max(h.manage_buyout_discount) + max(h.offline_buyout_discount), 0) buyout_discount
                //manage_buyout_discount买断优惠金额总金额+offline_buyout_discount买断金线下优惠券金额总额
                BigDecimal buyoutDiscount = stringToDecimal(domain.getBuyoutDiscount());
                //  sum(if(repay_status = 5,real_repay_capital,0))      already_pay,
                //  实际还款本金 总金额
                BigDecimal alreadyPay = stringToDecimal(domain.getAlreadyPay());
                // 订单金融方案模式: 1-租完买断/续租/归还; 2-租完买断/续租/归还(不生成子订单) 3-租完即送
                Integer renewType = Integer.valueOf(domain.getRenewType());
                Integer orderStatus = Integer.valueOf(domain.getOrderStatus());
                // if(min(ro.settle_date) is not null, 1, 0)  是否结清
                Integer isSettle = Integer.valueOf(domain.getIsSettle());
                // min(rorp.overdue_days) 逾期天数
                Integer termOverdueDays = Integer.valueOf(domain.getTermOverdueDays());
                // current_overdue_days:当前逾期天数
                Integer currentOverdueDays = Integer.valueOf(domain.getCurrentOverdueDays());
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setSurplusAmt(surplusAmt);
                repaymentPlanDO.setActualSupplyPrice(actualSupplyPrice);
                repaymentPlanDO.setSurplusBondAmt(surplusBondAmt);
                repaymentPlanDO.setBondRestFundAmount(bondRestFundAmount);
                repaymentPlanDO.setDiffPricingDiscountAmt(diffPricingDiscountAmt);
                repaymentPlanDO.setCouponDiscountAmt(couponDiscountAmt);
                repaymentPlanDO.setActBuyoutAmt(actBuyoutAmt);
                repaymentPlanDO.setBuyoutDiscount(buyoutDiscount);
                repaymentPlanDO.setOrderStatus(orderStatus);
                repaymentPlanDO.setAlreadyPay(alreadyPay);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setZmfLevel(zmfLevel);
                repaymentPlanDO.setRentType(isRent);
                repaymentPlanDO.setTermOverdueDays(termOverdueDays);
                repaymentPlanDO.setCurrentOverdueDays(currentOverdueDays);
                repaymentPlanDO.setRenewType(renewType);
                // 续租总租金
                BigDecimal renewTotalRent = stringToDecimal(domain.getRenewTotalRent());

                // 优惠定价模型 0:普通 1:先优惠后差异化
                Integer discountPricingMode = Integer.valueOf(domain.getDiscountPricingMode());
                repaymentPlanDO.setDiscountPricingMode(discountPricingMode);
                // max(repayment_term)  还款期数
                Integer maxTerm = Integer.valueOf(domain.getMaxTerm());

                // count_day 订单起租时间
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getCountDay(),
                        dateFormatter), LocalTime.MIN);
                // repay_date 应还日期
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getRepayDate(),
                        dateFormatter), LocalTime.MIN);

                // real_repay_time 实际还款日期
                LocalDateTime realRepayTime = null;
                if (null != domain.getRealRepayTime() &&!domain.getRealRepayTime().equals("\\N")) {
                    realRepayTime = LocalDateTime.parse(domain.getRealRepayTime(), YYYY_MM_DD_HH_MM_SS);
                }

                // rent_order_flow.ext 折扣返还金额 discount_return_amt
                BigDecimal discountReturnAmt = stringToDecimal(domain.getDiscountReturnAmt());
                // if(c.rate_config_type not in( 10,30), min(c.actual_financing_amt), avg(rorp.capital) * 12)

                // actual_financing_amt 实际融资金额
                // capital 应还款本金
                BigDecimal rentTotal = stringToDecimal(domain.getRentTotal());

                //应还买断金
                BigDecimal buyOutCapital = stringToDecimal(domain.getBuyOutCapital());

                //实还买断金
                BigDecimal buyOutRealRepayCapital = stringToDecimal(domain.getBuyOutRealRepayCapital());

                // 使用之后的优惠金额
                BigDecimal discountAmt = stringToDecimal(domain.getDiscountAmt());
                // buyout_amt 买断金
                BigDecimal buyoutAmt = stringToDecimal(domain.getBuyoutAmt());
                //overdue_fine 逾期滞纳金
                BigDecimal overdueFine = stringToDecimal(domain.getOverdueFine());
                // bond_amt 保证金
                BigDecimal bondAmt = stringToDecimal(domain.getBondAmt());
                //  round(nvl(min(c.bond_amt) / ((if(c.rate_config_type != 10, min(c.actual_financing_amt), avg(rorp
                //              .capital) * 12) + if(c.rate_config_type != 10, min(c.buyout_amt), 0)) - nvl(min(x.total_discount), 0)),
                //                    0), 2)
                double bondRate = Double.parseDouble(domain.getBondRate());
                BigDecimal mul = CalculateUtil.mul(new BigDecimal(bondRate).setScale(2, BigDecimal.ROUND_HALF_UP), 100);

                //***重要*** 获取判断区间
                String bondRateStr = fetchInterval(mul.doubleValue());

                //审核人名字
                repaymentPlanDO.setAuditTypeName(auditTypeName);

                //免押类型
                repaymentPlanDO.setDepositFreeType(domain.getDepositFreeType());
                //流量类型
                repaymentPlanDO.setTrafficType(domain.getTrafficType());
                //金融方案
                repaymentPlanDO.setFinancialSolutions(domain.getFinancialSolutions());
                //应用
                repaymentPlanDO.setApplicationName(domain.getApplicationName());
                //新旧程度
                repaymentPlanDO.setEquipmentState(domain.getEquipmentState());
                //是否监管
                repaymentPlanDO.setSupervisedMachine(domain.getSupervisedMachine());
                //机型
                repaymentPlanDO.setMachineType(domain.getMachineType());

                // before_discount 使用中的核销总金额
                BigDecimal beforeDiscount = stringToDecimal(domain.getBeforeDiscount());
                //核销总金额 total_discount = diff_pricing_discount_amt(优惠券金额)+diff_pricing_discount_amt(差异化优惠金额)
                BigDecimal totalDiscount = stringToDecimal(domain.getTotalDiscount());
                //这期是不是续租账单, 续租时间是否不为空
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setMaxTerm(maxTerm);
                repaymentPlanDO.setScore(score);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setRenewDay(renewDay);
                repaymentPlanDO.setBondRateStr(bondRateStr);
                repaymentPlanDO.setRenewTotalRent(renewTotalRent);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBuyoutAmt(buyoutAmt);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRenewStatus(renewStatus);
                repaymentPlanDO.setRenewTerm(renewTerm);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setTotalDiscount(totalDiscount);
                repaymentPlanDO.setDiscountAmt(discountAmt);
                repaymentPlanDO.setBuyOutCapital(buyOutCapital);
                repaymentPlanDO.setBuyOutRealRepayCapital(buyOutRealRepayCapital);
                repaymentPlanDO.setStageNo(domain.getStageNo());
                repaymentPlanDO.setRenewTime(domain.getRenewTime());
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        return planList;
    }
    /**
     * 判断区间
     *
     * @param d
     * @return
     */
    private String fetchInterval(Double d) {
        RangeMap<Double, String> level = TreeRangeMap.create();
        level.put(Range.closed(0D, 5D), "(0%-5%],1");
        level.put(Range.openClosed(5D, 10D), "(5%-10%],2");
        level.put(Range.openClosed(10D, 15D), "(10%-15%],3");
        level.put(Range.openClosed(15D, 20D), "(15%-20%],4");
        level.put(Range.openClosed(20D, 25D), "(20%-25%],5");
        level.put(Range.openClosed(25D, 30D), "(25%-30%],6");
        level.put(Range.openClosed(30D, 35D), "(30%-35%],7");
        level.put(Range.openClosed(35D, 40D), "(35%-40%],8");
        level.put(Range.openClosed(40D, 45D), "(40%-45%],9");
        level.put(Range.openClosed(45D, 50D), "(45%-50%],10");
        level.put(Range.openClosed(50D, 55D), "(50%-55%],11");
        level.put(Range.openClosed(55D, 60D), "(55%-60%],12");
        level.put(Range.openClosed(60D, 65D), "(60%-65%],13");
        level.put(Range.openClosed(65D, 70D), "(65%-70%],14");
        level.put(Range.openClosed(70D, 75D), "(70%-75%],15");
        level.put(Range.openClosed(75D, 80D), "(75%-80%],16");
        level.put(Range.openClosed(80D, 85D), "(80%-85%],17");
        level.put(Range.openClosed(85D, 90D), "(85%-90%],18");
        level.put(Range.openClosed(90D, 95D), "(90%-95%],19");
        level.put(Range.openClosed(95D, 100D), "(95%-100%],20");

        return level.get(d);
    }


    private int updateOverdueStatus(Map<Integer, List<RepaymentPlanDO>> term2PlanDo, int term) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(--term);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (repaymentPlanDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        int isOverdue = Integer.parseInt(planDO.getIsOverdue());
        // 上一期逾期并且未还款,这一期即使还没到,算逾期
        // 上一期逾期并且还款,这一期还没到,不算逾期
        // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
        // 如果当前期逾期,不管有没有到封账日,都算逾期
        int repayStatus = planDO.getRepayStatus();
        return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
    }

    private int updateOverdueStatusNew(Map<Integer, List<RepaymentPlanNewDO>> term2PlanDo, int term) {
        List<RepaymentPlanNewDO> repaymentPlanDOList = term2PlanDo.get(--term);
        RepaymentPlanNewDO planDO = repaymentPlanDOList.get(0);
        if (repaymentPlanDOList.size() > 1) {
            for (RepaymentPlanNewDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        int isOverdue = Integer.parseInt(planDO.getIsOverdue());
        // 上一期逾期并且未还款,这一期即使还没到,算逾期
        // 上一期逾期并且还款,这一期还没到,不算逾期
        // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
        // 如果当前期逾期,不管有没有到封账日,都算逾期
        int repayStatus = planDO.getRepayStatus();
        return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
    }



    private BigDecimal stringToDecimal(String val) {
        if (null==val || "\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }


    /**
     * 递归查找，最早的的逾期期数
     *
     * @param map term为key，还款计划为val
     * @param
     */
    public static void fetchTerm(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        List<RepaymentPlanDO> planDOList = map.get(key);
        RepaymentPlanDO planDO = planDOList.get(0);
        if (planDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : planDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        if (planDO != null) {
            String isOverdue = planDO.getIsOverdue();
            Integer repayStatus = planDO.getRepayStatus();
            Integer overdue = planDO.getOverdue();
            LocalDateTime realRepayTime = planDO.getRealRepayTime();
            BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));

            // //  本期逾期了判断上一期是否逾期,上一期未逾期，那么逾期期数就是本期
            // // 本期逾期了判断上一期是否逾期,上一期逾期，并且未还,继续向上判断,直到找到已还款的那一期,那一期+1就是逾期未还金额
            // // 本期逾期了判断上一期是否逾期,上一期逾期并且已经还款,逾期金额用本期算
            LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                    dateFormatter), LocalTime.MAX);

            if ("1".equals(isOverdue)) {
                // 先判断本期的状态是否已逾期未还款
                if (repayStatus == 1 && overdue == 5) {
                    if (term.compareTo(new BigDecimal(1)) == 0) {
                        resMap.put("term", term);
                    } else {
                        // 递归判断判断上一期的订单是否逾期
                        term = term.subtract(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                        fetchTerm(map, resMap);
                    }
                } else {
                    // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
                    // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
                    // 再递归往上判断
                    if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
                        if (term.compareTo(new BigDecimal(1)) == 0) {
                            resMap.put("term", term);
                        } else {
                            term = term.subtract(BigDecimal.valueOf(1));
                            resMap.put("term", term);
                            fetchTerm(map, resMap);
                        }
                    } else {
                        term = term.add(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                    }
                }

            } else {
                term = term.add(BigDecimal.valueOf(1));
                resMap.put("term", term);
            }


        }

    }

    public static void fetchTerm_new(Map<Integer, List<RepaymentPlanNewDO>> map, Map<String, Object> resMap) {
        int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        List<RepaymentPlanNewDO> planDOList = map.get(key);
        RepaymentPlanNewDO planDO = planDOList.get(0);
        if (planDOList.size() > 1) {
            for (RepaymentPlanNewDO repaymentPlanDO : planDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        if (planDO != null) {
            String isOverdue = planDO.getIsOverdue();
            Integer repayStatus = planDO.getRepayStatus();
            Integer overdue = planDO.getOverdue();
            LocalDateTime realRepayTime = planDO.getRealRepayTime();
            BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));

            // //  本期逾期了判断上一期是否逾期,上一期未逾期，那么逾期期数就是本期
            // // 本期逾期了判断上一期是否逾期,上一期逾期，并且未还,继续向上判断,直到找到已还款的那一期,那一期+1就是逾期未还金额
            // // 本期逾期了判断上一期是否逾期,上一期逾期并且已经还款,逾期金额用本期算
            LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                    dateFormatter), LocalTime.MAX);

            if ("1".equals(isOverdue)) {
                // 先判断本期的状态是否已逾期未还款
                if (repayStatus == 1 && overdue == 5) {
                    if (term.compareTo(new BigDecimal(1)) == 0) {
                        resMap.put("term", term);
                    } else {
                        // 递归判断判断上一期的订单是否逾期
                        term = term.subtract(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                        fetchTerm_new(map, resMap);
                    }
                } else {
                    // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
                    // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
                    // 再递归往上判断
                    if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
                        if (term.compareTo(new BigDecimal(1)) == 0) {
                            resMap.put("term", term);
                        } else {
                            term = term.subtract(BigDecimal.valueOf(1));
                            resMap.put("term", term);
                            fetchTerm_new(map, resMap);
                        }
                    } else {
                        term = term.add(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                    }
                }

            } else {
                term = term.add(BigDecimal.valueOf(1));
                resMap.put("term", term);
            }


        }

    }

    public static void fetchCapital(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                dateFormatter), LocalTime.MAX);
        resMap.put("level", 1);
        if (sourceTerm > 1) {
            fetchTerm(map, resMap);
        }
        int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        for (Map.Entry<Integer, List<RepaymentPlanDO>> entry : map.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            RepaymentPlanDO planDO = value.get(0);
            if (value.size() > 1) {
                for (RepaymentPlanDO repaymentPlanDO : value) {
                    if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                        planDO = repaymentPlanDO;
                    }
                }
            }
            if (entry.getKey() >= term) {
                BigDecimal capital;
                if (term == entry.getKey()) {
                    // 实际还款时间大于这一期封账日,那么这一期将当没还
                    if (planDO.getRealRepayTime() == null) {
                        capital = planDO.getCapital();
                    } else if (planDO.getRealRepayTime() != null && planDO.getRealRepayTime().compareTo(endTime) > 0) {
                        capital = planDO.getCapital();
                    } else {
                        capital = planDO.getRealCapital();
                    }
                } else {
                    capital = planDO.getCapital();
                }
                resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
            }
        }
    }

    public static void fetchCapitalNew(Map<Integer, List<RepaymentPlanNewDO>> map, Map<String, Object> resMap) {
        int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                dateFormatter), LocalTime.MAX);
        resMap.put("level", 1);
        if (sourceTerm > 1) {
            fetchTerm_new(map, resMap);
        }
        int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        //遍历还款计划的每一期
        for (Map.Entry<Integer, List<RepaymentPlanNewDO>> entry : map.entrySet()) {
            List<RepaymentPlanNewDO> value = entry.getValue();
            RepaymentPlanNewDO planDO = value.get(0);
            //如果有多个还款计划，选择未删除的那个
            if (value.size() > 1) {
                for (RepaymentPlanNewDO repaymentPlanDO : value) {
                    if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                        planDO = repaymentPlanDO;
                    }
                }
            }
            //只处理当前期数及之后的期数
            if (entry.getKey() >= term) {
                BigDecimal capital;
                //如果是当前逾期期数
                if (term == entry.getKey()) {
                    // 实际还款时间大于这一期封账日,那么这一期将当没还
                    if (planDO.getRealRepayTime() == null) { //未还款
                        //没有实际还款时间，使用应还本金
                        capital = planDO.getCapital();
                    } else if (planDO.getRealRepayTime().compareTo(endTime) > 0) {
                        //逾期还款 实际还款时间晚于封账日期，视为未还，使用应还本金
                        capital = planDO.getCapital();
                    } else {
                        //逾期剩余还款 实际还款时间晚于封账日期，视为未还，使用应还本金
                        capital = planDO.getRealCapital();
                    }
                } else {
                    //对于后续期数，直接使用应还本金
                    capital = planDO.getCapital();
                }
                if (planDO.getRenewTime() == null && "2".equals(planDO.getStageNo())) {
                    capital = BigDecimal.ZERO;
                }
                if (planDO.getOrderId() == 17600919) {
                    //只处理当前期数及之后的期数
                    log.error("planDO.getRenewTime() {}  {}", planDO.getRenewTime(), planDO.getStageNo());
                    log.error("entry.getKey() {}", entry.getKey());
                    log.error("term {}", term);
                    log.info("逾期金额 term {} capital {}", term , capital);
                }

                //累加到总的逾期未还金额中
                resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
            }
        }
    }


    private void setOverdueVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        }
    }

    private void setRenewAmt(RiskGeneralOrderOverdueDO riskOrderOverdueDO,
                             int term,
                             BigDecimal renewAmt) {
        if (term == 1) {
            riskOrderOverdueDO.setRenewAmt1(renewAmt);
        } else if (term == 2) {
            riskOrderOverdueDO.setRenewAmt2(renewAmt);
        } else if (term == 3) {
            riskOrderOverdueDO.setRenewAmt3(renewAmt);
        } else if (term == 4) {
            riskOrderOverdueDO.setRenewAmt4(renewAmt);
        } else if (term == 5) {
            riskOrderOverdueDO.setRenewAmt5(renewAmt);
        } else if (term == 6) {
            riskOrderOverdueDO.setRenewAmt6(renewAmt);
        } else if (term == 7) {
            riskOrderOverdueDO.setRenewAmt7(renewAmt);
        } else if (term == 8) {
            riskOrderOverdueDO.setRenewAmt8(renewAmt);
        } else if (term == 9) {
            riskOrderOverdueDO.setRenewAmt9(renewAmt);
        } else if (term == 10) {
            riskOrderOverdueDO.setRenewAmt10(renewAmt);
        } else if (term == 11) {
            riskOrderOverdueDO.setRenewAmt11(renewAmt);
        } else if (term == 12) {
            riskOrderOverdueDO.setRenewAmt12(renewAmt);
        } else if (term == 13) {
            riskOrderOverdueDO.setRenewAmt13(renewAmt);
        } else if (term == 14) {
            riskOrderOverdueDO.setRenewAmt14(renewAmt);
        } else if (term == 15) {
            riskOrderOverdueDO.setRenewAmt15(renewAmt);
        } else if (term == 16) {
            riskOrderOverdueDO.setRenewAmt16(renewAmt);
        } else if (term == 17) {
            riskOrderOverdueDO.setRenewAmt17(renewAmt);
        } else if (term == 18) {
            riskOrderOverdueDO.setRenewAmt18(renewAmt);
        } else if (term == 19) {
            riskOrderOverdueDO.setRenewAmt19(renewAmt);
        } else if (term == 20) {
            riskOrderOverdueDO.setRenewAmt20(renewAmt);
        } else if (term == 21) {
            riskOrderOverdueDO.setRenewAmt21(renewAmt);
        } else if (term == 22) {
            riskOrderOverdueDO.setRenewAmt22(renewAmt);
        } else if (term == 23) {
            riskOrderOverdueDO.setRenewAmt23(renewAmt);
        } else if (term == 24) {
            riskOrderOverdueDO.setRenewAmt24(renewAmt);
        } else if (term == 25) {
            riskOrderOverdueDO.setRenewAmt25(renewAmt);
        } else if (term == 26) {
            riskOrderOverdueDO.setRenewAmt26(renewAmt);
        } else if (term == 27) {
            riskOrderOverdueDO.setRenewAmt27(renewAmt);
        } else if (term == 28) {
            riskOrderOverdueDO.setRenewAmt28(renewAmt);
        } else if (term == 29) {
            riskOrderOverdueDO.setRenewAmt29(renewAmt);
        } else if (term == 30) {
            riskOrderOverdueDO.setRenewAmt30(renewAmt);
        }
    }


    private void setOverdueVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        } else if (term == 13) {
            riskOrderOverdueDO.setIsOverdue13(isOverdue);
            riskOrderOverdueDO.setOverdueFine13(overdueFine);
        } else if (term == 14) {
            riskOrderOverdueDO.setIsOverdue14(isOverdue);
            riskOrderOverdueDO.setOverdueFine14(overdueFine);
        } else if (term == 15) {
            riskOrderOverdueDO.setIsOverdue15(isOverdue);
            riskOrderOverdueDO.setOverdueFine15(overdueFine);
        } else if (term == 16) {
            riskOrderOverdueDO.setIsOverdue16(isOverdue);
            riskOrderOverdueDO.setOverdueFine16(overdueFine);
        } else if (term == 17) {
            riskOrderOverdueDO.setIsOverdue17(isOverdue);
            riskOrderOverdueDO.setOverdueFine17(overdueFine);
        } else if (term == 18) {
            riskOrderOverdueDO.setIsOverdue18(isOverdue);
            riskOrderOverdueDO.setOverdueFine18(overdueFine);
        } else if (term == 19) {
            riskOrderOverdueDO.setIsOverdue19(isOverdue);
            riskOrderOverdueDO.setOverdueFine19(overdueFine);
        } else if (term == 20) {
            riskOrderOverdueDO.setIsOverdue20(isOverdue);
            riskOrderOverdueDO.setOverdueFine20(overdueFine);
        } else if (term == 21) {
            riskOrderOverdueDO.setIsOverdue21(isOverdue);
            riskOrderOverdueDO.setOverdueFine21(overdueFine);
        } else if (term == 22) {
            riskOrderOverdueDO.setIsOverdue22(isOverdue);
            riskOrderOverdueDO.setOverdueFine22(overdueFine);
        } else if (term == 23) {
            riskOrderOverdueDO.setIsOverdue23(isOverdue);
            riskOrderOverdueDO.setOverdueFine23(overdueFine);
        } else if (term == 24) {
            riskOrderOverdueDO.setIsOverdue24(isOverdue);
            riskOrderOverdueDO.setOverdueFine24(overdueFine);
        } else if (term == 25) {
            riskOrderOverdueDO.setIsOverdue25(isOverdue);
            riskOrderOverdueDO.setOverdueFine25(overdueFine);
        } else if (term == 26) {
            riskOrderOverdueDO.setIsOverdue26(isOverdue);
            riskOrderOverdueDO.setOverdueFine26(overdueFine);
        } else if (term == 27) {
            riskOrderOverdueDO.setIsOverdue27(isOverdue);
            riskOrderOverdueDO.setOverdueFine27(overdueFine);
        } else if (term == 28) {
            riskOrderOverdueDO.setIsOverdue28(isOverdue);
            riskOrderOverdueDO.setOverdueFine28(overdueFine);
        } else if (term == 29) {
            riskOrderOverdueDO.setIsOverdue29(isOverdue);
            riskOrderOverdueDO.setOverdueFine29(overdueFine);
        } else if (term == 30) {
            riskOrderOverdueDO.setIsOverdue30(isOverdue);
            riskOrderOverdueDO.setOverdueFine30(overdueFine);
        }
    }


    private void setTermNotRepayVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        }
    }

    /**
     * 设置逾期未还金额
     * @param riskOrderOverdueDO
     * @param term
     * @param decimal
     */
    private void setTermNotRepayVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setTerm13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setTerm14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setTerm15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setTerm16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setTerm17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setTerm18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setTerm19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setTerm20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setTerm21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setTerm22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setTerm23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setTerm24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setTerm25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setTerm26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setTerm27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setTerm28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setTerm29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setTerm30(decimal);
        }
    }



    private BigDecimal getParentNotReturnAmt(RiskGeneralOrderOverdueDO parentDO, Integer term) {
        BigDecimal amt = BigDecimal.ZERO;
        if (term == 1) {
            amt = parentDO.getTerm1();
        } else if (term == 2) {
            amt = parentDO.getTerm2();
        } else if (term == 3) {
            amt = parentDO.getTerm3();
        } else if (term == 4) {
            amt = parentDO.getTerm4();
        } else if (term == 5) {
            amt = parentDO.getTerm5();
        } else if (term == 6) {
            amt = parentDO.getTerm6();
        } else if (term == 7) {
            amt = parentDO.getTerm7();
        } else if (term == 8) {
            amt = parentDO.getTerm8();
        } else if (term == 9) {
            amt = parentDO.getTerm9();
        } else if (term == 10) {
            amt = parentDO.getTerm10();
        } else if (term == 11) {
            amt = parentDO.getTerm11();
        } else if (term == 12) {
            amt = parentDO.getTerm12();
        }else if (term == 13) {
            amt = parentDO.getTerm13();
        }else if (term == 14) {
            amt = parentDO.getTerm14();
        }else if (term == 15) {
            amt = parentDO.getTerm15();
        }else if (term == 16) {
            amt = parentDO.getTerm16();
        }else if (term == 17) {
            amt = parentDO.getTerm17();
        }else if (term == 18) {
            amt = parentDO.getTerm18();
        }else if (term == 19) {
            amt = parentDO.getTerm19();
        }else if (term == 20) {
            amt = parentDO.getTerm20();
        }else if (term == 21) {
            amt = parentDO.getTerm21();
        }else if (term == 22) {
            amt = parentDO.getTerm22();
        }else if (term == 23) {
            amt = parentDO.getTerm23();
        }else if (term == 24) {
            amt = parentDO.getTerm24();
        }else if (term == 25) {
            amt = parentDO.getTerm25();
        }else if (term == 26) {
            amt = parentDO.getTerm26();
        }else if (term == 27) {
            amt = parentDO.getTerm27();
        }else if (term == 28) {
            amt = parentDO.getTerm28();
        }else if (term == 29) {
            amt = parentDO.getTerm29();
        }else if (term == 30) {
            amt = parentDO.getTerm30();
        }
        return amt;
    }
}



