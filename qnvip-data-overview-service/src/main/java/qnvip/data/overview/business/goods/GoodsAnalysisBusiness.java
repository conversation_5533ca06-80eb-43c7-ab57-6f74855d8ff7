package qnvip.data.overview.business.goods;

import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class GoodsAnalysisBusiness {

    private final OdpsUtil odpsUtil;

    /**
     * 商品整体概况
     *
     * @param fromTime
     * @param toTime
     */
    public void goodsOverallDataFormat(String fromTime,
                                       String toTime) {
        //
        // // todo 在架商品数
        //
        // // todo 被访问商品数
        // // 访客数、访问量
        // SqlInfo childSqlInfo = ZealotKhala.start().
        //         select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
        //         from(EventTrackSqlParamEnum.DATAVIEW_TRACK_PARTICIPATE_ACTIVITIES.name()).
        //         where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
        //         and(EventTrackSqlParamEnum.GOODS_DETAIL.getDesc()).
        //         andBetween(EventTrackSqlParamEnum.CREATE_TIME.name(), 1, 1).
        //         groupBy("item_id").
        //         end();
        // String childSql = SqlUtils.formart(childSqlInfo);
        //
        // SqlInfo sqlInfo = ZealotKhala.start().
        //         select(EventTrackSqlParamEnum.COUNT.getDesc()).
        //         from("(".concat(childSql).concat(") table")).
        //         where("table.cnt > 0").
        //         end();
        //
        // String sql1 = SqlUtils.formart(sqlInfo);
        // odpsUtil.querySql(sql1);
        // // todo 动销商品数
        //
        // // todo 商品曝光数
        // SqlInfo sqlInfo2 = ZealotKhala.start().
        //         select(EventTrackSqlParamEnum.COUNT.getDesc()).
        //         from(EventTrackSqlParamEnum.DATAVIEW_TRACK_GOODS_EXPOSURE.name()).
        //         where(EventTrackSqlParamEnum.CONDITION.getDesc()).
        //         andBetween(EventTrackSqlParamEnum.CREATE_TIME.name(), fromTime, toTime).
        //         end();
        // String sql2 = SqlUtils.formart(sqlInfo2);
        // odpsUtil.querySql(sql2);
        // // todo 商品浏览量、商品访客数
        // SqlInfo sqlInfo3 = ZealotKhala.start().
        //         select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
        //         from(EventTrackSqlParamEnum.DATAVIEW_TRACK_PAGE_STATISTICS.name()).
        //         where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
        //         and(EventTrackSqlParamEnum.GOODS_DETAIL.getDesc()).
        //         andBetween(EventTrackSqlParamEnum.CREATE_TIME.name(), fromTime, toTime).
        //         end();
        // String sql3 = SqlUtils.formart(sqlInfo3);
        // odpsUtil.querySql(sql3);
        // // todo 提交订单数
        // // todo 风控通过单数
        // // todo 支付单数

    }

    public static void main(String[] args) {

        // todo 被访问商品数
        // 访客数、访问量
        SqlInfo childSqlInfo = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_PARTICIPATE_ACTIVITIES.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                and(EventTrackSqlParamEnum.GOODS_DETAIL.getDesc()).
                andBetween(EventTrackSqlParamEnum.CREATE_TIME.name(), 1, 1).
                groupBy("item_id").
                end();
        String childSql = SqlUtils.formart(childSqlInfo);

        SqlInfo sqlInfo = ZealotKhala.start().
                select(EventTrackSqlParamEnum.COUNT.getDesc()).
                from("(".concat(childSql).concat(") table")).
                where("table.cnt > 0").
                end();

        String sql1 = SqlUtils.formart(sqlInfo);
        System.out.println(sql1);
    }
}
