package qnvip.data.overview.business.marketing;

import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.marketing.OperateActivityCoreDO;
import qnvip.data.overview.service.marketing.OperateActivityCoreService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateActivityBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateActivityCoreService activityCoreService;
    private final RedisTemplate redisTemplate;


    void initMap(Map<Long, OperateActivityCoreDO> activityId2Map,
                 OperateActivityCoreDO item) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!activityId2Map.containsKey(item.getActivityId())) {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            domain.setCountDay(countDay);
            domain.setName(item.getName());
            domain.setActivityBeginTime(item.getActivityBeginTime());
            domain.setActivityEndTime(item.getActivityEndTime());
            domain.setType(item.getType());
            domain.setCouponType(item.getCouponType());
            domain.setCouponIds(item.getCouponIds());
            domain.setStatus(item.getStatus());
            domain.setActivityId(item.getActivityId());
            activityId2Map.put(item.getActivityId(), domain);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        CompletableFuture<List<OperateActivityCoreDO>> f1 =
                CompletableFuture.supplyAsync(this::getOrderCount);
        CompletableFuture<List<OperateActivityCoreDO>> f2 =
                CompletableFuture.supplyAsync(this::getQualityGoodsCnt);
        CompletableFuture<List<OperateActivityCoreDO>> f3 =
                CompletableFuture.supplyAsync(this::getPvAndUv);
        CompletableFuture<List<OperateActivityCoreDO>> f4 =
                CompletableFuture.supplyAsync(this::getRiskPassCount);
        CompletableFuture<List<OperateActivityCoreDO>> f5 =
                CompletableFuture.supplyAsync(this::getPayCount);
        CompletableFuture<List<OperateActivityCoreDO>> f6 =
                CompletableFuture.supplyAsync(this::getActivityInfo);
        CompletableFuture<List<OperateActivityCoreDO>> f7 =
                CompletableFuture.supplyAsync(this::getGoodCnt);
        CompletableFuture<List<OperateActivityCoreDO>> f8 =
                CompletableFuture.supplyAsync(this::getMoveOffCnt);

        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7, f8)
                .join();
        Map<Long, OperateActivityCoreDO> id2Do = new HashMap<>();
        try {
            setActivityInfo(f6, id2Do);
            setOrderCount(f1, id2Do);
            setQualityGoodsCnt(f2, id2Do);
            setPvAndUv(f3, id2Do);
            setRiskCount(f4, id2Do);
            setPayCount(f5, id2Do);
            setGoodsCnt(f7, id2Do);
            setMoveOffCnt(f8, id2Do);
            List<OperateActivityCoreDO> collect = new ArrayList<>(id2Do.values());
            activityCoreService.removeDataByTime(countDay);
            if (activityCoreService.saveBatch(collect)) {
                String hashKey = DateUtils.dateToString(countDay);
                String redisKey = "dataView:ActivityCore_list";
                String jsonStr = JSONUtil.toJsonStr(collect);
                redisTemplate.opsForHash().put(redisKey, hashKey, jsonStr);
                redisTemplate.expire(redisKey, 1, TimeUnit.SECONDS);
            }
        } catch (Exception e) {
            log.error("OperateGoodsParticularsBusiness.runCore error:{}", e.getMessage());
        }
    }

    private void setActivityInfo(CompletableFuture<List<OperateActivityCoreDO>> future,
                                 Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
        }
    }

    private void setPayCount(CompletableFuture<List<OperateActivityCoreDO>> future,
                             Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setPayCount(core.getPayCount());
        }
    }

    private void setRiskCount(CompletableFuture<List<OperateActivityCoreDO>> future,
                              Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setRiskPassCount(core.getRiskPassCount());
        }
    }

    private void setPvAndUv(CompletableFuture<List<OperateActivityCoreDO>> future,
                            Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setPv(core.getPv());
            domain.setUv(core.getUv());
        }
    }

    private void setQualityGoodsCnt(CompletableFuture<List<OperateActivityCoreDO>> future,
                                    Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setQualityMoveOffCnt(core.getQualityMoveOffCnt());
        }
    }

    private void setMoveOffCnt(CompletableFuture<List<OperateActivityCoreDO>> future,
                               Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setMoveOffCnt(core.getMoveOffCnt());
        }
    }

    private void setGoodsCnt(CompletableFuture<List<OperateActivityCoreDO>> future,
                             Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setAssociatedGoodsCnt(core.getAssociatedGoodsCnt());
        }
    }

    private void setOrderCount(CompletableFuture<List<OperateActivityCoreDO>> future,
                               Map<Long, OperateActivityCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateActivityCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateActivityCoreDO domain = id2Do.get(core.getActivityId());
            domain.setOrderCount(core.getOrderCount());
        }
    }

    /**
     * 活动订单量
     */
    private List<OperateActivityCoreDO> getOrderCount() {
        String sql = "select ra.id, " +
                "       count(ro.id) num " +
                " from rent_order ro " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.create_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String orderCount = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setOrderCount(Long.valueOf(orderCount));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 通审单量
     */
    private List<OperateActivityCoreDO> getRiskPassCount() {
        String sql = "select ra.id, " +
                "       count(ro.id) num " +
                " from rent_order ro " +
                "         inner join rent_order_audit rda on ro.id = rda.order_id " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and rda.audit_status = 1 " +
                "  and rda.type = 2 " +
                "  and rda.operate_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rda.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 付款单量
     */
    private List<OperateActivityCoreDO> getPayCount() {
        String sql = "select ra.id, " +
                "       count(ro.id) num " +
                " from rent_order ro " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.status in (1, 5, 15, 60) " +
                "  and ro.payment_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setPayCount(Long.valueOf(num));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 发货单量
     */
    private List<OperateActivityCoreDO> getQualityGoodsCnt() {
        String sql = "select ra.id, " +
                "       count(ro.id) num " +
                " from rent_order ro " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.create_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id, ra.quantity " +
                " having num > 2";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setQualityMoveOffCnt(Long.valueOf(num));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 签收单量
     */
    private List<OperateActivityCoreDO> getPvAndUv() {
        String sql = " select ra.id," +
                "            count(dtpa.customer_third_id)          pv," +
                "                       count(distinct dtpa.customer_third_id) uv" +
                "                 from dataview_track_participate_activities dtpa" +
                "                         inner join rent_activity ra on ra.uid = dtpa.activity_id" +
                "                 where" +
                "                   ra.ds = to_char(getdate(), 'yyyymmdd')" +
                "                  and dtpa.ds = to_char(getdate(), 'yyyymmdd')" +
                "                  and dtpa.report_time between ${fromTime} and ${toTime}" +
                "                 group by ra.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String pv = Optional.ofNullable(record.getString("pv")).orElse("0");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");
            domain.setCountDay(countDay);
            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 关联商品数
     */
    private List<OperateActivityCoreDO> getGoodCnt() {
        String sql = "select ra.id, " +
                "       ra.quantity num " +
                " from rent_order ro " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.create_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id, ra.quantity";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setAssociatedGoodsCnt(Long.valueOf(num));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 动销商品
     */
    private List<OperateActivityCoreDO> getMoveOffCnt() {
        String sql = "select ra.id, " +
                "       count(ro.id) num " +
                " from rent_order ro " +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id " +
                "         inner join rent_item ri on ri.id = rdi.item_id " +
                "         inner join rent_activity ra on ra.id = ri.activity_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.parent_id = 0 " +
                "  and ro.type = 1 " +
                "  and ri.main_category = 2 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.create_time between ${fromTime} and ${toTime} " +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and ra.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by ra.id, ra.quantity " +
                " having num > 0";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setMoveOffCnt(Long.valueOf(num));
            domain.setActivityId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 活动信息
     */
    private List<OperateActivityCoreDO> getActivityInfo() {
        String sql = "select a.*, rc.name" +
                " from (select IF(ra.short_name is null,'', ra.short_name) short_name" +
                "           , (IF(ra.activity_begin_time is null, ra.create_time, ra.activity_begin_time))  activity_begin_time" +
                "           , (IF(ra.activity_end_time is null, ra.create_time, ra.activity_end_time))      activity_end_time" +
                "           , ra.type" +
                "           , (IF(ra.coupon_ids = '', '', get_json_object(ra.coupon_ids, '$[0].couponId'))) " +
                " couponid" +
                "           , ra.status" +
                "           , ra.discount_type" +
                "           , ra.id" +
                "      from rent_activity ra" +
                "      where ra.short_name <> ''" +
                "        and ra.ds = to_char(getdate(), 'yyyymmdd')) a" +
                "         left join rent_coupon rc on a.couponid = rc.id and rc.ds = to_char(getdate(), 'yyyymmdd')";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateActivityCoreDO domain = new OperateActivityCoreDO();
            String id = record.getString("id");
            String shortName = record.getString("short_name");
            String activityBeginTime = record.getString("activity_begin_time");
            String activityEndTime = record.getString("activity_end_time");
            String type = record.getString("type");
            String status = record.getString("status");
            String discountType = record.getString("discount_type");
            String name = record.getString("name");
            domain.setCountDay(countDay);
            domain.setActivityId(Long.valueOf(id));
            domain.setName(shortName);
            domain.setCouponIds(name);
            domain.setType(Integer.valueOf(type));
            domain.setStatus(Integer.valueOf(status));
            domain.setCouponType(Integer.valueOf(discountType));
            domain.setActivityBeginTime(DateUtils.stringToDate(activityBeginTime));
            domain.setActivityEndTime(DateUtils.stringToDate(activityEndTime));
            return domain;
        }).collect(Collectors.toList());
    }

}
