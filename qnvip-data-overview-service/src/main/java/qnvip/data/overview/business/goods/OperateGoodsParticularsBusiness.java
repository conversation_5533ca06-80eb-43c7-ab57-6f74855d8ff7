package qnvip.data.overview.business.goods;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.goods.OperateGoodsParticularsDO;
import qnvip.data.overview.service.goods.OperateGoodsParticularsService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateGoodsParticularsBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateGoodsParticularsService goodsParticularsService;

    void initMap(Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> miniType2Map,
                 OperateGoodsParticularsDO item) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!miniType2Map.containsKey(item)) {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            domain.setCountDay(countDay);
            domain.setName(item.getName());
            domain.setType(item.getType());
            domain.setColor(item.getColor());
            domain.setSpecification(item.getSpecification());
            domain.setGoodsTime(item.getGoodsTime());
            miniType2Map.put(item, domain);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        // 上架
        CompletableFuture<List<OperateGoodsParticularsDO>> f1 =
                CompletableFuture.supplyAsync(this::getOrderCountOnShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f2 =
                CompletableFuture.supplyAsync(this::getSendCountOnShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f3 =
                CompletableFuture.supplyAsync(this::getSignCountOnShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f4 =
                CompletableFuture.supplyAsync(this::getRiskPassCountOnShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f5 =
                CompletableFuture.supplyAsync(this::getPayCountOnShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f6 =
                CompletableFuture.supplyAsync(this::getuvOnShelves);

        // 下架
        CompletableFuture<List<OperateGoodsParticularsDO>> f7 =
                CompletableFuture.supplyAsync(this::getOrderCountOffShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f8 =
                CompletableFuture.supplyAsync(this::getSendCountOffShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f9 =
                CompletableFuture.supplyAsync(this::getSignCountOffShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f10 =
                CompletableFuture.supplyAsync(this::getRiskPassCountOffShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f11 =
                CompletableFuture.supplyAsync(this::getPayCountOffShelves);
        CompletableFuture<List<OperateGoodsParticularsDO>> f12 =
                CompletableFuture.supplyAsync(this::getuvOffShelves);

        // 全量
        CompletableFuture<List<OperateGoodsParticularsDO>> f13 =
                CompletableFuture.supplyAsync(this::getOrderCount);
        CompletableFuture<List<OperateGoodsParticularsDO>> f14 =
                CompletableFuture.supplyAsync(this::getSendCount);
        CompletableFuture<List<OperateGoodsParticularsDO>> f15 =
                CompletableFuture.supplyAsync(this::getSignCount);
        CompletableFuture<List<OperateGoodsParticularsDO>> f16 =
                CompletableFuture.supplyAsync(this::getRiskPassCount);
        CompletableFuture<List<OperateGoodsParticularsDO>> f17 =
                CompletableFuture.supplyAsync(this::getPayCount);
        CompletableFuture<List<OperateGoodsParticularsDO>> f18 =
                CompletableFuture.supplyAsync(this::getuv);
        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14, f15, f16, f17, f18)
                .join();
        Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map = new HashMap<>();
        try {
            setOrderCount(f1, sku2Map);
            setSendCount(f2, sku2Map);
            setSignCount(f3, sku2Map);
            setRiskCount(f4, sku2Map);
            setPayCount(f5, sku2Map);
            setUv(f6, sku2Map);

            setOrderCount(f7, sku2Map);
            setSendCount(f8, sku2Map);
            setSignCount(f9, sku2Map);
            setRiskCount(f10, sku2Map);
            setPayCount(f11, sku2Map);
            setUv(f12, sku2Map);

            setOrderCount(f13, sku2Map);
            setSendCount(f14, sku2Map);
            setSignCount(f15, sku2Map);
            setRiskCount(f16, sku2Map);
            setPayCount(f17, sku2Map);
            setUv(f18, sku2Map);
            List<OperateGoodsParticularsDO> collect = new ArrayList<>(sku2Map.values());
            goodsParticularsService.removeDataByTime(countDay);
            goodsParticularsService.saveBatch(collect);
        } catch (Exception e) {
            log.error("OperateGoodsParticularsBusiness.runCore error:{}", e.getMessage());
        }
    }

    private void setUv(CompletableFuture<List<OperateGoodsParticularsDO>> f6,
                       Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f6.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setUv(core.getUv());
        }
    }

    private void setPayCount(CompletableFuture<List<OperateGoodsParticularsDO>> f5,
                             Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f5.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setPayCount(core.getPayCount());
        }
    }

    private void setRiskCount(CompletableFuture<List<OperateGoodsParticularsDO>> f4,
                              Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f4.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setRiskPassCount(core.getRiskPassCount());
        }
    }

    private void setSignCount(CompletableFuture<List<OperateGoodsParticularsDO>> f3,
                              Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f3.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setSignCount(core.getSignCount());
        }
    }

    private void setSendCount(CompletableFuture<List<OperateGoodsParticularsDO>> f2,
                              Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f2.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setSendCount(core.getSendCount());
        }
    }

    private void setOrderCount(CompletableFuture<List<OperateGoodsParticularsDO>> f1,
                               Map<OperateGoodsParticularsDO, OperateGoodsParticularsDO> sku2Map)
            throws InterruptedException, ExecutionException {
        for (OperateGoodsParticularsDO core : f1.get()) {
            initMap(sku2Map, core);
            OperateGoodsParticularsDO domain = sku2Map.get(core);
            domain.setOrderCount(core.getOrderCount());
        }
    }

    /**
     * 商品上架 下单量
     */
    private List<OperateGoodsParticularsDO> getOrderCountOnShelves() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) order_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and a.create_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String orderCount = Optional.ofNullable(record.getString("order_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setOrderCount(Long.valueOf(orderCount));
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 商品上架 通审单量
     */
    private List<OperateGoodsParticularsDO> getRiskPassCountOnShelves() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) risk_pass_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "                inner join rent_order_audit d on a.id = d.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and d.audit_status = 1" +
                "        and d.type = 2" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.operate_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String riskPassCount = Optional.ofNullable(record.getString("risk_pass_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setRiskPassCount(Long.valueOf(riskPassCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 付款单量
     */
    private List<OperateGoodsParticularsDO> getPayCountOnShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) pay_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b" +
                "      on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and a.status in (1" +
                "          , 5" +
                "          , 15" +
                "          , 60)" +
                "        and a.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and b.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and c.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and a.payment_time between ${fromTime}" +
                "        and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String payCount = Optional.ofNullable(record.getString("pay_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setPayCount(Long.valueOf(payCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 发货单量
     */
    private List<OperateGoodsParticularsDO> getSendCountOnShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) send_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.send_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String sendCount = Optional.ofNullable(record.getString("send_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSendCount(Long.valueOf(sendCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 签收单量
     */
    private List<OperateGoodsParticularsDO> getSignCountOnShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) sign_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String signCount = Optional.ofNullable(record.getString("sign_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSignCount(Long.valueOf(signCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品上架 uv
     */
    private List<OperateGoodsParticularsDO> getuvOnShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(distinct f.customer_third_id) uv" +
                " from (select g.customer_third_id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          inner join rent_order_item b  on a.id = b.order_id" +
                "          inner join rent_item c on c.id = b.item_id" +
                "         inner join rent_customer f on a.customer_id = f.id" +
                "          inner  join dataview_track_page_statistics g   on get_json_object(f.bindinfo, '$[0].miniUserId') = g.customer_third_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 5" +
                "        and a.merchant_id = 100" +
                "        and g.action_type =1" +
                "        and g.enter_page_code = 10009" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and f.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.report_time between ${fromTime} and ${toTime}" +
                "      group by g.customer_third_id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(1);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 下单量
     */
    private List<OperateGoodsParticularsDO> getOrderCountOffShelves() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) order_count" +
                " from (select a.id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and a.create_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String orderCount = Optional.ofNullable(record.getString("order_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setOrderCount(Long.valueOf(orderCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 通审单量
     */
    private List<OperateGoodsParticularsDO> getRiskPassCountOffShelves() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) risk_pass_count" +
                " from (select a.id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "                inner join rent_order_audit d on a.id = d.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and d.audit_status = 1" +
                "        and d.type = 2" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.operate_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String riskPassCount = Optional.ofNullable(record.getString("risk_pass_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setRiskPassCount(Long.valueOf(riskPassCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 付款单量
     */
    private List<OperateGoodsParticularsDO> getPayCountOffShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) pay_count" +
                " from (select a.id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b" +
                "      on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and a.status in (1" +
                "          , 5" +
                "          , 15" +
                "          , 60)" +
                "        and a.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and b.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and c.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and a.payment_time between ${fromTime}" +
                "        and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String payCount = Optional.ofNullable(record.getString("pay_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setPayCount(Long.valueOf(payCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 发货单量
     */
    private List<OperateGoodsParticularsDO> getSendCountOffShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) send_count" +
                " from (select a.id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.send_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String sendCount = Optional.ofNullable(record.getString("send_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSendCount(Long.valueOf(sendCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 签收单量
     */
    private List<OperateGoodsParticularsDO> getSignCountOffShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) sign_count" +
                " from (select a.id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String signCount = Optional.ofNullable(record.getString("sign_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSignCount(Long.valueOf(signCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 商品下架 uv
     */
    private List<OperateGoodsParticularsDO> getuvOffShelves() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(distinct f.customer_third_id) uv" +
                " from (select g.customer_third_id, c.name, b.color, b.size, c.update_time time" +
                "      from rent_order a" +
                "          inner join rent_order_item b  on a.id = b.order_id" +
                "          inner join rent_item c on c.id = b.item_id" +
                "         inner join rent_customer f on a.customer_id = f.id" +
                "          inner  join dataview_track_page_statistics g   on get_json_object(f.bindinfo, '$[0].miniUserId') = g.customer_third_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and c.status = 6" +
                "        and a.merchant_id = 100" +
                "        and g.action_type =1" +
                "        and g.enter_page_code = 10009" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and f.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.report_time between ${fromTime} and ${toTime}" +
                "      group by g.customer_third_id, c.name, b.color, b.size, c.update_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(2);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单量
     */
    private List<OperateGoodsParticularsDO> getOrderCount() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) order_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and a.create_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String orderCount = Optional.ofNullable(record.getString("order_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setOrderCount(Long.valueOf(orderCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 通审单量
     */
    private List<OperateGoodsParticularsDO> getRiskPassCount() {
        String sql = "select f.time goods_time," +
                "       f.name," +
                "       f.size specification," +
                "       f.color," +
                "       count(f.id) risk_pass_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "               left join rent_order_item b on a.id = b.order_id" +
                "               left join rent_item c on c.id = b.item_id" +
                "                inner join rent_order_audit d on a.id = d.order_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and a.status != 30" +
                "        and d.audit_status = 1" +
                "        and d.type = 2" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.operate_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String riskPassCount = Optional.ofNullable(record.getString("risk_pass_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setRiskPassCount(Long.valueOf(riskPassCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 付款单量
     */
    private List<OperateGoodsParticularsDO> getPayCount() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) pay_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b" +
                "      on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and a.status in (1" +
                "          , 5" +
                "          , 15" +
                "          , 60)" +
                "        and a.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and b.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and c.ds = to_char(getdate()" +
                "          , 'yyyymmdd')" +
                "        and a.payment_time between ${fromTime}" +
                "        and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String payCount = Optional.ofNullable(record.getString("pay_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setPayCount(Long.valueOf(payCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 发货单量
     */
    private List<OperateGoodsParticularsDO> getSendCount() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) send_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.send_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String sendCount = Optional.ofNullable(record.getString("send_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSendCount(Long.valueOf(sendCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 签收单量
     */
    private List<OperateGoodsParticularsDO> getSignCount() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(f.id) sign_count" +
                " from (select a.id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          left join rent_order_item b  on a.id = b.order_id" +
                "          left join rent_item c on c.id = b.item_id" +
                "         inner join rent_order_logistics d on b.logistics_id = d.id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and d.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String signCount = Optional.ofNullable(record.getString("sign_count")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setSignCount(Long.valueOf(signCount));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * uv
     */
    private List<OperateGoodsParticularsDO> getuv() {
        String sql = "select f.time      goods_time," +
                "       f.name," +
                "       f.size      specification," +
                "       f.color," +
                "       count(distinct f.customer_third_id) uv" +
                " from (select g.customer_third_id, c.name, b.color, b.size, c.create_time time" +
                "      from rent_order a" +
                "          inner join rent_order_item b  on a.id = b.order_id" +
                "          inner join rent_item c on c.id = b.item_id" +
                "         inner join rent_customer f on a.customer_id = f.id" +
                "          inner  join dataview_track_page_statistics g   on get_json_object(f.bindinfo, '$[0].miniUserId') = g.customer_third_id" +
                "      where a.is_deleted = 0" +
                "        and a.parent_id = 0" +
                "        and a.type = 1" +
                "        and c.main_category = 2" +
                "        and a.merchant_id = 100" +
                "        and g.action_type =1" +
                "        and g.enter_page_code = 10009" +
                "        and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and f.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.ds = to_char(getdate(), 'yyyymmdd')" +
                "        and g.report_time between ${fromTime} and ${toTime}" +
                "      group by g.customer_third_id, c.name, b.color, b.size, c.create_time) f" +
                " group by f.time, f.name, f.size, f.color;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateGoodsParticularsDO domain = new OperateGoodsParticularsDO();
            String name = record.getString("name");
            String color = record.getString("color");
            String specification = record.getString("specification");
            String goodsTime = record.getString("goods_time");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");
            domain.setCountDay(countDay);
            domain.setType(3);
            domain.setName(name);
            domain.setColor(color);
            domain.setSpecification(specification);
            domain.setGoodsTime(DateUtils.stringToDate(goodsTime));
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }


}
