package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperateAccessSourceDO;
import qnvip.data.overview.domain.access.OperateAccessSourceQuotientDO;
import qnvip.data.overview.service.access.OperateAccessSourceQuotientService;
import qnvip.data.overview.service.access.OperateAccessSourceService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/15
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateAccessSourceBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateAccessSourceService sourceService;
    private final OperateAccessSourceQuotientService sourceQuotientService;

    void initMap(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, OperateAccessSourceDO sourceDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!scene2Do.containsKey(sourceDO)) {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            domain.setCountDay(countDay);
            domain.setScene(sourceDO.getScene());
            domain.setSceneName(sourceDO.getSceneName());
            domain.setMiniType(sourceDO.getMiniType());
            domain.setType(sourceDO.getType());
            scene2Do.put(sourceDO, domain);
        }

    }


    /**
     * 定时调度任务
     */
    public void runCore(String ds) {

        try {
            Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do = new HashMap<>();
            CompletableFuture.runAsync(()->getQuotientInfo(ds));
            CompletableFuture<List<OperateAccessSourceDO>> f6 = CompletableFuture.supplyAsync(()->getUvWithOrder(ds));
            CompletableFuture<List<OperateAccessSourceDO>> f7 = CompletableFuture.supplyAsync(()->getOrderCountWithOrder(ds));
            CompletableFuture<List<OperateAccessSourceDO>> f8 = CompletableFuture.supplyAsync(()->getRiskCountWithOrder(ds));
            CompletableFuture<List<OperateAccessSourceDO>> f9 = CompletableFuture.supplyAsync(()->getRiskPassCountWithOrder(ds));
            CompletableFuture<List<OperateAccessSourceDO>> f10 = CompletableFuture.supplyAsync(()->getPayCountWithOrder(ds));
            CompletableFuture.allOf(f6, f7, f8, f9, f10).join();
            // 订单来源场景分析
            getUv(scene2Do, f6);
            getOrderCount(scene2Do, f7);
            getRiskCount(scene2Do, f8);
            getRiskPassCount(scene2Do, f9);
            getPayCount(scene2Do, f10);
            LinkedList<OperateAccessSourceDO> list = Lists.newLinkedList();
            // 写入mysql
            for (Map.Entry<OperateAccessSourceDO, OperateAccessSourceDO> entrySet : scene2Do.entrySet()) {
                OperateAccessSourceDO value = entrySet.getValue();
                list.add(value);
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            sourceService.removeDataByTime(countDay);
            sourceService.saveBatch(list);

        } catch (Exception e) {
            log.error("OperateAccessSourceBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void getPayCount(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, CompletableFuture<List<OperateAccessSourceDO>> f5) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSourceDO coreDO : f5.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSourceDO domain = scene2Do.get(coreDO);
            domain.setPayCount(coreDO.getPayCount());
        }
    }

    private void getRiskPassCount(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, CompletableFuture<List<OperateAccessSourceDO>> f4) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSourceDO coreDO : f4.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSourceDO domain = scene2Do.get(coreDO);
            domain.setRiskPassCount(coreDO.getRiskPassCount());
        }
    }

    private void getRiskCount(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, CompletableFuture<List<OperateAccessSourceDO>> f3) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSourceDO coreDO : f3.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSourceDO domain = scene2Do.get(coreDO);
            domain.setRiskCount(coreDO.getRiskCount());
        }
    }

    private void getOrderCount(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, CompletableFuture<List<OperateAccessSourceDO>> f2) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSourceDO coreDO : f2.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSourceDO domain = scene2Do.get(coreDO);
            domain.setOrderCount(coreDO.getOrderCount());
        }
    }

    private void getUv(Map<OperateAccessSourceDO, OperateAccessSourceDO> scene2Do, CompletableFuture<List<OperateAccessSourceDO>> f1) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSourceDO coreDO : f1.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSourceDO domain = scene2Do.get(coreDO);
            domain.setUv(coreDO.getUv());
            domain.setPv(coreDO.getPv());
        }
    }

    /**
     * 获取导流商信息
     */
    private void getQuotientInfo(String ds) {
        String sql = "select " +
                "       scene," +
                "       scene_name," +
                "       click_count," +
                "       click_user_count," +
                "       order_count," +
                "       risk_pass_count," +
                "       payment_count," +
                "       mini_type" +
                " from rent_quotient_statistics" +
                " where is_deleted = 0" +
                "  and create_time >= ${fromTime}" +
                "  and create_time <= ${toTime}" +
                "  and ds = "+ds;

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return ;
        }
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        List<OperateAccessSourceQuotientDO> collect = records.stream().map(record -> {
            OperateAccessSourceQuotientDO domain = new OperateAccessSourceQuotientDO();
            String scene = record.getString("scene");
            String sceneName = record.getString("scene_name");
            String clickCount = record.getString("click_count");
            String clickUserCount = record.getString("click_user_count");
            String orderCount = record.getString("order_count");
            String riskPassCount = record.getString("risk_pass_count");
            String paymentCount = record.getString("payment_count");
            String miniType = record.getString("mini_type");

            domain.setScene(scene);
            domain.setSceneName(sceneName);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setCountDay(countDay);
            domain.setClickCount(Long.valueOf(clickCount));
            domain.setClickUserCount(Long.valueOf(clickUserCount));
            domain.setOrderCount(Long.valueOf(orderCount));
            domain.setRiskPassCount(Long.valueOf(riskPassCount));
            domain.setPaymentCount(Long.valueOf(paymentCount));

            return domain;
        }).collect(Collectors.toList());
        sourceQuotientService.removeDataByTime(countDay);
        sourceQuotientService.saveBatch(collect);
    }
    /**
     * UV
     */
    private List<OperateAccessSourceDO> getUvWithOrder(String ds) {
        String sql = "select scene,mini_type" +
                "       ,count(distinct  customer_third_id) uv" +
                "       ,count( customer_third_id) pv" +
                " from dataview_track_enter_applets" +
                " where action_type =1" +
                "  and ds = "+ds +
                " and report_time between ${fromTime}    and ${toTime}" +
                " group by mini_type,scene";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            String quotientScene = record.getString("scene");
            String miniType = record.getString("mini_type");
            String uv = record.getString("uv");
            String pv = record.getString("pv");
            domain.setScene(quotientScene);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUv(Long.valueOf(uv));
            domain.setPv(Long.valueOf(pv));
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 下单
     */
    private List<OperateAccessSourceDO> getOrderCountWithOrder(String ds) {
        String sql = "select ro.scene," +
                "       ro.mini_type," +
                "       count(distinct ro.id) order_count" +
                " from rent_order ro" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.ds = "+ds +
                "  and ro.create_time between ${fromTime}    and ${toTime}" +
                " group by ro.scene, ro.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            String quotientScene = record.getString("scene");
            String orderCount = record.getString("order_count");
            String miniType = record.getString("mini_type");
            domain.setScene(quotientScene);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderCount(Long.valueOf(orderCount));
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());

    }

    /**
     * 审核订单
     */
    private List<OperateAccessSourceDO> getRiskCountWithOrder(String ds) {
        String sql = "select ro.scene," +
                "       ro.mini_type," +
                "       count(distinct ro.id) order_count" +
                " from rent_order ro" +
                "         inner join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and roa.audit_status in (0, 1, 2)" +
                "  and roa.type = 2" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and roa.operate_time between ${fromTime}    and ${toTime}" +
                " group by ro.scene, ro.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            String quotientScene = record.getString("scene");
            String orderCount = record.getString("order_count");
            String miniType = record.getString("mini_type");
            domain.setScene(quotientScene);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRiskCount(Long.valueOf(orderCount));
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());

    }

    /**
     * 通审
     */
    private List<OperateAccessSourceDO> getRiskPassCountWithOrder(String ds) {
        String sql = "select ro.scene," +
                "       ro.mini_type," +
                "       count(distinct ro.id) order_count" +
                " from rent_order ro" +
                "         inner join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and roa.audit_status =1" +
                "  and roa.type =2" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and roa.operate_time between ${fromTime}    and ${toTime}" +
                " group by ro.scene, ro.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            String quotientScene = record.getString("scene");
            String orderCount = record.getString("order_count");
            String miniType = record.getString("mini_type");
            domain.setScene(quotientScene);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRiskPassCount(Long.valueOf(orderCount));
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());

    }

    /**
     * 支付
     */
    private List<OperateAccessSourceDO> getPayCountWithOrder(String ds) {
        String sql = "select ro.scene," +
                "       ro.mini_type," +
                "       count(distinct ro.id) order_count" +
                " from rent_order ro" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and  ro.status in (1, 5, 15, 60)" +
                "  and ro.merchant_id = 100" +
                "  and ro.ds = "+ds +
                "  and ro.payment_time between ${fromTime}    and ${toTime}" +
                " group by ro.scene, ro.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSourceDO domain = new OperateAccessSourceDO();
            String quotientScene = record.getString("scene");
            String orderCount = record.getString("order_count");
            String miniType = record.getString("mini_type");
            domain.setScene(quotientScene);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayCount(Long.valueOf(orderCount));
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());
    }
}
