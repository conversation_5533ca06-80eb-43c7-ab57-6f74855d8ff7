package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPayStructureDO;
import qnvip.data.overview.service.dataindicators.IndicatorPayStructureService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorPayStructureBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorPayStructureService indicatorPayStructureService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorPayStructureDO> collectMap = new HashMap<>();
        countSelfPayCount(ds, sTime, eTime, collectMap);
        countWithholdCount(ds, sTime, eTime, collectMap);
        countElectricCount(ds, sTime, eTime, collectMap);
        ArrayList<IndicatorPayStructureDO> list = Lists.newArrayList();
        for (IndicatorPayStructureDO value : collectMap.values()) {
            //实际主动支付=主动支付-电销支付
            value.setPlanOneTermOneSelfPay(value.getPlanOneTermOneSelfPay()-value.getPlanOneTermOneElectricPay());
            value.setPlanOneTermTwoSelfPay(value.getPlanOneTermTwoSelfPay()-value.getPlanOneTermTwoElectricPay());
            value.setPlanTwoTermOneSelfPay(value.getPlanTwoTermOneSelfPay()-value.getPlanTwoTermOneElectricPay());
            value.setPlanTwoTermTwoSelfPay(value.getPlanTwoTermTwoSelfPay()-value.getPlanTwoTermTwoElectricPay());
            value.setPlanThreeTermOneSelfPay(value.getPlanThreeTermOneSelfPay()-value.getPlanThreeTermOneElectricPay());
            value.setPlanThreeTermTwoSelfPay(value.getPlanThreeTermTwoSelfPay()-value.getPlanThreeTermTwoElectricPay());
            list.add(value);
            // indicatorPayStructureService.saveOrUpdate(value);
        }
        if (CollUtil.isNotEmpty(list)) {
            indicatorPayStructureService.saveOrUpdateBatch(list);
        }
    }


    /**
     * 支付人数中 主动支付人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countSelfPayCount(String ds, String sTime, String eTime,
                                   Map<String, IndicatorPayStructureDO> collectMap) {
        String sql = " select a.mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "    c.rate_config_type,c.bond_rate,count(1) count " +
                "from rent_order a " +
                "         inner join rent_order_flow b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on c.order_id = a.id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and b.flow_type = 1 " +
                "  and b.parent_uid = '' " +
                "  and b.pay_status = 10 " +
                "  and b.biz_type = 4 " +
                "  and b.alipay_pay_type not in (2, 4, 5) " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "  IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'),c.bond_rate; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorPayStructureDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThreeTermOneSelfPay(Optional.ofNullable(updateDO.getPlanThreeTermOneSelfPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanThreeTermTwoSelfPay(Optional.ofNullable(updateDO.getPlanThreeTermTwoSelfPay()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanOneTermOneSelfPay(Optional.ofNullable(updateDO.getPlanOneTermOneSelfPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanOneTermTwoSelfPay(Optional.ofNullable(updateDO.getPlanOneTermTwoSelfPay()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanTwoTermOneSelfPay(Optional.ofNullable(updateDO.getPlanTwoTermOneSelfPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanTwoTermTwoSelfPay(Optional.ofNullable(updateDO.getPlanTwoTermTwoSelfPay()).orElse(0) + count);
                }
            }
        }
    }


    /**
     * 支付人数中 系统代扣人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countWithholdCount(String ds, String sTime, String eTime,
                                    Map<String, IndicatorPayStructureDO> collectMap) {
        String sql = " select a.mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "    c.rate_config_type,c.bond_rate,count(1) count " +
                "from rent_order a " +
                "         inner join rent_order_flow b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on c.order_id = a.id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and b.flow_type = 1 " +
                "  and b.parent_uid = '' " +
                "  and b.pay_status = 10 " +
                "  and b.biz_type = 4 " +
                "  and b.alipay_pay_type in (2, 4, 5) " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "  IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'),c.bond_rate; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorPayStructureDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThreeTermOneWithhold(Optional.ofNullable(updateDO.getPlanThreeTermOneWithhold()).orElse(0) + count);
                } else {
                    updateDO.setPlanThreeTermTwoWithhold(Optional.ofNullable(updateDO.getPlanThreeTermTwoWithhold()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanOneTermOneWithhold(Optional.ofNullable(updateDO.getPlanOneTermOneWithhold()).orElse(0) + count);
                } else {
                    updateDO.setPlanOneTermTwoWithhold(Optional.ofNullable(updateDO.getPlanOneTermTwoWithhold()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanTwoTermOneWithhold(Optional.ofNullable(updateDO.getPlanTwoTermOneElectricPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanTwoTermTwoWithhold(Optional.ofNullable(updateDO.getPlanTwoTermTwoWithhold()).orElse(0) + count);
                }
            }
        }
    }


    /**
     * 支付人数中 电销支付人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countElectricCount(String ds, String sTime, String eTime,
                                    Map<String, IndicatorPayStructureDO> collectMap) {
        String sql = " select a.mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "    c.rate_config_type,c.bond_rate,count(1) count " +
                "from rent_order a " +
                "         inner join rent_order_flow b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on c.order_id = a.id " +
                "         inner join ( " +
                "    select max(mobile_service_operate_time) mobile_service_operate_time, order_id, " +
                "           max(service_operate_time) service_operate_time " +
                "    from ( " +
                "             select * " +
                "             from rent_service_allot " +
                "             where ds = " + ds +
                "             order by create_time desc " +
                "         ) x " +
                "    group by x.order_id " +
                ") d on a.id = d.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2  " +
                "  and b.flow_type = 1 " +
                "  and b.parent_uid = '' " +
                "  and b.pay_status = 10 " +
                "  and b.biz_type = 4 " +
                "  and b.alipay_pay_type not in (2, 4, 5) " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and (d.mobile_service_operate_time < b.flow_time or d.service_operate_time < b.flow_time) " +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "  IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'),c.bond_rate; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorPayStructureDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThreeTermOneElectricPay(Optional.ofNullable(updateDO.getPlanThreeTermOneElectricPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanThreeTermTwoElectricPay(Optional.ofNullable(updateDO.getPlanThreeTermTwoElectricPay()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanOneTermOneElectricPay(Optional.ofNullable(updateDO.getPlanOneTermOneElectricPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanOneTermTwoElectricPay(Optional.ofNullable(updateDO.getPlanOneTermTwoElectricPay()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanTwoTermOneElectricPay(Optional.ofNullable(updateDO.getPlanTwoTermOneElectricPay()).orElse(0) + count);
                } else {
                    updateDO.setPlanTwoTermTwoElectricPay(Optional.ofNullable(updateDO.getPlanTwoTermTwoElectricPay()).orElse(0) + count);
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorPayStructureDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorPayStructureDO ivd = new IndicatorPayStructureDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setPlanOneTermOneWithhold(0);
            ivd.setPlanOneTermOneSelfPay(0);
            ivd.setPlanOneTermOneElectricPay(0);
            ivd.setPlanOneTermTwoWithhold(0);
            ivd.setPlanOneTermTwoSelfPay(0);
            ivd.setPlanOneTermTwoElectricPay(0);
            ivd.setPlanTwoTermOneWithhold(0);
            ivd.setPlanTwoTermOneSelfPay(0);
            ivd.setPlanTwoTermOneElectricPay(0);
            ivd.setPlanTwoTermTwoWithhold(0);
            ivd.setPlanTwoTermTwoSelfPay(0);
            ivd.setPlanTwoTermTwoElectricPay(0);
            ivd.setPlanThreeTermOneWithhold(0);
            ivd.setPlanThreeTermOneSelfPay(0);
            ivd.setPlanThreeTermOneElectricPay(0);
            ivd.setPlanThreeTermTwoWithhold(0);
            ivd.setPlanThreeTermTwoSelfPay(0);
            ivd.setPlanThreeTermTwoElectricPay(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }


}