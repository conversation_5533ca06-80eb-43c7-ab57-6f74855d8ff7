package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.SignOrderDO;
import qnvip.data.overview.service.access.SignOrderService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

@Component
@RequiredArgsConstructor
public class SignOrderBusiness {

    private final OdpsUtil odpsUtil;

    private final SignOrderService signOrderService;


    public void getSignOrder() {
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String sql = "select a.mini_type as mini_type, b.order_id, a.merchant_id, a.type as order_type, no" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1" +
                "         inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and c.sign_time is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and b.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and c.ds = to_char(getdate(), 'yyyymmdd') " +
                "  and a.is_deleted = 0" +
                "  and a.create_time between ${fromTime} and ${toTime}";

        String sql2 = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(sql2.concat(";"));

        records.forEach(record -> {
            String miniType = record.getString("mini_type");
            Long orderId = record.getBigint("order_id");
            Long merchantId = record.getBigint("merchant_id");
            String orderType = record.getString("order_type");
            String no = record.getString("no");
            SignOrderDO signOrderDO = new SignOrderDO();
            signOrderDO.setOrderId(orderId);
            signOrderDO.setOrderNo(no);
            signOrderDO.setCountDay(countDay);
            signOrderDO.setMerchantId(merchantId);
            signOrderDO.setType(Integer.parseInt(orderType));
            signOrderDO.setMiniType(Integer.parseInt(miniType));
            signOrderService.saveOrUpdate(signOrderDO);
        });

    }

}
