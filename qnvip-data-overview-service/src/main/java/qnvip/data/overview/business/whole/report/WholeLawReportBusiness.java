package qnvip.data.overview.business.whole.report;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeLawReportDO;
import qnvip.data.overview.enums.whole.WholeRentReportTitleEnum;
import qnvip.data.overview.service.whole.report.WholeLawReportService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLawReportBusiness {

    public final static DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    public final static DateTimeFormatter YYYYMMDD = DateTimeFormatter.ofPattern("yyyyMMdd");
    public final static DateTimeFormatter YYYY_MM = DateTimeFormatter.ofPattern("yyyy-MM");

    private final OdpsUtil odpsUtil;
    private final WholeLawReportService wholeLawReportService;

    public void execData(String ds) {
        String countTime = LocalDate.parse(ds, YYYYMMDD).minusDays(1).format(YYYY_MM_DD);
        String time = LocalDate.parse(ds, YYYYMMDD).format(YYYY_MM_DD);
        if (StringUtils.isBlank(ds)) {
            ds = LocalDate.now().format(YYYYMMDD);
            countTime = LocalDate.now().minusDays(1).format(YYYY_MM_DD);
            time = LocalDate.now().format(YYYY_MM_DD);
        }
        countTime = "'" + countTime + "'";
        time = "'" + time + "'";
        wholeLawReportService.removeDataByDs(ds);
        getWeekList(countTime, time, ds,  WholeRentReportTitleEnum.WEEK.getCode());
        getMonthList(countTime, time, ds,  WholeRentReportTitleEnum.MONTH.getCode());
    }

    private void getWeekList(String countTime, String time, String ds, Integer type) {

        String sql = "with dt as (" +
                "       select " + ds + " as ds" +
                "   )," +
                "        count_day as (" +
                "            select " + countTime + " as time" +
                "        )" +
                "   select nvl(a.time1,c.time1)                                     count_day," +
                "          nvl(a.应还订单量, 0)                                         repay_cnt," +
                "          nvl(a.未逾期订单, 0)                                         un_overdue_cnt," +
                "          nvl(a.已逾期订单, 0)                                         overdue_cnt," +
                "          nvl(a.全量应还总额1, 0)                                       total_amt," +
                "          nvl(a.未逾期应还总额, 0)                                       un_overdue_repay_amt," +
                "          nvl(a.未逾期实还总额2, 0)                                      un_overdue_real_pay_amt," +
                "          nvl(a.已逾期应还总额, 0)                                       ," +
                "          nvl(a.已逾期实还总额, 0)                                       overdue_real_pay_amt," +
                "          nvl((a.全量应还总额1 - a.售前金额), 0)                            repay_amt," +
                "          nvl((a.全量应还总额xu1 - a.售前金额xu), 0)                        renew_repay_amt," +
                "          nvl((a.应还订单量 + a.应还订单量xu), 0)                           total_repay_cnt," +
                "          nvl((a.未逾期订单 + a.未逾期订单xu + b.逾期催收订单), 0)                total_un_overdue_cnt," +
                "          nvl((a.未逾期实还总额2 + b.已逾期催收总额), 0)                        overdue_total_amt," +
                "          nvl((a.全量应还总额1 - a.售前金额) + (a.全量应还总额xu1 - a.售前金额xu), 0) total_repay_amt," +
                "          nvl((a.未逾期实还总额2 + b.已逾期催收总额 + a.未逾期实还总额xu2), 0)         total_un_overdue_real_pay_amt," +
                "          nvl(a.入催订单, 0)                                          collection_cnt," +
                "          nvl(a.首逾, 0)                                            first_overdue_amt," +
                "          nvl(a.首期订单, 0)                                          first_cnt," +
                "          nvl(a.售前金额, 0)                                          before_amt," +
                "          nvl(a.全量应还总额xu1, 0)                                     renew_whole_repay_amt," +
                "          nvl(a.未逾期实还总额xu2, 0)                                    renew_un_overdue_real_pay_amt," +
                "          nvl(a.应还订单量xu, 0)                                       renew_repay_cnt," +
                "          nvl(a.未逾期订单xu, 0)                                       renew_un_overdue_cnt," +
                "          nvl(a.首逾xu, 0)                                          renew_first_overdue_amt," +
                "          nvl(a.首期订单xu, 0)                                        renew_first_cnt," +
                "          nvl(a.售前金额xu, 0)                                        renew_before_amt," +
                "          nvl(b.已逾期应还总额, 0)                                    overdue_repay_amt," +
                "          nvl(b.逾期催收订单, 0)                                        overdue_collection_cnt," +
                "          nvl(b.已逾期催收总额, 0)                                       overdue_collection_amt," +
                "          nvl(b.7天催回金额, 0)                                        total_day7_return_amt," +
                "          nvl(b.7天催回订单, 0)                                        total_day7_return_cnt," +
                "          nvl(b.30天催回金额, 0)                                       total_day30_return_amt," +
                "          nvl(b.30天催回订单, 0)                                       total_day30_return_cnt," +
                "          nvl(b.M2催回金额, 0)                                        month2_return_amt," +
                "          nvl(b.M2催回订单, 0)                                        month2_return_cnt," +
                "          nvl(b.7天催回金额主, 0)                                       day7_return_amt," +
                "          nvl(b.30天催回金额主, 0)                                      day30_return_amt," +
                "          nvl(b.7天催回金额续, 0)                                       renew_day7_return_amt," +
                "          nvl(b.30天催回金额续, 0)                                      renew_day30_return_amt," +
                "          nvl(b.已逾期订单, 0)," +
                "          nvl(c.本周7天应催订单, 0)                                      this_day7_should_cnt," +
                "          nvl(c.本周7天催回订单, 0)                                      this_day7_return_cnt," +
                "          nvl(c.本周30天应催金额, 0)                                     this_day30_should_amt," +
                "          nvl(c.本周30天催回金额, 0)                                     this_day30_return_amt," +
                "          nvl(c.上周7天应催订单, 0)                                      last_day7_should_cnt," +
                "          nvl(c.上周7天催回订单, 0)                                      last_day7_return_cnt," +
                "          nvl(c.上周30天应催金额, 0)                                     last_day30_should_amt," +
                "          nvl(c.上周30天催回金额, 0)                                     last_day30_return_amt" +
                "   from (" +
                "            select date(repay_date)                                                                             time1," +
                "                   count(distinct if(ro.parent_id = 0, rorp.order_id, null))                                    应还订单量," +
                "                   count(distinct if(overdue = 1 and ro.parent_id = 0, rorp.order_id, null))                    未逾期订单," +
                "                   count(distinct if(overdue = 5, rorp.order_id, null))                                         已逾期订单," +
                "                   sum(if(ro.parent_id = 0, capital, 0))                                                        全量应还总额1," +
                "                   sum(if(overdue = 1, capital, 0))                                                             未逾期应还总额," +
                "                   sum(if(overdue = 1 and ro.parent_id = 0, real_repay_capital, 0))                             未逾期实还总额2," +
                "                   sum(if(overdue = 5, capital, 0))                                                             已逾期应还总额," +
                "                   sum(if(overdue = 5, real_repay_capital, 0))                                                  已逾期实还总额," +
                "                   count(distinct if(overdue = 5 and" +
                "                                     (((date(repay_date)) + 1 < date(real_repay_time)) or (date(real_repay_time) is null))," +
                "                                     rorp.order_id, null))                                                      入催订单," +
                "                   count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))  首逾," +
                "                   count(distinct if(rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))                  首期订单," +
                "                   sum(if(use_status = 1 and ro.parent_id = 0, discount_amount, 0))                             售前金额," +
                "                   sum(if(ro.parent_id != 0, capital, 0))                                                       全量应还总额xu1," +
                "                   sum(if(overdue = 1 and ro.parent_id != 0, real_repay_capital, 0))                            未逾期实还总额xu2," +
                "                   count(distinct if(ro.parent_id != 0, rorp.order_id, null))                                   应还订单量xu," +
                "                   count(distinct if(overdue = 1 and ro.parent_id != 0, rorp.order_id, null))                   未逾期订单xu," +
                "                   count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null)) 首逾xu," +
                "                   count(distinct if(rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null))                 首期订单xu," +
                "                   sum(if(use_status = 1 and ro.parent_id != 0, discount_amount, 0))                            售前金额xu" +
                "   " +
                "            from rent_order_repayment_plan as rorp" +
                "                     left join rent_order ro" +
                "                               on ro.id = rorp.order_id and ro.ds = (select ds from dt)" +
                "                     left join rent_customer_coupon rcc on rcc.order_id = rorp.order_id and rcc.ds = (select ds from dt)" +
                "            where date(repay_date) between date_sub(" + time + "" +
                "                , 180)" +
                "                and date_sub(" + time + "" +
                "                    , 1)" +
                "              and ro.is_deleted = 0" +
                "              and ro.termination = 1" +
                "              and ro.type = 1" +
                "              and rorp.ds = (select ds from dt)" +
                "            group by time1" +
                "            order by time1" +
                "        ) a" +
                "            left join (" +
                "       select a.time1," +
                "              b.已逾期应还总额," +
                "              a.逾期催收订单," +
                "              a.已逾期催收总额," +
                "              a.7天催回金额," +
                "              a.7天催回订单," +
                "              a.30天催回金额," +
                "              a.30天催回订单," +
                "              a.M2催回金额," +
                "              a.M2催回订单," +
                "              a.7天催回金额主," +
                "              a.30天催回金额主," +
                "              a.7天催回金额续," +
                "              a.30天催回金额续," +
                "              b.已逾期订单" +
                "       from (" +
                "                select date(real_repay_time)                                                                        time1," +
                "                       sum(if(repay_status = 5, real_repay_capital, 0))                                             已逾期催收总额," +
                "                       count(if(repay_status = 5, rorp.order_id, null))                                             逾期催收订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7, real_repay_capital, 0)) 7天催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7, rorp.order_id, NULL)) 7天催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30, real_repay_capital," +
                "                              0))                                                                                   30天催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30, rorp.order_id," +
                "                                NULL))                                                                              30天催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') > 30, real_repay_capital, 0)) M2催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') > 30, rorp.order_id, NULL)) M2催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7 and parent_id = 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   7天催回金额主," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30 and parent_id = 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   30天催回金额主," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7 and parent_id != 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   7天催回金额续," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30 and parent_id != 0," +
                "                              real_repay_capital, 0))                                                               30天催回金额续" +
                "                from rent_order_repayment_plan as rorp" +
                "                         left join rent_order ro" +
                "                                   on ro.id = rorp.order_id" +
                "                where date(real_repay_time) between date_sub(" + time + "" +
                "                    , 180)" +
                "                    and date_sub(" + time + "" +
                "                        , 1)" +
                "                  and ro.is_deleted = 0" +
                "                  and ro.termination = 1" +
                "                  and ro.type = 1" +
                "                  and rorp.ds = (select ds from dt)" +
                "                  and ro.ds = (select ds from dt)" +
                "                  and rorp.overdue = 5" +
                "                group by time1" +
                "                order by time1) a" +
                "                left join (" +
                "           select date(repay_date)                    time1," +
                "                  sum(if(overdue = 5, capital, 0))    已逾期应还总额," +
                "                  count(if(overdue = 5, ro.id, null)) 已逾期订单" +
                "           from rent_order_repayment_plan as rorp" +
                "                    left join rent_order ro" +
                "                              on ro.id = rorp.order_id" +
                "           where date(repay_date) between date_sub(" + time + "" +
                "               , 180)" +
                "               and date_sub(" + time + "" +
                "                   , 1)" +
                "             and ro.is_deleted = 0" +
                "             and ro.termination = 1" +
                "             and ro.type = 1" +
                "             and rorp.is_deleted = 0" +
                "             and rorp.ds = (select ds from dt)" +
                "             and ro.ds = (select ds from dt)" +
                "           group by time1" +
                "           order by time1) b" +
                "                          on a.time1 = b.time1" +
                "       order by time1" +
                "   ) b on a.time1 = b.time1" +
                "            full join" +
                "        (" +
                "            select count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 13) and date_sub(" + time + ", 1) and" +
                "                            rorp.is_deleted = 0, ro.id, null))                      本周7天应催订单," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 13) and date_sub(" + time + ", 1) and" +
                "                            rorp.is_deleted = 0 and repay_status = 5, ro.id, null)) 本周7天催回订单," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 44) and date_sub(" + time + ", 15) and" +
                "                          rorp.is_deleted = 0, capital, 0))                         本周30天应催金额," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 44) and date_sub(" + time + ", 15) and" +
                "                          rorp.is_deleted = 0 and repay_status = 5, capital, 0))    本周30天催回金额," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 20) and date_sub(" + time + ", 8) and" +
                "                            rorp.is_deleted = 0, ro.id, null))                      上周7天应催订单," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 20) and date_sub(" + time + ", 8) and" +
                "                            rorp.is_deleted = 0 and repay_status = 5, ro.id, null)) 上周7天催回订单," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 51) and date_sub(" + time + ", 22) and" +
                "                          rorp.is_deleted = 0, capital, 0))                         上周30天应催金额," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 51) and date_sub(" + time + ", 22) and" +
                "                          rorp.is_deleted = 0 and repay_status = 5, capital, 0))    上周30天催回金额," +
                "                   (select time from count_day)                                     time1" +
                "            from rent_order_repayment_plan as rorp" +
                "                     left join rent_order ro on ro.id = rorp.order_id" +
                "            where date(repay_date) between date_sub(" + time + "" +
                "                , 180)" +
                "                and date_sub(" + time + "" +
                "                    , 1)" +
                "              and ro.is_deleted = 0" +
                "              and ro.termination = 1" +
                "              and ro.type = 1" +
                "              and rorp.ds = (select ds from dt)" +
                "              and ro.ds = (select ds from dt)" +
                "        ) c on a.time1 = c.time1;";

        assembleData(ds, sql, type);
    }

    private void getMonthList(String countTime, String time, String ds, Integer type) {

        String sql = "with dt as (" +
                "       select " + ds + " as ds" +
                "   )," +
                "        count_day as (" +
                "            select " + countTime + " as time" +
                "        )" +
                "   select nvl(a.time1,c.time1)                                     count_day," +
                "          nvl(a.应还订单量, 0)                                         repay_cnt," +
                "          nvl(a.未逾期订单, 0)                                         un_overdue_cnt," +
                "          nvl(a.已逾期订单, 0)                                         overdue_cnt," +
                "          nvl(a.全量应还总额1, 0)                                       total_amt," +
                "          nvl(a.未逾期应还总额, 0)                                       un_overdue_repay_amt," +
                "          nvl(a.未逾期实还总额2, 0)                                      un_overdue_real_pay_amt," +
                "          nvl(a.已逾期应还总额, 0)                                       ," +
                "          nvl(a.已逾期实还总额, 0)                                       overdue_real_pay_amt," +
                "          nvl((a.全量应还总额1 - a.售前金额), 0)                            repay_amt," +
                "          nvl((a.全量应还总额xu1 - a.售前金额xu), 0)                        renew_repay_amt," +
                "          nvl((a.应还订单量 + a.应还订单量xu), 0)                           total_repay_cnt," +
                "          nvl((a.未逾期订单 + a.未逾期订单xu + b.逾期催收订单), 0)                total_un_overdue_cnt," +
                "          nvl((a.未逾期实还总额2 + b.已逾期催收总额), 0)                        overdue_total_amt," +
                "          nvl((a.全量应还总额1 - a.售前金额) + (a.全量应还总额xu1 - a.售前金额xu), 0) total_repay_amt," +
                "          nvl((a.未逾期实还总额2 + b.已逾期催收总额 + a.未逾期实还总额xu2), 0)         total_un_overdue_real_pay_amt," +
                "          nvl(a.入催订单, 0)                                          collection_cnt," +
                "          nvl(a.首逾, 0)                                            first_overdue_amt," +
                "          nvl(a.首期订单, 0)                                          first_cnt," +
                "          nvl(a.售前金额, 0)                                          before_amt," +
                "          nvl(a.全量应还总额xu1, 0)                                     renew_whole_repay_amt," +
                "          nvl(a.未逾期实还总额xu2, 0)                                    renew_un_overdue_real_pay_amt," +
                "          nvl(a.应还订单量xu, 0)                                       renew_repay_cnt," +
                "          nvl(a.未逾期订单xu, 0)                                       renew_un_overdue_cnt," +
                "          nvl(a.首逾xu, 0)                                          renew_first_overdue_amt," +
                "          nvl(a.首期订单xu, 0)                                        renew_first_cnt," +
                "          nvl(a.售前金额xu, 0)                                        renew_before_amt," +
                "          nvl(b.已逾期应还总额, 0)                                    overdue_repay_amt," +
                "          nvl(b.逾期催收订单, 0)                                        overdue_collection_cnt," +
                "          nvl(b.已逾期催收总额, 0)                                       overdue_collection_amt," +
                "          nvl(b.7天催回金额, 0)                                        total_day7_return_amt," +
                "          nvl(b.7天催回订单, 0)                                        total_day7_return_cnt," +
                "          nvl(b.30天催回金额, 0)                                       total_day30_return_amt," +
                "          nvl(b.30天催回订单, 0)                                       total_day30_return_cnt," +
                "          nvl(b.M2催回金额, 0)                                        month2_return_amt," +
                "          nvl(b.M2催回订单, 0)                                        month2_return_cnt," +
                "          nvl(b.7天催回金额主, 0)                                       day7_return_amt," +
                "          nvl(b.30天催回金额主, 0)                                      day30_return_amt," +
                "          nvl(b.7天催回金额续, 0)                                       renew_day7_return_amt," +
                "          nvl(b.30天催回金额续, 0)                                      renew_day30_return_amt," +
                "          nvl(b.已逾期订单, 0)," +
                "          nvl(c.本周7天应催订单, 0)                                      this_day7_should_cnt," +
                "          nvl(c.本周7天催回订单, 0)                                      this_day7_return_cnt," +
                "          nvl(c.本周30天应催金额, 0)                                     this_day30_should_amt," +
                "          nvl(c.本周30天催回金额, 0)                                     this_day30_return_amt," +
                "          nvl(c.上周7天应催订单, 0)                                      last_day7_should_cnt," +
                "          nvl(c.上周7天催回订单, 0)                                      last_day7_return_cnt," +
                "          nvl(c.上周30天应催金额, 0)                                     last_day30_should_amt," +
                "          nvl(c.上周30天催回金额, 0)                                     last_day30_return_amt" +
                "   from (" +
                "            select date(repay_date)                                                                             time1," +
                "                   count(distinct if(ro.parent_id = 0, rorp.order_id, null))                                    应还订单量," +
                "                   count(distinct if(overdue = 1 and ro.parent_id = 0, rorp.order_id, null))                    未逾期订单," +
                "                   count(distinct if(overdue = 5, rorp.order_id, null))                                         已逾期订单," +
                "                   sum(if(ro.parent_id = 0, capital, 0))                                                        全量应还总额1," +
                "                   sum(if(overdue = 1, capital, 0))                                                             未逾期应还总额," +
                "                   sum(if(overdue = 1 and ro.parent_id = 0, real_repay_capital, 0))                             未逾期实还总额2," +
                "                   sum(if(overdue = 5, capital, 0))                                                             已逾期应还总额," +
                "                   sum(if(overdue = 5, real_repay_capital, 0))                                                  已逾期实还总额," +
                "                   count(distinct if(overdue = 5 and" +
                "                                     (((date(repay_date)) + 1 < date(real_repay_time)) or (date(real_repay_time) is null))," +
                "                                     rorp.order_id, null))                                                      入催订单," +
                "                   count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))  首逾," +
                "                   count(distinct if(rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))                  首期订单," +
                "                   sum(if(use_status = 1 and ro.parent_id = 0, discount_amount, 0))                             售前金额," +
                "                   sum(if(ro.parent_id != 0, capital, 0))                                                       全量应还总额xu1," +
                "                   sum(if(overdue = 1 and ro.parent_id != 0, real_repay_capital, 0))                            未逾期实还总额xu2," +
                "                   count(distinct if(ro.parent_id != 0, rorp.order_id, null))                                   应还订单量xu," +
                "                   count(distinct if(overdue = 1 and ro.parent_id != 0, rorp.order_id, null))                   未逾期订单xu," +
                "                   count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null)) 首逾xu," +
                "                   count(distinct if(rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null))                 首期订单xu," +
                "                   sum(if(use_status = 1 and ro.parent_id != 0, discount_amount, 0))                            售前金额xu" +
                "   " +
                "            from rent_order_repayment_plan as rorp" +
                "                     left join rent_order ro" +
                "                               on ro.id = rorp.order_id and ro.ds = (select ds from dt)" +
                "                     left join rent_customer_coupon rcc on rcc.order_id = rorp.order_id and rcc.ds = (select ds from dt)" +
                "            where date(repay_date) between date_sub(" + time + "" +
                "                , 180)" +
                "                and date_sub(" + time + "" +
                "                    , 1)" +
                "              and ro.is_deleted = 0" +
                "              and ro.termination = 1" +
                "              and ro.type = 1" +
                "              and rorp.ds = (select ds from dt)" +
                "            group by time1" +
                "            order by time1" +
                "        ) a" +
                "            left join (" +
                "       select a.time1," +
                "              b.已逾期应还总额," +
                "              a.逾期催收订单," +
                "              a.已逾期催收总额," +
                "              a.7天催回金额," +
                "              a.7天催回订单," +
                "              a.30天催回金额," +
                "              a.30天催回订单," +
                "              a.M2催回金额," +
                "              a.M2催回订单," +
                "              a.7天催回金额主," +
                "              a.30天催回金额主," +
                "              a.7天催回金额续," +
                "              a.30天催回金额续," +
                "              b.已逾期订单" +
                "       from (" +
                "                select date(real_repay_time)                                                                        time1," +
                "                       sum(if(repay_status = 5, real_repay_capital, 0))                                             已逾期催收总额," +
                "                       count(if(repay_status = 5, rorp.order_id, null))                                             逾期催收订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7, real_repay_capital, 0)) 7天催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7, rorp.order_id, NULL)) 7天催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30, real_repay_capital," +
                "                              0))                                                                                   30天催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30, rorp.order_id," +
                "                                NULL))                                                                              30天催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') > 30, real_repay_capital, 0)) M2催回金额," +
                "                       count(distinct" +
                "                             if(datediff(date(real_repay_time), date(repay_date), 'dd') > 30, rorp.order_id, NULL)) M2催回订单," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7 and parent_id = 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   7天催回金额主," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30 and parent_id = 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   30天催回金额主," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 7 and parent_id != 0," +
                "                              real_repay_capital," +
                "                              0))                                                                                   7天催回金额续," +
                "                       sum(if(datediff(date(real_repay_time), date(repay_date), 'dd') <= 30 and parent_id != 0," +
                "                              real_repay_capital, 0))                                                               30天催回金额续" +
                "                from rent_order_repayment_plan as rorp" +
                "                         left join rent_order ro" +
                "                                   on ro.id = rorp.order_id" +
                "                where date(real_repay_time) between date_sub(" + time + "" +
                "                    , 180)" +
                "                    and date_sub(" + time + "" +
                "                        , 1)" +
                "                  and ro.is_deleted = 0" +
                "                  and ro.termination = 1" +
                "                  and ro.type = 1" +
                "                  and rorp.ds = (select ds from dt)" +
                "                  and ro.ds = (select ds from dt)" +
                "                  and rorp.overdue = 5" +
                "                group by time1" +
                "                order by time1) a" +
                "                left join (" +
                "           select date(repay_date)                    time1," +
                "                  sum(if(overdue = 5, capital, 0))    已逾期应还总额," +
                "                  count(if(overdue = 5, ro.id, null)) 已逾期订单" +
                "           from rent_order_repayment_plan as rorp" +
                "                    left join rent_order ro" +
                "                              on ro.id = rorp.order_id" +
                "           where date(repay_date) between date_sub(" + time + "" +
                "               , 180)" +
                "               and date_sub(" + time + "" +
                "                   , 1)" +
                "             and ro.is_deleted = 0" +
                "             and ro.termination = 1" +
                "             and ro.type = 1" +
                "             and rorp.is_deleted = 0" +
                "             and rorp.ds = (select ds from dt)" +
                "             and ro.ds = (select ds from dt)" +
                "           group by time1" +
                "           order by time1) b" +
                "                          on a.time1 = b.time1" +
                "       order by time1" +
                "   ) b on a.time1 = b.time1" +
                "            full join" +
                "        (" +
                "            select count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 37) and date_sub(" + time + ", 1) and" +
                "                            rorp.is_deleted = 0, ro.id, null))                      本周7天应催订单," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 37) and date_sub(" + time + ", 1) and" +
                "                            rorp.is_deleted = 0 and repay_status = 5, ro.id, null)) 本周7天催回订单," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 74) and date_sub(" + time + ", 15) and" +
                "                          rorp.is_deleted = 0, capital, 0))                         本周30天应催金额," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 74) and date_sub(" + time + ", 15) and" +
                "                          rorp.is_deleted = 0 and repay_status = 5, capital, 0))    本周30天催回金额," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 20) and date_sub(" + time + ", 8) and" +
                "                            rorp.is_deleted = 0, ro.id, null))                      上周7天应催订单," +
                "                   count(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 20) and date_sub(" + time + ", 8) and" +
                "                            rorp.is_deleted = 0 and repay_status = 5, ro.id, null)) 上周7天催回订单," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 51) and date_sub(" + time + ", 22) and" +
                "                          rorp.is_deleted = 0, capital, 0))                         上周30天应催金额," +
                "                   sum(if(overdue = 5 and date(repay_date) between date_sub(" + time + ", 51) and date_sub(" + time + ", 22) and" +
                "                          rorp.is_deleted = 0 and repay_status = 5, capital, 0))    上周30天催回金额," +
                "                   (select time from count_day)                                     time1" +
                "            from rent_order_repayment_plan as rorp" +
                "                     left join rent_order ro on ro.id = rorp.order_id" +
                "            where date(repay_date) between date_sub(" + time + "" +
                "                , 180)" +
                "                and date_sub(" + time + "" +
                "                    , 1)" +
                "              and ro.is_deleted = 0" +
                "              and ro.termination = 1" +
                "              and ro.type = 1" +
                "              and rorp.ds = (select ds from dt)" +
                "              and ro.ds = (select ds from dt)" +
                "        ) c on a.time1 = c.time1;";

        assembleData(ds, sql, type);
    }

    private void assembleData(String ds, String sql, Integer type) {
        List<Record> records = odpsUtil.querySql(sql);
        List<WholeLawReportDO> collect = records.stream().map((record) -> {
            LocalDate countDay = LocalDate.parse(record.getString("count_day"), YYYY_MM_DD);
            WholeLawReportDO rentReportDO = new WholeLawReportDO();
            rentReportDO.setCountDay(countDay);
            rentReportDO.setDs(ds);
            rentReportDO.setType(type);
            rentReportDO.setRepayCnt(Integer.valueOf(record.getString("repay_cnt")));
            rentReportDO.setUnOverdueCnt(Integer.valueOf(record.getString("un_overdue_cnt")));
            rentReportDO.setOverdueCnt(Integer.valueOf(record.getString("overdue_cnt")));
            rentReportDO.setTotalAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("total_amt"))));
            rentReportDO.setUnOverdueRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("un_overdue_repay_amt"))));
            rentReportDO.setUnOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("un_overdue_real_pay_amt"))));
            rentReportDO.setOverdueRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("overdue_repay_amt"))));
            rentReportDO.setOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("overdue_real_pay_amt"))));
            rentReportDO.setCollectionCnt(Integer.valueOf(record.getString("collection_cnt")));
            rentReportDO.setFirstOverdueCnt(Integer.valueOf(record.getString("first_overdue_amt")));
            rentReportDO.setFirstCnt(Integer.valueOf(record.getString("first_cnt")));
            rentReportDO.setBeforeAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("before_amt"))));
            rentReportDO.setRenewWholeRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_whole_repay_amt"))));
            rentReportDO.setRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("repay_amt"))));
            rentReportDO.setRenewRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_repay_amt"))));
            rentReportDO.setOverdueTotalAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("overdue_total_amt"))));
            rentReportDO.setTotalRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("total_repay_amt"))));
            rentReportDO.setTotalUnOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("total_un_overdue_real_pay_amt"))));
            rentReportDO.setRenewUnOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_un_overdue_real_pay_amt"))));
            rentReportDO.setRenewRepayCnt(Integer.valueOf(record.getString("renew_repay_cnt")));
            rentReportDO.setTotalRepayCnt(Integer.valueOf(record.getString("total_repay_cnt")));
            rentReportDO.setTotalUnOverdueCnt(Integer.valueOf(record.getString("total_un_overdue_cnt")));

            rentReportDO.setRenewUnOverdueCnt(Integer.valueOf(record.getString("renew_un_overdue_cnt")));
            rentReportDO.setRenewFirstOverdueCnt(Integer.valueOf(record.getString("renew_first_overdue_amt")));
            rentReportDO.setRenewFirstCnt(Integer.valueOf(record.getString("renew_first_cnt")));
            rentReportDO.setRenewBeforeAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_before_amt"))));
            rentReportDO.setOverdueCollectionCnt(Integer.valueOf(record.getString("overdue_collection_cnt")));
            rentReportDO.setOverdueCollectionAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("overdue_collection_amt"))));
            rentReportDO.setTotalDay7ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("total_day7_return_amt"))));
            rentReportDO.setTotalDay7ReturnCnt(Integer.valueOf(record.getString("total_day7_return_cnt")));
            rentReportDO.setTotalDay30ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("total_day30_return_amt"))));
            rentReportDO.setTotalDay30ReturnCnt(Integer.valueOf(record.getString("total_day30_return_cnt")));
            rentReportDO.setMonth2ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("month2_return_amt"))));
            rentReportDO.setMonth2ReturnCnt(Integer.valueOf(record.getString("month2_return_cnt")));
            rentReportDO.setDay7ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("day7_return_amt"))));
            rentReportDO.setDay30ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("day30_return_amt"))));
            rentReportDO.setRenewDay7ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_day7_return_amt"))));
            rentReportDO.setRenewDay30ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_day30_return_amt"))));
            rentReportDO.setThisDay7ShouldCnt(Integer.valueOf(record.getString("this_day7_should_cnt")));
            rentReportDO.setThisDay7ReturnCnt(Integer.valueOf(record.getString("this_day7_return_cnt")));
            rentReportDO.setThisDay30ShouldAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "this_day30_should_amt"))));
            rentReportDO.setThisDay30ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("this_day30_return_amt"))));
            rentReportDO.setLastDay7ShouldCnt(Integer.valueOf(record.getString("last_day7_should_cnt")));
            rentReportDO.setLastDay7ReturnCnt(Integer.valueOf(record.getString("last_day7_return_cnt")));
            rentReportDO.setLastDay30ShouldAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("last_day30_should_amt"))));
            rentReportDO.setLastDay30ReturnAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("last_day30_return_amt"))));

            return rentReportDO;
        }).collect(Collectors.toList());
        wholeLawReportService.saveBatch(collect);
    }


}



