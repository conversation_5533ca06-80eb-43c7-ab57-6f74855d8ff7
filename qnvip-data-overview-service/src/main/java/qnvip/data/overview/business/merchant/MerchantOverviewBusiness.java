package qnvip.data.overview.business.merchant;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.merchant.OperateMerchantOverviewDO;
import qnvip.data.overview.service.merchant.OperateMerchantOverviewService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantOverviewBusiness {

    private final OdpsUtil odpsUtil;
    private final OperateMerchantOverviewService merchantOverviewService;
    private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public void execute() {

        String body = getBody();
        String countSql = "select count(*) num from (" + body + ") ;";

        Integer size = getCount(countSql);
        log.info("{}, 总数={}", "MerchantOverviewBusiness", size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<OperateMerchantOverviewDO> collect = new ArrayList<>();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql =  body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            List<OperateMerchantOverviewDO> list = records.stream().map(record -> {
                OperateMerchantOverviewDO operateMerchantOverviewDO = new OperateMerchantOverviewDO();
                operateMerchantOverviewDO.setCountDay(LocalDate.parse(record.getString("count_day"), YYYY_MM_DD).atStartOfDay());
                operateMerchantOverviewDO.setShopName(record.getString("shop_name"));
                operateMerchantOverviewDO.setOrderConcat(record.getString("order_concat"));
                operateMerchantOverviewDO.setMerchantId(Long.valueOf(record.getString("merchant_id")));
                operateMerchantOverviewDO.setGmv(stringToDecimal(record.getString("gmv")));
                operateMerchantOverviewDO.setPassCnt(Integer.valueOf(record.getString("pass_cnt")));
                operateMerchantOverviewDO.setPayCnt(Integer.valueOf(record.getString("pay_cnt")));
                operateMerchantOverviewDO.setSendCnt(Integer.valueOf(record.getString("send_cnt")));
                operateMerchantOverviewDO.setPayAmt(stringToDecimal(record.getString("pay_amt")));
                operateMerchantOverviewDO.setPayBondAmt(stringToDecimal(record.getString("pay_bond_amt")));
                operateMerchantOverviewDO.setPayBuyoutAmt(stringToDecimal(record.getString("pay_buyout_amt")));
                operateMerchantOverviewDO.setPayRentAmt(stringToDecimal(record.getString("pay_rent_amt")));
                operateMerchantOverviewDO.setDealCnt(Integer.valueOf(record.getString("deal_cnt")));
                operateMerchantOverviewDO.setFinishCnt(Integer.valueOf(record.getString("finish_cnt")));
                operateMerchantOverviewDO.setRentAmt(stringToDecimal(record.getString("rent_amt")));
                operateMerchantOverviewDO.setRepayAlreadyPay(stringToDecimal(record.getString("repay_already_pay")));
                operateMerchantOverviewDO.setOverdueAmt(stringToDecimal(record.getString("overdue_amt")));
                operateMerchantOverviewDO.setOverdueCnt(Integer.valueOf(record.getString("overdue_cnt")));
                operateMerchantOverviewDO.setRentCnt(Integer.valueOf(record.getString("rent_cnt")));
                operateMerchantOverviewDO.setPayAfterRent(stringToDecimal(record.getString("pay_after_rent")));
                operateMerchantOverviewDO.setPayAfterRent(stringToDecimal(record.getString("pay_after_rent")));
                return operateMerchantOverviewDO;
            }).collect(Collectors.toList());
            collect.addAll(list);
        }
        merchantOverviewService.removeAll();
        merchantOverviewService.saveBatch(collect);
    }
    private Integer getCount(String sql) {
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    private String getBody() {
        return  "\n" +
                "   with dt as (select date_format(now(), 'yyyymmdd') as ds)\n" +
                "   select a.count_day,\n" +
                "          trim(a.shop_name)                                                     shop_name,\n" +
                "          a.merchant_id                                                         merchant_id,\n" +
                "          nvl(z.gmv, 0)                                                         gmv,\n" +
                "          nvl(b.successOrderCount, 0)                                           pass_cnt,\n" +
                "          nvl(c.payOrderCount, 0)                                               pay_cnt,\n" +
                "          nvl(d.sendOrderCount, 0)                                              send_cnt,\n" +
                "          nvl(e.pay_amt, 0)                                                     pay_amt,\n" +
                "          nvl(f.bond_amt, 0)                                                    pay_bond_amt,\n" +
                "          nvl(g.buyout_amt, 0)                                                  pay_buyout_amt,\n" +
                "          nvl(h.pay_rent, 0)                                                    pay_rent_amt,\n" +
                "          nvl(i.signOrderCount, 0)                                              deal_cnt,\n" +
                "          nvl(j.buyoutCount, 0) + nvl(k.renewlCount, 0) + nvl(l.returnCount, 0) finish_cnt,\n" +
                "          nvl(m.rent_amt, 0)                                                    rent_amt,\n" +
                "          nvl(m.repay_already_pay, 0)                                           repay_already_pay,\n" +
                "          nvl(n.order_cnt, 0)                                                   overdue_cnt,\n" +
                "          nvl(o.order_cnt, 0)                                                   rent_cnt,\n" +
                "          nvl(o.order_concat, '')                                               order_concat,\n" +
                "          nvl(p.pay_amt, 0)                                                     pay_after_rent,\n" +
                "          nvl(q.overdueRent, 0)    + nvl(r.amt,0)                                 overdue_amt  " +
                "   from (\n" +
                "             select shop_name, merchant_id, date (count_day) count_day\n" +
                "         from dataview_operate_merchant_index\n" +
                "         where  ds = (select ds from dt)" +
                "        ) a\n" +
                "            left join\n" +
                "   \n" +
                "        (select a.shop_name, a.count_day, merchant_id, sum(gmv) gmv\n" +
                "         from (select date(sign_time)                                                                               count_day,\n" +
                "                      rm.shop_name,\n" +
                "                      o.merchant_id,\n" +
                "                      sum(distinct d.rent_total) + if(min(o.renew_time) is not null, min(d.renew_total_rent),\n" +
                "                                         sum(distinct if(d.rate_config_type not in (10), d.buyout_amt, 0)))    as gmv\n" +
                "               FROM rent_order o\n" +
                "                        LEFT JOIN rent_order_finance_detail d\n" +
                "                                  ON o.id = d.order_id and d.ds = (select ds from dt)\n" +
                "                        LEFT JOIN rent_order_repayment_plan p ON p.order_id = o.id and p.ds = (select ds from dt) and p.is_deleted = 0\n" +
                "                        LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.ds = (select ds from dt) and rol.is_deleted = 0 and rol.type = 0 \n" +
                "                        LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "               WHERE o.merchant_id != 100\n" +
                "                 and rol.sign_time is not null\n" +
                "                 and o.type = 1\n" +
                "                 and o.ds = (select ds from dt)\n" +
                "                 and o.is_deleted = 0\n" +
                "                 and o.termination <> 5\n" +
                "               group by o.id, rm.shop_name, o.merchant_id, date(sign_time)) a\n" +
                "         group by a.shop_name, a.count_day, a.merchant_id) z\n" +
                "        on a.shop_name = z.shop_name and a.count_day = z.count_day and a.merchant_id = z.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(operate_time)                               count_day,\n" +
                "                              count(distinct if(l.status = 10, l.order_id, 0)) successOrderCount\n" +
                "                       FROM rent_order_merchant_transfer_log l\n" +
                "                                LEFT JOIN rent_order o\n" +
                "                                          ON l.order_id = o.id and l.status = 10 and o.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE l.is_deleted = 0\n" +
                "                         and o.is_deleted = 0\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, date(operate_time), o.merchant_id) b\n" +
                "                      on a.shop_name = b.shop_name and a.count_day = b.count_day and a.merchant_id = b.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(payment_time)                                                       count_day,\n" +
                "                              count(distinct if(o.payment_time is not null and refund_time is null," +
                " o.id, 0)) payOrderCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_flow f ON f.order_id = o.id and flow_type = 1 " +
                " and f.biz_type = 4 and parent_uid <> '' and \n" +
                "                                                               f.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and payment_time is not null\n" +
                "                       group by rm.shop_name, date(payment_time), o.merchant_id\n" +
                "   ) c\n" +
                "                      on a.shop_name = c.shop_name and a.count_day = c.count_day and a.merchant_id = c.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(send_time)                                        count_day,\n" +
                "                              count(distinct if(rol.send_time is not null, o.id, 0)) sendOrderCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.type = 0  and rol.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n" +
                "                       group by rm.shop_name, date(send_time), o.merchant_id\n" +
                "   ) d on a.shop_name = d.shop_name and a.count_day = d.count_day and a.merchant_id = d.merchant_id\n" +
                "            left join (SELECT rm.shop_name,\n" +
                "                              l.merchant_id,\n" +
                "                              date(flow_time)                      count_day,\n" +
                "                              nvl(sum(flow_amt - refunded_amt), 0) pay_amt\n" +
                "                       FROM rent_order_flow l\n" +
                "                                LEFT JOIN rent_merchant rm ON l.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE l.is_deleted = 0\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                         AND (merchant_id <> 100 AND merchant_id > 0 AND flow_amt > refunded_amt AND flow_type = 1 AND\n" +
                "                              pay_status = 10 AND merchant_item_type = 2 AND  parent_uid='' AND \n" +
                "                              biz_type IN (1, 2, 3, 4, 5, 6, 7, 8, 10, 16, 22, 24, 25, 27, 28) AND\n" +
                "                              ((biz_type in (3, 4) and parent_uid = '') or (biz_type not in (3, 4))))\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, date(flow_time), l.merchant_id\n" +
                "   ) e on a.shop_name = e.shop_name and a.count_day = e.count_day and a.merchant_id = e.merchant_id\n" +
                "            left join (SELECT rm.shop_name,\n" +
                "                              date(flow_time)                      count_day,\n" +
                "                              l.merchant_id,\n" +
                "                              nvl(sum(flow_amt - refunded_amt), 0) bond_amt\n" +
                "                       FROM rent_order_flow l\n" +
                "                                LEFT JOIN rent_merchant rm ON l.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE l.is_deleted = 0\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                         AND (merchant_id <> 100 AND merchant_id > 0 AND flow_amt > refunded_amt AND flow_type = 1 AND\n" +
                "                              pay_status = 10 AND merchant_item_type = 2 AND biz_type IN (4) AND parent_uid <> '')\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, l.merchant_id,\n" +
                "                                date(flow_time)) f\n" +
                "                      on a.shop_name = f.shop_name and a.count_day = f.count_day and a.merchant_id = f.merchant_id\n" +
                "            left join (SELECT rm.shop_name,\n" +
                "                              l.merchant_id,\n" +
                "                              date(flow_time)                      count_day,\n" +
                "                              nvl(sum(flow_amt - refunded_amt), 0) buyout_amt\n" +
                "                       FROM rent_order_flow l\n" +
                "                                LEFT JOIN rent_merchant rm ON l.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE l.is_deleted = 0\n" +
                "                         AND (merchant_id <> 100 AND merchant_id > 0 AND flow_amt > refunded_amt AND flow_type = 1 AND\n" +
                "                              pay_status = 10 AND merchant_item_type = 2 AND biz_type IN (3) AND " +
                "                               parent_uid <> '')\n" +
                "                         and l.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, l.merchant_id,\n" +
                "                                date(flow_time)) g\n" +
                "                      on a.shop_name = g.shop_name and a.count_day = g.count_day and a.merchant_id = g.merchant_id\n" +
                "            left join ( " +
                "                          select date(real_repay_time)                       count_day,\n" +
                "                              shop_name,\n" +
                "                              ro.merchant_id,\n" +
                "                              sum(real_repay_capital) pay_rent\n" +
                "                       from rent_order ro\n" +
                "                                inner join rent_order_repayment_plan rp\n" +
                "                                           on ro.id = rp.order_id and rp.ds = (select ds from dt) and rp.is_deleted = 0\n" +
                "                                inner join rent_merchant rm on ro.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       where ro.merchant_id <> 100\n" +
                "                         and ro.merchant_id> 0\n" +
                "                         and ro.ds = (select ds from dt)\n" +
                "                       group by date(real_repay_time), shop_name, ro.merchant_id" +
                "                   ) h\n" +
                "                      on a.shop_name = h.shop_name and a.count_day = h.count_day and a.merchant_id = h.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(sign_time)                                                           count_day,\n" +
                "                              count(distinct if(rol.sign_time is not null and o.status != 30, o.id, 0)) signOrderCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_finance_detail d ON o.id = d.order_id and d.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.type = 0  and rol.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(sign_time)) i\n" +
                "                      on a.shop_name = i.shop_name and a.count_day = i.count_day and a.merchant_id = i.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(roi.buyout_time)                           count_day,\n" +
                "                              count(distinct if(o.status = 220, o.id, 0)) buyoutCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_infomore roi ON o.id = roi.order_id and roi.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(roi.buyout_time)) j\n" +
                "                      on a.shop_name = j.shop_name and a.count_day = j.count_day and a.merchant_id = j.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(o2.settle_date)                        count_day,\n" +
                "                              count(distinct if(o.status = 330, o.id, 0)) renewlCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order o2\n" +
                "                                          ON o.id = o2.parent_id and o2.is_deleted = 0 and o2.status = 330 and\n" +
                "                                             o.status = 320 and o.is_deleted = 0 and o2.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(o2.settle_date)) k\n" +
                "                      on a.shop_name = k.shop_name and a.count_day = k.count_day and a.merchant_id = k.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(roi.return_succ_time)                  count_day,\n" +
                "                              count(distinct if(o.status = 210, o.id, 0)) returnCount\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_infomore roi\n" +
                "                                          ON o.id = roi.order_id and o.status = 210 and roi.ds = (select ds from dt)\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(roi.return_succ_time)) l\n" +
                "                      on a.shop_name = l.shop_name and a.count_day = l.count_day and a.merchant_id = l.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(p.repay_date)     count_day,\n" +
                "                              nvl(sum(p.capital), 0) rent_amt,\n" +
                "                              nvl( sum(real_repay_capital) ,0) repay_already_pay\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_repayment_plan p ON p.order_id = o.id and p.ds = (select ds from dt)  and p.is_deleted = 0\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <>5\n\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(p.repay_date)) m\n" +
                "                      on a.shop_name = m.shop_name and a.count_day = m.count_day and a.merchant_id = m.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(p.repay_date)   count_day,\n" +
                "                              count(distinct o.id) order_cnt\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_repayment_plan p\n" +
                "                                          ON p.order_id = o.id and p.overdue = 5 and p.repay_status = 1 and\n" +
                "                                             p.ds = (select ds from dt)    and p.is_deleted = 0\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.type = 1\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(p.repay_date)) n\n" +
                "                      on a.shop_name = n.shop_name and a.count_day = n.count_day and a.merchant_id = n.merchant_id\n" +
                "            left join (select rm.shop_name,\n" +
                "                              o.merchant_id,\n" +
                "                              date(p.repay_date)   count_day,\n" +
                "                              count(distinct o.id) order_cnt,\n" +
                "                               wm_concat(',',o.id) order_concat\n" +
                "                       FROM rent_order o\n" +
                "                                LEFT JOIN rent_order_repayment_plan p\n" +
                "                                          ON p.order_id = o.id  and\n" +
                "                                             p.ds = (select ds from dt) and p.is_deleted=0\n" +
                "                                LEFT JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "                       WHERE o.is_deleted = 0\n" +
                "                         and o.merchant_id <> 100\n" +
                "                         and o.type = 1\n" +
                "                         and o.termination <> 5\n" +
                "                         and o.status = 15\n" +
                "                         and o.ds = (select ds from dt)\n" +
                "                       group by rm.shop_name, o.merchant_id,\n" +
                "                                date(p.repay_date)) o\n" +
                "                      on a.shop_name = o.shop_name and a.count_day = o.count_day and a.merchant_id = o.merchant_id\n" +
                "            left join (\n" +
                "       select a.shop_name, a.count_day, a.merchant_id, sum(a.amt) pay_amt\n" +
                "       FROM (select rm.shop_name,\n" +
                "                    o.merchant_id,\n" +
                "                    date(p.repay_date)                                               count_day,\n" +
                "                    (nvl(sum(p.capital), 0) + sum (distinct if(date(rent_end_date)= date(p.repay_date),d.current_buyout_amt,0))) as amt\n" +
                "             FROM rent_order o\n" +
                "                      INNER JOIN rent_order_finance_detail d\n" +
                "                                 ON d.order_id = o.id and o.is_deleted = 0 and d.ds = (select ds from dt)\n" +
                "                      INNER JOIN rent_order_repayment_plan p\n" +
                "                                 ON p.order_id = o.id  and\n" +
                "                                    p.ds = (select ds from dt)  and p.is_deleted = 0\n" +
                "                      INNER JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "             WHERE o.is_deleted = 0\n" +
                "               and o.merchant_id <> 100\n" +
                "               and o.type = 1\n" +
                "               and o.termination <>5\n" +
                "               and o.ds = (select ds from dt)\n" +
                "             group by o.id, rm.shop_name, o.merchant_id, date(p.repay_date)) a\n" +
                "       group by a.shop_name, a.merchant_id, a.count_day\n" +
                "   ) p on a.shop_name = p.shop_name and a.count_day = p.count_day and a.merchant_id = p.merchant_id\n" +
                "            left join (\n" +
                "       select shop_name, merchant_id, count_day, sum(amt) overdueRent\n" +
                "       from (select rm.shop_name,\n" +
                "                    o.merchant_id,\n" +
                "                    date(p.repay_date)                                                  count_day,\n" +
                "                    nvl(sum(p.capital), 0) amt\n" +
                "             FROM rent_order o\n" +
                "                      INNER JOIN rent_order_repayment_plan p\n" +
                "                                 ON p.order_id = o.id and p.overdue = 5 and p.repay_status = 1 and\n" +
                "                                    p.ds = (select ds from dt)  and p.is_deleted = 0\n" +
                "                      INNER JOIN rent_order_finance_detail f\n" +
                "                                 ON f.order_id = o.id and f.is_deleted = 0 and f.ds = (select ds from dt)\n" +
                "                      INNER JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds = (select ds from dt)\n" +
                "             WHERE o.is_deleted = 0\n" +
                "               and o.merchant_id <> 100\n" +
                "               and o.type = 1\n" +
                "               and o.ds = (select ds from dt)\n" +
                "             group by rm.shop_name, o.merchant_id, date(p.repay_date)) a\n" +
                "       group by shop_name, merchant_id, count_day\n" +
                "   ) q on a.shop_name = q.shop_name and a.count_day = q.count_day and a.merchant_id = q.merchant_id\n" +
                "          left join ( select count_day, shop_name, merchant_id, sum(amt) amt\n" +
                "       from (select o.id, date (rent_end_date) count_day,\n" +
                "            shop_name,\n" +
                "            merchant_id,\n" +
                "            sum(distinct nvl(f.current_buyout_amt, 0)) amt from rent_order o\n" +
                "                INNER JOIN rent_order_finance_detail f\n" +
                "       ON f.order_id = o.id and f.is_deleted = 0 and f.ds=(select ds from dt)\n" +
                "           INNER JOIN rent_merchant rm ON o.merchant_id = rm.id and rm.ds=(select ds from dt)\n" +
                "       WHERE o.is_deleted = 0\n" +
                "         and o.merchant_id <> 100\n" +
                "         and o.type = 1\n" +
                "         and o.termination <> 5\n" +
                "         and o.ds=(select ds from dt)\n" +
                "         and if(settle_date is null,getdate(),settle_date) > rent_end_date\n" +
                "       group by o.id, shop_name, merchant_id, date (rent_end_date)) a\n" +
                "       group by a.count_day, shop_name, merchant_id\n" +
                "       ) r on a.shop_name = r.shop_name and a.count_day = r.count_day and a.merchant_id = r.merchant_id "+
                " order by count_day desc  ";
    }

    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }

}



