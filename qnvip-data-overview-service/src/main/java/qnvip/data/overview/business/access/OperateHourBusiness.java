package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperateHourDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.access.OperateHourService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateHourBusiness {

    private final static DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH");

    private final OdpsUtil odpsUtil;

    private final OperateHourService hourService;

    void initMap(Map<Integer, Map<LocalDateTime, OperateHourDO>> miniType2Map, OperateHourDO hourDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<LocalDateTime, OperateHourDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(hourDO.getMiniType())) {
            OperateHourDO domain = new OperateHourDO();
            domain.setCountDay(countDay);
            domain.setCountTime(hourDO.getCountTime());
            domain.setMiniType(hourDO.getMiniType());
            merchantId2Do.put(hourDO.getCountTime(), domain);
        } else {
            merchantId2Do = miniType2Map.get(hourDO.getMiniType());
            if (!merchantId2Do.containsKey(hourDO.getCountTime())) {
                OperateHourDO domain = new OperateHourDO();
                domain.setCountDay(countDay);
                domain.setMiniType(hourDO.getMiniType());
                domain.setCountTime(hourDO.getCountTime());
                merchantId2Do.put(hourDO.getCountTime(), domain);
            }
        }
        miniType2Map.put(hourDO.getMiniType(), merchantId2Do);

    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<Integer, Map<LocalDateTime, OperateHourDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateHourDO>> f1 = CompletableFuture.supplyAsync(() -> {
            return getPvAndUvByMiniType(ds);
        });
        CompletableFuture<List<OperateHourDO>> f2 = CompletableFuture.supplyAsync(() -> {
            return getOrderUvByMiniType(ds);
        });
        CompletableFuture<List<OperateHourDO>> f3 = CompletableFuture.supplyAsync(() -> getUvQualityByMiniType(ds));

        // 计算全站数据
        CompletableFuture<List<OperateHourDO>> f4 = CompletableFuture.supplyAsync(() -> getPvAndUv(ds));
        CompletableFuture<List<OperateHourDO>> f5 = CompletableFuture.supplyAsync(() -> getOrderUv(ds));
        CompletableFuture<List<OperateHourDO>> f6 = CompletableFuture.supplyAsync(() -> getUvQuality(ds));

        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6).join();
        try {
            List<OperateHourDO> pvAndUv = f1.get();
            List<OperateHourDO> orderUv = f2.get();
            List<OperateHourDO> uvQuality = f3.get();
            parsePvAndUv(miniType2Map, pvAndUv);
            paeseOrderUv(miniType2Map, orderUv);
            parseUvQuality(miniType2Map, uvQuality);

            // 计算全站数据
            parsePvAndUv(miniType2Map, f4.get());
            paeseOrderUv(miniType2Map, f5.get());
            parseUvQuality(miniType2Map, f6.get());

            LinkedList<OperateHourDO> list = Lists.newLinkedList();
            for (Map.Entry<Integer, Map<LocalDateTime, OperateHourDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<LocalDateTime, OperateHourDO> time2Do : entry.getValue().entrySet()) {
                    OperateHourDO value = time2Do.getValue();
                    list.add(value);
                }
            }

            hourService.removeDataByTime(countDay);
            hourService.saveBatch(list);

        } catch (Exception e) {
            log.error("OperateHourBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void parseUvQuality(Map<Integer, Map<LocalDateTime, OperateHourDO>> miniType2Map, List<OperateHourDO> uvQuality) {
        for (OperateHourDO coreDO : uvQuality) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateHourDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateHourDO hourService = date2DO.get(coreDO.getCountTime());
            hourService.setUvQuality(coreDO.getUvQuality());
        }
    }

    private void paeseOrderUv(Map<Integer, Map<LocalDateTime, OperateHourDO>> miniType2Map, List<OperateHourDO> orderUv) {
        for (OperateHourDO coreDO : orderUv) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateHourDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateHourDO hourService = date2DO.get(coreDO.getCountTime());
            hourService.setOrderUv(coreDO.getOrderUv());
        }
    }

    private void parsePvAndUv(Map<Integer, Map<LocalDateTime, OperateHourDO>> miniType2Map, List<OperateHourDO> pvAndUv) {
        for (OperateHourDO coreDO : pvAndUv) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateHourDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateHourDO operateHourDO = date2DO.get(coreDO.getCountTime());
            operateHourDO.setUv(coreDO.getUv());
            operateHourDO.setPv(coreDO.getPv());
        }
    }

    /**
     * 全站PV和UV
     */
    private List<OperateHourDO> getPvAndUvByMiniType(String ds) {
        String sql = "select  to_char(report_time, 'yyyy-mm-dd hh') as time" +
                "        ,count(distinct customer_third_id) uv" +
                "        ,count(customer_third_id) pv" +
                "        ,mini_type" +
                " from    dataview_track_enter_applets" +
                " where   action_type = 1" +
                " and     ds = " + ds +
                " and     report_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by mini_type" +
                "         ,to_char(report_time, 'yyyy-mm-dd hh')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String time = record.getString("time");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 全站PV和UV
     */
    private List<OperateHourDO> getPvAndUv(String ds) {
        String sql = "select  to_char(report_time, 'yyyy-mm-dd hh') as time" +
                "        ,count(distinct customer_third_id) uv" +
                "        ,count(customer_third_id) pv" +
                " from    dataview_track_enter_applets" +
                " where   action_type = 1" +
                " and     ds = " + ds +
                " and     report_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by to_char(report_time, 'yyyy-mm-dd hh')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String time = record.getString("time");

            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 高质量UV
     */
    private List<OperateHourDO> getUvQualityByMiniType(String ds) {
        String sql = "select a.time," +
                "       mini_type," +
                "       count(distinct customer_third_id ) uv_quality" +
                " from (select to_char(report_time, 'yyyy-mm-dd hh') as time," +
                "        customer_third_id," +
                "         mini_type" +
                " from dataview_track_enter_applets" +
                " where action_type = 2" +
                "  and ds = " + ds +
                "   and report_time between ${fromTime}and ${toTime}" +
                " group by mini_type, customer_third_id , to_char(report_time, 'yyyy-mm-dd hh')" +
                " having sum (keep_alive_time) >= 67000 ) a" +
                " group by a.time, mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String time = record.getString("time");
            String uv = record.getString("uv_quality");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvQuality(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 高质量UV
     */
    private List<OperateHourDO> getUvQuality(String ds) {
        String sql = "select a.time," +
                "       count(distinct customer_third_id ) uv_quality" +
                " from (select to_char(report_time, 'yyyy-mm-dd hh') as time," +
                "        customer_third_id" +
                " from dataview_track_enter_applets" +
                " where action_type = 2" +
                "  and ds = " + ds +
                "   and report_time between ${fromTime}and ${toTime}" +
                " group by  customer_third_id , to_char(report_time, 'yyyy-mm-dd hh')" +
                " having sum (keep_alive_time) >= 67000 ) a" +
                " group by a.time";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String time = record.getString("time");
            String uv = record.getString("uv_quality");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvQuality(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单用户
     */
    private List<OperateHourDO> getOrderUvByMiniType(String ds) {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh') as time" +
                "        ,count(distinct customer_id) order_uv" +
                "        ,mini_type" +
                " from    rent_order" +
                " where   is_deleted = 0" +
                " and     parent_id = 0" +
                " and     type = 1" +
                " and merchant_id =100" +
                " and     ds = " + ds +
                " and     create_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by mini_type" +
                "         ,to_char(create_time, 'yyyy-mm-dd hh')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String miniType = record.getString("mini_type");
            String time = record.getString("time");
            String uv = record.getString("order_uv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单用户
     */
    private List<OperateHourDO> getOrderUv(String ds) {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh') as time" +
                "        ,count(distinct customer_id) order_uv" +
                " from    rent_order" +
                " where   is_deleted = 0" +
                " and     parent_id = 0" +
                " and     type = 1" +
                " and     ds = " + ds +
                " and     create_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by to_char(create_time, 'yyyy-mm-dd hh')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateHourDO domain = new OperateHourDO();
            String time = record.getString("time");
            String uv = record.getString("order_uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

}
