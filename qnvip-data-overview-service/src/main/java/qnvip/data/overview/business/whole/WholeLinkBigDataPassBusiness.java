package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkBigdataPassDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkBigdataPassService;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkBigDataPassBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkBigdataPassService wholeLinkBigdataPassService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkBigDataPassBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkBigdataPassDO> miniType2Map, WholeLinkBigdataPassDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkBigdataPassDO ac = new WholeLinkBigdataPassDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkBigdataPassDO> miniType2Map = new HashMap<>();
            CompletableFuture<List<WholeLinkBigdataPassDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getRiskStrategy(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getRiskUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getRiskUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f9 =
                    CompletableFuture.supplyAsync(() -> getNotPayByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBigdataPassDO>> f10 =
                    CompletableFuture.supplyAsync(() -> getNotPay(ds, hour), threadPoolExecutor);


            CompletableFuture.allOf(f3, f4, f5, f6, f7, f8, f9, f10).join();
            assembleValue(miniType2Map, f1.get(), "strategyARiskUv", "strategyAPassUv", "strategyARefuseUv",
                    "strategyBRiskUv", "strategyBPassUv", "strategyBRefuseUv", "strategyCRiskUv", "strategyCPassUv",
                    "strategyCRefuseUv");
            assembleValue(miniType2Map, f3.get(), "riskPassUv", "riskRefuseUv", "riskUv", "aliUv", "wechatUv", "otherUv", "headUv", "waistUv", "trailUv");
            assembleValue(miniType2Map, f4.get(), "riskPassUv", "riskRefuseUv", "riskUv", "aliUv", "wechatUv", "otherUv", "headUv", "waistUv", "trailUv");
            assembleValue(miniType2Map, f5.get(), "top5ChannelUv");
            assembleValue(miniType2Map, f6.get(), "top5ChannelUv");
            assembleValue(miniType2Map, f7.get(), "top5SceneUv");
            assembleValue(miniType2Map, f8.get(), "top5SceneUv");
            assembleValue(miniType2Map, f9.get(), "notPayCloseUv");
            assembleValue(miniType2Map, f10.get(), "notPayCloseUv");
            List<WholeLinkBigdataPassDO> list = Lists.newArrayList(miniType2Map.values());
            LocalDate now = LocalDate.now();
            wholeLinkBigdataPassService.removeByHour(hour, now);
            wholeLinkBigdataPassService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkBigdataPassDO> miniType2Map,
                               List<WholeLinkBigdataPassDO> list,
                               String... fields) throws Exception {

        for (WholeLinkBigdataPassDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkBigdataPassDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 获取风控用户数
     */
    private List<WholeLinkBigdataPassDO> getRiskUvByMiniType(String ds, Integer hour) {
        String sql = "select ro.mini_type,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 then idcardNo else null end)) risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code = 'ALIPAY' " +
                "   then idcardNo else null end)) ali_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code = 'WECHAT' " +
                "   then idcardNo else null end)) wechat_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code not in ('WECHAT', 'ALIPAY') " +
                "   then idcardNo else null end)) other_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 15 then idcardNo else null end)) risk_refuse_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus < 10 then idcardNo else null end)) risk_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion = '风控等级1.0' then idcardNo\n" +
                "                              else null end))                                                   head_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion > '风控等级1.0' and\n" +
                "                                   cl.riskOpinion < '风控等级9999.0' then idcardNo\n" +
                "                              else null end))                                                   waist_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion = '风控等级9999.0' then idcardNo\n" +
                "                              else null end))                                                   trail_uv\n" +
                "   from cl_loan cl\n" +
                "            left join rc_user ru on cl.`customerId` = ru.id\n" +
                "            left join serial_no sn on sn.businessNo = cl.loanNo\n" +
                "            left join rent_order ro on ro.no = cl.loanNo\n" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)\n" +
                "     and cl.parentNo = ''\n" +
                "     and cl.ds = \n" + ds +
                "     and ru.ds = \n" + ds +
                "     and sn.ds = \n" + ds +
                "     and ro.ds = \n" + ds +
                "     and date (to_date(sn.rhtime\n" +
                "       , 'yyyy-MM-dd HH:mi:ss')) = date (getdate())\n" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long riskPassUv = Long.parseLong(record.getString("risk_pass_uv"));
            Long riskRefuseUv = Long.parseLong(record.getString("risk_refuse_uv"));
            Long riskUv = Long.parseLong(record.getString("risk_uv"));
            Long headUv = Long.parseLong(record.getString("head_uv"));
            Long waistUv = Long.parseLong(record.getString("waist_uv"));
            Long trailUv = Long.parseLong(record.getString("trail_uv"));
            Long aliRiskPassUv = Long.parseLong(record.getString("ali_risk_pass_uv"));
            Long wechatRiskPassUv = Long.parseLong(record.getString("wechat_risk_pass_uv"));
            Long otherRiskPassUv = Long.parseLong(record.getString("other_risk_pass_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setRiskPassUv(riskPassUv);
            domain.setRiskRefuseUv(riskRefuseUv);
            domain.setAliUv(aliRiskPassUv);
            domain.setWechatUv(wechatRiskPassUv);
            domain.setOtherUv(otherRiskPassUv);
            domain.setRiskUv(riskUv);
            domain.setHeadUv(headUv);
            domain.setWaistUv(waistUv);
            domain.setTrailUv(trailUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取风控用户数
     */
    private List<WholeLinkBigdataPassDO> getRiskUv(String ds, Integer hour) {
        String sql = "select \n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 then idcardNo else null end)) risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code = 'ALIPAY' " +
                "   then idcardNo else null end)) ali_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code = 'WECHAT' " +
                "   then idcardNo else null end)) wechat_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 and rmc.platform_code not in ('WECHAT', 'ALIPAY') " +
                "   then idcardNo else null end)) other_risk_pass_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 15 then idcardNo else null end)) risk_refuse_uv,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus < 10 then idcardNo else null end)) risk_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion = '风控等级1.0' then idcardNo\n" +
                "                              else null end))                                                   head_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion > '风控等级1.0' and\n" +
                "                                   cl.riskOpinion < '风控等级9999.0' then idcardNo\n" +
                "                              else null end))                                                   waist_uv,\n" +
                "          count(DISTINCT (case\n" +
                "                              when cl.artificialAuditStatus = 10 and cl.riskOpinion = '风控等级9999.0' then idcardNo\n" +
                "                              else null end))                                                   trail_uv\n" +
                "   from cl_loan cl\n" +
                "            left join rc_user ru on cl.`customerId` = ru.id\n" +
                "            left join serial_no sn on sn.businessNo = cl.loanNo\n" +
                "            left join rent_order ro on ro.no = cl.loanNo\n" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)\n" +
                "     and cl.parentNo = ''\n" +
                "     and cl.ds = \n" + ds +
                "     and ru.ds = \n" + ds +
                "     and sn.ds = \n" + ds +
                "     and ro.ds = \n" + ds +
                "     and date (to_date(sn.rhtime\n" +
                "       , 'yyyy-MM-dd HH:mi:ss')) = date (getdate())\n" +
                "  ;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long riskPassUv = Long.parseLong(record.getString("risk_pass_uv"));
            Long riskRefuseUv = Long.parseLong(record.getString("risk_refuse_uv"));
            Long riskUv = Long.parseLong(record.getString("risk_uv"));
            Long headUv = Long.parseLong(record.getString("head_uv"));
            Long waistUv = Long.parseLong(record.getString("waist_uv"));
            Long trailUv = Long.parseLong(record.getString("trail_uv"));
            Long aliRiskPassUv = Long.parseLong(record.getString("ali_risk_pass_uv"));
            Long wechatRiskPassUv = Long.parseLong(record.getString("wechat_risk_pass_uv"));
            Long otherRiskPassUv = Long.parseLong(record.getString("other_risk_pass_uv"));
            domain.setAliUv(aliRiskPassUv);
            domain.setWechatUv(wechatRiskPassUv);
            domain.setOtherUv(otherRiskPassUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassUv(riskPassUv);
            domain.setRiskRefuseUv(riskRefuseUv);
            domain.setRiskUv(riskUv);
            domain.setHeadUv(headUv);
            domain.setWaistUv(waistUv);
            domain.setTrailUv(trailUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 风控策略
     */
    private List<WholeLinkBigdataPassDO> getRiskStrategy(String ds, Integer hour) {
        String sql = "select riskStrategy,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 10 then idcardNo else null end)) pass,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus = 15 then idcardNo else null end)) nopass,\n" +
                "          count(DISTINCT (case when cl.artificialAuditStatus < 10 then idcardNo else null end)) lodpass\n" +
                "   from cl_loan cl\n" +
                "            left join rc_user ru on cl.`customerId` = ru.id\n" +
                "            left join serial_no sn on sn.businessNo = cl.loanNo\n" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)\n" +
                "     and cl.parentNo = ''\n" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date (getdate())\n" +
                "     and cl.ds=\n" + ds +
                "     and ru.ds=\n" + ds +
                "     and sn.ds=\n" + ds +
                "   group by riskStrategy\n" +
                "   order by count (DISTINCT (case when cl.artificialAuditStatus = 10 then idcardNo else null end)) desc\n" +
                "   limit 3;";
        List<Record> records = odpsUtil.querySql(sql);
        LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
        WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
        domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
        domain.setCountDay(localDate);
        domain.setCountHour(hour);


        if (records.size() > 1) {
            Record record = records.get(0);
            domain.setStrategyARiskUv(Long.parseLong(record.getString("lodpass")));
            domain.setStrategyAPassUv(Long.parseLong(record.getString("pass")));
            domain.setStrategyARefuseUv(Long.parseLong(record.getString("nopass")));
        }
        if (records.size() > 2) {
            Record record1 = records.get(1);
            domain.setStrategyBRiskUv(Long.parseLong(record1.getString("lodpass")));
            domain.setStrategyBPassUv(Long.parseLong(record1.getString("pass")));
            domain.setStrategyBRefuseUv(Long.parseLong(record1.getString("nopass")));
        }
        if (records.size() > 3) {
            Record record2 = records.get(2);
            domain.setStrategyCRiskUv(Long.parseLong(record2.getString("lodpass")));
            domain.setStrategyCPassUv(Long.parseLong(record2.getString("pass")));
            domain.setStrategyCRefuseUv(Long.parseLong(record2.getString("nopass")));
        }

        return Lists.newArrayList(domain);
    }


    /**
     * top渠道
     */
    private List<WholeLinkBigdataPassDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and sn.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.artificialAuditStatus=10 then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setTop5ChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkBigdataPassDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and sn.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.artificialAuditStatus=10 then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setTop5ChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkBigdataPassDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and sn.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.artificialAuditStatus=10 then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setTop5SceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkBigdataPassDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and sn.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.artificialAuditStatus=10 then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.artificialAuditStatus=10 then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "                        left JOIN serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and date (to_date(sn.rhtime , 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and sn.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setTop5SceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取未支付关闭
     */
    private List<WholeLinkBigdataPassDO> getNotPayByMiniType(String ds, Integer hour) {
        String sql = "select ro.mini_type,\n" +
                "          count(distinct idcardno)  not_pay_close_uv\n" +
                "   from cl_loan cl\n" +
                "            left join rc_user ru on cl.`customerId` = ru.id\n" +
                "            left join rent_order ro on ro.no = cl.loanNo\n" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)\n" +
                "     and cl.parentNo = ''\n" +
                "     and cl.ds = \n" + ds +
                "     and ru.ds = \n" + ds +
                "     and cl.closeStatus = 1\n" +
                "     and cl.paystatus = 0\n" +
                "     and ro.ds = \n" + ds +
                "     and date (to_date(cl.closetime , 'yyyy-MM-dd HH:mi:ss')) = date (getdate())\n" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long notPayCloseUv = Long.parseLong(record.getString("not_pay_close_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setNotPayCloseUv(notPayCloseUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取未支付关闭
     */
    private List<WholeLinkBigdataPassDO> getNotPay(String ds, Integer hour) {
        String sql = "select \n" +
                "          count(distinct idcardno)  not_pay_close_uv\n" +
                "   from cl_loan cl\n" +
                "            left join rc_user ru on cl.`customerId` = ru.id\n" +
                "            left join rent_order ro on ro.no = cl.loanNo\n" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)\n" +
                "     and cl.parentNo = ''\n" +
                "     and cl.ds = \n" + ds +
                "     and ru.ds = \n" + ds +
                "     and cl.closeStatus = 1\n" +
                "     and cl.paystatus = 0\n" +
                "     and ro.ds = \n" + ds +
                "     and date (to_date(cl.closetime , 'yyyy-MM-dd HH:mi:ss')) = date (getdate());\n";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBigdataPassDO domain = new WholeLinkBigdataPassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long notPayCloseUv = Long.parseLong(record.getString("not_pay_close_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setNotPayCloseUv(notPayCloseUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }
}



