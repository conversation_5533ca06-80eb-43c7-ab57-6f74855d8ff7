package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateRecoveriesDO;
import qnvip.data.overview.service.order.OperateRecoveriesService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/16
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateRecoveriesBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateRecoveriesService recoveriesService;

    void initMap(Map<Integer, Map<String, OperateRecoveriesDO>> miniType2Map, OperateRecoveriesDO recoveriesDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, OperateRecoveriesDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(recoveriesDO.getMiniType())) {
            OperateRecoveriesDO domain = new OperateRecoveriesDO();
            domain.setCountDay(countDay);
            domain.setMiniType(recoveriesDO.getMiniType());
            domain.setMerchantId(recoveriesDO.getMerchantId());
            merchantId2Do.put(recoveriesDO.getMerchantId(), domain);
        } else {
            merchantId2Do = miniType2Map.get(recoveriesDO.getMiniType());
            if (!merchantId2Do.containsKey(recoveriesDO.getMerchantId())) {
                OperateRecoveriesDO domain = new OperateRecoveriesDO();
                domain.setCountDay(countDay);
                domain.setMiniType(recoveriesDO.getMiniType());
                domain.setMerchantId(recoveriesDO.getMerchantId());
                merchantId2Do.put(recoveriesDO.getMerchantId(), domain);
            }
        }
        miniType2Map.put(recoveriesDO.getMiniType(), merchantId2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, OperateRecoveriesDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateRecoveriesDO>> f1 = CompletableFuture.supplyAsync(()->getOrder(ds));
        CompletableFuture<List<OperateRecoveriesDO>> f2 = CompletableFuture.supplyAsync(()->getAlreadyCount(ds));
        CompletableFuture<List<OperateRecoveriesDO>> f3 = CompletableFuture.supplyAsync(()->getAheadOrder(ds));
        CompletableFuture.allOf(f1, f2, f3).join();
        try {
            List<OperateRecoveriesDO> order = f1.get();
            List<OperateRecoveriesDO> alreadyCount = f2.get();
            List<OperateRecoveriesDO> aheadOrder = f3.get();
            for (OperateRecoveriesDO coreDO : order) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateRecoveriesDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateRecoveriesDO operateRecoveriesDO = merchantId2Do.get(coreDO.getMerchantId());
                operateRecoveriesDO.setShouldOrderCount(coreDO.getShouldOrderCount());
                operateRecoveriesDO.setShouldAmt(coreDO.getShouldAmt());
            }
            for (OperateRecoveriesDO coreDO : alreadyCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateRecoveriesDO> core = miniType2Map.get(coreDO.getMiniType());
                OperateRecoveriesDO operateRecoveriesDO = core.get(coreDO.getMerchantId());
                operateRecoveriesDO.setAlreadyAmt(coreDO.getAlreadyAmt());
                operateRecoveriesDO.setAlreadyOrderCount(coreDO.getAlreadyOrderCount());
            }
            for (OperateRecoveriesDO coreDO : aheadOrder) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateRecoveriesDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateRecoveriesDO operateRecoveriesDO = merchantId2Do.get(coreDO.getMerchantId());
                operateRecoveriesDO.setAheadAmt(coreDO.getAheadAmt());
                operateRecoveriesDO.setAheadOrderCount(coreDO.getAheadOrderCount());
            }
            // 写入mysql
            LinkedList<OperateRecoveriesDO> list = Lists.newLinkedList();

            for (Map.Entry<Integer, Map<String, OperateRecoveriesDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, OperateRecoveriesDO> merchantId2Do : entry.getValue().entrySet()) {
                    OperateRecoveriesDO value = merchantId2Do.getValue();
                    list.add(value);
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            recoveriesService.removeDataByTime(countDay);
            recoveriesService.saveBatch(list);
        } catch (InterruptedException | ExecutionException e) {
            log.error("OperateAccessCoreBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 应回金额,应回订单
     */
    private List<OperateRecoveriesDO> getOrder(String ds) {
        String sql = "select sum(a.capital) num," +
                " count(distinct a.order_id)           orderCount" +
                " from rent_order_repayment_plan a" +
                " left join rent_order b on a.order_id = b.id" +
                " where b.is_deleted = 0" +
                " and b.parent_id = 0" +
                " and b.type = 1" +
                " and b.merchant_id = 100" +
                " and b.status not in (20,30)" +
                " and a.ds = "+ds +
                " and b.ds = "+ds +
                " and a.repay_date between ${fromTime} " +
                " and ${toTime} ";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateRecoveriesDO domain = new OperateRecoveriesDO();
            String orderCount = record.getString("ordercount");
            String num = record.getString("num");
            domain.setMiniType(-5);
            domain.setShouldOrderCount(Long.valueOf(orderCount));
            domain.setShouldAmt(new BigDecimal(num));
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 提前还款金额
     */
    private List<OperateRecoveriesDO> getAheadOrder(String ds) {
        String sql = "select sum(real_repay_capital) num ," +
                " count(a.id)         orderCount" +
                " from rent_order a" +
                " inner join rent_order_repayment_plan b on a.id = b.order_id" +
                " where  a.is_deleted = 0" +
                " and a.type = 1" +
                " and a.merchant_id = 100" +
                " and a.status not in (20,30)" +
                " and a.ds = "+ds +
                " and b.ds = "+ds +
                " and date(b.real_repay_time) < date(b.repay_date)" +
                " and b.real_repay_time between ${fromTime} " +
                " and ${toTime} ";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateRecoveriesDO domain = new OperateRecoveriesDO();
            String orderCount = record.getString("ordercount");
            String num = record.getString("num");
            domain.setMiniType(-5);
            domain.setAheadOrderCount(Long.valueOf(orderCount));
            domain.setAheadAmt(new BigDecimal(num));
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 已回金额
     */
    private List<OperateRecoveriesDO> getAlreadyCount(String ds) {
        String sql = "select sum(real_repay_capital) num," +
                " count(a.id)         orderCount" +
                " from rent_order a" +
                " inner join rent_order_repayment_plan b on a.id = b.order_id" +
                " where  a.is_deleted = 0" +
                " and a.type = 1" +
                " and a.merchant_id = 100" +
                " and a.status not in (20,30)" +
                " and a.ds = "+ds +
                " and b.ds = "+ds +
                " and b.real_repay_time between ${fromTime} " +
                " and ${toTime} ";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateRecoveriesDO domain = new OperateRecoveriesDO();
            String orderCount = record.getString("ordercount");
            String num = record.getString("num");
            domain.setMiniType(-5);
            domain.setAlreadyOrderCount(Long.valueOf(orderCount));
            domain.setAlreadyAmt(new BigDecimal(num));
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }
}
