package qnvip.data.overview.business.web;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.web.FishOrderDO;
import qnvip.data.overview.service.web.FishOrderService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.rent.common.base.MultiResult;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @Date 2021/10/15 12:58 下午
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class FishOrderBusiness {
    private final OdpsUtil odpsUtil;
    private final FishOrderService fishOrderService;

    private static String timeFormat = "yyyy-MM-dd HH:mm:ss";

    private static AtomicInteger atomic = new AtomicInteger();
    public static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 3, 0, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(2000), r -> {
        Thread t = new Thread(r);
        t.setName("fish-order-business-" + atomic.incrementAndGet());
        return t;
    });

    /**
     * 充当本地锁的常量池，String.itern()会有性能问题
     */
    public static Map<String, String> map = new ConcurrentHashMap<>();


    public MultiResult<FishOrderDO> getReportList(LocalDateTime startTime, LocalDateTime endTime, Integer miniType,
                                                  Integer pageNo, Integer pageSize) {
//        //判断数据库最早的countDay是否小于开始时间
//        FishOrderDO minCountDayOrderDO = fishOrderService.getMinCountDay();
//        if (minCountDayOrderDO == null) {
//            return null;
//        }
//        LocalDateTime minCountDay = minCountDayOrderDO.getCountDay();
//        String dsStr = LocalDate.now().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
//        if (minCountDay.isAfter(startTime)) {
//            //如果小于需要计算，相差的天数，更新插入数据库
//            long between = ChronoUnit.DAYS.between(minCountDay, startTime);
//            for (long i = between; i < 0; i++) {
//                LocalDateTime currentCountTime = minCountDay.plusDays(i);
//                LocalDateTime currentCountDay = LocalDateTime.of(currentCountTime.toLocalDate(), LocalTime.MIN);
//                String sTime = currentCountDay.format(DateTimeFormatter.ofPattern(timeFormat));
//                String eTime =
//                        LocalDateTime.of(currentCountDay.toLocalDate(), LocalTime.MAX).format(DateTimeFormatter.ofPattern(timeFormat));
//                if (!map.containsKey(sTime)) {
//                    map.put(sTime, sTime);
//                }
//                synchronized (map.get(sTime)) {
//                    runFishOrderCount(dsStr, miniType, currentCountDay, sTime, eTime);
//                }
//            }
//        }
//        //判断要查询的数据中是否有updateTime不是零点前更新的
//        LocalDateTime yesterdayZero = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
//        List<FishOrderDO> beforeUpdateTimeList = fishOrderService.getBeforeUpdateTimeList(startTime, endTime,
//                yesterdayZero);
//        for (FishOrderDO fishOrderDO : beforeUpdateTimeList) {
//            LocalDateTime countDay = fishOrderDO.getCountDay();
//            String sTime =
//                    LocalDateTime.of(countDay.toLocalDate(), LocalTime.MIN).format(DateTimeFormatter.ofPattern(timeFormat));
//            String eTime =
//                    LocalDateTime.of(countDay.toLocalDate(), LocalTime.MAX).format(DateTimeFormatter.ofPattern(timeFormat));
//            if (!map.containsKey(sTime)) {
//                map.put(sTime, sTime);
//            }
//            synchronized (map.get(sTime)) {
//                runFishOrderCount(dsStr, miniType, countDay, sTime, eTime);
//            }
//        }
        //查询报表
        return fishOrderService.getOrderPage(startTime, endTime, miniType, pageNo, pageSize);
    }


    /**
     * 按天进行统计鱼传数据报表 这里可能会出现即时查询的情况，所以需要提高查询速度
     *
     * @param dsStr
     * @param miniType
     * @param sTime
     * @param eTime
     */
    public void runFishOrderCount(String dsStr, Integer miniType, String sTime, String eTime) {
        try {
            Map<String, FishOrderDO> collectMap = new HashMap<>();
            CountDownLatch countDownLatch = new CountDownLatch(14);
            threadPoolExecutor.execute(()->{
                registerCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                stockCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                orderCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                payCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                auditCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                auditTerminationCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                approvedCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                refusedCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                waitPayCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                waitDeliveryCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                waitPayTerminationCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                deliveryCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                rentingCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            threadPoolExecutor.execute(()->{
                waitPayTerminationCount(dsStr,miniType,sTime,eTime,collectMap,countDownLatch);
            });
            countDownLatch.await();
            for (FishOrderDO fishOrderDO : collectMap.values()) {
                fishOrderDO.setAuditRatio(CalculateUtil.div(fishOrderDO.getApprovedCount(), fishOrderDO.getStockCount()));
                fishOrderService.saveOrUpdate(fishOrderDO);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 注册人数
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void registerCount(String ds, Integer miniType, String sTime, String eTime,
                              Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_customer a " +
                "where   get_json_object(a.bindinfo,'$[0].miniType') = " + miniType +
                "    and     a.create_time >= '" + sTime + "' " +
                "    and     a.create_time <= '" + eTime + "' " +
                "    and     a.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 进件（订单提交)
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void stockCount(String ds, Integer miniType, String sTime, String eTime,
                           Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd');";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setStockCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 下单人数
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void orderCount(String ds, Integer miniType, String sTime, String eTime,
                           Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.customer_id) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setOrderCount(count);
        }
        countDownLatch.countDown();
    }

    public void payCount(String ds, Integer miniType, String sTime, String eTime,
                         Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     a.payment_time is not null " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setPayCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 审批中
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void auditCount(String ds, Integer miniType, String sTime, String eTime,
                           Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_audit b " +
                " on      a.id = b.order_id left " +
                " join    rent_order_infomore c " +
                " on      a.id = c.order_id " +
                "where   c.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     b.type = 2 " +
                " and     a.termination != 5 " +
                " and     b.audit_status = 0 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " and     c.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setAuditCount(count);
        }
        countDownLatch.countDown();
    }


    /**
     * 审批中已关闭
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void auditTerminationCount(String ds, Integer miniType, String sTime, String eTime, Map<String,
            FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) as count," +
                " to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_audit b " +
                " on      a.id = b.order_id left " +
                " join    rent_order_infomore c " +
                " on      a.id = c.order_id " +
                "where   c.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     b.type = 2 " +
                " and     a.termination = 5 " +
                " and     b.audit_status = 0 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " and     c.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setAuditTerminationCount(count);
        }
        countDownLatch.countDown();
    }


    /**
     * 审核通过
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void approvedCount(String ds, Integer miniType, String sTime, String eTime,
                              Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_audit b " +
                " on      a.id = b.order_id left " +
                " join    rent_order_infomore c " +
                " on      a.id = c.order_id " +
                "where   c.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     b.type = 2 " +
                " and     b.audit_status = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " and     c.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setApprovedCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 审核拒绝
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void refusedCount(String ds, Integer miniType, String sTime, String eTime,
                             Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_audit b " +
                " on      a.id = b.order_id left " +
                " join    rent_order_infomore c " +
                " on      a.id = c.order_id " +
                "where   c.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     b.type = 2 " +
                " and     b.audit_status = 2 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " and     a.ds = " + ds +
                " and     c.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRefusedCount(count);
        }
        countDownLatch.countDown();
    }


    /**
     * 待付款（保证金）
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void waitPayCount(String ds, Integer miniType, String sTime, String eTime,
                             Map<String, FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.status = 35 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setWaitPayCount(count);
        }
        countDownLatch.countDown();
    }


    /**
     * 待付款已关闭
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void waitPayTerminationCount(String ds, Integer miniType, String sTime, String eTime, Map<String,
            FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(distinct a.id) as count " +
                "    ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_audit b " +
                " on      a.id = b.order_id left " +
                " join    rent_order_infomore c " +
                " on      a.id = c.order_id " +
                "where   c.activity_id != 39 " +
                " and     a.type = 1 " +
                " and     b.type = 2 " +
                " and     a.payment_time is null " +
                " and     a.termination = 5 " +
                " and     b.audit_status = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " and     c.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setWaitPayTerminationCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 待发货
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void waitDeliveryCount(String ds, Integer miniType, String sTime, String eTime, Map<String,
            FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.status = 1 " +
                " and     a.type = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setWaitDeliveryCount(count);
        }
        countDownLatch.countDown();
    }

    /**
     * 已发货
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void deliveryCount(String ds, Integer miniType, String sTime, String eTime, Map<String,
            FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.status in (5,10,11) " +
                " and     a.type = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setDeliveryCount(count);
        }
        countDownLatch.countDown();
    }


    /**
     * 租用中
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public void rentingCount(String ds, Integer miniType, String sTime, String eTime, Map<String,
            FishOrderDO> collectMap,CountDownLatch countDownLatch) {
        String sql = " select  count(1) as count " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "from    rent_order a " +
                " left join rent_order_infomore b " +
                " on      a.id = b.order_id " +
                "where   b.activity_id != 39 " +
                " and     a.status = 15 " +
                " and     a.type = 1 " +
                " and     a.mini_type = " + miniType +
                " and     a.create_time >= '" + sTime + "' " +
                " and     a.create_time <= '" + eTime + "' " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            FishOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRentingCount(count);
        }
        countDownLatch.countDown();
    }


    private synchronized void initMap(Map<String, FishOrderDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            FishOrderDO ivd = new FishOrderDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setRegisterCount(0);
            ivd.setStockCount(0);
            ivd.setOrderCount(0);
            ivd.setPayCount(0);
            ivd.setAuditCount(0);
            ivd.setAuditTerminationCount(0);
            ivd.setApprovedCount(0);
            ivd.setRefusedCount(0);
            ivd.setWaitPayCount(0);
            ivd.setWaitPayTerminationCount(0);
            ivd.setWaitDeliveryCount(0);
            ivd.setDeliveryCount(0);
            ivd.setRentingCount(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}