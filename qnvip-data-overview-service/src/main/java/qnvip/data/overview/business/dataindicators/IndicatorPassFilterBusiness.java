package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPassFilterDO;
import qnvip.data.overview.service.dataindicators.IndicatorPassFilterService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/18
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorPassFilterBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorPassFilterService indicatorPassFilterService;

    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorPassFilterDO> collectMap = new HashMap<>();
        countRiskRhExecCount(ds, sTime, eTime, collectMap);
        countRiskFraudPassCount(ds, sTime, eTime, collectMap);
        countRiskPassCloseCount(ds, sTime, eTime, collectMap);
        ArrayList<IndicatorPassFilterDO> list = Lists.newArrayList();
        for (IndicatorPassFilterDO value : collectMap.values()) {
            value.setRiskRhExecCount(value.getRiskRhExecCountOne() + value.getRiskRhExecCountTwo() + value.getRiskRhExecCountThree());
            list.add(value);
        }
        indicatorPassFilterService.saveOrUpdateBatch(list, 2000);
    }


    /**
     * 人行风控执行人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRiskRhExecCount(String ds, String sTime, String eTime,
                                     Map<String, IndicatorPassFilterDO> collectMap) {
        String sql = " select  mini_type,to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    d.rate_config_type, " +
                "    IF(ISNOTNULL(d.ext_json) and d.repayment_term=3,'1','0') as forcedconversion, " +
                "    count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rent_order_finance_detail d on a.id=d.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and c.rhtime is not null and c.rhtime != '' " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                " group by mini_type,to_char(a.create_time,'yyyy-mm-dd'), " +
                "    IF(ISNOTNULL(d.ext_json) and d.repayment_term=3,'1','0'), " +
                "    d.rate_config_type;";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            initMap(collectMap, miniType, day);
            IndicatorPassFilterDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                updateDO.setRiskRhExecCountThree(Optional.ofNullable(updateDO.getRiskRhExecCountThree()).orElse(0) + count);
            } else if (rateConfigType == 10) {
                updateDO.setRiskRhExecCountOne(Optional.ofNullable(updateDO.getRiskRhExecCountOne()).orElse(0) + count);
            } else {
                updateDO.setRiskRhExecCountTwo(Optional.ofNullable(updateDO.getRiskRhExecCountTwo()).orElse(0) + count);
            }
        }
    }


    /**
     * 反欺诈通过人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRiskFraudPassCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorPassFilterDO> collectMap) {
        String sql = " select  mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) " +
                "count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "         inner join serial_no c on b.loanno = c.businessNo " +
                "         inner join rc_risk_access_rule_result_2023 d on c.id = d.serialnoId " +
                "         inner join rc_risk_strategy_rule_set e on d.ruleid = e.id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and d.result = 10 " +
                "  and e.rulestage = 2 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and e.ds = " + ds +
                " group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassFilterDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskFraudPassCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 订单取消人数:风控通过、订单关闭人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRiskPassCloseCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorPassFilterDO> collectMap) {
        String sql = " select a.mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count" +
                "    from rent_order a   " +
                "                inner join cl_loan b on a.no = b.loanno   " +
                "    where a.is_deleted = 0   " +
                "        and a.merchant_id = 100   " +
                "        and a.parent_id = 0   " +
                "        and a.type = 1   " +
                "        and a.biz_type = 2   " +
                "        and a.create_time between '" + sTime + "' and '" + eTime + "'   " +
                "        and a.termination = 5 " +
                "        and b.riskstatus = 20   " +
                "        and a.ds = " + ds +
                "        and b.ds = " + ds +
                "        group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPassFilterDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskPassCloseCount(Integer.valueOf(record.getString("count")));
        }
    }


    private void initMap(Map<String, IndicatorPassFilterDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorPassFilterDO ivd = new IndicatorPassFilterDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setRiskRhExecCount(0);
            ivd.setRiskRhExecCountOne(0);
            ivd.setRiskRhExecCountThree(0);
            ivd.setRiskRhExecCountTwo(0);
            ivd.setRiskFraudPassCount(0);
            ivd.setRiskPassCloseCount(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}