package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.data.overview.service.order.OperateOrderService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/16
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateOrderBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateOrderService orderService;

    void initMap(Map<Integer, Map<String, OperateOrderDO>> miniType2Map, OperateOrderDO orderDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, OperateOrderDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(orderDO.getMiniType())) {
            OperateOrderDO domain = new OperateOrderDO();
            domain.setCountDay(countDay);
            domain.setMiniType(orderDO.getMiniType());
            domain.setMerchantId(orderDO.getMerchantId());
            merchantId2Do.put(orderDO.getMerchantId(), domain);
        } else {
            merchantId2Do = miniType2Map.get(orderDO.getMiniType());
            if (!merchantId2Do.containsKey(orderDO.getMerchantId())) {
                OperateOrderDO domain = new OperateOrderDO();
                domain.setCountDay(countDay);
                domain.setMiniType(orderDO.getMiniType());
                domain.setMerchantId(orderDO.getMerchantId());
                merchantId2Do.put(orderDO.getMerchantId(), domain);
            }
        }
        miniType2Map.put(orderDO.getMiniType(), merchantId2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, OperateOrderDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateOrderDO>> f1 = CompletableFuture.supplyAsync(()->getAmt(ds));
        CompletableFuture<List<OperateOrderDO>> f2 = CompletableFuture.supplyAsync(()->getBondAmt(ds));
        CompletableFuture<List<OperateOrderDO>> f4 = CompletableFuture.supplyAsync(()->getScreenRisk(ds));
        CompletableFuture<List<OperateOrderDO>> f5 = CompletableFuture.supplyAsync(()->getAccessories(ds));
        CompletableFuture<List<OperateOrderDO>> f9 = CompletableFuture.supplyAsync(()->getActBuyOutAmt(ds));
        CompletableFuture<List<OperateOrderDO>> f10 = CompletableFuture.supplyAsync(()->getPayAmt(ds));
        CompletableFuture<List<OperateOrderDO>> f11 = CompletableFuture.supplyAsync(()->getDiscountAmt(ds));
        CompletableFuture.allOf(f1, f2, f4, f5, f9, f10, f11).join();
        try {
            List<OperateOrderDO> amtList = f1.get();
            List<OperateOrderDO> bondAmtList = f2.get();
            List<OperateOrderDO> screenRiskList = f4.get();
            List<OperateOrderDO> accessoriesList = f5.get();
            List<OperateOrderDO> actBuyOutAmtList = f9.get();
            List<OperateOrderDO> payAmtList = f10.get();
            List<OperateOrderDO> discountAmt = f11.get();
            for (OperateOrderDO coreDO : amtList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setRentTotal(coreDO.getRentTotal());
                operateOrderDO.setBuyoutAmt(coreDO.getBuyoutAmt());
            }
            for (OperateOrderDO coreDO : bondAmtList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> core = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = core.get(coreDO.getMerchantId());
                operateOrderDO.setActBondAmt(coreDO.getActBondAmt());
            }
            for (OperateOrderDO coreDO : screenRiskList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setScreenRisks(coreDO.getScreenRisks());
            }
            for (OperateOrderDO coreDO : accessoriesList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setAccessories(coreDO.getAccessories());
            }

            for (OperateOrderDO coreDO : actBuyOutAmtList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setActBuyoutAmt(coreDO.getActBuyoutAmt());
            }
            for (OperateOrderDO coreDO : payAmtList) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setPayAmt(coreDO.getPayAmt());
            }
            for (OperateOrderDO coreDO : discountAmt) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderDO operateOrderDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderDO.setDiscountAmt(coreDO.getDiscountAmt());
            }
            LinkedList<OperateOrderDO> list = Lists.newLinkedList();
            // 写入mysql
            for (Map.Entry<Integer, Map<String, OperateOrderDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, OperateOrderDO> merchantId2Do : entry.getValue().entrySet()) {
                    OperateOrderDO value = merchantId2Do.getValue();
                    list.add(value);
                    // orderService.saveOrUpdate(value);
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            orderService.removeDataByTime(countDay);
            orderService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateAccessCoreBusiness.runCore error:{}", e.getMessage(), e);
        }

    }

    /**
     * 获取应收总租金 ，应收买断金
     */
    private List<OperateOrderDO> getAmt(String ds) {
        String sql = "select sum(rent_total) rent_total," +
                "       sum(buyout_amt) buyout_amt," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_finance_detail b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String rentTotal = record.getString("rent_total");
            String buyoutAmt = record.getString("buyout_amt");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRentTotal(new BigDecimal(rentTotal));
            domain.setBuyoutAmt(new BigDecimal(buyoutAmt));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 实收碎屏保障金
     */
    private List<OperateOrderDO> getScreenRisk(String ds) {
        String sql = "select NVL(sum(b.insurance_amt), 0) as screen_risks," +
                "       a.merchant_id," +
                "       a.mini_type" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and a.is_screen_risk_payed = 1" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and b.giving_flag = 0" +
                "  and a.is_deleted = 0" +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String screenRisks = record.getString("screen_risks");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setScreenRisks(new BigDecimal(screenRisks));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 实收保证金
     */
    private List<OperateOrderDO> getBondAmt(String ds) {
        String sql = "select sum(act_bond_amt) act_bond_amt," +
                "       a.merchant_id," +
                "       a.mini_type" +
                " from rent_order a" +
                "         inner join rent_order_finance_detail b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String actBondAmt = record.getString("act_bond_amt");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setActBondAmt(new BigDecimal(actBondAmt));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 实收配件金
     */
    private List<OperateOrderDO> getAccessories(String ds) {
        String sql = "select NVL(sum(b.operating_purchase_price), 0) as componentprice," +
                "       a.merchant_id," +
                "       a.mini_type" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and b.item_type = 10" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                "  and a.is_deleted = 0" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String componentPrice = record.getString("componentprice");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setAccessories(new BigDecimal(componentPrice));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 实收买断金
     */
    private List<OperateOrderDO> getActBuyOutAmt(String ds) {
        String sql = "select sum(actual_buyout_amt) act_buyout_amt," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_finance_detail b on a.id = b.order_id and b.is_deleted = 0" +
                "         inner join rent_order_infomore c on a.id = c.order_id" +
                " where a.parent_id = 0" +
                "  and a.is_deleted = 0" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and c.buyout_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("act_buyout_amt");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setActBuyoutAmt(new BigDecimal(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 实收历史回款
     */
    private List<OperateOrderDO> getPayAmt(String ds) {
        String sql = "select sum(real_repay_capital) pay_amt," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_repayment_plan b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and a.is_deleted = 0" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and b.real_repay_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("pay_amt");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayAmt(new BigDecimal(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 发生营销费用
     */
    private List<OperateOrderDO> getDiscountAmt(String ds) {
        String sql = "select sum(discount_amt) discount_amt," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_alipay_order_pay b on a.id = b.order_id and b.is_deleted = 0" +
                " where a.parent_id = 0" +
                "  and a.is_deleted = 0" +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderDO domain = new OperateOrderDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("discount_amt");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setDiscountAmt(new BigDecimal(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }


}
