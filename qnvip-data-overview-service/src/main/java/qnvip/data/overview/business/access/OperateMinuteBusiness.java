package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.*;
import qnvip.data.overview.domain.order.OperateOrderDO;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.access.OperateMinuteService;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateMinuteBusiness {

    private final static DateTimeFormatter DF = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");

    private final OdpsUtil odpsUtil;

    private final OperateMinuteService minuteService;

    void initMap(Map<Integer, Map<LocalDateTime, OperateMinuteDO>> miniType2Map, OperateMinuteDO minuteDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<LocalDateTime, OperateMinuteDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(minuteDO.getMiniType())) {
            OperateMinuteDO domain = new OperateMinuteDO();
            domain.setCountDay(countDay);
            domain.setCountTime(minuteDO.getCountTime());
            domain.setMiniType(minuteDO.getMiniType());
            merchantId2Do.put(minuteDO.getCountTime(), domain);
        } else {
            merchantId2Do = miniType2Map.get(minuteDO.getMiniType());
            if (!merchantId2Do.containsKey(minuteDO.getCountTime())) {
                OperateMinuteDO domain = new OperateMinuteDO();
                domain.setCountDay(countDay);
                domain.setMiniType(minuteDO.getMiniType());
                domain.setCountTime(minuteDO.getCountTime());
                merchantId2Do.put(minuteDO.getCountTime(), domain);
            }
        }
        miniType2Map.put(minuteDO.getMiniType(), merchantId2Do);

    }

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<Integer, Map<LocalDateTime, OperateMinuteDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateMinuteDO>> f1 = CompletableFuture.supplyAsync(this::getPvAndUvByMiniType);
        CompletableFuture<List<OperateMinuteDO>> f2 = CompletableFuture.supplyAsync(this::getOrderUvByMiniType);
        CompletableFuture<List<OperateMinuteDO>> f3 = CompletableFuture.supplyAsync(this::getUvQualityByMiniType);

        // 计算全站数据
        CompletableFuture<List<OperateMinuteDO>> f4 = CompletableFuture.supplyAsync(this::getPvAndUv);
        CompletableFuture<List<OperateMinuteDO>> f5 = CompletableFuture.supplyAsync(this::getOrderUv);
        CompletableFuture<List<OperateMinuteDO>> f6 = CompletableFuture.supplyAsync(this::getUvQuality);

        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6).join();
        try {
            List<OperateMinuteDO> pvAndUv = f1.get();
            List<OperateMinuteDO> orderUv = f2.get();
            List<OperateMinuteDO> uvQuality = f3.get();

            parsePvAndUv(miniType2Map, pvAndUv);
            paeseOrderUv(miniType2Map, orderUv);
            parseUvQuality(miniType2Map, uvQuality);

            // 计算全站数据
            parsePvAndUv(miniType2Map, f4.get());
            paeseOrderUv(miniType2Map, f5.get());
            parseUvQuality(miniType2Map, f6.get());

            LinkedList<OperateMinuteDO> list = Lists.newLinkedList();
            for (Map.Entry<Integer, Map<LocalDateTime, OperateMinuteDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<LocalDateTime, OperateMinuteDO> time2Do : entry.getValue().entrySet()) {
                    OperateMinuteDO value = time2Do.getValue();
                    list.add(value);
                }
            }

            minuteService.removeDataByTime(countDay);
            minuteService.saveBatch(list);

        } catch (Exception e) {
            log.error("OperateMinuteBusiness.runCore error:{}", e.getMessage());
        }

    }


    private void parseUvQuality(Map<Integer, Map<LocalDateTime, OperateMinuteDO>> miniType2Map, List<OperateMinuteDO> uvQuality) {
        for (OperateMinuteDO coreDO : uvQuality) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateMinuteDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateMinuteDO hourService = date2DO.get(coreDO.getCountTime());
            hourService.setUvQuality(coreDO.getUvQuality());
        }
    }

    private void paeseOrderUv(Map<Integer, Map<LocalDateTime, OperateMinuteDO>> miniType2Map, List<OperateMinuteDO> orderUv) {
        for (OperateMinuteDO coreDO : orderUv) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateMinuteDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateMinuteDO hourService = date2DO.get(coreDO.getCountTime());
            hourService.setOrderUv(coreDO.getOrderUv());
        }
    }

    private void parsePvAndUv(Map<Integer, Map<LocalDateTime, OperateMinuteDO>> miniType2Map, List<OperateMinuteDO> pvAndUv) {
        for (OperateMinuteDO coreDO : pvAndUv) {
            initMap(miniType2Map, coreDO);
            Map<LocalDateTime, OperateMinuteDO> date2DO = miniType2Map.get(coreDO.getMiniType());
            OperateMinuteDO OperateMinuteDO = date2DO.get(coreDO.getCountTime());
            OperateMinuteDO.setUv(coreDO.getUv());
            OperateMinuteDO.setPv(coreDO.getPv());
        }
    }

    /**
     * 全站PV和UV
     */
    private List<OperateMinuteDO> getPvAndUvByMiniType() {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh:mi') as time" +
                "        ,count(distinct customer_third_id) uv" +
                "        ,count(customer_third_id) pv" +
                "        ,mini_type" +
                " from    dataview_track_enter_applets" +
                " where   action_type = 1" +
                " and     ds = to_char(getdate(), 'yyyymmdd')" +
                " and     report_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by mini_type" +
                "         ,to_char(create_time, 'yyyy-mm-dd hh:mi')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String time = record.getString("time");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 全站PV和UV
     */
    private List<OperateMinuteDO> getPvAndUv() {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh:mi') as time" +
                "        ,count(distinct customer_third_id) uv" +
                "        ,count(customer_third_id) pv" +
                " from    dataview_track_enter_applets" +
                " where   action_type = 1" +
                " and     ds = to_char(getdate(), 'yyyymmdd')" +
                " and     report_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by to_char(create_time, 'yyyy-mm-dd hh:mi')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String time = record.getString("time");

            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 高质量UV
     */
    private List<OperateMinuteDO> getUvQualityByMiniType() {
        String sql = "select a.time," +
                "       mini_type," +
                "       count(distinct customer_third_id ) uv_quality" +
                " from (select to_char(report_time, 'yyyy-mm-dd hh:mi') as time," +
                "        customer_third_id," +
                "         mini_type" +
                " from dataview_track_enter_applets" +
                " where action_type = 2" +
                "  and ds = to_char(getdate(), 'yyyymmdd')" +
                "   and report_time between ${fromTime}and ${toTime}" +
                " group by mini_type, customer_third_id , to_char(report_time, 'yyyy-mm-dd hh:mi')" +
                " having sum (keep_alive_time) >= 67000 ) a" +
                " group by a.time, mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String time = record.getString("time");
            String uv = record.getString("uv_quality");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvQuality(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 高质量UV
     */
    private List<OperateMinuteDO> getUvQuality() {
        String sql = "select a.time," +
                "       count(distinct customer_third_id ) uv_quality" +
                " from (select to_char(report_time, 'yyyy-mm-dd hh:mi') as time," +
                "        customer_third_id" +
                " from dataview_track_enter_applets" +
                " where action_type = 2" +
                "  and ds = to_char(getdate(), 'yyyymmdd')" +
                "   and report_time between ${fromTime}and ${toTime}" +
                " group by  customer_third_id , to_char(report_time, 'yyyy-mm-dd hh:mi')" +
                " having sum (keep_alive_time) >= 67000 ) a" +
                " group by a.time";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String time = record.getString("time");
            String uv = record.getString("uv_quality");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvQuality(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单用户
     */
    private List<OperateMinuteDO> getOrderUvByMiniType() {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh:mi') as time" +
                "        ,count(distinct customer_id) order_uv" +
                "        ,mini_type" +
                " from    rent_order" +
                " where   is_deleted = 0" +
                " and     parent_id = 0" +
                " and     type = 1" +
                " and     ds = to_char(getdate(), 'yyyymmdd')" +
                " and     create_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by mini_type" +
                "         ,to_char(create_time, 'yyyy-mm-dd hh:mi')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String miniType = record.getString("mini_type");
            String time = record.getString("time");
            String uv = record.getString("order_uv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 下单用户
     */
    private List<OperateMinuteDO> getOrderUv() {
        String sql = "select  to_char(create_time, 'yyyy-mm-dd hh:mi') as time" +
                "        ,count(distinct customer_id) order_uv" +
                " from    rent_order" +
                " where   is_deleted = 0" +
                " and     parent_id = 0" +
                " and     type = 1" +
                " and     ds = to_char(getdate(), 'yyyymmdd')" +
                " and     create_time between ${fromTime}" +
                " and     ${toTime}" +
                " group by to_char(create_time, 'yyyy-mm-dd hh:mi')";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMinuteDO domain = new OperateMinuteDO();
            String time = record.getString("time");
            String uv = record.getString("order_uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUv(Long.valueOf(uv));
            domain.setCountTime(LocalDateTime.parse(time, DF));
            return domain;
        }).collect(Collectors.toList());
    }

}
