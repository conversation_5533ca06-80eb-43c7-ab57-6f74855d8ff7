package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerJobDO;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerJobService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerJobBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerJobService jobService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            jobService.removeDataByTime(countDay);
            placeTheOrder();
            riskPass();
            onPay();
            signIn();
        } catch (Exception e) {
            log.error("OperateCustomerJobBusiness runCore error :{}", e.getMessage());
        }
    }


    /**
     * 下单口径
     */
    private void placeTheOrder() {
        String sql = "select a.mini_type," +
                "       b.work_unit" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and b.work_unit is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, b.work_unit";
        assemble(sql, CustomerTypeEnum.PLACE_THE_ORDER.getTypeCode());
    }

    /**
     * 通审口径
     */
    private void riskPass() {
        String sql = "select a.mini_type," +
                "       b.work_unit" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_audit c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.merchant_id = 100" +
                "  and c.type = 2" +
                "  and c.audit_status = 1" +
                "  and b.work_unit is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.operate_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, b.work_unit";

        assemble(sql, CustomerTypeEnum.RISK_PASS.getTypeCode());
    }

    /**
     * 支付
     */
    private void onPay() {
        String sql = "select a.mini_type," +
                "       b.work_unit" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_audit c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.merchant_id = 100" +
                "  and b.work_unit is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, b.work_unit";

        assemble(sql, CustomerTypeEnum.ON_PAY.getTypeCode());
    }

    /**
     * 签收
     */
    private void signIn() {
        String sql = "select a.mini_type," +
                "       b.work_unit" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_logistics c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.merchant_id = 100" +
                "  and b.work_unit is not null" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.sign_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, b.work_unit";

        assemble(sql, CustomerTypeEnum.SIGN_IN.getTypeCode());
    }

    private void assemble(String sql, Integer type) {
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OperateCustomerJobDO> collect = records.stream().map(record -> {
            OperateCustomerJobDO domain = new OperateCustomerJobDO();
            String miniType = record.getString("mini_type");
            String workUnit = record.getString("work_unit");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setType(type);
            domain.setJob(workUnit);
            domain.setCountDay(countDay);
            return domain;
        }).collect(Collectors.toList());
        jobService.saveBatch(collect);
    }


}
