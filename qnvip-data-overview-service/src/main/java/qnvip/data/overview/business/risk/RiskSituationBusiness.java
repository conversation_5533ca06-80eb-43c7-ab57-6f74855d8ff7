package qnvip.data.overview.business.risk;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.alibaba.ttl.TtlWrappers;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.risk.RiskSituationDO;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskSituationService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskSituationBusiness {

    private final OdpsUtil odpsUtil;

    private final RiskSituationService riskSituationService;

    private final RentOrderService rentOrderService;

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            List<Map<String, Object>> list = Lists.newArrayList();

            getRiskUser(list, ds);
            getBigDataPass(list, ds);
            getValidUser(list, ds);
            getSendCount(list, ds);
            getMargin(list, ds);
            getInstallmentPayCountByChannel(list, ds);
            // 计算全部平台
            getRiskUserTotal(list, ds);
            getBigDataPassTotal(list, ds);
            getValidUserTotal(list, ds);
            getSendCountTotal(list, ds);
            getMarginTotal(list, ds);
            getMerchantSendCountTotal(list, ds);
            getInstallmentPayCount(list, ds);
            getPayUser(list, ds);

            List<CompletableFuture<List<Map<String, String>>>> futureList =
                    list.stream().map(this::getData).collect(Collectors.toList());
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<Map<String, String>> totalList = Lists.newArrayList();
            futureList.forEach(future -> {
                try {
                    totalList.addAll(future.get());
                } catch (Exception e) {
                    log.error("RiskSituationBusiness runCore error,{}", e.getMessage(), e);
                }
            });

            Map<String, List<Map<String, String>>> channel2list =
                    totalList.stream().collect(Collectors.groupingBy(map -> map.get("business_channel")));
            LinkedList<RiskSituationDO> dos = Lists.newLinkedList();
            channel2list.forEach((channel, channelList) -> {
                RiskSituationDO riskSituationDO = new RiskSituationDO();
                Map<String, String> merged = new HashMap<>();
                channelList.forEach(merged::putAll);
                String bigDataPass = Optional.ofNullable(merged.get("big_data_pass")).orElse("0");
                String centralBankPass = Optional.ofNullable(merged.get("central_bank_pass")).orElse("0");
                String centralBankInquiry = Optional.ofNullable(merged.get("central_bank_inquiry")).orElse("0");
                String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
                String validUser = Optional.ofNullable(merged.get("valid_user")).orElse("0");
                String passUser = Optional.ofNullable(merged.get("pass_user")).orElse("0");
                String riskUser = Optional.ofNullable(merged.get("risk_user")).orElse("0");
                String margin = Optional.ofNullable(merged.get("margin")).orElse("0");
                String totalAmount = Optional.ofNullable(merged.get("total_amount")).orElse("0");
                String buyOutAmt = Optional.ofNullable(merged.get("buy_out_amt")).orElse("0");
                String sendCount = Optional.ofNullable(merged.get("send_count")).orElse("0");
                String installmentSendCount = Optional.ofNullable(merged.get("installment_send_count")).orElse("0");
                String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
                String curDayNum = Optional.ofNullable(merged.get("cur_day_num")).orElse("0");
                String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
                String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
                String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
                String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");

                String installmentOtherPay = Optional.ofNullable(merged.get("installment_other_pay")).orElse("0");
                String installmentAliPay = Optional.ofNullable(merged.get("installment_ali_pay")).orElse("0");
                String InstallmentWeixinPay = Optional.ofNullable(merged.get("installment_weixin_pay")).orElse("0");

                String installmentPayCount = Optional.ofNullable(merged.get("installment_pay_count")).orElse("0");
                totalAmount = "\\N".equals(totalAmount) ? "0" : totalAmount;
                buyOutAmt = "\\N".equals(buyOutAmt) ? "0" : buyOutAmt;
                margin = "\\N".equals(margin) ? "0" : margin;

                riskSituationDO.setCountDay(countDay);

                riskSituationDO.setTotalAmt(new BigDecimal(totalAmount));
                riskSituationDO.setBuyOutAmt(new BigDecimal(buyOutAmt));
                riskSituationDO.setMargin(new BigDecimal(margin));
                riskSituationDO.setBusinessChannel(Integer.valueOf(channel));
                riskSituationDO.setRiskUser(Long.valueOf(riskUser));
                riskSituationDO.setValidUser(Long.valueOf(validUser));
                riskSituationDO.setPassUser(Long.valueOf(passUser));
                riskSituationDO.setCentralBankInquiry(Long.valueOf(centralBankInquiry));
                riskSituationDO.setCentralBankPass(Long.valueOf(centralBankPass));
                riskSituationDO.setBigDataPass(Long.valueOf(bigDataPass));
                riskSituationDO.setPayUser(Long.valueOf(payUser));
                riskSituationDO.setSendCount(Long.valueOf(sendCount));
                riskSituationDO.setPayUserClose(Long.valueOf(payUserClose));
                riskSituationDO.setCurDayCount(Long.valueOf(curDayNum));
                riskSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
                riskSituationDO.setAliPay(NumberUtil.add(aliPay, installmentAliPay).longValue());
                riskSituationDO.setWeiXinPay(NumberUtil.add(weixinPay, InstallmentWeixinPay).longValue());
                riskSituationDO.setOtherPay(NumberUtil.add(otherPay, installmentOtherPay).longValue());
                riskSituationDO.setInstallmentSendCount(Long.valueOf(installmentSendCount));
                riskSituationDO.setInstallmentPayCount(Long.valueOf(installmentPayCount));
                dos.add(riskSituationDO);
            });
            riskSituationService.saveBatch(dos);
        } catch (Exception e) {
            log.error("================RiskSituationBusiness ERROR==================== error:{}", e.getMessage(), e);
        }

    }

    /**
     * 定时调度任务
     */
    public void runCoreTidb(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            List<Map<String, Object>> list = Lists.newArrayList();

            getRiskUser(list, ds);
            getBigDataPass(list, ds);
            getValidUser(list, ds);
            getSendCount(list, ds);
            getMargin(list, ds);
            getInstallmentPayCountByChannel(list, ds);
            // 计算全部平台
            getRiskUserTotal(list, ds);
            getBigDataPassTotal(list, ds);
            getValidUserTotal(list, ds);
            getSendCountTotal(list, ds);
            getMarginTotal(list, ds);
            getMerchantSendCountTotal(list, ds);
            getInstallmentPayCount(list, ds);
            getPayUser(list, ds);

            List<CompletableFuture<List<Map<String, String>>>> futureList =
                    list.stream().map(this::getDataTidb).collect(Collectors.toList());
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<Map<String, String>> totalList = Lists.newArrayList();
            futureList.forEach(future -> {
                try {
                    totalList.addAll(future.get());
                } catch (Exception e) {
                    log.error("RiskSituationBusiness runCore error,{}", e.getMessage(), e);
                }
            });

            Map<String, List<Map<String, String>>> channel2list =
                    totalList.stream().collect(Collectors.groupingBy(map -> map.get("business_channel")));
            LinkedList<RiskSituationDO> dos = Lists.newLinkedList();
            channel2list.forEach((channel, channelList) -> {
                RiskSituationDO riskSituationDO = new RiskSituationDO();
                Map<String, String> merged = new HashMap<>();
                channelList.forEach(merged::putAll);
                String bigDataPass = Optional.ofNullable(merged.get("big_data_pass")).orElse("0");
                String centralBankPass = Optional.ofNullable(merged.get("central_bank_pass")).orElse("0");
                String centralBankInquiry = Optional.ofNullable(merged.get("central_bank_inquiry")).orElse("0");
                String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
                String validUser = Optional.ofNullable(merged.get("valid_user")).orElse("0");
                String passUser = Optional.ofNullable(merged.get("pass_user")).orElse("0");
                String riskUser = Optional.ofNullable(merged.get("risk_user")).orElse("0");
                String margin = Optional.ofNullable(merged.get("margin")).orElse("0");
                String totalAmount = Optional.ofNullable(merged.get("total_amount")).orElse("0");
                String buyOutAmt = Optional.ofNullable(merged.get("buy_out_amt")).orElse("0");
                String sendCount = Optional.ofNullable(merged.get("send_count")).orElse("0");
                String installmentSendCount = Optional.ofNullable(merged.get("installment_send_count")).orElse("0");
                String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
                String curDayNum = Optional.ofNullable(merged.get("cur_day_num")).orElse("0");
                String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
                String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
                String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
                String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");

                String installmentOtherPay = Optional.ofNullable(merged.get("installment_other_pay")).orElse("0");
                String installmentAliPay = Optional.ofNullable(merged.get("installment_ali_pay")).orElse("0");
                String InstallmentWeixinPay = Optional.ofNullable(merged.get("installment_weixin_pay")).orElse("0");

                String installmentPayCount = Optional.ofNullable(merged.get("installment_pay_count")).orElse("0");
                totalAmount = "\\N".equals(totalAmount) ? "0" : totalAmount;
                buyOutAmt = "\\N".equals(buyOutAmt) ? "0" : buyOutAmt;
                margin = "\\N".equals(margin) ? "0" : margin;

                riskSituationDO.setCountDay(countDay);

                riskSituationDO.setTotalAmt(new BigDecimal(totalAmount));
                riskSituationDO.setBuyOutAmt(new BigDecimal(buyOutAmt));
                riskSituationDO.setMargin(new BigDecimal(margin));
                riskSituationDO.setBusinessChannel(Integer.valueOf(channel));
                riskSituationDO.setRiskUser(Long.valueOf(riskUser));
                riskSituationDO.setValidUser(Long.valueOf(validUser));
                riskSituationDO.setPassUser(Long.valueOf(passUser));
                riskSituationDO.setCentralBankInquiry(Long.valueOf(centralBankInquiry));
                riskSituationDO.setCentralBankPass(Long.valueOf(centralBankPass));
                riskSituationDO.setBigDataPass(Long.valueOf(bigDataPass));
                riskSituationDO.setPayUser(Long.valueOf(payUser));
                riskSituationDO.setSendCount(Long.valueOf(sendCount));
                riskSituationDO.setPayUserClose(Long.valueOf(payUserClose));
                riskSituationDO.setCurDayCount(Long.valueOf(curDayNum));
                riskSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
                riskSituationDO.setAliPay(NumberUtil.add(aliPay, installmentAliPay).longValue());
                riskSituationDO.setWeiXinPay(NumberUtil.add(weixinPay, InstallmentWeixinPay).longValue());
                riskSituationDO.setOtherPay(NumberUtil.add(otherPay, installmentOtherPay).longValue());
                riskSituationDO.setInstallmentSendCount(Long.valueOf(installmentSendCount));
                riskSituationDO.setInstallmentPayCount(Long.valueOf(installmentPayCount));
                dos.add(riskSituationDO);
            });
            riskSituationService.saveBatch(dos);
        } catch (Exception e) {
            log.error("================RiskSituationBusiness ERROR==================== error:{}", e.getMessage(), e);
        }

    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = new BigDecimal("0");
        System.out.println("bigDecimal = " + bigDecimal);
    }

    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUser(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type               business_channel," +
                "       count(DISTINCT customerId)       risk_user," +
                "       COUNT(DISTINCT (CASE" +
                "                           WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN cl.customerId" +
                "                           ELSE 0 END)) pass_user" +
                " from alchemist.cl_loan cl" +
                "             INNER JOIN qnvip_rent.rent_order a on cl.loanno = a.no  " +
                "             INNER JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10  " +
                " WHERE  " +
                "  cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.parentNo = ''" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)" +
                " group by a.mini_type";
        list.add(packageParam(sql, "risk_user", "pass_user", "business_channel"));
    }

    /**
     * 发货数
     *
     * @param list
     */
    private void getMerchantSendCountTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT if(l.businesschannel in(28,1013,1014,1016,1018,1019,1015,1017) and l.sendStatus = 5, l.customerId, null))  " +
                " installment_send_count" +
                " from alchemist.cl_loan l" +
                " where l.parentNo = ''" +
                "  and l.businessChannel in(28,1013,1014,1016,1018,1019,1015,1017)" +
                "  and date(sendtime) = ${sendTime} ;";
        list.add(packageParam(sql, "installment_send_count"));
    }

    /**
     * 有效用户
     *
     * @param list
     */
    private void getValidUser(List<Map<String, Object>> list, String ds) {
        String sql = "select count(DISTINCT (s.userId)) valid_user, a.mini_type business_channel " +
                " from alchemist.serial_no s" +
                "             inner JOIN qnvip_rent.rent_order a on s.businessNo = a.no  " +
                "             inner JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id  and b.common_rent_flag <> 10 " +
                " where s.userId not in (select userId from alchemist.serial_no" +
                "    where riskStrategy !=''" +
                "        and createTime >= date_sub(${fromTime}, INTERVAL  30 DAY)" +
                "        and createTime <= ${fromTime}" +
                " )" +
                "  and s.createTime >= ${fromTime} and s.createTime <= ${toTime}" +
                "  and s.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)" +
                " group by a.mini_type;";
        list.add(packageParam(sql, "valid_user", "business_channel"));
    }

    /**
     * 发货数量
     *
     * @param list
     */
    private void getSendCount(List<Map<String, Object>> list, String ds) {
        String sql =
               " select  ro.mini_type business_channel,COUNT(DISTINCT  customer_id)  send_count" +
                    " from qnvip_rent.rent_order ro" +
                "         inner join qnvip_rent.rent_order_logistics rol on ro.id = rol.order_id " +
                       "  INNER JOIN qnvip_rent.rent_order_infomore b on b.order_id = ro.id and b.common_rent_flag <>10  " +
                " where ro.parent_id = 0" +
                "  and ro.is_deleted = 0" +
                "  and ro.termination <> 5" +
                "  and ro.type = 1" +
                "  and date(ro.send_time)=  ${sendTime}"+
                "  group by ro.mini_type";
        list.add(packageParam(sql, "send_count", "business_channel"));
    }


    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCount(List<Map<String, Object>> list, String ds) {
//        String sql = "select" +
//                "       count (DISTINCT if(so.closing_time is null, customer_id, null)) AS installment_pay_count," +
//                "       count (DISTINCT (if(smf.platform_code = 'ALIPAY' and so.closing_time is null, customer_id," +
//                "       null))) installment_ali_pay," +
//                "       count (DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
//                "       null))) installment_weixin_pay," +
//                "       count (DISTINCT" +
//                "       (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) installment_other_pay" +
//                "   from qnvip_merchant.sh_order so" +
//                "       inner join qnvip_merchant.sh_order_finance sof" +
//                "   on so.order_uid = sof.order_uid and sof.is_deleted=0" +
//                "       inner join qnvip_merchant.sh_mini_config smf on so.mini_type = smf.mini_type" +
//                "   where  so.cash_deposit_pay_time >= ${fromTime}" +
//                "     AND so.cash_deposit_pay_time <= ${toTime}";

        String sql = "SELECT COUNT(DISTINCT IF(so.closing_time IS NULL,customer_id,NULL)) AS installment_pay_count,\n" +
                "  COUNT(\n" +
                "    DISTINCT (\n" +
                "      IF(\n" +
                "        smf.platform_code = 'ALIPAY' \n" +
                "        AND so.closing_time IS NULL,\n" +
                "        customer_id,\n" +
                "        NULL\n" +
                "      )\n" +
                "    )\n" +
                "  ) installment_ali_pay,\n" +
                "  COUNT(\n" +
                "    DISTINCT (\n" +
                "      IF(\n" +
                "        smf.platform_code = 'WECHAT' \n" +
                "        AND so.closing_time IS NULL,\n" +
                "        customer_id,\n" +
                "        NULL\n" +
                "      )\n" +
                "    )\n" +
                "  ) installment_weixin_pay,\n" +
                "  COUNT(\n" +
                "    DISTINCT (\n" +
                "      IF(\n" +
                "        smf.platform_code NOT IN ('ALIPAY', 'WECHAT') \n" +
                "        AND so.closing_time IS NULL,\n" +
                "        customer_id,\n" +
                "        NULL\n" +
                "      )\n" +
                "    )\n" +
                "  ) installment_other_pay \n" +
                "FROM\n" +
                "  qnvip_merchant.sh_order so \n" +
                "  INNER JOIN qnvip_merchant.sh_order_finance sof \n" +
                "    ON so.order_uid = sof.order_uid \n" +
                "    AND sof.is_deleted = 0 \n" +
                "  INNER JOIN qnvip_merchant.sh_mini_config smf \n" +
                "    ON so.mini_type = smf.mini_type \n" +
                "WHERE so.cash_deposit_pay_time >= ${fromTime} \n" +
                "  AND so.cash_deposit_pay_time <= ${toTime} ;\n" +
                "\n";
        list.add(packageParam(sql, "installment_pay_count", "installment_weixin_pay", "installment_ali_pay", "installment_other_pay"));
    }

    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCountByChannel(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT so.mini_type business_channel,count(DISTINCT if(cl.closeStatus = 0, customer_id, null)) AS         " +
                "                                installment_pay_count," +
                "          count(DISTINCT (if(smf.platform_code = 'ALIPAY' and cl.closeStatus = 0, customer_id," +
                "                             null)))                                                                           installment_ali_pay," +
                "          count(DISTINCT (if(smf.platform_code = 'WECHAT' and cl.closeStatus = 0, customer_id," +
                "                             null)))                                                                           installment_weixin_pay," +
                "          count(DISTINCT" +
                "                (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and cl.closeStatus = 0, customer_id, null))) installment_other_pay" +
                "   FROM alchemist.cl_loan cl" +
                "            INNER JOIN alchemist.serial_no sn ON sn.businessNo = cl.loanNo" +
                "            inner join qnvip_merchant.sh_order so on so.order_uid = cl.loanNo" +
                "            inner join qnvip_merchant.sh_mini_config smf on so.mini_type = smf.mini_type" +
                "   WHERE cl.parentNo = ''" +
                "     and cl.payStatus = 1" +
                "     AND cl.paytime >= ${fromTime}" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)" +
                "     AND cl.paytime <= ${toTime}" +
                "   group by so.mini_type;";
        list.add(packageParam(sql, "installment_pay_count", "business_channel", "installment_weixin_pay", "installment_ali_pay", "installment_other_pay"));
    }


    /**
     * 获取大数据通过人数
     *
     * @param list
     */
    private void getBigDataPass(List<Map<String, Object>> list, String ds) {
        String sql = "select *\n" +
                "   from (\n" +
                "            select a.mini_type business_channel,\n" +
                "                   count(DISTINCT (case\n" +
                "                                       when cl.riskStatus > 16 and sn.rhtime >= ${fromTime}\n" +
                "                                           and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                                       else null end)\n" +
                "                       )  as   central_bank_inquiry,\n" +
                "                   count(DISTINCT (case\n" +
                "                                       when riskStatus in (20, 21, 25) and\n" +
                "                                            cl.createtime >= ${fromTime}\n" +
                "                                           and cl.createtime <= ${toTime}\n" +
                "                                           and\n" +
                "                                            date(sn.rhtime) =\n" +
                "                                            date(cl.createtime)\n" +
                "                                           then cl.customerId\n" +
                "                                       else null end)\n" +
                "                       )  as   cur_day_num,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.opinionAmount <> '' and sn.rhtime >= ${fromTime}\n" +
                "                               and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   central_bank_pass,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.artificialAuditStatus = 10 and sn.rhtime >= ${fromTime}\n" +
                "                               and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   big_data_pass,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.businessChannel not in(28,1013,1014,1016," +
                "   1018,1019,1015,1017) and cl.closeStatus = 0 and cl\n" +
                "                                                                                                             .paytime >=\n" +
                "                                                                                                         ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   pay_user,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 1 and cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   pay_user_close,\n" +
                "                   (count(if(roi.risk_auth_status = 5 and cl.closeStatus = 1 and\n" +
                "                             cl.paytime >= ${fromTime}\n" +
                "                                 and cl.paytime <= ${toTime}, cl.customerId, null))" +
                "   + count(if(roi.risk_auth_tag like '%未接通%' and cl.closeStatus = 1 and \n" +
                "                                         cl.paytime >= ${fromTime}\n" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null))\n" +
                "        +                  count(if(so.credit_audit_status = 30 and cl.closeStatus = 1 and\n" +
                "                          cl.paytime >= ${fromTime}\n" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null)))\n" +
                "                          as   pay_credit_audit_close,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code = 'WECHAT' and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   weixin_pay,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code = 'ALIPAY' and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   ali_pay,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and\n" +
                "                                rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as   other_pay\n" +
                "            from alchemist.cl_loan cl\n" +
                "                     inner join alchemist.serial_no sn\n" +
                "                                on sn.businessNo = cl.loanNo  " +
                "                     left JOIN qnvip_rent.rent_order a on cl.loanno = a.no " +
                "                     left join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                     left JOIN qnvip_merchant.sh_order so on cl.loanno = so.order_uid  " +
                "                     left JOIN qnvip_rent.rent_order_infomore roi on roi.order_id = a.id  " +
                "            where cl.parentNo = ''\n" +
                "              and ( roi.common_rent_flag <> 10 or  roi.common_rent_flag is null)"+
                "              and cl.businessChannel in (1, 7, 10, 11, 12, 14, 15, 23, 25, 26, 28, 27, 29, 31, 32, 34,1013,1014,1016,1018,1019,1015,1017)\n" +
                "            group by a.mini_type\n" +
                "            union all\n" +
                "            select so.mini_type business_channel,\n" +
                "                   count(DISTINCT (case\n" +
                "                                       when cl.riskStatus > 16 and sn.rhtime >= ${fromTime}\n" +
                "                                           and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                                       else null end)\n" +
                "                       )  as    central_bank_inquiry,\n" +
                "                   count(DISTINCT (case\n" +
                "                                       when riskStatus in (20, 21, 25) and\n" +
                "                                            cl.createtime >= ${fromTime}\n" +
                "                                           and cl.createtime <= ${toTime}\n" +
                "                                           and\n" +
                "                                            date(sn.rhtime) =\n" +
                "                                            date(cl.createtime)\n" +
                "                                           then cl.customerId\n" +
                "                                       else null end)\n" +
                "                       )  as    cur_day_num,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.opinionAmount <> '' and sn.rhtime >= ${fromTime}\n" +
                "                               and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    central_bank_pass,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.artificialAuditStatus = 10 and sn.rhtime >= ${fromTime}\n" +
                "                               and sn.rhtime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    big_data_pass,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.businessChannel not in(28,1013,1014,1016," +
                " 1018,1019,1015,1017) and cl.closeStatus = 0 and cl\n" +
                "                                                                                                             .paytime >=\n" +
                "                                                                                                         ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    pay_user,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 1 and cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    pay_user_close,\n" +
                "                   (count(if(roi.risk_auth_status = 5 and cl.closeStatus = 1 and\n" +
                "                             cl.paytime >= ${fromTime}\n" +
                "                                 and cl.paytime <= ${toTime}, cl.customerId, null)) + count(if(roi" +
                "   .risk_auth_tag like '%未接通%' and cl.closeStatus = 1 and \n" +
                "                                         cl.paytime >= ${fromTime}\n" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null))\n" +
                "                        +  count(if(so.credit_audit_status = 30 and cl.closeStatus = 1 and\n" +
                "                          cl.paytime >= ${fromTime}\n" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null)))\n" +
                "                          as    pay_credit_audit_close,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code = 'WECHAT' and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    weixin_pay,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code = 'ALIPAY' and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    ali_pay,\n" +
                "                   count(DISTINCT (\n" +
                "                       case\n" +
                "                           when cl.payStatus = 1 and cl.closeStatus = 0 and\n" +
                "                                rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                cl.paytime >= ${fromTime}\n" +
                "                               and cl.paytime <= ${toTime} then cl.customerId\n" +
                "                           else null end\n" +
                "                       )) as    other_pay\n" +
                "            from alchemist.cl_loan cl\n" +
                "                     inner join alchemist.serial_no sn\n" +
                "                                on sn.businessNo = cl.loanNo  " +
                "                     left JOIN qnvip_rent.rent_order a on cl.loanno = a.no  " +
                "                     left join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                     left JOIN qnvip_merchant.sh_order so on cl.loanno = so.order_uid  " +
                "                     left JOIN qnvip_rent.rent_order_infomore roi on roi.order_id = a.id  " +
                "            where cl.parentNo = ''\n" +
                "              and ( roi.common_rent_flag <> 10 or  roi.common_rent_flag is null)"+
                "              and cl.businessChannel in (1, 7, 10, 11, 12, 14, 15, 23, 25, 26, 28, 27, 29, 31, 32, " +
                "   34,1013,1014,1016,1018,1019,1015,1017)\n" +
                "            group by so.mini_type\n" +
                "        ) a\n" +
                "   where a.business_channel is not null;";
        list.add(packageParam(sql, "central_bank_inquiry", "big_data_pass", "pay_user", "central_bank_pass",
                "business_channel", "pay_user_close", "cur_day_num", "weixin_pay", "ali_pay", "other_pay",
                "pay_credit_audit_close"));
    }

    /**
     * 获取大数据通过人数
     *
     * @param list
     */
    private void getPayUser(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT \n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and roi.common_rent_flag <>10 " +
                "                                  THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and merchant_id not in( 100,********)  THEN " +
                "   customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_merchant,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination=5 and roi.common_rent_flag <>10 THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_close,\n" +
                "          (count(if(roi.risk_auth_status = 5 and a.termination=5 and roi.common_rent_flag <>10, customer_id, null)) +\n" +
                "           count(if(roi.risk_auth_tag like '%未接通%' and a.termination=5 and roi.common_rent_flag <>10, customer_id,\n" +
                "                    null)))                                                                                    as pay_credit_audit_close,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and roi.common_rent_flag <>10  and rmc.platform_code = 'WECHAT'\n" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as weixin_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and roi.common_rent_flag <>10  and rmc.platform_code = 'ALIPAY'\n" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as ali_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and roi.common_rent_flag <>10  and\n" +
                "                                   rmc.platform_code not in ('ALIPAY', 'WECHAT') then customer_id\n" +
                "                              else null end))                                                                  as other_pay\n" +
                "   FROM qnvip_rent.rent_order a\n" +
                "            inner join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "            inner JOIN qnvip_rent.rent_order_infomore roi on roi.order_id = a.id   " +
                "   where a.parent_id =0\n" +
                "     and a.is_deleted=0\n" +
                "   and a.type =1\n" +
                "     AND payment_time between ${fromTime} and ${toTime};";
        list.add(packageParam(sql, "pay_user", "pay_user_merchant", "pay_user_close", "weixin_pay", "ali_pay",
                "other_pay"));
    }


    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT count(DISTINCT customerId)       risk_user," +
                "       COUNT(DISTINCT (CASE" +
                "                           WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN cl.customerId" +
                "                           ELSE 0 END)) pass_user" +
                " from alchemist.cl_loan cl" +
                "             left JOIN qnvip_rent.rent_order a on cl.loanno = a.no  " +
                "  left JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id  " +
                " WHERE   " +
                "   cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "           and ( b.common_rent_flag <> 10 or  b.common_rent_flag is null)"+
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)" +
                "  and cl.parentNo = '';";
        list.add(packageParam(sql, "risk_user", "pass_user"));
    }

    /**
     * 有效用户
     *
     * @param list
     */
    private void getValidUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = " select count(DISTINCT(s.userId)) valid_user" +
                " from alchemist.serial_no s" +
                "             left JOIN qnvip_rent.rent_order a on s.businessNo = a.no " +
                "      left JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10  " +
                " where s.userId not in (" +
                "    select userId from alchemist.serial_no where riskStrategy !=''" +
                "        and createTime >= date_sub(${fromTime},INTERVAL 30 day)" +
                "        and createTime <= ${fromTime}" +
                "        )" +
                " and s.createTime >= ${fromTime}" +
                " and s.createTime <= ${toTime}" +
                "           and ( b.common_rent_flag <> 10 or  b.common_rent_flag is null)"+
                "  and s.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)";
        list.add(packageParam(sql.concat(";"), "valid_user"));
    }

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT  customer_id)  send_count" +
                " from qnvip_rent.rent_order ro" +
                "         inner join qnvip_rent.rent_order_logistics rol on ro.id = rol.order_id " +
                "  INNER JOIN qnvip_rent.rent_order_infomore b on b.order_id = ro.id and b.common_rent_flag <>10 " +
                " where ro.parent_id = 0" +
                "  and ro.is_deleted = 0" +
                "  and ro.termination <> 5" +
                "  and ro.type = 1" +
                "  and date(ro.send_time)=  ${sendTime};";
        list.add(packageParam(sql, "send_count"));
    }

    /**
     * 执行人行风控、反欺诈通过、评分通过数、成功支付数
     *
     * @param list
     */
    private void getBigDataPassTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select " +
                "       count(DISTINCT (case when cl.riskStatus>16 and sn.rhtime >= ${fromTime} " +
                "  and sn.rhtime <= ${toTime} then cl.customerId else null end)" +
                "           )  as central_bank_inquiry," +
                "       count(DISTINCT (case" +
                "                           when riskStatus in (20, 21, 25) and" +
                "                                cl.createtime >= ${fromTime}" +
                "                               and cl.createtime <= ${toTime}" +
                "                               and" +
                "                                date(sn.rhtime) =" +
                "                                date(cl.createtime)" +
                "                                then cl.customerId" +
                "                           else null end)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (" +
                "           case when cl.opinionAmount <> '' and sn.rhtime >= ${fromTime}" +
                "   and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as central_bank_pass," +
                "       count(DISTINCT (" +
                "           case when cl.artificialAuditStatus = 10 and sn.rhtime >= ${fromTime}" +
                "       and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as big_data_pass," +
                "       count(DISTINCT (" +
                "           case when cl.payStatus = 1 and cl.businessChannel not in(28,1013,1014,1016,1018,1019,1015,1017) and" +
                " cl.closeStatus = 0 and cl" +
                " .paytime >= ${fromTime}" +
                "               and cl.paytime <= ${toTime}  then cl.customerId else null end" +
                "           )) as pay_user," +
                "        count(DISTINCT (" +
                "           case" +
                "               when cl.payStatus = 1 and cl.closeStatus = 1 and cl.paytime >= ${fromTime}" +
                "                   and cl.paytime <= ${toTime} then cl.customerId" +
                "               else null end" +
                "           )) as pay_user_close," +
                "     (count(if(roi.risk_auth_status = 5 and cl.closeStatus = 1 and" +
                "                 cl.paytime >= ${fromTime}" +
                "                     and cl.paytime <= ${toTime}, cl.customerId, null))" +
                "           + count(if(roi.risk_auth_tag like '%未接通%' and cl.closeStatus = 1 and" +
                "                      cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null))" +
                "           + count(if(so.credit_audit_status = 30 and cl.closeStatus = 1 and" +
                "                      cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime}, cl.customerId, null)))" +
                "              as pay_credit_audit_close," +
                "       count(DISTINCT (" +
                "           case" +
                "               when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code='WECHAT' and" +
                "                    cl.paytime >= ${fromTime}" +
                "                   and cl.paytime <= ${toTime} then cl.customerId" +
                "               else null end" +
                "           ))        as weixin_pay," +
                "              count(DISTINCT (" +
                "                  case" +
                "                      when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code='ALIPAY' and" +
                "                           cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime} then cl.customerId" +
                "                      else null end" +
                "                  )) as ali_pay," +
                "              count(DISTINCT (" +
                "                  case" +
                "                      when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code not in('ALIPAY','WECHAT') and" +
                "                           cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime} then cl.customerId" +
                "                      else null end" +
                "                  )) as other_pay" +
                " from alchemist.cl_loan cl" +
                "         inner join alchemist.serial_no sn" +
                "                    on sn.businessNo = cl.loanNo   " +
                " left JOIN qnvip_rent.rent_order a on  cl.loanno = a.no" + "   " +
                "          left join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                   left JOIN qnvip_merchant.sh_order so on cl.loanno = so.order_uid " +
                "                    left JOIN qnvip_rent.rent_order_infomore roi on roi.order_id = a.id   " +
                " where  cl.parentNo = ''" +
                "   and ( roi.common_rent_flag <> 10 or  roi.common_rent_flag is null)"+
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017);";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "pay_user", "pay_user_close", "cur_day_num", "weixin_pay", "ali_pay", "other_pay", "pay_credit_audit_close"));
    }

    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt" +
                " FROM alchemist.cl_loan cl" +
                "    INNER JOIN alchemist.rc_assess_record rr ON rr.loanId=cl.id" +
                "             INNER JOIN qnvip_rent.rent_order a on cl.loanno = a.no  " +
                "  INNER JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <> 10  " +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)";
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt"));
    }


    /**
     * 获取总保证金
     *
     * @param list
     */
    private void getMargin(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT SUM(cl.performanceBond) margin," +
                "       SUM(cl.loanAmount)  total_amount," +
                "       sum(rr.brokenAmount) buy_out_amt," +
                "       a.mini_type business_channel" +
                " FROM alchemist.cl_loan cl" +
                "    INNER JOIN alchemist.rc_assess_record rr ON rr.loanId=cl.id" +
                "             INNER JOIN qnvip_rent.rent_order a on cl.loanno = a.no " +
                "  INNER JOIN qnvip_rent.rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <> 10 and b.common_rent_flag <>10  " +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34,1013,1014,1016,1018,1019,1015,1017)" +
                " group by a.mini_type";
        list.add(packageParam(sql, "margin", "total_amount", "buy_out_amt", "business_channel"));
    }


    private CompletableFuture<List<Map<String, String>>> getData(Map<String, Object> param2condition) {
        return CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);
            List<Record> records = odpsUtil.querySql(format.concat(";"));
            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            List<Map<String, String>> collect = records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = record.getString(column);
                    column2value.put(column, value);
                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
            return collect;

        }));
    }

    private CompletableFuture<List<Map<String, String>>> getDataTidb(Map<String, Object> param2condition) {
        return CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);
//            List<Record> records = odpsUtil.querySql(format.concat(";"));
            List<Map<String,String>> records=rentOrderService.getGeneralCommonSql(format.concat(";"));
            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            List<Map<String, String>> collect = records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
//                    String value = record.get(column);
//                    column2value.put(column, value);
                    String value = null;
                    if(null == record){
                        column2value.put(column, "0");
                    }
                    else {
                        value = String.valueOf(record.get(column) == null ? "0":record.get(column));
                        column2value.put(column, value);
                    }

                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
            return collect;

        }));
    }

    private Map<String, Object> packageParam(String sql, String... columns) {
        HashMap<String, Object> param2condition = Maps.newHashMap();
        param2condition.put("sql", sql);
        param2condition.put("column", columns);
        return param2condition;
    }
}
