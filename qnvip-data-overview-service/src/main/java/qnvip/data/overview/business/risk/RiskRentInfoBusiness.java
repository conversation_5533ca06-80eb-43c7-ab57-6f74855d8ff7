package qnvip.data.overview.business.risk;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.risk.RiskRentInfoDO;
import qnvip.data.overview.domain.risk.RiskRentInfoVo;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskRentInfoService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风控大盘-新租赁
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @date 2023/08/24
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskRentInfoBusiness {

    private final OdpsUtil odpsUtil;
    private final RiskRentInfoService rentInfoService;
    private final RentOrderService rentOrderService;
    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        getTotal(time);
        List<RiskRentInfoDO> distributionList = getDistributionByCondition(time);
        if (CollUtil.isNotEmpty(distributionList)) {
            rentInfoService.deleteDistributionDetailsByCountDay(countDay);
            rentInfoService.saveBatch(distributionList);
        }
        List<RiskRentInfoDO> details = getByCondition(time);
        if (CollUtil.isNotEmpty(details)) {
            rentInfoService.deleteDetailsByCountDay(countDay);
            rentInfoService.saveBatch(details);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCoreTidb() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
//        getTotalTidb(time);
        List<RiskRentInfoDO> distributionList = getDistributionByConditionTidb(time);
        if (CollUtil.isNotEmpty(distributionList)) {
            rentInfoService.deleteDistributionDetailsByCountDay(countDay);
            rentInfoService.saveBatch(distributionList);
        }
        List<RiskRentInfoDO> details = getByConditionTidb(time);
        if (CollUtil.isNotEmpty(details)) {
            rentInfoService.deleteDetailsByCountDay(countDay);
            rentInfoService.saveBatch(details);
        }
    }
    public void runCoreTotal() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        getTotal(time);
    }
    /**
     * 定时调度任务
     */
    public void runCoreWithRiskOption() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        List<RiskRentInfoDO> distributionList = getDistributionByCondition(time);
        if (CollUtil.isNotEmpty(distributionList)) {
            rentInfoService.deleteDistributionDetailsByCountDay(countDay);
            rentInfoService.saveBatch(distributionList);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCoreDetails() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        List<RiskRentInfoDO> details = getByCondition(time);
        if (CollUtil.isNotEmpty(details)) {
            rentInfoService.deleteDetailsByCountDay(countDay);
            rentInfoService.saveBatch(details);
        }
    }


    private void getTotal(String time) {
        String sql = "with dt as (select date_format(now(), 'yyyymmdd') as ds)" +
                "   select t1.count_day," +
                "          t1.is_on_rent," +
                "          0                                                 type," +
                "          -5                                                business_channel," +
                "          ''                                                quotient_name," +
                "          ''                                                scene," +
                "          -5                                                finance_type," +
                "          -5                                                customer_type," +
                "          ''                                                risk_opinion," +
                "          central_bank_inquiry_cnt," +
                "          cur_day_cnt," +
                "          central_bank_pass_cnt," +
                "          first_lvl_pass," +
                "          labour_audit_cnt," +
                "          credit_audit_pass_cnt+ first_lvl_pass as total_pass_cnt," +
                "          pay_cnt," +
                "          ali_pay_cnt," +
                "          wechat_pay_cnt," +
                "          other_pay_cnt," +
                "          pay_close_cnt," +
                "          credit_audit_pass_cnt," +
                "          credit_audit_refuse_cnt," +
                "          three_lvl_cnt," +
                "          send_cnt," +
                "          risk_cnt," +
                "          preposition_risk_cnt," +
                "          pass_cnt," +
                "          anti_fraud_pass_cnt," +
                "          merchant_pay_close_cnt," +
                "          merchant_pay_cnt," +
                "          merchant_accept_cnt," +
                "          merchant_send_cnt," +
                "          merchant_shunt_cnt," +
                "          cur_pay_cnt," +
                "          cur_merchant_pay_cnt," +
                "          cur_pay_close_cnt," +
                "          cur_merchant_pay_close_cnt," +
                "          cur_merchant_shunt_cnt," +
                "          cur_merchant_accept_cnt," +
                "          cur_send_cnt," +
                "          cur_merchant_send_cnt" +
                "   from (SELECT " + time + "                                                                                       count_day," +
                "                if(common_rent_flag not in (10,30), '0', '1')                                                               is_on_rent," +
                "                count(DISTINCT" +
                "                      if(artificialauditstatus in (10, 15), a.customer_id, null))                               as central_bank_inquiry_cnt," +
                "                count(DISTINCT (if(" +
                "                                date(a.create_time) = to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and" +
                "                                artificialauditstatus in (10, 15), a.customer_id," +
                "                                null)))                                                                         as cur_day_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 40 ), a.customer_id," +
                "                                   null)))                                                                      as central_bank_pass_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, a.customer_id," +
                "                                   null)))                                                                      as first_lvl_pass," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.customer_id," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), a.customer_id, null))     AS pay_cnt," +
                "                 count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.customer_id," +
                "                                               NULL)))                                                                      AS pay_close_cnt," +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.customer_id," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                 COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.customer_id," +
                "                                   null)))                                                                      as credit_audit_refuse_cnt," +
                "                COUNT(DISTINCT (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 90 ), a.customer_id," +
                "                                   null)))                                                                         three_lvl_cnt," +
                "                COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), a.customer_id," +
                "                                  null))                                                                           send_cnt," +
                "                count(DISTINCT case" +
                "                                   when rtl.order_id is not null then a.customer_id" +
                "                                   else null end)                                                               as merchant_shunt_cnt," +
                "                count(DISTINCT case" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.customer_id" +
                "                                   else null end)                                                               as merchant_accept_cnt," +
                "                count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), a.customer_id," +
                "                                  null))                                                                        AS merchant_pay_cnt," +
                "                count(DISTINCT (CASE" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5" +
                "                                        THEN a.customer_id" +
                "                                    ELSE NULL END))                                                             AS merchant_pay_close_cnt," +
                "                COUNT(DISTINCT" +
                "                      if(a.merchant_id not in (100, ********) and sendstatus = 5, a.customer_id, null))            merchant_send_cnt" +
                "         FROM rent_order a" +
                "                  inner join cl_loan cl on cl.loanno = a.no and cl.ds = (select ds from dt) and cl.businessChannel<>1020" +
                "                  INNER JOIN rent_order_finance_detail c" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                  left join rent_order_infomore roi" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                  inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and" +
                "                                             to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =" +
                "                                             " + time + "" +
                "                  left join rent_order_merchant_transfer_log rtl" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)" +
                "         where a.parent_id = 0" +
                "           AND a.ds = (select ds from dt)" +
                "           and a.is_deleted = 0" +
                "           and a.type = 1" +
                "         group by if(common_rent_flag not in (10,30), '0', '1')) t1" +
                "            left join (select " + time + "                         count_day," +
                "                              cl.corent is_on_rent," +
                "                              count(DISTINCT a.customer_id)        risk_cnt," +
                "                              count(DISTINCT cl.customerid) preposition_risk_cnt," +
                "                                  COUNT(DISTINCT (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 20 ), a.customer_id," +
                "                                                 0)))                                                        pass_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 30 ), a.customer_id, 0))) anti_fraud_pass_cnt" +
                "                       from cl_loan cl" +
                "                                left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt)" +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                                left JOIN rent_order_finance_detail c" +
                "                                           on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                       WHERE cl.ds = (select ds from dt)" +
                "                         and cl.parentno = ''" +
                "                         and date(cl.createtime)=  " + time + "" +
                "                         and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019,1020)" +
                "                       group by cl.corent) t2" +
                "                      on t1.count_day = t2.count_day and t1.is_on_rent = t2.is_on_rent" +
                "            left join (SELECT " + time + "                                                                      count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')                                              is_on_rent," +
                "                              count(DISTINCT if(" +
                "                                              a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and" +
                "                                              a.termination <> 5, a.customer_id," +
                "                                              NULL))                                                         AS cur_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                   a.customer_id," +
                "                                   null)))                                                                      as ali_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                               a.customer_id," +
                "                                               null)))                                                                      as wechat_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and date(a.payment_time) = " + time + "  and" +
                "                                               a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5, a.customer_id," +
                "                                               null)))                                                                      as other_pay_cnt," +
                "   " +
                "                              count(DISTINCT if(a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + " and a.termination <> 5, a.customer_id," +
                "                                                NULL))                                                       AS cur_merchant_pay_cnt," +
                "                              count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and" +
                "                                                 date(a.payment_time) = " + time + ", a.customer_id," +
                "                                                 NULL)))                                                     AS cur_pay_close_cnt," +
                "                              count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + ", a.customer_id," +
                "                                                NULL))                                                       AS cur_merchant_pay_close_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(date(rtl.create_time) = " + time + ", a.customer_id, null))              cur_merchant_shunt_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + "," +
                "                                       a.customer_id, null))                                                    cur_merchant_accept_cnt" +
                "                       FROM rent_order a" +
                "                                INNER JOIN rent_order_finance_detail c" +
                "                                           on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                                left join rent_order_merchant_transfer_log rtl" +
                "                                          on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)" +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                                inner join rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                       where a.parent_id = 0" +
                "                         AND a.ds = (select ds from dt)" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t3" +
                "                      on t1.count_day = t3.count_day and t1.is_on_rent = t3.is_on_rent" +
                "            left join (select " + time + "                            count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')    is_on_rent," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id in (100, ********) and date(send_time) = " + time + ", a.customer_id," +
                "                                       null))                         cur_send_cnt," +
                "                              COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(send_time) = " + time + "," +
                "                                                a.customer_id, null)) cur_merchant_send_cnt" +
                "                       FROM rent_order a" +
                "                                INNER JOIN rent_order_finance_detail c" +
                "                                           on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                                inner join rent_order_logistics rol" +
                "                                           on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0" +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                       where a.parent_id = 0" +
                "                         AND a.ds = (select ds from dt)" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t4" +
                "                      on t1.count_day = t4.count_day and t1.is_on_rent = t4.is_on_rent" +
                "   union all" +
                "   select t1.count_day," +
                "          t1.is_on_rent," +
                "          1                                                 type," +
                "          -5                                                business_channel," +
                "          ''                                                quotient_name," +
                "          ''                                                scene," +
                "          -5                                                finance_type," +
                "          -5                                                customer_type," +
                "          ''                                                risk_opinion," +
                "          central_bank_inquiry_cnt," +
                "          cur_day_cnt," +
                "          central_bank_pass_cnt," +
                "          first_lvl_pass," +
                "          labour_audit_cnt," +
                "          credit_audit_pass_cnt+ first_lvl_pass as total_pass_cnt," +
                "          pay_cnt," +
                "          ali_pay_cnt," +
                "          wechat_pay_cnt," +
                "          other_pay_cnt," +
                "          pay_close_cnt," +
                "          credit_audit_pass_cnt," +
                "          credit_audit_refuse_cnt," +
                "          three_lvl_cnt," +
                "          send_cnt," +
                "          risk_cnt," +
                "          preposition_risk_cnt," +
                "          pass_cnt," +
                "          anti_fraud_pass_cnt," +
                "          merchant_pay_close_cnt," +
                "          merchant_pay_cnt," +
                "          merchant_accept_cnt," +
                "          merchant_send_cnt," +
                "          merchant_shunt_cnt," +
                "          cur_pay_cnt," +
                "          cur_merchant_pay_cnt," +
                "          cur_pay_close_cnt," +
                "          cur_merchant_pay_close_cnt," +
                "          cur_merchant_shunt_cnt," +
                "          cur_merchant_accept_cnt," +
                "          cur_send_cnt," +
                "          cur_merchant_send_cnt" +
                "   from (SELECT " + time + "                                                                                                count_day," +
                "                if(common_rent_flag not in (10,30), '0', '1')                                                                        is_on_rent," +
                "                count(DISTINCT if(artificialauditstatus in (10, 15), a.no, null))                                        as central_bank_inquiry_cnt," +
                "                count(DISTINCT (if(" +
                "                                date(a.create_time) = to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and" +
                "                                artificialauditstatus in (10, 15), a.no," +
                "                                null)))                                                                                  as cur_day_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 40 ), a.no," +
                "                                   null)))                                                                               as central_bank_pass_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') <=10, a.no," +
                "                                   null)))                                                                               as first_lvl_pass," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT" +
                "                      if(paystatus = 1 and a.merchant_id in (100, ********), a.no, null))                                AS pay_cnt," +
                "                count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.no," +
                "                                   NULL)))                                                                               AS pay_close_cnt," +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as credit_audit_pass_cnt," +
                "                COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.customer_id," +
                "                                   null)))                                                                      as credit_audit_refuse_cnt," +
                "                COUNT(DISTINCT (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 90 ), a.no," +
                "                                   null)))                                                                                  three_lvl_cnt," +
                "                COUNT(DISTINCT" +
                "                      if(sendstatus = 5 and a.merchant_id in (100, ********), a.no, null))                                  send_cnt," +
                "                count(DISTINCT case" +
                "                                   when rtl.order_id is not null then a.no" +
                "                                   else null end)                                                                        as merchant_shunt_cnt," +
                "                count(DISTINCT case" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.no" +
                "                                   else null end)                                                                        as merchant_accept_cnt," +
                "                count(DISTINCT" +
                "                      if(paystatus = 1 and a.merchant_id not in (100, ********), a.no, null))                            AS merchant_pay_cnt," +
                "                count(DISTINCT (CASE" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5" +
                "                                        THEN a.no" +
                "                                    ELSE NULL END))                                                                      AS merchant_pay_close_cnt," +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, a.no, null))                     merchant_send_cnt" +
                "         FROM rent_order a" +
                "                  inner join cl_loan cl on cl.loanno = a.no and cl.ds = (select ds from dt) and cl.businessChannel<>1020" +
                "                  INNER JOIN rent_order_finance_detail c on c.order_id = a.id and c.ds = (select ds from dt)" +
                "                  inner join rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                  left join rent_order_infomore roi" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                  left join rent_order_merchant_transfer_log rtl" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)" +
                "                  inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and" +
                "                                             to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =" +
                "                                             " + time + "" +
                "         where a.parent_id = 0" +
                "           AND a.ds = (select ds from dt)" +
                "           and a.is_deleted = 0" +
                "           and a.type = 1" +
                "         group by if(common_rent_flag not in (10,30), '0', '1')) t1" +
                "            left join (select " + time + "                                                                   count_day," +
                "                              cl.corent                                                                      is_on_rent," +
                "                              count(DISTINCT a.no)                                                           risk_cnt," +
                "                                count(DISTINCT cl.loanno) preposition_risk_cnt," +
                "                              COUNT(DISTINCT (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 20 ), a.no," +
                "                                                 0)))                                                        pass_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 30 ), a.no, 0))) anti_fraud_pass_cnt" +
                "                       from cl_loan cl" +
                "                                left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt) " +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                                left JOIN rent_order_finance_detail c on c.order_id = a.id and c.ds = (select ds from dt)" +
                "                       WHERE cl.ds = (select ds from dt)" +
                "                         and cl.parentno = ''" +
                "                         and cl.businessChannel<>1020" +
                "                         and date(cl.createtime)=  " + time + "" +
                "                         and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019)" +
                "                       group by cl.corent) t2" +
                "                      on t1.count_day = t2.count_day and t1.is_on_rent = t2.is_on_rent" +
                "            left join (SELECT " + time + "                                                                                  count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')                                                          is_on_rent," +
                "                              count(DISTINCT if(" +
                "                                              a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and" +
                "                                              a.termination <> 5, a.no," +
                "                                              NULL))                                                                     AS cur_pay_cnt," +
                "                           count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                   a.no," +
                "                                   null)))                                                                      as ali_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                               a.no," +
                "                                               null)))                                                                      as wechat_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and date(a.payment_time) = " + time + "  and" +
                "                                               a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5, a.no," +
                "                                               null)))                                                                      as other_pay_cnt," +
                "                              count(DISTINCT if(a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + " and a.termination <> 5, a.no," +
                "                                                NULL))                                                                   AS cur_merchant_pay_cnt," +
                "                              count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and" +
                "                                                 date(a.payment_time) = " + time + ", a.no," +
                "                                                 NULL)))                                                                 AS cur_pay_close_cnt," +
                "                              count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + ", a.no," +
                "                                                NULL))                                                                   AS cur_merchant_pay_close_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(date(rtl.create_time) = " + time + ", a.no, null))                                   cur_merchant_shunt_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + "," +
                "                                       a.no, null))                                                                         cur_merchant_accept_cnt" +
                "                       FROM rent_order a" +
                "                                INNER JOIN rent_order_finance_detail c" +
                "                                           on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                                left join rent_order_merchant_transfer_log rtl" +
                "                                          on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)" +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                                 left join rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                       where a.parent_id = 0" +
                "                         AND a.ds = (select ds from dt)" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t3" +
                "                      on t1.count_day = t3.count_day and t1.is_on_rent = t3.is_on_rent" +
                "            left join (select " + time + "                         count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1') is_on_rent," +
                "                              COUNT(DISTINCT if(a.merchant_id in (100, ********) and date(send_time) = " + time + ", a.no," +
                "                                                null))             cur_send_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(send_time) = " + time + ", a.no," +
                "                                       null))                      cur_merchant_send_cnt" +
                "                       FROM rent_order a" +
                "                                INNER JOIN rent_order_finance_detail c" +
                "                                           on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0" +
                "                                inner join rent_order_logistics rol" +
                "                                           on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0" +
                "                                left join rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0" +
                "                       where a.parent_id = 0" +
                "                         AND a.ds = (select ds from dt)" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t4" +
                "                      on t1.count_day = t4.count_day and t1.is_on_rent = t4.is_on_rent;";
        List<Record> records = odpsUtil.querySql(sql);
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        rentInfoService.deleteByCountDay(countDay);
        List<RiskRentInfoDO> list = assemble(records);
        if (CollUtil.isNotEmpty(list)) {
            rentInfoService.saveBatch(list);
        }
    }

    private void getTotalTidb(String time) {
        String sql = " " +
                "   select t1.count_day," +
                "          t1.is_on_rent," +
                "          0                                                 type," +
                "          -5                                                business_channel," +
                "          ''                                                quotient_name," +
                "          ''                                                scene," +
                "          -5                                                finance_type," +
                "          -5                                                customer_type," +
                "          ''                                                risk_opinion," +
                "          central_bank_inquiry_cnt," +
                "          cur_day_cnt," +
                "          central_bank_pass_cnt," +
                "          first_lvl_pass," +
                "          labour_audit_cnt," +
                "          credit_audit_pass_cnt+ first_lvl_pass as total_pass_cnt," +
                "          pay_cnt," +
                "          ali_pay_cnt," +
                "          wechat_pay_cnt," +
                "          other_pay_cnt," +
                "          pay_close_cnt," +
                "          credit_audit_pass_cnt," +
                "          credit_audit_refuse_cnt," +
                "          three_lvl_cnt," +
                "          send_cnt," +
                "          risk_cnt," +
                "          preposition_risk_cnt," +
                "          pass_cnt," +
                "          anti_fraud_pass_cnt," +
                "          merchant_pay_close_cnt," +
                "          merchant_pay_cnt," +
                "          merchant_accept_cnt," +
                "          merchant_send_cnt," +
                "          merchant_shunt_cnt," +
                "          cur_pay_cnt," +
                "          cur_merchant_pay_cnt," +
                "          cur_pay_close_cnt," +
                "          cur_merchant_pay_close_cnt," +
                "          cur_merchant_shunt_cnt," +
                "          cur_merchant_accept_cnt," +
                "          cur_send_cnt," +
                "          cur_merchant_send_cnt" +
                "   from (SELECT " + time + "                                                                                       count_day," +
                "                if(common_rent_flag not in (10,30), '0', '1')                                                               is_on_rent," +
                "                count(DISTINCT" +
                "                      if(artificialauditstatus in (10, 15), a.customer_id, null))                               as central_bank_inquiry_cnt," +
                "                count(DISTINCT (if(" +
                "                                date(a.create_time) = DATE(sn.rhtime) and" +
                "                                artificialauditstatus in (10, 15), a.customer_id," +
                "                                null)))                                                                         as cur_day_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 40 ), a.customer_id," +
                "                                   null)))                                                                      as central_bank_pass_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, a.customer_id," +
                "                                   null)))                                                                      as first_lvl_pass," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.customer_id," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), a.customer_id, null))     AS pay_cnt," +
                "                 count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.customer_id," +
                "                                               NULL)))                                                                      AS pay_close_cnt," +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.customer_id," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                 COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.customer_id," +
                "                                   null)))                                                                      as credit_audit_refuse_cnt," +
                "                COUNT(DISTINCT (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 90 ), a.customer_id," +
                "                                   null)))                                                                         three_lvl_cnt," +
                "                COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), a.customer_id," +
                "                                  null))                                                                           send_cnt," +
                "                count(DISTINCT case" +
                "                                   when rtl.order_id is not null then a.customer_id" +
                "                                   else null end)                                                               as merchant_shunt_cnt," +
                "                count(DISTINCT case" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.customer_id" +
                "                                   else null end)                                                               as merchant_accept_cnt," +
                "                count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), a.customer_id," +
                "                                  null))                                                                        AS merchant_pay_cnt," +
                "                count(DISTINCT (CASE" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5" +
                "                                        THEN a.customer_id" +
                "                                    ELSE NULL END))                                                             AS merchant_pay_close_cnt," +
                "                COUNT(DISTINCT" +
                "                      if(a.merchant_id not in (100, ********) and sendstatus = 5, a.customer_id, null))            merchant_send_cnt" +
                "         FROM qnvip_rent.rent_order a" +
                "                  inner join alchemist.cl_loan cl on cl.loanno = a.no and cl.businessChannel<>1020" +
                "                  INNER JOIN qnvip_rent.rent_order_finance_detail c" +
                "                             on c.order_id = a.id and c.is_deleted = 0" +
                "                  left join qnvip_rent.rent_order_infomore roi" +
                "                            on roi.order_id = a.id and roi.is_deleted = 0" +
                "                  inner join alchemist.serial_no sn on sn.businessno = cl.loanno  and" +
                "                                            date(sn.rhtime) =" +
                "                                             " + time + "" +
                "                  left join qnvip_rent.rent_order_merchant_transfer_log rtl" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 " +
                "         where a.parent_id = 0" +
                "           and a.is_deleted = 0" +
                "           and a.type = 1" +
                "         group by if(common_rent_flag not in (10,30), '0', '1')) t1" +
                "            left join (select " + time + "                         count_day," +
                "                              cl.corent is_on_rent," +
                "                              count(DISTINCT a.customer_id)        risk_cnt," +
                "                              count(DISTINCT cl.customerid) preposition_risk_cnt," +
                "                                  COUNT(DISTINCT (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 20 ), a.customer_id," +
                "                                                 0)))                                                        pass_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 30 ), a.customer_id, 0))) anti_fraud_pass_cnt" +
                "                       from alchemist.cl_loan cl" +
                "                                left join qnvip_rent.rent_order a on cl.loanno = a.no " +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id and roi.is_deleted = 0" +
                "                                left JOIN qnvip_rent.rent_order_finance_detail c" +
                "                                           on c.order_id = a.id  and c.is_deleted = 0" +
                "                       WHERE " +
                "                          cl.parentno = ''" +
                "                         and date(cl.createtime)=  " + time + "" +
                "                         and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019,1020)" +
                "                       group by cl.corent) t2" +
                "                      on t1.count_day = t2.count_day and t1.is_on_rent = t2.is_on_rent" +
                "            left join (SELECT " + time + "                                                                      count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')                                              is_on_rent," +
                "                              count(DISTINCT if(" +
                "                                              a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and" +
                "                                              a.termination <> 5, a.customer_id," +
                "                                              NULL))                                                         AS cur_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                   a.customer_id," +
                "                                   null)))                                                                      as ali_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                               a.customer_id," +
                "                                               null)))                                                                      as wechat_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and date(a.payment_time) = " + time + "  and" +
                "                                               a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5, a.customer_id," +
                "                                               null)))                                                                      as other_pay_cnt," +
                "   " +
                "                              count(DISTINCT if(a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + " and a.termination <> 5, a.customer_id," +
                "                                                NULL))                                                       AS cur_merchant_pay_cnt," +
                "                              count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and" +
                "                                                 date(a.payment_time) = " + time + ", a.customer_id," +
                "                                                 NULL)))                                                     AS cur_pay_close_cnt," +
                "                              count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + ", a.customer_id," +
                "                                                NULL))                                                       AS cur_merchant_pay_close_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(date(rtl.create_time) = " + time + ", a.customer_id, null))              cur_merchant_shunt_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + "," +
                "                                       a.customer_id, null))                                                    cur_merchant_accept_cnt" +
                "                       FROM qnvip_rent.rent_order a" +
                "                                INNER JOIN qnvip_rent.rent_order_finance_detail c" +
                "                                           on c.order_id = a.id  and c.is_deleted = 0" +
                "                                left join qnvip_rent.rent_order_merchant_transfer_log rtl" +
                "                                          on rtl.order_id = a.id and rtl.is_deleted = 0 " +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id  and roi.is_deleted = 0" +
                "                                inner join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                       where a.parent_id = 0" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t3" +
                "                      on t1.count_day = t3.count_day and t1.is_on_rent = t3.is_on_rent" +
                "            left join (select " + time + "                            count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')    is_on_rent," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id in (100, ********) and date(a.send_time) = " + time + ", a.customer_id," +
                "                                       null))                         cur_send_cnt," +
                "                              COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(a.send_time) = " + time + "," +
                "                                                a.customer_id, null)) cur_merchant_send_cnt" +
                "                       FROM qnvip_rent.rent_order a" +
                "                                INNER JOIN qnvip_rent.rent_order_finance_detail c" +
                "                                           on c.order_id = a.id  and c.is_deleted = 0" +
                "                                inner join qnvip_rent.rent_order_logistics rol" +
                "                                           on a.id = rol.order_id  and rol.is_deleted = 0" +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id  and roi.is_deleted = 0" +
                "                       where a.parent_id = 0" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t4" +
                "                      on t1.count_day = t4.count_day and t1.is_on_rent = t4.is_on_rent" +
                "   union all" +
                "   select t1.count_day," +
                "          t1.is_on_rent," +
                "          1                                                 type," +
                "          -5                                                business_channel," +
                "          ''                                                quotient_name," +
                "          ''                                                scene," +
                "          -5                                                finance_type," +
                "          -5                                                customer_type," +
                "          ''                                                risk_opinion," +
                "          central_bank_inquiry_cnt," +
                "          cur_day_cnt," +
                "          central_bank_pass_cnt," +
                "          first_lvl_pass," +
                "          labour_audit_cnt," +
                "          credit_audit_pass_cnt+ first_lvl_pass as total_pass_cnt," +
                "          pay_cnt," +
                "          ali_pay_cnt," +
                "          wechat_pay_cnt," +
                "          other_pay_cnt," +
                "          pay_close_cnt," +
                "          credit_audit_pass_cnt," +
                "          credit_audit_refuse_cnt," +
                "          three_lvl_cnt," +
                "          send_cnt," +
                "          risk_cnt," +
                "          preposition_risk_cnt," +
                "          pass_cnt," +
                "          anti_fraud_pass_cnt," +
                "          merchant_pay_close_cnt," +
                "          merchant_pay_cnt," +
                "          merchant_accept_cnt," +
                "          merchant_send_cnt," +
                "          merchant_shunt_cnt," +
                "          cur_pay_cnt," +
                "          cur_merchant_pay_cnt," +
                "          cur_pay_close_cnt," +
                "          cur_merchant_pay_close_cnt," +
                "          cur_merchant_shunt_cnt," +
                "          cur_merchant_accept_cnt," +
                "          cur_send_cnt," +
                "          cur_merchant_send_cnt" +
                "   from (SELECT " + time + "                                                                                                count_day," +
                "                if(common_rent_flag not in (10,30), '0', '1')                                                                        is_on_rent," +
                "                count(DISTINCT if(artificialauditstatus in (10, 15), a.no, null))                                        as central_bank_inquiry_cnt," +
                "                count(DISTINCT (if(" +
                "                                date(a.create_time) = DATE(sn.rhtime) and" +
                "                                artificialauditstatus in (10, 15), a.no," +
                "                                null)))                                                                                  as cur_day_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 40 ), a.no," +
                "                                   null)))                                                                               as central_bank_pass_cnt," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') <=10, a.no," +
                "                                   null)))                                                                               as first_lvl_pass," +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT" +
                "                      if(paystatus = 1 and a.merchant_id in (100, ********), a.no, null))                                AS pay_cnt," +
                "                count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.no," +
                "                                   NULL)))                                                                               AS pay_close_cnt," +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as credit_audit_pass_cnt," +
                "                COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.customer_id," +
                "                                   null)))                                                                      as credit_audit_refuse_cnt," +
                "                COUNT(DISTINCT (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 90 ), a.no," +
                "                                   null)))                                                                                  three_lvl_cnt," +
                "                COUNT(DISTINCT" +
                "                      if(sendstatus = 5 and a.merchant_id in (100, ********), a.no, null))                                  send_cnt," +
                "                count(DISTINCT case" +
                "                                   when rtl.order_id is not null then a.no" +
                "                                   else null end)                                                                        as merchant_shunt_cnt," +
                "                count(DISTINCT case" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.no" +
                "                                   else null end)                                                                        as merchant_accept_cnt," +
                "                count(DISTINCT" +
                "                      if(paystatus = 1 and a.merchant_id not in (100, ********), a.no, null))                            AS merchant_pay_cnt," +
                "                count(DISTINCT (CASE" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5" +
                "                                        THEN a.no" +
                "                                    ELSE NULL END))                                                                      AS merchant_pay_close_cnt," +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, a.no, null))                     merchant_send_cnt" +
                "         FROM qnvip_rent.rent_order a" +
                "                  inner join alchemist.cl_loan cl on cl.loanno = a.no and cl.businessChannel<>1020" +
                "                  INNER JOIN qnvip_rent.rent_order_finance_detail c on c.order_id = a.id " +
                "                  inner join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                  left join qnvip_rent.rent_order_infomore roi" +
                "                            on roi.order_id = a.id and roi.is_deleted = 0" +
                "                  left join qnvip_rent.rent_order_merchant_transfer_log rtl" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 " +
                "                  inner join alchemist.serial_no sn on sn.businessno = cl.loanno  and" +
                "                                             DATE(sn.rhtime) =" +
                "                                             " + time + "" +
                "         where a.parent_id = 0" +
                "           and a.is_deleted = 0" +
                "           and a.type = 1" +
                "         group by if(common_rent_flag not in (10,30), '0', '1')) t1" +
                "            left join (select " + time + "                                                                   count_day," +
                "                              cl.corent                                                                      is_on_rent," +
                "                              count(DISTINCT a.no)                                                           risk_cnt," +
                "                                count(DISTINCT cl.loanno) preposition_risk_cnt," +
                "                              COUNT(DISTINCT (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 20 ), a.no," +
                "                                                 0)))                                                        pass_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or" +
                "                                   (riskFicoStatus > 30 ), a.no, 0))) anti_fraud_pass_cnt" +
                "                       from alchemist.cl_loan cl" +
                "                                left join qnvip_rent.rent_order a on cl.loanno = a.no  " +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id  and roi.is_deleted = 0" +
                "                                left JOIN qnvip_rent.rent_order_finance_detail c on c.order_id = a.id " +
                "                       WHERE " +
                "                          cl.parentno = ''" +
                "                         and cl.businessChannel<>1020" +
                "                         and date(cl.createtime)=  " + time + "" +
                "                         and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019)" +
                "                       group by cl.corent) t2" +
                "                      on t1.count_day = t2.count_day and t1.is_on_rent = t2.is_on_rent" +
                "            left join (SELECT " + time + "                                                                                  count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1')                                                          is_on_rent," +
                "                              count(DISTINCT if(" +
                "                                              a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and" +
                "                                              a.termination <> 5, a.no," +
                "                                              NULL))                                                                     AS cur_pay_cnt," +
                "                           count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                   a.no," +
                "                                   null)))                                                                      as ali_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + "  and a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5," +
                "                                               a.no," +
                "                                               null)))                                                                      as wechat_pay_cnt," +
                "                            count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and date(a.payment_time) = " + time + "  and" +
                "                                               a.merchant_id in (100, ********) and" +
                "                                              a.termination <> 5, a.no," +
                "                                               null)))                                                                      as other_pay_cnt," +
                "                              count(DISTINCT if(a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + " and a.termination <> 5, a.no," +
                "                                                NULL))                                                                   AS cur_merchant_pay_cnt," +
                "                              count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and" +
                "                                                 date(a.payment_time) = " + time + ", a.no," +
                "                                                 NULL)))                                                                 AS cur_pay_close_cnt," +
                "                              count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and" +
                "                                                date(a.payment_time) = " + time + ", a.no," +
                "                                                NULL))                                                                   AS cur_merchant_pay_close_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(date(rtl.create_time) = " + time + ", a.no, null))                                   cur_merchant_shunt_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + "," +
                "                                       a.no, null))                                                                         cur_merchant_accept_cnt" +
                "                       FROM qnvip_rent.rent_order a" +
                "                                INNER JOIN qnvip_rent.rent_order_finance_detail c" +
                "                                           on c.order_id = a.id  and c.is_deleted = 0" +
                "                                left join qnvip_rent.rent_order_merchant_transfer_log rtl" +
                "                                          on rtl.order_id = a.id and rtl.is_deleted = 0 " +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id  and roi.is_deleted = 0" +
                "                                 left join qnvip_rent.rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                       where a.parent_id = 0" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t3" +
                "                      on t1.count_day = t3.count_day and t1.is_on_rent = t3.is_on_rent" +
                "            left join (select " + time + "                         count_day," +
                "                              if(common_rent_flag not in (10,30), '0', '1') is_on_rent," +
                "                              COUNT(DISTINCT if(a.merchant_id in (100, ********) and date(a.send_time) = " + time + ", a.no," +
                "                                                null))             cur_send_cnt," +
                "                              COUNT(DISTINCT" +
                "                                    if(a.merchant_id not in (100, ********) and date(a.send_time) = " + time + ", a.no," +
                "                                       null))                      cur_merchant_send_cnt" +
                "                       FROM qnvip_rent.rent_order a" +
                "                                INNER JOIN qnvip_rent.rent_order_finance_detail c" +
                "                                           on c.order_id = a.id  and c.is_deleted = 0" +
                "                                inner join qnvip_rent.rent_order_logistics rol" +
                "                                           on a.id = rol.order_id  and rol.is_deleted = 0" +
                "                                left join qnvip_rent.rent_order_infomore roi" +
                "                                          on roi.order_id = a.id  and roi.is_deleted = 0" +
                "                       where a.parent_id = 0" +
                "                         and a.is_deleted = 0" +
                "                         and a.type = 1" +
                "                       group by if(common_rent_flag not in (10,30), '0', '1')) t4" +
                "                      on t1.count_day = t4.count_day and t1.is_on_rent = t4.is_on_rent;";
//        List<Record> records = odpsUtil.querySql(sql);
        List<RiskRentInfoVo> records=rentOrderService.getRiskRentInfo(sql);
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        rentInfoService.deleteByCountDay(countDay);
        List<RiskRentInfoDO> list = assembleTidb(records);
        if (CollUtil.isNotEmpty(list)) {
            rentInfoService.saveBatch(list);
        }
    }

    private List<RiskRentInfoDO> assemble(List<Record> records) {
        return records.stream().map(merged -> {
                    LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
                    RiskRentInfoDO riskRentSituationDO = new RiskRentInfoDO();
                    String businessChannel = merged.getString("business_channel");
                    String isOnRent = merged.getString("is_on_rent");
                    String quotientName = merged.getString("quotient_name");
                    String scene = merged.getString("scene");
                    String financeType = merged.getString("finance_type");
                    String customerType = merged.getString("customer_type");
                    String riskOpinion = merged.getString("risk_opinion");
                    if (customerType.equals("\\N")) {
                        customerType = "3";
                    }
                    String type = merged.getString("type");
                    String riskCnt = merged.getString("risk_cnt");
                    String prepositionRiskCnt = merged.getString("preposition_risk_cnt");
                    String passCnt = merged.getString("pass_cnt");
                    String antiFraudPassCnt = merged.getString("anti_fraud_pass_cnt");
                    String curDayCnt = merged.getString("cur_day_cnt");
                    String centralBankInquiryCnt = merged.getString("central_bank_inquiry_cnt");
                    String centralBankPassCnt = merged.getString("central_bank_pass_cnt");
                    String firstLvlPass = merged.getString("first_lvl_pass");
                    String labourAuditCnt =merged.getString("labour_audit_cnt");
                    String totalPassCnt = merged.getString("total_pass_cnt");
                    String payCnt = merged.getString("pay_cnt");
                    String aliPayCnt = merged.getString("ali_pay_cnt");
                    String wechatPayCnt = merged.getString("wechat_pay_cnt");
                    String otherPayCnt = merged.getString("other_pay_cnt");
                    String payCloseCnt = merged.getString("pay_close_cnt");
                    String creditAuditPassCnt = merged.getString("credit_audit_pass_cnt");
                    String creditAuditRefuseCnt = merged.getString("credit_audit_refuse_cnt");
                    String threeLvlCnt = merged.getString("three_lvl_cnt");
                    String sendCnt = merged.getString("send_cnt");
                    String merchantShuntCnt = merged.getString("merchant_shunt_cnt");
                    String merchantAcceptCnt = merged.getString("merchant_accept_cnt");
                    String merchantPayCnt = merged.getString("merchant_pay_cnt");
                    String merchantPayCloseCnt = merged.getString("merchant_pay_close_cnt");
                    String merchantSendCnt = merged.getString("merchant_send_cnt");
                    String curPayCnt = merged.getString("cur_pay_cnt");
                    String curPayCloseCnt = merged.getString("cur_pay_close_cnt");
                    String curSendCnt = merged.getString("cur_send_cnt");
                    String curMerchantShuntCnt = merged.getString("cur_merchant_shunt_cnt");
                    String curMerchantAcceptCnt = merged.getString("cur_merchant_accept_cnt");
                    String curMerchantPayCnt = merged.getString("cur_merchant_pay_cnt");
                    String curMerchantPayCloseCnt = merged.getString("cur_merchant_pay_close_cnt");
                    String curMerchantSendCnt = merged.getString("cur_merchant_send_cnt");
                    riskRentSituationDO.setCountDay(countDay);
                    riskRentSituationDO.setBusinessChannel(Integer.valueOf(businessChannel));
                    riskRentSituationDO.setIsOnRent(Integer.valueOf(isOnRent));
                    riskRentSituationDO.setQuotientName(quotientName);
                    riskRentSituationDO.setScene(scene);
                    riskRentSituationDO.setRiskOpinion(riskOpinion);
                    riskRentSituationDO.setFinanceType(Integer.valueOf(financeType));
                    riskRentSituationDO.setCustomerType(Integer.valueOf(customerType));
                    riskRentSituationDO.setType(Integer.valueOf(type));
                    riskRentSituationDO.setRiskCnt(Integer.valueOf(riskCnt));
                    riskRentSituationDO.setPrepositionRiskCnt(Integer.valueOf(prepositionRiskCnt));
                    riskRentSituationDO.setPassCnt(Integer.valueOf(passCnt));
                    riskRentSituationDO.setAntiFraudPassCnt(Integer.valueOf(antiFraudPassCnt));
                    riskRentSituationDO.setCurDayCnt(Integer.valueOf(curDayCnt));
                    riskRentSituationDO.setCentralBankInquiryCnt(Integer.valueOf(centralBankInquiryCnt));
                    riskRentSituationDO.setCentralBankPassCnt(Integer.valueOf(centralBankPassCnt));
                    riskRentSituationDO.setFirstLvlPass(Integer.valueOf(firstLvlPass));
                    riskRentSituationDO.setTotalPassCnt(Integer.valueOf(totalPassCnt));
                    riskRentSituationDO.setLabourAuditCnt(Integer.valueOf(labourAuditCnt));
                    riskRentSituationDO.setPayCnt(Integer.valueOf(payCnt));
                    riskRentSituationDO.setAliPayCnt(Integer.valueOf(aliPayCnt));
                    riskRentSituationDO.setWechatPayCnt(Integer.valueOf(wechatPayCnt));
                    riskRentSituationDO.setOtherPayCnt(Integer.valueOf(otherPayCnt));
                    riskRentSituationDO.setPayCloseCnt(Integer.valueOf(payCloseCnt));
                    riskRentSituationDO.setCreditAuditPassCnt(Integer.valueOf(creditAuditPassCnt));
                    riskRentSituationDO.setCreditAuditRefuseCnt(Integer.valueOf(creditAuditRefuseCnt));
                    riskRentSituationDO.setThreeLvlCnt(Integer.valueOf(threeLvlCnt));
                    riskRentSituationDO.setSendCnt(Integer.valueOf(sendCnt));
                    riskRentSituationDO.setMerchantShuntCnt(Integer.valueOf(merchantShuntCnt));
                    riskRentSituationDO.setMerchantAcceptCnt(Integer.valueOf(merchantAcceptCnt));
                    riskRentSituationDO.setMerchantPayCnt(Integer.valueOf(merchantPayCnt));
                    riskRentSituationDO.setMerchantPayCloseCnt(Integer.valueOf(merchantPayCloseCnt));
                    riskRentSituationDO.setMerchantSendCnt(Integer.valueOf(merchantSendCnt));
                    riskRentSituationDO.setCurPayCnt(Integer.valueOf(curPayCnt));
                    riskRentSituationDO.setCurPayCloseCnt(Integer.valueOf(curPayCloseCnt));
                    riskRentSituationDO.setCurSendCnt(Integer.valueOf(curSendCnt));
                    riskRentSituationDO.setCurMerchantShuntCnt(Integer.valueOf(curMerchantShuntCnt));
                    riskRentSituationDO.setCurMerchantAcceptCnt(Integer.valueOf(curMerchantAcceptCnt));
                    riskRentSituationDO.setCurMerchantPayCnt(Integer.valueOf(curMerchantPayCnt));
                    riskRentSituationDO.setCurMerchantPayCloseCnt(Integer.valueOf(curMerchantPayCloseCnt));
                    riskRentSituationDO.setCurMerchantSendCnt(Integer.valueOf(curMerchantSendCnt));
                    return riskRentSituationDO;
                }
        ).collect(Collectors.toList());
    }

    private List<RiskRentInfoDO> assembleTidb(List<RiskRentInfoVo> records) {
        return records.stream().map(merged -> {
                    LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
                    RiskRentInfoDO riskRentSituationDO = new RiskRentInfoDO();
                    String businessChannel = merged.getBusinessChannel();
                    String isOnRent = merged.getIsOnRent();
                    String quotientName = merged.getQuotientName();
                    String scene = merged.getScene();
                    String financeType = merged.getFinanceType();
                    String customerType = merged.getCustomerType();
                    String riskOpinion = merged.getRiskOpinion();
                    if (null == customerType || customerType.equals("\\N")) {
                        customerType = "3";
                    }
                    String type = merged.getType();
                    String riskCnt = merged.getRiskCnt();
                    String prepositionRiskCnt = merged.getPrepositionRiskCnt();
                    String passCnt = merged.getPassCnt();
                    String antiFraudPassCnt = merged.getAntiFraudPassCnt();
                    String curDayCnt = merged.getCurDayCnt();
                    String centralBankInquiryCnt = merged.getCentralBankInquiryCnt();
                    String centralBankPassCnt = merged.getCentralBankPassCnt();
                    String firstLvlPass = merged.getFirstLvlPass();
                    String labourAuditCnt =merged.getLabourAuditCnt();
                    String totalPassCnt = merged.getTotalPassCnt();
                    String payCnt = merged.getPayCnt();
                    String aliPayCnt = merged.getAliPayCnt();
                    String wechatPayCnt = merged.getWechatPayCnt();
                    String otherPayCnt = merged.getOtherPayCnt();
                    String payCloseCnt = merged.getPayCloseCnt();
                    String creditAuditPassCnt = merged.getCreditAuditPassCnt();
                    String creditAuditRefuseCnt = merged.getCreditAuditRefuseCnt();
                    String threeLvlCnt = merged.getThreeLvlCnt();
                    String sendCnt = merged.getSendCnt();
                    String merchantShuntCnt = merged.getMerchantShuntCnt();
                    String merchantAcceptCnt = merged.getMerchantAcceptCnt();
                    String merchantPayCnt = merged.getMerchantPayCnt();
                    String merchantPayCloseCnt = merged.getMerchantPayCloseCnt();
                    String merchantSendCnt = merged.getMerchantSendCnt();
                    String curPayCnt = merged.getCurPayCnt();
                    String curPayCloseCnt = merged.getCurPayCloseCnt();
                    String curSendCnt = merged.getCurSendCnt();
                    String curMerchantShuntCnt = merged.getCurMerchantShuntCnt();
                    String curMerchantAcceptCnt = merged.getCurMerchantAcceptCnt();
                    String curMerchantPayCnt = merged.getCurMerchantPayCnt();
                    String curMerchantPayCloseCnt = merged.getCurMerchantPayCloseCnt();
                    String curMerchantSendCnt = merged.getCurMerchantSendCnt();
                    riskRentSituationDO.setCountDay(countDay);
                    riskRentSituationDO.setBusinessChannel(Integer.valueOf(businessChannel));
                    riskRentSituationDO.setIsOnRent(Integer.valueOf(isOnRent));
                    riskRentSituationDO.setQuotientName(quotientName);
                    riskRentSituationDO.setScene(scene);
                    riskRentSituationDO.setRiskOpinion(riskOpinion);
                    riskRentSituationDO.setFinanceType(Integer.valueOf(financeType));
                    riskRentSituationDO.setCustomerType(Integer.valueOf(customerType));
                    riskRentSituationDO.setType(Integer.valueOf(type));
                    riskRentSituationDO.setRiskCnt(Integer.valueOf(riskCnt));
                    riskRentSituationDO.setPrepositionRiskCnt(Integer.valueOf(prepositionRiskCnt));
                    riskRentSituationDO.setPassCnt(Integer.valueOf(passCnt));
                    riskRentSituationDO.setAntiFraudPassCnt(Integer.valueOf(antiFraudPassCnt));
                    riskRentSituationDO.setCurDayCnt(Integer.valueOf(curDayCnt));
                    riskRentSituationDO.setCentralBankInquiryCnt(Integer.valueOf(centralBankInquiryCnt));
                    riskRentSituationDO.setCentralBankPassCnt(Integer.valueOf(centralBankPassCnt));
                    riskRentSituationDO.setFirstLvlPass(Integer.valueOf(firstLvlPass));
                    riskRentSituationDO.setTotalPassCnt(Integer.valueOf(totalPassCnt));
                    riskRentSituationDO.setLabourAuditCnt(Integer.valueOf(labourAuditCnt));
                    riskRentSituationDO.setPayCnt(Integer.valueOf(payCnt));
                    riskRentSituationDO.setAliPayCnt(Integer.valueOf(aliPayCnt));
                    riskRentSituationDO.setWechatPayCnt(Integer.valueOf(wechatPayCnt));
                    riskRentSituationDO.setOtherPayCnt(Integer.valueOf(otherPayCnt));
                    riskRentSituationDO.setPayCloseCnt(Integer.valueOf(payCloseCnt));
                    riskRentSituationDO.setCreditAuditPassCnt(Integer.valueOf(creditAuditPassCnt));
                    riskRentSituationDO.setCreditAuditRefuseCnt(Integer.valueOf(creditAuditRefuseCnt));
                    riskRentSituationDO.setThreeLvlCnt(Integer.valueOf(threeLvlCnt));
                    riskRentSituationDO.setSendCnt(Integer.valueOf(sendCnt));
                    riskRentSituationDO.setMerchantShuntCnt(Integer.valueOf(merchantShuntCnt));
                    riskRentSituationDO.setMerchantAcceptCnt(Integer.valueOf(merchantAcceptCnt));
                    riskRentSituationDO.setMerchantPayCnt(Integer.valueOf(merchantPayCnt));
                    riskRentSituationDO.setMerchantPayCloseCnt(Integer.valueOf(merchantPayCloseCnt));
                    riskRentSituationDO.setMerchantSendCnt(Integer.valueOf(merchantSendCnt));
                    riskRentSituationDO.setCurPayCnt(Integer.valueOf(curPayCnt));
                    riskRentSituationDO.setCurPayCloseCnt(Integer.valueOf(curPayCloseCnt));
                    riskRentSituationDO.setCurSendCnt(Integer.valueOf(curSendCnt));
                    riskRentSituationDO.setCurMerchantShuntCnt(Integer.valueOf(curMerchantShuntCnt));
                    riskRentSituationDO.setCurMerchantAcceptCnt(Integer.valueOf(curMerchantAcceptCnt));
                    riskRentSituationDO.setCurMerchantPayCnt(Integer.valueOf(curMerchantPayCnt));
                    riskRentSituationDO.setCurMerchantPayCloseCnt(Integer.valueOf(curMerchantPayCloseCnt));
                    riskRentSituationDO.setCurMerchantSendCnt(Integer.valueOf(curMerchantSendCnt));
                    return riskRentSituationDO;
                }
        ).collect(Collectors.toList());
    }
    private List<RiskRentInfoDO> getByConditionTidb(String time) {
        String prefix = "with dt as (select date_format(now(), 'yyyymmdd') as ds),\n" +
                "     t as (select gg.customer_id as customer_id,\n" +
                "                  (case\n" +
                "                       when gg.lastCreateTime is null or datediff(ro.create_time, gg.lastCreateTime) > 180 then 3\n" +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 180 and\n" +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 30 then 2\n" +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 30 and\n" +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 0 then 1\n" +
                "                       else 4 end)  customer_type\n" +
                "           from rent_order ro\n" +
                "                    left join (select customer_id, max(a.create_time) lastCreateTime\n" +
                "                               from (select ro.customer_id,\n" +
                "                                            ro.create_time,\n" +
                "                                            row_number() over (PARTITION by ro.customer_id order by ro.create_time desc) num\n" +
                "                                     from rent_order ro\n" +
                "                                              left join rent_customer rc\n" +
                "                                                        on ro.customer_id = rc.id and rc.ds = (select ds from dt)\n" +
                "                                              left join rc_user ru\n" +
                "                                                        on rc.id_card_no = ru.idcardno and ru.ds = (select ds from dt)\n" +
                "                                              left join cl_loan cl on cl.customerid = ru.id and cl.ds = (select ds from dt)\n" +
                "                                     where ro.ds = (select ds from dt)) a\n" +
                "                               where a.num <> 1\n" +
                "                               group by a.customer_id) gg on gg.customer_id = ro.customer_id\n" +
                "           where ro.ds = (select ds from dt)),\n" +
                "     t1 as (SELECT common_rent_flag                                                     is_on_rent,\n" +
                "                   cl.businessChannel                                                   mini_type,\n" +
                "                   cl.quotientname                                                      quotient_name,\n" +
                "                   cl.scene                                                             scene,\n" +
                "                   rate_config_type                                                     finance_type,\n" +
                "                   t.customer_type                                                      customer_type,\n" +
                "                   count(DISTINCT\n" +
                "                         if(artificialauditstatus in (10, 15), cl.customerid, null)) as central_bank_inquiry_cnt,\n" +
                "                   count(DISTINCT (if(date(cl.createtime) =\n" +
                "                                      to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                                      artificialauditstatus in (10, 15), cl.customerid,\n" +
                "                                      null)))                                        as cur_day_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                 as central_bank_pass_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10,\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                 as first_lvl_pass,\n" +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, cl.customerid," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                                     null))                                          AS pay_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                             NULL)))                                                 AS pay_close_cnt,\n" +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, cl.customerid," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                   COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, cl.customerid,\n" +
                "                                      null)))                                        as credit_audit_refuse_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90),\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                    three_lvl_cnt,\n" +
                "                   COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                                     null))                                             send_cnt,\n" +
                "                   count(DISTINCT case\n" +
                "                                      when rtl.order_id is not null then cl.customerid\n" +
                "                                      else null end)                                 as merchant_shunt_cnt,\n" +
                "                   count(DISTINCT case\n" +
                "                                      when a.merchant_id not in (100, ********) and merchant_transfer = 10\n" +
                "                                          then cl.customerid\n" +
                "                                      else null end)                                 as merchant_accept_cnt,\n" +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), cl.customerid,\n" +
                "                                     null))                                          AS merchant_pay_cnt,\n" +
                "                   count(DISTINCT (CASE\n" +
                "                                       WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5\n" +
                "                                           THEN cl.customerid\n" +
                "                                       ELSE NULL END))                               AS merchant_pay_close_cnt,\n" +
                "                   COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, cl.customerid,\n" +
                "                                     null))                                             merchant_send_cnt\n" +
                "            FROM cl_loan cl\n" +
                "                     left join rent_order a on cl.loanno = a.no and a.parent_id = 0\n" +
                "                AND a.ds = (select ds from dt)\n" +
                "                and a.is_deleted = 0\n" +
                "                and a.type = 1\n" +
                "                     left JOIN rent_order_finance_detail c\n" +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                     left join rent_order_infomore roi\n" +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                     left join rent_order_merchant_transfer_log rtl\n" +
                "                               on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                     INNER join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and\n" +
                "                                               to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =\n" +
                "                                               " + time + "\n" +
                "                     left join t on t.customer_id = a.customer_id\n" +
                "            where cl.ds = (select ds from dt)\n" +
                "                   and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "            group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t2 as (select common_rent_flag              is_on_rent,\n" +
                "                   cl.businessChannel            mini_type,\n" +
                "                   cl.quotientname               quotient_name,\n" +
                "                   cl.scene                      scene,\n" +
                "                   rate_config_type              finance_type,\n" +
                "                   t.customer_type               customer_type,\n" +
                "                   count(DISTINCT cl.customerid) risk_cnt,\n" +
                "                   count(DISTINCT cl.customerid) preposition_risk_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20),\n" +
                "                             cl.customerid, 0))) pass_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30),\n" +
                "                             cl.customerid, 0))) anti_fraud_pass_cnt\n" +
                "            from cl_loan cl\n" +
                "                     left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt)  and a.mini_type is not null\n" +
                "                     left join rent_order_infomore roi\n" +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                     left JOIN rent_order_finance_detail c\n" +
                "                               on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                     left join t on t.customer_id = a.customer_id\n" +
                "            WHERE cl.ds = (select ds from dt)\n" +
                "              and cl.parentno = ''\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "              and date(cl.createtime) = " + time + "\n" +
                "            group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t3 as (\n" +
                "         select nvl(t1.mini_type, t2.mini_type)         business_channel,\n" +
                "                nvl(t1.is_on_rent, t2.is_on_rent)       is_on_rent,\n" +
                "                nvl(t1.quotient_name, t2.quotient_name) quotient_name,\n" +
                "                nvl(t1.scene, t2.scene)                 scene,\n" +
                "                nvl(t1.finance_type, t2.finance_type)   finance_type,\n" +
                "                nvl(t1.customer_type, t2.customer_type) customer_type,\n" +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt,\n" +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt,\n" +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt,\n" +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass,\n" +
                "                nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                nvl(pay_cnt, 0)                         pay_cnt,\n" +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt,\n" +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt,\n" +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt,\n" +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt,\n" +
                "                nvl(send_cnt, 0)                        send_cnt,\n" +
                "                nvl(risk_cnt, 0)                        risk_cnt,\n" +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt,\n" +
                "                nvl(pass_cnt, 0)                        pass_cnt,\n" +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt,\n" +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt,\n" +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt,\n" +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt,\n" +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt,\n" +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt\n" +
                "         from t1 full\n" +
                "                  join t2\n" +
                "                       on t1.mini_type = t2.mini_type and t1.is_on_rent = t2.is_on_rent and\n" +
                "                          t1.quotient_name = t2.quotient_name and t1.scene = t2.scene and\n" +
                "                          t1.finance_type = t2.finance_type and t1.customer_type = t2.customer_type),\n" +
                "     t4 as (\n" +
                "         SELECT common_rent_flag                                                     is_on_rent,\n" +
                "                cl.businessChannel                                                   mini_type,\n" +
                "                cl.quotientname                                                      quotient_name,\n" +
                "                cl.scene                                                             scene,\n" +
                "                rate_config_type                                                     finance_type,\n" +
                "                t.customer_type                                                      customer_type,\n" +
                "                count(DISTINCT if(a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        ali_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        wechat_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                   date(a.payment_time) = " + time + " and a.merchant_id in (100, ********) and\n" +
                "                                   a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        other_pay_cnt,\n" +
                "                count(DISTINCT if(a.merchant_id not in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_merchant_pay_cnt,\n" +
                "                count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and\n" +
                "                                   date(a.payment_time) = " + time + ", cl.customerid,\n" +
                "                                   NULL))) AS                                        cur_pay_close_cnt,\n" +
                "                count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and\n" +
                "                                  date(a.payment_time) = " + time + ", cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(date(rtl.create_time) = " + time + ", cl.customerid, null)) cur_merchant_shunt_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + ",\n" +
                "                                  cl.customerid, null))                              cur_merchant_accept_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no  and a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where\n" +
                "                cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t5 as (\n" +
                "         select common_rent_flag                                            is_on_rent,\n" +
                "                cl.businessChannel                                          mini_type,\n" +
                "                cl.quotientname                                             quotient_name,\n" +
                "                cl.scene                                                    scene,\n" +
                "                rate_config_type                                            finance_type,\n" +
                "                t.customer_type                                             customer_type,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id in (100, ********) and date(send_time) = " + time + " and a.termination <> 5,\n" +
                "                         cl.customerid, null))                              cur_send_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(send_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid, null)) cur_merchant_send_cnt\n" +
                "         FROM  cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no and a.parent_id = 0\n" +
                "                           AND a.ds = (select ds from dt)\n" +
                "                           and a.is_deleted = 0\n" +
                "                           and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_logistics rol\n" +
                "                             on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "               and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t6 as (\n" +
                "         select nvl(t4.mini_type, t5.mini_type)         business_channel,\n" +
                "                nvl(t4.is_on_rent, t5.is_on_rent)       is_on_rent,\n" +
                "                nvl(t4.quotient_name, t5.quotient_name) quotient_name,\n" +
                "                nvl(t4.scene, t5.scene)                 scene,\n" +
                "                nvl(t4.finance_type, t5.finance_type)   finance_type,\n" +
                "                nvl(t4.customer_type, t5.customer_type) customer_type,\n" +
                "                nvl(cur_pay_cnt, 0)                     cur_pay_cnt,\n" +
                "                nvl(ali_pay_cnt, 0)                     ali_pay_cnt,\n" +
                "                nvl(wechat_pay_cnt, 0)                  wechat_pay_cnt,\n" +
                "                nvl(other_pay_cnt, 0)                   other_pay_cnt,\n" +
                "                nvl(cur_merchant_pay_cnt, 0)            cur_merchant_pay_cnt,\n" +
                "                nvl(cur_pay_close_cnt, 0)               cur_pay_close_cnt,\n" +
                "                nvl(cur_merchant_pay_close_cnt, 0)      cur_merchant_pay_close_cnt,\n" +
                "                nvl(cur_merchant_shunt_cnt, 0)          cur_merchant_shunt_cnt,\n" +
                "                nvl(cur_merchant_accept_cnt, 0)         cur_merchant_accept_cnt,\n" +
                "                nvl(cur_send_cnt, 0)                    cur_send_cnt,\n" +
                "                nvl(cur_merchant_send_cnt, 0)           cur_merchant_send_cnt\n" +
                "         from t4 full\n" +
                "                  join t5\n" +
                "                       on t4.mini_type = t5.mini_type and t4.is_on_rent = t5.is_on_rent and\n" +
                "                          t4.quotient_name = t5.quotient_name and t4.scene = t5.scene and\n" +
                "                          t4.finance_type = t5.finance_type and t4.customer_type = t5.customer_type),\n" +
                "     t7 as (\n" +
                "         SELECT common_rent_flag                                                                 is_on_rent,\n" +
                "                cl.businessChannel                                                               mini_type,\n" +
                "                cl.quotientname                                                                  quotient_name,\n" +
                "                cl.scene                                                                         scene,\n" +
                "                rate_config_type                                                                 finance_type,\n" +
                "                t.customer_type                                                                  customer_type,\n" +
                "                count(DISTINCT if(artificialauditstatus in (10, 15), a.no, null))             as central_bank_inquiry_cnt,\n" +
                "                count(DISTINCT (if(date(cl.createtime) =\n" +
                "                                   to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                                   artificialauditstatus in (10, 15), a.no,\n" +
                "                                   null)))                                                    as cur_day_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40), a.no,\n" +
                "                          null)))                                                             as central_bank_pass_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, a.no,\n" +
                "                          null)))                                                             as first_lvl_pass,\n" +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT\n" +
                "                      if(paystatus = 1 and a.merchant_id in (100, ********), a.no, null))     AS pay_cnt,\n" +
                "                count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.no,\n" +
                "                                   NULL)))                                                    AS pay_close_cnt,\n" +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.no,\n" +
                "                                   null)))                                                    as credit_audit_refuse_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90), a.no,\n" +
                "                          null)))                                                                three_lvl_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(sendstatus = 5 and a.merchant_id in (100, ********), a.no, null))       send_cnt,\n" +
                "                count(DISTINCT case\n" +
                "                                   when rtl.order_id is not null then a.no\n" +
                "                                   else null end)                                             as merchant_shunt_cnt,\n" +
                "                count(DISTINCT case\n" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.no\n" +
                "                                   else null end)                                             as merchant_accept_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      if(paystatus = 1 and a.merchant_id not in (100, ********), a.no, null)) AS merchant_pay_cnt,\n" +
                "                count(DISTINCT (CASE\n" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5\n" +
                "                                        THEN a.no\n" +
                "                                    ELSE NULL END))                                           AS merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id not in (100, ********) and sendstatus = 5, a.no, null))   merchant_send_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join  rent_order a\n" +
                "                             on cl.loanno = a.no and a.parent_id = 0\n" +
                "                               AND a.ds = (select ds from dt)\n" +
                "                               and a.is_deleted = 0\n" +
                "                               and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and\n" +
                "                                             to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =\n" +
                "                                             " + time + "\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "                          and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t8 as (\n" +
                "         select common_rent_flag          is_on_rent,\n" +
                "                cl.businessChannel        mini_type,\n" +
                "                cl.quotientname           quotient_name,\n" +
                "                cl.scene                  scene,\n" +
                "                rate_config_type          finance_type,\n" +
                "                t.customer_type           customer_type,\n" +
                "                count(DISTINCT a.no)      risk_cnt,\n" +
                "                count(DISTINCT cl.loanno) preposition_risk_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20), a.no,\n" +
                "                          0)))            pass_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30), a.no,\n" +
                "                          0)))            anti_fraud_pass_cnt\n" +
                "         from cl_loan cl\n" +
                "                  left join rent_order a\n" +
                "                            on cl.loanno = a.no and a.ds = (select ds from dt)  and a.mini_type is not null\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                            on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         WHERE cl.ds = (select ds from dt)\n" +
                "           and cl.parentno = ''\n" +
                "           and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "           and date(cl.createtime) = " + time + "\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t9 as (\n" +
                "         select nvl(t7.mini_type, t8.mini_type)         business_channel,\n" +
                "                nvl(t7.is_on_rent, t8.is_on_rent)       is_on_rent,\n" +
                "                nvl(t7.quotient_name, t8.quotient_name) quotient_name,\n" +
                "                nvl(t7.scene, t8.scene)                 scene,\n" +
                "                nvl(t7.finance_type, t8.finance_type)   finance_type,\n" +
                "                nvl(t7.customer_type, t8.customer_type) customer_type,\n" +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt,\n" +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt,\n" +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt,\n" +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass,\n" +
                "                nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                nvl(pay_cnt, 0)                         pay_cnt,\n" +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt,\n" +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt,\n" +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt,\n" +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt,\n" +
                "                nvl(send_cnt, 0)                        send_cnt,\n" +
                "                nvl(risk_cnt, 0)                        risk_cnt,\n" +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt,\n" +
                "                nvl(pass_cnt, 0)                        pass_cnt,\n" +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt,\n" +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt,\n" +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt,\n" +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt,\n" +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt,\n" +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt\n" +
                "         from t7 full\n" +
                "                  join t8\n" +
                "                       on t7.mini_type = t8.mini_type and t7.is_on_rent = t8.is_on_rent and\n" +
                "                          t7.quotient_name = t8.quotient_name and t7.scene = t8.scene and\n" +
                "                          t7.finance_type = t8.finance_type and t7.customer_type = t8.customer_type),\n" +
                "     t10 as (\n" +
                "         SELECT common_rent_flag                                            is_on_rent,\n" +
                "                cl.businessChannel                                          mini_type,\n" +
                "                cl.quotientname                                             quotient_name,\n" +
                "                cl.scene                                                    scene,\n" +
                "                rate_config_type                                            finance_type,\n" +
                "                t.customer_type                                             customer_type,\n" +
                "                count(DISTINCT if(a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no,\n" +
                "                                  NULL))   AS                               cur_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, a.no,\n" +
                "                                   null))) as                               ali_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, a.no,\n" +
                "                                   null))) as                               wechat_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                   date(a.payment_time) = " + time + " and a.merchant_id in (100, ********) and\n" +
                "                                   a.termination <> 5, a.no,\n" +
                "                                   null))) as                               other_pay_cnt,\n" +
                "                count(DISTINCT if(a.merchant_id not in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no,\n" +
                "                                  NULL))   AS                               cur_merchant_pay_cnt,\n" +
                "                count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and\n" +
                "                                   date(a.payment_time) = " + time + ", a.no,\n" +
                "                                   NULL))) AS                               cur_pay_close_cnt,\n" +
                "                count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and\n" +
                "                                  date(a.payment_time) = " + time + ", a.no,\n" +
                "                                  NULL))   AS                               cur_merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(date(rtl.create_time) = " + time + ", a.no, null)) cur_merchant_shunt_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + ", a.no,\n" +
                "                         null))                                             cur_merchant_accept_cnt\n" +
                "         FROM  cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no and  a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t11 as (\n" +
                "         select common_rent_flag                                   is_on_rent,\n" +
                "                cl.businessChannel                                 mini_type,\n" +
                "                cl.quotientname                                    quotient_name,\n" +
                "                cl.scene                                           scene,\n" +
                "                rate_config_type                                   finance_type,\n" +
                "                t.customer_type                                    customer_type,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id in (100, ********) and date(send_time) = " + time + " and a.termination <> 5,\n" +
                "                         a.no, null))                              cur_send_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(send_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no, null)) cur_merchant_send_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join  rent_order a  on cl.loanno = a.no and  a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_logistics rol\n" +
                "                             on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t12 as (\n" +
                "         select nvl(t10.mini_type, t11.mini_type)         business_channel,\n" +
                "                nvl(t10.is_on_rent, t11.is_on_rent)       is_on_rent,\n" +
                "                nvl(t10.quotient_name, t11.quotient_name) quotient_name,\n" +
                "                nvl(t10.scene, t11.scene)                 scene,\n" +
                "                nvl(t10.finance_type, t11.finance_type)   finance_type,\n" +
                "                nvl(t10.customer_type, t11.customer_type) customer_type,\n" +
                "                nvl(cur_pay_cnt, 0)                       cur_pay_cnt,\n" +
                "                nvl(ali_pay_cnt, 0)                       ali_pay_cnt,\n" +
                "                nvl(wechat_pay_cnt, 0)                    wechat_pay_cnt,\n" +
                "                nvl(other_pay_cnt, 0)                     other_pay_cnt,\n" +
                "                nvl(cur_merchant_pay_cnt, 0)              cur_merchant_pay_cnt,\n" +
                "                nvl(cur_pay_close_cnt, 0)                 cur_pay_close_cnt,\n" +
                "                nvl(cur_merchant_pay_close_cnt, 0)        cur_merchant_pay_close_cnt,\n" +
                "                nvl(cur_merchant_shunt_cnt, 0)            cur_merchant_shunt_cnt,\n" +
                "                nvl(cur_merchant_accept_cnt, 0)           cur_merchant_accept_cnt,\n" +
                "                nvl(cur_send_cnt, 0)                      cur_send_cnt,\n" +
                "                nvl(cur_merchant_send_cnt, 0)             cur_merchant_send_cnt\n" +
                "         from t10 full\n" +
                "                  join t11\n" +
                "                       on t10.mini_type = t11.mini_type and t10.is_on_rent = t11.is_on_rent and\n" +
                "                          t10.quotient_name = t11.quotient_name and t10.scene = t11.scene and\n" +
                "                          t10.finance_type = t11.finance_type and t10.customer_type = t11.customer_type)";
        String body = "select count_day,\n" +
                "       mini_type business_channel,\n" +
                "       type,\n" +
                "       is_on_rent,\n" +
                "       quotient_name,\n" +
                "       scene,\n" +
                "       nvl(finance_type,-5) finance_type,\n" +
                "       customer_type,\n" +
                "       risk_opinion,\n" +
                "       central_bank_inquiry_cnt,\n" +
                "       cur_day_cnt,\n" +
                "       central_bank_pass_cnt,\n" +
                "       first_lvl_pass,\n" +
                "       labour_audit_cnt,\n" +
                "       total_pass_cnt,\n" +
                "       pay_cnt,\n" +
                "       ali_pay_cnt,\n" +
                "       wechat_pay_cnt,\n" +
                "       other_pay_cnt,\n" +
                "       pay_close_cnt,\n" +
                "       credit_audit_pass_cnt,\n" +
                "       credit_audit_refuse_cnt,\n" +
                "       three_lvl_cnt,\n" +
                "       send_cnt,\n" +
                "       risk_cnt,\n" +
                "       preposition_risk_cnt,\n" +
                "       pass_cnt,\n" +
                "       anti_fraud_pass_cnt,\n" +
                "       merchant_pay_close_cnt,\n" +
                "       merchant_pay_cnt,\n" +
                "       merchant_accept_cnt,\n" +
                "       merchant_send_cnt,\n" +
                "       merchant_shunt_cnt,\n" +
                "       cur_pay_cnt,\n" +
                "       cur_merchant_pay_cnt,\n" +
                "       cur_pay_close_cnt,\n" +
                "       cur_merchant_pay_close_cnt,\n" +
                "       cur_merchant_shunt_cnt,\n" +
                "       cur_merchant_accept_cnt,\n" +
                "       cur_send_cnt,\n" +
                "       cur_merchant_send_cnt\n" +
                "from (\n" +
                "         select if(a.business_channel = 1020, 1020, rmc.mini_type) mini_type, a.*\n" +
                "         from (select " + time + "                                                           count_day,\n" +
                "                      0                                                                    type,\n" +
                "                      if(nvl(t3.is_on_rent, t6.is_on_rent) <> 10, '0', '1')                is_on_rent,\n" +
                "                      nvl(t3.business_channel, t6.business_channel)                        business_channel,\n" +
                "                      if(nvl(t3.quotient_name, t6.quotient_name) = '', '未标记上导流商',\n" +
                "                         nvl(t3.quotient_name, t6.quotient_name))                          quotient_name,\n" +
                "                      if(nvl(t3.scene, t6.scene) = '', '未标记上导流商', nvl(t3.scene, t6.scene)) scene,\n" +
                "                      nvl(t3.finance_type, t6.finance_type)                                finance_type,\n" +
                "                      if(nvl(t3.customer_type, t6.customer_type) = '', 3,\n" +
                "                         nvl(t3.customer_type, t6.customer_type))                          customer_type,\n" +
                "                      ''                                                                   risk_opinion,\n" +
                "                      nvl(central_bank_inquiry_cnt, 0)                                     central_bank_inquiry_cnt,\n" +
                "                      nvl(cur_day_cnt, 0)                                                  cur_day_cnt,\n" +
                "                      nvl(central_bank_pass_cnt, 0)                                        central_bank_pass_cnt,\n" +
                "                      nvl(first_lvl_pass, 0)                                               first_lvl_pass,\n" +
                "                      nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                      nvl(first_lvl_pass, 0) +  nvl(credit_audit_pass_cnt, 0)               total_pass_cnt,\n" +
                "                      nvl(pay_cnt, 0)                                                      pay_cnt,\n" +
                "                      nvl(ali_pay_cnt, 0)                                                  ali_pay_cnt,\n" +
                "                      nvl(wechat_pay_cnt, 0)                                               wechat_pay_cnt,\n" +
                "                      nvl(other_pay_cnt, 0)                                                other_pay_cnt,\n" +
                "                      nvl(pay_close_cnt, 0)                                                pay_close_cnt,\n" +
                "                      nvl(credit_audit_pass_cnt, 0)                                        credit_audit_pass_cnt,\n" +
                "                      nvl(credit_audit_refuse_cnt, 0)                                      credit_audit_refuse_cnt,\n" +
                "                      nvl(three_lvl_cnt, 0)                                                three_lvl_cnt,\n" +
                "                      nvl(send_cnt, 0)                                                     send_cnt,\n" +
                "                      nvl(risk_cnt, 0)                                                     risk_cnt,\n" +
                "                      nvl(preposition_risk_cnt, 0)                                         preposition_risk_cnt,\n" +
                "                      nvl(pass_cnt, 0)                                                     pass_cnt,\n" +
                "                      nvl(anti_fraud_pass_cnt, 0)                                          anti_fraud_pass_cnt,\n" +
                "                      nvl(merchant_pay_close_cnt, 0)                                       merchant_pay_close_cnt,\n" +
                "                      nvl(merchant_pay_cnt, 0)                                             merchant_pay_cnt,\n" +
                "                      nvl(merchant_accept_cnt, 0)                                          merchant_accept_cnt,\n" +
                "                      nvl(merchant_send_cnt, 0)                                            merchant_send_cnt,\n" +
                "                      nvl(merchant_shunt_cnt, 0)                                           merchant_shunt_cnt,\n" +
                "                      nvl(cur_pay_cnt, 0)                                                  cur_pay_cnt,\n" +
                "                      nvl(cur_merchant_pay_cnt, 0)                                         cur_merchant_pay_cnt,\n" +
                "                      nvl(cur_pay_close_cnt, 0)                                            cur_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_pay_close_cnt, 0)                                   cur_merchant_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_shunt_cnt, 0)                                       cur_merchant_shunt_cnt,\n" +
                "                      nvl(cur_merchant_accept_cnt, 0)                                      cur_merchant_accept_cnt,\n" +
                "                      nvl(cur_send_cnt, 0)                                                 cur_send_cnt,\n" +
                "                      nvl(cur_merchant_send_cnt, 0)                                        cur_merchant_send_cnt\n" +
                "               from t3 full\n" +
                "                        join t6 on t3.business_channel = t6.business_channel and t3.is_on_rent = t6.is_on_rent and\n" +
                "                                   t3.quotient_name = t6.quotient_name and t3.scene = t6.scene and\n" +
                "                                   t3.finance_type = t6.finance_type and t3.customer_type = t6.customer_type\n" +
                "               union all\n" +
                "               select " + time + "                                                             count_day,\n" +
                "                      1                                                                      type,\n" +
                "                      if(nvl(t9.is_on_rent, t12.is_on_rent) <> 10, '0', '1')                 is_on_rent,\n" +
                "                      nvl(t9.business_channel, t12.business_channel)                         business_channel,\n" +
                "                      if(nvl(t9.quotient_name, t12.quotient_name) = '', '未标记上导流商',\n" +
                "                         nvl(t9.quotient_name, t12.quotient_name))                           quotient_name,\n" +
                "                      if(nvl(t9.scene, t12.scene) = '', '未标记上导流商', nvl(t9.scene, t12.scene)) scene,\n" +
                "                      nvl(t9.finance_type, t12.finance_type)                                 finance_type,\n" +
                "                      if(nvl(t9.customer_type, t12.customer_type) = '', 3,\n" +
                "                         nvl(t9.customer_type, t12.customer_type))                           customer_type,\n" +
                "                      ''                                                                     risk_opinion,\n" +
                "                      nvl(central_bank_inquiry_cnt, 0)                                       central_bank_inquiry_cnt,\n" +
                "                      nvl(cur_day_cnt, 0)                                                    cur_day_cnt,\n" +
                "                      nvl(central_bank_pass_cnt, 0)                                          central_bank_pass_cnt,\n" +
                "                      nvl(first_lvl_pass, 0)                                                 first_lvl_pass,\n" +
                "                      nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                      nvl(first_lvl_pass, 0) +  nvl(credit_audit_pass_cnt, 0)               total_pass_cnt,\n" +
                "                      nvl(pay_cnt, 0)                                                        pay_cnt,\n" +
                "                      nvl(ali_pay_cnt, 0)                                                    ali_pay_cnt,\n" +
                "                      nvl(wechat_pay_cnt, 0)                                                 wechat_pay_cnt,\n" +
                "                      nvl(other_pay_cnt, 0)                                                  other_pay_cnt,\n" +
                "                      nvl(pay_close_cnt, 0)                                                  pay_close_cnt,\n" +
                "                      nvl(credit_audit_pass_cnt, 0)                                          credit_audit_pass_cnt,\n" +
                "                      nvl(credit_audit_refuse_cnt, 0)                                        credit_audit_refuse_cnt,\n" +
                "                      nvl(three_lvl_cnt, 0)                                                  three_lvl_cnt,\n" +
                "                      nvl(send_cnt, 0)                                                       send_cnt,\n" +
                "                      nvl(risk_cnt, 0)                                                       risk_cnt,\n" +
                "                      nvl(preposition_risk_cnt, 0)                                           preposition_risk_cnt,\n" +
                "                      nvl(pass_cnt, 0)                                                       pass_cnt,\n" +
                "                      nvl(anti_fraud_pass_cnt, 0)                                            anti_fraud_pass_cnt,\n" +
                "                      nvl(merchant_pay_close_cnt, 0)                                         merchant_pay_close_cnt,\n" +
                "                      nvl(merchant_pay_cnt, 0)                                               merchant_pay_cnt,\n" +
                "                      nvl(merchant_accept_cnt, 0)                                            merchant_accept_cnt,\n" +
                "                      nvl(merchant_send_cnt, 0)                                              merchant_send_cnt,\n" +
                "                      nvl(merchant_shunt_cnt, 0)                                             merchant_shunt_cnt,\n" +
                "                      nvl(cur_pay_cnt, 0)                                                    cur_pay_cnt,\n" +
                "                      nvl(cur_merchant_pay_cnt, 0)                                           cur_merchant_pay_cnt,\n" +
                "                      nvl(cur_pay_close_cnt, 0)                                              cur_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_pay_close_cnt, 0)                                     cur_merchant_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_shunt_cnt, 0)                                         cur_merchant_shunt_cnt,\n" +
                "                      nvl(cur_merchant_accept_cnt, 0)                                        cur_merchant_accept_cnt,\n" +
                "                      nvl(cur_send_cnt, 0)                                                   cur_send_cnt,\n" +
                "                      nvl(cur_merchant_send_cnt, 0)                                          cur_merchant_send_cnt\n" +
                "               from t9 full\n" +
                "                        join t12 on t9.business_channel = t12.business_channel and t9.is_on_rent = t12.is_on_rent and\n" +
                "                                    t9.quotient_name = t12.quotient_name and t9.scene = t12.scene and\n" +
                "                                    t9.finance_type = t12.finance_type and t9.customer_type = t12.customer_type) a\n" +
                "                  left join rent_mini_config rmc on rmc.business_channel = a.business_channel\n" +
                "     )\n" +
                "where mini_type is not null\n" +
                "   or mini_type <> ''";
        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        Integer size = getCount(countSql);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<RiskRentInfoDO> list = Lists.newArrayList();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = "    order by count_day,  " +
                    "             type,  " +
                    "             business_channel,  " +
                    "             customer_type,  " +
                    "             finance_type,  " +
                    "             scene,  " +
                    "             risk_opinion,  " +
                    "             is_on_rent,  " +
                    "             quotient_name" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            ;
            String pageSql = prefix + body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            list.addAll(assemble(records));
        }
        return list;
    }

    private List<RiskRentInfoDO> getByCondition(String time) {
        String prefix = "with dt as (select date_format(now(), 'yyyymmdd') as ds),\n" +
                "     t as (select gg.customer_id as customer_id,\n" +
                "                  (case\n" +
                "                       when gg.lastCreateTime is null or datediff(ro.create_time, gg.lastCreateTime) > 180 then 3\n" +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 180 and\n" +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 30 then 2\n" +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 30 and\n" +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 0 then 1\n" +
                "                       else 4 end)  customer_type\n" +
                "           from rent_order ro\n" +
                "                    left join (select customer_id, max(a.create_time) lastCreateTime\n" +
                "                               from (select ro.customer_id,\n" +
                "                                            ro.create_time,\n" +
                "                                            row_number() over (PARTITION by ro.customer_id order by ro.create_time desc) num\n" +
                "                                     from rent_order ro\n" +
                "                                              left join rent_customer rc\n" +
                "                                                        on ro.customer_id = rc.id and rc.ds = (select ds from dt)\n" +
                "                                              left join rc_user ru\n" +
                "                                                        on rc.id_card_no = ru.idcardno and ru.ds = (select ds from dt)\n" +
                "                                              left join cl_loan cl on cl.customerid = ru.id and cl.ds = (select ds from dt)\n" +
                "                                     where ro.ds = (select ds from dt)) a\n" +
                "                               where a.num <> 1\n" +
                "                               group by a.customer_id) gg on gg.customer_id = ro.customer_id\n" +
                "           where ro.ds = (select ds from dt)),\n" +
                "     t1 as (SELECT common_rent_flag                                                     is_on_rent,\n" +
                "                   cl.businessChannel                                                   mini_type,\n" +
                "                   cl.quotientname                                                      quotient_name,\n" +
                "                   cl.scene                                                             scene,\n" +
                "                   rate_config_type                                                     finance_type,\n" +
                "                   t.customer_type                                                      customer_type,\n" +
                "                   count(DISTINCT\n" +
                "                         if(artificialauditstatus in (10, 15), cl.customerid, null)) as central_bank_inquiry_cnt,\n" +
                "                   count(DISTINCT (if(date(cl.createtime) =\n" +
                "                                      to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                                      artificialauditstatus in (10, 15), cl.customerid,\n" +
                "                                      null)))                                        as cur_day_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40),\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                 as central_bank_pass_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10,\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                 as first_lvl_pass,\n" +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, cl.customerid," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                                     null))                                          AS pay_cnt,\n" +
                "                   count(DISTINCT\n" +
                "                         (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                             NULL)))                                                 AS pay_close_cnt,\n" +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, cl.customerid," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                   COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, cl.customerid,\n" +
                "                                      null)))                                        as credit_audit_refuse_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90),\n" +
                "                             cl.customerid,\n" +
                "                             null)))                                                    three_lvl_cnt,\n" +
                "                   COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), cl.customerid,\n" +
                "                                     null))                                             send_cnt,\n" +
                "                   count(DISTINCT case\n" +
                "                                      when rtl.order_id is not null then cl.customerid\n" +
                "                                      else null end)                                 as merchant_shunt_cnt,\n" +
                "                   count(DISTINCT case\n" +
                "                                      when a.merchant_id not in (100, ********) and merchant_transfer = 10\n" +
                "                                          then cl.customerid\n" +
                "                                      else null end)                                 as merchant_accept_cnt,\n" +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), cl.customerid,\n" +
                "                                     null))                                          AS merchant_pay_cnt,\n" +
                "                   count(DISTINCT (CASE\n" +
                "                                       WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5\n" +
                "                                           THEN cl.customerid\n" +
                "                                       ELSE NULL END))                               AS merchant_pay_close_cnt,\n" +
                "                   COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, cl.customerid,\n" +
                "                                     null))                                             merchant_send_cnt\n" +
                "            FROM cl_loan cl\n" +
                "                     left join rent_order a on cl.loanno = a.no and a.parent_id = 0\n" +
                "                AND a.ds = (select ds from dt)\n" +
                "                and a.is_deleted = 0\n" +
                "                and a.type = 1\n" +
                "                     left JOIN rent_order_finance_detail c\n" +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                     left join rent_order_infomore roi\n" +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                     left join rent_order_merchant_transfer_log rtl\n" +
                "                               on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                     INNER join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and\n" +
                "                                               to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =\n" +
                "                                               " + time + "\n" +
                "                     left join t on t.customer_id = a.customer_id\n" +
                "            where cl.ds = (select ds from dt)\n" +
                "                   and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "            group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t2 as (select common_rent_flag              is_on_rent,\n" +
                "                   cl.businessChannel            mini_type,\n" +
                "                   cl.quotientname               quotient_name,\n" +
                "                   cl.scene                      scene,\n" +
                "                   rate_config_type              finance_type,\n" +
                "                   t.customer_type               customer_type,\n" +
                "                   count(DISTINCT cl.customerid) risk_cnt,\n" +
                "                   count(DISTINCT cl.customerid) preposition_risk_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20),\n" +
                "                             cl.customerid, 0))) pass_cnt,\n" +
                "                   COUNT(DISTINCT\n" +
                "                         (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30),\n" +
                "                             cl.customerid, 0))) anti_fraud_pass_cnt\n" +
                "            from cl_loan cl\n" +
                "                     left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt)  and a.mini_type is not null\n" +
                "                     left join rent_order_infomore roi\n" +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                     left JOIN rent_order_finance_detail c\n" +
                "                               on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                     left join t on t.customer_id = a.customer_id\n" +
                "            WHERE cl.ds = (select ds from dt)\n" +
                "              and cl.parentno = ''\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "              and date(cl.createtime) = " + time + "\n" +
                "            group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t3 as (\n" +
                "         select nvl(t1.mini_type, t2.mini_type)         business_channel,\n" +
                "                nvl(t1.is_on_rent, t2.is_on_rent)       is_on_rent,\n" +
                "                nvl(t1.quotient_name, t2.quotient_name) quotient_name,\n" +
                "                nvl(t1.scene, t2.scene)                 scene,\n" +
                "                nvl(t1.finance_type, t2.finance_type)   finance_type,\n" +
                "                nvl(t1.customer_type, t2.customer_type) customer_type,\n" +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt,\n" +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt,\n" +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt,\n" +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass,\n" +
                "                nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                nvl(pay_cnt, 0)                         pay_cnt,\n" +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt,\n" +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt,\n" +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt,\n" +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt,\n" +
                "                nvl(send_cnt, 0)                        send_cnt,\n" +
                "                nvl(risk_cnt, 0)                        risk_cnt,\n" +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt,\n" +
                "                nvl(pass_cnt, 0)                        pass_cnt,\n" +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt,\n" +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt,\n" +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt,\n" +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt,\n" +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt,\n" +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt\n" +
                "         from t1 full\n" +
                "                  join t2\n" +
                "                       on t1.mini_type = t2.mini_type and t1.is_on_rent = t2.is_on_rent and\n" +
                "                          t1.quotient_name = t2.quotient_name and t1.scene = t2.scene and\n" +
                "                          t1.finance_type = t2.finance_type and t1.customer_type = t2.customer_type),\n" +
                "     t4 as (\n" +
                "         SELECT common_rent_flag                                                     is_on_rent,\n" +
                "                cl.businessChannel                                                   mini_type,\n" +
                "                cl.quotientname                                                      quotient_name,\n" +
                "                cl.scene                                                             scene,\n" +
                "                rate_config_type                                                     finance_type,\n" +
                "                t.customer_type                                                      customer_type,\n" +
                "                count(DISTINCT if(a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        ali_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        wechat_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                   date(a.payment_time) = " + time + " and a.merchant_id in (100, ********) and\n" +
                "                                   a.termination <> 5, cl.customerid,\n" +
                "                                   null))) as                                        other_pay_cnt,\n" +
                "                count(DISTINCT if(a.merchant_id not in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_merchant_pay_cnt,\n" +
                "                count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and\n" +
                "                                   date(a.payment_time) = " + time + ", cl.customerid,\n" +
                "                                   NULL))) AS                                        cur_pay_close_cnt,\n" +
                "                count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and\n" +
                "                                  date(a.payment_time) = " + time + ", cl.customerid,\n" +
                "                                  NULL))   AS                                        cur_merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(date(rtl.create_time) = " + time + ", cl.customerid, null)) cur_merchant_shunt_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + ",\n" +
                "                                  cl.customerid, null))                              cur_merchant_accept_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no  and a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where\n" +
                "                cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t5 as (\n" +
                "         select common_rent_flag                                            is_on_rent,\n" +
                "                cl.businessChannel                                          mini_type,\n" +
                "                cl.quotientname                                             quotient_name,\n" +
                "                cl.scene                                                    scene,\n" +
                "                rate_config_type                                            finance_type,\n" +
                "                t.customer_type                                             customer_type,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id in (100, ********) and date(send_time) = " + time + " and a.termination <> 5,\n" +
                "                         cl.customerid, null))                              cur_send_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(send_time) = " + time + " and\n" +
                "                                  a.termination <> 5, cl.customerid, null)) cur_merchant_send_cnt\n" +
                "         FROM  cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no and a.parent_id = 0\n" +
                "                           AND a.ds = (select ds from dt)\n" +
                "                           and a.is_deleted = 0\n" +
                "                           and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_logistics rol\n" +
                "                             on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "               and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t6 as (\n" +
                "         select nvl(t4.mini_type, t5.mini_type)         business_channel,\n" +
                "                nvl(t4.is_on_rent, t5.is_on_rent)       is_on_rent,\n" +
                "                nvl(t4.quotient_name, t5.quotient_name) quotient_name,\n" +
                "                nvl(t4.scene, t5.scene)                 scene,\n" +
                "                nvl(t4.finance_type, t5.finance_type)   finance_type,\n" +
                "                nvl(t4.customer_type, t5.customer_type) customer_type,\n" +
                "                nvl(cur_pay_cnt, 0)                     cur_pay_cnt,\n" +
                "                nvl(ali_pay_cnt, 0)                     ali_pay_cnt,\n" +
                "                nvl(wechat_pay_cnt, 0)                  wechat_pay_cnt,\n" +
                "                nvl(other_pay_cnt, 0)                   other_pay_cnt,\n" +
                "                nvl(cur_merchant_pay_cnt, 0)            cur_merchant_pay_cnt,\n" +
                "                nvl(cur_pay_close_cnt, 0)               cur_pay_close_cnt,\n" +
                "                nvl(cur_merchant_pay_close_cnt, 0)      cur_merchant_pay_close_cnt,\n" +
                "                nvl(cur_merchant_shunt_cnt, 0)          cur_merchant_shunt_cnt,\n" +
                "                nvl(cur_merchant_accept_cnt, 0)         cur_merchant_accept_cnt,\n" +
                "                nvl(cur_send_cnt, 0)                    cur_send_cnt,\n" +
                "                nvl(cur_merchant_send_cnt, 0)           cur_merchant_send_cnt\n" +
                "         from t4 full\n" +
                "                  join t5\n" +
                "                       on t4.mini_type = t5.mini_type and t4.is_on_rent = t5.is_on_rent and\n" +
                "                          t4.quotient_name = t5.quotient_name and t4.scene = t5.scene and\n" +
                "                          t4.finance_type = t5.finance_type and t4.customer_type = t5.customer_type),\n" +
                "     t7 as (\n" +
                "         SELECT common_rent_flag                                                                 is_on_rent,\n" +
                "                cl.businessChannel                                                               mini_type,\n" +
                "                cl.quotientname                                                                  quotient_name,\n" +
                "                cl.scene                                                                         scene,\n" +
                "                rate_config_type                                                                 finance_type,\n" +
                "                t.customer_type                                                                  customer_type,\n" +
                "                count(DISTINCT if(artificialauditstatus in (10, 15), a.no, null))             as central_bank_inquiry_cnt,\n" +
                "                count(DISTINCT (if(date(cl.createtime) =\n" +
                "                                   to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and\n" +
                "                                   artificialauditstatus in (10, 15), a.no,\n" +
                "                                   null)))                                                    as cur_day_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40), a.no,\n" +
                "                          null)))                                                             as central_bank_pass_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, a.no,\n" +
                "                          null)))                                                             as first_lvl_pass,\n" +
                "                count(DISTINCT (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10)  and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as labour_audit_cnt," +
                "                count(DISTINCT\n" +
                "                      if(paystatus = 1 and a.merchant_id in (100, ********), a.no, null))     AS pay_cnt,\n" +
                "                count(DISTINCT (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.no,\n" +
                "                                   NULL)))                                                    AS pay_close_cnt,\n" +
                "                COUNT(DISTINCT (if(riskFicoStatus>=65 and artificialAuditStatus=10 and replace(riskopinion,'风控等级','') >10, a.no," +
                "                                   null)))                                                                               as credit_audit_pass_cnt,"  +
                "                COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.no,\n" +
                "                                   null)))                                                    as credit_audit_refuse_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90), a.no,\n" +
                "                          null)))                                                                three_lvl_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(sendstatus = 5 and a.merchant_id in (100, ********), a.no, null))       send_cnt,\n" +
                "                count(DISTINCT case\n" +
                "                                   when rtl.order_id is not null then a.no\n" +
                "                                   else null end)                                             as merchant_shunt_cnt,\n" +
                "                count(DISTINCT case\n" +
                "                                   when a.merchant_id not in (100, ********) and merchant_transfer = 10 then a.no\n" +
                "                                   else null end)                                             as merchant_accept_cnt,\n" +
                "                count(DISTINCT\n" +
                "                      if(paystatus = 1 and a.merchant_id not in (100, ********), a.no, null)) AS merchant_pay_cnt,\n" +
                "                count(DISTINCT (CASE\n" +
                "                                    WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5\n" +
                "                                        THEN a.no\n" +
                "                                    ELSE NULL END))                                           AS merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id not in (100, ********) and sendstatus = 5, a.no, null))   merchant_send_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join  rent_order a\n" +
                "                             on cl.loanno = a.no and a.parent_id = 0\n" +
                "                               AND a.ds = (select ds from dt)\n" +
                "                               and a.is_deleted = 0\n" +
                "                               and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and\n" +
                "                                             to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =\n" +
                "                                             " + time + "\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "                          and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t8 as (\n" +
                "         select common_rent_flag          is_on_rent,\n" +
                "                cl.businessChannel        mini_type,\n" +
                "                cl.quotientname           quotient_name,\n" +
                "                cl.scene                  scene,\n" +
                "                rate_config_type          finance_type,\n" +
                "                t.customer_type           customer_type,\n" +
                "                count(DISTINCT a.no)      risk_cnt,\n" +
                "                count(DISTINCT cl.loanno) preposition_risk_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20), a.no,\n" +
                "                          0)))            pass_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30), a.no,\n" +
                "                          0)))            anti_fraud_pass_cnt\n" +
                "         from cl_loan cl\n" +
                "                  left join rent_order a\n" +
                "                            on cl.loanno = a.no and a.ds = (select ds from dt)  and a.mini_type is not null\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                            on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         WHERE cl.ds = (select ds from dt)\n" +
                "           and cl.parentno = ''\n" +
                "           and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "           and date(cl.createtime) = " + time + "\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t9 as (\n" +
                "         select nvl(t7.mini_type, t8.mini_type)         business_channel,\n" +
                "                nvl(t7.is_on_rent, t8.is_on_rent)       is_on_rent,\n" +
                "                nvl(t7.quotient_name, t8.quotient_name) quotient_name,\n" +
                "                nvl(t7.scene, t8.scene)                 scene,\n" +
                "                nvl(t7.finance_type, t8.finance_type)   finance_type,\n" +
                "                nvl(t7.customer_type, t8.customer_type) customer_type,\n" +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt,\n" +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt,\n" +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt,\n" +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass,\n" +
                "                nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                nvl(pay_cnt, 0)                         pay_cnt,\n" +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt,\n" +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt,\n" +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt,\n" +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt,\n" +
                "                nvl(send_cnt, 0)                        send_cnt,\n" +
                "                nvl(risk_cnt, 0)                        risk_cnt,\n" +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt,\n" +
                "                nvl(pass_cnt, 0)                        pass_cnt,\n" +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt,\n" +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt,\n" +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt,\n" +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt,\n" +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt,\n" +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt\n" +
                "         from t7 full\n" +
                "                  join t8\n" +
                "                       on t7.mini_type = t8.mini_type and t7.is_on_rent = t8.is_on_rent and\n" +
                "                          t7.quotient_name = t8.quotient_name and t7.scene = t8.scene and\n" +
                "                          t7.finance_type = t8.finance_type and t7.customer_type = t8.customer_type),\n" +
                "     t10 as (\n" +
                "         SELECT common_rent_flag                                            is_on_rent,\n" +
                "                cl.businessChannel                                          mini_type,\n" +
                "                cl.quotientname                                             quotient_name,\n" +
                "                cl.scene                                                    scene,\n" +
                "                rate_config_type                                            finance_type,\n" +
                "                t.customer_type                                             customer_type,\n" +
                "                count(DISTINCT if(a.merchant_id in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no,\n" +
                "                                  NULL))   AS                               cur_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'ALIPAY' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, a.no,\n" +
                "                                   null))) as                               ali_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code = 'WECHAT' and date(a.payment_time) = " + time + " and\n" +
                "                                   a.merchant_id in (100, ********) and a.termination <> 5, a.no,\n" +
                "                                   null))) as                               wechat_pay_cnt,\n" +
                "                count(DISTINCT (if(rmc.platform_code not in ('ALIPAY', 'WECHAT') and\n" +
                "                                   date(a.payment_time) = " + time + " and a.merchant_id in (100, ********) and\n" +
                "                                   a.termination <> 5, a.no,\n" +
                "                                   null))) as                               other_pay_cnt,\n" +
                "                count(DISTINCT if(a.merchant_id not in (100, ********) and date(a.payment_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no,\n" +
                "                                  NULL))   AS                               cur_merchant_pay_cnt,\n" +
                "                count(DISTINCT (if(a.termination = 5 and a.merchant_id in (100, ********) and\n" +
                "                                   date(a.payment_time) = " + time + ", a.no,\n" +
                "                                   NULL))) AS                               cur_pay_close_cnt,\n" +
                "                count(DISTINCT if(a.termination = 5 and a.merchant_id not in (100, ********) and\n" +
                "                                  date(a.payment_time) = " + time + ", a.no,\n" +
                "                                  NULL))   AS                               cur_merchant_pay_close_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(date(rtl.create_time) = " + time + ", a.no, null)) cur_merchant_shunt_cnt,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id not in (100, ********) and date(rtl.operate_time) = " + time + ", a.no,\n" +
                "                         null))                                             cur_merchant_accept_cnt\n" +
                "         FROM  cl_loan cl\n" +
                "                  left join rent_order a on cl.loanno = a.no and  a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_merchant_transfer_log rtl\n" +
                "                            on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt)\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t11 as (\n" +
                "         select common_rent_flag                                   is_on_rent,\n" +
                "                cl.businessChannel                                 mini_type,\n" +
                "                cl.quotientname                                    quotient_name,\n" +
                "                cl.scene                                           scene,\n" +
                "                rate_config_type                                   finance_type,\n" +
                "                t.customer_type                                    customer_type,\n" +
                "                COUNT(DISTINCT\n" +
                "                      if(a.merchant_id in (100, ********) and date(send_time) = " + time + " and a.termination <> 5,\n" +
                "                         a.no, null))                              cur_send_cnt,\n" +
                "                COUNT(DISTINCT if(a.merchant_id not in (100, ********) and date(send_time) = " + time + " and\n" +
                "                                  a.termination <> 5, a.no, null)) cur_merchant_send_cnt\n" +
                "         FROM cl_loan cl\n" +
                "                  left join  rent_order a  on cl.loanno = a.no and  a.parent_id = 0\n" +
                "                       AND a.ds = (select ds from dt)\n" +
                "                       and a.is_deleted = 0\n" +
                "                       and a.type = 1\n" +
                "                  left JOIN rent_order_finance_detail c\n" +
                "                             on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0\n" +
                "                  left join rent_order_logistics rol\n" +
                "                             on a.id = rol.order_id and rol.ds = (select ds from dt) and rol.is_deleted = 0\n" +
                "                  left join rent_order_infomore roi\n" +
                "                            on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0\n" +
                "                  left join t on t.customer_id = a.customer_id\n" +
                "         where  cl.ds = (select ds from dt)\n" +
                "              and cl.businessChannel not in (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019)\n" +
                "         group by cl.businessChannel, cl.quotientname, cl.scene, rate_config_type, is_on_rent, t.customer_type),\n" +
                "     t12 as (\n" +
                "         select nvl(t10.mini_type, t11.mini_type)         business_channel,\n" +
                "                nvl(t10.is_on_rent, t11.is_on_rent)       is_on_rent,\n" +
                "                nvl(t10.quotient_name, t11.quotient_name) quotient_name,\n" +
                "                nvl(t10.scene, t11.scene)                 scene,\n" +
                "                nvl(t10.finance_type, t11.finance_type)   finance_type,\n" +
                "                nvl(t10.customer_type, t11.customer_type) customer_type,\n" +
                "                nvl(cur_pay_cnt, 0)                       cur_pay_cnt,\n" +
                "                nvl(ali_pay_cnt, 0)                       ali_pay_cnt,\n" +
                "                nvl(wechat_pay_cnt, 0)                    wechat_pay_cnt,\n" +
                "                nvl(other_pay_cnt, 0)                     other_pay_cnt,\n" +
                "                nvl(cur_merchant_pay_cnt, 0)              cur_merchant_pay_cnt,\n" +
                "                nvl(cur_pay_close_cnt, 0)                 cur_pay_close_cnt,\n" +
                "                nvl(cur_merchant_pay_close_cnt, 0)        cur_merchant_pay_close_cnt,\n" +
                "                nvl(cur_merchant_shunt_cnt, 0)            cur_merchant_shunt_cnt,\n" +
                "                nvl(cur_merchant_accept_cnt, 0)           cur_merchant_accept_cnt,\n" +
                "                nvl(cur_send_cnt, 0)                      cur_send_cnt,\n" +
                "                nvl(cur_merchant_send_cnt, 0)             cur_merchant_send_cnt\n" +
                "         from t10 full\n" +
                "                  join t11\n" +
                "                       on t10.mini_type = t11.mini_type and t10.is_on_rent = t11.is_on_rent and\n" +
                "                          t10.quotient_name = t11.quotient_name and t10.scene = t11.scene and\n" +
                "                          t10.finance_type = t11.finance_type and t10.customer_type = t11.customer_type)";
        String body = "select count_day,\n" +
                "       mini_type business_channel,\n" +
                "       type,\n" +
                "       is_on_rent,\n" +
                "       quotient_name,\n" +
                "       scene,\n" +
                "       nvl(finance_type,-5) finance_type,\n" +
                "       customer_type,\n" +
                "       risk_opinion,\n" +
                "       central_bank_inquiry_cnt,\n" +
                "       cur_day_cnt,\n" +
                "       central_bank_pass_cnt,\n" +
                "       first_lvl_pass,\n" +
                "       labour_audit_cnt,\n" +
                "       total_pass_cnt,\n" +
                "       pay_cnt,\n" +
                "       ali_pay_cnt,\n" +
                "       wechat_pay_cnt,\n" +
                "       other_pay_cnt,\n" +
                "       pay_close_cnt,\n" +
                "       credit_audit_pass_cnt,\n" +
                "       credit_audit_refuse_cnt,\n" +
                "       three_lvl_cnt,\n" +
                "       send_cnt,\n" +
                "       risk_cnt,\n" +
                "       preposition_risk_cnt,\n" +
                "       pass_cnt,\n" +
                "       anti_fraud_pass_cnt,\n" +
                "       merchant_pay_close_cnt,\n" +
                "       merchant_pay_cnt,\n" +
                "       merchant_accept_cnt,\n" +
                "       merchant_send_cnt,\n" +
                "       merchant_shunt_cnt,\n" +
                "       cur_pay_cnt,\n" +
                "       cur_merchant_pay_cnt,\n" +
                "       cur_pay_close_cnt,\n" +
                "       cur_merchant_pay_close_cnt,\n" +
                "       cur_merchant_shunt_cnt,\n" +
                "       cur_merchant_accept_cnt,\n" +
                "       cur_send_cnt,\n" +
                "       cur_merchant_send_cnt\n" +
                "from (\n" +
                "         select if(a.business_channel = 1020, 1020, rmc.mini_type) mini_type, a.*\n" +
                "         from (select " + time + "                                                           count_day,\n" +
                "                      0                                                                    type,\n" +
                "                      if(nvl(t3.is_on_rent, t6.is_on_rent) <> 10, '0', '1')                is_on_rent,\n" +
                "                      nvl(t3.business_channel, t6.business_channel)                        business_channel,\n" +
                "                      if(nvl(t3.quotient_name, t6.quotient_name) = '', '未标记上导流商',\n" +
                "                         nvl(t3.quotient_name, t6.quotient_name))                          quotient_name,\n" +
                "                      if(nvl(t3.scene, t6.scene) = '', '未标记上导流商', nvl(t3.scene, t6.scene)) scene,\n" +
                "                      nvl(t3.finance_type, t6.finance_type)                                finance_type,\n" +
                "                      if(nvl(t3.customer_type, t6.customer_type) = '', 3,\n" +
                "                         nvl(t3.customer_type, t6.customer_type))                          customer_type,\n" +
                "                      ''                                                                   risk_opinion,\n" +
                "                      nvl(central_bank_inquiry_cnt, 0)                                     central_bank_inquiry_cnt,\n" +
                "                      nvl(cur_day_cnt, 0)                                                  cur_day_cnt,\n" +
                "                      nvl(central_bank_pass_cnt, 0)                                        central_bank_pass_cnt,\n" +
                "                      nvl(first_lvl_pass, 0)                                               first_lvl_pass,\n" +
                "                      nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                      nvl(first_lvl_pass, 0) +  nvl(credit_audit_pass_cnt, 0)               total_pass_cnt,\n" +
                "                      nvl(pay_cnt, 0)                                                      pay_cnt,\n" +
                "                      nvl(ali_pay_cnt, 0)                                                  ali_pay_cnt,\n" +
                "                      nvl(wechat_pay_cnt, 0)                                               wechat_pay_cnt,\n" +
                "                      nvl(other_pay_cnt, 0)                                                other_pay_cnt,\n" +
                "                      nvl(pay_close_cnt, 0)                                                pay_close_cnt,\n" +
                "                      nvl(credit_audit_pass_cnt, 0)                                        credit_audit_pass_cnt,\n" +
                "                      nvl(credit_audit_refuse_cnt, 0)                                      credit_audit_refuse_cnt,\n" +
                "                      nvl(three_lvl_cnt, 0)                                                three_lvl_cnt,\n" +
                "                      nvl(send_cnt, 0)                                                     send_cnt,\n" +
                "                      nvl(risk_cnt, 0)                                                     risk_cnt,\n" +
                "                      nvl(preposition_risk_cnt, 0)                                         preposition_risk_cnt,\n" +
                "                      nvl(pass_cnt, 0)                                                     pass_cnt,\n" +
                "                      nvl(anti_fraud_pass_cnt, 0)                                          anti_fraud_pass_cnt,\n" +
                "                      nvl(merchant_pay_close_cnt, 0)                                       merchant_pay_close_cnt,\n" +
                "                      nvl(merchant_pay_cnt, 0)                                             merchant_pay_cnt,\n" +
                "                      nvl(merchant_accept_cnt, 0)                                          merchant_accept_cnt,\n" +
                "                      nvl(merchant_send_cnt, 0)                                            merchant_send_cnt,\n" +
                "                      nvl(merchant_shunt_cnt, 0)                                           merchant_shunt_cnt,\n" +
                "                      nvl(cur_pay_cnt, 0)                                                  cur_pay_cnt,\n" +
                "                      nvl(cur_merchant_pay_cnt, 0)                                         cur_merchant_pay_cnt,\n" +
                "                      nvl(cur_pay_close_cnt, 0)                                            cur_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_pay_close_cnt, 0)                                   cur_merchant_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_shunt_cnt, 0)                                       cur_merchant_shunt_cnt,\n" +
                "                      nvl(cur_merchant_accept_cnt, 0)                                      cur_merchant_accept_cnt,\n" +
                "                      nvl(cur_send_cnt, 0)                                                 cur_send_cnt,\n" +
                "                      nvl(cur_merchant_send_cnt, 0)                                        cur_merchant_send_cnt\n" +
                "               from t3 full\n" +
                "                        join t6 on t3.business_channel = t6.business_channel and t3.is_on_rent = t6.is_on_rent and\n" +
                "                                   t3.quotient_name = t6.quotient_name and t3.scene = t6.scene and\n" +
                "                                   t3.finance_type = t6.finance_type and t3.customer_type = t6.customer_type\n" +
                "               union all\n" +
                "               select " + time + "                                                             count_day,\n" +
                "                      1                                                                      type,\n" +
                "                      if(nvl(t9.is_on_rent, t12.is_on_rent) <> 10, '0', '1')                 is_on_rent,\n" +
                "                      nvl(t9.business_channel, t12.business_channel)                         business_channel,\n" +
                "                      if(nvl(t9.quotient_name, t12.quotient_name) = '', '未标记上导流商',\n" +
                "                         nvl(t9.quotient_name, t12.quotient_name))                           quotient_name,\n" +
                "                      if(nvl(t9.scene, t12.scene) = '', '未标记上导流商', nvl(t9.scene, t12.scene)) scene,\n" +
                "                      nvl(t9.finance_type, t12.finance_type)                                 finance_type,\n" +
                "                      if(nvl(t9.customer_type, t12.customer_type) = '', 3,\n" +
                "                         nvl(t9.customer_type, t12.customer_type))                           customer_type,\n" +
                "                      ''                                                                     risk_opinion,\n" +
                "                      nvl(central_bank_inquiry_cnt, 0)                                       central_bank_inquiry_cnt,\n" +
                "                      nvl(cur_day_cnt, 0)                                                    cur_day_cnt,\n" +
                "                      nvl(central_bank_pass_cnt, 0)                                          central_bank_pass_cnt,\n" +
                "                      nvl(first_lvl_pass, 0)                                                 first_lvl_pass,\n" +
                "                      nvl(labour_audit_cnt, 0)                labour_audit_cnt,\n" +
                "                      nvl(first_lvl_pass, 0) +  nvl(credit_audit_pass_cnt, 0)               total_pass_cnt,\n" +
                "                      nvl(pay_cnt, 0)                                                        pay_cnt,\n" +
                "                      nvl(ali_pay_cnt, 0)                                                    ali_pay_cnt,\n" +
                "                      nvl(wechat_pay_cnt, 0)                                                 wechat_pay_cnt,\n" +
                "                      nvl(other_pay_cnt, 0)                                                  other_pay_cnt,\n" +
                "                      nvl(pay_close_cnt, 0)                                                  pay_close_cnt,\n" +
                "                      nvl(credit_audit_pass_cnt, 0)                                          credit_audit_pass_cnt,\n" +
                "                      nvl(credit_audit_refuse_cnt, 0)                                        credit_audit_refuse_cnt,\n" +
                "                      nvl(three_lvl_cnt, 0)                                                  three_lvl_cnt,\n" +
                "                      nvl(send_cnt, 0)                                                       send_cnt,\n" +
                "                      nvl(risk_cnt, 0)                                                       risk_cnt,\n" +
                "                      nvl(preposition_risk_cnt, 0)                                           preposition_risk_cnt,\n" +
                "                      nvl(pass_cnt, 0)                                                       pass_cnt,\n" +
                "                      nvl(anti_fraud_pass_cnt, 0)                                            anti_fraud_pass_cnt,\n" +
                "                      nvl(merchant_pay_close_cnt, 0)                                         merchant_pay_close_cnt,\n" +
                "                      nvl(merchant_pay_cnt, 0)                                               merchant_pay_cnt,\n" +
                "                      nvl(merchant_accept_cnt, 0)                                            merchant_accept_cnt,\n" +
                "                      nvl(merchant_send_cnt, 0)                                              merchant_send_cnt,\n" +
                "                      nvl(merchant_shunt_cnt, 0)                                             merchant_shunt_cnt,\n" +
                "                      nvl(cur_pay_cnt, 0)                                                    cur_pay_cnt,\n" +
                "                      nvl(cur_merchant_pay_cnt, 0)                                           cur_merchant_pay_cnt,\n" +
                "                      nvl(cur_pay_close_cnt, 0)                                              cur_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_pay_close_cnt, 0)                                     cur_merchant_pay_close_cnt,\n" +
                "                      nvl(cur_merchant_shunt_cnt, 0)                                         cur_merchant_shunt_cnt,\n" +
                "                      nvl(cur_merchant_accept_cnt, 0)                                        cur_merchant_accept_cnt,\n" +
                "                      nvl(cur_send_cnt, 0)                                                   cur_send_cnt,\n" +
                "                      nvl(cur_merchant_send_cnt, 0)                                          cur_merchant_send_cnt\n" +
                "               from t9 full\n" +
                "                        join t12 on t9.business_channel = t12.business_channel and t9.is_on_rent = t12.is_on_rent and\n" +
                "                                    t9.quotient_name = t12.quotient_name and t9.scene = t12.scene and\n" +
                "                                    t9.finance_type = t12.finance_type and t9.customer_type = t12.customer_type) a\n" +
                "                  left join rent_mini_config rmc on rmc.business_channel = a.business_channel\n" +
                "     )\n" +
                "where mini_type is not null\n" +
                "   or mini_type <> ''";
        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        Integer size = getCount(countSql);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<RiskRentInfoDO> list = Lists.newArrayList();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = "    order by count_day,  " +
                    "             type,  " +
                    "             business_channel,  " +
                    "             customer_type,  " +
                    "             finance_type,  " +
                    "             scene,  " +
                    "             risk_opinion,  " +
                    "             is_on_rent,  " +
                    "             quotient_name" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            ;
            String pageSql = prefix + body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            list.addAll(assemble(records));
        }
        return list;
    }


    /**
     * 进客群分布，不需要scene分组
     *
     * @param time 时间
     * @return {@code List<RiskRentInfoDO>}
     */
    private List<RiskRentInfoDO> getDistributionByCondition(String time) {
        String prefix = "with dt as (select date_format(now(), 'yyyymmdd') as ds), " +
                "     t as (select gg.customer_id as customer_id, " +
                "                  (case " +
                "                       when gg.lastCreateTime is null or datediff(ro.create_time, gg.lastCreateTime) > 180 then 3 " +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 180 and " +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 30 then 2 " +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 30 and " +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 0 then 1 " +
                "                       else 4 end)  customer_type " +
                "           from rent_order ro " +
                "                    left join (select customer_id, max(a.create_time) lastCreateTime " +
                "                               from (select ro.customer_id, " +
                "                                            ro.create_time, " +
                "                                            row_number() over (PARTITION by ro.customer_id order by ro.create_time desc) num " +
                "                                     from rent_order ro " +
                "                                              left join rent_customer rc " +
                "                                                        on ro.customer_id = rc.id and rc.ds = (select ds from dt) " +
                "                                              left join rc_user ru " +
                "                                                        on rc.id_card_no = ru.idcardno and ru.ds = (select ds from dt) " +
                "                                              left join cl_loan cl on cl.customerid = ru.id and cl.ds = (select ds from dt) " +
                "                                     where ro.ds = (select ds from dt)) a " +
                "                               where a.num <> 1 " +
                "                               group by a.customer_id) gg on gg.customer_id = ro.customer_id " +
                "           where ro.ds = (select ds from dt)), " +
                "     t1 as (SELECT common_rent_flag                                                                is_on_rent, " +
                "                   a.mini_type, " +
                "                   cl.quotientname                                                                 quotient_name, " +
                "                   rate_config_type                                                                finance_type, " +
                "                   riskOpinion                                                                     risk_opinion, " +
                "                   t.customer_type                                                                 customer_type, " +
                "                   count(DISTINCT " +
                "                         if(artificialauditstatus in (10, 15), a.customer_id, null))            as central_bank_inquiry_cnt, " +
                "                   count(DISTINCT (if(date(a.create_time) = " +
                "                                      to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and " +
                "                                      artificialauditstatus in (10, 15), a.customer_id, null))) as cur_day_cnt, " +
                "                   count(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40), " +
                "                             a.customer_id, " +
                "                             null)))                                                            as central_bank_pass_cnt, " +
                "                   count(DISTINCT " +
                "                         (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, " +
                "                             a.customer_id, " +
                "                             null)))                                                            as first_lvl_pass, " +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), a.customer_id, " +
                "                                     null))                                                     AS pay_cnt, " +
                "                   count(DISTINCT " +
                "                         (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.customer_id, " +
                "                             NULL)))                                                            AS pay_close_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 80 and cl.artificialauditstatus = 10) or (riskFicoStatus > 80), " +
                "                             a.customer_id, " +
                "                             null)))                                                            as credit_audit_pass_cnt, " +
                "                   COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.no, " +
                "                                      null)))                                                   as credit_audit_refuse_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90), " +
                "                             a.customer_id, " +
                "                             null)))                                                               three_lvl_cnt, " +
                "                   COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), a.customer_id, " +
                "                                     null))                                                        send_cnt, " +
                "                   count(DISTINCT case " +
                "                                      when rtl.order_id is not null then a.customer_id " +
                "                                      else null end)                                            as merchant_shunt_cnt, " +
                "                   count(DISTINCT case " +
                "                                      when a.merchant_id not in (100, ********) and merchant_transfer = 10 " +
                "                                          then a.customer_id " +
                "                                      else null end)                                            as merchant_accept_cnt, " +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), a.customer_id, " +
                "                                     null))                                                     AS merchant_pay_cnt, " +
                "                   count(DISTINCT (CASE " +
                "                                       WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5 " +
                "                                           THEN a.customer_id " +
                "                                       ELSE NULL END))                                          AS merchant_pay_close_cnt, " +
                "                   COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, a.customer_id, " +
                "                                     null))                                                        merchant_send_cnt " +
                "            FROM rent_order a " +
                "                     inner join cl_loan cl " +
                "                                on cl.loanno = a.no and cl.ds = (select ds from dt) and riskopinion like '风控等级%' " +
                "                     INNER JOIN rent_order_finance_detail c " +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0 " +
                "                     left join rent_order_infomore roi " +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0 " +
                "                     left join rent_order_merchant_transfer_log rtl " +
                "                               on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt) " +
                "                     inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and " +
                "                                                to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') = " +
                "                                                 " + time + " " +
                "                     left join t on t.customer_id = a.customer_id " +
                "            where a.parent_id = 0 " +
                "              AND a.ds = (select ds from dt) " +
                "              and a.is_deleted = 0 " +
                "              and a.type = 1 " +
                "            group by a.mini_type, cl.quotientname, rate_config_type, is_on_rent, t.customer_type, riskOpinion), " +
                "     t2 as (select common_rent_flag              is_on_rent, " +
                "                   a.mini_type, " +
                "                   cl.quotientname               quotient_name, " +
                "                   riskOpinion                   risk_opinion, " +
                "                   rate_config_type              finance_type, " +
                "                   t.customer_type               customer_type, " +
                "                   count(DISTINCT a.customer_id) risk_cnt, " +
                "                   count(DISTINCT cl.customerid) preposition_risk_cnt," +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20), " +
                "                             a.customer_id, 0))) pass_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30), " +
                "                             a.customer_id, 0))) anti_fraud_pass_cnt " +
                "            from cl_loan cl " +
                "                     left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt)  " +
                "                     left join rent_order_infomore roi " +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0 " +
                "                     left JOIN rent_order_finance_detail c " +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0 " +
                "                     left join t on t.customer_id = a.customer_id " +
                "            WHERE cl.ds = (select ds from dt) " +
                "              and cl.parentno = '' " +
                "              and date(cl.createtime)=  " + time + "" +
                "              and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019)" +
                "              and cl.riskopinion like '风控等级%' " +
                "            group by a.mini_type, cl.quotientname, rate_config_type, is_on_rent, t.customer_type, riskOpinion), " +
                "     t3 as ( " +
                "         select nvl(t1.mini_type, t2.mini_type)         business_channel, " +
                "                nvl(t1.is_on_rent, t2.is_on_rent)       is_on_rent, " +
                "                nvl(t1.quotient_name, t2.quotient_name) quotient_name, " +
                "                nvl(t1.risk_opinion, t2.risk_opinion)   risk_opinion, " +
                "                nvl(t1.finance_type, t2.finance_type)   finance_type, " +
                "                nvl(t1.customer_type, t2.customer_type) customer_type, " +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt, " +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt, " +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt, " +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass, " +
                "                nvl(pay_cnt, 0)                         pay_cnt, " +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt, " +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt, " +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt, " +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt, " +
                "                nvl(send_cnt, 0)                        send_cnt, " +
                "                nvl(risk_cnt, 0)                        risk_cnt, " +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt, " +
                "                nvl(pass_cnt, 0)                        pass_cnt, " +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt, " +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt, " +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt, " +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt, " +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt, " +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt " +
                "         from t1 full " +
                "                  join t2 " +
                "                       on t1.mini_type = t2.mini_type and t1.is_on_rent = t2.is_on_rent and " +
                "                          t1.quotient_name = t2.quotient_name and t1.finance_type = t2.finance_type and " +
                "                          t1.customer_type = t2.customer_type and t1.risk_opinion = t2.risk_opinion)";
        String body = "select  " + time + "                                           count_day, " +
                "       0                                                      type, " +
                "       if(t3.is_on_rent <> 10, '0', '1')                      is_on_rent, " +
                "       t3.business_channel                                    business_channel, " +
                "       rtrim(ltrim(t3.risk_opinion,'风控等级'),'.0')            risk_opinion, " +
                "       if(t3.quotient_name = '', '未标记上导流商', t3.quotient_name) quotient_name, " +
                "       ''                                                     scene, " +
                "       t3.finance_type                                        finance_type, " +
                "       if(t3.customer_type = '', 3, t3.customer_type)         customer_type, " +
                "       nvl(central_bank_inquiry_cnt,0)                        central_bank_inquiry_cnt, " +
                "       0                                                      cur_day_cnt, " +
                "       0                                                      central_bank_pass_cnt, " +
                "       nvl(first_lvl_pass, 0)                                 first_lvl_pass, " +
                "       0                                                      pay_cnt, " +
                "       0                                                      labour_audit_cnt," +
                "       0                                                      total_pass_cnt," +
                "       0                                                      ali_pay_cnt, " +
                "       0                                                      wechat_pay_cnt, " +
                "       0                                                      other_pay_cnt, " +
                "       0                                                      pay_close_cnt, " +
                "       0                                                      credit_audit_pass_cnt, " +
                "       0                                                      credit_audit_refuse_cnt, " +
                "       0                                                      three_lvl_cnt, " +
                "       0                                                      send_cnt, " +
                "       nvl(risk_cnt, 0)                                       risk_cnt, " +
                "       0                                                      preposition_risk_cnt, " +
                "       0                                                      pass_cnt, " +
                "       0                                                      anti_fraud_pass_cnt, " +
                "       0                                                      merchant_pay_close_cnt, " +
                "       0                                                      merchant_pay_cnt, " +
                "       0                                                      merchant_accept_cnt, " +
                "       0                                                      merchant_send_cnt, " +
                "       0                                                      merchant_shunt_cnt, " +
                "       0                                                      cur_pay_cnt, " +
                "       0                                                      cur_merchant_pay_cnt, " +
                "       0                                                      cur_pay_close_cnt, " +
                "       0                                                      cur_merchant_pay_close_cnt, " +
                "       0                                                      cur_merchant_shunt_cnt, " +
                "       0                                                      cur_merchant_accept_cnt, " +
                "       0                                                      cur_send_cnt, " +
                "       0                                                      cur_merchant_send_cnt " +
                "   from t3 WHERE business_channel is not null";

        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        int size = getCount(countSql);
        int PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<RiskRentInfoDO> list = Lists.newArrayList();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = "    order by count_day,  " +
                    "             type,  " +
                    "             business_channel,  " +
                    "             customer_type,  " +
                    "             finance_type,  " +
                    "             scene,  " +
                    "             risk_opinion,  " +
                    "             is_on_rent,  " +
                    "             quotient_name" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            ;
            String pageSql = prefix + body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            list.addAll(assemble(records));
        }
        return list;
    }


    /**
     * 进客群分布，不需要scene分组
     *
     * @param time 时间
     * @return {@code List<RiskRentInfoDO>}
     */
    private List<RiskRentInfoDO> getDistributionByConditionTidb(String time) {
        String prefix = "with dt as (select date_format(now(), 'yyyymmdd') as ds), " +
                "     t as (select gg.customer_id as customer_id, " +
                "                  (case " +
                "                       when gg.lastCreateTime is null or datediff(ro.create_time, gg.lastCreateTime) > 180 then 3 " +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 180 and " +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 30 then 2 " +
                "                       when datediff(ro.create_time, gg.lastCreateTime) <= 30 and " +
                "                            datediff(ro.create_time, gg.lastCreateTime) > 0 then 1 " +
                "                       else 4 end)  customer_type " +
                "           from rent_order ro " +
                "                    left join (select customer_id, max(a.create_time) lastCreateTime " +
                "                               from (select ro.customer_id, " +
                "                                            ro.create_time, " +
                "                                            row_number() over (PARTITION by ro.customer_id order by ro.create_time desc) num " +
                "                                     from rent_order ro " +
                "                                              left join rent_customer rc " +
                "                                                        on ro.customer_id = rc.id and rc.ds = (select ds from dt) " +
                "                                              left join rc_user ru " +
                "                                                        on rc.id_card_no = ru.idcardno and ru.ds = (select ds from dt) " +
                "                                              left join cl_loan cl on cl.customerid = ru.id and cl.ds = (select ds from dt) " +
                "                                     where ro.ds = (select ds from dt)) a " +
                "                               where a.num <> 1 " +
                "                               group by a.customer_id) gg on gg.customer_id = ro.customer_id " +
                "           where ro.ds = (select ds from dt)), " +
                "     t1 as (SELECT common_rent_flag                                                                is_on_rent, " +
                "                   a.mini_type, " +
                "                   cl.quotientname                                                                 quotient_name, " +
                "                   rate_config_type                                                                finance_type, " +
                "                   riskOpinion                                                                     risk_opinion, " +
                "                   t.customer_type                                                                 customer_type, " +
                "                   count(DISTINCT " +
                "                         if(artificialauditstatus in (10, 15), a.customer_id, null))            as central_bank_inquiry_cnt, " +
                "                   count(DISTINCT (if(date(a.create_time) = " +
                "                                      to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') and " +
                "                                      artificialauditstatus in (10, 15), a.customer_id, null))) as cur_day_cnt, " +
                "                   count(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 40 and cl.artificialauditstatus = 10) or (riskFicoStatus > 40), " +
                "                             a.customer_id, " +
                "                             null)))                                                            as central_bank_pass_cnt, " +
                "                   count(DISTINCT " +
                "                         (if((cl.riskFicoStatus >= 60 and cl.artificialauditstatus = 10) and replace(riskopinion,'风控等级','') <=10, " +
                "                             a.customer_id, " +
                "                             null)))                                                            as first_lvl_pass, " +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id in (100, ********), a.customer_id, " +
                "                                     null))                                                     AS pay_cnt, " +
                "                   count(DISTINCT " +
                "                         (if(paystatus = 1 and a.termination = 5 and a.merchant_id in (100, ********), a.customer_id, " +
                "                             NULL)))                                                            AS pay_close_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 80 and cl.artificialauditstatus = 10) or (riskFicoStatus > 80), " +
                "                             a.customer_id, " +
                "                             null)))                                                            as credit_audit_pass_cnt, " +
                "                   COUNT(DISTINCT (if(cl.riskFicoStatus = 80 and cl.artificialauditstatus = 15, a.no, " +
                "                                      null)))                                                   as credit_audit_refuse_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 90 and cl.artificialauditstatus = 10) or (riskFicoStatus > 90), " +
                "                             a.customer_id, " +
                "                             null)))                                                               three_lvl_cnt, " +
                "                   COUNT(DISTINCT if(sendstatus = 5 and a.merchant_id in (100, ********), a.customer_id, " +
                "                                     null))                                                        send_cnt, " +
                "                   count(DISTINCT case " +
                "                                      when rtl.order_id is not null then a.customer_id " +
                "                                      else null end)                                            as merchant_shunt_cnt, " +
                "                   count(DISTINCT case " +
                "                                      when a.merchant_id not in (100, ********) and merchant_transfer = 10 " +
                "                                          then a.customer_id " +
                "                                      else null end)                                            as merchant_accept_cnt, " +
                "                   count(DISTINCT if(paystatus = 1 and a.merchant_id not in (100, ********), a.customer_id, " +
                "                                     null))                                                     AS merchant_pay_cnt, " +
                "                   count(DISTINCT (CASE " +
                "                                       WHEN paystatus = 1 and a.merchant_id not in (100, ********) and a.termination = 5 " +
                "                                           THEN a.customer_id " +
                "                                       ELSE NULL END))                                          AS merchant_pay_close_cnt, " +
                "                   COUNT(DISTINCT if(a.merchant_id not in (100, ********) and sendstatus = 5, a.customer_id, " +
                "                                     null))                                                        merchant_send_cnt " +
                "            FROM rent_order a " +
                "                     inner join cl_loan cl " +
                "                                on cl.loanno = a.no and cl.ds = (select ds from dt) and riskopinion like '风控等级%' " +
                "                     INNER JOIN rent_order_finance_detail c " +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0 " +
                "                     left join rent_order_infomore roi " +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0 " +
                "                     left join rent_order_merchant_transfer_log rtl " +
                "                               on rtl.order_id = a.id and rtl.is_deleted = 0 and rtl.ds = (select ds from dt) " +
                "                     inner join serial_no sn on sn.businessno = cl.loanno and sn.ds = (select ds from dt) and " +
                "                                                to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') = " +
                "                                                 " + time + " " +
                "                     left join t on t.customer_id = a.customer_id " +
                "            where a.parent_id = 0 " +
                "              AND a.ds = (select ds from dt) " +
                "              and a.is_deleted = 0 " +
                "              and a.type = 1 " +
                "            group by a.mini_type, cl.quotientname, rate_config_type, is_on_rent, t.customer_type, riskOpinion), " +
                "     t2 as (select common_rent_flag              is_on_rent, " +
                "                   a.mini_type, " +
                "                   cl.quotientname               quotient_name, " +
                "                   riskOpinion                   risk_opinion, " +
                "                   rate_config_type              finance_type, " +
                "                   t.customer_type               customer_type, " +
                "                   count(DISTINCT a.customer_id) risk_cnt, " +
                "                   count(DISTINCT cl.customerid) preposition_risk_cnt," +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 20 and cl.artificialauditstatus = 10) or (riskFicoStatus > 20), " +
                "                             a.customer_id, 0))) pass_cnt, " +
                "                   COUNT(DISTINCT " +
                "                         (if((cl.riskFicoStatus = 30 and cl.artificialauditstatus = 10) or (riskFicoStatus > 30), " +
                "                             a.customer_id, 0))) anti_fraud_pass_cnt " +
                "            from cl_loan cl " +
                "                     left join rent_order a on cl.loanno = a.no and a.ds = (select ds from dt)  " +
                "                     left join rent_order_infomore roi " +
                "                               on roi.order_id = a.id and roi.ds = (select ds from dt) and roi.is_deleted = 0 " +
                "                     left JOIN rent_order_finance_detail c " +
                "                                on c.order_id = a.id and c.ds = (select ds from dt) and c.is_deleted = 0 " +
                "                     left join t on t.customer_id = a.customer_id " +
                "            WHERE cl.ds = (select ds from dt) " +
                "              and cl.parentno = '' " +
                "              and date(cl.createtime)=  " + time + "" +
                "              and cl.businessChannel not in (28,51,1013,1014,1015,1016,1017,1018,1019)" +
                "              and cl.riskopinion like '风控等级%' " +
                "            group by a.mini_type, cl.quotientname, rate_config_type, is_on_rent, t.customer_type, riskOpinion), " +
                "     t3 as ( " +
                "         select nvl(t1.mini_type, t2.mini_type)         business_channel, " +
                "                nvl(t1.is_on_rent, t2.is_on_rent)       is_on_rent, " +
                "                nvl(t1.quotient_name, t2.quotient_name) quotient_name, " +
                "                nvl(t1.risk_opinion, t2.risk_opinion)   risk_opinion, " +
                "                nvl(t1.finance_type, t2.finance_type)   finance_type, " +
                "                nvl(t1.customer_type, t2.customer_type) customer_type, " +
                "                nvl(central_bank_inquiry_cnt, 0)        central_bank_inquiry_cnt, " +
                "                nvl(cur_day_cnt, 0)                     cur_day_cnt, " +
                "                nvl(central_bank_pass_cnt, 0)           central_bank_pass_cnt, " +
                "                nvl(first_lvl_pass, 0)                  first_lvl_pass, " +
                "                nvl(pay_cnt, 0)                         pay_cnt, " +
                "                nvl(pay_close_cnt, 0)                   pay_close_cnt, " +
                "                nvl(credit_audit_pass_cnt, 0)           credit_audit_pass_cnt, " +
                "                nvl(credit_audit_refuse_cnt, 0)         credit_audit_refuse_cnt, " +
                "                nvl(three_lvl_cnt, 0)                   three_lvl_cnt, " +
                "                nvl(send_cnt, 0)                        send_cnt, " +
                "                nvl(risk_cnt, 0)                        risk_cnt, " +
                "                nvl(preposition_risk_cnt, 0)            preposition_risk_cnt, " +
                "                nvl(pass_cnt, 0)                        pass_cnt, " +
                "                nvl(anti_fraud_pass_cnt, 0)             anti_fraud_pass_cnt, " +
                "                nvl(merchant_pay_close_cnt, 0)          merchant_pay_close_cnt, " +
                "                nvl(merchant_pay_cnt, 0)                merchant_pay_cnt, " +
                "                nvl(merchant_accept_cnt, 0)             merchant_accept_cnt, " +
                "                nvl(merchant_send_cnt, 0)               merchant_send_cnt, " +
                "                nvl(merchant_shunt_cnt, 0)              merchant_shunt_cnt " +
                "         from t1 full " +
                "                  join t2 " +
                "                       on t1.mini_type = t2.mini_type and t1.is_on_rent = t2.is_on_rent and " +
                "                          t1.quotient_name = t2.quotient_name and t1.finance_type = t2.finance_type and " +
                "                          t1.customer_type = t2.customer_type and t1.risk_opinion = t2.risk_opinion)";
        String body = "select  " + time + "                                           count_day, " +
                "       0                                                      type, " +
                "       if(t3.is_on_rent <> 10, '0', '1')                      is_on_rent, " +
                "       t3.business_channel                                    business_channel, " +
                "       rtrim(ltrim(t3.risk_opinion,'风控等级'),'.0')            risk_opinion, " +
                "       if(t3.quotient_name = '', '未标记上导流商', t3.quotient_name) quotient_name, " +
                "       ''                                                     scene, " +
                "       t3.finance_type                                        finance_type, " +
                "       if(t3.customer_type = '', 3, t3.customer_type)         customer_type, " +
                "       nvl(central_bank_inquiry_cnt,0)                        central_bank_inquiry_cnt, " +
                "       0                                                      cur_day_cnt, " +
                "       0                                                      central_bank_pass_cnt, " +
                "       nvl(first_lvl_pass, 0)                                 first_lvl_pass, " +
                "       0                                                      pay_cnt, " +
                "       0                                                      labour_audit_cnt," +
                "       0                                                      total_pass_cnt," +
                "       0                                                      ali_pay_cnt, " +
                "       0                                                      wechat_pay_cnt, " +
                "       0                                                      other_pay_cnt, " +
                "       0                                                      pay_close_cnt, " +
                "       0                                                      credit_audit_pass_cnt, " +
                "       0                                                      credit_audit_refuse_cnt, " +
                "       0                                                      three_lvl_cnt, " +
                "       0                                                      send_cnt, " +
                "       nvl(risk_cnt, 0)                                       risk_cnt, " +
                "       0                                                      preposition_risk_cnt, " +
                "       0                                                      pass_cnt, " +
                "       0                                                      anti_fraud_pass_cnt, " +
                "       0                                                      merchant_pay_close_cnt, " +
                "       0                                                      merchant_pay_cnt, " +
                "       0                                                      merchant_accept_cnt, " +
                "       0                                                      merchant_send_cnt, " +
                "       0                                                      merchant_shunt_cnt, " +
                "       0                                                      cur_pay_cnt, " +
                "       0                                                      cur_merchant_pay_cnt, " +
                "       0                                                      cur_pay_close_cnt, " +
                "       0                                                      cur_merchant_pay_close_cnt, " +
                "       0                                                      cur_merchant_shunt_cnt, " +
                "       0                                                      cur_merchant_accept_cnt, " +
                "       0                                                      cur_send_cnt, " +
                "       0                                                      cur_merchant_send_cnt " +
                "   from t3 WHERE business_channel is not null";

        String countSql = prefix + "select count(*) num from (" + body + ") ;";
        int size = getCountTidb(countSql);
//        int size = 100000;
        int PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<RiskRentInfoDO> list = Lists.newArrayList();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = "    order by count_day,  " +
                    "             type,  " +
                    "             business_channel,  " +
                    "             customer_type,  " +
                    "             finance_type,  " +
                    "             scene,  " +
                    "             risk_opinion,  " +
                    "             is_on_rent,  " +
                    "             quotient_name" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            ;
            String pageSql = prefix + body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            list.addAll(assemble(records));
        }
        return list;
    }
    /**
     * 获取order最大记录数
     */
    private Integer getCount(String sql) {
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCountTidb(String sql) {
        List<Map<String, String>> records = rentOrderService.getGeneralCommonSql(sql);
        Object num = records.get(0).get("num");
        return ((Long) num).intValue();
    }
}
