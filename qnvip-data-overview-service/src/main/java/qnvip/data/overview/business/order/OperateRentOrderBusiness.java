package qnvip.data.overview.business.order;

import com.aliyun.odps.Column;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.OperateOrderForecastDO;
import qnvip.data.overview.enums.StrategyEnum;
import qnvip.data.overview.service.order.OperateOrderForecastService;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateRentOrderBusiness {

    private final RentOrderService rentOrderService;
    private final OperateOrderForecastService operateOrderForecastService;
    private final OdpsUtil odpsUtil;

    public void runCore(StrategyEnum strategy) {
        LocalDate now = LocalDate.now();
        LocalDateTime countDay = LocalDateTime.of(now, LocalTime.MIN);
        LinkedList<OperateOrderForecastDO> list = getRentList(strategy, countDay);
        List<OperateOrderForecastDO> merchantList = getMerchantList(DateUtils.localDateToString(now));
        list.addAll(merchantList);
        operateOrderForecastService.removeDataByTime(countDay);
        operateOrderForecastService.saveBatch(list);
    }

    private LinkedList<OperateOrderForecastDO> getRentList(StrategyEnum strategy, LocalDateTime countDay) {
        Map<String, Object> predictedMap = Maps.newHashMap();
        if (Objects.nonNull(strategy)) {
            predictedMap = rentOrderService.selectPredictedValue(strategy);
        }
        Map<String, Object> actualMap = rentOrderService.selectActualValue();
        Map<String, Object> mergeMap = Stream.
                concat(predictedMap.entrySet().stream(), actualMap.entrySet().stream()).
                collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
        List<Map.Entry<String, Object>> maps = Lists.newArrayList(mergeMap.entrySet());
        OperateOrderForecastDO rentDo;
        LinkedList<OperateOrderForecastDO> list = Lists.newLinkedList();
        for (Map.Entry<String, Object> map : maps) {
            rentDo = new OperateOrderForecastDO();
            BigDecimal value = CalculateUtil.toDecimal(map.getValue());
            rentDo.setHourTime(StrategyEnum.getCode(map.getKey()));
            rentDo.setOrderUvCount(value.longValue());
            rentDo.setCountDay(countDay);
            Integer BIZ_TYPE_RENT = 1;
            rentDo.setBizType(BIZ_TYPE_RENT);
            Integer COUNT_TYPE_RENT = 1;
            rentDo.setCountType(COUNT_TYPE_RENT);
            list.add(rentDo);
        }
        return list;
    }

    private List<OperateOrderForecastDO> getMerchantList(String time) {
        String sql = "with dt as (select date_format(now(), 'yyyymmdd') as ds),\n" +
                "  day as (select '" + time + "'  as count_day)\n" +
                "select\n" +
                "    date (create_time) count_day,\n" +
                "    2 biz_type,\n" +
                "    2 count_type,\n" +
                "    count (distinct if(HOUR (create_time) < 1, order_uid, null)) one,\n" +
                "    count (distinct if(HOUR (create_time) < 2, order_uid, null)) two,\n" +
                "    count (distinct if(HOUR (create_time) < 3, order_uid, null)) three,\n" +
                "    count (distinct if(HOUR (create_time) < 4, order_uid, null)) four,\n" +
                "    count (distinct if(HOUR (create_time) < 5, order_uid, null)) five,\n" +
                "    count (distinct if(HOUR (create_time) < 6, order_uid, null)) six,\n" +
                "    count (distinct if(HOUR (create_time) < 7, order_uid, null)) seven,\n" +
                "    count (distinct if(HOUR (create_time) < 8, order_uid, null)) eight,\n" +
                "    count (distinct if(HOUR (create_time) < 9, order_uid, null)) nine,\n" +
                "    count (distinct if(HOUR (create_time) < 10, order_uid, null)) ten,\n" +
                "    count (distinct if(HOUR (create_time) < 11, order_uid, null)) eleven,\n" +
                "    count (distinct if(HOUR (create_time) < 12, order_uid, null)) twelve,\n" +
                "    count (distinct if(HOUR (create_time) < 13, order_uid, null)) thirteen,\n" +
                "    count (distinct if(HOUR (create_time) < 14, order_uid, null)) fourteen,\n" +
                "    count (distinct if(HOUR (create_time) < 15, order_uid, null)) fifteen,\n" +
                "    count (distinct if(HOUR (create_time) < 16, order_uid, null)) sixteen,\n" +
                "    count (distinct if(HOUR (create_time) < 17, order_uid, null)) seventeen,\n" +
                "    count (distinct if(HOUR (create_time) < 18, order_uid, null)) eighteen,\n" +
                "    count (distinct if(HOUR (create_time) < 19, order_uid, null)) nineteen,\n" +
                "    count (distinct if(HOUR (create_time) < 20, order_uid, null)) twenty,\n" +
                "    count (distinct if(HOUR (create_time) < 21, order_uid, null)) twenty_one,\n" +
                "    count (distinct if(HOUR (create_time) < 22, order_uid, null)) twenty_two,\n" +
                "    count (distinct if(HOUR (create_time) < 23, order_uid, null)) twenty_three,\n" +
                "    count (distinct if(HOUR (create_time) < 24, order_uid, null)) twenty_four\n" +
                "from sh_order\n" +
                "where is_deleted = 0\n" +
                "  and ds = (select ds from dt)\n" +
                "  and date (create_time) =(select count_day from day)\n" +
                "group by date (create_time);";
        Integer BIZ_TYPE_MERCHANT = 2;
        Integer COUNT_TYPE_MERCHANT = 2;
        List<OperateOrderForecastDO> list = getList(sql, BIZ_TYPE_MERCHANT, COUNT_TYPE_MERCHANT, time);
        String sql2 = "with dt as (select date_format(now(), 'yyyymmdd') as ds),\n" +
                "  day as (select '" + time + "'  as count_day)\n" +
                "select\n" +
                "    date (create_time) count_day,\n" +
                "    2 biz_type,\n" +
                "    1 count_type,\n" +
                "    count (distinct if(HOUR (create_time) < 1, customer_id, null)) one,\n" +
                "    count (distinct if(HOUR (create_time) < 2, customer_id, null)) two,\n" +
                "    count (distinct if(HOUR (create_time) < 3, customer_id, null)) three,\n" +
                "    count (distinct if(HOUR (create_time) < 4, customer_id, null)) four,\n" +
                "    count (distinct if(HOUR (create_time) < 5, customer_id, null)) five,\n" +
                "    count (distinct if(HOUR (create_time) < 6, customer_id, null)) six,\n" +
                "    count (distinct if(HOUR (create_time) < 7, customer_id, null)) seven,\n" +
                "    count (distinct if(HOUR (create_time) < 8, customer_id, null)) eight,\n" +
                "    count (distinct if(HOUR (create_time) < 9, customer_id, null)) nine,\n" +
                "    count (distinct if(HOUR (create_time) < 10, customer_id, null)) ten,\n" +
                "    count (distinct if(HOUR (create_time) < 11, customer_id, null)) eleven,\n" +
                "    count (distinct if(HOUR (create_time) < 12, customer_id, null)) twelve,\n" +
                "    count (distinct if(HOUR (create_time) < 13, customer_id, null)) thirteen,\n" +
                "    count (distinct if(HOUR (create_time) < 14, customer_id, null)) fourteen,\n" +
                "    count (distinct if(HOUR (create_time) < 15, customer_id, null)) fifteen,\n" +
                "    count (distinct if(HOUR (create_time) < 16, customer_id, null)) sixteen,\n" +
                "    count (distinct if(HOUR (create_time) < 17, customer_id, null)) seventeen,\n" +
                "    count (distinct if(HOUR (create_time) < 18, customer_id, null)) eighteen,\n" +
                "    count (distinct if(HOUR (create_time) < 19, customer_id, null)) nineteen,\n" +
                "    count (distinct if(HOUR (create_time) < 20, customer_id, null)) twenty,\n" +
                "    count (distinct if(HOUR (create_time) < 21, customer_id, null)) twenty_one,\n" +
                "    count (distinct if(HOUR (create_time) < 22, customer_id, null)) twenty_two,\n" +
                "    count (distinct if(HOUR (create_time) < 23, customer_id, null)) twenty_three,\n" +
                "    count (distinct if(HOUR (create_time) < 24, customer_id, null)) twenty_four\n" +
                "from sh_order\n" +
                "where is_deleted = 0\n" +
                "  and ds = (select ds from dt)\n" +
                "  and date (create_time) =(select count_day from day)\n" +
                "group by date (create_time);";
        Integer COUNT_TYPE_MERCHANT_UV = 1;
        List<OperateOrderForecastDO> list2 = getList(sql2, BIZ_TYPE_MERCHANT, COUNT_TYPE_MERCHANT_UV, time);
        list.addAll(list2);
        return list;

    }

    private List<OperateOrderForecastDO> getList(String sql, Integer bizType, Integer countType, String time) {
        List<Record> records = odpsUtil.querySql(sql);
        Map<String, Object> sourceMap = Maps.newHashMap();
        for (Record record : records) {
            Column[] columns = record.getColumns();
            for (Column column : columns) {
                String name = column.getName();
                sourceMap.put(name, record.getString(name));
            }
        }
        LinkedList<OperateOrderForecastDO> list = Lists.newLinkedList();
        Map<String, Object> mergeMap =
                sourceMap.entrySet().stream().
                        collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
        List<Map.Entry<String, Object>> maps = Lists.newArrayList(mergeMap.entrySet());
        OperateOrderForecastDO rentDo;
        for (Map.Entry<String, Object> map : maps) {
            rentDo = new OperateOrderForecastDO();
            if (StringUtils.equalsAny(map.getKey(), "count_day", "biz_type", "count_type")) {
                continue;
            }
            BigDecimal value = CalculateUtil.toDecimal(map.getValue());
            rentDo.setHourTime(StrategyEnum.getCode(map.getKey()));
            rentDo.setOrderUvCount(value.longValue());
            rentDo.setBizType(bizType);
            rentDo.setCountType(countType);
            rentDo.setCountDay(DateUtils.stringToLocateDate(time).atStartOfDay());
            list.add(rentDo);
        }

        return list;
    }

}
