package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateOrderStatusDO;
import qnvip.data.overview.service.order.OperateOrderStatusService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateOrderStatusBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateOrderStatusService orderStatusService;

    void initMap(Map<Integer, Map<String, OperateOrderStatusDO>> miniType2Map, OperateOrderStatusDO screenRiskDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, OperateOrderStatusDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(screenRiskDO.getMiniType())) {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            domain.setCountDay(countDay);
            domain.setMiniType(screenRiskDO.getMiniType());
            domain.setMerchantId(screenRiskDO.getMerchantId());
            merchantId2Do.put(screenRiskDO.getMerchantId(), domain);
        } else {
            merchantId2Do = miniType2Map.get(screenRiskDO.getMiniType());
            if (!merchantId2Do.containsKey(screenRiskDO.getMerchantId())) {
                OperateOrderStatusDO domain = new OperateOrderStatusDO();
                domain.setCountDay(countDay);
                domain.setMiniType(screenRiskDO.getMiniType());
                domain.setMerchantId(screenRiskDO.getMerchantId());
                merchantId2Do.put(screenRiskDO.getMerchantId(), domain);
            }
        }
        miniType2Map.put(screenRiskDO.getMiniType(), merchantId2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, OperateOrderStatusDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateOrderStatusDO>> f1 = CompletableFuture.supplyAsync(()->getUnAuditClosed(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f2 = CompletableFuture.supplyAsync(()->getInAudit(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f3 = CompletableFuture.supplyAsync(()->getAuditFailed(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f4 = CompletableFuture.supplyAsync(()->getAuditFailedClosed(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f5 = CompletableFuture.supplyAsync(()->getAuditPassClosed(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f6 = CompletableFuture.supplyAsync(()->getPayClosed(ds));
        CompletableFuture<List<OperateOrderStatusDO>> f7 = CompletableFuture.supplyAsync(()->getSignClosed(ds));
        CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7).join();
        try {
            for (OperateOrderStatusDO coreDO : f1.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setUnAuditClosed(coreDO.getUnAuditClosed());
            }
            for (OperateOrderStatusDO coreDO : f2.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setInAudit(coreDO.getInAudit());
            }
            for (OperateOrderStatusDO coreDO : f3.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setAuditFailed(coreDO.getAuditFailed());
            }
            for (OperateOrderStatusDO coreDO : f4.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setAuditFailedClosed(coreDO.getAuditFailedClosed());
            }
            for (OperateOrderStatusDO coreDO : f5.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setAuditPassClosed(coreDO.getAuditPassClosed());
            }
            for (OperateOrderStatusDO coreDO : f6.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setPayClosedCount(coreDO.getPayClosedCount());
            }
            for (OperateOrderStatusDO coreDO : f7.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderStatusDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderStatusDO operateOrderStatusDO = merchantId2Do.get(coreDO.getMerchantId());
                operateOrderStatusDO.setSignClosedCount(coreDO.getSignClosedCount());
            }
            // 写入mysql
            LinkedList<OperateOrderStatusDO> list = Lists.newLinkedList();

            for (Map.Entry<Integer, Map<String, OperateOrderStatusDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, OperateOrderStatusDO> merchantId2Do : entry.getValue().entrySet()) {
                    OperateOrderStatusDO value = merchantId2Do.getValue();
                    list.add(value);
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            orderStatusService.removeDataByTime(countDay);
            orderStatusService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateOrderStatusBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 获取未审核关闭
     */
    private List<OperateOrderStatusDO> getUnAuditClosed(String ds) {
        String sql = "select count(1) num," +
                "       ro.mini_type," +
                "       ro.merchant_id" +
                " from rent_order ro" +
                "         inner join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.parent_id = 0" +
                "  and roa.type = 2" +
                "  and ro.status in (20, 30)" +
                "  and (roa.audit_status in (0, 3) or roa.operate_time is null)" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and ro.create_time between ${fromTime} and ${toTime}" +
                " group by ro.mini_type, ro.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUnAuditClosed(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取审核中
     */
    private List<OperateOrderStatusDO> getInAudit(String ds) {
        String sql = "select a.mini_type          as mini_type," +
                "       count(a.customer_id) as num," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         left join rent_order_audit b on a.id = b.order_id" +
                " where a.parent_id = 0" +
                "  and a.type = 1" +
                "  and a.merchant_id = 100 " +
                "  and b.audit_status = 0" +
                "  and a.is_deleted = 0" +
                "  and b.type = 2" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setInAudit(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取审核未通过
     */
    private List<OperateOrderStatusDO> getAuditFailed(String ds) {
        String sql = "select a.mini_type          as mini_type," +
                "       count(a.customer_id) as num," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         left join rent_order_audit b on a.id = b.order_id" +
                " where a.parent_id = 0" +
                "  and a.type = 1" +
                "  and a.merchant_id = 100 " +
                "  and b.audit_status =2" +
                "  and b.type = 2" +
                "  and a.is_deleted = 0" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and b.operate_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setAuditFailed(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取审核未通过关闭
     */
    private List<OperateOrderStatusDO> getAuditFailedClosed(String ds) {
        String sql = "select count(1) num," +
                "       ro.mini_type," +
                "       ro.merchant_id" +
                " from rent_order ro" +
                "         left join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.parent_id = 0" +
                "  and ro.status in (20, 30)" +
                "  and roa.type = 2" +
                "  and roa.audit_status =2" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and ro.create_time between ${fromTime} and ${toTime}" +
                " group by ro.mini_type, ro.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setAuditFailedClosed(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取审核通过关闭
     */
    private List<OperateOrderStatusDO> getAuditPassClosed(String ds) {
        String sql = "select count(1) num," +
                "       ro.mini_type," +
                "       ro.merchant_id" +
                " from rent_order ro" +
                "         left join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.parent_id = 0" +
                "  and ro.status in (20, 30)" +
                "  and roa.type = 2" +
                "  and roa.audit_status in (1)" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and ro.create_time between ${fromTime} and ${toTime}" +
                " group by ro.mini_type, ro.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setAuditPassClosed(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取订单支付后关闭
     */
    private List<OperateOrderStatusDO> getPayClosed(String ds) {
        String sql = "select count(1) num," +
                "       ro.mini_type," +
                "       ro.merchant_id" +
                " from rent_order ro" +
                "         left join rent_order_audit roa on ro.id = roa.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.parent_id = 0" +
                "  and ro.status in (20, 30)" +
                "  and roa.audit_status in (1)" +
                "  and ro.payment_time is not null" +
                "  and ro.ds = "+ds +
                "  and roa.ds = "+ds +
                "  and ro.create_time between ${fromTime} and ${toTime}" +
                " group by ro.mini_type, ro.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayClosedCount(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取签收后关闭
     */
    private List<OperateOrderStatusDO> getSignClosed(String ds) {
        String sql = "select count(1) num, " +
                "       ro.mini_type, " +
                "       ro.merchant_id " +
                " from rent_order ro " +
                "         left join rent_order_audit roa on ro.id = roa.order_id " +
                "         left join rent_order_logistics rol on ro.id = rol.order_id " +
                " where ro.is_deleted = 0 " +
                "  and ro.type = 1 " +
                "  and ro.merchant_id = 100 " +
                "  and ro.parent_id = 0 " +
                "  and ro.status in (20, 30) " +
                "  and roa.audit_status in (1) " +
                "  and ro.payment_time is not null " +
                "  and rol.express_state in (3, 4, 6) " +
                "  and ro."+ds +
                "  and roa."+ds +
                "  and rol."+ds +
                "  and ro.create_time between ${fromTime} and ${toTime} " +
                " group by ro.mini_type, ro.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderStatusDO domain = new OperateOrderStatusDO();
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setSignClosedCount(Long.parseLong(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }
}
