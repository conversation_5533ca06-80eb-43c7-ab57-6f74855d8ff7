package qnvip.data.overview.business.merchant;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import lombok.val;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.merchant.OperateMerchantIndexDO;
import qnvip.data.overview.domain.merchant.OperateMerchantOverviewDO;
import qnvip.data.overview.service.merchant.OperateMerchantIndexService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantIndexBusiness {

    private final OdpsUtil odpsUtil;
    private final OperateMerchantIndexService service;
    private static final DateTimeFormatter YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    public void execute() {
        String body = getBody();
        String countSql = "select count(*) num from (" + body + ") ;";
        Integer size = getCount(countSql);
        log.info("{}, 总数={}", "MerchantIndexBusiness", size);
        Integer PAGE_SIZE = 10000;
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        List<OperateMerchantIndexDO> collect = new ArrayList<>();
        for (int startPage = 0; startPage < times; startPage++) {
            String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = body + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            List<OperateMerchantIndexDO> list = records.stream().map(record -> {
                OperateMerchantIndexDO OperateMerchantIndexDO = new OperateMerchantIndexDO();
                OperateMerchantIndexDO.setCountDay(LocalDate.parse(record.getString("count_day"), YYYY_MM_DD).atStartOfDay());
                OperateMerchantIndexDO.setShopName(record.getString("shop_name"));
                OperateMerchantIndexDO.setMerchantId(Long.valueOf(record.getString("merchant_id")));
                return OperateMerchantIndexDO;
            }).collect(Collectors.toList());
            collect.addAll(list);
        }
        service.remove();
        service.saveBatch(collect);
    }

    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }


    private Integer getCount(String countSql) {
        List<Record> records = odpsUtil.querySql(countSql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    private String getBody() {
        return "with dt as (select date_format(now(), 'yyyymmdd') as ds)\n" +
                "   select distinct a.merchant_id, count_day, b.shop_name\n" +
                "   from (\n" +
                "            select o.merchant_id, date(sign_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     LEFT JOIN rent_order_finance_detail d ON o.id = d.order_id and d.ds = (select ds from dt)\n" +
                "                     LEFT JOIN rent_order_repayment_plan p ON p.order_id = o.id and p.ds = (select ds from dt)\n" +
                "                     LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.ds = (select ds from dt)\n" +
                "            WHERE o.merchant_id <> 100\n" +
                "              and rol.sign_time is not null\n" +
                "              and o.type = 1\n" +
                "              and o.is_deleted = 0\n" +
                "              and o.ds = (select ds from dt)\n" +
                "            group by o.merchant_id, date(sign_time)\n" +
                "            union all\n" +
                "            select l.merchant_id, date(operate_time) count_day\n" +
                "            FROM rent_order_merchant_transfer_log l\n" +
                "                     INNER JOIN rent_order o ON l.order_id = o.id and l.status = 10 and o.ds = (select ds from dt)\n" +
                "            WHERE l.is_deleted = 0\n" +
                "              and o.is_deleted = 0\n" +
                "              and l.ds = (select ds from dt)\n" +
                "            group by l.merchant_id, date(operate_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(payment_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     LEFT JOIN rent_order_flow f\n" +
                "                               ON f.order_id = o.id and flow_type = 2 and f.biz_type = 4 and f.ds = (select ds from dt)\n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "            group by o.merchant_id, date(payment_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(send_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.ds = (select ds from dt)\n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "            group by o.merchant_id, date(send_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(sign_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     LEFT JOIN rent_order_finance_detail d ON o.id = d.order_id and d.ds = (select ds from dt)\n" +
                "                     LEFT JOIN rent_order_logistics rol ON rol.order_id = o.id and rol.ds = (select ds from dt)\n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "            group by o.merchant_id, date(sign_time)\n" +
                "            union all\n" +
                "            SELECT merchant_id, date(flow_time) count_day\n" +
                "            FROM rent_order_flow\n" +
                "            where merchant_id <> 100\n" +
                "              AND flow_type = 1\n" +
                "              AND pay_status = 10\n" +
                "              AND merchant_item_type = 2\n" +
                "              and parent_uid <> ''\n" +
                "              AND merchant_id > 0\n" +
                "              AND flow_amt > refunded_amt\n" +
                "              and ds = (select ds from dt)\n" +
                "            group by merchant_id, date(flow_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(roi.buyout_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     INNER JOIN rent_order_infomore roi ON o.id = roi.order_id and roi.ds = (select ds from dt)\n" +
                "   \n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "            group by o.merchant_id, date(roi.buyout_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(o2.settle_date) count_day\n" +
                "            FROM rent_order o\n" +
                "                     INNER JOIN rent_order o2\n" +
                "                                ON o.id = o2.parent_id and o2.is_deleted = 0 and o2.status = 330 and o.status = 320 and\n" +
                "                                   o.is_deleted = 0 and o2.ds = (select ds from dt)\n" +
                "   \n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "              and date(o2.settle_date) <= date(now())\n" +
                "            group by o.merchant_id, date(o2.settle_date)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(return_succ_time) count_day\n" +
                "            FROM rent_order o\n" +
                "                     INNER JOIN rent_order_infomore roi\n" +
                "                                ON o.id = roi.order_id and o.status = 210 and roi.ds = (select ds from dt)\n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "              and date(return_succ_time) <= date(now())\n" +
                "            group by o.merchant_id, date(return_succ_time)\n" +
                "            union all\n" +
                "            select o.merchant_id, date(repay_date) count_day\n" +
                "            FROM rent_order o\n" +
                "                     LEFT JOIN rent_order_repayment_plan p ON p.order_id = o.id and p.ds = (select ds from dt)\n" +
                "            WHERE o.is_deleted = 0\n" +
                "              and o.merchant_id <> 100\n" +
                "              and o.type = 1\n" +
                "              and o.ds = (select ds from dt)\n" +
                "              and date(repay_date) <= date(now())\n" +
                "            group by o.merchant_id, date(repay_date)\n" +
                "            union all\n" +
                "            select ro.merchant_id,date(real_repay_time) count_day\n" +
                "                from rent_order ro\n" +
                "                    inner join rent_order_repayment_plan rp on ro.id=rp.order_id and rp.ds=(select ds from dt)\n" +
                "                where ro.merchant_id <> 100\n" +
                "                and ro.merchant_id >0\n" +
                "                 and ro.ds=(select ds from dt)\n" +
                "                group by date(real_repay_time),ro.merchant_id" +
                "             union all " +
                "               select merchant_id,\n" +
                "                       date (rent_end_date) count_day\n" +
                "                   from rent_order o\n" +
                "                       INNER JOIN rent_order_finance_detail f\n" +
                "                   ON f.order_id = o.id and f.is_deleted = 0 and f.ds = (select ds from dt)\n" +
                "                   WHERE o.is_deleted = 0\n" +
                "                     and o.merchant_id <> 100\n" +
                "                     and o.type = 1\n" +
                "                     and o.termination <> 5\n" +
                "                     and o.ds = (select ds from dt)\n" +
                "                     and if(settle_date is null\n" +
                "                       , getdate()\n" +
                "                       , settle_date)\n" +
                "                       > rent_end_date\n" +
                "                   group by merchant_id, date (rent_end_date)  " +
                "        ) a\n" +
                "            inner join\n" +
                "            (select id,shop_name from rent_merchant b\n" +
                "            where b.ds = (select ds from dt)) b\n" +
                "            on a.merchant_id = b.id\n" +
                "   where count_day is not null order by count_day desc ";
    }
}



