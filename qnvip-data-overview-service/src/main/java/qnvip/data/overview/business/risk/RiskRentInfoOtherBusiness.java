package qnvip.data.overview.business.risk;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.risk.RiskRentInfoDO;
import qnvip.data.overview.service.risk.RiskRentInfoService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <专门用来更新前置风控用户>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskRentInfoOtherBusiness {

    private final OdpsUtil odpsUtil;
    private final RiskRentInfoService rentInfoService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        getTotal(time);

    }

    // todo 先搁置
    private void getTotal(String time) {
        String sql = "select date(cl.createtime) count_day,\n" +
                "  count(distinct customerid) preposition_risk_cnt\n" +
                "from cl_loan cl\n" +
                " WHERE cl.ds = 20230607\n" +
                "  and cl.parentno = ''\n" +
                "  and date(cl.createtime)  between date_sub(getdate(), 7) and date(getdate())\n" +
                "  and cl.businessChannel =11\n" +
                "group by  date(cl.createtime);";
        List<Record> records = odpsUtil.querySql(sql);
        List<RiskRentInfoDO> list = assemble(records);
        if (CollUtil.isNotEmpty(list)) {
            rentInfoService.saveBatch(list);
        }
    }

    private List<RiskRentInfoDO> assemble(List<Record> records) {
        return records.stream().map(merged -> {
                    LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
                    RiskRentInfoDO riskRentSituationDO = new RiskRentInfoDO();
                    String businessChannel = merged.getString("business_channel");
                    String isOnRent = merged.getString("is_on_rent");
                    String quotientName = merged.getString("quotient_name");
                    String scene = merged.getString("scene");
                    String financeType = merged.getString("finance_type");
                    String customerType = merged.getString("customer_type");
                    String riskOpinion = merged.getString("risk_opinion");
                    if (customerType.equals("\\N")) {
                        customerType = "3";
                    }
                    String type = merged.getString("type");
                    String riskCnt = merged.getString("risk_cnt");
                    String prepositionRiskCnt = merged.getString("preposition_risk_cnt");
                    String passCnt = merged.getString("pass_cnt");
                    String antiFraudPassCnt = merged.getString("anti_fraud_pass_cnt");
                    String curDayCnt = merged.getString("cur_day_cnt");
                    String centralBankInquiryCnt = merged.getString("central_bank_inquiry_cnt");
                    String centralBankPassCnt = merged.getString("central_bank_pass_cnt");
                    String firstLvlPass = merged.getString("first_lvl_pass");
                    String payCnt = merged.getString("pay_cnt");
                    String aliPayCnt = merged.getString("ali_pay_cnt");
                    String wechatPayCnt = merged.getString("wechat_pay_cnt");
                    String otherPayCnt = merged.getString("other_pay_cnt");
                    String payCloseCnt = merged.getString("pay_close_cnt");
                    String creditAuditPassCnt = merged.getString("credit_audit_pass_cnt");
                    String creditAuditRefuseCnt = merged.getString("credit_audit_refuse_cnt");
                    String threeLvlCnt = merged.getString("three_lvl_cnt");
                    String sendCnt = merged.getString("send_cnt");
                    String merchantShuntCnt = merged.getString("merchant_shunt_cnt");
                    String merchantAcceptCnt = merged.getString("merchant_accept_cnt");
                    String merchantPayCnt = merged.getString("merchant_pay_cnt");
                    String merchantPayCloseCnt = merged.getString("merchant_pay_close_cnt");
                    String merchantSendCnt = merged.getString("merchant_send_cnt");
                    String curPayCnt = merged.getString("cur_pay_cnt");
                    String curPayCloseCnt = merged.getString("cur_pay_close_cnt");
                    String curSendCnt = merged.getString("cur_send_cnt");
                    String curMerchantShuntCnt = merged.getString("cur_merchant_shunt_cnt");
                    String curMerchantAcceptCnt = merged.getString("cur_merchant_accept_cnt");
                    String curMerchantPayCnt = merged.getString("cur_merchant_pay_cnt");
                    String curMerchantPayCloseCnt = merged.getString("cur_merchant_pay_close_cnt");
                    String curMerchantSendCnt = merged.getString("cur_merchant_send_cnt");
                    riskRentSituationDO.setCountDay(countDay);
                    riskRentSituationDO.setBusinessChannel(Integer.valueOf(businessChannel));
                    riskRentSituationDO.setIsOnRent(Integer.valueOf(isOnRent));
                    riskRentSituationDO.setQuotientName(quotientName);
                    riskRentSituationDO.setScene(scene);
                    riskRentSituationDO.setRiskOpinion(riskOpinion);
                    riskRentSituationDO.setFinanceType(Integer.valueOf(financeType));
                    riskRentSituationDO.setCustomerType(Integer.valueOf(customerType));
                    riskRentSituationDO.setType(Integer.valueOf(type));
                    riskRentSituationDO.setRiskCnt(Integer.valueOf(riskCnt));
                    riskRentSituationDO.setPrepositionRiskCnt(Integer.valueOf(prepositionRiskCnt));
                    riskRentSituationDO.setPassCnt(Integer.valueOf(passCnt));
                    riskRentSituationDO.setAntiFraudPassCnt(Integer.valueOf(antiFraudPassCnt));
                    riskRentSituationDO.setCurDayCnt(Integer.valueOf(curDayCnt));
                    riskRentSituationDO.setCentralBankInquiryCnt(Integer.valueOf(centralBankInquiryCnt));
                    riskRentSituationDO.setCentralBankPassCnt(Integer.valueOf(centralBankPassCnt));
                    riskRentSituationDO.setFirstLvlPass(Integer.valueOf(firstLvlPass));
                    riskRentSituationDO.setPayCnt(Integer.valueOf(payCnt));
                    riskRentSituationDO.setAliPayCnt(Integer.valueOf(aliPayCnt));
                    riskRentSituationDO.setWechatPayCnt(Integer.valueOf(wechatPayCnt));
                    riskRentSituationDO.setOtherPayCnt(Integer.valueOf(otherPayCnt));
                    riskRentSituationDO.setPayCloseCnt(Integer.valueOf(payCloseCnt));
                    riskRentSituationDO.setCreditAuditPassCnt(Integer.valueOf(creditAuditPassCnt));
                    riskRentSituationDO.setCreditAuditRefuseCnt(Integer.valueOf(creditAuditRefuseCnt));
                    riskRentSituationDO.setThreeLvlCnt(Integer.valueOf(threeLvlCnt));
                    riskRentSituationDO.setSendCnt(Integer.valueOf(sendCnt));
                    riskRentSituationDO.setMerchantShuntCnt(Integer.valueOf(merchantShuntCnt));
                    riskRentSituationDO.setMerchantAcceptCnt(Integer.valueOf(merchantAcceptCnt));
                    riskRentSituationDO.setMerchantPayCnt(Integer.valueOf(merchantPayCnt));
                    riskRentSituationDO.setMerchantPayCloseCnt(Integer.valueOf(merchantPayCloseCnt));
                    riskRentSituationDO.setMerchantSendCnt(Integer.valueOf(merchantSendCnt));
                    riskRentSituationDO.setCurPayCnt(Integer.valueOf(curPayCnt));
                    riskRentSituationDO.setCurPayCloseCnt(Integer.valueOf(curPayCloseCnt));
                    riskRentSituationDO.setCurSendCnt(Integer.valueOf(curSendCnt));
                    riskRentSituationDO.setCurMerchantShuntCnt(Integer.valueOf(curMerchantShuntCnt));
                    riskRentSituationDO.setCurMerchantAcceptCnt(Integer.valueOf(curMerchantAcceptCnt));
                    riskRentSituationDO.setCurMerchantPayCnt(Integer.valueOf(curMerchantPayCnt));
                    riskRentSituationDO.setCurMerchantPayCloseCnt(Integer.valueOf(curMerchantPayCloseCnt));
                    riskRentSituationDO.setCurMerchantSendCnt(Integer.valueOf(curMerchantSendCnt));
                    return riskRentSituationDO;
                }
        ).collect(Collectors.toList());
    }
}
