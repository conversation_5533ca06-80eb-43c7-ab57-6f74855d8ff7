package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateGoodsDO;
import qnvip.data.overview.service.order.OperateGoodsService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商品销量分析
 *
 * <AUTHOR>
 * @Date 2021/9/30 10:25 上午
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateGoodsBusiness {

    private final OdpsUtil odpsUtil;
    private final OperateGoodsService goodsService;

    /**
     * 启动计算任务
     */
    public void runGoodsOrderCount(String ds) {
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        assert key2value != null;
        String fromTime = (String) key2value.get("fromTime");
        String toTime = (String) key2value.get("toTime");
        List<OperateGoodsDO> list = goodsOrderCount(countDay, fromTime, toTime, ds);
        // for (OperateGoodsDO goodsDO : list) {
        //     goodsService.saveOrUpdate(goodsDO);
        // }
        goodsService.removeDataByTime(countDay);
        goodsService.saveBatch(list);
    }

    /**
     * 商品销量分析、动销商品数,这个粒度的数据，在代码上做处理
     * 商品下单量分组
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<OperateGoodsDO> goodsOrderCount(LocalDateTime countDay, String sTime, String eTime, String ds) {
        StringBuilder sb = new StringBuilder(" select " +
                "c.first_category_ids as firstCategoryIds,c.second_category_ids as secondCategoryIds,b.item_id as itemId, " +
                "b.short_name as name,NVL(sum(b.actual_supply_price),0) as totalSupply,NVL(sum(b.operating_purchase_price),0) as totalBuy," +
                "NVL(d.repayment_term,0) as totalTerm,NVL(sum(d.rent_total),0) as totalPrice1,NVL(sum(d.buyout_amt),0) as totalPrice2," +
                " concat(b.color,' ',b.size) as model," +
                "count(1) as num,c.main_category as main_category from rent_order a inner join rent_order_item b on a.id = b.order_id inner join rent_item c on " +
                "b.item_id = c.id inner join rent_order_finance_detail d on a.id = d.order_id where a.biz_type = 2 and " +
                "a.merchant_id = 100 and a.payment_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds =" + ds);
        sb.append(" and b.ds = " + ds);
        sb.append(" and c.ds = " + ds);
        sb.append(" and d.ds = " + ds);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append(sTime);
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append(eTime);
        }
        sb.append(" group by b.item_id,b.short_name,b.color,b.size,c.main_category," +
                "c.first_category_ids,c.second_category_ids,d.repayment_term; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) return new ArrayList<>();
        //查询分类名称
        Map<String, String> categoryNameMap = new HashMap<>();
        String[] split;
        List<OperateGoodsDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            OperateGoodsDO goodsDO = new OperateGoodsDO();
            goodsDO.setCountDay(countDay);
            goodsDO.setItemId(Long.parseLong(record.getString("itemid")));
            goodsDO.setGoodsName(record.getString("name"));
            goodsDO.setPayCount(Long.parseLong(record.getString("num")));
            goodsDO.setModel(record.getString("model"));
            goodsDO.setTotalBuyPrice(new BigDecimal(record.getString("totalbuy")));
            goodsDO.setTotalTerm(Long.parseLong(record.getString("totalterm")));
            goodsDO.setTotalWorth(new BigDecimal(record.getString("totalprice1")).add(new BigDecimal(record.getString("totalprice2"))));
            goodsDO.setTotalSupplyPrice(new BigDecimal(record.getString("totalsupply")));
            goodsDO.setFirstCategoryIds(record.getString("firstcategoryids"));
            goodsDO.setSecondCategoryIds(record.getString("secondcategoryids"));
            goodsDO.setMainCategory(record.getString("main_category"));

            if (!categoryNameMap.containsKey(goodsDO.getMainCategory())) {
                //从odps上查分类名字
                String categoryName = getCategoryName(goodsDO.getMainCategory(),ds);
                //找不到数据，忽略
                categoryNameMap.put(goodsDO.getMainCategory(), categoryName);
                if (categoryName == null) {
                    log.error("mainCategoryId：{}找不到，忽略该数据", goodsDO.getMainCategory());
//                    continue;
                }
                goodsDO.setMainCategoryName(categoryName);
            } else {
                String categoryName = categoryNameMap.get(goodsDO.getMainCategory());
                if (categoryName == null) {
                    log.error("mainCategoryId：{}找不到，忽略该数据", goodsDO.getMainCategory());
//                    continue;
                }
                goodsDO.setMainCategoryName(categoryName);
            }
            if (!categoryNameMap.containsKey(goodsDO.getFirstCategoryIds())) {
                //从odps上查分类名字
                split = goodsDO.getFirstCategoryIds().split(",");

                StringBuilder csb = new StringBuilder();
                for (String s : split) {
                    if (!categoryNameMap.containsKey(s)) {
                        //从odps上查分类名字
                        String categoryName = getCategoryName(s,ds);
                        csb.append(",").append(categoryName);
                        categoryNameMap.put(s, categoryName);
                    } else {
                        csb.append(",").append(categoryNameMap.get(s));
                    }
                }
                goodsDO.setFirstCategoryNames(csb.deleteCharAt(0).toString());
            } else {
                String categoryName = categoryNameMap.get(goodsDO.getFirstCategoryIds());
                goodsDO.setFirstCategoryNames(categoryName);
            }
            if (StringUtils.isNotEmpty(goodsDO.getSecondCategoryIds())) {
                split = goodsDO.getSecondCategoryIds().split(",");
                StringBuilder csb = new StringBuilder();
                for (String s : split) {
                    if (!categoryNameMap.containsKey(s)) {
                        //从odps上查分类名字
                        String categoryName = getCategoryName(s,ds);
                        csb.append(",").append(categoryName);
                        categoryNameMap.put(s, categoryName);
                    } else {
                        csb.append(",").append(categoryNameMap.get(s));
                    }
                }
                goodsDO.setSecondCategoryNames(csb.deleteCharAt(0).toString());
            }
            Long goodsOrderCount = getGoodsOrderCount(goodsDO.getItemId(), sTime, eTime,ds);
            goodsDO.setOrderCount(goodsOrderCount);
            Long goodsSignCount = getGoodsSignCount(goodsDO.getItemId(), sTime, eTime,ds);
            goodsDO.setSignCount(goodsSignCount);
            Long goodsApprovedCount = getGoodsApprovedCount(goodsDO.getItemId(), sTime, eTime,ds);
            goodsDO.setApprovedCount(goodsApprovedCount);

            list.add(goodsDO);
        }
        return list;
    }


    /**
     * 查询分类
     *
     * @param id
     * @return
     */
    public String getCategoryName(String id,String ds ) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        StringBuilder csb = new StringBuilder(" select name from rent_category where id = ");
        csb.append(id);
        csb.append(" and ds = "+ds);
        csb.append(";");
        List<Record> recordList = odpsUtil.querySql(csb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return null;
        }
        return recordList.get(0).getString("name");
    }


    /**
     * 查询商品下单人数
     */
    private Long getGoodsOrderCount(Long itemId, String sTime, String eTime,String ds) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from rent_order a inner join rent_order_item b on a.id = b.order_id " +
                "where a.is_deleted = 0 and b.item_id = ");
        sb.append(itemId);
        sb.append(" and a.ds ="+ds);
        sb.append(" and b.ds = "+ds);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append(sTime);
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append(eTime);
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }

    /**
     * 签收单数
     *
     * @param itemId
     * @param sTime
     * @param eTime
     * @return
     */
    private Long getGoodsSignCount(Long itemId, String sTime, String eTime,String ds) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from  rent_order a inner" +
                " join rent_order_item b on a.id =b.order_id and b.item_type = 1 inner join rent_order_logistics c on b.logistics_id = c.id where" +
                " a.biz_type = 2 and a.merchant_id = 100 and a.payment_time is not null and c.sign_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds ="+ds);
        sb.append(" and b.ds ="+ds);
        sb.append(" and c.ds ="+ds);
        if (sTime != null) {
            sb.append(" and c.sign_time >= ").append(sTime);
        }
        if (eTime != null) {
            sb.append(" and c.sign_time <= ").append(eTime);
        }
        sb.append(" and b.item_id = ").append(itemId);
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }


    /**
     * 风控通过人数
     *
     * @param itemId
     * @param sTime
     * @param eTime
     * @return
     */
    private Long getGoodsApprovedCount(Long itemId, String sTime, String eTime,String ds) {
        StringBuilder sb = new StringBuilder(" select " +
                " count(a.customer_id) as num from rent_order a left join rent_order_audit b on a.id = b.order_id left join" +
                " rent_order_item c on a.id = c.order_id where a.biz_type = 2 and a.merchant_id = 100 and a.parent_id = 0 and b.type = 2" +
                " and b.audit_status = 1 and a.is_deleted = 0 and c.item_id = ");
        sb.append(itemId);
        sb.append(" and a.ds ="+ds);
        sb.append(" and b.ds ="+ds);
        sb.append(" and c.ds ="+ds);
        if (sTime != null) {
            sb.append(" and b.update_time >= ").append(sTime);
        }
        if (eTime != null) {
            sb.append(" and b.update_time <=  ").append(eTime);
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return 0l;
        }
        return Long.parseLong(recordList.get(0).getString("num"));
    }



}