// package qnvip.data.overview.business.risk;
//
// import cn.hutool.core.collection.CollUtil;
// import com.aliyun.odps.data.Record;
// import com.google.common.collect.Lists;
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Service;
// import qnvip.data.overview.domain.datacheck.DataCheckAfterRentOverviewDO;
// import qnvip.data.overview.domain.risk.RepaymentPlanDO;
// import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
// import qnvip.data.overview.domain.risk.RiskRenewOverdueBaseDO;
// import qnvip.data.overview.domain.risk.RiskRenewalOrderOverdueDO;
// import qnvip.data.overview.enums.finance.FinanceTypeEnum;
// import qnvip.data.overview.service.datacheck.DataCheckAfterRentOverviewService;
// import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
// import qnvip.data.overview.service.risk.RiskRenewOverdueBaseService;
// import qnvip.data.overview.service.risk.RiskRenewalOrderOverdueService;
// import qnvip.data.overview.util.CalculateUtil;
// import qnvip.data.overview.util.OdpsUtil;
// import qnvip.rent.common.base.MultiResult;
//
// import java.math.BigDecimal;
// import java.math.RoundingMode;
// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.LocalTime;
// import java.time.format.DateTimeFormatter;
// import java.util.*;
// import java.util.concurrent.LinkedBlockingQueue;
// import java.util.concurrent.ThreadPoolExecutor;
// import java.util.concurrent.TimeUnit;
// import java.util.concurrent.atomic.AtomicInteger;
// import java.util.function.Function;
// import java.util.stream.Collectors;
//
// /**
//  * create by gw on 2022/3/24
//  */
// @Slf4j
// @Service
// @RequiredArgsConstructor
// @Deprecated
// public class RiskRenewalOverdueBusinessNew {
//
//     private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//     private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//     private static final Integer PAGE_SIZE = 830;
//
//     private final OdpsUtil odpsUtil;
//     private final RiskRenewalOrderOverdueService riskRenewalOrderOverdueService;
//     private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
//     private final RiskRenewOverdueBaseService riskRenewOverdueBaseService;
//     private final DataCheckAfterRentOverviewService dataCheckAfterRentOverviewService;
//
//
//     private static AtomicInteger atomicInteger = new AtomicInteger();
//     private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
//             new LinkedBlockingQueue(1000), r -> {
//         Thread t = new Thread(r);
//         t.setName("RiskRenewalOverdueBusiness-" + atomicInteger.incrementAndGet());
//         return t;
//     });
//
//     /**
//      * @param ds
//      */
//     public void execOldData(String ds, int... overdueDays) {
//         // order总记录数
//         riskRenewalOrderOverdueService.deleteAll();
//
//         for (int overdueDay : overdueDays) {
//             Integer size = riskRenewOverdueBaseService.getNum(overdueDay);
//             int times = size / PAGE_SIZE;
//             if (size % PAGE_SIZE != 0) {
//                 times += 1;
//             }
//             // int times = 1;
//             int startPage = 0;
//             for (int i = 0; i < times; i++) {
//                 int p = startPage * PAGE_SIZE;
//                 threadPoolExecutor.execute(() -> {
//                     oldRepayTask(p, overdueDay);
//                 });
//                 startPage++;
//             }
//         }
//     }
//
//     /**
//      * 获取order最大记录数
//      */
//     private Integer getCount(String ds) {
//         String sql = "select count(*) num" +
//                 " from rent_order" +
//                 " where merchant_id = 100" +
//                 "  and termination != 5" +
//                 "  and biz_type = 2" +
//                 "  and type = 1" +
//                 "  and parent_id > 0" +
//                 "    AND date(rent_start_date) >= '2020-10-01'" +
//                 "  and ds=" + ds +
//                 ";";
//         List<Record> records = odpsUtil.querySql(sql);
//         return Integer.valueOf(records.get(0).getString("num"));
//     }
//
//
//     private void oldRepayTask(int startPage, Integer overdueDay) {
//         MultiResult<RiskRenewOverdueBaseDO> result = riskRenewOverdueBaseService.getList(overdueDay, PAGE_SIZE,
//                 startPage);
//         List<Long> collect = result.getData().stream().map(RiskRenewOverdueBaseDO::getOrderId).collect(Collectors.toList());
//         List<RiskRenewOverdueBaseDO> records = riskRenewOverdueBaseService.getListByOrderId(overdueDay, collect);
//         saveData(records, overdueDay);
//     }
//
//
//     private void saveData(List<RiskRenewOverdueBaseDO> records, Integer overdueDay) {
//         try {
//             List<RiskRenewalOrderOverdueDO> resList = fetchList(records, overdueDay);
//             riskRenewalOrderOverdueService.saveBatch(resList);
//             //  通用列表
//             List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay);
//             riskGeneralOrderOverdueService.updateBatchById(generalList);
//
//             List<DataCheckAfterRentOverviewDO> checkList = fetchAfterCheckList(records);
//             dataCheckAfterRentOverviewService.updateBatchById(checkList);
//         } catch (Exception e) {
//             log.error(e.getMessage(), e);
//         }
//     }
//
//
//     private List<RiskRenewalOrderOverdueDO> fetchList(List<RiskRenewOverdueBaseDO> records, Integer overdueDay) {
//         List<RepaymentPlanDO> planList = Lists.newArrayList();
//         if (CollUtil.isNotEmpty(records)) {
//             planList = records.stream().map(domain -> {
//                 RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
//                 Long orderId = domain.getOrderId();
//                 Integer term = domain.getTerm();
//                 Integer overdue = domain.getOverdue();
//                 Integer isDeleted = domain.getDeleted();
//                 Integer autoRenewal = domain.getAutoRenewal();
//                 Integer miniType = domain.getMiniType();
//                 Integer repayStatus = domain.getRepayStatus();
//                 Integer realRepayTimeStatus = domain.getRealRepayTimeStatus();
//                 String platform = domain.getPlatform();
//                 String no = domain.getOrderNo();
//                 Integer financeType = domain.getFinanceType();
//                 String riskLevel = domain.getRiskLevel();
//                 String riskStrategy = domain.getRiskStrategy();
//                 Integer auditType = domain.getAuditType().equals("自动风控") ? 1 : 0;
//                 Integer isSettle = domain.getIsSettle();
//                 Integer forcedConversion = domain.getForcedConversion();
//                 String isOverdue = String.valueOf(domain.getIsOverdue());
//                 BigDecimal capital = domain.getCapital();
//                 BigDecimal realCapital = domain.getRealCapital();
//                 LocalDateTime rentStartDay = domain.getCountDay();
//                 LocalDateTime repayDate = domain.getRepayDate();
//                 LocalDateTime realRepayTime = domain.getRealRepayTime();
//
//                 BigDecimal discountReturnAmt = domain.getDiscountReturnAmt();
//                 BigDecimal rentTotal = domain.getRentTotal();
//                 BigDecimal overdueFine = domain.getOverdueFine();
//                 BigDecimal bondAmt = domain.getBondAmt();
//                 double bondRate = domain.getBondRate();
//                 BigDecimal beforeDiscount = domain.getBeforeDiscount();
//                 repaymentPlanDO.setCapital(capital);
//                 repaymentPlanDO.setRealRepayTime(realRepayTime);
//                 repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
//                 repaymentPlanDO.setRealCapital(realCapital);
//                 repaymentPlanDO.setRepayDate(repayDate);
//                 repaymentPlanDO.setRealRepayTime(realRepayTime);
//                 repaymentPlanDO.setOrderId(orderId);
//                 repaymentPlanDO.setTerm(term);
//                 repaymentPlanDO.setOverdue(overdue);
//                 repaymentPlanDO.setRenewWay(autoRenewal);
//                 repaymentPlanDO.setIsSettle(isSettle);
//                 repaymentPlanDO.setIsDeleted(isDeleted);
//                 repaymentPlanDO.setBondRate(bondRate);
//                 repaymentPlanDO.setRepayStatus(repayStatus);
//                 repaymentPlanDO.setIsOverdue(isOverdue);
//                 repaymentPlanDO.setNo(no);
//                 repaymentPlanDO.setRentStartDate(rentStartDay);
//                 repaymentPlanDO.setMiniType(miniType);
//                 repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
//                         forcedConversion)));
//                 repaymentPlanDO.setAuditType(String.valueOf(auditType));
//                 repaymentPlanDO.setPlatform(platform);
//                 repaymentPlanDO.setRiskLevel(riskLevel);
//                 repaymentPlanDO.setRiskStrategy(riskStrategy);
//                 repaymentPlanDO.setRentTotal(rentTotal);
//                 repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
//                 repaymentPlanDO.setOverdueFine(overdueFine);
//                 repaymentPlanDO.setBondAmt(bondAmt);
//                 repaymentPlanDO.setBeforeDiscount(beforeDiscount);
//                 return repaymentPlanDO;
//             }).collect(Collectors.toList());
//         }
//
//         List<RiskRenewalOrderOverdueDO> resList = new ArrayList<>();
//         Map<Long, List<RepaymentPlanDO>> orderId2List =
//                 planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));
//
//         for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
//             Long orderId = entry.getKey();
//             List<RepaymentPlanDO> value = entry.getValue();
//             RiskRenewalOrderOverdueDO riskOrderOverdueDO = null;
//             Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
//                     value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
//             Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
//             for (RepaymentPlanDO repaymentPlanDO : value) {
//                 if (riskOrderOverdueDO == null) {
//                     riskOrderOverdueDO = new RiskRenewalOrderOverdueDO();
//                     resList.add(riskOrderOverdueDO);
//                     riskOrderOverdueDO.setOrderId(orderId);
//                     riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
//                     riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
//                     riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
//                     riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
//                     riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
//                     riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
//                     riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
//                     riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
//                     riskOrderOverdueDO.setDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
//                     riskOrderOverdueDO.setBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
//                     riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRate());
//                     riskOrderOverdueDO.setIsSettle(repaymentPlanDO.getIsSettle());
//                     riskOrderOverdueDO.setOverdueDay(overdueDay);
//                     riskOrderOverdueDO.setRenewWay(repaymentPlanDO.getRenewWay());
//                     riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
//                 }
//
//                 int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
//                 int term = repaymentPlanDO.getTerm();
//                 //对当前期逾期情况做修正
//                 if (isOverdue == 2) {
//                     isOverdue = updateOverdueStatus(term2PlanDo, term);
//                     repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
//                     repaymentPlanDO.setOverdue(5);
//                 }
//                 LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
//                 riskOrderOverdueDO.setCountDay(rentStartDay);
//                 riskOrderOverdueDO.setMaxDay(maxTerm);
//                 setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
//                 RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
//                 if (isOverdue == 1) {
//                     Map<String, Object> resMap = new HashMap<>();
//                     resMap.put("term", BigDecimal.valueOf(term));
//                     resMap.put("capital", BigDecimal.ZERO);
//                     LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
//                     // 封账日期
//                     LocalDate localDate =
//                             LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                     LocalDate maxRepayDate =
//                             LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                     resMap.put("maxRepayDate", maxRepayDate);
//                     resMap.put("endTime", localDate);
//                     // 获取逾期未还金额
//                     fetchCapital(term2PlanDo, resMap);
//                     // 设置对应期数未还金额
//                     BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
//                     setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
//                 } else {
//                     // 未逾期，逾期未还金额为0
//                     setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
//                 }
//             }
//         }
//         return resList;
//     }
//
//     private int updateOverdueStatus(Map<Integer, List<RepaymentPlanDO>> term2PlanDo, int term) {
//         List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(--term);
//         RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
//         if (repaymentPlanDOList.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         int isOverdue = Integer.parseInt(planDO.getIsOverdue());
//         // 上一期逾期并且未还款,这一期即使还没到,算逾期
//         // 上一期逾期并且还款,这一期还没到,不算逾期
//         // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
//         // 如果当前期逾期,不管有没有到封账日,都算逾期
//         int repayStatus = planDO.getRepayStatus();
//         return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
//     }
//
//     private List<RiskGeneralOrderOverdueDO> fetchGeneralList(List<RiskRenewOverdueBaseDO> records, Integer overdueDay) {
//         List<RepaymentPlanDO> planList = Lists.newArrayList();
//         List<Long> parentIds = Lists.newArrayList();
//         if (CollUtil.isNotEmpty(records)) {
//             planList = records.stream().map(domain -> {
//                 RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
//                 Long orderId = domain.getOrderId();
//                 Long parentId = domain.getParentId();
//                 parentIds.add(parentId);
//                 Integer term = domain.getTerm();
//                 Integer overdue = domain.getOverdue();
//                 Integer isDeleted = domain.getDeleted();
//                 Integer autoRenewal = domain.getAutoRenewal();
//                 Integer miniType = domain.getMiniType();
//                 Integer repayStatus = domain.getRepayStatus();
//                 Integer realRepayTimeStatus = domain.getRealRepayTimeStatus();
//                 String platform = domain.getPlatform();
//                 String no = domain.getOrderNo();
//                 Integer financeType = domain.getFinanceType();
//                 String riskLevel = domain.getRiskLevel();
//                 String riskStrategy = domain.getRiskStrategy();
//                 String auditType = String.valueOf(domain.getAuditType());
//                 Integer isSettle = domain.getIsSettle();
//                 Integer forcedConversion = domain.getForcedConversion();
//                 String isOverdue = String.valueOf(domain.getIsOverdue());
//                 BigDecimal capital = domain.getCapital();
//                 BigDecimal realCapital = domain.getRealCapital();
//                 LocalDateTime rentStartDay = domain.getCountDay();
//                 LocalDateTime repayDate = domain.getRepayDate();
//                 LocalDateTime settleDate = domain.getSettleDate();
//                 LocalDateTime realRepayTime = domain.getRealRepayTime();
//                 BigDecimal discountReturnAmt = domain.getDiscountReturnAmt();
//                 BigDecimal rentTotal = domain.getRentTotal();
//                 BigDecimal overdueFine = domain.getOverdueFine();
//                 BigDecimal bondAmt = domain.getBondAmt();
//                 double bondRate = domain.getBondRate();
//                 BigDecimal beforeDiscount = domain.getBeforeDiscount();
//                 repaymentPlanDO.setCapital(capital);
//                 repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
//                 repaymentPlanDO.setRealCapital(realCapital);
//                 repaymentPlanDO.setOverdue(overdue);
//                 repaymentPlanDO.setRepayDate(repayDate);
//                 repaymentPlanDO.setRealRepayTime(realRepayTime);
//                 repaymentPlanDO.setOrderId(orderId);
//                 repaymentPlanDO.setSettleDate(settleDate);
//                 repaymentPlanDO.setTerm(term);
//                 repaymentPlanDO.setParentId(parentId);
//                 repaymentPlanDO.setRenewWay(autoRenewal);
//                 repaymentPlanDO.setIsSettle(isSettle);
//                 repaymentPlanDO.setIsDeleted(isDeleted);
//                 repaymentPlanDO.setBondRate(bondRate);
//                 repaymentPlanDO.setRepayStatus(repayStatus);
//                 repaymentPlanDO.setIsOverdue(isOverdue);
//                 repaymentPlanDO.setRentStartDate(rentStartDay);
//                 repaymentPlanDO.setMiniType(miniType);
//                 repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
//                         forcedConversion)));
//                 repaymentPlanDO.setAuditType(auditType);
//                 repaymentPlanDO.setPlatform(platform);
//                 repaymentPlanDO.setRiskLevel(riskLevel);
//                 repaymentPlanDO.setRiskStrategy(riskStrategy);
//                 repaymentPlanDO.setRentTotal(rentTotal);
//                 repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
//                 repaymentPlanDO.setOverdueFine(overdueFine);
//                 repaymentPlanDO.setNo(no);
//                 repaymentPlanDO.setBondAmt(bondAmt);
//                 repaymentPlanDO.setBeforeDiscount(beforeDiscount);
//                 return repaymentPlanDO;
//             }).collect(Collectors.toList());
//         }
//         List<RiskGeneralOrderOverdueDO> parentList = riskGeneralOrderOverdueService.getList(parentIds, overdueDay);
//         Map<Long, RiskGeneralOrderOverdueDO> orderId2DO =
//                 parentList.stream().collect(Collectors.toMap(RiskGeneralOrderOverdueDO::getOrderId, Function.identity()));
//
//
//         List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
//         Map<Long, List<RepaymentPlanDO>> orderId2List =
//                 planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));
//
//         for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
//             List<RepaymentPlanDO> value = entry.getValue();
//             Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
//                     value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
//
//             // 获取一个还款计划对象
//             RepaymentPlanDO repaymentPlanDO1 = value.get(0);
//             Long parentId = repaymentPlanDO1.getParentId();
//             int isSettle = repaymentPlanDO1.getIsSettle();
//             // 获取父订单对象
//             RiskGeneralOrderOverdueDO parentDO = orderId2DO.get(parentId);
//             if (parentDO == null) {
//                 continue;
//             }
//             parentDO.setDiscountReturnAmt(repaymentPlanDO1.getDiscountReturnAmt());
//             parentDO.setBeforeDiscount(repaymentPlanDO1.getBeforeDiscount());
//             parentDO.setRenewWay(repaymentPlanDO1.getRenewWay());
//             parentDO.setChildNo(repaymentPlanDO1.getNo());
//             parentDO.setChildOrderId(repaymentPlanDO1.getOrderId());
//             resList.add(parentDO);
//
//             List<RepaymentPlanDO> collect = value.stream().
//                     sorted(Comparator.comparing(RepaymentPlanDO::getTerm)).collect(Collectors.toList());
//
//             // 获取父订单最后一期的未还租金
//             BigDecimal parentNotReturnAmt = getParentNotReturnAmt(parentDO, parentDO.getPMaxTerm());
//             Integer renewMaxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
//             for (RepaymentPlanDO repaymentPlanDO : collect) {
//
//                 int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
//                 Integer pMaxTerm = parentDO.getPMaxTerm();
//                 int term = repaymentPlanDO.getTerm();
//                 //对当前期逾期情况做修正
//                 if (isOverdue == 2) {
//                     isOverdue = updateOverdueStatus(term2PlanDo, term);
//                     repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
//                     repaymentPlanDO.setOverdue(5);
//                 }
//                 parentDO.setMaxTerm(pMaxTerm + renewMaxTerm);
//                 setOverdueVal(parentDO, term + pMaxTerm, isOverdue, repaymentPlanDO.getOverdueFine());
//                 LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
//                 LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                 RepaymentPlanDO maxTerPlanDO = getPlanDo(value, renewMaxTerm, term2PlanDo);
//                 LocalDate maxRepayDate =
//                         LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                 // 设置续租订单逾期未还金额
//                 if (isOverdue == 1) {
//                     Map<String, Object> resMap = new HashMap<>();
//                     resMap.put("term", BigDecimal.valueOf(term));
//                     resMap.put("capital", BigDecimal.ZERO);
//                     // 封账日期
//                     resMap.put("endTime", localDate);
//                     resMap.put("maxRepayDate", maxRepayDate);
//                     // 获取逾期未还金额
//                     fetchCapital(term2PlanDo, resMap);
//                     // 设置对应期数未还金额
//                     BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
//                     // term + pMaxTerm 续租订单连上主订单的期数
//                     setRenewAmtVal(parentDO, term + pMaxTerm, decimal);
//                 } else {
//                     // 未逾期，逾期未还金额为0
//                     setRenewAmtVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
//                 }
//
//                 // 设置续租订单的未还租金为父订单的逾期未还金额
//                 LocalDateTime settleDate = repaymentPlanDO1.getSettleDate();
//                 LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
//                 if (isSettle == 1 && settleDate != null && settleDate.compareTo(endTime) <= 0) {
//                     // 父订单已结清，并且结清时间小于等于这一期封账,就是未逾期.直接为0,
//                     continue;
//                 }
//                 // 设置续租订单逾期未还金额
//                 if (isOverdue == 1) {
//                     // 获取逾期未还金额
//                     // 设置对应期数未还金额
//                     setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
//                 } else {
//                     parentNotReturnAmt = BigDecimal.ZERO;
//                     // 未逾期，逾期未还金额为0
//                     setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
//                 }
//             }
//         }
//         return resList;
//     }
//
//
//     private List<DataCheckAfterRentOverviewDO> fetchAfterCheckList(List<RiskRenewOverdueBaseDO> records) {
//         List<Long> parentIds = records.stream().map(RiskRenewOverdueBaseDO::getParentId).collect(Collectors.toList());
//         //  获取父订单信息
//         List<DataCheckAfterRentOverviewDO> parentList = dataCheckAfterRentOverviewService.getList(parentIds);
//         Map<Long, DataCheckAfterRentOverviewDO> orderId2DO =
//                 parentList.stream().collect(Collectors.toMap(DataCheckAfterRentOverviewDO::getOrderId, Function.identity()));
//
//         List<DataCheckAfterRentOverviewDO> resList = new ArrayList<>();
//         Map<Long, List<RiskRenewOverdueBaseDO>> orderId2List =
//                 records.stream().collect(Collectors.groupingBy(RiskRenewOverdueBaseDO::getParentId));
//
//         for (Map.Entry<Long, List<RiskRenewOverdueBaseDO>> entry : orderId2List.entrySet()) {
//             List<RiskRenewOverdueBaseDO> value = entry.getValue();
//
//             // 获取一个还款计划对象
//             RiskRenewOverdueBaseDO renewOverdueBaseDO = value.get(0);
//             Long parentId = entry.getKey();
//             // 获取父订单对象
//             DataCheckAfterRentOverviewDO parentDO = orderId2DO.get(parentId);
//             if (parentDO == null) {
//                 continue;
//             }
//             parentDO.setChildSurplusAmt(renewOverdueBaseDO.getSurplusAmt());
//             parentDO.setChildStatus(renewOverdueBaseDO.getOrderStatus());
//             parentDO.setChildNo(renewOverdueBaseDO.getOrderNo());
//             parentDO.setChildOrderId(renewOverdueBaseDO.getOrderId());
//             parentDO.setChildOverdueDay(renewOverdueBaseDO.getCurrentOverdueDays());
//             parentDO.setRenewType(renewOverdueBaseDO.getRenewType());
//             resList.add(parentDO);
//         }
//         return resList;
//     }
//
//     private RepaymentPlanDO getPlanDo(List<RepaymentPlanDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanDO>> term2PlanDo) {
//         List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
//         RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
//         if (value.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         return planDO;
//     }
//
//     private BigDecimal getParentNotReturnAmt(RiskGeneralOrderOverdueDO parentDO, Integer term) {
//         BigDecimal amt = BigDecimal.ZERO;
//         if (term == 1) {
//             amt = parentDO.getTerm1();
//         } else if (term == 2) {
//             amt = parentDO.getTerm2();
//         } else if (term == 3) {
//             amt = parentDO.getTerm3();
//         } else if (term == 4) {
//             amt = parentDO.getTerm4();
//         } else if (term == 5) {
//             amt = parentDO.getTerm5();
//         } else if (term == 6) {
//             amt = parentDO.getTerm6();
//         } else if (term == 7) {
//             amt = parentDO.getTerm7();
//         } else if (term == 8) {
//             amt = parentDO.getTerm8();
//         } else if (term == 9) {
//             amt = parentDO.getTerm9();
//         } else if (term == 10) {
//             amt = parentDO.getTerm10();
//         } else if (term == 11) {
//             amt = parentDO.getTerm11();
//         } else if (term == 12) {
//             amt = parentDO.getTerm12();
//         }
//         return amt;
//     }
//
//     private BigDecimal stringToDecimal(String val) {
//         if ("\\N".equals(val)) {
//             val = "0";
//         }
//         return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
//     }
//
//
//     /**
//      * 递归查找，最早的的逾期期数
//      *
//      * @param map term为key，还款计划为val
//      * @param
//      */
//     public static void fetchTerm(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
//         int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         int level = (int) resMap.get("level");
//         List<RepaymentPlanDO> planDOList = map.get(key);
//         RepaymentPlanDO planDO = planDOList.get(0);
//         if (planDOList.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : planDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         if (planDO != null) {
//             String isOverdue = planDO.getIsOverdue();
//             LocalDateTime realRepayTime = planDO.getRealRepayTime();
//             Integer repayStatus = planDO.getRepayStatus();
//             Integer overdue = planDO.getOverdue();
//             BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));
//             LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
//                     dateFormatter), LocalTime.MIN);
//             LocalDateTime maxRepayDate = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("maxRepayDate")),
//                     dateFormatter), LocalTime.MAX);
//
//             LocalDateTime currentTime = LocalDateTime.now();
//             //还款日期在本期封账之前还的就截止
//             // "还款日期" >= "本期封账"
//             if ("1".equals(isOverdue)) {
//                 //先判断本期的状态是否已逾期未还款
//                 if (repayStatus == 1 && overdue == 5) {
//                     if (term.compareTo(new BigDecimal(1)) == 0) {
//                         resMap.put("term", term);
//                     } else {
//                         // 递归判断判断上一期的订单是否逾期
//                         term = term.subtract(BigDecimal.valueOf(1));
//                         resMap.put("term", term);
//                         fetchTerm(map, resMap);
//                     }
//                 } else {
//                     // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
//                     // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
//                     // 再递归往上判断
//                     if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
//                         if (term.compareTo(new BigDecimal(1)) == 0) {
//                             resMap.put("term", term);
//                         } else {
//                             term = term.subtract(BigDecimal.valueOf(1));
//                             resMap.put("term", term);
//                             fetchTerm(map, resMap);
//                         }
//                     } else {
//                         term = term.add(BigDecimal.valueOf(1));
//                         resMap.put("term", term);
//                     }
//                 }
//
//             } else {
//                 term = term.add(BigDecimal.valueOf(1));
//                 resMap.put("term", term);
//             }
//
//         }
//     }
//
//     public static void fetchCapital(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
//         int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         LocalDateTime maxRepayDate = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("maxRepayDate")),
//                 dateFormatter), LocalTime.MAX);
//         resMap.put("level", 1);
//         if (sourceTerm > 1) {
//             fetchTerm(map, resMap);
//         }
//         int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         for (Map.Entry<Integer, List<RepaymentPlanDO>> entry : map.entrySet()) {
//             List<RepaymentPlanDO> value = entry.getValue();
//             RepaymentPlanDO planDO = value.get(0);
//             if (value.size() > 1) {
//                 for (RepaymentPlanDO repaymentPlanDO : value) {
//                     if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                         planDO = repaymentPlanDO;
//                     }
//                 }
//             }
//             if (entry.getKey() >= term) {
//                 BigDecimal capital;
//                 if (term == entry.getKey()) {
//                     if (planDO.getRealRepayTime() == null) {
//                         capital = planDO.getCapital();
//                         // 实际还款时间大于最大封账日,那么这一期将当没还
//                     } else if (planDO.getRealRepayTime() != null && planDO.getRealRepayTime().compareTo(maxRepayDate) > 0) {
//                         capital = planDO.getCapital();
//                     } else {
//                         capital = planDO.getRealCapital();
//                     }
//                 } else {
//                     capital = planDO.getCapital();
//                 }
//                 resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
//             }
//         }
//     }
//
//
//     private void setOverdueVal(RiskRenewalOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
//                                BigDecimal overdueFine) {
//         if (term == 1) {
//             riskOrderOverdueDO.setIsOverdue1(isOverdue);
//             riskOrderOverdueDO.setOverdueFine1(overdueFine);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setIsOverdue2(isOverdue);
//             riskOrderOverdueDO.setOverdueFine2(overdueFine);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setIsOverdue3(isOverdue);
//             riskOrderOverdueDO.setOverdueFine3(overdueFine);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setIsOverdue4(isOverdue);
//             riskOrderOverdueDO.setOverdueFine4(overdueFine);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setIsOverdue5(isOverdue);
//             riskOrderOverdueDO.setOverdueFine5(overdueFine);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setIsOverdue6(isOverdue);
//             riskOrderOverdueDO.setOverdueFine6(overdueFine);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setIsOverdue7(isOverdue);
//             riskOrderOverdueDO.setOverdueFine7(overdueFine);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setIsOverdue8(isOverdue);
//             riskOrderOverdueDO.setOverdueFine8(overdueFine);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setIsOverdue9(isOverdue);
//             riskOrderOverdueDO.setOverdueFine9(overdueFine);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setIsOverdue10(isOverdue);
//             riskOrderOverdueDO.setOverdueFine10(overdueFine);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setIsOverdue11(isOverdue);
//             riskOrderOverdueDO.setOverdueFine11(overdueFine);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setIsOverdue12(isOverdue);
//             riskOrderOverdueDO.setOverdueFine12(overdueFine);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setIsOverdue13(isOverdue);
//             riskOrderOverdueDO.setOverdueFine13(overdueFine);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setIsOverdue14(isOverdue);
//             riskOrderOverdueDO.setOverdueFine14(overdueFine);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setIsOverdue15(isOverdue);
//             riskOrderOverdueDO.setOverdueFine15(overdueFine);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setIsOverdue16(isOverdue);
//             riskOrderOverdueDO.setOverdueFine16(overdueFine);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setIsOverdue17(isOverdue);
//             riskOrderOverdueDO.setOverdueFine17(overdueFine);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setIsOverdue18(isOverdue);
//             riskOrderOverdueDO.setOverdueFine18(overdueFine);
//         }
//     }
//
//     private void setOverdueVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
//                                BigDecimal overdueFine) {
//         if (term == 1) {
//             riskOrderOverdueDO.setIsOverdue1(isOverdue);
//             riskOrderOverdueDO.setOverdueFine1(overdueFine);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setIsOverdue2(isOverdue);
//             riskOrderOverdueDO.setOverdueFine2(overdueFine);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setIsOverdue3(isOverdue);
//             riskOrderOverdueDO.setOverdueFine3(overdueFine);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setIsOverdue4(isOverdue);
//             riskOrderOverdueDO.setOverdueFine4(overdueFine);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setIsOverdue5(isOverdue);
//             riskOrderOverdueDO.setOverdueFine5(overdueFine);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setIsOverdue6(isOverdue);
//             riskOrderOverdueDO.setOverdueFine6(overdueFine);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setIsOverdue7(isOverdue);
//             riskOrderOverdueDO.setOverdueFine7(overdueFine);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setIsOverdue8(isOverdue);
//             riskOrderOverdueDO.setOverdueFine8(overdueFine);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setIsOverdue9(isOverdue);
//             riskOrderOverdueDO.setOverdueFine9(overdueFine);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setIsOverdue10(isOverdue);
//             riskOrderOverdueDO.setOverdueFine10(overdueFine);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setIsOverdue11(isOverdue);
//             riskOrderOverdueDO.setOverdueFine11(overdueFine);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setIsOverdue12(isOverdue);
//             riskOrderOverdueDO.setOverdueFine12(overdueFine);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setIsOverdue13(isOverdue);
//             riskOrderOverdueDO.setOverdueFine13(overdueFine);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setIsOverdue14(isOverdue);
//             riskOrderOverdueDO.setOverdueFine14(overdueFine);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setIsOverdue15(isOverdue);
//             riskOrderOverdueDO.setOverdueFine15(overdueFine);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setIsOverdue16(isOverdue);
//             riskOrderOverdueDO.setOverdueFine16(overdueFine);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setIsOverdue17(isOverdue);
//             riskOrderOverdueDO.setOverdueFine17(overdueFine);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setIsOverdue18(isOverdue);
//             riskOrderOverdueDO.setOverdueFine18(overdueFine);
//         }
//     }
//
//     private void setTermNotRepayVal(RiskRenewalOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
//         if (term == 1) {
//             riskOrderOverdueDO.setTerm1(decimal);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setTerm2(decimal);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setTerm3(decimal);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setTerm4(decimal);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setTerm5(decimal);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setTerm6(decimal);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setTerm7(decimal);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setTerm8(decimal);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setTerm9(decimal);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setTerm10(decimal);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setTerm11(decimal);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setTerm12(decimal);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setTerm13(decimal);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setTerm14(decimal);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setTerm15(decimal);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setTerm16(decimal);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setTerm17(decimal);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setTerm18(decimal);
//         }
//
//     }
//
//     private void setTermNotRepayVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
//
//         if (term == 1) {
//             riskOrderOverdueDO.
//                     setTerm1(decimal);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setTerm2(decimal);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setTerm3(decimal);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setTerm4(decimal);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setTerm5(decimal);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setTerm6(decimal);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setTerm7(decimal);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setTerm8(decimal);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setTerm9(decimal);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setTerm10(decimal);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setTerm11(decimal);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setTerm12(decimal);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setTerm13(decimal);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setTerm14(decimal);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setTerm15(decimal);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setTerm16(decimal);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setTerm17(decimal);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setTerm18(decimal);
//         } else if (term == 19) {
//             riskOrderOverdueDO.setTerm19(decimal);
//         } else if (term == 20) {
//             riskOrderOverdueDO.setTerm20(decimal);
//         } else if (term == 21) {
//             riskOrderOverdueDO.setTerm21(decimal);
//         } else if (term == 22) {
//             riskOrderOverdueDO.setTerm22(decimal);
//         } else if (term == 23) {
//             riskOrderOverdueDO.setTerm23(decimal);
//         } else if (term == 24) {
//             riskOrderOverdueDO.setTerm24(decimal);
//         } else if (term == 25) {
//             riskOrderOverdueDO.setTerm25(decimal);
//         } else if (term == 26) {
//             riskOrderOverdueDO.setTerm26(decimal);
//         } else if (term == 27) {
//             riskOrderOverdueDO.setTerm27(decimal);
//         } else if (term == 28) {
//             riskOrderOverdueDO.setTerm28(decimal);
//         } else if (term == 29) {
//             riskOrderOverdueDO.setTerm29(decimal);
//         } else if (term == 30) {
//             riskOrderOverdueDO.setTerm30(decimal);
//         }
//
//     }
//
//     private void setRenewAmtVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
//         if (term == 1) {
//             riskOrderOverdueDO.setRenewAmt1(decimal);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setRenewAmt2(decimal);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setRenewAmt3(decimal);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setRenewAmt4(decimal);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setRenewAmt5(decimal);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setRenewAmt6(decimal);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setRenewAmt7(decimal);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setRenewAmt8(decimal);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setRenewAmt9(decimal);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setRenewAmt10(decimal);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setRenewAmt11(decimal);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setRenewAmt12(decimal);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setRenewAmt13(decimal);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setRenewAmt14(decimal);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setRenewAmt15(decimal);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setRenewAmt16(decimal);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setRenewAmt17(decimal);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setRenewAmt18(decimal);
//         } else if (term == 19) {
//             riskOrderOverdueDO.setRenewAmt19(decimal);
//         } else if (term == 20) {
//             riskOrderOverdueDO.setRenewAmt20(decimal);
//         } else if (term == 21) {
//             riskOrderOverdueDO.setRenewAmt21(decimal);
//         } else if (term == 22) {
//             riskOrderOverdueDO.setRenewAmt22(decimal);
//         } else if (term == 23) {
//             riskOrderOverdueDO.setRenewAmt23(decimal);
//         } else if (term == 24) {
//             riskOrderOverdueDO.setRenewAmt24(decimal);
//         } else if (term == 25) {
//             riskOrderOverdueDO.setRenewAmt25(decimal);
//         } else if (term == 26) {
//             riskOrderOverdueDO.setRenewAmt26(decimal);
//         } else if (term == 27) {
//             riskOrderOverdueDO.setRenewAmt27(decimal);
//         } else if (term == 28) {
//             riskOrderOverdueDO.setRenewAmt28(decimal);
//         } else if (term == 29) {
//             riskOrderOverdueDO.setRenewAmt29(decimal);
//         } else if (term == 30) {
//             riskOrderOverdueDO.setRenewAmt30(decimal);
//         }
//
//     }
//
// }
//
//
