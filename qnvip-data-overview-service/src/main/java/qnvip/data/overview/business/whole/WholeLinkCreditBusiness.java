package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkCreditDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkCreditService;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkCreditBusiness {

    private final OdpsUtil odpsUtil;
    private final WholeLinkCreditService wholeLinkCreditService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkCreditBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkCreditDO> miniType2Map, WholeLinkCreditDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkCreditDO ac = new WholeLinkCreditDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    private void assembleValue(Map<String, WholeLinkCreditDO> miniType2Map,
                               List<WholeLinkCreditDO> list,
                               String... fields) throws Exception {

        for (WholeLinkCreditDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkCreditDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkCreditDO> miniType2Map = new HashMap<>();


            CompletableFuture<List<WholeLinkCreditDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getCreditByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkCreditDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getCredit(ds, hour), threadPoolExecutor);

            CompletableFuture.allOf(f3, f4).join();

            assembleValue(miniType2Map, f3.get(), "creditThrough", "onQueryT0", "onQueryT1", "queryUv", "resultT0", "resultT1", "resultTimeT0", "resultTimeT1",
                    "timeConsume", "fjThrough", "rhThrough", "ahThrough", "creditPut", "fjPut", "rhPut", "ahPut");
            assembleValue(miniType2Map, f4.get(), "creditThrough", "onQueryT0", "onQueryT1", "queryUv", "resultT0", "resultT1", "resultTimeT0", "resultTimeT1",
                    "timeConsume", "fjThrough", "rhThrough", "ahThrough", "creditPut", "fjPut", "rhPut", "ahPut");

            List<WholeLinkCreditDO> list = Lists.newArrayList(miniType2Map.values());
            LocalDate now = LocalDate.now();
            wholeLinkCreditService.removeByHour(hour, now);
            wholeLinkCreditService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    /**
     * 获取征信指标
     */
    private List<WholeLinkCreditDO> getCreditByMiniType(String ds, Integer hour) {
        String sql = "select ro.mini_type,count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = " +
                " date(getdate()), ru.idcardNo, null)) credit_through," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null)) credit_put," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(cl.createTime) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     on_query_t0," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(cl.createTime) < date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     on_query_t1," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     result_t0," +
                "   " +
                "          nvl(sum(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                              date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())," +
                "                              datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       'ss')," +
                "                              null)) /" +
                "              count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                                date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null))," +
                "              0)                                                                                                       result_time_t0," +
                "   " +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                            date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     result_t1," +
                "   " +
                "          nvl(sum(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                              date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())," +
                "                              datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       'ss')," +
                "                              null)) /" +
                "              count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                                date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null))," +
                "              0)                                                                                                       result_time_t1," +
                "          nvl((sum(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                " ," +
                "                           datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                    to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                    'ss'), null)) /" +
                "           count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                             null))),0)                                                             " +
                "                      time_consume," +
                "          count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel in (3, 30)," +
                "                            ru.idcardNo," +
                "                            null))                                                                                 fj_through    ," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel in (3, 30)," +
                "                            ru.idcardNo," +
                "                            null))                                                                                     fj_put," +
                "          count(distinct" +
                "                if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 0, ru.idcardNo," +
                "                   null))                                                                                           rh_through   ," +
                "          count(distinct" +
                "                if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 0, ru.idcardNo," +
                "                   null))                                                                                             rh_put ," +
                "          count(distinct" +
                "                if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 2, ru.idcardNo," +
                "                   null))                                                                                             ah_through ," +
                "          count(distinct" +
                "                if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 2, ru.idcardNo," +
                "                   null))                                                                                             ah_put ," +
                "          count(distinct" +
                "                if(ca.status = 8 and date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                   null))                                                                                              query_uv" +
                "   from cl_loan cl" +
                "            left join rent_order ro" +
                "                      on cl.loanno = ro.no" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join cd_report_apply ca on ru.idcardNo = ca.idcardNo" +
                "   where cl.ds = " + ds +
                "     and ro.ds =  " + ds +
                "     and ru.ds =  " + ds +
                "     and ca.ds = " + ds + "" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkCreditDO domain = new WholeLinkCreditDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long creditThrough = Long.parseLong(record.getString("credit_through"));
            Long onQueryT0 = Long.parseLong(record.getString("on_query_t0"));
            Long onQueryT1 = Long.parseLong(record.getString("on_query_t1"));
            Long queryUv = Long.parseLong(record.getString("query_uv"));
            Long resultT0 = Long.parseLong(record.getString("result_t0"));
            Long resultT1 = Long.parseLong(record.getString("result_t1"));
            Double resultTimeT0 = Double.parseDouble(record.getString("result_time_t0"));
            Double resultTimeT1 = Double.parseDouble(record.getString("result_time_t1"));
            Double timeConsume = Double.parseDouble(record.getString("time_consume"));
            Long fjThrough = Long.parseLong(record.getString("fj_through"));
            Long rhThrough = Long.parseLong(record.getString("rh_through"));
            Long ahThrough = Long.parseLong(record.getString("ah_through"));
            Long creditPut = Long.parseLong(record.getString("credit_put"));
            Long fjPut = Long.parseLong(record.getString("fj_put"));
            Long rhPut = Long.parseLong(record.getString("rh_put"));
            Long ahPut = Long.parseLong(record.getString("ah_put"));
            Integer miniType = Integer.parseInt(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setCreditThrough(creditThrough);
            domain.setOnQueryT0(onQueryT0);
            domain.setOnQueryT1(onQueryT1);
            domain.setResultT0(resultT0);
            domain.setResultT1(resultT1);
            domain.setResultTimeT0(resultTimeT0);
            domain.setResultTimeT1(resultTimeT1);
            domain.setTimeConsume(timeConsume);
            domain.setFjThrough(fjThrough);
            domain.setRhThrough(rhThrough);
            domain.setAhThrough(ahThrough);
            domain.setCreditPut(creditPut);
            domain.setFjPut(fjPut);
            domain.setRhPut(rhPut);
            domain.setAhPut(ahPut);
            domain.setQueryUv(queryUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取征信指标
     */
    private List<WholeLinkCreditDO> getCredit(String ds, Integer hour) {
        String sql = "select count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null)) credit_through," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null)) credit_put," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(cl.createTime) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     on_query_t0," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(cl.createTime) < date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     on_query_t1," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                            date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     result_t0," +
                "   " +
                "          nvl(sum(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                              date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())," +
                "                              datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       'ss')," +
                "                              null)) /" +
                "              count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and" +
                "                                date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null))," +
                "              0)                                                                                                       result_time_t0," +
                "   " +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                            date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                            null))                                                                                     result_t1," +
                "   " +
                "          nvl(sum(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                              date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())," +
                "                              datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                       'ss')," +
                "                              null)) /" +
                "              count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate()) and" +
                "                                date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo, null))," +
                "              0)                                                                                                       result_time_t1," +
                "          nvl((sum(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                " ," +
                "                           datediff(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                    to_date(ca.createtime, 'yyyy-MM-dd HH:mi:ss')," +
                "                                    'ss'), null)) /" +
                "           count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                             null))),0)                                                             " +
                "                      time_consume," +
                "          count(distinct if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel in (3, 30)," +
                "                            ru.idcardNo," +
                "                            null))                                                                                   fj_through  ," +
                "          count(distinct if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel in (3, 30)," +
                "                            ru.idcardNo," +
                "                            null))                                                                                    fj_put ," +
                "          count(distinct" +
                "                if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 0, ru.idcardNo," +
                "                   null))                                                                                            rh_through  ," +
                "          count(distinct" +
                "                if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 0, ru.idcardNo," +
                "                   null))                                                                                             rh_put ," +
                "          count(distinct" +
                "                if(date(to_date(ca.uploadtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 2, ru.idcardNo," +
                "                   null))                                                                                            ah_through  ," +
                "          count(distinct" +
                "                if(date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()) and channel = 2, ru.idcardNo," +
                "                   null))                                                                                             ah_put ," +
                "          count(distinct" +
                "                if(ca.status = 8 and date(to_date(ca.submitTime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate()), ru.idcardNo," +
                "                   null))                                                                                              query_uv" +
                "   from cl_loan cl" +
                "            left join rent_order ro" +
                "                      on cl.loanno = ro.no" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join cd_report_apply ca on ru.idcardNo = ca.idcardNo" +
                "   where cl.ds = " + ds +
                "     and ro.ds =  " + ds +
                "     and ru.ds =  " + ds +
                "     and ca.ds = " + ds + ";";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkCreditDO domain = new WholeLinkCreditDO();

            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long creditThrough = Long.parseLong(record.getString("credit_through"));
            Long onQueryT0 = Long.parseLong(record.getString("on_query_t0"));
            Long onQueryT1 = Long.parseLong(record.getString("on_query_t1"));
            Long queryUv = Long.parseLong(record.getString("query_uv"));
            Long resultT0 = Long.parseLong(record.getString("result_t0"));
            Long resultT1 = Long.parseLong(record.getString("result_t1"));
            Double resultTimeT0 = Double.parseDouble(record.getString("result_time_t0"));
            Double resultTimeT1 = Double.parseDouble(record.getString("result_time_t1"));
            Double timeConsume = Double.parseDouble(record.getString("time_consume"));
            Long fjThrough = Long.parseLong(record.getString("fj_through"));
            Long rhThrough = Long.parseLong(record.getString("rh_through"));
            Long ahThrough = Long.parseLong(record.getString("ah_through"));
            Long creditPut = Long.parseLong(record.getString("credit_put"));
            Long fjPut = Long.parseLong(record.getString("fj_put"));
            Long rhPut = Long.parseLong(record.getString("rh_put"));
            Long ahPut = Long.parseLong(record.getString("ah_put"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCreditThrough(creditThrough);
            domain.setOnQueryT0(onQueryT0);
            domain.setOnQueryT1(onQueryT1);
            domain.setResultT0(resultT0);
            domain.setResultT1(resultT1);
            domain.setResultTimeT0(resultTimeT0);
            domain.setResultTimeT1(resultTimeT1);
            domain.setTimeConsume(timeConsume);
            domain.setFjThrough(fjThrough);
            domain.setRhThrough(rhThrough);
            domain.setAhThrough(ahThrough);
            domain.setCreditPut(creditPut);
            domain.setFjPut(fjPut);
            domain.setRhPut(rhPut);
            domain.setAhPut(ahPut);
            domain.setQueryUv(queryUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);

            return domain;
        }).collect(Collectors.toList());
    }


    private Long stringToLong(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return Long.parseLong(val);
    }
}



