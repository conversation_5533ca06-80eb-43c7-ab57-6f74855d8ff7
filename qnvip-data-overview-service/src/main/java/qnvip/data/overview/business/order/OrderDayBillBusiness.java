package qnvip.data.overview.business.order;

import cn.hutool.core.util.NumberUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OrderDayBillDO;
import qnvip.data.overview.enums.OrderDayBillTypeEnum;
import qnvip.data.overview.service.order.OrderDayBillService;
import qnvip.data.overview.util.*;

import java.io.ByteArrayOutputStream;
import java.io.PrintStream;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/8
 * @since 1.0.0
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class OrderDayBillBusiness {

    private final OdpsUtil odpsUtil;

    private final OrderDayBillService orderDayBillService;

    private final DingDingAlarmUtil dingDingAlarmUtil;

    /**
     * 启动订单日对账
     *
     * @return
     */
    public void runOrderDayBill() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        try {
            orderDayBillService.removeDataByTime(countDay);
            payAmtDeal();
            buyOutAmyAndOthersDeal();
            // abnormalOrders();
        } catch (Exception e) {
            String exceptionInfo = getExceptionInfo(e);
            log.error("error:{}", exceptionInfo);
            dingDingAlarmUtil.dataViewProExceptionAlarm(exceptionInfo);
        }
    }

    /**
     * 对账
     *
     * @return
     */
    public void run() {
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String fromTime = (String) key2value.get("startTime");
        try {
            List<OrderDayBillDO> list = orderDayBillService.getList(fromTime);
            Map<Integer, List<OrderDayBillDO>> map =
                    list.stream().collect(Collectors.groupingBy(OrderDayBillDO::getType));

            BigDecimal offLineFlow = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.OFFLINE_FLOW.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal onLineFlow = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.ONLINE_FLOW.getCode()), OrderDayBillDO::getRealPayAmount);
            BigDecimal actualBuyoutAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.ACT_BUYOUT_AMOUNT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal repaymentAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.REPAYMENT_AMT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal bondAmount = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.BOND_AMOUNT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal screenRisksAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.SCREEN_RISKS_AMT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal partsAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.PARTS_AMT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal overdueFine = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.OVERDUE_FINE.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal discountAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.DISCOUNT_AMT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal planDeductionAmount = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.PLAN_DEDUCTION_AMOUNT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal lawsuitAmt = ObjectUtils.getSum(map.get(OrderDayBillTypeEnum.LAWSUIT_AMT.getCode()),
                    OrderDayBillDO::getRealPayAmount);
            BigDecimal amount1 = CalculateUtil.add(offLineFlow, onLineFlow);
            BigDecimal amount2 = NumberUtil.add(actualBuyoutAmt, repaymentAmt,bondAmount,screenRisksAmt,partsAmt,
                    overdueFine,lawsuitAmt).subtract(discountAmt).subtract(planDeductionAmount);
            BigDecimal subtract = amount1.subtract(amount2);
            orderDayBillService.update(fromTime,subtract);
        } catch (Exception e) {
            String exceptionInfo = getExceptionInfo(e);
            log.error("error:{}", exceptionInfo);
            dingDingAlarmUtil.dataViewProExceptionAlarm(exceptionInfo);

        }
    }

    public static String getExceptionInfo(Exception e) {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        e.printStackTrace(new PrintStream(baos));
        return baos.toString();
    }

    /**
     * 资金流
     *
     * @return
     */
    private void payAmtDeal() {

        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        String sqlStr = "select round(sum(c.originPayAmt), 2) offline_flow," +
                "       round(sum(flow_amt), 2)       flow_amt," +
                "       mini_type" +
                " from (" +
                "         select " +
                "               IF(rof.ext <> '' AND get_json_object(rof.ext, '$.originPayAmt') > 0, " +
                "               get_json_object(rof.ext, '$.originPayAmt') - flow_amt, 0) originPayAmt, " +
                "               IF(rof.ext <> '' AND get_json_object(rof.ext, '$.realPayAmount') > 0, " +
                "               get_json_object(rof.ext, '$.realPayAmount'), flow_amt)    flow_amt, " +
                "               rof.mini_type " +
                "         from rent_order_flow rof" +
                "         where rof.flow_time between ${fromTime} and ${toTime}" +
                "           and flow_type = 1" +
                "           and parent_uid = '' " +
                "           and pay_status = 10 " +
                "           and pay_type <> 4 " +
                "           and merchant_id =100" +
                "           and is_deleted =0" +
                "           and biz_type in(1, 3, 4, 10, 24, 25) " +
                "           and rof.ds = to_char(getdate(), 'yyyymmdd')" +
                "          ) c" +
                " group by c.mini_type";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String sql = SqlUtils.processTemplate(sqlStr, key2value);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OrderDayBillDO> list = Lists.newArrayList();
        OrderDayBillDO domain;
        BigDecimal payAmt;
        BigDecimal offlineFlow;
        String miniType;
        for (Record record : records) {
            domain = new OrderDayBillDO();
            payAmt = new BigDecimal(record.getString("flow_amt"));
            offlineFlow = new BigDecimal(record.getString("offline_flow"));
            miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRealPayAmount(payAmt);
            domain.setCountDay(countDay);
            domain.setType(OrderDayBillTypeEnum.ONLINE_FLOW.getCode());
            list.add(domain);
            list.add(getDo("offline_flow", offlineFlow, domain));
        }
        orderDayBillService.saveBatch(list);
    }

    /**
     * 保证金、买断金、还款金
     *
     * @return
     */
    private void buyOutAmyAndOthersDeal() {

        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        String sqlStr = "select mini_type," +
                "       round(sum(nvl(actual_buyout_amt,0)), 2) actual_buyout_amt," +
                "       round(sum(nvl(repayment_amt,0)), 2) repayment_amt," +
                "       round(sum(nvl(bond_amount,0)), 2) bond_amount," +
                "       round(sum(nvl(screen_risks_amt,0)), 2) screen_risks_amt," +
                "       round(sum(nvl(parts_amt,0)), 2) parts_amt," +
                "       round(sum(nvl(discount_amt,0)), 2) discount_amt," +
                "       round(sum(nvl(deduction_bond_amt,0)), 2) deduction_bond_amt," +
                "       round(sum(nvl(overdue_fine,0)), 2) overdue_fine," +
                "       round(sum(nvl(refunded_amt,0)), 2) refunded_amt," +
                "       round(sum(nvl(discount_return_amt,0)), 2) discount_return_amt," +
                "       round(sum(nvl(buyout_deduction_amount,0)), 2) buyout_deduction_amount," +
                "       round(sum(nvl(buyout_coupon_amount,0)), 2) buyout_coupon_amount," +
                "       round(sum(nvl(buyout_coupon_offline_amount,0)), 2) buyout_coupon_offline_amount," +
                "       round(sum(nvl(platform_discount_amt,0)), 2) platform_discount_amt," +
                "       round(sum(nvl(plan_deduction_amount,0)), 2) plan_deduction_amount," +
                "       round(sum(nvl(plan_coupon_amount,0)), 2) plan_coupon_amount," +
                "       round(sum(nvl(plan_coupon_offline_amount,0)), 2) plan_coupon_offline_amount," +
                "       round(sum(nvl(user_actual_buyout_amt,0)), 2) user_actual_buyout_amt," +
                "       round(sum(nvl(user_act_repayment_amt,0)), 2) user_act_repayment_amt," +
                "       round(sum(nvl(lawsuit_amt,0)), 2) lawsuit_amt" +
                " from rent_order_account_check" +
                " where real_pay_time between ${fromTime} and ${toTime}" +
                "  and ds = to_char(getdate(), 'yyyymmdd')" +
                "  and is_deleted =0" +
                " group by mini_type";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String sql = SqlUtils.processTemplate(sqlStr, key2value);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        LinkedList<OrderDayBillDO> list = Lists.newLinkedList();
        OrderDayBillDO domain;
        BigDecimal actualBuyoutAmt;
        BigDecimal repaymentAmt;
        BigDecimal bondAmount;
        BigDecimal screenRisksAmt;
        BigDecimal partsAmt;
        BigDecimal discountAmt;
        BigDecimal deductionBondAmt;
        BigDecimal overdueFine;
        BigDecimal refundedAmt;
        BigDecimal discountReturnAmt;
        BigDecimal lawsuitAmt;
        BigDecimal buyoutDeductionAmount;
        BigDecimal buyoutCouponAmount;
        BigDecimal buyoutCouponOfflineAmount;
        BigDecimal platformDiscountAmt;
        BigDecimal planDeductionAmount;
        BigDecimal planCouponAmount;
        BigDecimal planCouponOfflineAmount;
        BigDecimal userActualBuyoutAmt;
        BigDecimal userActRepaymentAmt;
        for (Record v : records) {
            domain = new OrderDayBillDO();
            actualBuyoutAmt = new BigDecimal(v.getString("actual_buyout_amt"));
            repaymentAmt = new BigDecimal(v.getString("repayment_amt"));
            bondAmount = new BigDecimal(v.getString("bond_amount"));
            screenRisksAmt = new BigDecimal(v.getString("screen_risks_amt"));
            partsAmt = new BigDecimal(v.getString("parts_amt"));
            discountAmt = new BigDecimal(v.getString("discount_amt"));
            deductionBondAmt = new BigDecimal(v.getString("deduction_bond_amt"));
            overdueFine = new BigDecimal(v.getString("overdue_fine"));
            refundedAmt = new BigDecimal(v.getString("refunded_amt"));
            discountReturnAmt = new BigDecimal(v.getString("discount_return_amt"));
            buyoutDeductionAmount = new BigDecimal(v.getString("buyout_deduction_amount"));
            buyoutCouponAmount = new BigDecimal(v.getString("buyout_coupon_amount"));
            buyoutCouponOfflineAmount = new BigDecimal(v.getString("buyout_coupon_offline_amount"));
            platformDiscountAmt = new BigDecimal(v.getString("platform_discount_amt"));
            planDeductionAmount = new BigDecimal(v.getString("plan_deduction_amount"));
            planCouponAmount = new BigDecimal(v.getString("plan_coupon_amount"));
            planCouponOfflineAmount = new BigDecimal(v.getString("plan_coupon_offline_amount"));
            userActualBuyoutAmt = new BigDecimal(v.getString("user_actual_buyout_amt"));
            userActRepaymentAmt = new BigDecimal(v.getString("user_act_repayment_amt"));
            lawsuitAmt = new BigDecimal(v.getString("lawsuit_amt"));
            String miniType = v.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRealPayAmount(actualBuyoutAmt);
            domain.setCountDay(countDay);
            domain.setType(OrderDayBillTypeEnum.ACT_BUYOUT_AMOUNT.getCode());

            list.add(domain);
            list.add(getDo("repayment_amt", repaymentAmt, domain));
            list.add(getDo("bond_amount", bondAmount, domain));
            list.add(getDo("screen_risks_amt", screenRisksAmt, domain));
            list.add(getDo("parts_amt", partsAmt, domain));
            list.add(getDo("discount_amt", discountAmt, domain));
            list.add(getDo("deduction_bond_amt", deductionBondAmt, domain));
            list.add(getDo("overdue_fine", overdueFine, domain));
            list.add(getDo("refunded_amt", refundedAmt, domain));
            list.add(getDo("discount_return_amt", discountReturnAmt, domain));
            list.add(getDo("lawsuit_amt", lawsuitAmt, domain));
            list.add(getDo("buyout_deduction_amount", buyoutDeductionAmount, domain));
            list.add(getDo("buyout_coupon_amount", buyoutCouponAmount, domain));
            list.add(getDo("buyout_coupon_offline_amount", buyoutCouponOfflineAmount, domain));
            list.add(getDo("platform_discount_amt", platformDiscountAmt, domain));
            list.add(getDo("plan_deduction_amount", planDeductionAmount, domain));
            list.add(getDo("plan_coupon_amount", planCouponAmount, domain));
            list.add(getDo("plan_coupon_offline_amount", planCouponOfflineAmount, domain));
            list.add(getDo("user_actual_buyout_amt", userActualBuyoutAmt, domain));
            list.add(getDo("user_act_repayment_amt", userActRepaymentAmt, domain));
        }
        orderDayBillService.saveBatch(list);
    }

    /**
     * 异常订单
     *
     * @return
     */
    private void abnormalOrders() {

        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        String sqlStr = "select mini_type, flow_uid" +
                " from rent_order_account_check" +
                " where ext <> '{}'" +
                "  and ds = to_char(getdate(), 'yyyymmdd')" +
                "  and real_pay_time between ${fromTime} and ${toTime}" +
                "  and is_deleted =0" +
                " group by mini_type, ext, flow_uid, order_id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String sql = SqlUtils.processTemplate(sqlStr, key2value);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OrderDayBillDO> list = Lists.newArrayList();
        OrderDayBillDO domain;
        String flowUid;
        String miniType;
        for (Record record : records) {
            domain = new OrderDayBillDO();
            flowUid = record.getString("flow_uid");
            miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setCountDay(countDay);
            domain.setExt(flowUid);
            domain.setType(OrderDayBillTypeEnum.ABNORMAL_ORDERS.getCode());
            list.add(domain);
        }
        orderDayBillService.saveBatch(list);
    }


    private OrderDayBillDO getDo(String name,
                                 BigDecimal decimal,
                                 OrderDayBillDO domain) {
        OrderDayBillDO clone = null;
        try {
            clone = domain.clone();
            clone.setType(OrderDayBillTypeEnum.getCodeByName(name));
            clone.setRealPayAmount(decimal);
        } catch (CloneNotSupportedException e) {
            log.error("OrderDayBillBusiness.getDo error:{}", e.getMessage());
            String exceptionInfo = getExceptionInfo(e);
            dingDingAlarmUtil.dataViewProExceptionAlarm("OrderDayBillBusiness.getDo:  \n"+exceptionInfo);
        }

        return clone;
    }

}
