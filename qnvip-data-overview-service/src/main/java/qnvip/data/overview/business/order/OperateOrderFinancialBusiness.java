package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateOrderFinancialDO;
import qnvip.data.overview.service.order.OperateOrderFinancialService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/16
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateOrderFinancialBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateOrderFinancialService orderFinancialService;

    void initMap(Map<Integer, Map<String, OperateOrderFinancialDO>> miniType2Map, OperateOrderFinancialDO orderFinancialDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, OperateOrderFinancialDO> rentType2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(orderFinancialDO.getMiniType())) {
            OperateOrderFinancialDO domain = new OperateOrderFinancialDO();
            domain.setCountDay(countDay);
            domain.setMiniType(orderFinancialDO.getMiniType());
            domain.setRentType(orderFinancialDO.getRentType());
            rentType2Do.put(orderFinancialDO.getRentType(), domain);
        } else {
            rentType2Do = miniType2Map.get(orderFinancialDO.getMiniType());
            if (!rentType2Do.containsKey(orderFinancialDO.getRentType())) {
                OperateOrderFinancialDO domain = new OperateOrderFinancialDO();
                domain.setCountDay(countDay);
                domain.setMiniType(orderFinancialDO.getMiniType());
                domain.setRentType(orderFinancialDO.getRentType());
                rentType2Do.put(orderFinancialDO.getRentType(), domain);
            }
        }
        miniType2Map.put(orderFinancialDO.getMiniType(), rentType2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, OperateOrderFinancialDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateOrderFinancialDO>> f1 = CompletableFuture.supplyAsync(()->getOrderCount(ds));
        CompletableFuture<List<OperateOrderFinancialDO>> f2 = CompletableFuture.supplyAsync(()->getPayOrder(ds));
        CompletableFuture<List<OperateOrderFinancialDO>> f3 = CompletableFuture.supplyAsync(()->getSignCount(ds));
        CompletableFuture.allOf(f1, f2, f3).join();
        try {
            for (OperateOrderFinancialDO coreDO : f1.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderFinancialDO> rentType2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderFinancialDO financialDO = rentType2Do.get(coreDO.getRentType());
                financialDO.setOrderCount(coreDO.getOrderCount());

            }
            for (OperateOrderFinancialDO coreDO : f2.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderFinancialDO> rentType2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderFinancialDO financialDO = rentType2Do.get(coreDO.getRentType());
                financialDO.setPayCount(coreDO.getPayCount());
            }
            for (OperateOrderFinancialDO coreDO : f3.get()) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateOrderFinancialDO> rentType2Do = miniType2Map.get(coreDO.getMiniType());
                OperateOrderFinancialDO financialDO = rentType2Do.get(coreDO.getRentType());
                financialDO.setSignCount(coreDO.getSignCount());
            }

            // 写入mysql
            LinkedList<OperateOrderFinancialDO> list = Lists.newLinkedList();

            for (Map.Entry<Integer, Map<String, OperateOrderFinancialDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, OperateOrderFinancialDO> rentType2do : entry.getValue().entrySet()) {
                    OperateOrderFinancialDO value = rentType2do.getValue();
                    list.add(value);
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            orderFinancialService.removeDataByTime(countDay);
            orderFinancialService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateOrderFinancialBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 获取下单量
     */
    private List<OperateOrderFinancialDO> getOrderCount(String ds) {
        String sql = "select d.repayment_term as rent_type," +
                "       a.mini_type," +
                "       count(a.id)         num" +
                " from rent_order a" +
                "         inner join rent_order_finance_detail d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type =1" +
                "  and a.parent_id =0" +
                "  and a.ds = "+ds +
                "  and d.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, d.repayment_term";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderFinancialDO domain = new OperateOrderFinancialDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String rentType = record.getString("rent_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRentType(rentType);
            domain.setOrderCount(Long.valueOf(num));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 获取加购订单支付
     */
    private List<OperateOrderFinancialDO> getPayOrder(String ds) {
        String sql = "select d.repayment_term as rent_type," +
                "       a.mini_type," +
                "       count(a.id)         num" +
                " from rent_order a" +
                "         inner join rent_order_finance_detail d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type =1" +
                "  and a.parent_id =0" +
                "  and a.ds = "+ds +
                "  and d.ds = "+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, d.repayment_term;";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderFinancialDO domain = new OperateOrderFinancialDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String rentType = record.getString("rent_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRentType(rentType);
            domain.setPayCount(Long.valueOf(num));
            return domain;
        }).collect(Collectors.toList());


    }


    /**
     * 签收
     */
    private List<OperateOrderFinancialDO> getSignCount(String ds) {
        String sql = "select d.repayment_term as rent_type," +
                "       a.mini_type," +
                "       count(a.id)         num" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id" +
                "         inner join rent_item c on b.item_id = c.id" +
                "         inner join rent_order_finance_detail d on a.id = d.order_id" +
                "         inner join rent_order_logistics e on b.logistics_id = e.id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type =1" +
                "  and a.parent_id =0" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and e.ds = "+ds +
                "  and e.sign_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, d.repayment_term";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderFinancialDO domain = new OperateOrderFinancialDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String rentType = record.getString("rent_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setRentType(rentType);
            domain.setSignCount(Long.valueOf(num));
            return domain;
        }).collect(Collectors.toList());
    }


}
