package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPayQualityDO;
import qnvip.data.overview.service.dataindicators.IndicatorPayQualityService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorPayQualityBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorPayQualityService indicatorPayQualityService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorPayQualityDO> collectMap = new HashMap<>();
        countPayLevelCount(ds, sTime, eTime, collectMap);
        ArrayList<IndicatorPayQualityDO> list = new ArrayList<>(collectMap.values());
        if (CollUtil.isNotEmpty(list)) {
            indicatorPayQualityService.saveOrUpdateBatch(list, 2000);
        }
        // for (IndicatorPayQualityDO value : collectMap.values()) {
        //     indicatorPayQualityService.saveOrUpdate(value);
        // }
    }


    public void countPayLevelCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorPayQualityDO> collectMap) {
        String sql = " select a.mini_type,c.rate_config_type, " +
                "    to_char(a.create_time,'yyyy-mm-dd') day, " +
                "    get_json_object(d.operate_note,'$.riskLevel') risk_level, " +
                "    count(1) count " +
                "from rent_order a " +
                "     inner join rent_order_finance_detail c on a.id=c.order_id " +
                "     inner join rent_order_audit d on a.id=d.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '"+sTime+"' and '"+eTime+"' " +
                "  and a.payment_time is not null " +
                "  and d.type = 2  and ISNOTNULL(get_json_object(d.operate_note, '$.riskLevel')) " +
                "  and a.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "  get_json_object(d.operate_note,'$.riskLevel'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer riskLevel = Integer.valueOf(record.getString("risk_level"));
            Integer count = Integer.valueOf(record.getString("count"));
            IndicatorPayQualityDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                switch (riskLevel) {
                    case 1:
                    case 2:
                        updateDO.setPlanOneLevelOne(Optional.ofNullable(updateDO.getPlanOneLevelOne()).orElse(0) + count);
                        break;
                    case 3:
                    case 4:
                        updateDO.setPlanOneLevelTwo(Optional.ofNullable(updateDO.getPlanOneLevelTwo()).orElse(0) + count);
                        break;
                    case 5:
                    case 6:
                        updateDO.setPlanOneLevelThree(Optional.ofNullable(updateDO.getPlanOneLevelThree()).orElse(0) + count);
                        break;
                    case 7:
                    case 8:
                        updateDO.setPlanOneLevelFour(Optional.ofNullable(updateDO.getPlanOneLevelFour()).orElse(0) + count);
                        break;
                    default:
                        updateDO.setPlanOneLevelFive(Optional.ofNullable(updateDO.getPlanOneLevelFive()).orElse(0) + count);
                        break;
                }
            } else {
                switch (riskLevel) {
                    case 1:
                    case 2:
                        updateDO.setPlanTwoLevelOne(Optional.ofNullable(updateDO.getPlanTwoLevelOne()).orElse(0) + count);
                        break;
                    case 3:
                    case 4:
                        updateDO.setPlanTwoLevelTwo(Optional.ofNullable(updateDO.getPlanTwoLevelTwo()).orElse(0) + count);
                        break;
                    case 5:
                    case 6:
                        updateDO.setPlanTwoLevelThree(Optional.ofNullable(updateDO.getPlanTwoLevelThree()).orElse(0) + count);
                        break;
                    case 7:
                    case 8:
                        updateDO.setPlanTwoLevelFour(Optional.ofNullable(updateDO.getPlanTwoLevelFour()).orElse(0) + count);
                        break;
                    default:
                        updateDO.setPlanTwoLevelFive(Optional.ofNullable(updateDO.getPlanTwoLevelFive()).orElse(0) + count);
                        break;
                }
            }
        }
    }



    private void initMap(Map<String, IndicatorPayQualityDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorPayQualityDO ivd = new IndicatorPayQualityDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setPlanOneLevelOne(0);
            ivd.setPlanOneLevelTwo(0);
            ivd.setPlanOneLevelThree(0);
            ivd.setPlanOneLevelFour(0);
            ivd.setPlanOneLevelFive(0);
            ivd.setPlanTwoLevelOne(0);
            ivd.setPlanTwoLevelTwo(0);
            ivd.setPlanTwoLevelThree(0);
            ivd.setPlanTwoLevelFour(0);
            ivd.setPlanTwoLevelFive(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}