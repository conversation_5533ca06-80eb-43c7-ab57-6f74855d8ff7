package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.TotalOrderDO;
import qnvip.data.overview.service.order.TotalOrderService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @Date 2021/10/10 5:37 下午
 */
@Component
@RequiredArgsConstructor
public class TotalOrderBusiness {

    private final OdpsUtil odpsUtil;
    private final TotalOrderService totalOrderService;

    /**
     * 启动订单数据计算
     *
     * @param countDay
     * @param eTime
     * @return
     */
    public void runTotalOrderCount(String dsStr, LocalDateTime countDay, String eTime) {
        TotalOrderDO totalOrderDO = new TotalOrderDO();
        totalOrderDO.setCountDay(countDay);
        rentGMVAndOrderCount(dsStr, totalOrderDO, eTime);
        rentPayCount(dsStr, totalOrderDO, eTime);
        orderMarginTotal(dsStr, totalOrderDO, eTime);
        orderInsuranceAmt(dsStr, totalOrderDO, eTime);
        orderComponentPrice(dsStr, totalOrderDO, eTime);
        deliveryCount(dsStr, totalOrderDO, eTime);
        auditApprovalCount(dsStr, totalOrderDO, eTime);

        // 写入mysql
        totalOrderService.saveOrUpdate(totalOrderDO);
    }


    /**
     * 计算订单总租金GMV、订单量
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO rentGMVAndOrderCount(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(b.rent_total),0) as renttotal,count(1) as num from " +
                " rent_order a inner join rent_order_finance_detail b on a.id = b.order_id where a.biz_type = 2 " +
                " and a.type = 1 and a.merchant_id = 100 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setOrderCount(Long.parseLong(recordList.get(0).getString("num")));
        return totalOrderDO;
    }


    /**
     * 支付订单量
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO rentPayCount(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from rent_order a where a.biz_type = 2 and " +
                " a.merchant_id = 100 and a.type = 1 and a.payment_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setPayCount(Long.parseLong(recordList.get(0).getString("num")));
        return totalOrderDO;
    }

    /**
     * 保证金支付金额
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO orderMarginTotal(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(b.bond_amt),0) as bondamt from rent_order a inner" +
                " join rent_order_finance_detail b on a.id = b.order_id inner join rent_order_item c on " +
                " b.order_id = c.order_id where a.biz_type = 2  and a.type = 1 and a.merchant_id = 100 " +
                " and a.payment_time is not null and c.item_type = 1 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setMarginTotal(new BigDecimal(recordList.get(0).getString("bondamt")));

        return totalOrderDO;
    }

    /**
     * 碎屏险支付金额
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO orderInsuranceAmt(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(b.insurance_amt),0) as insuranceamt from rent_order a " +
                " inner join rent_order_insurance b on a.id = b.order_id where a.biz_type = 2  and a.type = 1 " +
                " and a.merchant_id = 100 and a.payment_time is not null and a.is_screen_risk_payed = 1 " +
                " and b.giving_flag = 0 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setInsuranceAmt(new BigDecimal(recordList.get(0).getString("insuranceamt")));
        return totalOrderDO;
    }

    /**
     * 配件支付金额
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO orderComponentPrice(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(d.operating_purchase_price),0) as componentprice," +
                " count(1) as num from rent_order a inner join rent_order_item d on a.id = d.order_id  where " +
                " a.biz_type = 2 and a.merchant_id = 100 and a.type = 1 and a.payment_time is not null and " +
                " d.item_type=10 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and d.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setComponentPrice(new BigDecimal(recordList.get(0).getString("componentprice")));
        return totalOrderDO;
    }


    /**
     * 发货数量
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO deliveryCount(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from  rent_order a inner join rent_order_item" +
                " b on a.id =b.order_id inner join rent_order_logistics c on b.logistics_id = c.id where a.biz_type =" +
                " 2 and a.merchant_id = 100 and a.type = 1 and a.payment_time is not null and c.send_time is not null" +
                " and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and c.send_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setDeliveryCount(Long.parseLong(recordList.get(0).getString("num")));

        return totalOrderDO;
    }


    /**
     * 风控通过人数
     *
     * @param totalOrderDO
     * @param eTime
     * @return
     */
    public TotalOrderDO auditApprovalCount(String dsStr, TotalOrderDO totalOrderDO, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(distinct(a.customer_id)) as num from rent_order a left " +
                "join rent_order_audit b on a.id=b.order_id " +
                "where biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.parent_id = 0 and b.type = 2 and " +
                "b.audit_status = 1 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (eTime != null) {
            sb.append(" and b.update_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(" ;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        totalOrderDO.setRiskPassCount(Long.parseLong(recordList.get(0).getString("num")));
        return totalOrderDO;
    }

}