package qnvip.data.overview.business.overdue;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.business.whole.report.WholeLawReportBusiness;
import qnvip.data.overview.domain.overdue.RepayPerformanceOntimeRepayDO;
import qnvip.data.overview.service.overdue.RepayPerformanceOntimeRepayService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RepayPerformanceOnTimeBusiness {

    private final OdpsUtil odpsUtil;

    private final RepayPerformanceOntimeRepayService service;

    public void excute(String ds) {
        String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);
        String sql = "with dt as (select " + ds + " as ds) ," +
                "   calc_repay_info AS (SELECT capital\n" +
                "                               ,order_id\n" +
                "                               ,overdue_fine\n" +
                "                               ,real_repay_capital\n" +
                "                               ,real_repay_time\n" +
                "                               ,SUBSTRING(real_repay_time,1,10) AS real_repay_date\n" +
                "                               ,SUBSTRING(repay_date,1,10) AS repay_date\n" +
                "                               ,real_overdue_fine\n" +
                "                               ,reduce_amt\n" +
                "                               ,withhold_type\n" +
                "                               ,repay_status\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "',IF(repay_status=10,real_repay_capital+report_reduce_amt,capital),0) AS collect_amt\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND real_repay_time<repay_date,real_repay_capital+report_reduce_amt,0) AS collect_ahead_repaied_amt\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND SUBSTRING(real_repay_time,1,10)=SUBSTRING(repay_date,1,10),real_repay_capital+report_reduce_amt,0) AS collect_ontime_repaied_amt\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "', report_reduce_amt,0) AS collect_reduce_amt\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND real_repay_time<repay_date AND withhold_type IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_ahead_active_capital\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND SUBSTRING(real_repay_time,1,10)=SUBSTRING(repay_date,1,10) AND withhold_type IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_ontime_active_capital\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND real_repay_time<repay_date AND withhold_type NOT IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_ahead_withhold_capital\n" +
                "                               ,IF(SUBSTRING(repay_date,1,10)='" + time + "' AND SUBSTRING(real_repay_time,1,10)=SUBSTRING(repay_date,1,10) AND withhold_type NOT IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_ontime_withhold_capital\n" +
                "                               ,term\n" +
                "                               , ds\n" +
                "                         FROM rent_order_repayment_plan\n" +
                "                         WHERE ds=(select ds from dt)\n" +
                "                              AND is_deleted=0)\n" +
                "    ,calc_ontime_repay_detail AS (SELECT send.*\n" +
                "                 -- 6+6买断到期后会计算买断金,买断金按照实还期数计算剩余价值,不在统计账单应还\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_amt) AS collect_amt\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ahead_repaied_amt) AS collect_ahead_repaied_amt\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ontime_repaied_amt) AS collect_ontime_repaied_amt\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_reduce_amt) AS collect_reduce_amt\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ahead_active_capital) AS collect_ahead_active_capital\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ontime_active_capital) AS collect_ontime_active_capital\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ahead_withhold_capital) AS collect_ahead_withhold_capital\n" +
                "                ,IF(send.finance_title LIKE '%6+6%' AND send.rent_end_date2<='" + time + "',0,repay.collect_ontime_withhold_capital) AS collect_ontime_withhold_capital\n" +
                "                ,IF(repay.repay_date='" + time + "',send.parent_order_no,NULL) AS collect_orderno\n" +
                "                 -- 待确认是不是全还完，才统计已还订单号\n" +
                "                 -- 待确认部分还的不太好划分到提前还的行列，因为系统会刷新最后一次还的real_repay_time\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date<repay.repay_date,send.parent_order_no,NULL) AS collect_ahead_repaied_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date=repay.repay_date,send.parent_order_no,NULL) AS collect_ontime_repaied_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.collect_reduce_amt>0,send.parent_order_no,NULL) AS collect_reduce_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date<repay.repay_date AND repay.withhold_type IN (0,1,3,6,7,9),send.parent_order_no,NULL) AS collect_ahead_active_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date=repay.repay_date AND repay.withhold_type IN (0,1,3,6,7,9),send.parent_order_no,NULL) AS collect_ontime_active_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date<repay.repay_date AND repay.withhold_type NOT IN (0,1,3,6,7,9),send.parent_order_no,NULL) AS collect_ahead_withhold_orderno\n" +
                "                ,IF(repay.repay_date='" + time + "' AND repay.real_repay_date=repay.repay_date AND repay.withhold_type NOT IN (0,1,3,6,7,9),send.parent_order_no,NULL) AS collect_ontime_withhold_orderno\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        -- 针对账单关联,关联到认定买断的主订单最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND repay.term=send.total_term)\n" +
                "                                 THEN send.parent_order_no\n" +
                "                                 ELSE NULL END)\n" +
                "                        ELSE NULL END AS collect_buyout_orderno\n" +
                "\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        -- 针对账单关联,关联到认定买断的主订单最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND send.total_term=repay.term)\n" +
                "                                 THEN send.buyout_amt\n" +
                "                                 ELSE 0 END)\n" +
                "                        ELSE 0 END AS collect_buyout_amt\n" +
                "\n" +
                "\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        AND SUBSTRING(infomore.buyout_time,1,10)<'" + time + "' -- 当日之前买断\n" +
                "                        -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND send.total_term=repay.term)\n" +
                "                                THEN send.parent_order_no\n" +
                "                                ELSE NULL END)\n" +
                "                        ELSE NULL END AS collect_ahead_buyout_orderno\n" +
                "\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        AND SUBSTRING(infomore.buyout_time,1,10)='" + time + "' -- 当日买断\n" +
                "                        -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND send.total_term=repay.term)\n" +
                "                                THEN send.parent_order_no\n" +
                "                                ELSE NULL END)\n" +
                "                        ELSE NULL END AS collect_ontime_buyout_orderno\n" +
                "\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        AND SUBSTRING(infomore.buyout_time,1,10)<'" + time + "' -- 当日之前买断\n" +
                "                        -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND send.total_term=repay.term)\n" +
                "                                THEN send.buyout_amt\n" +
                "                                ELSE 0 END)\n" +
                "                        ELSE 0 END AS collect_ahead_buyout_amt\n" +
                "\n" +
                "                ,CASE WHEN SUBSTRING(send.rent_end_date2,1,10)='" + time + "'  -- 当日达到买断金到期时间\n" +
                "                        AND SUBSTRING(infomore.buyout_time,1,10)='" + time + "' -- 当日买断\n" +
                "                        -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                        THEN (CASE WHEN ((send.finance_title='3+X' OR send.finance_title LIKE '%6+6%')\n" +
                "                                          AND send.last_term=repay.term)\n" +
                "                                          OR\n" +
                "                                          (send.finance_title!='3+X' AND send.finance_title NOT LIKE '%6+6%'\n" +
                "                                          AND extend.extend_no IS NULL AND send.total_term=repay.term)\n" +
                "                                THEN send.buyout_amt\n" +
                "                                ELSE 0 END)\n" +
                "                        ELSE 0 END AS collect_ontime_buyout_amt\n" +
                "\n" +
                "                , CASE\n" +
                "               WHEN  rent_order.parent_id = 0 THEN repay.term +10\n" +
                "               WHEN  rent_order.parent_id > 0 THEN repay.term +22\n" +
                "        END                             AS stat_time_zone\n" +
                "        FROM `calc_repay_info` repay\n" +
                "        JOIN (SELECT id,`no` as order_no,parent_id FROM rent_order WHERE ds=(select ds from dt) AND is_deleted=0) rent_order\n" +
                "        ON repay.order_id=rent_order.id\n" +
                "        JOIN dataview_repay_ontime_order_detail send\n" +
                "        ON repay.ds=send.ds AND rent_order.order_no=send.order_no\n" +
                "        LEFT JOIN (SELECT id,`no` AS extend_no,parent_id FROM rent_order WHERE ds=(select ds from dt) AND parent_id<>0) extend\n" +
                "        ON repay.order_id=extend.parent_id\n" +
                "        LEFT JOIN (SELECT order_id\n" +
                "                         ,NULLIF(TRIM(buyout_time), '')      AS buyout_time\n" +
                "                         ,NULLIF(TRIM(return_succ_time), '') AS return_time\n" +
                "                   FROM rent_order_infomore\n" +
                "                   WHERE ds=(select ds from dt)) infomore\n" +
                "                   ON repay.order_id = infomore.order_id AND rent_order.parent_id = 0)\n" +
                "\n" +
                "\n" +
                "SELECT stat_time_zone\n" +
                "      ,stat_type\n" +
                "      ,collection_item\n" +
                "      ,ahead_repaied_item\n" +
                "      ,if(collection_item = 0, 0, ahead_repaied_item / collection_item)  AS ahead_repaied_ratio\n" +
                "      ,ahead_active_capital_item\n" +
                "      ,if(collection_item = 0, 0, ahead_active_capital_item / collection_item)  AS ahead_active_capital_ratio\n" +
                "      ,ahead_withhold_capital_item\n" +
                "      ,if(collection_item = 0, 0, ahead_withhold_capital_item / collection_item)  AS ahead_withhold_capital_ratio\n" +
                "      ,ahead_active_buyout_item\n" +
                "      ,if(collection_item = 0, 0, ahead_active_buyout_item / collection_item)  AS ahead_active_buyout_ratio\n" +
                "      ,ontime_repaied_item\n" +
                "      ,if(collection_item = 0, 0, ontime_repaied_item / collection_item)  AS ontime_repaied_ratio\n" +
                "      ,reduce_item\n" +
                "      ,if(collection_item = 0, 0, reduce_item / collection_item)  AS reduce_ratio\n" +
                "      ,collection_item - ahead_repaied_item - ontime_repaied_item  AS unpay_item\n" +
                "      ,if(collection_item = 0, 0, (collection_item - ahead_repaied_item - ontime_repaied_item) / collection_item)  AS unpay_ratio\n" +
                "      ,ontime_active_capital_item\n" +
                "      ,if(collection_item = 0, 0, ontime_active_capital_item / collection_item)  AS ontime_active_capital_ratio\n" +
                "      ,ontime_withhold_capital_item\n" +
                "      ,if(collection_item = 0, 0, ontime_withhold_capital_item / collection_item)  AS ontime_withhold_capital_ratio\n" +
                "      ,ontime_active_buyout_item\n" +
                "      ,if(collection_item = 0, 0, ontime_active_buyout_item / collection_item)  AS ontime_active_buyout_ratio\n" +
                "FROM\n" +
                "(SELECT COALESCE(stat_time_zone, 0)                                     AS stat_time_zone\n" +
                "      ,1                                                                AS stat_type     -- 数量\n" +
                "      ,COUNT(DISTINCT collect_orderno) AS collection_item\n" +
                "      ,COUNT(DISTINCT collect_ahead_repaied_orderno) AS ahead_repaied_item\n" +
                "      ,COUNT(DISTINCT collect_ahead_active_orderno) AS ahead_active_capital_item\n" +
                "      ,COUNT(DISTINCT collect_ahead_withhold_orderno) AS ahead_withhold_capital_item\n" +
                "      ,COUNT(DISTINCT collect_ahead_buyout_orderno) AS ahead_active_buyout_item\n" +
                "      ,COUNT(DISTINCT collect_ontime_repaied_orderno) AS ontime_repaied_item\n" +
                "      ,COUNT(DISTINCT collect_ontime_active_orderno) AS ontime_active_capital_item\n" +
                "      ,COUNT(DISTINCT collect_ontime_withhold_orderno) AS ontime_withhold_capital_item\n" +
                "      ,COUNT(DISTINCT collect_reduce_orderno) AS reduce_item -- 减免金额\n" +
                "      ,COUNT(DISTINCT collect_ontime_buyout_orderno) AS ontime_active_buyout_item\n" +
                "FROM (SELECT collect_orderno\n" +
                "            ,collect_ahead_repaied_orderno\n" +
                "            ,collect_ahead_active_orderno\n" +
                "            ,collect_ahead_withhold_orderno\n" +
                "            ,collect_ahead_buyout_orderno\n" +
                "            ,collect_ontime_repaied_orderno\n" +
                "            ,collect_ontime_active_orderno\n" +
                "            ,collect_ontime_withhold_orderno\n" +
                "            ,collect_reduce_orderno\n" +
                "            ,collect_ontime_buyout_orderno\n" +
                "            ,stat_time_zone\n" +
                "      FROM calc_ontime_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4\n" +
                "      UNION ALL\n" +
                "      SELECT collect_buyout_orderno AS collect_orderno\n" +
                "            ,collect_ahead_buyout_orderno AS collect_ahead_repaied_orderno\n" +
                "            ,NULL AS collect_ahead_active_orderno\n" +
                "            ,NULL AS collect_ahead_withhold_orderno\n" +
                "            ,collect_ahead_buyout_orderno\n" +
                "            ,collect_ontime_buyout_orderno AS collect_ontime_repaied_orderno\n" +
                "            ,NULL AS collect_ontime_active_orderno\n" +
                "            ,NULL AS collect_ontime_withhold_orderno\n" +
                "            ,NULL AS collect_reduce_orderno\n" +
                "            ,collect_ontime_buyout_orderno\n" +
                "            ,100 AS stat_time_zone\n" +
                "      FROM calc_ontime_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4) tmp\n" +
                "group by stat_time_zone\n" +
                "    GROUPING SETS ((stat_time_zone), ())\n" +
                "UNION ALL\n" +
                "SELECT COALESCE(stat_time_zone,0)                                        AS stat_time_zone\n" +
                "      ,0                                                                 AS stat_type\n" +
                "      ,SUM(collect_amt) AS collection_item   -- 应收金额(已还+未还)\n" +
                "      ,SUM(collect_ahead_repaied_amt)   AS ahead_repaied_amt -- 已还金额(包含减免金额)\n" +
                "      ,SUM(collect_ahead_active_capital)   AS ahead_active_capital_item -- 提前-主动租金(包含保证金减免等金额)\n" +
                "      ,SUM(collect_ahead_withhold_capital) AS ahead_withhold_capital_item -- 提前-代扣租金(包含保证金减免等金额)\n" +
                "      ,SUM(collect_ahead_buyout_amt) AS ahead_active_buyout_item   -- 提前-主动还买断金\n" +
                "      ,SUM(collect_ontime_repaied_amt) AS ontime_repaied_item   -- 正常已还额\n" +
                "      ,SUM(collect_ontime_active_capital) AS ontime_active_capital_item   -- 按时-主动还租金\n" +
                "      ,SUM(collect_ontime_withhold_capital) AS ontime_withhold_capital_item   -- 按时-代扣还租金\n" +
                "      ,SUM(collect_reduce_amt) AS reduce_item   -- 减免额\n" +
                "      ,SUM(collect_ontime_buyout_amt) AS ontime_active_buyout_item -- 按时-主动还买断金\n" +
                "FROM (SELECT collect_amt\n" +
                "            ,collect_ahead_repaied_amt\n" +
                "            ,collect_ahead_active_capital\n" +
                "            ,collect_ahead_withhold_capital\n" +
                "            ,0 AS collect_ahead_buyout_amt\n" +
                "            ,collect_ontime_repaied_amt\n" +
                "            ,collect_ontime_active_capital\n" +
                "            ,collect_ontime_withhold_capital\n" +
                "            ,collect_reduce_amt\n" +
                "            ,0 as collect_ontime_buyout_amt\n" +
                "            ,stat_time_zone\n" +
                "      FROM calc_ontime_repay_detail\n" +
                "      UNION ALL\n" +
                "      SELECT collect_buyout_amt\n" +
                "            ,collect_ahead_buyout_amt AS collect_ahead_repaied_amt\n" +
                "            ,0 AS collect_ahead_active_capital\n" +
                "            ,0 AS collect_ahead_withhold_capital\n" +
                "            ,collect_ahead_buyout_amt\n" +
                "            ,collect_ontime_buyout_amt AS collect_ontime_repaied_amt\n" +
                "            ,0 AS collect_ontime_active_capital\n" +
                "            ,0 AS collect_ontime_withhold_capital\n" +
                "            ,0 AS collect_reduce_amt\n" +
                "            ,collect_ontime_buyout_amt\n" +
                "            ,100 AS stat_time_zone\n" +
                "      FROM calc_ontime_repay_detail ) tmp2\n" +
                "group by stat_time_zone\n" +
                "    GROUPING SETS ((stat_time_zone), ())) tmp   ";

        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        LocalDate localDate = DateUtils.stringToLocateDate(time);
        List<RepayPerformanceOntimeRepayDO> collect = records.stream().map(o -> {
            RepayPerformanceOntimeRepayDO domain = new RepayPerformanceOntimeRepayDO();
            domain.setStatTimeZone(Long.valueOf(o.getString("stat_time_zone")));
            domain.setStatType(Long.valueOf(o.getString("stat_type")));
            domain.setCollectionItem(new BigDecimal(o.getString("collection_item")));
            domain.setAheadRepaiedItem(new BigDecimal(o.getString("ahead_repaied_item")));
            domain.setAheadRepaiedRatio(new BigDecimal(o.getString("ahead_repaied_ratio")));
            domain.setAheadActiveCapitalItem(new BigDecimal(o.getString("ahead_active_capital_item")));
            domain.setAheadActiveCapitalRatio(new BigDecimal(o.getString("ahead_active_capital_ratio")));
            domain.setAheadWithholdCapitalItem(new BigDecimal(o.getString("ahead_withhold_capital_item")));
            domain.setAheadWithholdCapitalRatio(new BigDecimal(o.getString("ahead_withhold_capital_ratio")));
            domain.setAheadActiveBuyoutItem(new BigDecimal(o.getString("ahead_active_buyout_item")));
            domain.setAheadActiveBuyoutRatio(new BigDecimal(o.getString("ahead_active_buyout_ratio")));
            domain.setOntimeRepaiedItem(new BigDecimal(o.getString("ontime_repaied_item")));
            domain.setOntimeRepaiedRatio(new BigDecimal(o.getString("ontime_repaied_ratio")));
            domain.setReduceItem(new BigDecimal(o.getString("reduce_item")));
            domain.setReduceRatio(new BigDecimal(o.getString("reduce_ratio")));
            domain.setUnpayItem(new BigDecimal(o.getString("unpay_item")));
            domain.setUnpayRatio(new BigDecimal(o.getString("unpay_ratio")));
            domain.setOntimeActiveCapitalItem(new BigDecimal(o.getString("ontime_active_capital_item")));
            domain.setOntimeActiveCapitalRatio(new BigDecimal(o.getString("ontime_active_capital_ratio")));
            domain.setOntimeWithholdCapitalItem(new BigDecimal(o.getString("ontime_withhold_capital_item")));
            domain.setOntimeWithholdCapitalRatio(new BigDecimal(o.getString("ontime_withhold_capital_ratio")));
            domain.setOntimeActiveBuyoutItem(new BigDecimal(o.getString("ontime_active_buyout_item")));
            domain.setOntimeActiveBuyoutRatio(new BigDecimal(o.getString("ontime_active_buyout_ratio")));
            domain.setCountDay(time);
            return domain;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            service.removeDataByTime(localDate);
            service.saveBatch(collect);
        }
    }

}
