package qnvip.data.overview.business.risk;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.datacheck.DataCheckAfterRentOverviewDO;
import qnvip.data.overview.domain.risk.*;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.datacheck.DataCheckAfterRentOverviewService;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskDistributionOrderOverdueService;
import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
import qnvip.data.overview.service.risk.RiskRenewalOrderOverdueService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskRenewalOverdueBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private static final Integer PAGE_SIZE = 10000;
    private static final Integer PAGE_SIZE_2000 = 2000;
    private static final Integer PAGE_SIZE_5000 = 5000;

    private final OdpsUtil odpsUtil;
    private final RiskRenewalOrderOverdueService riskRenewalOrderOverdueService;
    private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
    private final DataCheckAfterRentOverviewService dataCheckAfterRentOverviewService;
    private final RiskDistributionOrderOverdueService riskDistributionOrderOverdueService;

    private final RentOrderService rentOrderService;


    private static AtomicInteger atomicInteger = new AtomicInteger();
    private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskRenewalOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    private static ThreadPoolExecutor threadPoolExecutorTidb = new ThreadPoolExecutor(1, 1, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskRenewalOverdueBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    /**
     * @param ds
     */
    public void execOldData(String ds, int... overdueDays) {
        // order总记录数
        riskRenewalOrderOverdueService.deleteAll();
        Integer size = getCount(ds);

        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            // int times = 1;
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutor.execute(() -> oldRepayTask(ds, p, overdueDay));
                startPage++;
            }
        }
    }

    /**
     * @param ds
     */
    public void execOldDataTidb(String ds, int... overdueDays) {
        // order总记录数
        Integer size = getRenewCountTidb();
        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE_2000;
            if (size % PAGE_SIZE_2000 != 0) {
                times += 1;
            }
            // int times = 1;
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                final int p = startPage;
                threadPoolExecutorTidb.execute(() -> oldRepayTaskTidb(ds, p, overdueDay));
                startPage++;
            }
        }
    }

    /**
     * @param ds
     */
    public void execOldDataDistributionTidb(String ds, int... overdueDays) {
        // order总记录数
        Integer size = getRenewCountDistributionTidb();

        for (int overdueDay : overdueDays) {
            int times = size / PAGE_SIZE_5000;
            if (size % PAGE_SIZE_5000 != 0) {
                times += 1;
            }
            // int times = 1;
          //  int startPage = 0;
            int id = 12908556;

            // 创建CountDownLatch来等待所有任务完成
            CountDownLatch latch = new CountDownLatch(times);

            for (int i = 0; i < times; i++) {
              //  final int p = startPage;
                int finalId = id;
                threadPoolExecutorTidb.execute(() -> {
                    try {
                        oldRepayTaskDistributionTidb(ds, finalId, overdueDay);
                    } finally {
                        latch.countDown(); // 任务完成后计数减1
                    }
                });
                id = id + PAGE_SIZE_5000;
               // startPage++;
            }

            try {
                // 等待所有任务完成
                latch.await();
                log.info("riskRenewDistributionJobTidb数据运行完成 {} 的所有异步任务已完成，共执行 {} 个任务", overdueDay, times);

            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.error("等待异步任务完成时被中断", e);
            }
        }
    }

    private Integer getRenewCountDistributionTidb() {
        Integer res = rentOrderService.getRenewCountDistributionTidb();

        return res;
    }

    private Integer getRenewCountTidb() {

       Integer res = rentOrderService.getRenewCountTidb();

       return res;
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String ds) {
        String sql = "select count(*) num" +
                " from rent_order" +
                " where merchant_id in (100,10000107)" +
                "  and termination != 5" +
                "  and biz_type = 2" +
                "  and type = 1" +
                "  and parent_id > 0" +
                "    AND date(rent_start_date) >= '2020-10-01'" +
                "  and ds=" + ds +
                ";";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }


    private void oldRepayTask(String ds, int startPage, Integer overdueDay) {
        String sql = "with dt as (\n" +
                "           select date_format(now(), 'yyyymmdd') as ds\n" +
                "   )\n" +
                "   SELECT date(z.rent_start_date)                                                                    rent_start_date,\n" +
                "          rorp.order_id,\n" +
                "          z.no                                                                                       order_no,\n" +
                "          ro.mini_type,\n" +
                "       cl.scene                                                                                        scene,\n" +
                "       cl.quotientname                                                                                 quotient_name,"+
                "          date(repay_date)                                                                           repay_date,\n" +
                "          real_repay_time,\n" +
                "          nvl(auto_renewal, 1)                                                                  auto_renewal,\n" +
                "          IF(settle_date is not null, '1', '0')                                                      is_settle,\n" +
                "          date(settle_date)                                                                          settle_date,\n" +
                "          (CASE\n" +
                "               WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'\n" +
                "               WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信'\n" +
                "               WHEN ro.mini_type = 2 THEN '字节跳动'\n" +
                "               WHEN ro.mini_type = 3 THEN 'app' END)\n" +
                "                                                                                                     platform,\n" +
                "          rate_config_type                                                                           finance_type,\n" +
                "          rorp.term                                                                                  term,\n" +
                "          cl.riskOpinion                                                                             risk_level,\n" +
                "          sn.riskStrategy                                                                            risk_strategy,\n" +
                "          IF(artificialAuditorId = 1, '自动风控', '人工审核')                                                audit_type,\n" +
                "          if(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0')                               forced_conversion,\n" +
                "          (case\n" +
                "               when\n" +
                "                       add_months(date(repay_date) - day(repay_date) + 2, 1) > date(getdate())\n" +
                "                   THEN (case\n" +
                "                             when year(repay_date)=year(getdate()) and month(repay_date) > month" +
                " (getdate()) then '0'\n" +
                "                             when term = 1\n" +
                "                                 then if(date(repay_date) >= date(getdate()), '0',\n" +
                "                                         if(min(repay_status) = 1 and min(overdue) = 5, '1', '0'))\n" +
                "                             else IF(min(repay_status) = 1 and min(overdue) = 5, '1', if(min(repay_status) =5, '0', '2'))\n" +
                "                   end\n" +
                "                   )\n" +
                "               else\n" +
                "                   (case\n" +
                "                        when\n" +
                "                                date(NVL(real_repay_time, getdate())) <= add_months(date(repay_date)" +
                " -\n" +
                "                                                                                   day(repay_date) + 2, 1)\n" +
                "                            then if( min(repay_status) = 5 ,'0','1')\n" +
                "                        else IF(datediff(date(NVL(real_repay_time, getdate())),\n" +
                "                                         date(add_months(date(repay_date) - day(repay_date) + 2, 1)), 'dd') > 3, '1',\n" +
                "                                IF(real_repay_time is not null and min(repay_status) = 5 , '0', '1'))\n" +
                "                       end)\n" +
                "              end\n" +
                "              )                                                                                           is_overdue,\n" +
                "          nvl(if(min(g.ext) != '', get_json_object(min(g.ext), '$.discountReturnAmt'), '0'),\n" +
                "                   '0')                                                                              discount_return_amt,\n" +
                "          if(min(c.renew_type) != 2, min(c.actual_financing_amt), avg(rorp.capital) * 12)\n" +
                "                                                                                                     rent_total,\n" +
                "          nvl(min(rorp.overdue_fine), 0)\n" +
                "                                                                                                     overdue_fine,\n" +
                "          nvl(min(c.act_bond_amt), 0)\n" +
                "                                                                                                     bond_amt,\n" +
                "          nvl(min(x.before_discount), 0)\n" +
                "                                                                                                     before_discount,\n" +
                "          nvl(min(rorpp.bond_rate), 0)                                                               bond_rate,\n" +
                "          (case\n" +
                "               when min(repay_status) = 5 then avg(rorp.capital)\n" +
                "               when min(repay_status) = 1 then (avg(capital) - (avg(real_repay_capital) - avg(discount_amt))) end)\n" +
                "                                                                                                     real_capital,\n" +
                "          avg(rorp.capital)\n" +
                "                                                                                                     capital,\n" +
                "          min(rorp.is_deleted)\n" +
                "                                                                                                     deleted,\n" +
                "          min(repay_status)\n" +
                "                                                                                                     repay_status,\n" +
                "          min(overdue)                                                                               overdue,\n" +
                "          if(real_repay_time is null, 1, 0)                                                          real_repay_time_status,\n" +
                "          min(z.parent_id)                                                                           parent_id,\n" +
                "          min(z.status)                                                                              order_status,\n" +
                "          min(roos.current_overdue_days)                                                             current_overdue_days,\n" +
                "          min(rorp.overdue_days)                                                                     term_overdue_days,\n" +
                "          sum(if(repay_status = 5,real_repay_capital,0))      already_pay,\n" +
                "          sum(if(repay_status = 1, if(real_repay_time is not null, real_repay_capital, capital), 0)) surplus_amt\n" +
                "   FROM (SELECT id, parent_id, no, rent_start_date, status\n" +
                "         FROM rent_order ro\n" +
                "         WHERE ds = (select ds from dt)\n" +
                "           AND merchant_id in (100,10000107)\n" +
                "           AND ro.parent_id > 0\n" +
                "           and biz_type = 2\n" +
                "           AND termination != 5\n" +
                "           AND is_deleted = 0\n" +
                "           AND ro.type = 1\n" +
                "           AND date(rent_start_date) >= date'2020-10-01'\n" +
                "                      ORDER BY id   limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ") z  " +
                "            inner join rent_order ro\n" +
                "                       ON ro.id = z.parent_id\n" +
                "            INNER JOIN rent_order_repayment_plan rorp ON z.id = rorp.order_id\n" +
                "            inner join rent_order_infomore roi on roi.order_id = z.id\n" +
                "            INNER JOIN rent_order_overdue_stat roos\n" +
                "                       ON z.id = roos.order_id\n" +
                "            LEFT join (SELECT min(ext) ext, order_id\n" +
                "                       FROM rent_order_flow b\n" +
                "                       WHERE b.ds = (select ds from dt)\n" +
                "                         AND b.biz_type = 3\n" +
                "                         AND b.is_deleted = 0\n" +
                "                         AND b.pay_status = 10\n" +
                "                         and (b.mark_refund IS NULL OR b.mark_refund = 0)\n" +
                "                         AND b.flow_type = 1\n" +
                "                         AND b.refunded_amt = 0\n" +
                "                         AND b.ext IS NOT NULL\n" +
                "                         AND b.ext != ''\n" +
                "                       GROUP BY order_id) g ON z.id = g.order_id\n" +
                "            INNER JOIN rent_order_finance_detail c ON z.id = c.order_id\n" +
                "            INNER JOIN (select nvl(min(b.bond_amt) / (if(min(b.rate_config_type) != 10, min(b.actual_financing_amt),\n" +
                "                                                              avg(capital) *\n" +
                "                                                              12) +\n" +
                "                                                           if(min(b.rate_config_type != 10), min(b.buyout_amt), 0) -\n" +
                "                                                           min(xx.total_discount)),\n" +
                "                                        0) bond_rate,\n" +
                "                               a.order_id\n" +
                "                        from rent_order_repayment_plan a\n" +
                "                                 INNER JOIN rent_order_finance_detail b ON a.order_id = b.order_id\n" +
                "                                 LEFT join (select order_id,\n" +
                "                                                   if(min(isbefore) = 1, min(amt1) + min(amt2), 0)           total_discount,\n" +
                "                                                   if(min(isbefore) = 1, min(yuji_amt1) + min(yuji_amt2), 0) before_discount\n" +
                "                                            from (SELECT x.order_id,\n" +
                "                                                         if(min(x.bind_order_time) IS NOT NULL AND\n" +
                "                                                            min(y.payment_time) >= min(x.bind_order_time), 1, 0) isbefore,\n" +
                "                                                         if(x.type IN (1, 3), max(x.write_off_amt), 0)           amt1,\n" +
                "                                                         if(x.type IN (2, 4), sum(x.write_off_amt), 0)           amt2,\n" +
                "                                                         if(x.type IN (1, 3) AND min(x.use_status = 2),\n" +
                "                                                            max(x.write_off_amt), 0)                             yuji_amt1,\n" +
                "                                                         if(x.type IN (2, 4) AND min(x.use_status = 2),\n" +
                "                                                            sum(x.write_off_amt), 0)                             yuji_amt2\n" +
                "                                                  FROM rent_customer_coupon x\n" +
                "                                                           INNER JOIN rent_order y ON x.order_id = y.id\n" +
                "                                                  WHERE x.order_id > 0\n" +
                "                                                    AND x.scene = 1\n" +
                "                                                    AND x.is_deleted = 0\n" +
                "                                                    and x.ds = (select ds from dt)\n" +
                "                                                    and y.ds = (select ds from dt)\n" +
                "                                                    AND y.is_deleted = 0\n" +
                "                                                  GROUP BY x.order_id, x.type) d\n" +
                "                                            group by order_id) xx ON b.order_id = xx.order_id\n" +
                "                        where a.ds = (select ds from dt)\n" +
                "                          and b.ds = (select ds from dt)\n" +
                "                        group by a.order_id\n" +
                "   ) rorpp ON z.parent_id = rorpp.order_id\n" +
                "            LEFT join (select order_id,\n" +
                "                              if(min(isbefore) = 1, min(amt1) + min(amt2), 0)\n" +
                "                                  total_discount,\n" +
                "                              if(min(isbefore) = 1, min(yuji_amt1) + min(yuji_amt2), 0)\n" +
                "                                  before_discount\n" +
                "                       from (SELECT x.order_id,\n" +
                "                                    if(min(x.bind_order_time) IS NOT NULL AND\n" +
                "                                       min(y.payment_time) >= min(x.bind_order_time), 1, 0)\n" +
                "                                                            isbefore,\n" +
                "                                    if(x.type IN (1, 3), max(x.write_off_amt), 0)\n" +
                "                                                            amt1,\n" +
                "                                    if(x.type IN (2, 4), sum(x.write_off_amt), 0)\n" +
                "                                                            amt2,\n" +
                "                                    if(x.type IN (1, 3) AND min(x.use_status = 2), max(x\n" +
                "                                        .write_off_amt), 0) yuji_amt1,\n" +
                "                                    if(x.type IN (2, 4) AND min(x.use_status = 2), sum(x\n" +
                "                                        .write_off_amt), 0) yuji_amt2\n" +
                "                             FROM rent_customer_coupon x\n" +
                "                                      INNER JOIN rent_order y ON x.order_id = y.id\n" +
                "                             WHERE x.order_id > 0\n" +
                "                               AND x.scene = 1\n" +
                "                               AND x.is_deleted = 0\n" +
                "                               AND y.is_deleted = 0\n" +
                "                               AND x.ds = (select ds from dt)\n" +
                "                               AND y.ds = (select ds from dt)\n" +
                "                             GROUP BY x.order_id, x.type)\n" +
                "                       group by order_id) x ON z.id = x.order_id\n" +
                "            INNER JOIN cl_loan cl ON cl.loanno = ro.no\n" +
                "            INNER JOIN serial_no sn ON sn.businessno = ro.no\n" +
                "   WHERE ro.ds = (select ds from dt)\n" +
                "     AND rorp.ds = (select ds from dt)\n" +
                "     AND rorp.is_deleted = 0\n" +
                "     AND c.ds = (select ds from dt)\n" +
                "     AND cl.ds = (select ds from dt)\n" +
                "     AND sn.ds = (select ds from dt)\n" +
                "     AND roi.ds = (select ds from dt)\n" +
                "     AND roos.ds = (select ds from dt)\n" +
                "   GROUP BY date(z.rent_start_date), ro.mini_type, rorp.order_id, rate_config_type,\n" +
                "            IF(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0'), rorp.term, cl.riskOpinion,\n" +
                "            sn.riskStrategy,\n" +
                "            auto_renewal,\n" +
                "            settle_date,\n" +
                "            cl.artificialAuditorId, z.no, repay_date, cl.scene, cl.quotientname,real_repay_time,\n" +
                "            (c.bond_amt / (if(c.rate_config_type != 10, c.actual_financing_amt,\n" +
                "                              rorp.capital * 12) + if(c.rate_config_type != 10, c.buyout_amt, 0)) - nvl(c.diff_pricing_discount_amt,0)-nvl(c.coupon_discount_amt,0))\n" +
                "   ORDER BY date(z.rent_start_date);";
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        saveData(records, overdueDay);
    }


    private void oldRepayTaskTidb(String ds, int startPage, Integer overdueDay) {
        startPage = startPage * PAGE_SIZE_2000;
        List<GeneralRenewRecord> generalRecords = rentOrderService.getAllGeneralRenewData(startPage,PAGE_SIZE_2000,overdueDay);
        log.info("tidb-大盘通用续租renew模块查询原始表跑出来的数据量大小为:ds="+ds+"  总条数为: "+generalRecords.size()+"开始页startPage="+startPage+" PAGE_SIZE="+PAGE_SIZE_2000);
        saveData_tidb(generalRecords, overdueDay);
    }


    private void oldRepayTaskDistributionTidb(String ds, int id, Integer overdueDay) {
        //startPage = startPage * PAGE_SIZE_5000;
        List<GeneralRenewRecord> distributionRecords = rentOrderService.getAllDistributionRenewData(id,
                PAGE_SIZE_5000, overdueDay);
        log.info(
                "tidb-renew商户续租分页查询[日期ds={}, total={}, pageNo={}, pageSize={}]",
                ds,
                distributionRecords.size(),
                id,
                PAGE_SIZE_5000
        );
        saveDataDistribution_tidb(distributionRecords, overdueDay);
    }



    private void saveData(List<Record> records, Integer overdueDay) {
        try {
//            List<RiskRenewalOrderOverdueDO> resList = fetchList(records, overdueDay);
//            riskRenewalOrderOverdueService.saveBatch(resList);
            // todo 通用列表
            List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay);
            riskGeneralOrderOverdueService.updateBatchById(generalList,5000);

//            List<DataCheckAfterRentOverviewDO> checkList = fetchAfterCheckList(records);
//            dataCheckAfterRentOverviewService.updateBatchById(checkList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void saveData_tidb(List<GeneralRenewRecord> records, Integer overdueDay) {
        try {
//            List<RiskRenewalOrderOverdueDO> resList = fetchList(records, overdueDay);
//            riskRenewalOrderOverdueService.saveBatch(resList);
            // todo 通用列表
            List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralListTidb(records, overdueDay);
            riskGeneralOrderOverdueService.updateBatchById(generalList,5000);

//            List<DataCheckAfterRentOverviewDO> checkList = fetchAfterCheckList(records);
//            dataCheckAfterRentOverviewService.updateBatchById(checkList);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private void saveDataDistribution_tidb(List<GeneralRenewRecord> records, Integer overdueDay) {
        try {
//            List<RiskRenewalOrderOverdueDO> resList = fetchList(records, overdueDay);
//            riskRenewalOrderOverdueService.saveBatch(resList);
            // todo 商户列表
            List<RiskDistributionOrderOverdueDO> distributionList = fetchDistributionListTidb(records, overdueDay);
            riskDistributionOrderOverdueService.updateBatchById(distributionList,5000);

        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    private List<DataCheckAfterRentOverviewDO> fetchAfterCheckList(List<Record> records) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        List<Long> parentIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Long parentId = Long.valueOf(domain.getString("parent_id"));
                parentIds.add(parentId);
                Integer currentOverdueDays = Integer.valueOf(domain.getString("current_overdue_days"));
                String orderNo = domain.getString("order_no");
                Integer orderStatus = Integer.valueOf(domain.getString("order_status"));
                BigDecimal surplusAmt = stringToDecimal(domain.getString("surplus_amt"));
                BigDecimal alreadyPay = stringToDecimal(domain.getString("already_pay"));
                Integer autoRenewal = Integer.valueOf(domain.getString("auto_renewal"));
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setParentId(parentId);
                repaymentPlanDO.setCurrentOverdueDays(currentOverdueDays);
                repaymentPlanDO.setNo(orderNo);
                repaymentPlanDO.setOrderStatus(orderStatus);
                repaymentPlanDO.setSurplusAmt(surplusAmt);
                repaymentPlanDO.setAlreadyPay(alreadyPay);
                repaymentPlanDO.setRenewType(autoRenewal);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }

        //  获取父订单信息
        List<DataCheckAfterRentOverviewDO> parentList = dataCheckAfterRentOverviewService.getList(parentIds);
        Map<Long, DataCheckAfterRentOverviewDO> orderId2DO =
                parentList.stream().collect(Collectors.toMap(DataCheckAfterRentOverviewDO::getOrderId, Function.identity()));

        List<DataCheckAfterRentOverviewDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getParentId));
        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            BigDecimal sum = ObjectUtils.getSum(value, RepaymentPlanDO::getSurplusAmt);
            BigDecimal sumPay = ObjectUtils.getSum(value, RepaymentPlanDO::getAlreadyPay);
            // 获取一个还款计划对象
            RepaymentPlanDO renewOverdueBaseDO = value.get(0);
            Long parentId = entry.getKey();
            // 获取父订单对象
            DataCheckAfterRentOverviewDO parentDO = orderId2DO.get(parentId);
            if (parentDO == null) {
                continue;
            }
            parentDO.setChildSurplusAmt(sum);
            parentDO.setChildAlreadyPay(sumPay);
            parentDO.setChildStatus(renewOverdueBaseDO.getOrderStatus());
            parentDO.setChildNo(renewOverdueBaseDO.getNo());
            parentDO.setChildOrderId(renewOverdueBaseDO.getOrderId());
            parentDO.setChildOverdueDay(renewOverdueBaseDO.getCurrentOverdueDays());
            parentDO.setRenewType(renewOverdueBaseDO.getRenewType());
            resList.add(parentDO);
        }
        return resList;
    }
    private List<RiskRenewalOrderOverdueDO> fetchList(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Integer term = Integer.valueOf(domain.getString("term"));
                Integer overdue = Integer.valueOf(domain.getString("overdue"));
                Integer isDeleted = Integer.valueOf(domain.getString("deleted"));
                Integer autoRenewal = Integer.valueOf(domain.getString("auto_renewal"));
                Integer miniType = Integer.valueOf(domain.getString("mini_type"));
                Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
                Integer realRepayTimeStatus = Integer.valueOf(domain.getString("real_repay_time_status"));
                String platform = domain.getString("platform");
                String no = domain.getString("order_no");
                Integer financeType = Integer.valueOf(domain.getString("finance_type"));
                String riskLevel = domain.getString("risk_level");
                if(riskLevel.contains("风控等级")){
                    // 将风控等级4.0和风控等级4之类的进行合并
                    String[] split = riskLevel.split("\\.");
                    riskLevel = split[0];
                }
                String riskStrategy = domain.getString("risk_strategy");
                String auditType = domain.getString("audit_type");
                Integer isSettle = Integer.valueOf(domain.getString("is_settle"));
                Integer forcedConversion = Integer.valueOf(domain.getString("forced_conversion"));
                String isOverdue = domain.getString("is_overdue");
                BigDecimal capital = stringToDecimal(domain.getString("capital"));
                BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
                String scene = domain.getString("scene");
                String quotientName = domain.getString("quotient_name");
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("rent_start_date"),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime realRepayTime = null;
                if (!domain.getString("real_repay_time").equals("\\N")) {
                    // 有8小时时差
                    realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);

                }
                BigDecimal discountReturnAmt = stringToDecimal(domain.getString("discount_return_amt"));
                BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));
                BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
                BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
                double bondRate = Double.parseDouble(domain.getString("bond_rate"));
                BigDecimal beforeDiscount = stringToDecimal(domain.getString("before_discount"));
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setRenewWay(autoRenewal);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }

        List<RiskRenewalOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            Long orderId = entry.getKey();
            List<RepaymentPlanDO> value = entry.getValue();
            RiskRenewalOrderOverdueDO riskOrderOverdueDO = null;
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
            Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            for (RepaymentPlanDO repaymentPlanDO : value) {
                if (riskOrderOverdueDO == null) {
                    riskOrderOverdueDO = new RiskRenewalOrderOverdueDO();
                    resList.add(riskOrderOverdueDO);
                    riskOrderOverdueDO.setOrderId(orderId);
                    riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
                    riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
                    riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
                    riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
                    riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
                    riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
                    riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
                    riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
                    riskOrderOverdueDO.setDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
                    riskOrderOverdueDO.setBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
                    riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRate());
                    riskOrderOverdueDO.setIsSettle(repaymentPlanDO.getIsSettle());
                    riskOrderOverdueDO.setOverdueDay(overdueDay);
                    riskOrderOverdueDO.setRenewWay(repaymentPlanDO.getRenewWay());
                    riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
                    riskOrderOverdueDO.setScene(repaymentPlanDO.getScene());
                    riskOrderOverdueDO.setQuotientName(repaymentPlanDO.getQuotientName());
                }

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                int term = repaymentPlanDO.getTerm();
                //对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
                riskOrderOverdueDO.setCountDay(rentStartDay);
                riskOrderOverdueDO.setMaxDay(maxTerm);
                setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                    // 封账日期
                    LocalDate localDate =
                            LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    LocalDate maxRepayDate =
                            LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                    resMap.put("maxRepayDate", maxRepayDate);
                    resMap.put("endTime", localDate);
                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
                }
            }
        }
        return resList;
    }

    private int updateOverdueStatus(Map<Integer, List<RepaymentPlanDO>> term2PlanDo, int term) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(--term);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (repaymentPlanDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        int isOverdue = Integer.parseInt(planDO.getIsOverdue());
        // 上一期逾期并且未还款,这一期即使还没到,算逾期
        // 上一期逾期并且还款,这一期还没到,不算逾期
        // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
        // 如果当前期逾期,不管有没有到封账日,都算逾期
        int repayStatus = planDO.getRepayStatus();
        return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
    }

    private List<RiskGeneralOrderOverdueDO> fetchGeneralList(List<Record> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        List<Long> parentIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getString("order_id"));
                Long parentId = Long.valueOf(domain.getString("parent_id"));
                parentIds.add(parentId);
                Integer term = Integer.valueOf(domain.getString("term"));
                Integer overdue = Integer.valueOf(domain.getString("overdue"));
                Integer isDeleted = Integer.valueOf(domain.getString("deleted"));
                Integer autoRenewal = Integer.valueOf(domain.getString("auto_renewal"));
                Integer miniType = Integer.valueOf(domain.getString("mini_type"));
                Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
                Integer realRepayTimeStatus = Integer.valueOf(domain.getString("real_repay_time_status"));
                String platform = domain.getString("platform");
                String no = domain.getString("order_no");
                Integer financeType = Integer.valueOf(domain.getString("finance_type"));
                String riskLevel = domain.getString("risk_level");
                String riskStrategy = domain.getString("risk_strategy");
                String auditType = domain.getString("audit_type");
                Integer isSettle = Integer.valueOf(domain.getString("is_settle"));
                Integer forcedConversion = Integer.valueOf(domain.getString("forced_conversion"));
                String isOverdue = domain.getString("is_overdue");
                BigDecimal capital = stringToDecimal(domain.getString("capital"));
                BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("rent_start_date"),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime settleDate = null;
                if (!domain.getString("settle_date").equals("\\N")) {
                    settleDate = LocalDateTime.of(LocalDate.parse(domain.getString("settle_date"),
                            dateFormatter), LocalTime.MIN);
                }
                LocalDateTime realRepayTime = null;
                if (!domain.getString("real_repay_time").equals("\\N")) {
                    // 有8小时时差
                    realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);

                }
                BigDecimal discountReturnAmt = stringToDecimal(domain.getString("discount_return_amt"));
                BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));
                BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
                BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
                double bondRate = Double.parseDouble(domain.getString("bond_rate"));
                BigDecimal beforeDiscount = stringToDecimal(domain.getString("before_discount"));
                String scene = domain.getString("scene");
                String quotientName = domain.getString("quotient_name");
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setSettleDate(settleDate);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setParentId(parentId);
                repaymentPlanDO.setRenewWay(autoRenewal);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        // TODO 获取父订单信息
        List<RiskGeneralOrderOverdueDO> parentList = riskGeneralOrderOverdueService.getList(parentIds, overdueDay);
        Map<Long, RiskGeneralOrderOverdueDO> orderId2DO =
                parentList.stream().collect(Collectors.toMap(RiskGeneralOrderOverdueDO::getOrderId, Function.identity()));


        List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));

            // 获取一个还款计划对象
            RepaymentPlanDO repaymentPlanDO1 = value.get(0);
            Long parentId = repaymentPlanDO1.getParentId();
            int isSettle = repaymentPlanDO1.getIsSettle();
            // 获取父订单对象
            RiskGeneralOrderOverdueDO parentDO = orderId2DO.get(parentId);
            if (parentDO == null) {
                continue;
            }
            parentDO.setDiscountReturnAmt(repaymentPlanDO1.getDiscountReturnAmt());
            parentDO.setBeforeDiscount(repaymentPlanDO1.getBeforeDiscount());
            parentDO.setRenewWay(repaymentPlanDO1.getRenewWay());
            parentDO.setChildNo(repaymentPlanDO1.getNo());
            parentDO.setChildOrderId(repaymentPlanDO1.getOrderId());
            resList.add(parentDO);

            List<RepaymentPlanDO> collect = value.stream().
                    sorted(Comparator.comparing(RepaymentPlanDO::getTerm)).collect(Collectors.toList());

            // TODO 父订单未还金额
            // 获取父订单最后一期的未还租金
            BigDecimal parentNotReturnAmt = getParentNotReturnAmt(parentDO, parentDO.getPMaxTerm());
            Integer renewMaxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            for (RepaymentPlanDO repaymentPlanDO : collect) {

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                Integer pMaxTerm = parentDO.getPMaxTerm();
                int term = repaymentPlanDO.getTerm();
                //对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                parentDO.setMaxTerm(pMaxTerm + renewMaxTerm);
                setOverdueVal(parentDO, term + pMaxTerm, isOverdue, repaymentPlanDO.getOverdueFine());
                LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, renewMaxTerm, term2PlanDo);
                LocalDate maxRepayDate =
                        LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                // 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    // 封账日期
                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);
                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // term + pMaxTerm 续租订单连上主订单的期数
                    setRenewAmtVal(parentDO, term + pMaxTerm, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setRenewAmtVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
                }

                // 设置续租订单的未还租金为父订单的逾期未还金额
                LocalDateTime settleDate = repaymentPlanDO1.getSettleDate();
                LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
                if (isSettle == 1 && settleDate != null && settleDate.compareTo(endTime) <= 0) {
                    // 父订单已结清，并且结清时间小于等于这一期封账,就是未逾期.直接为0,
                    continue;
                }
                // 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    // 获取逾期未还金额
                    // 设置对应期数未还金额
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                } else {
                    parentNotReturnAmt = BigDecimal.ZERO;
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                }
            }
        }
        return resList;
    }

    private List<RiskGeneralOrderOverdueDO> fetchGeneralListTidb(List<GeneralRenewRecord> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        List<Long> parentIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getOrderId()); //当前子订单的订单orderId
                Long parentId = Long.valueOf(domain.getParentId()); //父订单的订单id
                parentIds.add(parentId);
                Integer term = Integer.valueOf(domain.getTerm());
                Integer overdue = Integer.valueOf(domain.getOverdue());
                Integer isDeleted = Integer.valueOf(domain.getDeleted());
                Integer autoRenewal = Integer.valueOf(domain.getAutoRenewal());
                Integer miniType = Integer.valueOf(domain.getMiniType());
                Integer repayStatus = Integer.valueOf(domain.getRepayStatus());
                Integer realRepayTimeStatus = Integer.valueOf(domain.getRealRepayTimeStatus());
                String platform = domain.getPlatform();
                String no = domain.getOrderNo(); //子订单的订单号
                Integer financeType = Integer.valueOf(domain.getFinanceType());
                String riskLevel = domain.getRiskLevel();
                String riskStrategy = domain.getRiskStrategy();
                String auditType = domain.getAuditType();
                Integer isSettle = Integer.valueOf(domain.getIsSettle());
                Integer forcedConversion = Integer.valueOf(domain.getForcedConversion());
                String isOverdue = domain.getIsOverdue(); //子订单是否逾期
                BigDecimal capital = stringToDecimal(domain.getCapital()); //子订单应还金额
                BigDecimal realCapital = stringToDecimal(domain.getRealCapital());//子订单未还金额
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getRentStartDate(),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getRepayDate(),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime settleDate = null;
                if (null != domain.getSettleDate()&& !domain.getSettleDate().equals("\\N")) {
                    settleDate = LocalDateTime.of(LocalDate.parse(domain.getSettleDate(),
                            dateFormatter), LocalTime.MIN);
                }
                LocalDateTime realRepayTime = null;
                if (null != domain.getRealRepayTime() && !domain.getRealRepayTime().equals("\\N")) {
                    // 有8小时时差
                    realRepayTime = LocalDateTime.parse(domain.getRealRepayTime(), YYYY_MM_DD_HH_MM_SS);

                }
                BigDecimal discountReturnAmt = stringToDecimal(domain.getDiscountReturnAmt());
                BigDecimal rentTotal = stringToDecimal(domain.getRentTotal());
                BigDecimal buyOutCapital = domain.getBuyOutCapital();
                BigDecimal buyOutRealRepayCapital = domain.getBuyOutRealRepayCapital();
                BigDecimal overdueFine = stringToDecimal(domain.getOverdueFine());
                BigDecimal bondAmt = stringToDecimal(domain.getBondAmt());
                double bondRate = Double.parseDouble(domain.getBondRate());
                BigDecimal beforeDiscount = stringToDecimal(domain.getBeforeDiscount());
                String scene = domain.getScene();
                String quotientName = domain.getQuotientName();
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setSettleDate(settleDate);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setParentId(parentId);
                repaymentPlanDO.setRenewWay(autoRenewal);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                repaymentPlanDO.setBuyOutCapital(buyOutCapital);
                repaymentPlanDO.setBuyOutRealRepayCapital(buyOutRealRepayCapital);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        // TODO 获取父订单信息
        List<RiskGeneralOrderOverdueDO> parentList = riskGeneralOrderOverdueService.getList(parentIds, overdueDay);
        Map<Long, RiskGeneralOrderOverdueDO> orderId2DO =
                parentList.stream().collect(Collectors.toMap(RiskGeneralOrderOverdueDO::getOrderId, Function.identity()));


        //子订单的订单orderId
        List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));

            // 获取一个还款计划对象
            RepaymentPlanDO repaymentPlanDO1 = value.get(0);
            Long parentId = repaymentPlanDO1.getParentId();
            int isSettle = repaymentPlanDO1.getIsSettle();
            // 获取父订单对象
            RiskGeneralOrderOverdueDO parentDO = orderId2DO.get(parentId);
            if (parentDO == null) {
                continue;
            }
            parentDO.setDiscountReturnAmt(repaymentPlanDO1.getDiscountReturnAmt());
            parentDO.setBeforeDiscount(repaymentPlanDO1.getBeforeDiscount());
            parentDO.setRenewWay(repaymentPlanDO1.getRenewWay());
            parentDO.setChildNo(repaymentPlanDO1.getNo());
            parentDO.setChildOrderId(repaymentPlanDO1.getOrderId());
            parentDO.setBuyOutCapital(repaymentPlanDO1.getBuyOutCapital());
            parentDO.setBuyOutRealRepayCapital(repaymentPlanDO1.getBuyOutRealRepayCapital());
            resList.add(parentDO);

            List<RepaymentPlanDO> collect = value.stream().
                    sorted(Comparator.comparing(RepaymentPlanDO::getTerm)).collect(Collectors.toList());

            // TODO 父订单未还金额
            // 获取父订单最后一期的未还租金
            BigDecimal parentNotReturnAmt = getParentNotReturnAmt(parentDO, parentDO.getPMaxTerm());
            Integer renewMaxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            for (RepaymentPlanDO repaymentPlanDO : collect) {

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                Integer pMaxTerm = parentDO.getPMaxTerm();
                int term = repaymentPlanDO.getTerm();
                //对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                parentDO.setMaxTerm(pMaxTerm + renewMaxTerm);
                setOverdueVal(parentDO, term + pMaxTerm, isOverdue, repaymentPlanDO.getOverdueFine());
                LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, renewMaxTerm, term2PlanDo);
                LocalDate maxRepayDate =
                        LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                // 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    // 封账日期
                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);
                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // term + pMaxTerm 续租订单连上主订单的期数
                    setRenewAmtVal(parentDO, term + pMaxTerm, decimal);
//                    log.info("*********fetchGeneralListTidb****setTermNotRepayVal=******"+decimal+"***********期数="+term + pMaxTerm);
//                    setTermNotRepayVal(parentDO, term + pMaxTerm, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setRenewAmtVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
                    setTermNotRepayVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
                }

                // 设置续租订单的未还租金为父订单的逾期未还金额
                LocalDateTime settleDate = repaymentPlanDO1.getSettleDate();
                LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
                if (isSettle == 1 && settleDate != null && settleDate.compareTo(endTime) <= 0) {
                    // 父订单已结清，并且结清时间小于等于这一期封账,就是未逾期.直接为0,
                    continue;
                }
                // 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    // 获取逾期未还金额
                    // 设置对应期数未还金额
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                } else {
                    parentNotReturnAmt = BigDecimal.ZERO;
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                }
            }
        }
        return resList;
    }


    private List<RiskDistributionOrderOverdueDO> fetchDistributionListTidb(List<GeneralRenewRecord> records, Integer overdueDay) {
        List<RepaymentPlanDO> planList = Lists.newArrayList();
        List<Long> parentIds = Lists.newArrayList();
        if (CollUtil.isNotEmpty(records)) {
            planList = records.stream().map(domain -> {
                RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
                Long orderId = Long.valueOf(domain.getOrderId()); //当前子订单的订单orderId
                Long parentId = Long.valueOf(domain.getParentId()); //父订单的订单id
                parentIds.add(parentId);
                Integer term = Integer.valueOf(domain.getTerm());
                Integer overdue = Integer.valueOf(domain.getOverdue());
                Integer isDeleted = Integer.valueOf(domain.getDeleted());
                Integer autoRenewal = Integer.valueOf(domain.getAutoRenewal());
                Integer miniType = Integer.valueOf(domain.getMiniType());
                Integer repayStatus = Integer.valueOf(domain.getRepayStatus());
                Integer realRepayTimeStatus = Integer.valueOf(domain.getRealRepayTimeStatus());
                String platform = domain.getPlatform();
                String no = domain.getOrderNo(); //子订单的订单号
                Integer financeType = Integer.valueOf(domain.getFinanceType());
                String riskLevel = domain.getRiskLevel();
                String riskStrategy = domain.getRiskStrategy();
                String auditType = domain.getAuditType();
                Integer isSettle = Integer.valueOf(domain.getIsSettle());
                Integer forcedConversion = Integer.valueOf(domain.getForcedConversion());
                String isOverdue = domain.getIsOverdue(); //子订单是否逾期
                BigDecimal capital = stringToDecimal(domain.getCapital()); //子订单应还金额
                BigDecimal realCapital = stringToDecimal(domain.getRealCapital());//子订单未还金额
                LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getRentStartDate(),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getRepayDate(),
                        dateFormatter), LocalTime.MIN);
                LocalDateTime settleDate = null;
                if (null != domain.getSettleDate()&& !domain.getSettleDate().equals("\\N")) {
                    settleDate = LocalDateTime.of(LocalDate.parse(domain.getSettleDate(),
                            dateFormatter), LocalTime.MIN);
                }
                LocalDateTime realRepayTime = null;
                if (null != domain.getRealRepayTime() && !domain.getRealRepayTime().equals("\\N")) {
                    // 有8小时时差
                    realRepayTime = LocalDateTime.parse(domain.getRealRepayTime(), YYYY_MM_DD_HH_MM_SS);

                }
                BigDecimal discountReturnAmt = stringToDecimal(domain.getDiscountReturnAmt());
                BigDecimal rentTotal = stringToDecimal(domain.getRentTotal());
                BigDecimal overdueFine = stringToDecimal(domain.getOverdueFine());
                BigDecimal bondAmt = stringToDecimal(domain.getBondAmt());
                double bondRate = Double.parseDouble(domain.getBondRate());
                BigDecimal beforeDiscount = stringToDecimal(domain.getBeforeDiscount());
                String scene = domain.getScene();
                String quotientName = domain.getQuotientName();
                repaymentPlanDO.setCapital(capital);
                repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
                repaymentPlanDO.setRealCapital(realCapital);
                repaymentPlanDO.setOverdue(overdue);
                repaymentPlanDO.setRepayDate(repayDate);
                repaymentPlanDO.setRealRepayTime(realRepayTime);
                repaymentPlanDO.setOrderId(orderId);
                repaymentPlanDO.setSettleDate(settleDate);
                repaymentPlanDO.setTerm(term);
                repaymentPlanDO.setParentId(parentId);
                repaymentPlanDO.setRenewWay(autoRenewal);
                repaymentPlanDO.setIsSettle(isSettle);
                repaymentPlanDO.setIsDeleted(isDeleted);
                repaymentPlanDO.setBondRate(bondRate);
                repaymentPlanDO.setRepayStatus(repayStatus);
                repaymentPlanDO.setIsOverdue(isOverdue);
                repaymentPlanDO.setRentStartDate(rentStartDay);
                repaymentPlanDO.setMiniType(miniType);
                repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
                        forcedConversion)));
                repaymentPlanDO.setAuditType(auditType);
                repaymentPlanDO.setPlatform(platform);
                repaymentPlanDO.setRiskLevel(riskLevel);
                repaymentPlanDO.setRiskStrategy(riskStrategy);
                repaymentPlanDO.setRentTotal(rentTotal);
                repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
                repaymentPlanDO.setOverdueFine(overdueFine);
                repaymentPlanDO.setNo(no);
                repaymentPlanDO.setBondAmt(bondAmt);
                repaymentPlanDO.setBeforeDiscount(beforeDiscount);
                repaymentPlanDO.setScene(scene);
                repaymentPlanDO.setQuotientName(quotientName);
                return repaymentPlanDO;
            }).collect(Collectors.toList());
        }
        // TODO 获取父订单信息
        List<RiskDistributionOrderOverdueDO> parentList = riskDistributionOrderOverdueService.getList(parentIds, overdueDay);
        Map<Long, RiskDistributionOrderOverdueDO> orderId2DO =
                parentList.stream().collect(Collectors.toMap(RiskDistributionOrderOverdueDO::getOrderId, Function.identity()));


        //子订单的订单orderId
        List<RiskDistributionOrderOverdueDO> resList = new ArrayList<>();
        Map<Long, List<RepaymentPlanDO>> orderId2List =
                planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));

        for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
                    value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));

            // 获取一个还款计划对象
            RepaymentPlanDO repaymentPlanDO1 = value.get(0);
            Long parentId = repaymentPlanDO1.getParentId();
            int isSettle = repaymentPlanDO1.getIsSettle();
            // 获取父订单对象
            RiskDistributionOrderOverdueDO parentDO = orderId2DO.get(parentId);
            if (parentDO == null) {
                continue;
            }
            parentDO.setDiscountReturnAmt(repaymentPlanDO1.getDiscountReturnAmt());
            parentDO.setBeforeDiscount(repaymentPlanDO1.getBeforeDiscount());
//            parentDO.setRenewWay(repaymentPlanDO1.getRenewWay());
            parentDO.setChildNo(repaymentPlanDO1.getNo());
            parentDO.setChildOrderId(repaymentPlanDO1.getOrderId());
            resList.add(parentDO);

            List<RepaymentPlanDO> collect = value.stream().
                    sorted(Comparator.comparing(RepaymentPlanDO::getTerm)).collect(Collectors.toList());

            // TODO 父订单未还金额
            // 获取父订单最后一期的未还租金
            BigDecimal parentNotReturnAmt = getParentNotReturnAmt(parentDO, parentDO.getPMaxTerm());
            Integer renewMaxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
            for (RepaymentPlanDO repaymentPlanDO : collect) {

                int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
                Integer pMaxTerm = parentDO.getPMaxTerm();
                int term = repaymentPlanDO.getTerm();
                //对当前期逾期情况做修正
                if (isOverdue == 2) {
                    isOverdue = updateOverdueStatus(term2PlanDo, term);
                    repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
                    repaymentPlanDO.setOverdue(5);
                }
                parentDO.setMaxTerm(pMaxTerm + renewMaxTerm);
                setOverdueVal(parentDO, term + pMaxTerm, isOverdue, repaymentPlanDO.getOverdueFine());
                LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
                LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                RepaymentPlanDO maxTerPlanDO = getPlanDo(value, renewMaxTerm, term2PlanDo);
                LocalDate maxRepayDate =
                        LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
                // 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    Map<String, Object> resMap = new HashMap<>();
                    resMap.put("term", BigDecimal.valueOf(term));
                    resMap.put("capital", BigDecimal.ZERO);
                    // 封账日期
                    resMap.put("endTime", localDate);
                    resMap.put("maxRepayDate", maxRepayDate);
                    // 获取逾期未还金额
                    fetchCapital(term2PlanDo, resMap);
                    // 设置对应期数未还金额
                    BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
                    // term + pMaxTerm 续租订单连上主订单的期数
                    setRenewAmtVal(parentDO, term + pMaxTerm, decimal);
//                    log.info("*********fetchGeneralListTidb****setTermNotRepayVal=******"+decimal+"***********期数="+term + pMaxTerm);
//                    setTermNotRepayVal(parentDO, term + pMaxTerm, decimal);
                } else {
                    // 未逾期，逾期未还金额为0
                    setRenewAmtVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
                    setTermNotRepayVal(parentDO, term + pMaxTerm, BigDecimal.ZERO);
                }

                // 设置续租订单的未还租金为父订单的逾期未还金额
                LocalDateTime settleDate = repaymentPlanDO1.getSettleDate();
                LocalDateTime endTime = LocalDateTime.of(localDate, LocalTime.MAX);
                if (isSettle == 1 && settleDate != null && settleDate.compareTo(endTime) <= 0) {
                    // 父订单已结清，并且结清时间小于等于这一期封账,就是未逾期.直接为0,
                    continue;
                }
//                 设置续租订单逾期未还金额
                if (isOverdue == 1) {
                    // 获取逾期未还金额
                    // 设置对应期数未还金额
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                } else {
                    parentNotReturnAmt = BigDecimal.ZERO;
                    // 未逾期，逾期未还金额为0
                    setTermNotRepayVal(parentDO, term + pMaxTerm, parentNotReturnAmt);
                }
            }
        }
        return resList;
    }

    private RepaymentPlanDO getPlanDo(List<RepaymentPlanDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanDO>> term2PlanDo) {
        List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
        RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
        if (value.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        return planDO;
    }

    private BigDecimal getParentNotReturnAmt(RiskDistributionOrderOverdueDO parentDO, Integer term) {
        BigDecimal amt = BigDecimal.ZERO;
        if (term == 1) {
            amt = parentDO.getTerm1();
        } else if (term == 2) {
            amt = parentDO.getTerm2();
        } else if (term == 3) {
            amt = parentDO.getTerm3();
        } else if (term == 4) {
            amt = parentDO.getTerm4();
        } else if (term == 5) {
            amt = parentDO.getTerm5();
        } else if (term == 6) {
            amt = parentDO.getTerm6();
        } else if (term == 7) {
            amt = parentDO.getTerm7();
        } else if (term == 8) {
            amt = parentDO.getTerm8();
        } else if (term == 9) {
            amt = parentDO.getTerm9();
        } else if (term == 10) {
            amt = parentDO.getTerm10();
        } else if (term == 11) {
            amt = parentDO.getTerm11();
        } else if (term == 12) {
            amt = parentDO.getTerm12();
        }
        else if (term == 13) {
            amt = parentDO.getTerm13();
        }
        else if (term == 14) {
            amt = parentDO.getTerm14();
        }
        else if (term == 15) {
            amt = parentDO.getTerm15();
        }
        else if (term == 16) {
            amt = parentDO.getTerm16();
        }
        else if (term == 17) {
            amt = parentDO.getTerm17();
        }
        else if (term == 18) {
            amt = parentDO.getTerm18();
        }
        else if (term == 19) {
            amt = parentDO.getTerm19();
        }
        else if (term == 20) {
            amt = parentDO.getTerm20();
        }
        else if (term == 21) {
            amt = parentDO.getTerm21();
        }
        else if (term == 22) {
            amt = parentDO.getTerm22();
        }
        else if (term == 23) {
            amt = parentDO.getTerm23();
        }
        else if (term == 24) {
            amt = parentDO.getTerm24();
        }
        else if (term == 25) {
            amt = parentDO.getTerm25();
        }
        else if (term == 26) {
            amt = parentDO.getTerm26();
        }
        else if (term == 27) {
            amt = parentDO.getTerm27();
        }
        else if (term == 28) {
            amt = parentDO.getTerm28();
        }
        else if (term == 29) {
            amt = parentDO.getTerm29();
        }
        else if (term == 30) {
            amt = parentDO.getTerm30();
        }
        return amt;
    }

    private BigDecimal getParentNotReturnAmt(RiskGeneralOrderOverdueDO parentDO, Integer term) {
        BigDecimal amt = BigDecimal.ZERO;
        if (term == 1) {
            amt = parentDO.getTerm1();
        } else if (term == 2) {
            amt = parentDO.getTerm2();
        } else if (term == 3) {
            amt = parentDO.getTerm3();
        } else if (term == 4) {
            amt = parentDO.getTerm4();
        } else if (term == 5) {
            amt = parentDO.getTerm5();
        } else if (term == 6) {
            amt = parentDO.getTerm6();
        } else if (term == 7) {
            amt = parentDO.getTerm7();
        } else if (term == 8) {
            amt = parentDO.getTerm8();
        } else if (term == 9) {
            amt = parentDO.getTerm9();
        } else if (term == 10) {
            amt = parentDO.getTerm10();
        } else if (term == 11) {
            amt = parentDO.getTerm11();
        } else if (term == 12) {
            amt = parentDO.getTerm12();
        }
        else if (term == 13) {
            amt = parentDO.getTerm13();
        }
        else if (term == 14) {
            amt = parentDO.getTerm14();
        }
        else if (term == 15) {
            amt = parentDO.getTerm15();
        }
        else if (term == 16) {
            amt = parentDO.getTerm16();
        }
        else if (term == 17) {
            amt = parentDO.getTerm17();
        }
        else if (term == 18) {
            amt = parentDO.getTerm18();
        }
        else if (term == 19) {
            amt = parentDO.getTerm19();
        }
        else if (term == 20) {
            amt = parentDO.getTerm20();
        }
        else if (term == 21) {
            amt = parentDO.getTerm21();
        }
        else if (term == 22) {
            amt = parentDO.getTerm22();
        }
        else if (term == 23) {
            amt = parentDO.getTerm23();
        }
        else if (term == 24) {
            amt = parentDO.getTerm24();
        }
        else if (term == 25) {
            amt = parentDO.getTerm25();
        }
        else if (term == 26) {
            amt = parentDO.getTerm26();
        }
        else if (term == 27) {
            amt = parentDO.getTerm27();
        }
        else if (term == 28) {
            amt = parentDO.getTerm28();
        }
        else if (term == 29) {
            amt = parentDO.getTerm29();
        }
        else if (term == 30) {
            amt = parentDO.getTerm30();
        }
        return amt;
    }


    private BigDecimal stringToDecimal(String val) {
        if (null == val || "\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }


    /**
     * 递归查找，最早的的逾期期数
     *
     * @param map term为key，还款计划为val
     * @param
     */
    public static void fetchTerm(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        int level = (int) resMap.get("level");
        List<RepaymentPlanDO> planDOList = map.get(key);
        RepaymentPlanDO planDO = planDOList.get(0);
        if (planDOList.size() > 1) {
            for (RepaymentPlanDO repaymentPlanDO : planDOList) {
                if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                    planDO = repaymentPlanDO;
                }
            }
        }
        if (planDO != null) {
            String isOverdue = planDO.getIsOverdue();
            LocalDateTime realRepayTime = planDO.getRealRepayTime();
            Integer repayStatus = planDO.getRepayStatus();
            Integer overdue = planDO.getOverdue();
            BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));
            //2024-10-11号修改
//            LocalDate localDate_endtime = LocalDate.from(planDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(3));
//            resMap.put("endTime",localDate_endtime);

            LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
                    dateFormatter), LocalTime.MIN);
            LocalDateTime maxRepayDate = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("maxRepayDate")),
                    dateFormatter), LocalTime.MAX);

            LocalDateTime currentTime = LocalDateTime.now();
            //还款日期在本期封账之前还的就截止
            // "还款日期" >= "本期封账"
            if ("1".equals(isOverdue)) {
                //先判断本期的状态是否已逾期未还款
                if (repayStatus == 1 && overdue == 5) {
                    if (term.compareTo(new BigDecimal(1)) == 0) {
                        resMap.put("term", term);
                    } else {
                        // 递归判断判断上一期的订单是否逾期
                        term = term.subtract(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                        fetchTerm(map, resMap);
                    }
                } else {
                    // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
                    // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
                    // 再递归往上判断
                    if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
                        if (term.compareTo(new BigDecimal(1)) == 0) {
                            resMap.put("term", term);
                        } else {
                            term = term.subtract(BigDecimal.valueOf(1));
                            resMap.put("term", term);
                            fetchTerm(map, resMap);
                        }
                    } else {
                        term = term.add(BigDecimal.valueOf(1));
                        resMap.put("term", term);
                    }
                }

            } else {
                term = term.add(BigDecimal.valueOf(1));
                resMap.put("term", term);
            }

        }
    }

    public static void fetchCapital(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
        int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        LocalDateTime maxRepayDate = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("maxRepayDate")),
                dateFormatter), LocalTime.MAX);
        resMap.put("level", 1);
        //递归遍历,找到首个逾期的期数
        if (sourceTerm > 1) {
            fetchTerm(map, resMap);
        }
        int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
        for (Map.Entry<Integer, List<RepaymentPlanDO>> entry : map.entrySet()) {
            List<RepaymentPlanDO> value = entry.getValue();
            RepaymentPlanDO planDO = value.get(0);
            if (value.size() > 1) {
                for (RepaymentPlanDO repaymentPlanDO : value) {
                    if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
                        planDO = repaymentPlanDO;
                    }
                }
            }
            if (entry.getKey() >= term) {
                BigDecimal capital;
                if (term == entry.getKey()) {
                    if (planDO.getRealRepayTime() == null) {
                        capital = planDO.getCapital();
                        // 实际还款时间大于最大封账日,那么这一期将当没还
                    } else if (planDO.getRealRepayTime() != null && planDO.getRealRepayTime().compareTo(maxRepayDate) > 0) {
                        capital = planDO.getCapital();
                    } else {
                        capital = planDO.getRealCapital();
                    }
                } else {
                    capital = planDO.getCapital();
                }
               // log.info("**********fetchCapital****resMap.capital*******"+capital+"****期数="+term);
                resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
            }
        }
    }


    private void setOverdueVal(RiskRenewalOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        } else if (term == 13) {
            riskOrderOverdueDO.setIsOverdue13(isOverdue);
            riskOrderOverdueDO.setOverdueFine13(overdueFine);
        } else if (term == 14) {
            riskOrderOverdueDO.setIsOverdue14(isOverdue);
            riskOrderOverdueDO.setOverdueFine14(overdueFine);
        } else if (term == 15) {
            riskOrderOverdueDO.setIsOverdue15(isOverdue);
            riskOrderOverdueDO.setOverdueFine15(overdueFine);
        } else if (term == 16) {
            riskOrderOverdueDO.setIsOverdue16(isOverdue);
            riskOrderOverdueDO.setOverdueFine16(overdueFine);
        } else if (term == 17) {
            riskOrderOverdueDO.setIsOverdue17(isOverdue);
            riskOrderOverdueDO.setOverdueFine17(overdueFine);
        } else if (term == 18) {
            riskOrderOverdueDO.setIsOverdue18(isOverdue);
            riskOrderOverdueDO.setOverdueFine18(overdueFine);
        }
    }

    private void setOverdueVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        } else if (term == 13) {
            riskOrderOverdueDO.setIsOverdue13(isOverdue);
            riskOrderOverdueDO.setOverdueFine13(overdueFine);
        } else if (term == 14) {
            riskOrderOverdueDO.setIsOverdue14(isOverdue);
            riskOrderOverdueDO.setOverdueFine14(overdueFine);
        } else if (term == 15) {
            riskOrderOverdueDO.setIsOverdue15(isOverdue);
            riskOrderOverdueDO.setOverdueFine15(overdueFine);
        } else if (term == 16) {
            riskOrderOverdueDO.setIsOverdue16(isOverdue);
            riskOrderOverdueDO.setOverdueFine16(overdueFine);
        } else if (term == 17) {
            riskOrderOverdueDO.setIsOverdue17(isOverdue);
            riskOrderOverdueDO.setOverdueFine17(overdueFine);
        } else if (term == 18) {
            riskOrderOverdueDO.setIsOverdue18(isOverdue);
            riskOrderOverdueDO.setOverdueFine18(overdueFine);
        }
        else if (term == 19) {
            riskOrderOverdueDO.setIsOverdue19(isOverdue);
            riskOrderOverdueDO.setOverdueFine19(overdueFine);
        }
        else if (term == 20) {
            riskOrderOverdueDO.setIsOverdue20(isOverdue);
            riskOrderOverdueDO.setOverdueFine20(overdueFine);
        }
        else if (term == 21) {
            riskOrderOverdueDO.setIsOverdue21(isOverdue);
            riskOrderOverdueDO.setOverdueFine21(overdueFine);
        }
        else if (term == 22) {
            riskOrderOverdueDO.setIsOverdue22(isOverdue);
            riskOrderOverdueDO.setOverdueFine22(overdueFine);
        }
        else if (term == 23) {
            riskOrderOverdueDO.setIsOverdue23(isOverdue);
            riskOrderOverdueDO.setOverdueFine23(overdueFine);
        }
        else if (term == 24) {
            riskOrderOverdueDO.setIsOverdue24(isOverdue);
            riskOrderOverdueDO.setOverdueFine24(overdueFine);
        }
        else if (term == 25) {
            riskOrderOverdueDO.setIsOverdue25(isOverdue);
            riskOrderOverdueDO.setOverdueFine25(overdueFine);
        }
        else if (term == 26) {
            riskOrderOverdueDO.setIsOverdue26(isOverdue);
            riskOrderOverdueDO.setOverdueFine26(overdueFine);
        }
        else if (term == 27) {
            riskOrderOverdueDO.setIsOverdue27(isOverdue);
            riskOrderOverdueDO.setOverdueFine27(overdueFine);
        }
        else if (term == 28) {
            riskOrderOverdueDO.setIsOverdue28(isOverdue);
            riskOrderOverdueDO.setOverdueFine28(overdueFine);
        }
        else if (term == 29) {
            riskOrderOverdueDO.setIsOverdue29(isOverdue);
            riskOrderOverdueDO.setOverdueFine29(overdueFine);
        }
        else if (term == 30) {
            riskOrderOverdueDO.setIsOverdue30(isOverdue);
            riskOrderOverdueDO.setOverdueFine30(overdueFine);
        }
    }

    private void setOverdueVal(RiskDistributionOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
                               BigDecimal overdueFine) {
        if (term == 1) {
            riskOrderOverdueDO.setIsOverdue1(isOverdue);
            riskOrderOverdueDO.setOverdueFine1(overdueFine);
        } else if (term == 2) {
            riskOrderOverdueDO.setIsOverdue2(isOverdue);
            riskOrderOverdueDO.setOverdueFine2(overdueFine);
        } else if (term == 3) {
            riskOrderOverdueDO.setIsOverdue3(isOverdue);
            riskOrderOverdueDO.setOverdueFine3(overdueFine);
        } else if (term == 4) {
            riskOrderOverdueDO.setIsOverdue4(isOverdue);
            riskOrderOverdueDO.setOverdueFine4(overdueFine);
        } else if (term == 5) {
            riskOrderOverdueDO.setIsOverdue5(isOverdue);
            riskOrderOverdueDO.setOverdueFine5(overdueFine);
        } else if (term == 6) {
            riskOrderOverdueDO.setIsOverdue6(isOverdue);
            riskOrderOverdueDO.setOverdueFine6(overdueFine);
        } else if (term == 7) {
            riskOrderOverdueDO.setIsOverdue7(isOverdue);
            riskOrderOverdueDO.setOverdueFine7(overdueFine);
        } else if (term == 8) {
            riskOrderOverdueDO.setIsOverdue8(isOverdue);
            riskOrderOverdueDO.setOverdueFine8(overdueFine);
        } else if (term == 9) {
            riskOrderOverdueDO.setIsOverdue9(isOverdue);
            riskOrderOverdueDO.setOverdueFine9(overdueFine);
        } else if (term == 10) {
            riskOrderOverdueDO.setIsOverdue10(isOverdue);
            riskOrderOverdueDO.setOverdueFine10(overdueFine);
        } else if (term == 11) {
            riskOrderOverdueDO.setIsOverdue11(isOverdue);
            riskOrderOverdueDO.setOverdueFine11(overdueFine);
        } else if (term == 12) {
            riskOrderOverdueDO.setIsOverdue12(isOverdue);
            riskOrderOverdueDO.setOverdueFine12(overdueFine);
        } else if (term == 13) {
            riskOrderOverdueDO.setIsOverdue13(isOverdue);
            riskOrderOverdueDO.setOverdueFine13(overdueFine);
        } else if (term == 14) {
            riskOrderOverdueDO.setIsOverdue14(isOverdue);
            riskOrderOverdueDO.setOverdueFine14(overdueFine);
        } else if (term == 15) {
            riskOrderOverdueDO.setIsOverdue15(isOverdue);
            riskOrderOverdueDO.setOverdueFine15(overdueFine);
        } else if (term == 16) {
            riskOrderOverdueDO.setIsOverdue16(isOverdue);
            riskOrderOverdueDO.setOverdueFine16(overdueFine);
        } else if (term == 17) {
            riskOrderOverdueDO.setIsOverdue17(isOverdue);
            riskOrderOverdueDO.setOverdueFine17(overdueFine);
        } else if (term == 18) {
            riskOrderOverdueDO.setIsOverdue18(isOverdue);
            riskOrderOverdueDO.setOverdueFine18(overdueFine);
        }
        else if (term == 19) {
            riskOrderOverdueDO.setIsOverdue19(isOverdue);
            riskOrderOverdueDO.setOverdueFine19(overdueFine);
        }
        else if (term == 20) {
            riskOrderOverdueDO.setIsOverdue20(isOverdue);
            riskOrderOverdueDO.setOverdueFine20(overdueFine);
        }
        else if (term == 21) {
            riskOrderOverdueDO.setIsOverdue21(isOverdue);
            riskOrderOverdueDO.setOverdueFine21(overdueFine);
        }
        else if (term == 22) {
            riskOrderOverdueDO.setIsOverdue22(isOverdue);
            riskOrderOverdueDO.setOverdueFine22(overdueFine);
        }
        else if (term == 23) {
            riskOrderOverdueDO.setIsOverdue23(isOverdue);
            riskOrderOverdueDO.setOverdueFine23(overdueFine);
        }
        else if (term == 24) {
            riskOrderOverdueDO.setIsOverdue24(isOverdue);
            riskOrderOverdueDO.setOverdueFine24(overdueFine);
        }
        else if (term == 25) {
            riskOrderOverdueDO.setIsOverdue25(isOverdue);
            riskOrderOverdueDO.setOverdueFine25(overdueFine);
        }
        else if (term == 26) {
            riskOrderOverdueDO.setIsOverdue26(isOverdue);
            riskOrderOverdueDO.setOverdueFine26(overdueFine);
        }
        else if (term == 27) {
            riskOrderOverdueDO.setIsOverdue27(isOverdue);
            riskOrderOverdueDO.setOverdueFine27(overdueFine);
        }
        else if (term == 28) {
            riskOrderOverdueDO.setIsOverdue28(isOverdue);
            riskOrderOverdueDO.setOverdueFine28(overdueFine);
        }
        else if (term == 29) {
            riskOrderOverdueDO.setIsOverdue29(isOverdue);
            riskOrderOverdueDO.setOverdueFine29(overdueFine);
        }
        else if (term == 30) {
            riskOrderOverdueDO.setIsOverdue30(isOverdue);
            riskOrderOverdueDO.setOverdueFine30(overdueFine);
        }
    }


    private void setTermNotRepayVal(RiskRenewalOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setTerm13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setTerm14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setTerm15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setTerm16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setTerm17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setTerm18(decimal);
        }

    }

    private void setTermNotRepayVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {

        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setTerm13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setTerm14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setTerm15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setTerm16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setTerm17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setTerm18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setTerm19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setTerm20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setTerm21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setTerm22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setTerm23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setTerm24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setTerm25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setTerm26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setTerm27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setTerm28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setTerm29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setTerm30(decimal);
        }

    }

    private void setTermNotRepayVal(RiskDistributionOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {

        if (term == 1) {
            riskOrderOverdueDO.setTerm1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setTerm2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setTerm3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setTerm4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setTerm5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setTerm6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setTerm7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setTerm8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setTerm9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setTerm10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setTerm11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setTerm12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setTerm13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setTerm14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setTerm15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setTerm16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setTerm17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setTerm18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setTerm19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setTerm20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setTerm21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setTerm22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setTerm23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setTerm24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setTerm25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setTerm26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setTerm27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setTerm28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setTerm29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setTerm30(decimal);
        }

    }

    private void setRenewAmtVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setRenewAmt1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setRenewAmt2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setRenewAmt3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setRenewAmt4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setRenewAmt5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setRenewAmt6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setRenewAmt7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setRenewAmt8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setRenewAmt9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setRenewAmt10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setRenewAmt11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setRenewAmt12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setRenewAmt13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setRenewAmt14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setRenewAmt15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setRenewAmt16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setRenewAmt17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setRenewAmt18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setRenewAmt19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setRenewAmt20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setRenewAmt21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setRenewAmt22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setRenewAmt23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setRenewAmt24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setRenewAmt25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setRenewAmt26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setRenewAmt27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setRenewAmt28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setRenewAmt29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setRenewAmt30(decimal);
        }

    }

    private void setRenewAmtVal(RiskDistributionOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
        if (term == 1) {
            riskOrderOverdueDO.setRenewAmt1(decimal);
        } else if (term == 2) {
            riskOrderOverdueDO.setRenewAmt2(decimal);
        } else if (term == 3) {
            riskOrderOverdueDO.setRenewAmt3(decimal);
        } else if (term == 4) {
            riskOrderOverdueDO.setRenewAmt4(decimal);
        } else if (term == 5) {
            riskOrderOverdueDO.setRenewAmt5(decimal);
        } else if (term == 6) {
            riskOrderOverdueDO.setRenewAmt6(decimal);
        } else if (term == 7) {
            riskOrderOverdueDO.setRenewAmt7(decimal);
        } else if (term == 8) {
            riskOrderOverdueDO.setRenewAmt8(decimal);
        } else if (term == 9) {
            riskOrderOverdueDO.setRenewAmt9(decimal);
        } else if (term == 10) {
            riskOrderOverdueDO.setRenewAmt10(decimal);
        } else if (term == 11) {
            riskOrderOverdueDO.setRenewAmt11(decimal);
        } else if (term == 12) {
            riskOrderOverdueDO.setRenewAmt12(decimal);
        } else if (term == 13) {
            riskOrderOverdueDO.setRenewAmt13(decimal);
        } else if (term == 14) {
            riskOrderOverdueDO.setRenewAmt14(decimal);
        } else if (term == 15) {
            riskOrderOverdueDO.setRenewAmt15(decimal);
        } else if (term == 16) {
            riskOrderOverdueDO.setRenewAmt16(decimal);
        } else if (term == 17) {
            riskOrderOverdueDO.setRenewAmt17(decimal);
        } else if (term == 18) {
            riskOrderOverdueDO.setRenewAmt18(decimal);
        } else if (term == 19) {
            riskOrderOverdueDO.setRenewAmt19(decimal);
        } else if (term == 20) {
            riskOrderOverdueDO.setRenewAmt20(decimal);
        } else if (term == 21) {
            riskOrderOverdueDO.setRenewAmt21(decimal);
        } else if (term == 22) {
            riskOrderOverdueDO.setRenewAmt22(decimal);
        } else if (term == 23) {
            riskOrderOverdueDO.setRenewAmt23(decimal);
        } else if (term == 24) {
            riskOrderOverdueDO.setRenewAmt24(decimal);
        } else if (term == 25) {
            riskOrderOverdueDO.setRenewAmt25(decimal);
        } else if (term == 26) {
            riskOrderOverdueDO.setRenewAmt26(decimal);
        } else if (term == 27) {
            riskOrderOverdueDO.setRenewAmt27(decimal);
        } else if (term == 28) {
            riskOrderOverdueDO.setRenewAmt28(decimal);
        } else if (term == 29) {
            riskOrderOverdueDO.setRenewAmt29(decimal);
        } else if (term == 30) {
            riskOrderOverdueDO.setRenewAmt30(decimal);
        }

    }

}


