package qnvip.data.overview.business.realTime;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperateAccessSciDO;
import qnvip.data.overview.domain.realTime.OperateRealTimeDO;
import qnvip.data.overview.service.realTime.OperateRealTimeService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateRealTimeBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateRealTimeService realTimeService;

    void initMap(Map<Integer, OperateRealTimeDO> scene2Do, OperateRealTimeDO sourceDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!scene2Do.containsKey(sourceDO.getMiniType())) {
            OperateRealTimeDO domain = new OperateRealTimeDO();
            domain.setCountDay(countDay);
            domain.setMiniType(sourceDO.getMiniType());
            scene2Do.put(sourceDO.getMiniType(), domain);
        }

    }


    /**
     * 定时调度任务
     */
    public void runCore() {

        try {
            Map<Integer, OperateRealTimeDO> scene2Do = new HashMap<>();

            CompletableFuture<List<OperateRealTimeDO>> f1 = CompletableFuture.supplyAsync(this::get30DAUS);
            CompletableFuture<List<OperateRealTimeDO>> f2 = CompletableFuture.supplyAsync(this::getThisMonthAgoGmvData);
            CompletableFuture.allOf(f1,f2).join();
            // 上个月
            getDaus(scene2Do, f1);
            getGmv(scene2Do, f2);
            // 写入mysql
            Collection<OperateRealTimeDO> values = scene2Do.values();
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            realTimeService.removeDataByTime(countDay);
            realTimeService.saveBatch(values);

        } catch (Exception e) {
            log.error("OperateRealTimeBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void getDaus(Map<Integer, OperateRealTimeDO> scene2Do, CompletableFuture<List<OperateRealTimeDO>> future) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateRealTimeDO coreDO : future.get()) {
            initMap(scene2Do, coreDO);
            OperateRealTimeDO domain = scene2Do.get(coreDO.getMiniType());
            domain.setDausThirty(coreDO.getDausThirty());
        }
    }
    private void getGmv(Map<Integer, OperateRealTimeDO> scene2Do, CompletableFuture<List<OperateRealTimeDO>> future) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateRealTimeDO coreDO : future.get()) {
            initMap(scene2Do, coreDO);
            OperateRealTimeDO domain = scene2Do.get(coreDO.getMiniType());
            domain.setGmvThirty(coreDO.getGmvThirty());
            domain.setPayThirty(coreDO.getPayThirty());
        }
    }
    /**
     * 获取30日活跃人数
     */
    private List<OperateRealTimeDO> get30DAUS() {
        String sql = "select mini_type,count(distinct a.customer_third_id)                      tol_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null))       keep_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null)) / count(distinct a.customer_third_id) keep_rate" +
                " from (select count(distinct date (report_time)) aa, customer_third_id,mini_type" +
                "      from dataview_track_enter_applets" +
                "      where ds = to_char(getdate(), 'yyyymmdd')" +
                "        and date (report_time) between dateadd(to_date(${fromTime}),-29,'dd') and dateadd(to_date(${fromTime})" +
                " ,0,'dd')" +
                "        and mini_type in (1, 2, 4, 5, 7)" +
                "        and action_type = 1" +
                " group by customer_third_id ,mini_type) a" +
                "   group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(record -> {
            OperateRealTimeDO domain = new OperateRealTimeDO();
            String tolUsrCnt = record.getString("tol_usr_cnt");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setDausThirty(Long.valueOf(tolUsrCnt));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取上上个月支付信息
     */
    private List<OperateRealTimeDO> getThisMonthAgoGmvData() {
        String sql = "select count(distinct out_trade_no) pay_cnt," +
                "       sum(pay_amt) gmv," +
                "       mini_type " +
                " from rent_alipay_order_pay" +
                " where is_deleted = 0" +
                " and mini_type in (1, 2, 4, 5, 7)" +
                " and status = 10" +
                " and biz_type in (1,4)" +
                " and pay_type in (1,4,5)" +
                " and ds = to_char(getdate(), 'yyyymmdd')" +
                " and date(create_time) between dateadd(to_date(${fromTime}),-29,'dd') and dateadd(to_date(${fromTime}),0 ," +
                " 'dd')" +
                "  group by mini_type";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(record -> {
            OperateRealTimeDO domain = new OperateRealTimeDO();
            String payCnt = record.getString("pay_cnt");
            String gmv = record.getString("gmv");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayThirty(Long.valueOf(payCnt));
            domain.setGmvThirty(new BigDecimal(gmv));
            return domain;
        }).collect(Collectors.toList());
    }
}
