package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.WholeLinkPayDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkPayService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkPayBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkPayService wholeLinkpayService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkPayBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkPayDO> miniType2Map, WholeLinkPayDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkPayDO ac = new WholeLinkPayDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkPayDO> miniType2Map = new HashMap<>();

            CompletableFuture<List<WholeLinkPayDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getPayByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f2 =
                    CompletableFuture.supplyAsync(() -> getPay(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getunPayByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getunPay(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f9 =
                    CompletableFuture.supplyAsync(() -> getPayCloseUvByMIniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkPayDO>> f10 =
                    CompletableFuture.supplyAsync(() -> getPayCloseUv(ds, hour), threadPoolExecutor);
            CompletableFuture.allOf(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10).join();

            assembleValue(miniType2Map, f1.get(), "payUv", "otherPayUv", "wechatPayUv", "aliPayUv", "t0PayUv",
                    "t1PayUv", "t2PayUv");
            assembleValue(miniType2Map, f2.get(), "payUv", "otherPayUv", "wechatPayUv", "aliPayUv", "t0PayUv",
                    "t1PayUv", "t2PayUv");
            assembleValue(miniType2Map, f3.get(), "t0UnpaidUv", "t1UnpaidUv", "t2UnpaidUv");
            assembleValue(miniType2Map, f4.get(), "t0UnpaidUv", "t1UnpaidUv", "t2UnpaidUv");
            assembleValue(miniType2Map, f5.get(), "payTop5ChannelUv");
            assembleValue(miniType2Map, f6.get(), "payTop5ChannelUv");
            assembleValue(miniType2Map, f7.get(), "payTop5SceneUv");
            assembleValue(miniType2Map, f8.get(), "payTop5SceneUv");
            assembleValue(miniType2Map, f9.get(), "payCloseUv");
            assembleValue(miniType2Map, f10.get(), "payCloseUv");

            List<WholeLinkPayDO> list = Lists.newArrayList(miniType2Map.values());
            LocalDate now = LocalDate.now();
            wholeLinkpayService.removeByHour(hour, now);
            wholeLinkpayService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkPayDO> miniType2Map,
                               List<WholeLinkPayDO> list,
                               String... fields) throws Exception {

        for (WholeLinkPayDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkPayDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 获取支付信息
     */
    private List<WholeLinkPayDO> getPayByMiniType(String ds, Integer hour) {
        String sql = "select ro.mini_type," +
                "          date(ro.payment_time)                                                                   time," +
                "          count(distinct ro.customer_id)                                                          pay_uv," +
                "          count(distinct if(rmc.platform_code = 'ALIPAY', ro.customer_id, null))                  ali_pay_uv," +
                "          count(distinct if(rmc.platform_code = 'WECHAT', ro.customer_id, null))                  wechat_pay_uv," +
                "          count(distinct if(rmc.platform_code not in ('WECHAT', 'ALIPAY')" +
                "              , ro.customer_id, null))                                                            other_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) = 0, ro.customer_id, null)) t0_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) = 1, ro.customer_id, null)) t1_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) > 1, ro.customer_id, null)) t2_pay_uv" +
                "   from rent_order ro" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds =" + ds +
                "     and date(ro.payment_time) = date(getdate())" +
                "   group by date(ro.payment_time),ro. mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long payUv = Long.parseLong(record.getString("pay_uv"));
            Integer miniType = Integer.parseInt(record.getString("mini_type"));
            Long aliPayUv = Long.parseLong(record.getString("ali_pay_uv"));
            Long wechatPayUv = Long.parseLong(record.getString("wechat_pay_uv"));
            Long otherPayUv = Long.parseLong(record.getString("other_pay_uv"));
            Long t0PayUv = Long.parseLong(record.getString("t0_pay_uv"));
            Long t1PayUv = Long.parseLong(record.getString("t1_pay_uv"));
            Long t2PayUv = Long.parseLong(record.getString("t2_pay_uv"));
            domain.setPayUv(payUv);
            domain.setAliPayUv(aliPayUv);
            domain.setWechatPayUv(wechatPayUv);
            domain.setOtherPayUv(otherPayUv);
            domain.setT0PayUv(t0PayUv);
            domain.setMiniType(miniType);
            domain.setT1PayUv(t1PayUv);
            domain.setT2PayUv(t2PayUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取支付信息
     */
    private List<WholeLinkPayDO> getPay(String ds, Integer hour) {
        String sql = "select " +
                "          date(ro.payment_time)                                                                   time," +
                "          count(distinct ro.customer_id)                                                          pay_uv," +
                "          count(distinct if(rmc.platform_code = 'ALIPAY', ro.customer_id, null))                  ali_pay_uv," +
                "          count(distinct if(rmc.platform_code = 'WECHAT', ro.customer_id, null))                  wechat_pay_uv," +
                "          count(distinct if(rmc.platform_code not in ('WECHAT', 'ALIPAY')" +
                "              , ro.customer_id, null))                                                            other_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) = 0, ro.customer_id, null)) t0_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) = 1, ro.customer_id, null)) t1_pay_uv," +
                "          count(distinct if(datediff(ro.payment_time, ro.create_time) > 1, ro.customer_id, null)) t2_pay_uv" +
                "   from rent_order ro" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds =" + ds +
                "     and date(ro.payment_time) = date(getdate())" +
                "   group by date(ro.payment_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long payUv = Long.parseLong(record.getString("pay_uv"));
            Long aliPayUv = Long.parseLong(record.getString("ali_pay_uv"));
            Long wechatPayUv = Long.parseLong(record.getString("wechat_pay_uv"));
            Long otherPayUv = Long.parseLong(record.getString("other_pay_uv"));
            Long t0PayUv = Long.parseLong(record.getString("t0_pay_uv"));
            Long t1PayUv = Long.parseLong(record.getString("t1_pay_uv"));
            Long t2PayUv = Long.parseLong(record.getString("t2_pay_uv"));
            domain.setPayUv(payUv);
            domain.setAliPayUv(aliPayUv);
            domain.setWechatPayUv(wechatPayUv);
            domain.setOtherPayUv(otherPayUv);
            domain.setT0PayUv(t0PayUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setT1PayUv(t1PayUv);
            domain.setT2PayUv(t2PayUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 待支付
     */
    private List<WholeLinkPayDO> getunPayByMiniType(String ds, Integer hour) {
        String sql = "select " +
                "          mini_type," +
                "          count(distinct" +
                "                if(date(roa.operate_time) = date(getdate()), ro.customer_id, null)) t0_unpaid_uv," +
                "          count(distinct" +
                "                if(date(roa.operate_time) = dateadd(date(getdate()), -1, 'dd')," +
                "                   ro.customer_id, null))                                     t1_unpaid_uv," +
                "          count(distinct" +
                "                if(date(roa.operate_time) <  dateadd(date(getdate()), -2, 'dd')," +
                "                   ro.customer_id, null))                                     t2_unpaid_uv" +
                "   from rent_order ro" +
                "            left join rent_order_audit roa on ro.id = roa.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds =" + ds +
                "     and roa.ds =" + ds +
                "     and ro.payment_time is null" +
                "     and roa.audit_status = 1" +
                "     and roa.type = 2" +
                "     and ro.termination = 1" +
                "   group by  mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long t0UnpaidUv = Long.parseLong(record.getString("t0_unpaid_uv"));
            Long t1UnpaidUv = Long.parseLong(record.getString("t1_unpaid_uv"));
            Long t2UnpaidUv = Long.parseLong(record.getString("t2_unpaid_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setT0UnpaidUv(t0UnpaidUv);
            domain.setT1UnpaidUv(t1UnpaidUv);
            domain.setT2UnpaidUv(t2UnpaidUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 待支付
     */
    private List<WholeLinkPayDO> getunPay(String ds, Integer hour) {
        String sql = "select " +
                "          count(distinct" +
                "                if(date(roa.operate_time) = date(getdate()), ro.customer_id, null)) t0_unpaid_uv," +
                "          count(distinct" +
                "                if(date(roa.operate_time) = dateadd(date(getdate()), -1, 'dd')," +
                "                   ro.customer_id, null))                                     t1_unpaid_uv," +
                "          count(distinct" +
                "                if(date(roa.operate_time) <  dateadd(date(getdate()), -2, 'dd')," +
                "                   ro.customer_id, null))                                     t2_unpaid_uv" +
                "   from rent_order ro" +
                "            left join rent_order_audit roa on ro.id = roa.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds =" + ds +
                "     and roa.ds =" + ds +
                "     and ro.payment_time is null" +
                "     and roa.audit_status = 1" +
                "     and roa.type = 2" +
                "     and ro.termination = 1;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long t0UnpaidUv = Long.parseLong(record.getString("t0_unpaid_uv"));
            Long t1UnpaidUv = Long.parseLong(record.getString("t1_unpaid_uv"));
            Long t2UnpaidUv = Long.parseLong(record.getString("t2_unpaid_uv"));
            domain.setT0UnpaidUv(t0UnpaidUv);
            domain.setT1UnpaidUv(t1UnpaidUv);
            domain.setT2UnpaidUv(t2UnpaidUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkPayDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.payment_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(ro.payment_time) time ,ro.mini_type, COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and date(ro.payment_time) = date(getdate())" +
                "     and hour(ro.payment_time) <= " + hour +
                "   GROUP BY date(ro.payment_time),ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setPayTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkPayDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.payment_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(ro.payment_time) time , COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and date(ro.payment_time) = date(getdate())" +
                "     and hour(ro.payment_time) <= " + hour +
                "   GROUP BY date(ro.payment_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setPayTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkPayDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.payment_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 5)" +
                "   SELECT date(ro.payment_time) time ,ro.mini_type, COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(ro.payment_time) = date(getdate())" +
                "     and hour(ro.payment_time) <= " + hour +
                "   GROUP BY date(ro.payment_time),ro.mini_type" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setPayTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkPayDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and date(ro.payment_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 5)" +
                "   SELECT date(ro.payment_time) time ,COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(ro.payment_time) = date(getdate())" +
                "     and hour(ro.payment_time) <= " + hour +
                "   GROUP BY date(ro.payment_time)" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            domain.setPayTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 已支付关闭
     */
    private List<WholeLinkPayDO> getPayCloseUvByMIniType(String ds, Integer hour) {
        String sql = "select date(roi.termination_time) time," +
                "          ro.mini_type," +
                "          count(distinct ro.customer_id) pay_close_uv" +
                "   from rent_order ro" +
                "            left join rent_order_infomore roi on ro.id = roi.order_id" +
                "   where ro.is_deleted = 0" +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     " +
                "     and ro.ds =" + ds +
                "     and roi.ds =" + ds +
                "     and ro.payment_time is not null" +
                "     and date(roi.termination_time) = date(getdate())" +
                "   group by date(roi.termination_time),ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long payCloseUv = stringToLong(record.getString("pay_close_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setPayCloseUv(payCloseUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 已支付关闭
     */
    private List<WholeLinkPayDO> getPayCloseUv(String ds, Integer hour) {
        String sql = "select date(roi.termination_time) time," +
                "          count(distinct ro.customer_id) pay_close_uv" +
                "   from rent_order ro" +
                "            left join rent_order_infomore roi on ro.id = roi.order_id" +
                "   where ro.is_deleted = 0" +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     " +
                "     and ro.ds =" + ds +
                "     and roi.ds =" + ds +
                "     and ro.payment_time is not null" +
                "     and date(roi.termination_time) = date(getdate())" +
                "   group by date(roi.termination_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkPayDO domain = new WholeLinkPayDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long payCloseUv = stringToLong(record.getString("pay_close_uv"));
            domain.setPayCloseUv(payCloseUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    private Long stringToLong(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return Long.parseLong(val);
    }
}



