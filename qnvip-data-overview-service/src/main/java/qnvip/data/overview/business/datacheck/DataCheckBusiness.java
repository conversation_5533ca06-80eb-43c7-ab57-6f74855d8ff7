package qnvip.data.overview.business.datacheck;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;
import qnvip.data.overview.business.finance.FinanceReportBusiness;
import qnvip.data.overview.domain.datacheck.DataCheckAfterRentDO;
import qnvip.data.overview.domain.datacheck.DataCheckDeliveryDO;
import qnvip.data.overview.domain.datacheck.DataCheckRentDO;
import qnvip.data.overview.domain.datacheck.DataCheckRepayDO;
import qnvip.data.overview.dto.OARecoverStockInfoDTO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.datacheck.DataCheckAfterRentService;
import qnvip.data.overview.service.datacheck.DataCheckDeliveryService;
import qnvip.data.overview.service.datacheck.DataCheckRentService;
import qnvip.data.overview.service.datacheck.DataCheckRepayService;
import qnvip.data.overview.service.risk.RiskOverdueBaseService;
import qnvip.data.overview.service.risk.RiskRenewOverdueBaseService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DataCheckBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final String DELIVERY_STATUS = "15, 30, 200, 210, 220, 310, 320, 330";
    private static final String GET_STOCK_INFO_URL = "http://supplier.qnvipmall.com/api/index/getStock";
    private static final Integer PAGE_SIZE = 9000;

    private final OdpsUtil odpsUtil;
    private final DataCheckRentService dataCheckRentService;
    private final DataCheckRepayService dataCheckRepayService;
    private final DataCheckDeliveryService dataCheckDeliveryService;
    private final DataCheckAfterRentService dataCheckAfterRentService;
    private final RiskOverdueBaseService riskOverdueBaseService;
    private final RiskRenewOverdueBaseService renewOverdueBaseService;


    private final FinanceReportBusiness financeReportBusiness;

    private final RestTemplate restTemplate;

    public void execCount(String ds) {
        String testerIds = financeReportBusiness.getTesterIds(ds);
        CompletableFuture.runAsync(() -> {
            try {
                Integer size = getDeliveryTaskCount(ds, testerIds);
                dataCheckDeliveryService.deleteAll();

                int times = size / PAGE_SIZE;
                if (size % PAGE_SIZE != 0) {
                    times += 1;
                }
                int startPage = 0;
                for (int i = 0; i < times; i++) {
                    countDeliveryTask(ds, testerIds, startPage);
                    startPage++;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        CompletableFuture.runAsync(() -> {
            try {
                Integer size = getRentTaskCount(ds, testerIds);
                dataCheckRentService.deleteAll();

                int times = size / PAGE_SIZE;
                if (size % PAGE_SIZE != 0) {
                    times += 1;
                }
                int startPage = 0;
                for (int i = 0; i < times; i++) {
                    countRentTask(ds, testerIds, startPage);
                    startPage++;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        CompletableFuture.runAsync(() -> {
            try {
                Integer size = getAfterRentTaskCount(ds, testerIds);
                dataCheckAfterRentService.deleteAll();

                int times = size / PAGE_SIZE;
                if (size % PAGE_SIZE != 0) {
                    times += 1;
                }
                int startPage = 0;
                for (int i = 0; i < times; i++) {
                    countAfterRentTask(ds, testerIds, startPage);
                    startPage++;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
        CompletableFuture.runAsync(() -> {
            try {
                Integer size = getRepayTaskCount(ds, testerIds);
                dataCheckRepayService.deleteAll();

                int times = size / PAGE_SIZE;
                if (size % PAGE_SIZE != 0) {
                    times += 1;
                }
                int startPage = 0;
                for (int i = 0; i < times; i++) {
                    countRepayTask(ds, testerIds, startPage);
                    startPage++;
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    /**
     * 租后统计
     *
     * @param ds
     */
    private Integer getDeliveryTaskCount(String ds, String testerIds) {
        String sql = " select count(*) num from( select date(e.send_time)                                            " +
                "                    day, " +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')                 forcedconversion, " +
                "       count(1)                                                                         number, " +
                "       sum(b.actual_supply_price) actual_supply_price, " +
                "       sum(if(a.termination = 5, 1, 0)) back_number, " +
                "       sum(if(a.termination = 5, b.actual_supply_price, 0)) back_price, " +
                "       sum(if(e.sign_time is not null, 1, 0)) sign_number ," +
                "       sum(if(e.sign_time is null, 1, 0)) un_sign_number " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                " and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 and c.ds =" +
                ds +
                "         inner join rent_order_logistics e on a.id = e.order_id  and e.ds = " +
                ds +
                " where a.merchant_id = 100 " +
                "  and a.payment_time is not null " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.parent_id = 0 " +
                "  and e.send_time is not null  " +
                "  and termination <> 5" +
                "  and a.customer_id not in " +
                "      (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ") " +
                "  and b.type_class != 1 " +
                "  and a.is_deleted = 0 " +
                "  and a.ds = " + ds +
                " group by date(e.send_time), " +
                "         a.mini_type, " +
                "         c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0') order by date(e.send_time) asc )" +
                "; ";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 发货统计
     *
     * @param ds
     */
    private void countDeliveryTask(String ds, String testerIds, Integer startPage) {
        OARecoverStockInfoDTO body = restTemplate.exchange(GET_STOCK_INFO_URL, HttpMethod.GET, null,
                OARecoverStockInfoDTO.class).getBody();
        List<OARecoverStockInfoDTO.InnerInfo> oaDataList = body.getData();
        String sql = " select date(e.send_time)                                                                day, " +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')                 forcedconversion, " +
                "       count(1)                                                                         number, " +
                "       sum(b.actual_supply_price) actual_supply_price, " +
                "       sum(if(a.termination = 5, 1, 0)) back_number, " +
                "       sum(if(a.termination = 5, b.actual_supply_price, 0)) back_price, " +
                "       sum(if(e.sign_time is not null, 1, 0)) sign_number ," +
                "       sum(if(e.sign_time is null, 1, 0)) un_sign_number " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                " and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 and c.ds =" +
                ds +
                "         inner join rent_order_logistics e on a.id = e.order_id  and e.ds = " +
                ds +
                " where a.merchant_id = 100 " +
                "  and a.payment_time is not null " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.parent_id = 0 " +
                "  and e.send_time is not null  " +
                "  and termination <> 5" +
                "  and a.customer_id not in " +
                "      (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ") " +
                "  and b.type_class != 1 " +
                "  and a.is_deleted = 0 " +
                "  and a.ds = " + ds +
                " group by date(e.send_time), " +
                "         a.mini_type, " +
                "         c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0') " +
                " order by date(e.send_time) asc" +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE +
                ";";
        List<Record> records = odpsUtil.querySql(sql);
        List<DataCheckDeliveryDO> resList = new ArrayList<>();
        records.forEach(k -> {
            String day = k.getString("day");
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(day, dateFormatter), LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            Integer rateConfigType = Integer.valueOf(k.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer number = Integer.valueOf(k.getString("number"));
            BigDecimal actualSupplyPrice = stringToDecimal(k.getString("actual_supply_price"));
            Integer backNumber = Integer.valueOf(k.getString("back_number"));
            BigDecimal backPrice = stringToDecimal(k.getString("back_price"));
            Integer signNumber = Integer.valueOf(k.getString("sign_number"));
            Integer unSignNumber = Integer.valueOf(k.getString("un_sign_number"));

            Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, 0, forcedConversion);

            DataCheckDeliveryDO deliveryDO = new DataCheckDeliveryDO();
            deliveryDO.setCountDay(rentStartDate);
            deliveryDO.setMiniType(miniType);
            deliveryDO.setFinanceType(financeType);
            deliveryDO.setNumber(number);
            deliveryDO.setActSupplyPrice(actualSupplyPrice);
            deliveryDO.setBackNumber(backNumber);
            deliveryDO.setBackActSupplyPrice(backPrice);
            deliveryDO.setSignNumber(signNumber);
            deliveryDO.setUnSignNumber(unSignNumber);
            if (CollUtil.isNotEmpty(oaDataList)) {
                for (OARecoverStockInfoDTO.InnerInfo innerInfo : oaDataList) {
                    if (day.equals(innerInfo.getSendTime())) {
                        deliveryDO.setStockBackNumber(innerInfo.getNumber());
                        deliveryDO.setStockBackActSupplyPrice(innerInfo.getSupplyPrice());
                    }
                }
            }
            resList.add(deliveryDO);
        });
        dataCheckDeliveryService.saveBatch(resList);
    }

    /**
     * 租后统计
     *
     * @param ds
     */
    private Integer getRentTaskCount(String ds, String testerIds) {
        String sql = "select count(*) num from( select date(a.rent_start_date)                                       " +
                "    " +
                "   day," +
                "       a.mini_type," +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0') as           forcedconversion," +
                "       count(1)                                                                number," +
                "       sum(b.actual_supply_price)                                              actual_supply_price," +
                "       sum(if(c.act_bond_amt = 0 and i.act_bond_amt is not null, i.act_bond_amt, c.act_bond_amt)) " +
                "                                                                               bond_amt," +
                "       sum(if(c.rate_config_type!=10,c.actual_financing_amt,d.avg_capital*12)) total_rent," +
                "       sum(if(c.rate_config_type!=10,c.buyout_amt,0))                          buyout_amt," +
                "       sum(d.real_repay_capital)                                               real_repay_capital," +
                "       sum(if(f.current_overdue_days > 0, d.capital, 0))                       overdue_capital," +
                "       sum(d.real_overdue_fine)                                                real_overdue_fine," +
                "       sum(d.reduce_amt)                                                       reduce_amt," +
                "       sum(if(a.status in (15, 310) and i.id is null, 1, 0))                   renting," +
                "       sum(if(a.status in (15, 310) and i.id is not null, 1, 0))               renew_renting," +
                "       sum(if(a.status in (220, 330), 1, 0))                                   buyouted," +
                "       sum(if(i.id is not null, 1, 0))                                         renewing," +
                "       sum(if(a.status in (200,210), 1, 0))                                    backed," +
                "       sum(if(c.rate_config_type!=10,h.user_actual_buyout_amt,0))              actual_buyout_amt," +
                "       sum(if(i.id is not null, i.actual_financing_amt, 0))                    renew_total_rent," +
                "       sum(if(i.id is not null, i.real_repay_capital, 0))                   renew_act_rent_amt," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.real_overdue_fine,0))            " +
                "                                                                            renew_real_overdue_fine," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.capital, 0))           " +
                "                                                                            renew_overdue_capital," +
                "       sum(if(i.id is not null, i.reduce_amt, 0))                              renew_reduce_amt," +
                "       sum(get_json_object(g.ext, '$.buyoutDeductionAmount') +" +
                "           if(i.id is not null, get_json_object(i.ext, '$.buyoutDeductionAmount'), 0))            " +
                "                                                                               bond_deduct_amt" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id" +
                "         inner join (select order_id," +
                "                            avg(capital)                               avg_capital," +
                "                            sum(IF(repay_status = 5, real_repay_capital - discount_amt, 0)) " +
                "                                                                       real_repay_capital," +
                "                            sum(IF(repay_status != 5, capital, 0))     capital," +
                "                            sum(real_overdue_fine)                     real_overdue_fine," +
                "                            sum(discount_amt + reduce_amt)             reduce_amt" +
                "                     from rent_order_repayment_plan" +
                "                     where is_deleted = 0" +
                "                       and ds = " + ds +
                "                     group by order_id) d on a.id = d.order_id" +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0" +
                "         inner join rent_order_overdue_stat f on a.id = f.order_id" +
                "         left join rent_order_flow g" +
                "                   on a.id = g.order_id and g.biz_type = 3 and g.pay_status = 10 and g.flow_type = 1" +
                "                   and g.refunded_amt = 0 and g.ext is not null and g.ext != '' and g.mark_refund =0" +
                "                   and g.is_deleted = 0 and g.ds = " + ds +
                "         left join (select order_id,  " +
                "                            sum(user_actual_buyout_amt)  user_actual_buyout_amt" +
                "                    from rent_order_account_check  " +
                "                    where is_deleted = 0  " +
                "                    and ds = " + ds +
                "                    group by order_id) h on a.id = h.order_id  " +
                "         left join (select x.id," +
                "                           x.parent_id," +
                "                           y.actual_financing_amt," +
                "                           y.act_bond_amt," +
                "                           z.real_repay_capital," +
                "                           z.capital," +
                "                           z.real_overdue_fine," +
                "                           m.current_overdue_days," +
                "                           n.ext," +
                "                           z.reduce_amt" +
                "                    from rent_order x" +
                "                             inner join rent_order_finance_detail y on x.id = y.order_id" +
                "                             inner join (select order_id," +
                "                                                sum(IF(repay_status = 5, " +
                "                                                            real_repay_capital-discount_amt, 0)) " +
                "                                                                       real_repay_capital," +
                "                                                sum(IF(repay_status != 5, capital, 0))           " +
                "                                                                       capital," +
                "                                                sum(real_overdue_fine)                           " +
                "                                                                       real_overdue_fine," +
                "                                                sum(discount_amt + reduce_amt)                   " +
                "                                                                       reduce_amt" +
                "                                         from rent_order_repayment_plan" +
                "                                         where is_deleted = 0" +
                "                                           and ds = " + ds +
                "                                         group by order_id) z on x.id = z.order_id" +
                "                             inner join rent_order_overdue_stat m on x.id = m.order_id" +
                "                             left join rent_order_flow n" +
                "                                       on x.id = n.order_id and n.biz_type = 3 and n.pay_status = 10" +
                "                       and n.flow_type = 1 and n.refunded_amt = 0 and n.ext is not null " +
                "                       and n.ext != '' and n.mark_refund = 0 and n.is_deleted = 0 and n.ds = " + ds +
                "                    where x.parent_id > 0" +
                "                      and x.is_deleted = 0" +
                "                      and y.is_deleted = 0" +
                "                      and m.is_deleted = 0" +
                "                      and x.ds = " + ds +
                "                      and y.ds = " + ds +
                "                      and m.ds = " + ds + ") i on a.id = i.parent_id" +
                " where a.merchant_id = 100" +
                "  and a.payment_time is not null" +
                "  and a.termination != 5" +
                "  and a.biz_type = 2" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and b.type_class != 1" +
                "  and a.customer_id not in (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + FinanceReportBusiness.EFFECTIVE_ORDER_STATUS + ")" +
                "  and a.is_deleted = 0" +
                "  and c.is_deleted = 0" +
                "  and f.is_deleted = 0" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and e.ds = " + ds +
                "  and f.ds = " + ds +
                " group by date(a.rent_start_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "       and c.repayment_term = 3, '1', '0'), c.rate_config_type ); ";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 租赁统计
     *
     * @param ds
     */
    private void countRentTask(String ds, String testerIds, Integer startPage) {
        String sql = " select date(a.rent_start_date)                                              day," +
                "       a.mini_type," +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0') as           forcedconversion," +
                "       count(1)                                                                number," +
                "       sum(b.actual_supply_price)                                              actual_supply_price," +
                "       sum(if(c.act_bond_amt = 0 and i.act_bond_amt is not null, i.act_bond_amt, c.act_bond_amt)) " +
                "                                                                               bond_amt," +
                "       sum(if(c.rate_config_type!=10,c.actual_financing_amt,d.avg_capital*12)) total_rent," +
                "       sum(if(c.rate_config_type!=10,c.buyout_amt,0))                          buyout_amt," +
                "       sum(d.real_repay_capital)                                               real_repay_capital," +
                "       sum(if(f.current_overdue_days > 0, d.capital, 0))                       overdue_capital," +
                "       sum(d.real_overdue_fine)                                                real_overdue_fine," +
                "       sum(d.reduce_amt)                                                       reduce_amt," +
                "       sum(if(a.status in (15, 310) and i.id is null, 1, 0))                   renting," +
                "       sum(if(a.status in (15, 310) and i.id is not null, 1, 0))               renew_renting," +
                "       sum(if(a.status in (220, 330), 1, 0))                                   buyouted," +
                "       sum(if(i.id is not null, 1, 0))                                         renewing," +
                "       sum(if(a.status in (200,210), 1, 0))                                    backed," +
                "       sum(if(c.rate_config_type!=10,h.user_actual_buyout_amt,0))              actual_buyout_amt," +
                "       sum(if(i.id is not null, i.actual_financing_amt, 0))                    renew_total_rent," +
                "       sum(if(i.id is not null, i.real_repay_capital, 0))                   renew_act_rent_amt," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.real_overdue_fine,0))            " +
                "                                                                            renew_real_overdue_fine," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.capital, 0))           " +
                "                                                                            renew_overdue_capital," +
                "       sum(if(i.id is not null, i.reduce_amt, 0))                              renew_reduce_amt," +
                "       sum(get_json_object(g.ext, '$.buyoutDeductionAmount') +" +
                "           if(i.id is not null, get_json_object(i.ext, '$.buyoutDeductionAmount'), 0))            " +
                "                                                                               bond_deduct_amt" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id" +
                "         inner join (select order_id," +
                "                            avg(capital)                               avg_capital," +
                "                            sum(IF(repay_status = 5, real_repay_capital - discount_amt, 0)) " +
                "                                                                       real_repay_capital," +
                "                            sum(IF(repay_status != 5, capital, 0))     capital," +
                "                            sum(real_overdue_fine)                     real_overdue_fine," +
                "                            sum(discount_amt + reduce_amt)             reduce_amt" +
                "                     from rent_order_repayment_plan" +
                "                     where is_deleted = 0" +
                "                       and ds = " + ds +
                "                     group by order_id) d on a.id = d.order_id" +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0" +
                "         inner join rent_order_overdue_stat f on a.id = f.order_id" +
                "         left join rent_order_flow g" +
                "                   on a.id = g.order_id and g.biz_type = 3 and g.pay_status = 10 and g.flow_type = 1" +
                "                   and g.refunded_amt = 0 and g.ext is not null and g.ext != '' and g.mark_refund =0" +
                "                   and g.is_deleted = 0 and g.ds = " + ds +
                "         left join (select order_id,  " +
                "                            sum(user_actual_buyout_amt)  user_actual_buyout_amt" +
                "                    from rent_order_account_check  " +
                "                    where is_deleted = 0  " +
                "                    and ds = " + ds +
                "                    group by order_id) h on a.id = h.order_id  " +
                "         left join (select x.id," +
                "                           x.parent_id," +
                "                           y.actual_financing_amt," +
                "                           y.act_bond_amt," +
                "                           z.real_repay_capital," +
                "                           z.capital," +
                "                           z.real_overdue_fine," +
                "                           m.current_overdue_days," +
                "                           n.ext," +
                "                           z.reduce_amt" +
                "                    from rent_order x" +
                "                             inner join rent_order_finance_detail y on x.id = y.order_id" +
                "                             inner join (select order_id," +
                "                                                sum(IF(repay_status = 5, " +
                "                                                            real_repay_capital-discount_amt, 0)) " +
                "                                                                       real_repay_capital," +
                "                                                sum(IF(repay_status != 5, capital, 0))           " +
                "                                                                       capital," +
                "                                                sum(real_overdue_fine)                           " +
                "                                                                       real_overdue_fine," +
                "                                                sum(discount_amt + reduce_amt)                   " +
                "                                                                       reduce_amt" +
                "                                         from rent_order_repayment_plan" +
                "                                         where is_deleted = 0" +
                "                                           and ds = " + ds +
                "                                         group by order_id) z on x.id = z.order_id" +
                "                             inner join rent_order_overdue_stat m on x.id = m.order_id" +
                "                             left join rent_order_flow n" +
                "                                       on x.id = n.order_id and n.biz_type = 3 and n.pay_status = 10" +
                "                       and n.flow_type = 1 and n.refunded_amt = 0 and n.ext is not null " +
                "                       and n.ext != '' and n.mark_refund = 0 and n.is_deleted = 0 and n.ds = " + ds +
                "                    where x.parent_id > 0" +
                "                      and x.is_deleted = 0" +
                "                      and y.is_deleted = 0" +
                "                      and m.is_deleted = 0" +
                "                      and x.ds = " + ds +
                "                      and y.ds = " + ds +
                "                      and m.ds = " + ds + ") i on a.id = i.parent_id" +
                " where a.merchant_id = 100" +
                "  and a.payment_time is not null" +
                "  and a.termination != 5" +
                "  and a.biz_type = 2" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and b.type_class != 1" +
                "  and a.customer_id not in (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + FinanceReportBusiness.EFFECTIVE_ORDER_STATUS + ")" +
                "  and a.is_deleted = 0" +
                "  and c.is_deleted = 0" +
                "  and f.is_deleted = 0" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and e.ds = " + ds +
                "  and f.ds = " + ds +
                " group by date(a.rent_start_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "       and c.repayment_term = 3, '1', '0'), c.rate_config_type" +
                " order by date(a.rent_start_date)" +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE +
                "; ";
        List<Record> records = odpsUtil.querySql(sql);
        List<DataCheckRentDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(k.getString("day"), dateFormatter),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            Integer rateConfigType = Integer.valueOf(k.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer number = Integer.valueOf(k.getString("number"));
            BigDecimal actualSupplyPrice = stringToDecimal(k.getString("actual_supply_price"));
            BigDecimal bondAmt = stringToDecimal(k.getString("bond_amt"));
            BigDecimal totalRent = stringToDecimal(k.getString("total_rent"));
            BigDecimal buyoutAmt = stringToDecimal(k.getString("buyout_amt"));
            BigDecimal realRepayCapital = stringToDecimal(k.getString("real_repay_capital"));
            BigDecimal overdueCapital = stringToDecimal(k.getString("overdue_capital"));
            BigDecimal realOverdueFine = stringToDecimal(k.getString("real_overdue_fine"));
            BigDecimal reduceAmt = stringToDecimal(k.getString("reduce_amt"));
            Integer renting = Integer.valueOf(k.getString("renting"));
            Integer renewRenting = Integer.valueOf(k.getString("renew_renting"));
            Integer buyouted = Integer.valueOf(k.getString("buyouted"));
            Integer renewing = Integer.valueOf(k.getString("renewing"));
            Integer backed = Integer.valueOf(k.getString("backed"));
            BigDecimal actualBuyoutAmt = stringToDecimal(k.getString("actual_buyout_amt"));
            BigDecimal renewTotalRent = stringToDecimal(k.getString("renew_total_rent"));
            BigDecimal renewActRentAmt = stringToDecimal(k.getString("renew_act_rent_amt"));
            BigDecimal renewOverdueCapital = stringToDecimal(k.getString("renew_overdue_capital"));
            BigDecimal renewRealOverdueFine = stringToDecimal(k.getString("renew_real_overdue_fine"));
            BigDecimal renewReduceAmt = stringToDecimal(k.getString("renew_reduce_amt"));
            BigDecimal bondDeductAmt = stringToDecimal(k.getString("bond_deduct_amt"));

            DataCheckRentDO rentDO = new DataCheckRentDO();
            rentDO.setCountDay(rentStartDate);
            rentDO.setMiniType(miniType);
            rentDO.setFinanceType(FinanceTypeEnum.getFinanceType(rateConfigType, 0, forcedConversion));
            rentDO.setNumber(number);
            rentDO.setActSupplyPrice(actualSupplyPrice);
            rentDO.setBondAmt(bondAmt);
            rentDO.setTotalRent(totalRent);
            rentDO.setBuyoutAmt(buyoutAmt);
            rentDO.setActReceiveRentAmt(realRepayCapital);
            rentDO.setOverdueCapital(overdueCapital);
            rentDO.setRealOverdueFineAmt(realOverdueFine);
            rentDO.setDiscountAmt(reduceAmt);
            rentDO.setInLeaseNumber(renting);
            rentDO.setInLeaseRenewNumber(renewRenting);
            rentDO.setBuyoutNumber(buyouted);
            rentDO.setInRenewNumber(renewing);
            rentDO.setReturnNumber(backed);
            rentDO.setActBuyoutAmt(actualBuyoutAmt);
            rentDO.setRenewTotalRent(renewTotalRent);
            rentDO.setRenewActReceiveRent(renewActRentAmt);
            rentDO.setRenewOverdueCapital(renewOverdueCapital);
            rentDO.setRenewRealOverdueFineAmt(renewRealOverdueFine);
            rentDO.setRenewDiscountAmt(renewReduceAmt);
            rentDO.setBondDeductionAmt(bondDeductAmt);
            resList.add(rentDO);
        });
        dataCheckRentService.saveBatch(resList);
    }

    /**
     * 租后统计
     *
     * @param ds
     */
    private Integer getAfterRentTaskCount(String ds, String testerIds) {
        String sql = " select count(*) num from ( select date(a.rent_end_date)                                       " +
                " " +
                " day," +
                "       a.mini_type," +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')         forcedconversion," +
                "       count(1)                                                           number," +
                "       sum(if(a.status = 220 and k.lawsuit_status not in (2, 5, 9), 1, 0))                                      buyouted," +
                "       sum(if(c.rate_config_type!=10,c.buyout_amt,0))                     buyout_amt," +
                "       sum(if(c.rate_config_type!=10,h.user_actual_buyout_amt,0))         act_buyout_amt," +
                "       sum(if(c.rate_config_type!=10,h.manage_buyout_discount+h.offline_buyout_discount,0)) " +
                "                                                                          buyout_deduct_amt," +
                "       sum(if(i.id is not null and k.lawsuit_status not in (2, 5, 9), 1, 0))                                    renew_number," +
                "       sum(if(i.id is not null, i.actual_financing_amt, 0))               renew_total_rent," +
                "       sum(if(i.id is not null, i.real_repay_capital, 0))                 renew_act_rent_amt," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.capital, 0))           " +
                "                                                                          renew_overdue_capital," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.real_overdue_fine, 0)) " +
                "                                                                          renew_real_overdue_fine," +
                "       sum(if(i.id is not null, i.reduce_amt, 0))                         renew_reduce_amt," +
                "       sum(if(a.status in (200,210) and k.lawsuit_status not in (2, 5, 9), 1, 0))                               backed," +
                "       sum(if(k.id>0 and k.lawsuit_status  in (2, 5, 9),1,0))                                                lawsuit_num" +
                " from rent_order a" +
                "         inner join rent_order_item b" +
                "                    on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                "                               and b.ds = " + ds + " and b.type_class != 1" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.ds = " + ds +
                "         inner join (select order_id," +
                "                            avg(capital)                                 avg_capital," +
                "                            sum(IF(repay_status = 5, real_repay_capital - discount_amt, 0)) " +
                "                                                                         real_repay_capital," +
                "                            sum(IF(repay_status != 5, capital, 0))       capital," +
                "                            sum(real_overdue_fine)                       real_overdue_fine," +
                "                            sum(discount_amt + reduce_amt)               reduce_amt" +
                "                     from rent_order_repayment_plan" +
                "                     where is_deleted = 0" +
                "                       and ds = " + ds +
                "                     group by order_id) d on a.id = d.order_id" +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0 and e.ds = " + ds +
                "         left join rent_order_flow g" +
                "                   on a.id = g.order_id and g.biz_type = 3 and g.pay_status = 10 " +
                "                           and g.flow_type = 1 and g.refunded_amt = 0 and g.ext is not null " +
                "                           and g.ext != '' and g.mark_refund = 0 and g.is_deleted = 0 and g.ds = " + ds +
                "         left join (select order_id,  " +
                "                            sum(user_actual_buyout_amt)  user_actual_buyout_amt," +
                "                            sum(buyout_coupon_amount) manage_buyout_discount, " +
                "                            sum(buyout_coupon_offline_amount) offline_buyout_discount " +
                "                    from rent_order_account_check  " +
                "                    where is_deleted = 0  " +
                "                    and ds = " + ds +
                "                    group by order_id) h on a.id = h.order_id  " +
                "         left join (select x.id," +
                "                           x.parent_id," +
                "                           y.actual_financing_amt," +
                "                           z.real_repay_capital," +
                "                           z.capital," +
                "                           z.real_overdue_fine," +
                "                           m.current_overdue_days," +
                "                           z.reduce_amt" +
                "                    from rent_order x" +
                "                             inner join rent_order_finance_detail y on x.id = y.order_id " +
                "                                                                       and y.ds = " + ds +
                "                             inner join (select order_id," +
                "                                                sum(IF(repay_status = 5, " +
                "                                                           real_repay_capital-discount_amt, 0))" +
                "                                                                real_repay_capital," +
                "                                                sum(IF(repay_status != 5, capital, 0))           " +
                "                                                                capital," +
                "                                                sum(real_overdue_fine)                           " +
                "                                                                real_overdue_fine," +
                "                                                sum(discount_amt + reduce_amt)                   " +
                "                                                                reduce_amt" +
                "                                         from rent_order_repayment_plan" +
                "                                         where is_deleted = 0" +
                "                                           and ds = " + ds +
                "                                         group by order_id) z on x.id = z.order_id" +
                "                             inner join rent_order_overdue_stat m on x.id = m.order_id and m.ds = " + ds +
                "                    where x.parent_id > 0" +
                "                      and x.is_deleted = 0" +
                "                      and x.ds = " + ds +
                "                      and y.is_deleted = 0" +
                "                      and m.is_deleted = 0) i on a.id = i.parent_id" +
                "         left join rent_order_infomore k on a.id = k.order_id  and k.ds = " + ds +
                " where a.merchant_id = 100" +
                "  and a.payment_time is not null" +
                "  and a.termination != 5" +
                "  and a.biz_type = 2" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.customer_id not in (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + FinanceReportBusiness.EFFECTIVE_ORDER_STATUS + ")" +
                "  and a.is_deleted = 0" +
                "  and c.is_deleted = 0" +
                "  and a.ds = " + ds +
                " group by date(a.rent_end_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "       and c.repayment_term = 3, '1','0'), c.rate_config_type );  ";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 租后统计
     *
     * @param ds
     */
    private void countAfterRentTask(String ds, String testerIds, Integer startPage) {
        String sql = " select date(a.rent_end_date)                                        day," +
                "       a.mini_type," +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')         forcedconversion," +
                "       count(1)                                                           number," +
                "       sum(if(a.status = 220 and k.lawsuit_status not in (2, 5, 9), 1, 0))                                      buyouted," +
                "       sum(if(c.rate_config_type!=10,c.buyout_amt,0))                     buyout_amt," +
                "       sum(if(c.rate_config_type!=10,h.user_actual_buyout_amt,0))         act_buyout_amt," +
                "       sum(if(c.rate_config_type!=10,h.manage_buyout_discount+h.offline_buyout_discount,0)) " +
                "                                                                          buyout_deduct_amt," +
                "       sum(if(i.id is not null and k.lawsuit_status not in (2, 5, 9), 1, 0))                                    renew_number," +
                "       sum(if(i.id is not null, i.actual_financing_amt, 0))               renew_total_rent," +
                "       sum(if(i.id is not null, i.real_repay_capital, 0))                 renew_act_rent_amt," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.capital, 0))           " +
                "                                                                          renew_overdue_capital," +
                "       sum(if(a.status in (15, 310) and i.id is null and k.lawsuit_status not in (2, 5, 9), 1, 0))  " +
                "                                    renting," +
                "       sum(if(i.id is not null and i.current_overdue_days > 0, i.real_overdue_fine, 0)) " +
                "                                                                          renew_real_overdue_fine," +
                "       sum(if(i.id is not null, i.reduce_amt, 0))                         renew_reduce_amt," +
                "       sum(if(a.status in (200,210) and k.lawsuit_status not in (2, 5, 9), 1, 0))                               backed," +
                "       sum(if(k.id>0 and k.lawsuit_status  in (2, 5, 9),1,0))                                                lawsuit_num" +
                " from rent_order a" +
                "         inner join rent_order_item b" +
                "                    on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                "                               and b.ds = " + ds + " and b.type_class != 1" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.ds = " + ds +
                "         inner join (select order_id," +
                "                            avg(capital)                                 avg_capital," +
                "                            sum(IF(repay_status = 5, real_repay_capital - discount_amt, 0)) " +
                "                                                                         real_repay_capital," +
                "                            sum(IF(repay_status != 5, capital, 0))       capital," +
                "                            sum(real_overdue_fine)                       real_overdue_fine," +
                "                            sum(discount_amt + reduce_amt)               reduce_amt" +
                "                     from rent_order_repayment_plan" +
                "                     where is_deleted = 0" +
                "                       and ds = " + ds +
                "                     group by order_id) d on a.id = d.order_id" +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0 and e.ds = " + ds +
                "         left join rent_order_flow g" +
                "                   on a.id = g.order_id and g.biz_type = 3 and g.pay_status = 10 " +
                "                           and g.flow_type = 1 and g.refunded_amt = 0 and g.ext is not null " +
                "                           and g.ext != '' and g.mark_refund = 0 and g.is_deleted = 0 and g.ds = " + ds +
                "         left join (select order_id,  " +
                "                            sum(user_actual_buyout_amt)  user_actual_buyout_amt," +
                "                            sum(buyout_coupon_amount) manage_buyout_discount, " +
                "                            sum(buyout_coupon_offline_amount) offline_buyout_discount " +
                "                    from rent_order_account_check  " +
                "                    where is_deleted = 0  " +
                "                    and ds = " + ds +
                "                    group by order_id) h on a.id = h.order_id  " +
                "         left join (select x.id," +
                "                           x.parent_id," +
                "                           y.actual_financing_amt," +
                "                           z.real_repay_capital," +
                "                           z.capital," +
                "                           z.real_overdue_fine," +
                "                           m.current_overdue_days," +
                "                           z.reduce_amt" +
                "                    from rent_order x" +
                "                             inner join rent_order_finance_detail y on x.id = y.order_id " +
                "                                                                       and y.ds = " + ds +
                "                             inner join (select order_id," +
                "                                                sum(IF(repay_status = 5, " +
                "                                                           real_repay_capital-discount_amt, 0))" +
                "                                                                real_repay_capital," +
                "                                                sum(IF(repay_status != 5, capital, 0))           " +
                "                                                                capital," +
                "                                                sum(real_overdue_fine)                           " +
                "                                                                real_overdue_fine," +
                "                                                sum(discount_amt + reduce_amt)                   " +
                "                                                                reduce_amt" +
                "                                         from rent_order_repayment_plan" +
                "                                         where is_deleted = 0" +
                "                                           and ds = " + ds +
                "                                         group by order_id) z on x.id = z.order_id" +
                "                             inner join rent_order_overdue_stat m on x.id = m.order_id and m.ds = " + ds +
                "                    where x.parent_id > 0" +
                "                      and x.is_deleted = 0" +
                "                      and x.ds = " + ds +
                "                      and y.is_deleted = 0" +
                "                      and m.is_deleted = 0) i on a.id = i.parent_id" +
                "         left join rent_order_infomore k on a.id = k.order_id  and k.ds = " + ds +
                " where a.merchant_id = 100" +
                "  and a.payment_time is not null" +
                "  and a.termination != 5" +
                "  and a.biz_type = 2" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.customer_id not in (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + FinanceReportBusiness.EFFECTIVE_ORDER_STATUS + ")" +
                "  and a.is_deleted = 0" +
                "  and c.is_deleted = 0" +
                "  and a.ds = " + ds +
                " group by date(a.rent_end_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "       and c.repayment_term = 3, '1','0'), c.rate_config_type" +
                " order by date(a.rent_end_date)" +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE +
                ";  ";
        List<Record> records = odpsUtil.querySql(sql);
        List<DataCheckAfterRentDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(k.getString("day"), dateFormatter),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            Integer rateConfigType = Integer.valueOf(k.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer number = Integer.valueOf(k.getString("number"));
            Integer buyouted = Integer.valueOf(k.getString("buyouted"));
            BigDecimal buyoutAmt = stringToDecimal(k.getString("buyout_amt"));
            BigDecimal actualSupplyPrice = stringToDecimal(k.getString("act_buyout_amt"));
            BigDecimal buyoutDeductAmt = stringToDecimal(k.getString("buyout_deduct_amt"));
            Integer renewNumber = Integer.valueOf(k.getString("renew_number"));
            BigDecimal renewTotalRent = stringToDecimal(k.getString("renew_total_rent"));
            BigDecimal renewActRentAmt = stringToDecimal(k.getString("renew_act_rent_amt"));
            BigDecimal renewOverdueCapital = stringToDecimal(k.getString("renew_overdue_capital"));
            BigDecimal renewRealOverdueFine = stringToDecimal(k.getString("renew_real_overdue_fine"));
            BigDecimal renewReduceAmt = stringToDecimal(k.getString("renew_reduce_amt"));
            Integer renting = Integer.valueOf(k.getString("renting"));
            Integer backed = Integer.valueOf(k.getString("backed"));
            Integer lawsuitNum = Integer.valueOf(k.getString("lawsuit_num"));

            DataCheckAfterRentDO afterRentDO = new DataCheckAfterRentDO();
            afterRentDO.setCountDay(rentStartDate);
            afterRentDO.setMiniType(miniType);
            afterRentDO.setFinanceType(FinanceTypeEnum.getFinanceType(rateConfigType, 0, forcedConversion));
            afterRentDO.setNumber(number);
            afterRentDO.setBuyoutNumber(buyouted);
            afterRentDO.setBuyoutAmt(buyoutAmt);
            afterRentDO.setRenting(renting);
            afterRentDO.setActBuyoutAmt(actualSupplyPrice);
            afterRentDO.setBuyoutDiscountAmt(buyoutDeductAmt);
            afterRentDO.setRenewNumber(renewNumber);
            afterRentDO.setRenewTotalRentAmt(renewTotalRent);
            afterRentDO.setRenewActRentAmt(renewActRentAmt);
            afterRentDO.setRenewOverdueCapital(renewOverdueCapital);
            afterRentDO.setRenewRealOverdueFineAmt(renewRealOverdueFine);
            afterRentDO.setRenewDiscountAmt(renewReduceAmt);
            afterRentDO.setRenewReturnNumber(backed);
            afterRentDO.setLawsuitNum(lawsuitNum);
            resList.add(afterRentDO);
        });
        dataCheckAfterRentService.saveBatch(resList);
    }


    /**
     * 还款统计
     *
     * @param ds
     */
    private Integer getRepayTaskCount(String ds, String testerIds) {
        String sql = "select count(*) num from ( select date(d.repay_date)                                           " +
                "     day, " +
                "       a.mini_type, " +
                "       IF(a.parent_id = 0, 0, 1)                                               isrenew, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')              forcedconversion, " +
                "       count(1)                                                                number, " +
                "       sum(d.capital)                                                          capital, " +
                //这里是实还本金
                "       sum(if(d.overdue = 5, 0, d.real_repay_capital))                         real_repay_capital, " +
                "       sum(if(d.overdue = 5, d.capital, 0))                                    overdue_capital, " +
                "       sum(d.real_overdue_fine)                                                real_overdue_fine, " +
                "       sum(d.discount_amt + d.reduce_amt)                                      reduce_amt " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                "    and b.type_class != 1 and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 and c.ds =" +
                ds +
                "         inner join rent_order_repayment_plan d on a.id = d.order_id and d.is_deleted = 0 and d.ds =" +
                ds +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0 and e.sign_time" +
                "          is not null and e.ds = " + ds +
                "         inner join rent_order_overdue_stat h on a.id = h.order_id and h.is_deleted = 0 and h.ds = " +
                ds +
                " where a.merchant_id = 100 " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in " +
                "      (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and a.status <> 220" +
                "  and a.ds = " + ds +
                " group by date(d.repay_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "   and c.repayment_term = 3, '1','0'), " +
                "   IF(a.parent_id = 0, 0, 1), c.rate_config_type ) " +
                "; ";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }


    /**
     * 还款统计
     *
     * @param ds
     */
    private void countRepayTask(String ds, String testerIds, Integer startPage) {

        String sql = " select date(d.repay_date)                                                day, " +
                "       a.mini_type, " +
                "       IF(a.parent_id = 0, 0, 1)                                               isrenew, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.repayment_term = 3, '1', '0')              forcedconversion, " +
                "       count(1)                                                                number, " +
                "       sum(d.capital)                                                          capital, " +
                //这里是实还本金
                "       sum(if(d.overdue = 5, 0, d.real_repay_capital))                         real_repay_capital, " +
                "       sum(if(d.overdue = 5, d.capital, 0))                                    overdue_capital, " +
                "       sum(d.real_overdue_fine)                                                real_overdue_fine, " +
                "       sum(d.discount_amt + d.reduce_amt)                                      reduce_amt " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.is_deleted = 0 " +
                "    and b.type_class != 1 and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 and c.ds =" +
                ds +
                "         inner join rent_order_repayment_plan d on a.id = d.order_id and d.is_deleted = 0 and d.ds =" +
                ds +
                "         inner join rent_order_logistics e on a.id = e.order_id and e.is_deleted = 0 and e.sign_time" +
                "          is not null and e.ds = " + ds +
                "         inner join rent_order_overdue_stat h on a.id = h.order_id and h.is_deleted = 0 and h.ds = " +
                ds +
                " where a.merchant_id = 100 " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in " +
                "      (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and a.status <> 220" +
                "  and a.ds = " + ds +
                " group by date(d.repay_date), a.mini_type, IF(!ISNULL(c.ext_json) " +
                "   and c.repayment_term = 3, '1','0'), " +
                "   IF(a.parent_id = 0, 0, 1), c.rate_config_type" +
                " order by  date(d.repay_date) " +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
        List<Record> records = odpsUtil.querySql(sql);
        List<DataCheckRepayDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(k.getString("day"), dateFormatter),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            Integer isRenew = Integer.valueOf(k.getString("isrenew"));
            Integer rateConfigType = Integer.valueOf(k.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer number = Integer.valueOf(k.getString("number"));
            BigDecimal capital = stringToDecimal(k.getString("capital"));
            BigDecimal realRepayCapital = stringToDecimal(k.getString("real_repay_capital"));
            BigDecimal overdueCapital = stringToDecimal(k.getString("overdue_capital"));
            BigDecimal realOverdueFine = stringToDecimal(k.getString("real_overdue_fine"));
            BigDecimal reduceAmt = stringToDecimal(k.getString("reduce_amt"));

            DataCheckRepayDO repayDO = new DataCheckRepayDO();
            repayDO.setCountDay(rentStartDate);
            repayDO.setMiniType(miniType);
            repayDO.setFinanceType(FinanceTypeEnum.getFinanceType(rateConfigType, 0, forcedConversion));
            repayDO.setNumber(number);
            if (isRenew == 0) {
                repayDO.setCapital(capital);
                repayDO.setRealRepayCapital(realRepayCapital);
                repayDO.setOverdueFineAmt(overdueCapital);
                repayDO.setRealOverdueFineAmt(realOverdueFine);
                repayDO.setDiscountAmt(reduceAmt);
            } else {
                repayDO.setRenewCapital(capital);
                repayDO.setRenewRealRepayCapital(realRepayCapital);
                repayDO.setRenewOverdueCapital(overdueCapital);
                repayDO.setRenewRealOverdueFineAmt(realOverdueFine);
                repayDO.setRenewDiscountAmt(reduceAmt);
            }
            resList.add(repayDO);
        });
        dataCheckRepayService.saveBatch(resList);
    }


    private BigDecimal stringToDecimal(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }

}