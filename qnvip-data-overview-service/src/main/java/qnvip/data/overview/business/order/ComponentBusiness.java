package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.ComponentDO;
import qnvip.data.overview.domain.order.ComponentDetailDO;
import qnvip.data.overview.service.order.ComponentDetailService;
import qnvip.data.overview.service.order.ComponentService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2021/10/8 2:42 下午
 */
@Component
@RequiredArgsConstructor
public class ComponentBusiness {

    private final OdpsUtil odpsUtil;
    private final ComponentService componentService;
    private final ComponentDetailService componentDetailService;

    //TODO 配件销量排行

    /**
     * 启动配件任务
     * @param componentDO
     * @param sTime
     * @param eTime
     */
    public void runComponentCount(String dsStr,ComponentDO componentDO,String sTime, String eTime){
        componentGMVAndCount(dsStr,componentDO,sTime,eTime);
        componentOrderCount(dsStr,componentDO,sTime,eTime);
        componentPayCount(dsStr,componentDO,sTime,eTime);
        totalComponentOrderCount(dsStr,componentDO,sTime,eTime);
        totalComponentPayCount(dsStr,componentDO,sTime,eTime);
        componentService.saveOrUpdate(componentDO);
    }


    /**
     * 启动配件订单统计业务
     * @param countDay
     * @param sTime
     * @param eTime
     */
    public void runComponentDetail(String dsStr,LocalDateTime countDay,String sTime,String eTime){
        List<ComponentDetailDO> list = componentDetailOrderCount(dsStr,countDay, sTime, eTime);
        Map<Long, ComponentDetailDO> map = new HashMap<>();
        for (ComponentDetailDO componentDetailDO : list) {
            initMap(map,componentDetailDO,countDay);
            ComponentDetailDO detailDO = map.get(componentDetailDO.getItemId());
            detailDO.setGmv(componentDetailDO.getGmv());
            detailDO.setOrderCount(componentDetailDO.getOrderCount());
        }
        list = componentDetailPayCount(dsStr,countDay,sTime,eTime);
        for (ComponentDetailDO componentDetailDO : list) {
            initMap(map,componentDetailDO,countDay);
            ComponentDetailDO detailDO = map.get(componentDetailDO.getItemId());
            detailDO.setPayCount(componentDetailDO.getPayCount());
        }
        // 写入mysql
        for(Map.Entry<Long,ComponentDetailDO> entry : map.entrySet()){
            ComponentDetailDO detailDO = entry.getValue();
            componentDetailService.saveOrUpdate(detailDO);
        }
    }

    void initMap(Map<Long, ComponentDetailDO> map, ComponentDetailDO detailDO, LocalDateTime countDay){
        if(!map.containsKey(detailDO.getItemId())){
            ComponentDetailDO ac = new ComponentDetailDO();
            ac.setCountDay(countDay);
            ac.setGmv(BigDecimal.ZERO);
            ac.setItemName(detailDO.getItemName());
            ac.setItemId(detailDO.getItemId());
            ac.setOrderCount(0l);
            ac.setPayCount(0l);
            map.put(detailDO.getItemId(),ac);
        }
    }


    /**
     * 已签收订单数 及 GMV
     * 这里小茹说 'operating_purchase_price 目前没人维护配件的实际供货价 所以两个值一样 取 operating_purchase_price'
     * @param sTime
     * @param eTime
     * @return
     */
    public ComponentDO componentGMVAndCount(String dsStr,ComponentDO componentDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(operating_purchase_price),0) as ordergmv,count(1) as num from rent_order_item " +
                " where order_id in ( " +
                "    select b.order_id from rent_order a inner join rent_order_item b on a.id = b.order_id " +
                "    inner join rent_order_logistics c on b.order_id = c.order_id " +
                "    where a.biz_type = 2 and a.merchant_id = 100 and c.sign_time is not null and a.is_deleted = 0" +
                " and b.is_deleted = 0 and c.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and c.sign_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and c.sign_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by b.order_id having count(1) > 1) ");
        sb.append(" and item_type = 10 and ds = ").append(dsStr).append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        componentDO.setSaleCount(Long.parseLong(recordList.get(0).getString("num")));
        componentDO.setOrderGmv(new BigDecimal(recordList.get(0).getString("ordergmv")));
        componentDO.setCostPrice(new BigDecimal(recordList.get(0).getString("ordergmv")));
        return componentDO;
    }


    /**
     * 配件下单量
     * @param sTime
     * @param eTime
     * @return
     */
    public ComponentDO componentOrderCount(String dsStr,ComponentDO componentDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from ( " +
                "            select count(1) from rent_order a inner join rent_order_item b on a.id = b.order_id " +
                "            where a.biz_type = 2 and a.merchant_id = 100 and a.is_deleted = 0 and b.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
            sb.append(" group by b.order_id having count(1) > 1) b ; ");

        List<Record> recordList = odpsUtil.querySql(sb.toString());
        componentDO.setOrderCount(Long.parseLong(recordList.get(0).getString("num")));
        return componentDO;
    }


    /**
     * 配件支付单量,同一笔主订单多个配件订单，算一笔订单
     * @param sTime
     * @param eTime
     * @return
     */
    public ComponentDO componentPayCount(String dsStr,ComponentDO componentDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from ( " +
                "            select count(1) from rent_order a inner join rent_order_item b on a.id = b.order_id " +
                "            where a.biz_type = 2 and a.merchant_id = 100 and a.payment_time is not null and a.is_deleted = 0" +
                " and b.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by b.order_id having count(1) > 1) b ; ");

        List<Record> recordList = odpsUtil.querySql(sb.toString());
        componentDO.setOrderCount(Long.parseLong(recordList.get(0).getString("num")));
        return componentDO;
    }

    /**
     * 总配件下单量
     * @param sTime
     * @param eTime
     * @return
     */
    public ComponentDO totalComponentOrderCount(String dsStr,ComponentDO componentDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(distinct(c.target_item_id)) as num from rent_order a inner join rent_order_item b" +
                " on a.id = b.order_id inner join rent_item_relation c on b.item_id = c.target_item_id where a.biz_type = 2 and" +
                " a.merchant_id = 100 and b.item_type = 1 and a.is_deleted = 0 and b.is_deleted = 0 and c.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ; ");

        List<Record> recordList = odpsUtil.querySql(sb.toString());
        componentDO.setTotalOrderCount(Long.parseLong(recordList.get(0).getString("num")));
        return componentDO;
    }

    /**
     * 总配件支付单量
     * @param sTime
     * @param eTime
     * @return
     */
    public ComponentDO totalComponentPayCount(String dsStr,ComponentDO componentDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(distinct(c.target_item_id)) as num from rent_order a inner join rent_order_item b" +
                " on a.id = b.order_id inner join rent_item_relation c on b.item_id = c.target_item_id where a.biz_type = 2 and" +
                " a.merchant_id = 100 and b.item_type = 1 and a.payment_time is not null and a.is_deleted = 0 and b.is_deleted = 0 " +
                " and c.is_deleted = 0");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" ; ");

        List<Record> recordList = odpsUtil.querySql(sb.toString());
        componentDO.setTotalPayCount(Long.parseLong(recordList.get(0).getString("num")));
        return componentDO;
    }


    /**
     * 配件商品明细(下单量)
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ComponentDetailDO> componentDetailOrderCount(String dsStr,LocalDateTime countDay, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select c.id as item_id,c.name as name,sum(b.operating_purchase_price)" +
                " as operating_purchase_price,count(1) as num from rent_order a inner join rent_order_item b on a.id = " +
                " b.order_id inner join rent_item c on c.id = b.item_id where a.biz_type = 2 and a.merchant_id = 100 " +
                " and b.item_type = 10 and a.is_deleted = 0 and b.is_deleted = 0 and c.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by c.id,c.name; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<ComponentDetailDO> list = new ArrayList<>();
        for (Record record : recordList) {
            ComponentDetailDO detailDO = new ComponentDetailDO();
            detailDO.setCountDay(countDay);
            detailDO.setItemId(Long.parseLong(record.getString("item_id")));
            detailDO.setItemName(record.getString("name"));
            detailDO.setGmv(new BigDecimal(record.getString("operating_purchase_price")));
            detailDO.setOrderCount(Long.parseLong(record.getString("num")));
            list.add(detailDO);
        }
        return list;
    }

    /**
     * 配件商品明细（支付单量）
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ComponentDetailDO> componentDetailPayCount(String dsStr,LocalDateTime countDay, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select c.id as item_id,c.name as name,count(1) as num from rent_order " +
                " a inner join rent_order_item b on a.id = b.order_id inner join rent_item c on c.id = b.item_id where " +
                " a.biz_type = 2 and a.merchant_id = 100 and b.item_type = 10 and a.is_deleted = 0 and a.is_deleted = 0 " +
                " and b.is_deleted = 0");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by c.id,c.name; ");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        List<ComponentDetailDO> list = new ArrayList<>();
        for (Record record : recordList) {
            ComponentDetailDO detailDO = new ComponentDetailDO();
            detailDO.setCountDay(countDay);
            detailDO.setItemId(Long.parseLong(record.getString("item_id")));
            detailDO.setItemName(record.getString("name"));
            detailDO.setPayCount(Long.parseLong(record.getString("num")));
            list.add(detailDO);
        }
        return list;
    }

}