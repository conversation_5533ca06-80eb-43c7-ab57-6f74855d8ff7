package qnvip.data.overview.business.order;

import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.order.OperationQuotientDO;
import qnvip.data.overview.domain.order.OperationQuotientTagDO;
import qnvip.data.overview.service.order.OperationQuotientService;
import qnvip.data.overview.service.order.OperationQuotientTagService;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OperateQuotientBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final Integer PAGE_SIZE = 3000;

    private final OdpsUtil odpsUtil;
    private final OperationQuotientService riskQuotientService;
    private final OperationQuotientTagService tagService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(3, 5, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskQuotientBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    public void execCount(String ds) {

        try {
            riskQuotientService.deleteAll();
            Integer size = getCount(ds);
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                int finalStartPage = startPage;
                threadPoolExecutor.execute(() -> {
                    List<OperationQuotientDO> list = countRepayTask(ds, finalStartPage);
                    riskQuotientService.saveBatch(list);
                });
                startPage++;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    public void execTagCount(String ds) {

        try {
            tagService.deleteAll();
            Integer size = getTagCount(ds);
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                int finalStartPage = startPage;
                threadPoolExecutor.execute(() -> {
                    List<OperationQuotientTagDO> list = countTagRepayTask(ds, finalStartPage);
                    tagService.saveBatch(list);
                });
                startPage++;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }

    /**
     * 风控导流商
     *
     * @param ds
     */
    private List<OperationQuotientDO> countRepayTask(String ds, int startPage) {
        String sql = "with t1 as(" +
                "   select customer_id," +
                "        date (create_time) create_time," +
                "        mini_type," +
                "        no," +
                "        (CASE WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "       WHEN mini_type IN (6, 8, 10, 11,13,132) THEN '微信'" +
                "       WHEN mini_type in (2,12) THEN '字节跳动'" +
                "       WHEN mini_type = 14 THEN '快手'" +
                "       WHEN mini_type = 3 THEN 'app'" +
                " else  mini_type " +
                " END) platform," +
                "        if(nvl(quotient_name, '') = '', '未标记上导流商', quotient_name) quotient_name," +
                "        if(nvl(scene, '') = '', '未标记上场景值', scene) scene," +
                "        risk_status," +
                "        risk_strategy," +
                "        merchant_id ," +
                "        payment_time ," +
                "        termination," +
                "        sign_time ," +
                "        send_time ," +
                "        risk_auth_tag," +
                "        risk_auth_status," +
                "        merchant_transfer," +
                "        audit_type,audit_status" +
                "        from (" +
                "       select first_value(customerid)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) customer_id," +
                "       first_value(cl.createtime)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) create_time," +
                "       first_value(no)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) no," +
                "       first_value(mini_type)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) mini_type," +
                "       first_value(cl.quotientname)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) quotient_name," +
                "       first_value(cl.scene)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) scene," +
                "       first_value(cl.artificialAuditStatus)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_status," +
                "       first_value(riskStrategy)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_strategy," +
                "       first_value(merchant_id)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) merchant_id," +
                "       first_value(payment_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) payment_time," +
                "       first_value(termination)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) termination," +
                "       first_value(merchant_transfer)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) merchant_transfer," +
                "       first_value(risk_auth_tag)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_auth_tag," +
                "       first_value(risk_auth_status)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_auth_status," +
                "       first_value(sign_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) sign_time," +
                "       first_value(send_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl" +
                ".quotientname = '', 99, 0) asc, cl.createtime desc) send_time," +
                "  first_value(roa.audit_status)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) audit_status," +
                "    first_value(roa.type)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) audit_type" +
                "       from cl_loan cl" +
                "       left join serial_no sn on cl.loanno = sn.businessno and sn.ds = " + ds +
                "       left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid and a.ds = " + ds +
                "       inner join rent_order c on cl.loanno = c.no and c.ds =" + ds + " and c.is_deleted=0 and c" +
                ".type=1" +
                "       INNER JOIN rent_order_audit roa ON      c.id = roa.order_id AND     roa.ds = " + ds +
                "       left join (select order_id, send_time, sign_time from rent_order_logistics where ds = " + ds + ") rol on rol.order_id = c.id" +
                "       inner join rent_order_infomore roi on roi.order_id = c.id and roi.ds = " + ds + "" +
                "       left join rc_risk_strategy_rule_set b on a.ruleid = b.id and b.ds = " + ds + "" +
                "       where cl.ds = " + ds + "" +
                "       and date (cl.createtime)>= '2022-11-01'" +
                "       and parentno = ''" +
                "       )" +
                "   group by customer_id, date(create_time), quotient_name, scene, risk_status," +
                "       risk_strategy, mini_type, no, merchant_id," +
                "       payment_time," +
                "       termination," +
                "       risk_auth_tag," +
                "       risk_auth_status," +
                "       sign_time," +
                "       send_time,audit_type,audit_status,merchant_transfer )" +
                "   select" +
                "   t1.create_time, " +
                "                       t1.mini_type, " +
                "                       t1.platform, " +
                "                       t1.quotient_name, " +
                "                        t1.scene, " +
                "                        t1.risk_strategy, " +
                "                       count (distinct customer_id) risk_cnt, " +
                "                       count (distinct if(risk_status in (1, 5), customer_id, null)) in_audit_cnt, " +
                "                       count (distinct if(risk_status = 10, customer_id, null)) audit_pass_cnt, " +
                "                       count (distinct if(risk_status = 15, customer_id, null)) audit_refuse_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=1, customer_id, null)) pay_cnt, " +
                "                       count (distinct if(merchant_id = 100 and send_time is not null and termination=1, customer_id, null)) send_cnt, " +
                "                       count (distinct if(merchant_id = 100 and sign_time is not null and termination=1, customer_id, null)) sign_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=5, customer_id, null)) pay_close_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=5 " +
                "                       and (risk_auth_status = 5 or risk_auth_tag like '%未接通%'), customer_id, null)) audit_close_cnt," +
                "" +
                "                       count (distinct if(merchant_id <> 100 and merchant_transfer =10 and audit_status = 1 and audit_type=2, customer_id, null)) merchant_audit_cnt," +
                "" +
                "                         count(distinct " +
                "                             if(merchant_id <> 100 and audit_status = 1 and audit_type=2, customer_id, null))   merchant_audit_pass_cnt, " +
                "                       count(distinct" +
                "                             if(merchant_id <> 100 and audit_status <> 1 , customer_id, null))   merchant_audit_refuse_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and payment_time is not null, customer_id, null)) merchant_pay_cnt," +
                "                       count ( distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and send_time is not null and termination=1, customer_id, null)) merchant_send_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and sign_time is not null and termination=1, customer_id, null)) merchant_sign_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and payment_time is not null and termination=5, customer_id, null)) merchant_pay_close_cnt ," +
                "" +
                "                       count (distinct if(merchant_id <> 100 and  audit_status <> 1 and merchant_transfer =10, customer_id, null)) merchant_audit_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and  audit_status <> 1 and payment_time is not null, customer_id, null)) merchant_pay_cnt_refuse," +
                "                       count ( distinct if(merchant_id <> 100 and audit_status <> 1 and send_time is not null and termination=1, customer_id, null)) merchant_send_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and audit_status <> 1  and sign_time is not null and termination=1, customer_id, null)) merchant_sign_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and audit_status <> 1 and payment_time is not null and termination=5, customer_id, null)) merchant_pay_close_cnt_refuse" +
                "   from t1" +
                "   group by  t1.create_time," +
                "       t1.mini_type," +
                "       t1.platform," +
                "       t1.risk_strategy," +
                "       t1.quotient_name," +
                "        t1.scene" +
                "   order by   t1.create_time desc " +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";

        List<Record> records = odpsUtil.querySql(sql);
        List<OperationQuotientDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("create_time"), dateFormatter),
                    LocalTime.MIN);
            String platform = k.getString("platform");
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            String quotientName = k.getString("quotient_name");
            String scene = k.getString("scene");
            String riskStrategy = k.getString("risk_strategy");
            Integer riskCnt = Integer.valueOf(k.getString("risk_cnt"));
            Integer inAuditCnt = Integer.valueOf(k.getString("in_audit_cnt"));
            Integer auditPassCnt = Integer.valueOf(k.getString("audit_pass_cnt"));
            Integer auditRefuseCnt = Integer.valueOf(k.getString("audit_refuse_cnt"));
            Integer payCnt = Integer.valueOf(k.getString("pay_cnt"));
            Integer sendCnt = Integer.valueOf(k.getString("send_cnt"));
            Integer signCnt = Integer.valueOf(k.getString("sign_cnt"));
            Integer payCloseCnt = Integer.valueOf(k.getString("pay_close_cnt"));
            Integer auditCloseCnt = Integer.valueOf(k.getString("audit_close_cnt"));
            Integer merchantAuditCnt = Integer.valueOf(k.getString("merchant_audit_cnt"));
            Integer merchantAuditPassCnt = Integer.valueOf(k.getString("merchant_audit_pass_cnt"));
            Integer merchantAuditRefuseCnt = Integer.valueOf(k.getString("merchant_audit_refuse_cnt"));
            Integer merchantPayCnt = Integer.valueOf(k.getString("merchant_pay_cnt"));
            Integer merchantSendCnt = Integer.valueOf(k.getString("merchant_send_cnt"));
            Integer merchantSignCnt = Integer.valueOf(k.getString("merchant_sign_cnt"));
            Integer merchantPayCloseCnt = Integer.valueOf(k.getString("merchant_pay_close_cnt"));

            Integer merchantAuditCntRefuse = Integer.valueOf(k.getString("merchant_audit_cnt_refuse"));
            Integer merchantPayCntRefuse = Integer.valueOf(k.getString("merchant_pay_cnt_refuse"));
            Integer merchantSendCntRefuse = Integer.valueOf(k.getString("merchant_send_cnt_refuse"));
            Integer merchantSignCntRefuse = Integer.valueOf(k.getString("merchant_sign_cnt_refuse"));
            Integer merchantPayCloseCntRefuse = Integer.valueOf(k.getString("merchant_pay_close_cnt_refuse"));

            OperationQuotientDO repayDO = new OperationQuotientDO();
            // 订单创建时间
            repayDO.setCountType(1);
            repayDO.setRiskCnt(riskCnt);
            repayDO.setInAuditCnt(inAuditCnt);
            repayDO.setAuditPassCnt(auditPassCnt);
            repayDO.setAuditRefuseCnt(auditRefuseCnt);
            repayDO.setPayCnt(payCnt);
            repayDO.setSendCnt(sendCnt);
            repayDO.setRiskStrategy(riskStrategy);
            repayDO.setSignCnt(signCnt);
            repayDO.setPayCloseCnt(payCloseCnt);
            repayDO.setAuditCloseCnt(auditCloseCnt);
            repayDO.setMerchantCnt(merchantAuditPassCnt + merchantAuditRefuseCnt);
            repayDO.setMerchantAuditPassCnt(merchantAuditPassCnt);
            repayDO.setMerchantAuditCnt(merchantAuditCnt + merchantAuditRefuseCnt);
            repayDO.setMerchantAuditRefuseCnt(merchantAuditRefuseCnt);
            repayDO.setMerchantPayCnt(merchantPayCnt + merchantPayCntRefuse);
            repayDO.setMerchantSendCnt(merchantSendCnt + merchantSendCntRefuse);
            repayDO.setMerchantSignCnt(merchantSignCnt + merchantSignCntRefuse);
            repayDO.setMerchantPayCloseCnt(merchantPayCloseCnt + merchantPayCloseCntRefuse);

            repayDO.setMerchantAuditCntRefuse(merchantAuditCntRefuse);
            repayDO.setMerchantPayCntRefuse(merchantPayCntRefuse);
            repayDO.setMerchantSendCntRefuse(merchantSendCntRefuse);
            repayDO.setMerchantSignCntRefuse(merchantSignCntRefuse);
            repayDO.setMerchantPayCloseCntRefuse(merchantPayCloseCntRefuse);

            repayDO.setCountDay(countDay);
            repayDO.setPlatform(platform);
            repayDO.setMiniType(miniType);
            repayDO.setQuotientName(quotientName);
            repayDO.setScene(scene);

            resList.add(repayDO);
        });
        return resList;
    }


    /**
     * 风控导流商
     *
     * @param ds
     */
    private List<OperationQuotientTagDO> countTagRepayTask(String ds, int startPage) {
        String sql = "select customer_id," +
                "    date (create_time) create_time," +
                "    mini_type," +
                "    no," +
                "    (CASE" +
                "    WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "    WHEN mini_type IN (6, 8, 10, 11, 13, 132) THEN '微信'" +
                "    WHEN mini_type in (2, 12) THEN '字节跳动'" +
                "    WHEN mini_type = 14 THEN '快手'" +
                "    WHEN mini_type = 3 THEN 'app'" +
                "    else mini_type END) platform," +
                "    if(nvl(quotient_name, '') = '', '未标记上导流商', quotient_name) quotient_name," +
                "    if(nvl(scene, '') = '', '未标记上场景值', scene) scene," +
                "    risk_strategy," +
                "    min (tag) tag" +
                " from (select first_value(customerid)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) customer_id," +
                "    first_value(cl.createtime)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) create_time," +
                "    first_value(no)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) no," +
                "    first_value(mini_type)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) mini_type," +
                "    first_value(cl.quotientname)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) quotient_name," +
                "    first_value(cl.scene)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) scene," +
                "    first_value(riskStrategy)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_strategy," +
                "    wm_concat('%', concat_ws('&', concat_ws('_'," +
                "    concat_ws('_', concat_ws('_', b.scene, xsShow), result)," +
                "    masterModel), tag))" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) tag" +
                "    from cl_loan cl" +
                "    left join serial_no sn on cl.loanno = sn.businessno and sn.ds =  " + ds +
                "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid and a.ds =  " + ds +
                "    inner join rent_order c" +
                "    on cl.loanno = c.no and c.ds =  " + ds + " and c.is_deleted = 0 and c.type = 1" +
                "    left join (select order_id, send_time, sign_time" +
                "    from rent_order_logistics" +
                "    where ds = " + ds + " ) rol on rol.order_id = c.id" +
                "    inner join rent_order_infomore roi on roi.order_id = c.id and roi.ds =  " + ds +
                "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id and b.ds =  " + ds +
                "    where cl.ds =  " + ds +
                "    and date (cl.createtime)>= '2022-11-01'" +
                "    and cl.artificialAuditStatus = 15" +
                "    and parentno = '')" +
                "   group by customer_id, date(create_time), quotient_name, scene,  risk_strategy, mini_type, no" +
                "   order by customer_id desc" +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";

        List<Record> records = odpsUtil.querySql(sql);
        List<OperationQuotientTagDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("create_time"), dateFormatter),
                    LocalTime.MIN);
            String platform = k.getString("platform");
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            String quotientName = k.getString("quotient_name");
            String scene = k.getString("scene");
            String riskStrategy = k.getString("risk_strategy");
            String tag = k.getString("tag");
            String[] split = tag.split("%");

            Set<String> collect = Arrays.stream(split).collect(Collectors.toSet());
            String jsonStr = JSONUtil.toJsonStr(collect);
            OperationQuotientTagDO repayDO = new OperationQuotientTagDO();
            // 订单创建时间
            repayDO.setCountType(1);
            repayDO.setRiskStrategy(riskStrategy);

            repayDO.setCountDay(countDay);
            repayDO.setPlatform(platform);
            repayDO.setMiniType(miniType);
            repayDO.setQuotientName(quotientName);
            repayDO.setScene(scene);
            repayDO.setTag(jsonStr);

            resList.add(repayDO);
        });
        return resList;
    }


    /**
     * 风控经营
     *
     * @param ds
     */
    private Integer getCount(String ds) {

        String sql = "with t1 as(" +
                "   select customer_id," +
                "        date (create_time) create_time," +
                "        mini_type," +
                "        no," +
                "        (CASE WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "       WHEN mini_type IN (6, 8, 10, 11,13,132) THEN '微信'" +
                "       WHEN mini_type in (2,12) THEN '字节跳动'" +
                "       WHEN mini_type = 14 THEN '快手'" +
                "       WHEN mini_type = 3 THEN 'app'" +
                " else  mini_type " +
                " END) platform," +
                "        if(nvl(quotient_name, '') = '', '未标记上导流商', quotient_name) quotient_name," +
                "        if(nvl(scene, '') = '', '未标记上场景值', scene) scene," +
                "        risk_status," +
                "        risk_strategy," +
                "        merchant_id ," +
                "        payment_time ," +
                "        termination," +
                "        sign_time ," +
                "        send_time ," +
                "        risk_auth_tag," +
                "        risk_auth_status," +
                "        merchant_transfer," +
                "        audit_type,audit_status" +
                "        from (" +
                "       select first_value(customerid)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) customer_id," +
                "       first_value(cl.createtime)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) create_time," +
                "       first_value(no)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) no," +
                "       first_value(mini_type)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) mini_type," +
                "       first_value(cl.quotientname)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) quotient_name," +
                "       first_value(cl.scene)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) scene," +
                "       first_value(cl.artificialAuditStatus)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_status," +
                "       first_value(riskStrategy)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_strategy," +
                "       first_value(merchant_id)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) merchant_id," +
                "       first_value(payment_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) payment_time," +
                "       first_value(termination)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) termination," +
                "       first_value(merchant_transfer)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) merchant_transfer," +
                "       first_value(risk_auth_tag)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_auth_tag," +
                "       first_value(risk_auth_status)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_auth_status," +
                "       first_value(sign_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) sign_time," +
                "       first_value(send_time)" +
                "       over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl" +
                ".quotientname = '', 99, 0) asc, cl.createtime desc) send_time," +
                "  first_value(roa.audit_status)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) audit_status," +
                "    first_value(roa.type)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) audit_type" +
                "       from cl_loan cl" +
                "       left join serial_no sn on cl.loanno = sn.businessno and sn.ds = " + ds +
                "       left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid and a.ds = " + ds +
                "       inner join rent_order c on cl.loanno = c.no and c.ds =" + ds + " and c.is_deleted=0 and c" +
                ".type=1" +
                "       INNER JOIN rent_order_audit roa ON      c.id = roa.order_id AND     roa.ds = " + ds +
                "       left join (select order_id, send_time, sign_time from rent_order_logistics where ds = " + ds + ") rol on rol.order_id = c.id" +
                "       inner join rent_order_infomore roi on roi.order_id = c.id and roi.ds = " + ds + "" +
                "       left join rc_risk_strategy_rule_set b on a.ruleid = b.id and b.ds = " + ds + "" +
                "       where cl.ds = " + ds + "" +
                "       and date (cl.createtime)>= '2022-11-01'" +
                "       and parentno = ''" +
                "       )" +
                "   group by customer_id, create_time, quotient_name, scene, risk_status," +
                "       risk_strategy, mini_type, no, merchant_id," +
                "       payment_time," +
                "       termination," +
                "       risk_auth_tag," +
                "       risk_auth_status," +
                "       sign_time," +
                "       send_time,audit_type,audit_status,merchant_transfer )" +
                "  select count(*) num from (" +
                "   select" +
                "   t1.create_time, " +
                "                       t1.mini_type, " +
                "                       t1.platform, " +
                "                       t1.quotient_name, " +
                "                        t1.scene, " +
                "                        t1.risk_strategy, " +
                "                       count (distinct customer_id) risk_cnt, " +
                "                       count (distinct if(risk_status in (1, 5), customer_id, null)) in_audit_cnt, " +
                "                       count (distinct if(risk_status = 10, customer_id, null)) audit_pass_cnt, " +
                "                       count (distinct if(risk_status = 15, customer_id, null)) audit_refuse_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=1, customer_id, null)) pay_cnt, " +
                "                       count (distinct if(merchant_id = 100 and send_time is not null and termination=1, customer_id, null)) send_cnt, " +
                "                       count (distinct if(merchant_id = 100 and sign_time is not null and termination=1, customer_id, null)) sign_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=5, customer_id, null)) pay_close_cnt, " +
                "                       count (distinct if(merchant_id = 100 and payment_time is not null and termination=5 " +
                "                       and (risk_auth_status = 5 or risk_auth_tag like '%未接通%'), customer_id, null)) audit_close_cnt," +
                "" +
                "                       count (distinct if(merchant_id <> 100 and merchant_transfer =10 and audit_status = 1 and audit_type=2, customer_id, null)) merchant_audit_cnt," +
                "" +
                "                         count(distinct " +
                "                             if(merchant_id <> 100 and audit_status = 1 and audit_type=2, customer_id, null))   merchant_audit_pass_cnt, " +
                "                       count(distinct" +
                "                             if(merchant_id <> 100 and audit_status <> 1 , customer_id, null))   merchant_audit_refuse_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and payment_time is not null, customer_id, null)) merchant_pay_cnt," +
                "                       count ( distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and send_time is not null and termination=1, customer_id, null)) merchant_send_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and sign_time is not null and termination=1, customer_id, null)) merchant_sign_cnt," +
                "                       count (distinct if(merchant_id <> 100 and audit_status = 1 and audit_type=2 and payment_time is not null and termination=5, customer_id, null)) merchant_pay_close_cnt ," +
                "" +
                "                       count (distinct if(merchant_id <> 100 and  audit_status <> 1 and merchant_transfer =10, customer_id, null)) merchant_audit_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and  audit_status <> 1 and payment_time is not null, customer_id, null)) merchant_pay_cnt_refuse," +
                "                       count ( distinct if(merchant_id <> 100 and audit_status <> 1 and send_time is not null and termination=1, customer_id, null)) merchant_send_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and audit_status <> 1  and sign_time is not null and termination=1, customer_id, null)) merchant_sign_cnt_refuse," +
                "                       count (distinct if(merchant_id <> 100 and audit_status <> 1 and payment_time is not null and termination=5, customer_id, null)) merchant_pay_close_cnt_refuse" +
                "   from t1" +
                "   group by  t1.create_time," +
                "       t1.mini_type," +
                "       t1.platform," +
                "       t1.risk_strategy," +
                "       t1.quotient_name," +
                "        t1.scene" +
                "   order by   t1.create_time desc );";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 风控经营
     *
     * @param ds
     */
    private Integer getTagCount(String ds) {

        String sql = " select count(*) num from (select customer_id," +
                "    date (create_time) create_time," +
                "    mini_type," +
                "    no," +
                "    (CASE" +
                "    WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "    WHEN mini_type IN (6, 8, 10, 11, 13, 132) THEN '微信'" +
                "    WHEN mini_type in (2, 12) THEN '字节跳动'" +
                "    WHEN mini_type = 14 THEN '快手'" +
                "    WHEN mini_type = 3 THEN 'app'" +
                "    else mini_type END) platform," +
                "    if(nvl(quotient_name, '') = '', '未标记上导流商', quotient_name) quotient_name," +
                "    if(nvl(scene, '') = '', '未标记上场景值', scene) scene," +
                "    risk_strategy," +
                "    min (tag) tag" +
                " from (select first_value(customerid)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) customer_id," +
                "    first_value(cl.createtime)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) create_time," +
                "    first_value(no)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) no," +
                "    first_value(mini_type)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) mini_type," +
                "    first_value(cl.quotientname)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) quotient_name," +
                "    first_value(cl.scene)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) scene," +
                "    first_value(riskStrategy)" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) risk_strategy," +
                "    wm_concat('%', concat_ws('&', concat_ws('_'," +
                "    concat_ws('_', concat_ws('_', b.scene, xsShow), result)," +
                "    masterModel), tag))" +
                "    over (partition by customerid, date (cl.createtime), cl.quotientname order by if(cl.quotientname = '', 99, 0) asc, cl.createtime desc) tag" +
                "    from cl_loan cl" +
                "    left join serial_no sn on cl.loanno = sn.businessno and sn.ds =  " + ds +
                "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid and a.ds =  " + ds +
                "    inner join rent_order c" +
                "    on cl.loanno = c.no and c.ds =  " + ds + " and c.is_deleted = 0 and c.type = 1" +
                "    left join (select order_id, send_time, sign_time" +
                "    from rent_order_logistics" +
                "    where ds = " + ds + " ) rol on rol.order_id = c.id" +
                "    inner join rent_order_infomore roi on roi.order_id = c.id and roi.ds =  " + ds +
                "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id and b.ds =  " + ds +
                "    where cl.ds =  " + ds +
                "    and date (cl.createtime)>= '2022-11-01'" +
                "    and cl.artificialAuditStatus = 15" +
                "    and parentno = '')" +
                "   group by customer_id, create_time, quotient_name, scene,  risk_strategy, mini_type, " +
                " no);";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

}


