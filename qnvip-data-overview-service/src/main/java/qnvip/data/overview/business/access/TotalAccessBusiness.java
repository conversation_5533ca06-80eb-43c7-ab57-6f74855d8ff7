package qnvip.data.overview.business.access;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.AccessTotalDO;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.service.access.AccessTotalService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/13
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class TotalAccessBusiness {

    private final OdpsUtil odpsUtil;

    private final AccessTotalService accessTotalService;


    /**
     * 组装核心指标参数
     */
    public void runCore() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String sTime = ThreadLocalCacheUtil.get("fromTime");
        String eTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        AccessTotalDO accessTotalDO = new AccessTotalDO();
        accessTotalDO.setCountDay(countDay);
        accessUvAndPvCount(dsStr, accessTotalDO);
        getRegistrationsTotal(dsStr, accessTotalDO);
        accessUvAndPvByDate(dsStr, accessTotalDO, sTime,
                eTime);
        getRegistrationsByDate(dsStr, accessTotalDO, sTime,
                eTime);
        newUvByDate(dsStr, accessTotalDO, sTime, eTime);
        getGoodsExposurePvByDate(dsStr, accessTotalDO, sTime, eTime);
        // 写入mysql
        accessTotalService.saveOrUpdate(accessTotalDO);
    }

    /**
     * 计算累计PVAndUV
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO accessUvAndPvCount(String dsStr, AccessTotalDO accessTotalDO) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(customer_third_id) pv ,count(distinct customer_third_id) uv").
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                and("ds = " + dsStr).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String uv = records.get(0).getString("uv");
        String pv = records.get(0).getString("pv");

        accessTotalDO.setUvTotal(Long.parseLong(uv));
        accessTotalDO.setPvTotal(Long.parseLong(pv));
        return accessTotalDO;
    }

    /**
     * 计算累计注册量
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO getRegistrationsTotal(String dsStr, AccessTotalDO accessTotalDO) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(1) cnt").
                from("rent_customer").
                where("ds =" + dsStr).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String cnt;
        if (CollUtil.isEmpty(records)) {
            cnt = "0";
        } else {
            cnt = records.get(0).getString("cnt");
        }
        accessTotalDO.setRegistrationsTotal(Long.parseLong(cnt));
        return accessTotalDO;
    }

    /**
     * 计算昨日PVAndUV
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO accessUvAndPvByDate(
            String dsStr,
            AccessTotalDO accessTotalDO,
            String sTime,
            String eTime) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(customer_third_id) pv ,count(distinct customer_third_id) uv").
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), sTime, eTime).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String uv = records.get(0).getString("uv");
        String pv = records.get(0).getString("pv");

        accessTotalDO.setUv(Long.parseLong(uv));
        accessTotalDO.setPv(Long.parseLong(pv));
        return accessTotalDO;
    }

    /**
     * 计算昨日PVAndUV
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO newUvByDate(
            String dsStr,
            AccessTotalDO accessTotalDO,
            String sTime,
            String eTime) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(distinct customer_third_id) uv").
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_NEW_ACCESS.name()).
                where(EventTrackSqlParamEnum.CONDITION.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), sTime, eTime).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String uv = records.get(0).getString("uv");
        accessTotalDO.setUvNew(Long.parseLong(uv));
        return accessTotalDO;
    }

    /**
     * 计算注册量
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO getRegistrationsByDate(
            String dsStr,
            AccessTotalDO accessTotalDO,
            String sTime,
            String eTime) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(1) cnt").
                from("rent_customer").
                where(EventTrackSqlParamEnum.CONDITION.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.CREATE_TIME.name(), sTime, eTime).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String cnt = records.get(0).getString("cnt");
        accessTotalDO.setRegistrations(Long.parseLong(cnt));
        return accessTotalDO;
    }

    /**
     * 计算注册量
     *
     * @param accessTotalDO
     * @return
     */
    public AccessTotalDO getGoodsExposurePvByDate(
            String dsStr,
            AccessTotalDO accessTotalDO,
            String sTime,
            String eTime) {
        SqlInfo sqlInfo = ZealotKhala.start().
                select("count(customer_third_id) pv").
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_GOODS_EXPOSURE.name()).
                where(EventTrackSqlParamEnum.CONDITION.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), sTime, eTime).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        if (CollectionUtils.isEmpty(records)) {
            return new AccessTotalDO();
        }
        String pv = records.get(0).getString("pv");
        accessTotalDO.setPvGoodsExposure(Long.parseLong(pv));
        return accessTotalDO;
    }
}
