package qnvip.data.overview.business.finance;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.finance.FinanceReportDO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.finance.FinanceReportService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/2/28
 */
@Service
@RequiredArgsConstructor
public class FinanceReportBusiness {

    /**
     * 15:已签收 200:归还中 210:已归还 220:已买断 310:续租中 320:已续租 330:续租完成
     */
    public static final String EFFECTIVE_ORDER_STATUS = "15,200,210,220,310,320,330";
    public static final String EXCEPT_FINANCE_TYPE = "17,18";
    public static final String EXCEPT_MINI_TYPE = "-1";
    private final Integer PAGE_SIZE = 10000;

    private final OdpsUtil odpsUtil;
    private final FinanceReportService financeReportService;


    public void execCount(String ds) {
        Map<String, FinanceReportDO> collectMap = new HashMap<>();
        // String testerIds = getTesterIds(ds);
        String testerIds = "-1";
        // 到期买断金(基础项),归还本金(已收买断金)
        countTask1(ds, testerIds, collectMap);
        // 真实采购价(基础项) 收取保证金(基础项) 协议总租金(基础项)
        countTask2(ds, testerIds, collectMap);
        // 已收月租(已收租金)
        countTask3(ds, testerIds, collectMap);
        // 续租本金(已收买断金或续租报表) 转续租(实收保证金)
        countTask4(ds, testerIds, collectMap);
        // 保证金抵扣买断金、保证金抵扣租金(已收买断金)
        countTask5(ds, testerIds, collectMap);
        // 逾期滞纳金(其他收入)
        countTask6(ds, testerIds, collectMap);
        // 结清/归还退回(保证金)
        countTask7(ds, testerIds, collectMap);
        // 未退保证金(所有逾期)
        countTask8(ds, testerIds, collectMap);
        // 到期买断金(所有逾期)
        countTask9(ds, testerIds, collectMap);
        // 逾期滞纳金(所有逾期)
        countTask10(ds, testerIds, collectMap);
        // 剩余总租金(所有逾期)
        countTask11(ds, testerIds, collectMap);
        // 售前优惠(基础项)
        countTask12(ds, testerIds, collectMap);
        // 售后减免租金(已收租金)
        countTask13(ds, testerIds, collectMap);
        // 售前实收减免租金、运营实收减免租金(已收租金)、预计售前收入
        countTask14(ds, testerIds, collectMap);
        // 售后减免买断金(已收买断金)、运营减免买断金、保证金抵扣买断、已收买断金
        countTask15(ds, testerIds, collectMap);
        // 其他收入:优惠返还
        countTask16(ds, testerIds, collectMap);
        // 3+N已收总额
        countTask17(ds, testerIds, collectMap);
        // 保证金抵扣赔偿金
        countTask18(ds, testerIds, collectMap);
        // 6+6已收总额
        countTask19(ds, testerIds, collectMap);
        // 自营
        Integer selfSupport = 1;
        financeReportService.deleteAllWithBusinessType(selfSupport);
        for (FinanceReportDO financeReportDO : collectMap.values()) {
            // 实收租金 等于 已收租金本金-保证金抵扣租金-售前优惠-运营减免租金-租后减免租金
            financeReportDO.setRentReceiveRent(CalculateUtil.sub(financeReportDO.getRentReceiveRent(),
                    CalculateUtil.add(CalculateUtil.add(financeReportDO.getBaseActBeforeRentDiscountAmt(),
                                    financeReportDO.getRentDeductBondAmt()),
                            CalculateUtil.add(financeReportDO.getRentDeductAfterRent(),
                                    financeReportDO.getRentDeductMarketing()))));
            financeReportDO.setBusinessType(selfSupport);
        }
        financeReportService.saveBatch(collectMap.values());
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String sql) {
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 获取测试人员id
     *
     * @param ds
     * @return
     */
    public String getTesterIds(String ds) {
        String getTesterMobile = " select a.value " +
                "    from rent_system_const a " +
                "    where a.key = 'test.user.phone.list' " +
                "      and ds = " + ds + ";";
        List<Record> records = odpsUtil.querySql(getTesterMobile);
        if (CollUtil.isEmpty(records)) {
            return "-1";
        }
        String testerMobileStr = records.get(0).getString("value");
        if (StringUtils.isBlank(testerMobileStr)) {
            return "-1";
        }
        String s = Arrays.stream(testerMobileStr.split(","))
                .map((str) -> "'" + str + "'").collect(Collectors.joining(","));
        String sql2 = "     select id " +
                "    from rent_customer " +
                "    where mobile in ( "
                + s +
                "    ) " +
                "      and ds = " + ds + ";";
        List<Record> records2 = odpsUtil.querySql(sql2);
        if (CollUtil.isEmpty(records2)) {
            return "-1";
        }
        List<String> objects = new ArrayList<>();
        for (Record record : records2) {
            objects.add(record.getString("id"));
        }
        return objects.stream().collect(Collectors.joining(","));

    }


    /**
     * 到期买断金(基础项),归还本金(已收买断金)
     *
     * @param ds
     */
    private void countTask1(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {

        // 3+N买断金为0
        String sql = " select date(a.rent_start_date)                                     day,  " +
                "           date(e.rent_start_date)                                       parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(e.rent_end_date))                                    parent_end_time," +
                "           a.mini_type,  " +
                "           c.rate_config_type,  " +
                "           IF(a.parent_id > 0, 1, 0)                                     isrenew,  " +
                "           IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30) , '1', '0')   forcedconversion,  " +
                "           sum(if(c.rate_config_type not in( 10,30) and a.status in (200,210) and a.parent_id = 0 and c.repayment_term != 6," +
                "                                       c.buyout_amt,0))                  return_capital," +
                "           sum(if(c.rate_config_type not in( 10,30) and a.status in (200,210) and a.parent_id = 0  and c.repayment_term != 6,1,0)) " +
                "                                                                         return_capital_count," +
                "           sum(if(c.rate_config_type not in( 10,30)  and c.repayment_term != 6,c.buyout_amt,0))                buyout_amt, " +
                "           sum(if(c.rate_config_type not in( 10,30)  and c.repayment_term != 6,1,0))                           count  " +
                "    from rent_order a  " +
                "             inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and " +
                "               b.type_class != 1  " +
                "             inner join rent_order_finance_detail c on a.id = c.order_id  " +
                "             left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) e on e.id = a.parent_id " +
                "    where a.merchant_id in (100, 10000107)  " +
                "      and a.payment_time is not null  " +
                "      and a.termination != 5  " +
                "      and a.biz_type = 2  " +
                "      and a.type = 1  " +
                "      and a.customer_id not in (  " +
                testerIds +
                "        )  " +
                "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ")  " +
                "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ")  " +
                "      and a.is_deleted = 0  " +
                "      and b.is_deleted = 0  " +
                "      and c.is_deleted = 0  " +

                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                "      and c.ds = " + ds +
                "    group by date(a.rent_start_date),  " +
                "             date(e.rent_start_date),  " +
                "             a.mini_type,  " +
                "             IF(a.parent_id > 0, 1, 0),  " +
                "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'),  " +
                "             c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = "    order by date(a.rent_start_date),  " +
                    "             date(e.rent_start_date),  " +
                    "             a.mini_type,  " +
                    "             IF(a.parent_id > 0, 1, 0),  " +
                    "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'),  " +
                    "             c.rate_config_type" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            ;
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));

                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                BigDecimal returnCapital = stringToDecimal(record.getString("return_capital"));
                Integer returnCount = Integer.valueOf(record.getString("return_capital_count"));
                BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));

                financeReportDO.setBaseBuyoutAmt(CalculateUtil.add(financeReportDO.getBaseBuyoutAmt(), buyoutAmt));
                financeReportDO.setBaseBuyoutCount(financeReportDO.getBaseBuyoutCount() + count);

                financeReportDO.setBuyoutReturnCapitalAmt(CalculateUtil.add(financeReportDO.getBuyoutReturnCapitalAmt(),
                        returnCapital));
                financeReportDO.setBuyoutReturnCapitalCount(financeReportDO.getBuyoutReturnCapitalCount() + returnCount);

            }
            startPage++;
        }

    }


    /**
     * 真实采购价(基础项) 收取保证金(基础项) 协议总租金(基础项)
     *
     * @param ds
     */
    private void countTask2(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 3+N总租金=12期租金之和
        String sql = "   select date(a.rent_start_date)                                 day, " +
                "           date(k.rent_start_date)                                     parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "           a.mini_type, " +
                "           c.rate_config_type, " +
                "           IF(a.parent_id > 0, 1, 0) isrenew, " +
                "           IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')  forcedconversion, " +
                "           sum(b.actual_supply_price)                                  actual_supply_price, " +
                "           sum(if(c.act_bond_amt>0,c.act_bond_amt,if(f.act_bond_amt>0,f.act_bond_amt,e.act_bond_amt)" +
                "                   ))  bond_amt, " +
                "           sum(if(c.rate_config_type not in( 10,30),c.actual_financing_amt,d.capital*12))    rent_total, " +
                "           sum(report_reduce_amt)    report_reduce_amt, " +
                "           count(1)                                                    count " +
                "    from rent_order a " +
                "             inner join rent_order_item b on a.id = b.order_id " +
                "                                                 and b.is_deleted = 0 " +
                "                                                 and b.item_type = 1 " +
                "                                                 and b.type_class != 1 " +
                "                                                 and b.ds = " + ds +
                "             inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                                                           and c.is_deleted = 0 " +
                "                                                           and c.ds = " + ds +
                "             inner join (select rp.order_id," +
                "                                   avg(capital) capital ," +
                "                                   sum(report_reduce_amt) report_reduce_amt from " +
                "                                               rent_order_repayment_plan rp" +
                "                     inner join rent_order_finance_detail rf on rp.order_id=rf.order_id" +
                "                                                 where rp.is_deleted = 0 " +
                "                                                    and rp.ds = " + ds + " " +
                "                                                    and rf.ds = " + ds + " " +
                "                   group by rp.order_id) d " +
                "                                                    on a.id = d.order_id" +
                "             left join rent_order_finance_detail f on a.parent_id = f.order_id and f.ds = " + ds +
                "             left join ( select x.parent_id, y.act_bond_amt " +
                "                           from rent_order x" +
                "                           inner join rent_order_finance_detail y on x.id = y.order_id" +
                "                           where x.ds = " + ds + " and y.ds=" + ds + " ) e on e.parent_id = a.id " +
                "             left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "    where a.merchant_id in (100, 10000107) " +
                "      and a.payment_time is not null " +
                "      and a.termination != 5 " +
                "      and a.biz_type = 2 " +
                "      and a.type = 1 " +

                "      and a.customer_id not in (" + testerIds + ") " +
                "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "      and a.is_deleted = 0 " +
                "      and a.ds = " + ds +

                "    group by date(a.rent_start_date), a.mini_type, " +
                "             date(k.rent_start_date), " +
                "             IF(a.parent_id > 0, 1, 0), " +
                "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'), " +
                "             c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = "    order by date(a.rent_start_date), a.mini_type, " +
                    "             date(k.rent_start_date), " +
                    "             IF(a.parent_id > 0, 1, 0), " +
                    "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'), " +
                    "             c.rate_config_type  " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                BigDecimal actualSupplyPrice = stringToDecimal(record.getString("actual_supply_price"));
                BigDecimal bondAmt = stringToDecimal(record.getString("bond_amt"));
                BigDecimal rentTotal = stringToDecimal(record.getString("rent_total"));
                BigDecimal reportReduceAmt = stringToDecimal(record.getString("report_reduce_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setBaseActSupplyPrice(CalculateUtil.add(financeReportDO.getBaseActSupplyPrice(),
                        actualSupplyPrice));
                financeReportDO.setBaseActSupplyPriceCount(financeReportDO.getBaseActSupplyPriceCount() + count);

                financeReportDO.setBaseBondAmt(CalculateUtil.add(financeReportDO.getBaseBondAmt(), bondAmt));
                financeReportDO.setBaseBondCount(financeReportDO.getBaseBondCount() + count);

                financeReportDO.setBaseRentAmt(CalculateUtil.add(financeReportDO.getBaseRentAmt(), rentTotal));
                financeReportDO.setBaseRentCount(financeReportDO.getBaseRentCount() + count);

            }
            startPage++;
        }
    }


    /**
     * 已收月租(已收租金)
     *
     * @param ds
     */
    private void countTask3(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 12+N
        String sql = " select date(a.rent_start_date)                                            day, " +
                "           date(k.rent_start_date)                                     parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "           a.mini_type, " +
                "           c.rate_config_type, " +
                "           IF(a.parent_id > 0, 1, 0) isrenew, " +
                "           IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')  forcedconversion, " +
                "           sum(b.real_repay_capital)                  real_repay_capital, " +
                "           sum(b.report_reduce_amt)                  report_reduce_amt, " +
                // 3+N买断时的已收买断金也算已收租金
                "           sum(if(c.rate_config_type  in( 10,30) and a.status=220,c.actual_buyout_amt,0))   rent_buyout_amt," +
                "           sum(if(c.rate_config_type not in( 10,30) and a.status in (200,210)," +
                "                        c.actual_financing_amt-e.already_repay_capital,0)) " +
                "                                               rent_return_discount," +
                "           sum(if(c.rate_config_type not in( 10,30) and a.status in (200,210) " +
                "                   and c.actual_financing_amt>e.already_repay_capital,1,0))   " +
                "                                               rent_return_discount_count," +
                "           count(distinct a.id)                                        count " +
                "    from rent_order a " +
                "             inner join (select order_id,sum(real_repay_capital) real_repay_capital,max(term) term," +
                " sum(report_reduce_amt) report_reduce_amt" +
                "                                        from rent_order_repayment_plan " +
                "                                                   where is_deleted = 0 " +
                "                                                   and repay_status = 5" +
                "                                                   and ds = " + ds +
                "                                   group by order_id) b on a.id = b.order_id" +
                "             inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                                                           and c.is_deleted = 0" +
                "                                                           and c.ds = " + ds +
                "             inner join rent_order_item d on a.id = d.order_id  " +
                "                                                   and d.item_type = 1  " +
                "                                                   and d.is_deleted = 0  " +
                "                                                   and d.type_class != 1  " +
                "                                                   and d.ds = " + ds +
                "             left join (select order_id,sum(capital) already_repay_capital" +
                "                                        from rent_order_repayment_plan " +
                "                                                   where repay_status = 5" +
                "                                                   and is_deleted = 0" +
                "                                                   and ds = " + ds +
                "                                   group by order_id) e on a.id = e.order_id" +
                "             left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "    where a.is_deleted = 0 " +
                "      and a.merchant_id in (100, 10000107) " +
                "      and a.payment_time is not null " +
                "      and a.termination != 5 " +
                "      and a.biz_type = 2 " +

                "      and a.type = 1 " +
                "      and a.customer_id not in (" + testerIds + ") " +
                "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "      and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "      and a.is_deleted = 0 " +
                "      and a.ds = " + ds +
                "    group by date(a.rent_start_date), a.mini_type, " +
                "             date(k.rent_start_date),  " +
                "             IF(a.parent_id > 0, 1, 0), " +
                "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'), " +
                "             c.rate_config_type";


        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = "    order by date(a.rent_start_date), a.mini_type, " +
                    "             date(k.rent_start_date),  " +
                    "             IF(a.parent_id > 0, 1, 0), " +
                    "             IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0'), " +
                    "             c.rate_config_type  " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal realRepayCapital = stringToDecimal(record.getString("real_repay_capital"));
                // 上报减免金额
                BigDecimal reportReduceAmt = stringToDecimal(record.getString("report_reduce_amt"));
                BigDecimal rentBuyoutAmt = stringToDecimal(record.getString("rent_buyout_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                BigDecimal rentReturnDiscount = stringToDecimal(record.getString("rent_return_discount"));
                rentReturnDiscount = rentReturnDiscount.add(reportReduceAmt);
                Integer rentReturnDiscountCount = Integer.valueOf(record.getString("rent_return_discount_count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);

                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setRentReceiveRent(CalculateUtil.add(financeReportDO.getRentReceiveRent(),
                        CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                financeReportDO.setRentReceiveRentCount(financeReportDO.getRentReceiveRentCount() + count);
                // 针对非3+X的订单，有的账单没有还完，用户要归还。对这种已归还的订单，就存在一部分未收租金，将该未收部分的租金统计为归还优惠
                financeReportDO.setRentReturnDiscount(CalculateUtil.add(financeReportDO.getRentReturnDiscount(),
                        rentReturnDiscount));
                financeReportDO.setRentReturnDiscountCount(financeReportDO.getRentReturnDiscountCount() + rentReturnDiscountCount);
            }
            startPage++;
        }
    }


    /**
     * 续租本金(已收买断金或续租报表) 转续租(实收保证金)
     *
     * @param ds
     */
    private void countTask4(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 12+n
        String sql = " select date(c.rent_start_date)                                                     day, " +
                "           date(a.rent_start_date)                                     parent_day," +
                "           min(date(c.rent_end_date))                                    rent_end_time," +
                "           min(date(a.rent_end_date))                                    parent_end_time," +
                "           c.mini_type, " +
                "           c.rate_config_type, " +
                "           IF(c.parent_id > 0, 1, 0) isrenew, " +
                "           IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')          forcedconversion, " +
                "           sum(b.buyout_amt)                                                           buyout_amt, " +
                "           sum(c.act_bond_amt)                                                     act_bond_amt," +
                "           count(1)                                                                    count " +
                "    from rent_order a " +
                "             inner join (select x.parent_id, " +
                "                                x.rent_start_date, " +
                "                                x.rent_end_date, " +
                "                                x.mini_type, " +
                "                                y.rate_config_type, " +
                "                                y.renew_term, " +
                "                                y.repayment_term, " +
                "                                y.ext_json, " +
                "                                y.act_bond_amt " +
                "                         from rent_order x " +
                "                                  inner join rent_order_finance_detail y on x.id = y.order_id " +
                "                         where x.merchant_id in (100, 10000107) " +
                "                           and x.payment_time is not null " +
                "                           and x.termination != 5 " +
                "                           and x.biz_type = 2 " +
                "                           and x.type = 1 " +
                "                           and x.parent_id > 0 " +
                "                           and x.customer_id not in (" + testerIds + ") " +
                "                           and x.is_deleted = 0 " +
                "                           and x.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "                           and x.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "                           and x.ds = " + ds +
                "                           and y.ds = " + ds + ") c on a.id = c.parent_id " +
                "             inner join rent_order_finance_detail b on a.id = b.order_id " +
                "                                                           and b.is_deleted = 0 " +
                "                                                           and b.ds = " + ds +
                "             inner join rent_order_item d " +
                "                        on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 and " +
                "                           d.type_class != 1  " +
                "                           and d.ds = " + ds +
                "    where a.merchant_id in (100, 10000107) " +
                "      and a.payment_time is not null " +
                "      and a.termination != 5 " +
                "      and a.biz_type = 2 " +
                "      and a.type = 1 " +


                "      and a.customer_id not in (" + testerIds + ") " +
                "      and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "      and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "      and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "      and b.rate_config_type not in( 10,30) " +
                "      and a.is_deleted = 0 " +
                "      and a.ds = " + ds +

                "    group by date(c.rent_start_date),date(a.rent_start_date), c.mini_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30),'1', '0'), IF(c.parent_id > 0, 1, 0)," +
                "        c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = "    order by date(c.rent_start_date),date(a.rent_start_date), c.mini_type, " +
                    "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30),'1', '0'), IF(c.parent_id > 0, 1, 0)," +
                    "        c.rate_config_type  " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
                BigDecimal actBondAmt = stringToDecimal(record.getString("act_bond_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setBuyoutRenewPrincipal(CalculateUtil.add(buyoutAmt,
                        financeReportDO.getBuyoutRenewPrincipal()));
                financeReportDO.setBuyoutRenewPrincipalCount(count + financeReportDO.getBuyoutRenewPrincipalCount());

                financeReportDO.setBondRenewAmt(CalculateUtil.add(actBondAmt, financeReportDO.getBondRenewAmt()));
                financeReportDO.setBondRenewCount(count + financeReportDO.getBondRenewCount());

                // 给主订单续租本金赋值
                // Integer extraFinanceType = FinanceTypeEnum.reverseFinanceType(financeType);
                if (rateConfigType != null) {
                    initMap(collectMap, miniType, rateConfigType, parentDay, parentEndTime, null, null);
                    FinanceReportDO extraFinanceReportDO = collectMap.get(getMapKey(miniType, rateConfigType, parentDay
                            , parentEndTime, null, null));
                    extraFinanceReportDO.setBuyoutRenewPrincipal(CalculateUtil.add(extraFinanceReportDO.getBuyoutRenewPrincipal(), buyoutAmt));
                    extraFinanceReportDO.setBuyoutRenewPrincipalCount(extraFinanceReportDO.getBuyoutRenewPrincipalCount() + count);
                    extraFinanceReportDO.setBondRenewAmt(CalculateUtil.add(extraFinanceReportDO.getBondRenewAmt(),
                            actBondAmt));
                    extraFinanceReportDO.setBondRenewCount(extraFinanceReportDO.getBondRenewCount() + count);
                }

            }
            startPage++;
        }

    }


    /**
     * 保证金抵扣买断金、保证金抵扣租金(已收买断金)
     *
     * @param ds
     */
    private void countTask5(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') as forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       sum(if(plan_deduction_amount2 is null,plan_deduction_amount1,plan_deduction_amount2)) " +
                "           plan_deduction_amount1, " +
                "       sum(if(plan_deduction_amount1 is not null or plan_deduction_amount2 is not null,1,0))" +
                "           plan_count1, " +
                "       sum(if(c.rate_config_type  in( 10,30) and c.repayment_term != 6,if(buyout_deduction_amount2 is null,buyout_deduction_amount1," +
                "               buyout_deduction_amount2),0)) plan_deduction_amount2, " +
                "       sum(if(c.rate_config_type  in( 10,30) and c.repayment_term != 6,if(buyout_deduction_amount1 is not null or " +
                "               buyout_deduction_amount2 is not null,1,0),0)) plan_count2, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6,if(buyout_deduction_amount2 is null,buyout_deduction_amount1," +
                "               buyout_deduction_amount2),0)) buyout_deduction_amount, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6,if(buyout_deduction_amount1 is not null or " +
                "               buyout_deduction_amount2 is not null,1,0),0)) buyout_count, " +
                "       sum(return_damage_amt) return_damage_amt, " +
                "       sum(if(return_damage_amt>0,1,0)) return_damage_count " +
                " from rent_order a " +
                "       inner join (select sum(if(biz_type in (3,34),get_json_object(ext,'$.planDeductionAmount'),null)) " +
                "                     plan_deduction_amount1," +
                "                   sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=1,flow_amt,null))" +
                "                     plan_deduction_amount2," +
                "                   sum(if(biz_type in (3,34),get_json_object(ext," +
                "                           '$.buyoutDeductionAmount'),null)) buyout_deduction_amount1," +
                "                   sum(if(biz_type=23 and get_json_object(ext," +
                "                           '$.deductionType')=3,flow_amt,null))  buyout_deduction_amount2," +
                "                   sum(if(biz_type=2,flow_amt,0))  return_damage_amt," +
                "                   sum(if(biz_type in (3),get_json_object(ext,'$.buyoutActAmt'),null)) buyoutActFlowAmt, " +
                "               order_id from rent_order_flow  " +
                "                                   where biz_type in (3,23,34) " +
                "                                   and pay_status = 10 " +
                "                                   and flow_type =1 " +
                "                                   and (mark_refund is null or mark_refund = 0) " +
                "                                   and refunded_amt = 0 " +
                "                                   and ext is not null " +
                "                                   and ext != '' " +
                "                                   and is_deleted = 0 " +
                "                                   and ds = " + ds +
                "                          group by order_id) b on a.id = b.order_id" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                                   and d.type_class != 1 and d.ds = " + ds +
                "             left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in ( "
                + testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and c.ds = " + ds +


                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                "         c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                    "         c.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal planDeductionAmount1 = stringToDecimal(record.getString("plan_deduction_amount1"));
                Integer planCount1 = Integer.valueOf(record.getString("plan_count1"));
                BigDecimal planDeductionAmount2 = stringToDecimal(record.getString("plan_deduction_amount2"));
                Integer planCount2 = Integer.valueOf(record.getString("plan_count2"));
                BigDecimal buyoutDeductionAmount = stringToDecimal(record.getString("buyout_deduction_amount"));
                Integer buyoutCount = Integer.valueOf(record.getString("buyout_count"));
                BigDecimal returnDamageAmt = stringToDecimal(record.getString("return_damage_amt"));
                Integer returnDamageCount = Integer.valueOf(record.getString("return_damage_count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setRentDeductBondAmt(CalculateUtil.add(financeReportDO.getRentDeductBondAmt(),
                        CalculateUtil.add(planDeductionAmount1, planDeductionAmount2)));
                financeReportDO.setRentDeductBondCount(financeReportDO.getRentDeductBondCount() + planCount1 + planCount2);

                financeReportDO.setBuyoutDeductBuyoutAmt(CalculateUtil.add(financeReportDO.getBuyoutDeductBuyoutAmt(),
                        buyoutDeductionAmount));
                financeReportDO.setBuyoutDeductBuyoutCount(financeReportDO.getBuyoutDeductBuyoutCount() + buyoutCount);

                financeReportDO.setEndReturnDamageAmt(CalculateUtil.add(financeReportDO.getEndReturnDamageAmt(), returnDamageAmt));
                financeReportDO.setEndReturnDamageCount(financeReportDO.getEndReturnDamageCount() + returnDamageCount);
            }
            startPage++;
        }
    }


    /**
     * 逾期滞纳金(其他收入)
     *
     * @param ds
     */
    private void countTask6(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 12+N
        String sql = " select date(a.rent_start_date)                                 day, " +
                "       date(k.rent_start_date)                                       parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') as forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0)                                   isrenew, " +
                "       sum(b.real_overdue_fine) real_overdue_fine, " +
                "       count(distinct a.id)                                                      count " +
                "from rent_order a " +
                "         inner join rent_order_repayment_plan b on a.id = b.order_id " +
                "                                           and b.real_overdue_fine > 0 " +
                "                                           and b.is_deleted = 0 " +
                "                                           and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                                           and c.is_deleted = 0 " +
                "                                           and c.ds = " + ds +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                                           and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +


                "  and a.customer_id not in ( "
                + testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and a.ds = " + ds +
                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                "         c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                    "         c.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal realOverdueFine = stringToDecimal(record.getString("real_overdue_fine"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setOtherOverdueAmt(CalculateUtil.add(financeReportDO.getOtherOverdueAmt(),
                        realOverdueFine));
                financeReportDO.setOtherOverdueCount(financeReportDO.getOtherOverdueCount() + count);
            }
            startPage++;
        }

    }


    /**
     * 结清/归还退回(保证金)
     *
     * @param ds
     */
    private void countTask7(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                 day, " +
                "       date(k.rent_start_date)                                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       IF(a.status in (200,210),1,0) isreturn," +
                "       b.rate_config_type, " +
                "       IF(!ISNULL(b.ext_json) and b.rate_config_type  in( 10,30), '1', '0') as forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       sum(if(b.bond_refund_time is not null,b.bond_refund_amount,0)) bond_refund_amount, " +
                "       sum(if(b.bond_refund_time is not null,1,0))                   refund_count, " +
                "       sum(if(a.status in (200,210,220,330) and b.bond_refund_time is null,b.bond_refund_amount,0)) " +
                "                               surplus_refund_amount, " +
                "       sum(if(a.status in (200,210,220,330) and b.bond_refund_time is null and" +
                "                               b.bond_refund_amount>0,1,0)) surplus_count " +
                " from rent_order a " +
                "         inner join rent_order_finance_detail b on a.id = b.order_id " +
                "                                           and b.is_deleted = 0 " +
                "                                           and b.ds = " + ds +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                                           and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +


                "  and a.customer_id not in ( "
                + testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and a.is_deleted = 0 " +
                "  and a.ds = " + ds +
                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(b.ext_json) and b.rate_config_type  in( 10,30), '1', '0')," +
                // 是否归还
                "         IF(a.status in (200,210),1,0), " +
                "         b.rate_config_type";


        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(b.ext_json) and b.rate_config_type  in( 10,30), '1', '0')," +
                    // 是否归还
                    "         IF(a.status in (200,210),1,0), " +
                    "         b.rate_config_type" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                Integer isReturn = Integer.valueOf(record.getString("isreturn"));
                BigDecimal bondRefundAmount = stringToDecimal(record.getString("bond_refund_amount"));
                Integer refundCount = Integer.valueOf(record.getString("refund_count"));

                BigDecimal surplusRefundAmount = stringToDecimal(record.getString("surplus_refund_amount"));
                Integer surplusCount = Integer.valueOf(record.getString("surplus_count"));

                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                if (isReturn == 1) {
                    financeReportDO.setBondReturnBackBondAmt(CalculateUtil.add(financeReportDO.getBondReturnBackBondAmt(),
                            bondRefundAmount));
                    financeReportDO.setBondReturnBackBondCount(financeReportDO.getBondReturnBackBondCount() + refundCount);
                } else {
                    financeReportDO.setBondBackBondAmt(CalculateUtil.add(financeReportDO.getBondBackBondAmt(),
                            bondRefundAmount));
                    financeReportDO.setBondBackBondCount(financeReportDO.getBondBackBondCount() + refundCount);
                }

                // 结清未退保证金
                financeReportDO.setBondNotBackBondAmt(CalculateUtil.add(financeReportDO.getBondNotBackBondAmt(),
                        surplusRefundAmount));
                financeReportDO.setBondNotBackBondCount(financeReportDO.getBondNotBackBondCount() + surplusCount);
            }
            startPage++;
        }
    }


    /**
     * 未退保证金(所有逾期)
     *
     * @param ds
     */
    private void countTask8(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select e.day, " +
                "       e.parent_day, " +
                "       min(e.rent_end_time) rent_end_time, " +
                "       min(e.parent_end_time) parent_end_time, " +
                "       e.level, " +
                "       e.mini_type, " +
                "       e.isrenew, " +
                "       e.forcedconversion," +
                "       e.rate_config_type, " +
                "       sum(e.bond_amt)       bond_amt, " +
                "       sum(e.count)          count " +
                "from ( " +
                "         select date(a.rent_start_date)   day, " +
                "                date(k.rent_start_date)   parent_day, " +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "                a.mini_type, " +
                "                b.rate_config_type," +
                "                IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0') as " +
                " forcedconversion," +
                "                IF(a.parent_id > 0, 1, 0) isrenew, " +
                "                sum(b.surplus_bond_Amt)            bond_amt, " +
                "                sum(if(b.bond_refund_time is null and b.surplus_bond_Amt>0,1,0))       count, " +
                "                (case " +
                "                     when c.current_overdue_days = 0 then 0 " +
                "                     when c.current_overdue_days <= 7 then 1 " +
                "                     when c.current_overdue_days <= 31 then 2 " +
                "                     else 3 end) as       level " +
                "         from rent_order a " +
                "                  inner join rent_order_finance_detail b on a.id = b.order_id " +
                "                  inner join rent_order_overdue_stat c on a.id = c.order_id " +
                "                  inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and " +
                "                                     d.is_deleted = 0 and d.type_class != 1 and d.ds = " + ds +
                "                  left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "         where a.merchant_id in (100, 10000107) " +
                "           and a.payment_time is not null " +
                "           and a.termination != 5 " +
                "           and a.biz_type = 2 " +
                "           and a.type = 1 " +
                "           and a.customer_id not in ( "
                + testerIds +
                "             ) " +
                "           and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "           and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "           and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "           and a.is_deleted = 0 " +
                "           and b.is_deleted = 0 " +
                "           and c.is_deleted = 0 " +
                "           and a.ds = " + ds +


                "           and b.ds = " + ds +
                "           and c.ds = " + ds +
                "         group by date(a.rent_start_date), " +
                "                  date(k.rent_start_date)," +
                "                  a.mini_type, " +
                "                  IF(a.parent_id > 0, 1, 0), " +
                "                  b.rate_config_type, " +
                "                  IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0')," +
                "                  c.current_overdue_days " +
                "     ) e " +
                " group by e.level, " +
                "         e.day, " +
                "         e.parent_day, " +
                "         e.mini_type, " +
                "         e.isrenew, " +
                "         e.forcedconversion," +
                "         e.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by e.level, " +
                    "         e.day, " +
                    "         e.parent_day, " +
                    "         e.mini_type, " +
                    "         e.isrenew, " +
                    "         e.forcedconversion," +
                    "         e.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer level = Integer.valueOf(record.getString("level"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal bondAmt = stringToDecimal(record.getString("bond_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                switch (level) {
                    case 0:
                        financeReportDO.setOverZeroNotBackBondAmt(CalculateUtil.add(financeReportDO.getOverZeroNotBackBondAmt(), bondAmt));
                        financeReportDO.setOverZeroNotBackBondCount(financeReportDO.getOverZeroNotBackBondCount() + count);
                        break;
                    case 1:
                        financeReportDO.setOverFirstNotBackBondAmt(CalculateUtil.add(financeReportDO.getOverFirstNotBackBondAmt(), bondAmt));
                        financeReportDO.setOverFirstNotBackBondCount(financeReportDO.getOverFirstNotBackBondCount() + count);
                        break;
                    case 2:
                        financeReportDO.setOverSecondNotBackBondAmt(CalculateUtil.add(financeReportDO.getOverSecondNotBackBondAmt(), bondAmt));
                        financeReportDO.setOverSecondNotBackBondCount(financeReportDO.getOverSecondNotBackBondCount() + count);
                        break;
                    case 3:
                        financeReportDO.setOverThirdNotBackBondAmt(CalculateUtil.add(financeReportDO.getOverThirdNotBackBondAmt(), bondAmt));
                        financeReportDO.setOverThirdNotBackBondCount(financeReportDO.getOverThirdNotBackBondCount() + count);
                        break;
                    default:
                        break;
                }
            }
            startPage++;
        }
    }


    /**
     * 到期买断金(所有逾期)
     *
     * @param ds
     */
    private void countTask9(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 12+N
        String sql = " select e.day, " +
                "       e.parent_day, " +
                "       min(e.rent_end_time) rent_end_time, " +
                "       min(e.parent_end_time) parent_end_time, " +
                "       e.level, " +
                "       e.mini_type, " +
                "       e.isrenew, " +
                "       e.forcedconversion, " +
                "       e.rate_config_type, " +
                "       sum(e.buyout_amt) buyout_amt, " +
                "       sum(e.count)      count " +
                "  from (select date(a.rent_start_date)                                                     day, " +
                "             date(k.rent_start_date)                                                  parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "             a.mini_type, " +
                "             b.rate_config_type, " +
                "             IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0') forcedconversion, " +
                "             IF(a.parent_id > 0, 1, 0) isrenew, " +
                "             sum(if(b.rate_config_type not in( 10,30) and b.repayment_term != 6  and a.status in (15, 310) " +
                "                                       and j.parent_id is null, b.buyout_amt,0)) buyout_amt," +
                "             sum(if(b.rate_config_type not in( 10,30)  and b.repayment_term != 6  and a.status in (15, 310) " +
                "                                       and j.parent_id is null, 1, 0)) count, " +
                "             (case " +
                "                  when c.current_overdue_days = 0 then 0 " +
                "                  when c.current_overdue_days <= 7 then 1 " +
                "                  when c.current_overdue_days <= 31 then 2 " +
                "                  else 3 end)                                           as               level " +
                "      from rent_order a " +
                "               inner join rent_order_finance_detail b on a.id = b.order_id " +
                "               inner join rent_order_overdue_stat c on a.id = c.order_id " +
                "               inner join rent_order_item h on a.id = h.order_id and h.item_type = 1 " +
                "                                                   and h.is_deleted = 0 and h.type_class != 1 " +
                "                                                   and h.ds = " + ds +
                "               left join ( select parent_id from rent_order " +
                "                           where ds = " + ds + " ) j on j.parent_id = a.id " +
                "               left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "      where a.merchant_id in (100, 10000107) " +
                "        and a.payment_time is not null " +
                "        and a.termination != 5 " +
                "        and a.biz_type = 2 " +
                "        and a.type = 1 " +
                "        and a.customer_id not in (" + testerIds + ") " +
                "        and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "        and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "        and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "        and a.is_deleted = 0 " +
                "        and b.is_deleted = 0 " +
                "        and c.is_deleted = 0 " +
                "        and a.ds = " + ds +


                "        and b.ds = " + ds +
                "        and c.ds = " + ds +
                "      group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type," +
                "               IF(a.parent_id > 0, 1,0),b.rate_config_type, " +
                "               IF(!ISNULL(b.ext_json) and b.rate_config_type  in( 10,30),'1', '0'), c.current_overdue_days) " +
                "e" +
                "   group by e.level, e.day,e.parent_day, e.mini_type, e.isrenew," +
                "            e.forcedconversion, e.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = "   order by e.level, e.day,e.parent_day, e.mini_type, e.isrenew," +
                    "            e.forcedconversion, e.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer level = Integer.valueOf(record.getString("level"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal buyoutAmt = stringToDecimal(record.getString("buyout_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                switch (level) {
                    case 0:
                        financeReportDO.setOverZeroBuyoutAmt(CalculateUtil.add(financeReportDO.getOverZeroBuyoutAmt(),
                                buyoutAmt));
                        financeReportDO.setOverZeroBuyoutCount(financeReportDO.getOverZeroBuyoutCount() + count);
                        break;
                    case 1:
                        financeReportDO.setOverFirstBuyoutAmt(CalculateUtil.add(financeReportDO.getOverFirstBuyoutAmt(),
                                buyoutAmt));
                        financeReportDO.setOverFirstBuyoutCount(financeReportDO.getOverFirstBuyoutCount() + count);
                        break;
                    case 2:
                        financeReportDO.setOverSecondBuyoutAmt(CalculateUtil.add(financeReportDO.getOverSecondBuyoutAmt()
                                , buyoutAmt));
                        financeReportDO.setOverSecondBuyoutCount(financeReportDO.getOverSecondBuyoutCount() + count);
                        break;
                    case 3:
                        financeReportDO.setOverThirdBuyoutAmt(CalculateUtil.add(financeReportDO.getOverThirdBuyoutAmt(),
                                buyoutAmt));
                        financeReportDO.setOverThirdBuyoutCount(financeReportDO.getOverThirdBuyoutCount() + count);
                        break;
                    default:
                        break;
                }
            }
            startPage++;
        }
    }


    /**
     * 逾期滞纳金(所有逾期)
     *
     * @param ds
     */
    private void countTask10(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select e.day, " +
                "       e.parent_day, " +
                "       min(e.rent_end_time) rent_end_time, " +
                "       min(e.parent_end_time) parent_end_time, " +
                "       e.level, " +
                "       e.isrenew, " +
                "       e.mini_type, " +
                "       e.forcedconversion," +
                "       e.rate_config_type, " +
                "       sum(e.overdue_fine)     overdue_fine, " +
                "       sum(e.count)            count " +
                "from ( " +
                "         select date(a.rent_start_date)   day, " +
                "                date(k.rent_start_date)   parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "                a.mini_type, " +
                "                b.rate_config_type, " +
                "                IF(a.parent_id > 0, 1, 0) isrenew, " +
                "                IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0') as forcedconversion," +
                "                sum(d.overdue_fine)       overdue_fine, " +
                "                count(distinct a.id)      count, " +
                "                (case " +
                "                     when c.current_overdue_days = 0 then 0 " +
                "                     when c.current_overdue_days <= 7 then 1 " +
                "                     when c.current_overdue_days <= 31 then 2 " +
                "                     else 3 end) as       level " +
                "         from rent_order a " +
                "                  inner join rent_order_finance_detail b on a.id = b.order_id " +
                "                  inner join rent_order_overdue_stat c on a.id = c.order_id " +
                "                  inner join rent_order_item j on a.id = j.order_id and j.item_type = 1 " +
                "                     and j.is_deleted = 0  and j.type_class != 1 and j.ds = " + ds +
                "                  inner join rent_order_repayment_plan d on a.id = d.order_id " +
                "                  left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "         where a.merchant_id in (100, 10000107) " +
                "           and a.payment_time is not null " +
                "           and a.termination != 5 " +
                "           and a.biz_type = 2 " +
                "           and a.type = 1 " +


                "           and a.customer_id not in ( "
                + testerIds +
                "             ) " +
                "           and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "           and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "           and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "           and d.repay_status =1 " +
                "           and a.is_deleted = 0 " +
                "           and b.is_deleted = 0 " +
                "           and c.is_deleted = 0 " +
                "           and d.is_deleted = 0 " +
                "           and a.ds = " + ds +
                "           and b.ds = " + ds +
                "           and c.ds = " + ds +
                "           and d.ds = " + ds +
                "         group by date(a.rent_start_date), " +
                "                  date(k.rent_start_date)," +
                "                  a.mini_type, " +
                "                  IF(a.parent_id > 0, 1, 0), " +
                "                  b.rate_config_type , " +
                "                  IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0')," +
                "                  c.current_overdue_days " +
                "     ) e " +
                "group by e.level, " +
                "         e.parent_day, " +
                "         e.day, " +
                "         e.mini_type, " +
                "         e.isrenew, " +
                "         e.forcedconversion," +
                "         e.rate_config_type";


        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by e.level, " +
                    "         e.parent_day, " +
                    "         e.day, " +
                    "         e.mini_type, " +
                    "         e.isrenew, " +
                    "         e.forcedconversion," +
                    "         e.rate_config_type" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer level = Integer.valueOf(record.getString("level"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal overdueFine = stringToDecimal(record.getString("overdue_fine"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                switch (level) {
                    case 1:
                        financeReportDO.setOverFirstOverDueAmt(CalculateUtil.add(financeReportDO.getOverFirstOverDueAmt()
                                , overdueFine));
                        financeReportDO.setOverFirstOverDueCount(financeReportDO.getOverFirstOverDueCount() + count);
                        break;
                    case 2:
                        financeReportDO.setOverSecondOverDueAmt(CalculateUtil.add(financeReportDO.getOverSecondOverDueAmt(), overdueFine));
                        financeReportDO.setOverSecondOverDueCount(financeReportDO.getOverSecondOverDueCount() + count);
                        break;
                    case 3:
                        financeReportDO.setOverThirdOverDueAmt(CalculateUtil.add(financeReportDO.getOverThirdOverDueAmt()
                                , overdueFine));
                        financeReportDO.setOverThirdOverDueCount(financeReportDO.getOverThirdOverDueCount() + count);
                        break;
                    default:
                        break;
                }
            }
            startPage++;
        }
    }


    /**
     * 剩余总租金(所有逾期)
     *
     * @param ds
     */
    private void countTask11(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        // 12+N
        String sql = " select e.day, " +
                "       e.parent_day," +
                "       min(e.rent_end_time) rent_end_time, " +
                "       min(e.parent_end_time) parent_end_time, " +
                "       e.level, " +
                "       e.isrenew, " +
                "       e.mini_type, " +
                "       e.forcedconversion, " +
                "       e.rate_config_type, " +
                "       sum(e.capital) capital, " +
                "       sum(e.count)   count " +
                " from (select date(a.rent_start_date)                                                     day, " +
                "             date(k.rent_start_date)   parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "             a.mini_type, " +
                "             b.rate_config_type, " +
                "             IF(a.parent_id > 0, 1, 0) isrenew, " +
                "             IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30), '1', '0')   forcedconversion, " +
                "             sum( case when b.rate_config_type in( 10,30)  " +
                "                       then if( a.status not in (200,210),d.capital,0)     " +
                "               else    if( a.status not in (200,210)  and d.term<=b.repayment_term,d.capital,0) " +
                " end)     " +
                "   capital," +
                "             count(distinct a.id)                                                        count, " +
                "             (case " +
                "                  when c.current_overdue_days = 0 then 0 " +
                "                  when c.current_overdue_days <= 7 then 1 " +
                "                  when c.current_overdue_days <= 31 then 2 " +
                "                  else 3 end)                                           as               level " +
                "      from rent_order a " +
                "               inner join rent_order_finance_detail b on a.id = b.order_id  " +
                "                                                             and b.is_deleted = 0 and b.ds = " + ds +
                "               inner join rent_order_overdue_stat c on a.id = c.order_id  " +
                "                                                           and c.is_deleted = 0 and c.ds = " + ds +
                "               inner join rent_order_item j on a.id = j.order_id  " +
                "                                                   and j.item_type = 1 and j.is_deleted = 0  " +
                "                                                   and j.type_class != 1 and j.ds = " + ds +
                "               left join rent_order_repayment_plan d on a.id = d.order_id  " +
                "                                                             and d.repay_status = 1  " +
                "                                                             and d.is_deleted = 0 and d.ds = " + ds +
                "               left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                "      where a.merchant_id in (100, 10000107) " +
                "        and a.payment_time is not null " +
                "        and a.termination != 5 " +
                "        and a.biz_type = 2 " +
                "        and a.type = 1 " +


                "        and a.customer_id not in (" + testerIds + ") " +
                "        and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "        and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "        and b.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "        and a.is_deleted = 0 " +
                "        and a.ds = " + ds +
                "      group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                "               IF(a.parent_id > 0, 1, 0), " +
                "               b.rate_config_type," +
                "               IF(!ISNULL(b.ext_json) and b.rate_config_type in( 10,30),'1', '0'), c.current_overdue_days) " +
                "e" +
                " group by e.level, e.day,e.parent_day, e.mini_type, e.isrenew, " +
                "          e.forcedconversion, e.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by e.level, e.day,e.parent_day, e.mini_type, e.isrenew, " +
                    "          e.forcedconversion, e.rate_config_type" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer level = Integer.valueOf(record.getString("level"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal capital = stringToDecimal(record.getString("capital"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                switch (level) {
                    case 0:
                        financeReportDO.setOverZeroSurplusRentAmt(CalculateUtil.add(financeReportDO.getOverZeroSurplusRentAmt(), capital));
                        financeReportDO.setOverZeroSurplusRentCount(financeReportDO.getOverZeroSurplusRentCount() + count);
                        break;
                    case 1:
                        financeReportDO.setOverFirstSurplusRentAmt(CalculateUtil.add(financeReportDO.getOverFirstSurplusRentAmt(), capital));
                        financeReportDO.setOverFirstSurplusRentCount(financeReportDO.getOverFirstSurplusRentCount() + count);
                        break;
                    case 2:
                        financeReportDO.setOverSecondSurplusRentAmt(CalculateUtil.add(financeReportDO.getOverSecondSurplusRentAmt(), capital));
                        financeReportDO.setOverSecondSurplusRentCount(financeReportDO.getOverSecondSurplusRentCount() + count);
                        break;
                    case 3:
                        financeReportDO.setOverThirdSurplusRentAmt(CalculateUtil.add(financeReportDO.getOverThirdSurplusRentAmt(), capital));
                        financeReportDO.setOverThirdSurplusRentCount(financeReportDO.getOverThirdSurplusRentCount() + count);
                        break;
                    default:
                        break;
                }
            }
            startPage++;
        }
    }


    /**
     * 售前优惠(基础项)
     *
     * @param ds
     */
    private void countTask12(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') forcedconversion, " +
                "       IF(a.parent_id > 0, 1, 0)                                     isrenew, " +
                "       sum(c.activity_discount_amt)                                          write_off_amt, " +
                "       sum(count)                                                    count " +
                " from rent_order a " +
                "         inner join (select order_id, sum(if(isbefore=1,total_before_write_off_amt,0)) write_off_amt, " +
                "                      sum(if(isbefore=1,1,0)) count" +
                "                     from (select x.order_id, " +
                "                                  if(!isnull(min(x.bind_order_time)) and " +
                "                                   min(y.payment_time) >= min(x.bind_order_time),1,0)  isbefore, " +
                "                                   min(z.total_before_write_off_amt)  total_before_write_off_amt  " +
                "                           from rent_customer_coupon x inner join rent_order y on x.order_id = y.id " +
                "                                   inner join (select x.order_id,sum(write_off_amt) total_before_write_off_amt\n" +
                "                                        from (select order_id\n" +
                "                                                   , nvl(x.write_off_amt, 0)                                                                                               write_off_amt\n" +
                "                                                   , if(x.type in (2, 4), 1, row_number() over( partition by x.order_id,x.use_term order by nvl(x.write_off_amt,0) desc )) rowid\n" +
                "                                              from rent_customer_coupon x\n" +
                "                                                       inner join rent_order y on x.order_id = y.id\n" +
                "                                              where x.order_id > 0\n" +
                "                                                and x.scene = 1\n" +
                "                                                and x.is_deleted = 0\n" +
                "                                                and x.ds = " + ds +
                "                                                and x.bind_order_time is not null\n" +
                "                                                and y.payment_time >= x.bind_order_time\n" +
                "                                                and y.ds = " + ds +
                "                                                and y.is_deleted = 0) x\n" +
                "                                        where x.rowid=1\n" +
                "                                        group by x.order_id) z on z.order_id= y.id" +
                "                           where x.order_id > 0 and x.scene = 1 " +
                "                             and x.is_deleted = 0 " +
                "                             and y.is_deleted = 0 " +
                "                             and x.ds = " + ds +
                "                             and y.ds = " + ds +
                "                           group by x.order_id) " +
                "                     group by order_id) b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                       and c.is_deleted = 0 and c.ds = " + ds +
                "         inner join rent_order_item d " +
                "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0" +
                "                       and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in (" + testerIds + ") " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +


                "  and a.ds = " + ds +
                " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0')";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal writeOffAmt = stringToDecimal(record.getString("write_off_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setBasePreDiscount(CalculateUtil.add(financeReportDO.getBasePreDiscount(), writeOffAmt));
                financeReportDO.setBasePreDiscountCount(financeReportDO.getBasePreDiscountCount() + count);
            }
            startPage++;
        }
    }


    /**
     * 售后减免租金(已收租金)
     *
     * @param ds
     */
    private void countTask13(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') as forcedconversion, " +
                "       IF(a.parent_id > 0, 1, 0)                     isrenew, " +
                "       sum(if(c.rate_config_type <> 30,get_json_object(b.ext, '$.planCouponAmount'),0)) plan_coupon_amt," +
                "       count(1)                                                      count " +
                "from rent_order a " +
                "         inner join rent_order_flow b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 " +
                "                     and d.is_deleted = 0  and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in ( " +
                testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and b.biz_type in (3,34) " +
                "  and b.pay_status = 10 " +
                "  and (b.mark_refund is null or b.mark_refund = 0) " +
                "  and b.flow_type = 1 " +
                "  and b.refunded_amt = 0 " +
                "  and b.ext is not null " +
                "  and b.ext != '' " +
                "  and get_json_object(b.ext, '$.planCouponAmount') is not null " +
                "  and get_json_object(b.ext, '$.planCouponAmount') >0 " +
                "  and a.is_deleted = 0 " +
                "  and b.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0')";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         c.rate_config_type, " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal planCouponAmt = stringToDecimal(record.getString("plan_coupon_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setRentDeductAfterRent(CalculateUtil.add(financeReportDO.getRentDeductAfterRent(),
                        planCouponAmt));
                financeReportDO.setRentDeductAfterRentCount(financeReportDO.getRentDeductAfterRentCount() + count);
            }
            startPage++;
        }
    }


    /**
     * 售前实收减免租金、运营实收减免租金(已收租金)、预计售前收入
     *
     * @param ds
     */
    private void countTask14(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') forcedconversion, " +
                "       IF(a.parent_id > 0, 1, 0)                                     isrenew, " +
                "       sum(manage_write_off_amt)                                     manage_write_off_amt, " +
                "       sum(manage_count)                                             manage_count, " +
                "       sum(before_write_off_amt)                                     before_write_off_amt, " +
                "       sum(before_count)                                             before_count, " +
                "       sum(expected_income)                                          expected_income, " +
                "       sum(expected_income_count)                                    expected_income_count " +
                " from rent_order a " +
                "         inner join (select order_id, " +
                "                            sum(manage_write_off_amt)  manage_write_off_amt, " +
                "                            sum(manage_count)            manage_count, " +
                "                            sum(before_write_off_amt)  before_write_off_amt, " +
                "                            sum(before_count)            before_count, " +
                "                            sum(expected_income) expected_income, " +
                "                            sum(expected_income_count)           expected_income_count " +
                "                     from (select x.order_id, " +
                "                                  if(ISNULL(min(x.bind_order_time)) or " +
                "                                         min(y.payment_time) < min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0), 0) manage_write_off_amt," +
                "                                  if((ISNULL(min(x.bind_order_time)) or " +
                "                                         min(y.payment_time) < min(x.bind_order_time)) and  " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0)>0,1, 0) manage_count, " +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1, " +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1, " +
                "                                            x.write_off_amt, 0)), 0), 0) before_write_off_amt," +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time) and  " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0)>0,1, 0) before_count, " +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) != 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status != 1," +
                "                                            x.write_off_amt, 0)), 0), 0) expected_income, " +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time) and  " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) != 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status != 1," +
                "                                            x.write_off_amt, 0)), 0)>0,1, 0) expected_income_count " +
                "                           from rent_customer_coupon x " +
                "                                    inner join rent_order y on x.order_id = y.id " +
                "                           where x.order_id > 0 and x.scene = 1 " +
                "                             and x.is_deleted = 0 " +
                "                             and y.is_deleted = 0 " +
                "                             and x.ds = " + ds +
                "                             and y.ds = " + ds +
                "                           group by x.order_id, x.type) " +
                "                     group by order_id) b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join rent_order_item d " +
                "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                       and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in (" + testerIds + ") " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +


                "  and a.ds = " + ds +
                "  and c.ds = " + ds +
                " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type," +
                "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0')";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date),date(k.rent_start_date), a.mini_type," +
                    "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0')" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal manageWriteOffAmt = stringToDecimal(record.getString("manage_write_off_amt"));
                BigDecimal beforeWriteOffAmt = stringToDecimal(record.getString("before_write_off_amt"));
                BigDecimal expectedIncome = stringToDecimal(record.getString("expected_income"));
                Integer manageCount = Integer.valueOf(record.getString("manage_count"));
                Integer beforeCount = Integer.valueOf(record.getString("before_count"));
                Integer expectedIncomeCount = Integer.valueOf(record.getString("expected_income_count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setRentDeductMarketing(CalculateUtil.add(financeReportDO.getRentDeductMarketing(),
                        manageWriteOffAmt));
                financeReportDO.setRentDeductMarketingCount(financeReportDO.getRentDeductMarketingCount() + manageCount);
                financeReportDO.setBaseActBeforeRentDiscountAmt(CalculateUtil.add(financeReportDO.getBaseActBeforeRentDiscountAmt(),
                        beforeWriteOffAmt));
                financeReportDO.setBaseActBeforeRentDiscountCount(financeReportDO.getBaseActBeforeRentDiscountCount() + beforeCount);
                financeReportDO.setExpectedIncomeAmt(CalculateUtil.add(financeReportDO.getExpectedIncomeAmt(),
                        expectedIncome));
                financeReportDO.setExpectedIncomeCount(financeReportDO.getExpectedIncomeCount() + expectedIncomeCount);
            }
            startPage++;
        }
    }


    /**
     * 售后减免买断金(已收买断金)、运营减免买断金、保证金抵扣买断、已收买断金
     *
     * @param ds
     */
    private void countTask15(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                          day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') as forcedconversion, " +
                "       IF(a.parent_id > 0, 1, 0)                                        isrenew, " +
                "       sum(if(c.rate_config_type not in( 10,30)  and c.repayment_term != 6,b.buyoutActFlowAmt - b.discountReturnAmt,0))                        " +
                "                                                               user_actual_buyout_amt, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutActFlowAmt) " +
                "                   and b.buyoutActFlowAmt > 0,1,0))        user_actual_buyout_amt_count, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutReduceAmount) ,b.buyoutReduceAmount,0))  buyout_reduce_amount,     " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutRebateDiscountAmt) ,b.buyoutRebateDiscountAmt,0))  buyout_rebate_discount_amt,     " +

                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutCouponAmount) and !isnull(b.buyoutCouponId) and b.buyoutCouponAmount > 0 and b.buyoutCouponId > 0 ,b.buyoutCouponAmount,0))       " +
                "                                                              manage_buyout_discount, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutCouponAmount) and !isnull(b.buyoutCouponId)  and b.buyoutCouponAmount > 0 and b.buyoutCouponId > 0 ,1,0)) manage_buyout_discount_count, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutCouponAmount) and b.buyoutCouponId = 0,b.buyoutCouponAmount,0))              " +
                "                                                              offline_buyout_discount, " +
                "       sum(if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(b.buyoutCouponAmount) and b.buyoutCouponId = 0 " +
                "                   and b.buyoutCouponAmount > 0,1,0))      offline_buyout_discount_count " +
                " from rent_order a " +
                "         inner join (select sum(if(biz_type in (3),get_json_object(ext,'$.buyoutActAmt'),0)) buyoutActFlowAmt,\n" +
                "       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutCouponAmount') is not null,get_json_object(ext,'$.buyoutCouponAmount'),0)) buyoutCouponAmount,\n" +
                "       sum(if(biz_type in (3) and get_json_object(ext,'$.discountReturnAmt') is not null,get_json_object(ext,'$.discountReturnAmt'),0)) discountReturnAmt,\n" +
                "       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutReduceAmount') is not null,get_json_object(ext,'$.buyoutReduceAmount'),0)) buyoutReduceAmount,\n" +
                "       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutCouponId') is not null ,get_json_object(ext,'$.buyoutCouponId'),0)) buyoutCouponId,\n" +
                "       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutRebateDiscountAmt') is not null,get_json_object(ext,'$.buyoutRebateDiscountAmt'),0)) buyoutRebateDiscountAmt,\n" +
                "       order_id\n" +
                "       from rent_order_flow\n" +
                "       where biz_type = 3\n" +
                "         and pay_status = 10\n" +
                "         and flow_type = 1\n" +
                "         and is_deleted = 0\n" +
                "         and get_json_object(ext,'$.buyoutActAmt') >= 0\n" +
                "         and (mark_refund is null or mark_refund = 0)\n" +
                "         and ext is not null  and ext != '' and ds = "+ds+"\n" +
                "         GROUP BY order_id ) b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id = c.order_id and c.is_deleted = 0 " +
                "                   and c.ds = " + ds +
                "         inner join rent_order_item d " +
                "                    on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                   and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in (" + testerIds + ") " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ") " +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "  and a.is_deleted = 0 " +


                "  and a.ds = " + ds +
                " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), c.rate_config_type, " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                // 用户实际支付买断金
                BigDecimal userActualBuyoutAmt = stringToDecimal(record.getString("user_actual_buyout_amt"));
                Integer userActualBuyoutAmtCount = Integer.valueOf(record.getString("user_actual_buyout_amt_count"));
                // 运营减免买断金
                BigDecimal manageBuyoutDiscount = stringToDecimal(record.getString("manage_buyout_discount"));
                Integer manageBuyoutDiscountCount = Integer.valueOf(record.getString("manage_buyout_discount_count"));
                // 售后减免买断金
                BigDecimal offlineBuyoutDiscount = stringToDecimal(record.getString("offline_buyout_discount"));
                Integer offlineBuyoutDiscountCount = Integer.valueOf(record.getString("offline_buyout_discount_count"));

                // 买断金售后减免
                BigDecimal buyoutReduceAmount = stringToDecimal(record.getString("buyout_reduce_amount"));
                // 买断金返利抵扣
                BigDecimal buyoutRebateDiscountAmt = stringToDecimal(record.getString("buyout_rebate_discount_amt"));

                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));

                financeReportDO.setBuyoutReceiveBuyoutAmt(CalculateUtil.add(financeReportDO.getBuyoutReceiveBuyoutAmt(),
                        userActualBuyoutAmt));
                financeReportDO.setBuyoutReceiveBuyoutCount(financeReportDO.getBuyoutReceiveBuyoutCount() + userActualBuyoutAmtCount);

                financeReportDO.setBuyoutReduceMarketingAmt(CalculateUtil.add(financeReportDO.getBuyoutReduceMarketingAmt(), manageBuyoutDiscount));
                financeReportDO.setBuyoutReduceMarketingCount(financeReportDO.getBuyoutReduceMarketingCount() + manageBuyoutDiscountCount);

                financeReportDO.setBuyoutReduceAfterAmt(CalculateUtil.add(financeReportDO.getBuyoutReduceAfterAmt(),
                        offlineBuyoutDiscount.add(buyoutReduceAmount).add(buyoutRebateDiscountAmt)));
                financeReportDO.setBuyoutReduceAfterCount(financeReportDO.getBuyoutReduceAfterCount() + offlineBuyoutDiscountCount);
            }
            startPage++;
        }
    }


    /**
     * 其他收入:优惠返还
     *
     * @param ds
     */
    private void countTask16(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') as forcedconversion, " +
                "       IF(a.parent_id > 0, 1, 0)                     isrenew, " +
                "       sum(get_json_object(ext, '$.discountReturnAmt'))        discount_return_amt, " +
                "       count(1)                                                      count " +
                "from rent_order a " +
                "         inner join rent_order_flow b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 " +
                "                     and d.is_deleted = 0  and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in ( " +
                testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and b.biz_type = 3 " +
                "  and b.pay_status = 10 " +
                "  and (b.mark_refund is null or b.mark_refund = 0) " +
                "  and b.flow_type = 1 " +
                "  and b.refunded_amt = 0 " +
                "  and b.ext is not null " +
                "  and b.ext != '' " +
                "  and get_json_object(ext, '$.discountReturnAmt') is not null " +
                "  and get_json_object(ext, '$.discountReturnAmt') >0 " +
                "  and a.is_deleted = 0 " +
                "  and b.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +


                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         c.rate_config_type, " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')";


        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         c.rate_config_type, " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal discountReturnAmt = stringToDecimal(record.getString("discount_return_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setOtherDiscountReturnAmt(CalculateUtil.add(financeReportDO.getOtherDiscountReturnAmt(),
                        discountReturnAmt));
                financeReportDO.setOtherDiscountReturnCount(financeReportDO.getOtherDiscountReturnCount() + count);
            }
            startPage++;
        }
    }


    /**
     * 3+N已收总额
     *
     * @param ds
     */
    private void countTask17(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                                     day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       IF(a.status in (200,210),1,0)                       isreturn," +
                "       a.mini_type, " +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type =10, '1', '0')             forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       sum(d.capital*12) rent_total," +
                "       sum(d.act_rent) act_rent," +
                "       sum(d.report_reduce_amt) report_reduce_amt," +
                "       sum(c.actual_buyout_amt) actual_buyout_amt, " +
                "       count(1)                                                                    count " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 " +
                "                                             and b.type_class != 1 and b.is_deleted = 0 " +
                "                                             and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                                                       and c.is_deleted = 0 and c.ds = " + ds +
                "         inner join (select order_id,avg(capital) capital," +
                "               sum(if(repay_status = 5,real_repay_capital,0)) act_rent," +
                "               sum(report_reduce_amt) report_reduce_amt" +
                "                                                from rent_order_repayment_plan  " +
                "                                                 where is_deleted = 0 " +
                "                                                    and ds = " + ds + " group by order_id) d " +
                "                                                    on a.id = d.order_id" +
                "         left join rent_order_flow g on a.id = g.order_id and g.biz_type = 3 " +
                "                and g.pay_status = 10 " +
                "                and g.flow_type = 1 " +
                "                and g.refunded_amt = 0 " +
                "                and g.ext is not null " +
                "                and g.ext != ''" +
                "                and (g.mark_refund is null or g.mark_refund = 0) " +
                "                and g.is_deleted = 0  " +
                "                and g.parent_id = 0" +
                "                and g.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in (" + testerIds + ") " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and c.rate_config_type = 10 " +
                "  and a.status in(200,210,220)" +
                "  and a.is_deleted = 0 " +


                "  and a.ds = " + ds +
                " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'), " +
                "         IF(a.status in (200,210),1,0), " +
                "         c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type=10, '1', '0'), " +
                    "         IF(a.status in (200,210),1,0), " +
                    "         c.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer isReturn = Integer.valueOf(record.getString("isreturn"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal actualBuyoutAmt = stringToDecimal(record.getString("actual_buyout_amt"));
                BigDecimal rentTotal = stringToDecimal(record.getString("rent_total"));
                BigDecimal reportReduceAmt = stringToDecimal(record.getString("report_reduce_amt"));
                rentTotal = rentTotal.subtract(reportReduceAmt);
                BigDecimal actRent = stringToDecimal(record.getString("act_rent"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                // 3+N结清优惠 协议总租金-实收总额-已收租金
                if (isReturn == 0) {
                    financeReportDO.setEndDiscountAmt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
                            actualBuyoutAmt)));
                    financeReportDO.setEndDiscountCount(financeReportDO.getEndDiscountCount() + count);
                } else {
                    financeReportDO.setEndReturnNotRepayAmt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
                            actualBuyoutAmt)));
                    financeReportDO.setEndReturnNotRepayCount(financeReportDO.getEndReturnNotRepayCount() + count);
                }
            }
            startPage++;
        }

    }


    /**
     * 保证金抵扣赔偿金(已收买断金)
     *
     * @param ds
     */
    private void countTask18(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                       day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       a.mini_type, " +
                "       c.rate_config_type, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0') as forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       sum(b.bond_deduct_damage_amt)  bond_deduct_damage_amt, " +
                "       sum(if(b.bond_deduct_damage_amt is not null,1,0)) count " +
                " from rent_order a " +
                "       inner join (select sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=2,flow_amt," +
                "               null)) bond_deduct_damage_amt," +
                "               order_id from rent_order_flow  " +
                "                                   where biz_type in (23) " +
                "                                   and pay_status = 10 " +
                "                                   and flow_type =1 " +
                "                                   and (mark_refund is null or mark_refund = 0) " +
                "                                   and refunded_amt = 0 " +
                "                                   and ext is not null " +
                "                                   and ext != '' " +
                "                                   and is_deleted = 0 " +
                "                                   and ds = " + ds +
                "                          group by order_id) b on a.id = b.order_id" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join rent_order_item d on a.id = d.order_id and d.item_type = 1 and d.is_deleted = 0 " +
                "                                   and d.type_class != 1 and d.ds = " + ds +
                "         left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +


                "  and a.customer_id not in ( "
                + testerIds +
                "    ) " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ")" +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and c.ds = " + ds +
                " group by date(a.rent_start_date), " +
                "         date(k.rent_start_date),  " +
                "         a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                "         c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date), " +
                    "         date(k.rent_start_date),  " +
                    "         a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type  in( 10,30), '1', '0')," +
                    "         c.rate_config_type" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal bondDeductDamageAmt = stringToDecimal(record.getString("bond_deduct_damage_amt"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                financeReportDO.setBondDeductDamageAmt(CalculateUtil.add(financeReportDO.getBondDeductDamageAmt(),
                        bondDeductDamageAmt));
                financeReportDO.setBondDeductDamageCount(financeReportDO.getBondDeductDamageCount() + count);
            }
            startPage++;
        }
    }

    /**
     * 6+6已收总额
     *
     * @param ds
     */
    private void countTask19(String ds, String testerIds, Map<String, FinanceReportDO> collectMap) {
        String sql = " select date(a.rent_start_date)                                                     day, " +
                "       date(k.rent_start_date)                             parent_day," +
                "           min(date(a.rent_end_date))                                    rent_end_time," +
                "           min(date(k.rent_end_date))                                    parent_end_time," +
                "       IF(a.status in (200,210),1,0)                       isreturn," +
                "       a.mini_type, " +
                "       c.rate_config_type," +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type =30, '1', '0')             forcedconversion," +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       sum(d.capital*12) rent_total," +
                "       sum(d.act_rent) act_rent," +
                "       sum(d.report_reduce_amt) report_reduce_amt," +
                "       sum(c.actual_buyout_amt) actual_buyout_amt, " +
                "       count(1)                                                                    count " +
                " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 " +
                "                                             and b.type_class != 1 and b.is_deleted = 0 " +
                "                                             and b.ds = " + ds +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "                                                       and c.is_deleted = 0 and c.ds = " + ds +
                "         inner join (select order_id,avg(capital) capital," +
                "               sum(if(repay_status = 5,real_repay_capital,0)) act_rent," +
                "               sum(report_reduce_amt) report_reduce_amt" +
                "                                                from rent_order_repayment_plan  " +
                "                                                 where is_deleted = 0 " +
                "                                                    and ds = " + ds + " group by order_id) d " +
                "                                                    on a.id = d.order_id" +
                "         left join rent_order_flow g on a.id = g.order_id and g.biz_type = 3 " +
                "                and g.pay_status = 10 " +
                "                and g.flow_type = 1 " +
                "                and g.refunded_amt = 0 " +
                "                and g.ext is not null " +
                "                and g.ext != ''" +
                "                and (g.mark_refund is null or g.mark_refund = 0) " +
                "                and g.is_deleted = 0  " +
                "                and g.parent_id = 0" +
                "                and g.ds = " + ds +
                "         left join ( select id,rent_start_date ,rent_end_date" +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) k on k.id = a.parent_id " +
                " where a.merchant_id in (100, 10000107) " +
                "  and a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in (" + testerIds + ") " +
                "  and a.mini_type not in (" + EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + EFFECTIVE_ORDER_STATUS + ") " +
                "  and c.rate_config_type not in (" + EXCEPT_FINANCE_TYPE + ") " +
                "  and c.rate_config_type = 30 " +
                "  and a.status in(200,210,220)" +
                "  and a.is_deleted = 0 " +


                "  and a.ds = " + ds +
                " group by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                "         IF(a.parent_id > 0, 1, 0), " +
                "         IF(!ISNULL(c.ext_json) and c.rate_config_type=30, '1', '0'), " +
                "         IF(a.status in (200,210),1,0), " +
                "         c.rate_config_type";

        String countSql = "select count(*) num from (" + sql + ") ;";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String suffix = " order by date(a.rent_start_date),date(k.rent_start_date), a.mini_type, " +
                    "         IF(a.parent_id > 0, 1, 0), " +
                    "         IF(!ISNULL(c.ext_json) and c.rate_config_type=30, '1', '0'), " +
                    "         IF(a.status in (200,210),1,0), " +
                    "         c.rate_config_type " +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
            String pageSql = sql + suffix;
            List<Record> records = odpsUtil.querySql(pageSql);
            for (Record record : records) {
                LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
                LocalDateTime parentDay = stringTOLocalDate(record.getString("parent_day"));
                Integer miniType = Integer.valueOf(record.getString("mini_type"));
                Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
                Integer isRenew = Integer.valueOf(record.getString("isrenew"));
                Integer isReturn = Integer.valueOf(record.getString("isreturn"));
                Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
                BigDecimal actualBuyoutAmt = stringToDecimal(record.getString("actual_buyout_amt"));
                BigDecimal rentTotal = stringToDecimal(record.getString("rent_total"));
                BigDecimal reportReduceAmt = stringToDecimal(record.getString("report_reduce_amt"));
                rentTotal = rentTotal.subtract(reportReduceAmt);
                BigDecimal actRent = stringToDecimal(record.getString("act_rent"));
                Integer count = Integer.valueOf(record.getString("count"));
                // Integer financeType = FinanceTypeEnum.getFinanceTypeNew(rateConfigType, forcedConversion);
                LocalDateTime rentEndTime = LocalDateTime.of(LocalDate.parse(record.getString("rent_end_time")), LocalTime.MIN);
                LocalDateTime parentEndTime = stringTOLocalDate(record.getString("parent_end_time"));
                initMap(collectMap, miniType, rateConfigType, day, rentEndTime, parentDay, parentEndTime);
                FinanceReportDO financeReportDO = collectMap.get(getMapKey(miniType, rateConfigType, day, rentEndTime,
                        parentDay, parentEndTime));
                // 3+N结清优惠 协议总租金-实收总额-已收租金
                if (isReturn == 0) {
                    financeReportDO.setEndDiscountFor6Amt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
                            actualBuyoutAmt)));
                    financeReportDO.setEndDiscountFor6Count(financeReportDO.getEndDiscountCount() + count);
                } else {
                    financeReportDO.setEndReturnNotRepayFor6Amt(CalculateUtil.sub(rentTotal, CalculateUtil.add(actRent,
                            actualBuyoutAmt)));
                    financeReportDO.setEndReturnNotRepayFor6Count(financeReportDO.getEndReturnNotRepayCount() + count);
                }
            }
            startPage++;
        }

    }


    private void initMap(Map<String, FinanceReportDO> miniType2Map,
                         Integer miniType,
                         Integer financeType,
                         LocalDateTime countDay,
                         LocalDateTime rentEndDate,
                         LocalDateTime parentDay,
                         LocalDateTime parentEndDate) {
        String key = getMapKey(miniType, financeType, countDay, rentEndDate, parentDay, parentEndDate);
        if (!miniType2Map.containsKey(key)) {
            FinanceReportDO ivd = new FinanceReportDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setRentEndDate(rentEndDate);
            ivd.setParentDay(parentDay);
            ivd.setParentEndDate(parentEndDate);
            ivd.setFinanceType(financeType);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType,
                             Integer financeType,
                             LocalDateTime countDay,
                             LocalDateTime rentEndDate,
                             LocalDateTime parentDay,
                             LocalDateTime parentEndDate
    ) {
        String str = "";
        String endStr = "";
        if (parentDay != null) {
            str = DateUtils.dateToString(parentDay);
        }
        if (parentEndDate != null) {
            endStr = DateUtils.dateToString(parentEndDate);
        }
        return String.join("_", miniType.toString(), financeType.toString(), DateUtils.dateToString(countDay),
                DateUtils.dateToString(rentEndDate), str, endStr);
    }


    private BigDecimal stringToDecimal(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return new BigDecimal(val).setScale(2, RoundingMode.HALF_UP);
    }

    private LocalDateTime stringTOLocalDate(String val) {
        if (val.equals("\\N")) {
            return null;
        }
        return LocalDateTime.of(LocalDate.parse(val), LocalTime.MIN);
    }

}