package qnvip.data.overview.business.dataindicators;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorLossOrderCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorLossOrderCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/2/9
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLossOrderCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorLossOrderCustomerService indicatorLossOrderCustomerService;


    /**
     * 启动计算任务（计算一天的数据）
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorLossOrderCustomerDO> miniType2Map = new HashMap<>();
        //一天天计算取消下单人数
        List<LocalDateTime> dateTimeList = DateUtils.getDateTimeList(DateUtils.stringToDate(sTime),
                DateUtils.stringToDate(eTime));
        for (LocalDateTime time : dateTimeList) {
            LocalDateTime startTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MIN);
            LocalDateTime endTime = LocalDateTime.of(time.toLocalDate(), LocalTime.MAX);
            countRegisterAndNotOrder(ds, DateUtils.dateToString(startTime), DateUtils.dateToString(endTime),
                    miniType2Map);
            countLtNdRefusedCount(ds, DateUtils.dateToString(startTime), DateUtils.dateToString(endTime), miniType2Map);
        }
        countLtNhRegisterCount(ds, sTime, eTime, miniType2Map);
        countServiceOrder(ds, sTime, eTime, true, miniType2Map);
        countServiceOrder(ds, sTime, eTime, false, miniType2Map);
        for (IndicatorLossOrderCustomerDO value : miniType2Map.values()) {
            indicatorLossOrderCustomerService.saveOrUpdate(value);
        }
    }


    /**
     * 下单放弃人数中 N小时内注册人数（一天一天查询）
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorLossOrderCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                        inner join rent_customer c " +
                "                                   on get_json_object(c.bindinfo, '$[0].miniUserId') = a" +
                ".customer_third_id " +
                "                        inner join ( " +
                "                   select id, day, mini_type " +
                "                   from ( " +
                "                            select  b.mini_type " +
                "                                    ,c.id " +
                "                                    ,to_char(report_time, 'yyyy-mm-dd') as day " +
                "                            from    dataview_track_enter_applets b inner " +
                "                            join    rent_customer c " +
                "                            on      get_json_object(c.bindinfo, '$[0].miniUserId') = b" +
                ".customer_third_id " +
                "                            where   b.action_type = 2 " +
                "                            and     c.id not in ( " +
                "                                select customer_id from rent_order  " +
                "                                    where is_deleted = 0  " +
                "                                    and type = 1  " +
                "                                    and parent_id = 0  " +
                "                                    and biz_type = 2  " +
                "                                    and merchant_id = 100  " +
                "                                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                                    and ds = " + ds +
                "                                    group by customer_id " +
                "                            ) " +
                "                            and     b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011," +
                "10012,10014) " +
                "                            and     b.ds = " + ds +
                "                            and     c.ds = " + ds +
                "                            and     b.report_time between '" + sTime + "' " +
                "                            and     '" + eTime + "' " +
                "                            group by b.mini_type " +
                "                                     ,to_char(b.report_time, 'yyyy-mm-dd') " +
                "                                     ,c.id " +
                "                            having  sum(b.keep_alive_time) > " + IndicatorBeforeSaleBusiness.WANT_N_MILL_SECOND +
                "                        ) x " +
                "                   group by id, mini_type, day " +
                "               ) b on a.mini_type = b.mini_type and c.id = b.id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and c.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' " +
                "                   and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                "where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorLossOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(count);
        }
    }


    /**
     * 下单放弃人数中 当天注册人数（一天一天查询）
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRegisterAndNotOrder(String ds, String sTime, String eTime,
                                          Map<String, IndicatorLossOrderCustomerDO> collectMap) {
        String sql = " select  a.day " +
                "        ,a.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  b.mini_type " +
                "                    ,to_char(report_time, 'yyyy-mm-dd') as day " +
                "            from    dataview_track_enter_applets b inner " +
                "            join    rent_customer c " +
                "            on      get_json_object(c.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "            where   b.action_type = 2 " +
                "            and     c.id not in ( " +
                "                select customer_id from rent_order  " +
                "                    where is_deleted = 0  " +
                "                    and type = 1  " +
                "                    and parent_id = 0  " +
                "                    and biz_type = 2  " +
                "                    and merchant_id = 100  " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                    and ds = " + ds +
                "                    group by customer_id " +
                "            ) " +
                "            and     b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "            and     b.ds = " + ds +
                "            and     c.ds = " + ds +
                "            and     b.report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     c.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            group by b.mini_type " +
                "                     ,to_char(b.report_time, 'yyyy-mm-dd') " +
                "                     ,c.id " +
                "            having  sum(b.keep_alive_time) > " + IndicatorBeforeSaleBusiness.WANT_N_MILL_SECOND +
                "        ) a " +
                "group by a.mini_type " +
                "         ,a.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterAndNotOrder(count);
        }
    }


    /**
     * 下单放弃人数中 服务中/结束客户数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countServiceOrder(String ds, String sTime, String eTime, Boolean inServiceFlag,
                                   Map<String, IndicatorLossOrderCustomerDO> collectMap) {
        String sql = " select  a.day " +
                "        ,a.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  b.mini_type " +
                "                    ,to_char(report_time, 'yyyy-mm-dd') as day " +
                "            from    dataview_track_enter_applets b inner " +
                "            join    rent_customer c " +
                "            on      get_json_object(c.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "            inner join rent_order e on c.id = e.customer_id " +
                "            where   b.action_type = 2 " +
                "            and     b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "            and     b.ds = " + ds +
                "            and     c.ds = " + ds +
                "            and     e.ds = " + ds +
                "            and     e.merchant_id = 100 " +
                "            and     e.type = 1 " +
                "            and     e.biz_type = 2 " +
                "            and     c.id not in ( " +
                "                select customer_id from rent_order  " +
                "                    where is_deleted = 0  " +
                "                    and type = 1  " +
                "                    and parent_id = 0  " +
                "                    and biz_type = 2  " +
                "                    and merchant_id = 100  " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                    and ds = " + ds +
                "                    group by customer_id " +
                "            ) " +
                "            and     e.is_deleted = 0 " +
                "            and     e.payment_time is not null " +
                "            and     e.termination != 5 " +
                "            and     e.status " + (inServiceFlag ? "not" : "") + " in (220,330)" +
                "            and     b.report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            group by b.mini_type " +
                "                     ,to_char(b.report_time, 'yyyy-mm-dd') " +
                "                     ,c.id " +
                "            having  sum(b.keep_alive_time) > " + IndicatorBeforeSaleBusiness.WANT_N_MILL_SECOND +
                "        ) a " +
                "group by a.mini_type " +
                "         ,a.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (inServiceFlag) {
                updateDO.setInServiceCount(count);
            } else {
                updateDO.setOutServiceCount(count);
            }
        }
    }


    /**
     * 下单放弃人数中 N天内被据人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNdRefusedCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorLossOrderCustomerDO> collectMap) {
        String sql = " select  a.day " +
                "        ,a.mini_type " +
                "        ,count(1) as count " +
                "from    ( " +
                "            select  b.mini_type " +
                "                    ,to_char(report_time, 'yyyy-mm-dd') as day " +
                "            from    dataview_track_enter_applets b inner " +
                "            join    rent_customer c " +
                "            on      get_json_object(c.bindinfo, '$[0].miniUserId') = b.customer_third_id " +
                "            inner join ( " +
                "                select max(risktime) risktime , max(id) order_id, customer_id " +
                "                from ( " +
                "                         select b.risktime , a.customer_id , a.id " +
                "                         from rent_order a " +
                "                                  inner join cl_loan b on a.no = b.loanno " +
                "                         where a.is_deleted = 0 " +
                "                           and a.merchant_id = 100 " +
                "                           and a.parent_id = 0 " +
                "                           and a.type = 1 " +
                "                           and a.biz_type = 2 " +
                "                           and b.riskstatus in (15, 25) " +
                "                           and a.ds = " + ds +
                "                           and b.ds = " + ds +
                "                         order by b.risktime asc " +
                "                     ) " +
                "                group by customer_id " +
                "            ) e on c.id = e.customer_id " +
                "            where   b.action_type = 2 " +
                "            and     b.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "            and     b.ds = " + ds +
                "            and     c.ds = " + ds +
                "            and     b.report_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     c.id not in ( " +
                "                select customer_id from rent_order  " +
                "                    where is_deleted = 0  " +
                "                    and type = 1  " +
                "                    and parent_id = 0  " +
                "                    and biz_type = 2  " +
                "                    and merchant_id = 100  " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "'  " +
                "                    and ds = " + ds +
                "                    group by customer_id " +
                "            ) " +
                "            and datediff(b.report_time, e.risktime) < " + IndicatorBeforeSaleBusiness.N_DAYS +
                "            group by b.mini_type " +
                "                     ,to_char(b.report_time, 'yyyy-mm-dd') " +
                "                     ,c.id " +
                "            having  sum(b.keep_alive_time) > " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "        ) a " +
                " group by a.mini_type,a.day;  ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossOrderCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(count);
        }
    }


    private void initMap(Map<String, IndicatorLossOrderCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorLossOrderCustomerDO ioid = new IndicatorLossOrderCustomerDO();
            ioid.setMiniType(miniType);
            ioid.setCountDay(countDay);
            ioid.setLtNhRegisterCount(0);
            ioid.setRegisterAndNotOrder(0);
            ioid.setInServiceCount(0);
            ioid.setOutServiceCount(0);
            ioid.setLtNdRefusedCount(0);
            miniType2Map.put(key, ioid);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}