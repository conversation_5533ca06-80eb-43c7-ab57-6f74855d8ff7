package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.ActivityDO;
import qnvip.data.overview.service.order.ActivityService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/8 9:15 上午
 */
@Component
@RequiredArgsConstructor
public class ActivityBusiness {

    private final OdpsUtil odpsUtil;

    private final ActivityService activityService;

    /**
     * 启动活动计算
     *
     * @param sTime
     * @param eTime
     */
    public void runActivityCount(String dsStr, LocalDateTime countDay, String sTime, String eTime) {
        Map<Long, ActivityDO> map = new HashMap<>();
        List<ActivityDO> list = orderCountByActivity(dsStr, sTime, eTime);
        for (ActivityDO activityDO : list) {
            initMap(map, activityDO, countDay);
            ActivityDO updateDO = map.get(activityDO.getActivityId());
            updateDO.setOrderCount(activityDO.getOrderCount());
        }
        list = auditActivityCount(dsStr, sTime, eTime);
        for (ActivityDO activityDO : list) {
            initMap(map, activityDO, countDay);
            ActivityDO updateDO = map.get(activityDO.getActivityId());
            updateDO.setAuditCount(activityDO.getAuditCount());
        }
        list = payCountByActivity(dsStr, sTime, eTime);
        for (ActivityDO activityDO : list) {
            initMap(map, activityDO, countDay);
            ActivityDO updateDO = map.get(activityDO.getActivityId());
            updateDO.setPayCount(activityDO.getPayCount());
        }
        list = signCountByActivity(dsStr, sTime, eTime);
        for (ActivityDO activityDO : list) {
            initMap(map, activityDO, countDay);
            ActivityDO updateDO = map.get(activityDO.getActivityId());
            updateDO.setSignCount(activityDO.getSignCount());
        }

        list = getPvAndUv(dsStr, sTime, eTime);
        for (ActivityDO activityDO : list) {
            initMap(map, activityDO, countDay);
            ActivityDO updateDO = map.get(activityDO.getActivityId());
            updateDO.setPv(activityDO.getPv());
            updateDO.setUv(activityDO.getUv());
        }
        long totalOrderCount = 0;
        long totalPayCount = 0;
        long totalSignCount = 0;
        long totalAuditCount = 0;
        long totalOverdueCount = 0;
        long totalPv = 0;
        long totalUv = 0;


        for (Map.Entry<Long, ActivityDO> entry : map.entrySet()) {
            ActivityDO activityDO = entry.getValue();
            //活动当前逾期订单数
            Long overdueCount = queryActivityOverdue(dsStr, activityDO.getActivityId());
            activityDO.setOverdueCount(overdueCount);
            activityService.saveOrUpdate(activityDO);
            totalOrderCount += activityDO.getOrderCount();
            totalPayCount += activityDO.getPayCount();
            totalSignCount += activityDO.getSignCount();
            totalAuditCount += activityDO.getAuditCount();
            totalOverdueCount += activityDO.getOverdueCount();
            totalPv += activityDO.getPv();
            totalUv += activityDO.getUv();
        }
        // 计算总数据
        ActivityDO total = new ActivityDO();
        total.setCountDay(countDay);
        total.setActivityName("");
        total.setActivityId(-1l);
        total.setOrderCount(totalOrderCount);
        total.setPayCount(totalPayCount);
        total.setSignCount(totalSignCount);
        total.setAuditCount(totalAuditCount);
        total.setOverdueCount(totalOverdueCount);
        total.setType(2);
        total.setUv(totalUv);
        total.setPv(totalPv);

        //查询总的活动逾期订单数

        activityService.saveOrUpdate(total);

    }

    void initMap(Map<Long, ActivityDO> map, ActivityDO activityDO, LocalDateTime countDay) {
        if (!map.containsKey(activityDO.getActivityId())) {
            ActivityDO ac = new ActivityDO();
            ac.setCountDay(countDay);
            ac.setActivityName(activityDO.getActivityName());
            ac.setActivityId(activityDO.getActivityId());
            ac.setOrderCount(0l);
            ac.setPayCount(0l);
            ac.setSignCount(0l);
            ac.setAuditCount(0l);
            ac.setOverdueCount(0l);
            ac.setPv(0l);
            ac.setUv(0l);
            ac.setType(1);
            map.put(activityDO.getActivityId(), ac);
        }
    }

    /**
     * @param fromTime
     * @param toTime
     */
    public List<ActivityDO> getPvAndUv(
            String dsStr,
            String fromTime,
            String toTime) {
        // 访客数、访问量
        String sql = "SELECT b.id as                           activity_id," +
                "       count(a.customer_third_id)          pv," +
                "       count(distinct a.customer_third_id) uv" +
                " FROM DATAVIEW_TRACK_PARTICIPATE_ACTIVITIES a" +
                "         inner join rent_activity b" +
                "                    on a.activity_id = b.uid" +
                " WHERE b.ds = ${dsStr}" +
                "  AND a.ds = ${dsStr}" +
                "  AND REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " GROUP BY b.id;";
        HashMap<String, Object> key2value = Maps.newHashMap();
        key2value.put("fromTime", "'".concat(fromTime).concat("'"));
        key2value.put("toTime", "'".concat(toTime).concat("'"));
        key2value.put("dsStr", dsStr);
        String sql1 = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(sql1);
        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            ActivityDO domain = new ActivityDO();
            String pv = Optional.ofNullable(record.getString("pv")).orElse("0");
            String activityId = StringUtils.isBlank(record.getString("activity_id")) ? "0" : record.getString("activity_id");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");

            domain.setPv(Long.valueOf(pv));
            domain.setUv(Long.valueOf(uv));
            domain.setActivityId(Long.valueOf(activityId));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 活动下单量 按活动id分组
     * 所有活动数据还需代码上额外统计一次
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ActivityDO> orderCountByActivity(String dsStr, String sTime, String eTime) {

        StringBuilder sb = new StringBuilder(" select b.activity_id as activityid, b.activity_name as activityname, count(1)" +
                " as num from rent_order a inner join rent_order_infomore b on a.id = b.order_id where a.biz_type = 2 and" +
                " a.merchant_id = 100 and a.is_deleted = 0 and b.activity_id > 0 ");
        //指定分区
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by b.activity_id, b.activity_name;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<ActivityDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            ActivityDO activityDO = new ActivityDO();
            activityDO.setActivityId(Long.parseLong(record.getString("activityid")));
            activityDO.setActivityName(record.getString("activityname"));
            activityDO.setOrderCount(Long.parseLong(record.getString("num")));
            list.add(activityDO);
        }
        return list;
    }


    /**
     * 活动下单审核通过量 按活动id分组
     * 所有活动数据还需代码上额外统计一次
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ActivityDO> auditActivityCount(String dsStr, String sTime, String eTime) {

        StringBuilder sb = new StringBuilder(" select c.activity_id as activityid, c.activity_name as activityname,count(1)" +
                " as num from rent_order a inner join rent_order_audit b on a.id=b.order_id inner join rent_order_infomore c" +
                " on a.id = c.order_id where biz_type = 2 and a.merchant_id = 100 and a.parent_id = 0 and b.type = 2 and" +
                " b.audit_status = 1 and c.activity_id > 0 and a.is_deleted = 0 and b.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and b.operate_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append("  and b.operate_time <=  ").append("'").append(eTime).append("'");
        }
        sb.append(" group by c.activity_id, c.activity_name;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<ActivityDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            ActivityDO activityDO = new ActivityDO();
            activityDO.setActivityId(Long.parseLong(record.getString("activityid")));
            activityDO.setActivityName(record.getString("activityname"));
            activityDO.setAuditCount(Long.parseLong(record.getString("num")));
            list.add(activityDO);
        }
        return list;
    }


    /**
     * 活动支付单量 按活动id分组
     * 所有活动数据还需代码上额外统计一次
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ActivityDO> payCountByActivity(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select b.activity_id as activityid, b.activity_name as activityname, count(1)" +
                " as num from rent_order a inner join rent_order_infomore b on a.id = b.order_id where a.biz_type = 2 and" +
                " a.merchant_id = 100 and b.activity_id > 0 and a.payment_time is not null and a.is_deleted = 0 and b.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by b.activity_id, b.activity_name;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<ActivityDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            ActivityDO activityDO = new ActivityDO();
            activityDO.setActivityId(Long.parseLong(record.getString("activityid")));
            activityDO.setActivityName(record.getString("activityname"));
            activityDO.setPayCount(Long.parseLong(record.getString("num")));
            list.add(activityDO);
        }
        return list;
    }


    /**
     * 活动签收单量 按活动id分组
     * 所有活动数据还需代码上额外统计一次
     *
     * @param sTime
     * @param eTime
     * @return
     */
    public List<ActivityDO> signCountByActivity(String dsStr, String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select c.activity_id as activityid, c.activity_name as activityname,count(1) " +
                " as num from rent_order a inner join rent_order_logistics b on a.id = b.order_id inner join rent_order_infomore" +
                " c on a.id = c.order_id where a.biz_type = 2 and a.merchant_id = 100 and c.activity_id>0 and b.sign_time" +
                " is not null and a.is_deleted = 0 and b.is_deleted = 0 and c.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and b.sign_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and b.sign_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(" group by c.activity_id, c.activity_name;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return new ArrayList<>();
        }
        List<ActivityDO> list = new ArrayList<>(recordList.size());
        for (Record record : recordList) {
            ActivityDO activityDO = new ActivityDO();
            activityDO.setActivityId(Long.parseLong(record.getString("activityid")));
            activityDO.setActivityName(record.getString("activityname"));
            activityDO.setSignCount(Long.parseLong(record.getString("num")));
            list.add(activityDO);
        }
        return list;
    }


    /**
     * 活动逾期订单数，定时任务只查全量数据，具体活动需要查odps
     *
     * @return
     */
    public Long queryActivityOverdue(String dsStr, Long activityId) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from rent_order a inner join" +
                " rent_order_overdue_stat b on a.id = b.order_id inner join rent_order_infomore c on a.id = c.order_id " +
                " where a.biz_type = 2 and a.merchant_id = 100 and a.is_deleted = 0 and b.is_deleted = 0 and c.is_deleted = 0");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (activityId != null) {
            sb.append(" and c.activity_id = ").append(activityId);
        }
        sb.append(" and c.activity_id>0 and b.current_overdue_days > 0;");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        if (CollectionUtils.isEmpty(recordList)) {
            return 0L;
        }
        long num = Long.parseLong(recordList.get(0).getString("num"));
        return num;
    }

    public static void main(String[] args) {
        StringBuilder sb = new StringBuilder(" select b.activity_id as activityid, b.activity_name as activityname, count(1)" +
                " as num from rent_order a inner join rent_order_infomore b on a.id = b.order_id where a.biz_type = 2 and" +
                " a.merchant_id = 100 and a.is_deleted = 0 and b.activity_id > 0 ");
        //指定分区
        sb.append(" and a.ds = ").append(22);
        sb.append(" and b.ds = ").append(22);
        sb.append(" and a.create_time >= ").append("'").append(22).append("'");
        sb.append(" and a.create_time <= ").append("'").append(22).append("'");
        sb.append(" group by b.activity_id, b.activity_name;");
        System.out.println(sb.toString());
    }

}