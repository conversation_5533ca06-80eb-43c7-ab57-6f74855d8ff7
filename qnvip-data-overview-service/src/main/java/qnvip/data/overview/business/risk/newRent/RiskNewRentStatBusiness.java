package qnvip.data.overview.business.risk.newRent;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.DataviewRiskNewRentJobLogDO;
import qnvip.data.overview.domain.risk.DataviewRiskNewRentOrderInfoDO;
import qnvip.data.overview.domain.risk.RiskNewRentOrderInfoVo;
import qnvip.data.overview.mapper.rent.OaAuditLogMapper;
import qnvip.data.overview.param.job.JobParam;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.DataviewRiskNewRentJobLogService;
import qnvip.data.overview.service.risk.DataviewRiskNewRentOrderInfoService;
import qnvip.data.overview.util.DingDingAlarmUtil;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.rent.common.enums.StatusEnum;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * All things are difficult before they are easy. by: zl
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskNewRentStatBusiness {
    private final OdpsUtil odpsUtil;
    private final DingDingAlarmUtil dingDingAlarmUtil;

    private final DataviewRiskNewRentOrderInfoService rentOrderInfoService;
    private final DataviewRiskNewRentJobLogService jobLogService;

    private final OaAuditLogMapper oaAuditLogMapper;

    private final DateTimeFormatter FORMAT_TIME = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private final DateTimeFormatter FORMAT_TIME_TIDB = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.S");
    private final DateTimeFormatter FORMAT_TIME_TIDB2 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SS");
    private final DateTimeFormatter FORMAT_TIME_TIDB3 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSS");
    private final DateTimeFormatter FORMAT_TIME_TIDB4 = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss.SSSS");
    private final DateTimeFormatter FORMAT_DATE = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final RentOrderService rentOrderService;


    private final AtomicInteger ATOMIC_INTEGER = new AtomicInteger();
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(1, 3, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(2048), r -> {
        Thread t = new Thread(r);
        t.setName("RiskNewRentStatBusiness-" + ATOMIC_INTEGER.incrementAndGet());
        return t;
    });


    public void runRiskNewRentOrderSys(JobParam jobParam) {
        String errorInfo = "";
        DataviewRiskNewRentJobLogDO jobLogDO = new DataviewRiskNewRentJobLogDO();
        jobLogDO.setJobTime(jobParam.getJobTime());
        jobLogDO.setJobName(jobParam.getJobName());
        try {
            log.info("开始同步新租赁明细, param={}", JSONUtil.toJsonStr(jobParam));
            // 获取sql明细
            String prefix = "WITH dt AS( SELECT DATE_FORMAT(now(), 'yyyymmdd') AS ds) ";
            String body = getSqlBody(jobParam);
            String countSql = prefix + "select count(*) num from (" + body + ") ;";
            jobLogDO.setStatSql(countSql);
            Integer size = getCount(countSql);
            log.info("{}, 总数={}", jobParam.getJobName(), size);
            jobLogDO.setStatCount(size);
            Integer PAGE_SIZE = 10000;
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }

            for (int startPage = 0; startPage < times; startPage++) {
                List<DataviewRiskNewRentOrderInfoDO> list = Lists.newArrayList();
                String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
                String pageSql = prefix + body + suffix;
                List<Record> records = odpsUtil.querySql(pageSql);

                if (CollUtil.isEmpty(records)) {
                    continue;
                }
                list.addAll(assemble(records));
                batchSave(list);
                // rentOrderInfoService.saveOrUpdateBatch(list,2000);
            }
            jobLogDO.setStatus(StatusEnum.YES.getCode());
        } catch (Exception e) {
            errorInfo = e.getMessage();
            log.error("数据大盘生产环境-数据总览-新租赁报错:",jobParam.getJobName() , e);
        } finally {
            jobLogDO.setErrorInfo(errorInfo);
            jobLogService.save(jobLogDO);
            log.info(jobParam.getJobName() + " 结束");
        }
    }

    public void runRiskNewRentOrderSysTidb(JobParam jobParam) {
        String errorInfo = "";
        DataviewRiskNewRentJobLogDO jobLogDO = new DataviewRiskNewRentJobLogDO();
        jobLogDO.setJobTime(jobParam.getJobTime());
        jobLogDO.setJobName(jobParam.getJobName());
        try {
            log.info("tidb开始同步新租赁明细, param={}", JSONUtil.toJsonStr(jobParam));
            // 获取sql明细
            String prefix = " ";
            String body = getSqlBody(jobParam);
            String countSql = prefix + "select count(*) num from (" + body + ") a;";
            jobLogDO.setStatSql(countSql);
            Integer size = getCount(countSql);
            log.info("{}, 总数={}", jobParam.getJobName(), size);
            jobLogDO.setStatCount(size);
            Integer PAGE_SIZE = 10000;
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }

            for (int startPage = 0; startPage < times; startPage++) {
                List<DataviewRiskNewRentOrderInfoDO> list = Lists.newArrayList();
                String suffix = " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ";";
                String pageSql = prefix + body + suffix;
//                List<Record> records = odpsUtil.querySql(pageSql);
//                List<Map<String, String>> records = rentOrderService.getGeneralCommonSql(pageSql);
                List<RiskNewRentOrderInfoVo> records=rentOrderService.getNewRentOrderInfo(pageSql);
                if (CollUtil.isEmpty(records)) {
                    continue;
                }
                list.addAll(assembleTidb(records));
                batchSave(list);
                // rentOrderInfoService.saveOrUpdateBatch(list,2000);
            }
            jobLogDO.setStatus(StatusEnum.YES.getCode());
        } catch (Exception e) {
            errorInfo = e.getMessage();
            log.error(jobParam.getJobName() + " 异常 ", e);
        } finally {
            jobLogDO.setErrorInfo(errorInfo);
            log.info(jobParam.getJobName() + " 结束");
        }
    }

    private void batchSave(List<DataviewRiskNewRentOrderInfoDO> list) {
        List<List<DataviewRiskNewRentOrderInfoDO>> partition = Lists.partition(list, 5000);
        log.info("批量插入风控-新租赁明细分区数 num={}", partition.size());
        for (List<DataviewRiskNewRentOrderInfoDO> infoDOS : partition) {
            if (CollUtil.isEmpty(infoDOS)) {
                continue;
            }
            try {
                List<String> noList = infoDOS.stream().map(DataviewRiskNewRentOrderInfoDO::getOrderNo).collect(Collectors.toList());
                // 获取信审人员数据
            //    List<String> auditList = oaAuditLogMapper.queryOrderNo(noList.stream().collect(Collectors.joining(
               //     "','", "'", "'")));
                // 删除
                rentOrderInfoService.lambdaUpdate().in(DataviewRiskNewRentOrderInfoDO::getOrderNo, noList).remove();
                // 插入
                rentOrderInfoService.saveOrUpdateBatch(infoDOS, 2500);
                // 更新信审
//                if (CollUtil.isNotEmpty(auditList)) {
//                    rentOrderInfoService.lambdaUpdate()
//                            .in(DataviewRiskNewRentOrderInfoDO::getOrderNo,auditList)
//                            .set(DataviewRiskNewRentOrderInfoDO::getRentArtificialAuditFlag,1)
//                            .update();
//                }
                log.info("批量插入风控-新租赁明细size={}", infoDOS.size());
            } catch (Exception e) {
                log.error("批量插入风控-新租赁明细异常", e);
//                dingDingAlarmUtil.dataViewProExceptionAlarm("批量插入风控-新租赁明细异常 ", e);
            }

        }
    }


    public String getSqlBody(JobParam jobParam) {
        StringBuilder builder = new StringBuilder();
        builder.append("SELECT oi.need_risk_auth,cl.createtime risk_create_time,o.create_time rent_create_time, cl.updatetime risk_update_time, sn.rhtime risk_rh_time, ol.send_time rent_send_time, cl.sendtime risk_send_time, o.payment_time rent_payment_time, cl.paytime risk_pay_time, cl.loanno " +
                "order_no, cl.customerid risk_customer_id, o.customer_id rent_customer_id, o.merchant_id rent_merchant_id, cl.merchantid risk_merchant_id, o.termination rent_termination, o.status rent_order_status, cl.closestatus risk_close_status, cl.businesschannel risk_business_channel, o.type rent_type, o.mini_type rent_mini_type, oi.common_rent_flag rent_common_rent_flag, cl.corent risk_co_rent, item.equipment_state rent_equipment_state, item.equipment_type rent_equipment_type, cl.supervisorymachine risk_supervisory_machine, cl.scene risk_scene, os.quotient_id rent_quotient_id, cl.quotientcode risk_quotient_code, cl.quotientname risk_quotient_name, cl.quotientScene risk_quotient_scene, os.order_source_tag rent_order_source_tag, ofd.rate_config_type rent_rate_config_type, cl.financetemplatetype risk_finance_template_type, ofd.bond_free_status rent_bond_free_status, cl.riskFicoStatus risk_fico_status, cl.artificialauditstatus risk_artificial_audit_status, cl.riskopinion risk_opinion, mc.platform_code rent_platform_code, o.merchant_transfer rent_merchant_transfer, IF( cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 20, 1, 0 ) front1_pass_flag, IF( cl.riskFicoStatus IS NOT NULL AND cl.riskFicoStatus > 30, 1, 0 ) front2_pass_flag, IF( cl.riskFicoStatus IS NOT NULL AND ( ( cl.riskFicoStatus = 60 AND cl.artificialauditstatus = 10 ) OR cl.riskFicoStatus > 60 ) AND REPLACE (riskopinion, '风控等级', '') <= 10, 1, 0 ) risk_pass_flag, IF( cl.riskFicoStatus IS NOT NULL AND ( ( cl.riskFicoStatus = 60 AND cl.artificialauditstatus = 10 ) OR cl.riskFicoStatus > 60 ) AND REPLACE (riskopinion, '风控等级', '') IN (11, 12, 13), 1, 0 ) labour_audit_flag, IF( cl.riskFicoStatus IS NOT NULL AND riskFicoStatus > 65 AND artificialAuditStatus = 10 AND REPLACE (riskopinion, '风控等级', '') IN (11, 12, 13), 1, 0 ) credit_audit_pass_flag FROM alchemist.cl_loan cl LEFT JOIN alchemist.serial_no sn ON sn.businessno = cl.loanno  LEFT JOIN qnvip_rent.rent_order o ON cl.loanno = o.no AND o.is_deleted = 0 AND o.type = 1 AND o.parent_id = 0  LEFT JOIN qnvip_rent.rent_order_finance_detail ofd ON o.id = ofd.order_id AND ofd.is_deleted = 0   LEFT JOIN qnvip_rent.rent_order_infomore oi ON o.id = oi.order_id AND oi.is_deleted = 0  LEFT JOIN qnvip_rent.rent_order_item item ON o.id = item.order_id AND item.item_type = 1 AND item.is_deleted = 0 LEFT JOIN qnvip_rent.rent_order_source os ON o.id = os.order_id AND os.is_deleted = 0  LEFT JOIN qnvip_rent.rent_order_logistics ol ON o.id = ol.order_id AND ol.type = 0 AND ol.is_deleted = 0  LEFT JOIN qnvip_rent.rent_mini_config mc ON o.mini_type = mc.mini_type AND mc.is_deleted = 0 ");
        builder.append("WHERE  cl.businessChannel NOT IN(28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019) AND cl.parentNo is not null and cl.parentNo = '' ");
        if (jobParam.getInitFlag()) {
            builder.append(" AND cl.createtime >= '{startTime}' and cl.createtime <= '{endTime}' ORDER BY cl.id DESC ");
        } else {
            builder.append(" AND cl.updateTime >= '{startTime}' and cl.updateTime <= '{endTime}' ORDER BY cl.id DESC ");
        }
        return builder.toString().replace("{startTime}", jobParam.getStartTime()).replace("{endTime}", jobParam.getEndTime());
    }

    /**
     * 获取order最大记录数
     */
    private Integer getCount(String sql) {
        // List<Record> records = odpsUtil.querySql(sql);
        List<Map<String, String>> records = rentOrderService.getGeneralCommonSql(sql);
        Object num = records.get(0).get("num");
        return ((Long) num).intValue();
    }

    /**
     * 排除了订单号为空的订单
     */
    private List<DataviewRiskNewRentOrderInfoDO> assemble(List<Record> records) {
        return records.stream().map(merged -> {
            DataviewRiskNewRentOrderInfoDO orderInfoDO = new DataviewRiskNewRentOrderInfoDO();

            String needRiskAuth = merged.getString("need_risk_auth");
            if (StringUtils.isNotBlank(needRiskAuth) && !Objects.equals(needRiskAuth, "\\N") && !Objects.equals(needRiskAuth, "NaN")) {
                orderInfoDO.setNeedRiskAuth(Integer.valueOf(needRiskAuth));
            }
            String riskCreateTime = merged.getString("risk_create_time");
            if (StringUtils.isNotBlank(riskCreateTime) && !Objects.equals(riskCreateTime, "\\N") && !Objects.equals(riskCreateTime, "NaN")) {
                orderInfoDO.setRiskCreateTime(LocalDateTime.parse(riskCreateTime, FORMAT_TIME));
            }
            String rentCreateTime = merged.getString("rent_create_time");
            if (StringUtils.isNotBlank(rentCreateTime) && !Objects.equals(rentCreateTime, "\\N") && !Objects.equals(rentCreateTime, "NaN")) {
                orderInfoDO.setRentCreateTime(LocalDateTime.parse(rentCreateTime, FORMAT_TIME));
            }
            String riskUpdateTime = merged.getString("risk_update_time");
            if (StringUtils.isNotBlank(riskUpdateTime) && !Objects.equals(riskUpdateTime, "\\N") && !Objects.equals(riskUpdateTime, "NaN")) {
                orderInfoDO.setRiskUpdateTime(LocalDateTime.parse(riskUpdateTime, FORMAT_TIME));
            }
            String riskRhTime = merged.getString("risk_rh_time");
            if (StringUtils.isNotBlank(riskRhTime) && !Objects.equals(riskRhTime, "\\N") && !Objects.equals(riskRhTime, "NaN")) {
                orderInfoDO.setRiskRhTime(LocalDateTime.parse(riskRhTime, FORMAT_TIME));
            }
            String rentSendTime = merged.getString("rent_send_time");
            if (StringUtils.isNotBlank(rentSendTime) && !Objects.equals(rentSendTime, "\\N") && !Objects.equals(rentSendTime, "NaN")) {
                orderInfoDO.setRentSendTime(LocalDateTime.parse(rentSendTime, FORMAT_TIME));
            }
            String riskSendTime = merged.getString("risk_send_time");
            if (StringUtils.isNotBlank(riskSendTime) && !Objects.equals(riskSendTime, "\\N") && !Objects.equals(riskSendTime, "NaN")) {
                orderInfoDO.setRiskSendTime(riskSendTime);
            }
            String rentPaymentTime = merged.getString("rent_payment_time");
            if (StringUtils.isNotBlank(rentPaymentTime) && !Objects.equals(rentPaymentTime, "\\N") && !Objects.equals(rentPaymentTime, "NaN")) {
                orderInfoDO.setRentPaymentTime(LocalDateTime.parse(rentPaymentTime, FORMAT_TIME));
            }
            String riskPayTime = merged.getString("risk_pay_time");
            if (StringUtils.isNotBlank(riskPayTime) && !Objects.equals(riskPayTime, "\\N") && !Objects.equals(riskPayTime, "NaN")) {
                orderInfoDO.setRiskPayTime(LocalDateTime.parse(riskPayTime, FORMAT_TIME));
            }
            String orderNo = merged.getString("order_no");
            if (StringUtils.isNotBlank(orderNo) && !Objects.equals(orderNo, "\\N") && !Objects.equals(orderNo, "NaN")) {
                orderInfoDO.setOrderNo(orderNo);
            }
            String riskCustomerId = merged.getString("risk_customer_id");
            if (StringUtils.isNotBlank(riskCustomerId) && !Objects.equals(riskCustomerId, "\\N") && !Objects.equals(riskCustomerId, "NaN")) {
                orderInfoDO.setRiskCustomerId(Long.valueOf(riskCustomerId));
            }
            String rentCustomerId = merged.getString("rent_customer_id");
            if (StringUtils.isNotBlank(rentCustomerId) && !Objects.equals(rentCustomerId, "\\N") && !Objects.equals(rentCustomerId, "NaN")) {
                orderInfoDO.setRentCustomerId(Long.valueOf(rentCustomerId));
            }
            String rentMerchantId = merged.getString("rent_merchant_id");
            if (StringUtils.isNotBlank(rentMerchantId) && !Objects.equals(rentMerchantId, "\\N") && !Objects.equals(rentMerchantId, "NaN")) {
                orderInfoDO.setRentMerchantId(Long.valueOf(rentMerchantId));
            }
            String riskMerchantId = merged.getString("risk_merchant_id");
            if (StringUtils.isNotBlank(riskMerchantId) && !Objects.equals(riskMerchantId, "\\N") && !Objects.equals(riskMerchantId, "NaN")) {
                orderInfoDO.setRiskMerchantId(Long.valueOf(riskMerchantId));
            }
            String rentTermination = merged.getString("rent_termination");
            if (StringUtils.isNotBlank(rentTermination) && !Objects.equals(rentTermination, "\\N") && !Objects.equals(rentTermination, "NaN")) {
                orderInfoDO.setRentTermination(Integer.valueOf(rentTermination));
            }
            String rentOrderStatus = merged.getString("rent_order_status");
            if (StringUtils.isNotBlank(rentOrderStatus) && !Objects.equals(rentOrderStatus, "\\N") && !Objects.equals(rentOrderStatus, "NaN")) {
                orderInfoDO.setRentOrderStatus(Integer.valueOf(rentOrderStatus));
            }
            String riskCloseStatus = merged.getString("risk_close_status");
            if (StringUtils.isNotBlank(riskCloseStatus) && !Objects.equals(riskCloseStatus, "\\N") && !Objects.equals(riskCloseStatus, "NaN")) {
                orderInfoDO.setRiskCloseStatus(Integer.valueOf(riskCloseStatus));
            }
            String riskBusinessChannel = merged.getString("risk_business_channel");
            if (StringUtils.isNotBlank(riskBusinessChannel) && !Objects.equals(riskBusinessChannel, "\\N") && !Objects.equals(riskBusinessChannel, "NaN")) {
                orderInfoDO.setRiskBusinessChannel(Integer.valueOf(riskBusinessChannel));
            }
            String rentType = merged.getString("rent_type");
            if (StringUtils.isNotBlank(rentType) && !Objects.equals(rentType, "\\N") && !Objects.equals(rentType, "NaN")) {
                orderInfoDO.setRentType(Integer.valueOf(rentType));
            }
            String rentMiniType = merged.getString("rent_mini_type");
            if (StringUtils.isNotBlank(rentMiniType) && !Objects.equals(rentMiniType, "\\N") && !Objects.equals(rentMiniType, "NaN")) {
                orderInfoDO.setRentMiniType(Integer.valueOf(rentMiniType));
            }
            String rentCommonRentFlag = merged.getString("rent_common_rent_flag");
            if (StringUtils.isNotBlank(rentCommonRentFlag) && !Objects.equals(rentCommonRentFlag, "\\N") && !Objects.equals(rentCommonRentFlag, "NaN")) {
                orderInfoDO.setRentCommonRentFlag(Integer.valueOf(rentCommonRentFlag));
            }
            String riskCoRent = merged.getString("risk_co_rent");
            if (StringUtils.isNotBlank(riskCoRent) && !Objects.equals(riskCoRent, "\\N") && !Objects.equals(riskCoRent, "NaN")) {
                orderInfoDO.setRiskCoRent(Integer.valueOf(riskCoRent));
            }
            String rentEquipmentState = merged.getString("rent_equipment_state");
            if (StringUtils.isNotBlank(rentEquipmentState) && !Objects.equals(rentEquipmentState, "\\N") && !Objects.equals(rentEquipmentState, "NaN")) {
                orderInfoDO.setRentEquipmentState(Integer.valueOf(rentEquipmentState));
            }
            String rentEquipmentType = merged.getString("rent_equipment_type");
            if (StringUtils.isNotBlank(rentEquipmentType) && !Objects.equals(rentEquipmentType, "\\N") && !Objects.equals(rentEquipmentType, "NaN")) {
                orderInfoDO.setRentEquipmentType(Integer.valueOf(rentEquipmentType));
            }
            String riskSupervisoryMachine = merged.getString("risk_supervisory_machine");
            if (StringUtils.isNotBlank(riskSupervisoryMachine) && !Objects.equals(riskSupervisoryMachine, "\\N") && !Objects.equals(riskSupervisoryMachine, "NaN")) {
                orderInfoDO.setRiskSupervisoryMachine(Integer.valueOf(riskSupervisoryMachine));
            }
            String riskScene = merged.getString("risk_scene");
            if (StringUtils.isNotBlank(riskScene) && !Objects.equals(riskScene, "\\N") && !Objects.equals(riskScene, "NaN")) {
                orderInfoDO.setRiskScene(riskScene);
            }
            String rentQuotientId = merged.getString("rent_quotient_id");
            if (StringUtils.isNotBlank(rentQuotientId) && !Objects.equals(rentQuotientId, "\\N") && !Objects.equals(rentQuotientId, "NaN")) {
                orderInfoDO.setRentQuotientId(Long.valueOf(rentQuotientId));
            }
            String riskQuotientCode = merged.getString("risk_quotient_code");
            if (StringUtils.isNotBlank(riskQuotientCode) && !Objects.equals(riskQuotientCode, "\\N") && !Objects.equals(riskQuotientCode, "NaN")) {
                orderInfoDO.setRiskQuotientCode(riskQuotientCode);
            }
            String riskQuotientName = merged.getString("risk_quotient_name");
            if (StringUtils.isNotBlank(riskQuotientName) && !Objects.equals(riskQuotientName, "\\N") && !Objects.equals(riskQuotientName, "NaN")) {
                orderInfoDO.setRiskQuotientName(riskQuotientName);
            }
            String riskQuotientScene = merged.getString("risk_quotient_scene");
            if (StringUtils.isNotBlank(riskQuotientScene) && !Objects.equals(riskQuotientScene, "\\N") && !Objects.equals(riskQuotientScene, "NaN")) {
                orderInfoDO.setRiskQuotientScene(riskQuotientScene);
            }
            String rentOrderSourceTag = merged.getString("rent_order_source_tag");
            if (StringUtils.isNotBlank(rentOrderSourceTag) && !Objects.equals(rentOrderSourceTag, "\\N") && !Objects.equals(rentOrderSourceTag, "NaN")) {
                orderInfoDO.setRentOrderSourceTag(Integer.valueOf(rentOrderSourceTag));
                orderInfoDO.setZwDrainageFlag(orderInfoDO.getRentOrderSourceTag() == 1 ? 1 : 0);
            }
            String rentRateConfigType = merged.getString("rent_rate_config_type");
            if (StringUtils.isNotBlank(rentRateConfigType) && !Objects.equals(rentRateConfigType, "\\N") && !Objects.equals(rentRateConfigType, "NaN")) {
                orderInfoDO.setRentRateConfigType(Integer.valueOf(rentRateConfigType));
            }
            String riskFinanceTemplateType = merged.getString("risk_finance_template_type");
            if (StringUtils.isNotBlank(riskFinanceTemplateType) && !Objects.equals(riskFinanceTemplateType, "\\N") && !Objects.equals(riskFinanceTemplateType, "NaN")) {
                orderInfoDO.setRiskFinanceTemplateType(Integer.valueOf(riskFinanceTemplateType));
            }
            String rentBondFreeStatus = merged.getString("rent_bond_free_status");
            if (StringUtils.isNotBlank(rentBondFreeStatus) && !Objects.equals(rentBondFreeStatus, "\\N") && !Objects.equals(rentBondFreeStatus, "NaN")) {
                orderInfoDO.setRentBondFreeStatus(Integer.valueOf(rentBondFreeStatus));
            }
            String riskFicoStatus = merged.getString("risk_fico_status");
            if (StringUtils.isNotBlank(riskFicoStatus) && !Objects.equals(riskFicoStatus, "\\N") && !Objects.equals(riskFicoStatus, "NaN")) {
                orderInfoDO.setRiskFicoStatus(Integer.valueOf(riskFicoStatus));
            }
            String riskArtificialAuditStatus = merged.getString("risk_artificial_audit_status");
            if (StringUtils.isNotBlank(riskArtificialAuditStatus) && !Objects.equals(riskArtificialAuditStatus, "\\N") && !Objects.equals(riskArtificialAuditStatus, "NaN")) {
                orderInfoDO.setRiskArtificialAuditStatus(Integer.valueOf(riskArtificialAuditStatus));
            }
            String riskOpinion = merged.getString("risk_opinion");
            if (StringUtils.isNotBlank(riskOpinion) && !Objects.equals(riskOpinion, "\\N") && !Objects.equals(riskOpinion, "NaN")) {
                orderInfoDO.setRiskOpinion(riskOpinion);
            }
            String rentPlatformCode = merged.getString("rent_platform_code");
            if (StringUtils.isNotBlank(rentPlatformCode) && !Objects.equals(rentPlatformCode, "\\N") && !Objects.equals(rentPlatformCode, "NaN")) {
                orderInfoDO.setRentPlatformCode(rentPlatformCode);
            }
            String rentMerchantTransfer = merged.getString("rent_merchant_transfer");
            if (StringUtils.isNotBlank(rentMerchantTransfer) && !Objects.equals(rentMerchantTransfer, "\\N") && !Objects.equals(rentMerchantTransfer, "NaN")) {
                orderInfoDO.setRentMerchantTransfer(Integer.valueOf(rentMerchantTransfer));
            }
            String front1PassFlag = merged.getString("front1_pass_flag");
            if (StringUtils.isNotBlank(front1PassFlag) && !Objects.equals(front1PassFlag, "\\N") && !Objects.equals(front1PassFlag, "NaN")) {
                orderInfoDO.setFront1PassFlag(Integer.valueOf(front1PassFlag));
            }
            String front2PassFlag = merged.getString("front2_pass_flag");
            if (StringUtils.isNotBlank(front2PassFlag) && !Objects.equals(front2PassFlag, "\\N") && !Objects.equals(front2PassFlag, "NaN")) {
                orderInfoDO.setFront2PassFlag(Integer.valueOf(front2PassFlag));
            }
            String riskPassFlag = merged.getString("risk_pass_flag");
            if (StringUtils.isNotBlank(riskPassFlag) && !Objects.equals(riskPassFlag, "\\N") && !Objects.equals(riskPassFlag, "NaN")) {
                orderInfoDO.setRiskPassFlag(Integer.valueOf(riskPassFlag));
            }
            String labourAuditFlag = merged.getString("labour_audit_flag");
            if (StringUtils.isNotBlank(labourAuditFlag) && !Objects.equals(labourAuditFlag, "\\N") && !Objects.equals(labourAuditFlag, "NaN")) {
                orderInfoDO.setLabourAuditFlag(Integer.valueOf(labourAuditFlag));
            }
            String creditAuditPassFlag = merged.getString("credit_audit_pass_flag");
            if (StringUtils.isNotBlank(creditAuditPassFlag) && !Objects.equals(creditAuditPassFlag, "\\N") && !Objects.equals(creditAuditPassFlag, "NaN")) {
                orderInfoDO.setCreditAuditPassFlag(Integer.valueOf(creditAuditPassFlag));
            }
            return orderInfoDO;
        }).filter(info -> StringUtils.isNotBlank(info.getOrderNo())).collect(Collectors.toList());
    }

    /**
     * 排除了订单号为空的订单
     */
    private List<DataviewRiskNewRentOrderInfoDO> assembleTidb(List<RiskNewRentOrderInfoVo> records) {
        return records.stream().map(merged -> {
            DataviewRiskNewRentOrderInfoDO orderInfoDO = new DataviewRiskNewRentOrderInfoDO();

            String needRiskAuth = merged.getNeedRiskAuth();
            if (StringUtils.isNotBlank(needRiskAuth) && !Objects.equals(needRiskAuth, "\\N") && !Objects.equals(needRiskAuth, "NaN")) {
                orderInfoDO.setNeedRiskAuth(Integer.valueOf(needRiskAuth));
            }
            String riskCreateTime = merged.getRiskCreateTime();
            if (StringUtils.isNotBlank(riskCreateTime) && !Objects.equals(riskCreateTime, "\\N") && !Objects.equals(riskCreateTime, "NaN")) {
                orderInfoDO.setRiskCreateTime(stringToObjectTidb(riskCreateTime, LocalDateTime.class));
            }
            String rentCreateTime = merged.getRentCreateTime();
            if (StringUtils.isNotBlank(rentCreateTime) && !Objects.equals(rentCreateTime, "\\N") && !Objects.equals(rentCreateTime, "NaN")) {
                orderInfoDO.setRentCreateTime(stringToObjectTidb(rentCreateTime, LocalDateTime.class));
            }
            String riskUpdateTime = merged.getRiskUpdateTime();
            if (StringUtils.isNotBlank(riskUpdateTime) && !Objects.equals(riskUpdateTime, "\\N") && !Objects.equals(riskUpdateTime, "NaN")) {
                orderInfoDO.setRiskUpdateTime(stringToObjectTidb(riskUpdateTime, LocalDateTime.class));
            }
            String riskRhTime = merged.getRiskRhTime();
            if (StringUtils.isNotBlank(riskRhTime) && !Objects.equals(riskRhTime, "\\N") && !Objects.equals(riskRhTime, "NaN")) {
                orderInfoDO.setRiskRhTime(stringToObjectTidb(riskRhTime, LocalDateTime.class));
            }
            String rentSendTime = merged.getRentSendTime();
            if (StringUtils.isNotBlank(rentSendTime) && !Objects.equals(rentSendTime, "\\N") && !Objects.equals(rentSendTime, "NaN")) {
                orderInfoDO.setRentSendTime(stringToObjectTidb(rentSendTime, LocalDateTime.class));
            }
            String riskSendTime = merged.getRiskSendTime();
            if (StringUtils.isNotBlank(riskSendTime) && !Objects.equals(riskSendTime, "\\N") && !Objects.equals(riskSendTime, "NaN")) {
                orderInfoDO.setRiskSendTime(riskSendTime);
            }
            String rentPaymentTime = merged.getRentPaymentTime();
            if (StringUtils.isNotBlank(rentPaymentTime) && !Objects.equals(rentPaymentTime, "\\N") && !Objects.equals(rentPaymentTime, "NaN")) {
                orderInfoDO.setRentPaymentTime(stringToObjectTidb(rentPaymentTime, LocalDateTime.class));
            }
            String riskPayTime = merged.getRiskPayTime();
            if (StringUtils.isNotBlank(riskPayTime) && !Objects.equals(riskPayTime, "\\N") && !Objects.equals(riskPayTime, "NaN")) {
                orderInfoDO.setRiskPayTime(stringToObjectTidb(riskPayTime, LocalDateTime.class));
            }
            String orderNo = merged.getOrderNo();
            if (StringUtils.isNotBlank(orderNo) && !Objects.equals(orderNo, "\\N") && !Objects.equals(orderNo, "NaN")) {
                orderInfoDO.setOrderNo(orderNo);
            }
            String riskCustomerId = merged.getRiskCustomerId();
            if (StringUtils.isNotBlank(riskCustomerId) && !Objects.equals(riskCustomerId, "\\N") && !Objects.equals(riskCustomerId, "NaN")) {
                orderInfoDO.setRiskCustomerId(Long.valueOf(riskCustomerId));
            }
            String rentCustomerId = merged.getRentCustomerId();
            if (StringUtils.isNotBlank(rentCustomerId) && !Objects.equals(rentCustomerId, "\\N") && !Objects.equals(rentCustomerId, "NaN")) {
                orderInfoDO.setRentCustomerId(Long.valueOf(rentCustomerId));
            }
            String rentMerchantId = merged.getRentMerchantId();
            if (StringUtils.isNotBlank(rentMerchantId) && !Objects.equals(rentMerchantId, "\\N") && !Objects.equals(rentMerchantId, "NaN")) {
                orderInfoDO.setRentMerchantId(Long.valueOf(rentMerchantId));
            }
            String riskMerchantId = merged.getRiskMerchantId();
            if (StringUtils.isNotBlank(riskMerchantId) && !Objects.equals(riskMerchantId, "\\N") && !Objects.equals(riskMerchantId, "NaN")) {
                orderInfoDO.setRiskMerchantId(Long.valueOf(riskMerchantId));
            }
            String rentTermination = merged.getRentTermination();
            if (StringUtils.isNotBlank(rentTermination) && !Objects.equals(rentTermination, "\\N") && !Objects.equals(rentTermination, "NaN")) {
                orderInfoDO.setRentTermination(Integer.valueOf(rentTermination));
            }
            String rentOrderStatus = merged.getRentOrderStatus();
            if (StringUtils.isNotBlank(rentOrderStatus) && !Objects.equals(rentOrderStatus, "\\N") && !Objects.equals(rentOrderStatus, "NaN")) {
                orderInfoDO.setRentOrderStatus(Integer.valueOf(rentOrderStatus));
            }
            String riskCloseStatus = merged.getRiskCloseStatus();
            if (StringUtils.isNotBlank(riskCloseStatus) && !Objects.equals(riskCloseStatus, "\\N") && !Objects.equals(riskCloseStatus, "NaN")) {
                orderInfoDO.setRiskCloseStatus(Integer.valueOf(riskCloseStatus));
            }
            String riskBusinessChannel = merged.getRiskBusinessChannel();
            if (StringUtils.isNotBlank(riskBusinessChannel) && !Objects.equals(riskBusinessChannel, "\\N") && !Objects.equals(riskBusinessChannel, "NaN")) {
                orderInfoDO.setRiskBusinessChannel(Integer.valueOf(riskBusinessChannel));
            }
            String rentType = merged.getRentType();
            if (StringUtils.isNotBlank(rentType) && !Objects.equals(rentType, "\\N") && !Objects.equals(rentType, "NaN")) {
                orderInfoDO.setRentType(Integer.valueOf(rentType));
            }
            String rentMiniType = merged.getRentMiniType();
            if (StringUtils.isNotBlank(rentMiniType) && !Objects.equals(rentMiniType, "\\N") && !Objects.equals(rentMiniType, "NaN")) {
                orderInfoDO.setRentMiniType(Integer.valueOf(rentMiniType));
            }
            String rentCommonRentFlag = merged.getRentCommonRentFlag();
            if (StringUtils.isNotBlank(rentCommonRentFlag) && !Objects.equals(rentCommonRentFlag, "\\N") && !Objects.equals(rentCommonRentFlag, "NaN")) {
                orderInfoDO.setRentCommonRentFlag(Integer.valueOf(rentCommonRentFlag));
            }
            String riskCoRent =  merged.getRiskCoRent();
            if (StringUtils.isNotBlank(riskCoRent) && !Objects.equals(riskCoRent, "\\N") && !Objects.equals(riskCoRent, "NaN")) {
                orderInfoDO.setRiskCoRent(Integer.valueOf(riskCoRent));
            }
            String rentEquipmentState = merged.getRentEquipmentState() ;
            if (StringUtils.isNotBlank(rentEquipmentState) && !Objects.equals(rentEquipmentState, "\\N") && !Objects.equals(rentEquipmentState, "NaN")) {
                orderInfoDO.setRentEquipmentState(Integer.valueOf(rentEquipmentState));
            }
            String rentEquipmentType = merged.getRentEquipmentType();
            if (StringUtils.isNotBlank(rentEquipmentType) && !Objects.equals(rentEquipmentType, "\\N") && !Objects.equals(rentEquipmentType, "NaN")) {
                orderInfoDO.setRentEquipmentType(Integer.valueOf(rentEquipmentType));
            }
            String riskSupervisoryMachine = merged.getRiskSupervisoryMachine();
            if (StringUtils.isNotBlank(riskSupervisoryMachine) && !Objects.equals(riskSupervisoryMachine, "\\N") && !Objects.equals(riskSupervisoryMachine, "NaN")) {
                orderInfoDO.setRiskSupervisoryMachine(Integer.valueOf(riskSupervisoryMachine));
            }
            String riskScene = merged.getRiskScene();
            if (StringUtils.isNotBlank(riskScene) && !Objects.equals(riskScene, "\\N") && !Objects.equals(riskScene, "NaN")) {
                orderInfoDO.setRiskScene(riskScene);
            }
            String rentQuotientId = merged.getRentQuotientId();
            if (StringUtils.isNotBlank(rentQuotientId) && !Objects.equals(rentQuotientId, "\\N") && !Objects.equals(rentQuotientId, "NaN")) {
                orderInfoDO.setRentQuotientId(Long.valueOf(rentQuotientId));
            }
            String riskQuotientCode = merged.getRiskQuotientCode();
            if (StringUtils.isNotBlank(riskQuotientCode) && !Objects.equals(riskQuotientCode, "\\N") && !Objects.equals(riskQuotientCode, "NaN")) {
                orderInfoDO.setRiskQuotientCode(riskQuotientCode);
            }
            String riskQuotientName = merged.getRiskQuotientName();
            if (StringUtils.isNotBlank(riskQuotientName) && !Objects.equals(riskQuotientName, "\\N") && !Objects.equals(riskQuotientName, "NaN")) {
                orderInfoDO.setRiskQuotientName(riskQuotientName);
            }
            String riskQuotientScene = merged.getRiskQuotientScene();
            if (StringUtils.isNotBlank(riskQuotientScene) && !Objects.equals(riskQuotientScene, "\\N") && !Objects.equals(riskQuotientScene, "NaN")) {
                orderInfoDO.setRiskQuotientScene(riskQuotientScene);
            }
            String rentOrderSourceTag = merged.getRentOrderSourceTag();
            if (StringUtils.isNotBlank(rentOrderSourceTag) && !Objects.equals(rentOrderSourceTag, "\\N") && !Objects.equals(rentOrderSourceTag, "NaN")) {
                orderInfoDO.setRentOrderSourceTag(Integer.valueOf(rentOrderSourceTag));
                orderInfoDO.setZwDrainageFlag(orderInfoDO.getRentOrderSourceTag() == 1 ? 1 : 0);
            }
            String rentRateConfigType = merged.getRentRateConfigType();
            if (StringUtils.isNotBlank(rentRateConfigType) && !Objects.equals(rentRateConfigType, "\\N") && !Objects.equals(rentRateConfigType, "NaN")) {
                orderInfoDO.setRentRateConfigType(Integer.valueOf(rentRateConfigType));
            }
            String riskFinanceTemplateType = merged.getRiskFinanceTemplateType();
            if (StringUtils.isNotBlank(riskFinanceTemplateType) && !Objects.equals(riskFinanceTemplateType, "\\N") && !Objects.equals(riskFinanceTemplateType, "NaN")) {
                orderInfoDO.setRiskFinanceTemplateType(Integer.valueOf(riskFinanceTemplateType));
            }
            String rentBondFreeStatus = merged.getRentBondFreeStatus();
            if (StringUtils.isNotBlank(rentBondFreeStatus) && !Objects.equals(rentBondFreeStatus, "\\N") && !Objects.equals(rentBondFreeStatus, "NaN")) {
                orderInfoDO.setRentBondFreeStatus(Integer.valueOf(rentBondFreeStatus));
            }
            String riskFicoStatus = merged.getRiskFicoStatus();
            if (StringUtils.isNotBlank(riskFicoStatus) && !Objects.equals(riskFicoStatus, "\\N") && !Objects.equals(riskFicoStatus, "NaN")) {
                orderInfoDO.setRiskFicoStatus(Integer.valueOf(riskFicoStatus));
            }
            String riskArtificialAuditStatus = merged.getRiskArtificialAuditStatus();
            if (StringUtils.isNotBlank(riskArtificialAuditStatus) && !Objects.equals(riskArtificialAuditStatus, "\\N") && !Objects.equals(riskArtificialAuditStatus, "NaN")) {
                orderInfoDO.setRiskArtificialAuditStatus(Integer.valueOf(riskArtificialAuditStatus));
            }
            String riskOpinion = merged.getRiskOpinion();
            if (StringUtils.isNotBlank(riskOpinion) && !Objects.equals(riskOpinion, "\\N") && !Objects.equals(riskOpinion, "NaN")) {
                orderInfoDO.setRiskOpinion(riskOpinion);
            }
            String rentPlatformCode = merged.getRentPlatformCode();
            if (StringUtils.isNotBlank(rentPlatformCode) && !Objects.equals(rentPlatformCode, "\\N") && !Objects.equals(rentPlatformCode, "NaN")) {
                orderInfoDO.setRentPlatformCode(rentPlatformCode);
            }
            String rentMerchantTransfer = merged.getRentMerchantTransfer();
            if (StringUtils.isNotBlank(rentMerchantTransfer) && !Objects.equals(rentMerchantTransfer, "\\N") && !Objects.equals(rentMerchantTransfer, "NaN")) {
                orderInfoDO.setRentMerchantTransfer(Integer.valueOf(rentMerchantTransfer));
            }
            String front1PassFlag = merged.getFront1PassFlag();
            if (StringUtils.isNotBlank(front1PassFlag) && !Objects.equals(front1PassFlag, "\\N") && !Objects.equals(front1PassFlag, "NaN")) {
                orderInfoDO.setFront1PassFlag(Integer.valueOf(front1PassFlag));
            }
            String front2PassFlag = merged.getFront2PassFlag();
            if (StringUtils.isNotBlank(front2PassFlag) && !Objects.equals(front2PassFlag, "\\N") && !Objects.equals(front2PassFlag, "NaN")) {
                orderInfoDO.setFront2PassFlag(Integer.valueOf(front2PassFlag));
            }
            String riskPassFlag = merged.getRiskPassFlag();
            if (StringUtils.isNotBlank(riskPassFlag) && !Objects.equals(riskPassFlag, "\\N") && !Objects.equals(riskPassFlag, "NaN")) {
                orderInfoDO.setRiskPassFlag(Integer.valueOf(riskPassFlag));
            }
            String labourAuditFlag = merged.getLabourAuditFlag();
            if (StringUtils.isNotBlank(labourAuditFlag) && !Objects.equals(labourAuditFlag, "\\N") && !Objects.equals(labourAuditFlag, "NaN")) {
                orderInfoDO.setLabourAuditFlag(Integer.valueOf(labourAuditFlag));
            }
            String creditAuditPassFlag = merged.getCreditAuditPassFlag();
            if (StringUtils.isNotBlank(creditAuditPassFlag) && !Objects.equals(creditAuditPassFlag, "\\N") && !Objects.equals(creditAuditPassFlag, "NaN")) {
                orderInfoDO.setCreditAuditPassFlag(Integer.valueOf(creditAuditPassFlag));
            }
            return orderInfoDO;
        }).filter(info -> StringUtils.isNotBlank(info.getOrderNo())).collect(Collectors.toList());
    }

    public <T> T stringToObjectTidb(String str, Class<T> type) {
        if (StringUtils.isNotBlank(str) && !Objects.equals(str, "\\N") && !Objects.equals(str, "NaN") && !Objects.equals(str, "N")) {
            if (type == Integer.class) {
                return (T) Integer.valueOf(str);
            } else if (type == Long.class) {
                return (T) Long.valueOf(str);
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal(str);
            } else if (type == LocalDateTime.class) {
                if(str.contains(".")){
                    int index = str.indexOf(".");
                    String res = str.substring(index + 1);
                    if(res.length() == 1){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB);
                    }else if(res.length() == 2){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB2);
                    }
                    else if(res.length() == 3){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB3);
                    }
                    else if(res.length() == 4){
                        return (T) LocalDateTime.parse(str, FORMAT_TIME_TIDB4);
                    }
                    else {
                        return (T) LocalDateTime.parse(str, FORMAT_TIME);
                    }
                }
                return (T) LocalDateTime.parse(str, FORMAT_TIME);
            } else {
                log.error("类型转换异常");
                return null;
            }
        } else {
            if (type == Integer.class) {
                return (T) Integer.valueOf("-1");
            } else if (type == Long.class) {
                return (T) Long.valueOf("0");
            } else if (type == String.class) {
                return (T) str;
            } else if (type == BigDecimal.class) {
                return (T) new BigDecimal("0");
            } else {
                return null;
            }
        }
    }
}
