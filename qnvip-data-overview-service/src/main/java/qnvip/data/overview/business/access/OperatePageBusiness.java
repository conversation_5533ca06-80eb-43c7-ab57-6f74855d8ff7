package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperatePageDO;
import qnvip.data.overview.service.access.OperatePageService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/15
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperatePageBusiness {

    private final OdpsUtil odpsUtil;

    private final OperatePageService pageService;

    void initMap(Map<Integer, Map<String, Map<Long, OperatePageDO>>> miniType2Map, OperatePageDO orderDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, Map<Long, OperatePageDO>> merchantId2Do = Maps.newHashMap();
        Map<Long, OperatePageDO> pageCode2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(orderDO.getMiniType())) {
            OperatePageDO domain = new OperatePageDO();
            domain.setCountDay(countDay);
            domain.setMiniType(orderDO.getMiniType());
            domain.setMerchantId(orderDO.getMerchantId());
            domain.setEnterPageCode(orderDO.getEnterPageCode());
            pageCode2Do.put(orderDO.getEnterPageCode(), domain);
            merchantId2Do.put(orderDO.getMerchantId(), pageCode2Do);
        } else {
            merchantId2Do = miniType2Map.get(orderDO.getMiniType());
            if (!merchantId2Do.containsKey(orderDO.getMerchantId())) {
                OperatePageDO domain = new OperatePageDO();
                domain.setCountDay(countDay);
                domain.setMiniType(orderDO.getMiniType());
                domain.setMerchantId(orderDO.getMerchantId());
                domain.setEnterPageCode(orderDO.getEnterPageCode());
                pageCode2Do.put(orderDO.getEnterPageCode(), domain);
                merchantId2Do.put(orderDO.getMerchantId(), pageCode2Do);
            } else {
                pageCode2Do = merchantId2Do.get(orderDO.getMerchantId());
                if (!pageCode2Do.containsKey(orderDO.getEnterPageCode())) {
                    OperatePageDO domain = new OperatePageDO();
                    domain.setCountDay(countDay);
                    domain.setMiniType(orderDO.getMiniType());
                    domain.setMerchantId(orderDO.getMerchantId());
                    domain.setEnterPageCode(orderDO.getEnterPageCode());
                    pageCode2Do.put(orderDO.getEnterPageCode(), domain);
                    merchantId2Do.put(orderDO.getMerchantId(), pageCode2Do);
                }
            }
        }
        miniType2Map.put(orderDO.getMiniType(), merchantId2Do);
    }


    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, Map<Long, OperatePageDO>>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperatePageDO>> f1 = CompletableFuture.supplyAsync(()->getPvAndUv(ds));
        CompletableFuture<List<OperatePageDO>> f2 = CompletableFuture.supplyAsync(()->getKeepTime(ds));
        CompletableFuture.allOf(f1, f2).join();
        try {
            List<OperatePageDO> pvAndUv = f1.get();
            List<OperatePageDO> keepTime = f2.get();
            for (OperatePageDO coreDO : pvAndUv) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Long, OperatePageDO>> merchantId2Map = miniType2Map.get(coreDO.getMiniType());
                Map<Long, OperatePageDO> pageCode2DO = merchantId2Map.get(coreDO.getMerchantId());
                OperatePageDO operatePageDO = pageCode2DO.get(coreDO.getEnterPageCode());
                operatePageDO.setPv(coreDO.getPv());
                operatePageDO.setUv(coreDO.getUv());
            }
            for (OperatePageDO coreDO : keepTime) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Long, OperatePageDO>> merchantId2Map = miniType2Map.get(coreDO.getMiniType());
                Map<Long, OperatePageDO> pageCode2DO = merchantId2Map.get(coreDO.getMerchantId());
                OperatePageDO operatePageDO = pageCode2DO.get(coreDO.getEnterPageCode());
                operatePageDO.setKeepTime(coreDO.getKeepTime());
            }
            LinkedList<OperatePageDO> list = Lists.newLinkedList();
            // 写入mysql
            for (Map.Entry<Integer, Map<String, Map<Long, OperatePageDO>>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, Map<Long, OperatePageDO>> merchantId2Map : entry.getValue().entrySet()) {
                    for (Map.Entry<Long, OperatePageDO> entrySet : merchantId2Map.getValue().entrySet()) {
                        OperatePageDO value = entrySet.getValue();
                        list.add(value);
                    }
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            pageService.removeDataByTime(countDay);
            pageService.saveBatch(list);

        } catch (Exception e) {
            log.error("OperatePageBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * PV和UV
     */
    private List<OperatePageDO> getPvAndUv(String ds) {
        String sql = "select count(distinct customer_third_id) uv," +
                "       count(customer_third_id)          pv," +
                "       enter_page_code," +
                "       mini_type," +
                "       merchant_id" +
                " from dataview_track_page_statistics" +
                " where action_type = 1" +
                "  and ds = "+ds +
                "  and report_time between ${fromTime} and ${toTime}" +
                " group by enter_page_code, mini_type, merchant_id;";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperatePageDO domain = new OperatePageDO();
            String miniType = record.getString("mini_type");
            String merchantId = record.getString("merchant_id");
            String enterPageCode = record.getString("enter_page_code");
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPv(Long.valueOf(pv));
            domain.setEnterPageCode(Long.valueOf(enterPageCode));
            domain.setMerchantId(merchantId);
            domain.setUv(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 页面停留时长
     */
    private List<OperatePageDO> getKeepTime(String ds) {
        String sql = "select sum(keep_alive_time) keep_time," +
                "       enter_page_code," +
                "       mini_type," +
                "       merchant_id" +
                " from dataview_track_page_statistics" +
                " where action_type = 2" +
                "  and ds = "+ds +
                "  and report_time between ${fromTime} and ${toTime}" +
                " group by enter_page_code, mini_type, merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperatePageDO domain = new OperatePageDO();
            String miniType = record.getString("mini_type");
            String merchantId = record.getString("merchant_id");
            String enterPageCode = record.getString("enter_page_code");
            String keepTime = record.getString("keep_time");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setEnterPageCode(Long.valueOf(enterPageCode));
            domain.setMerchantId(merchantId);
            domain.setKeepTime(new BigDecimal(keepTime));
            return domain;
        }).collect(Collectors.toList());


    }

}
