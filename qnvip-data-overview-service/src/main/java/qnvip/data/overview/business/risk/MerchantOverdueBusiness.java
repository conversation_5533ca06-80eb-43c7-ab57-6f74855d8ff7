// package qnvip.data.overview.business.risk;
//
// import cn.hutool.core.collection.CollUtil;
// import cn.hutool.json.JSONObject;
// import cn.hutool.json.JSONUtil;
// import com.aliyun.odps.data.Record;
// import com.baomidou.mybatisplus.core.toolkit.StringUtils;
// import com.google.common.collect.*;
// import lombok.RequiredArgsConstructor;
// import lombok.extern.slf4j.Slf4j;
// import org.springframework.stereotype.Service;
// import qnvip.data.overview.domain.risk.RepaymentPlanDO;
// import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
// import qnvip.data.overview.domain.risk.RiskOrderOverdueDO;
// import qnvip.data.overview.enums.finance.FinanceTypeEnum;
// import qnvip.data.overview.service.risk.RiskGeneralOrderOverdueService;
// import qnvip.data.overview.service.risk.RiskOrderOverdueService;
// import qnvip.data.overview.util.CalculateUtil;
// import qnvip.data.overview.util.OdpsUtil;
//
// import java.math.BigDecimal;
// import java.math.RoundingMode;
// import java.time.LocalDate;
// import java.time.LocalDateTime;
// import java.time.LocalTime;
// import java.time.format.DateTimeFormatter;
// import java.util.*;
// import java.util.concurrent.LinkedBlockingQueue;
// import java.util.concurrent.ThreadPoolExecutor;
// import java.util.concurrent.TimeUnit;
// import java.util.concurrent.atomic.AtomicInteger;
// import java.util.stream.Collectors;
//
// /**
//  * create by gw on 2022/3/24
//  */
// @Slf4j
// @Service
// @RequiredArgsConstructor
// public class MerchantOverdueBusiness {
//
//     private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
//     private static final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
//     private static final Integer PAGE_SIZE = 830;
//
//     private final OdpsUtil odpsUtil;
//     private final RiskOrderOverdueService riskOrderOverdueService;
//     private final RiskGeneralOrderOverdueService riskGeneralOrderOverdueService;
//
//     private static AtomicInteger atomicInteger = new AtomicInteger();
//     private static ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(5, 10, 0, TimeUnit.SECONDS,
//             new LinkedBlockingQueue(1000), r -> {
//         Thread t = new Thread(r);
//         t.setName("RiskOverdueBusiness-" + atomicInteger.incrementAndGet());
//         return t;
//     });
//
//     /**
//      * @param ds
//      */
//     public void execOldData(String ds, int... overdueDays) {
//         // order总记录数
//         riskOrderOverdueService.deleteAll();
//         riskGeneralOrderOverdueService.deleteAll();
//         Integer size = getCount(ds);
//         for (int overdueDay : overdueDays) {
//             int times = size / PAGE_SIZE;
//             if (size % PAGE_SIZE != 0) {
//                 times += 1;
//             }
//             int startPage = 0;
//             for (int i = 0; i < times; i++) {
//                 final int p = startPage;
//                 threadPoolExecutor.execute(() -> {
//                     oldRepayTask(ds, p, overdueDay);
//                 });
//                 startPage++;
//             }
//         }
//     }
//
//     /**
//      * 获取order最大记录数
//      */
//     private Integer getCount(String ds) {
//         String sql = "select count(*) num" +
//                 " from rent_order ro" +
//                 "           inner join rent_order_logistics rol on ro.id = rol.order_id" +
//                 "    WHERE ro.ds =  " + ds +
//                 "     and rol.ds = " + ds + "" +
//                 "     and rol.sign_time is not null" +
//                 " and ro.merchant_id = 100" +
//                 "  and ro.termination != 5" +
//                 "  and ro.biz_type = 2" +
//                 "  and ro.type = 1" +
//                 "  and ro.parent_id = 0" +
//                 "  AND date(ro.rent_start_date) >= '2020-10-01' " +
//                 ";";
//         List<Record> records = odpsUtil.querySql(sql);
//         return Integer.valueOf(records.get(0).getString("num"));
//     }
//
//
//     private void oldRepayTask(String ds, int startPage, Integer overdueDay) {
//         String sql = " SELECT date(ro.rent_start_date)                                              rent_start_date, " +
//                 "       rorp.order_id, " +
//                 "       nvl(min(aa.hitValue) ,'')                                                           hit_value," +
//                 "       ro.no, " +
//                 "       ro.mini_type, " +
//                 "       date(repay_date) repay_date," +
//                 "       real_repay_time real_repay_time, " +
//                 "       (CASE " +
//                 "            WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝' " +
//                 "            WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信' " +
//                 "            WHEN ro.mini_type = 2 THEN '字节跳动' " +
//                 "            WHEN ro.mini_type = 3 THEN 'app' END)                                      platform, " +
//                 "       rate_config_type                                                                finance_type," +
//                 "       rorp.term                                                                       term, " +
//                 "       cl.riskOpinion                                                                  risk_level, " +
//                 "       sn.riskStrategy                                                                risk_strategy," +
//                 "       artificialAuditorId                                  audit_type, " +
//                 "       IF(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0')               forcedconversion, " +
//                 "       (case" +
//                 "            when" +
//                 "                    add_months(date(repay_date) - day(repay_date) + 2, 1 ) > date(getdate())" +
//                 "               THEN (case  when month (repay_date) > month (getdate()) then '0' when term =1\n" +
//                 "               then if(date (repay_date) >= date (getdate()) ,'0', if(min (repay_status) = 1 and min (overdue) = 5,'1','0'))\n" +
//                 "               else IF( min (repay_status)=1 and min (overdue) = 5 ,'1',if(min (repay_status) =5,'0','2') )\n" +
//                 "               end\n" +
//                 "               ) " +
//                 "            else" +
//                 "                (case" +
//                 "                     when" +
//                 "                             date(NVL(real_repay_time, getdate())) < add_months(date(repay_date) - " +
//                 " day(repay_date) + 2, 1 )" +
//                 "                         then '0'" +
//                 "                     else IF(datediff(date (NVL(real_repay_time,getdate()))，date(add_months(date (repay_date) - day (repay_date) + 2, 1)),'dd') > " + overdueDay + ", '1', IF(real_repay_time is null,'1','0'))" +
//                 "                    end)" +
//                 "           end" +
//                 "           ) is_overdue, " +
//                 "       if(min(g.ext) != '',get_json_object(min(g.ext),'$.discountReturnAmt'),0) discount_return_amt," +
//                 "       if(c.rate_config_type != 10,min(c.actual_financing_amt),avg(rorp.capital)*12)   rent_total, " +
//                 "       if(c.rate_config_type!=10,max(c.buyout_amt),0)                                  buyout_amt, " +
//                 "       nvl(min(rorp.overdue_fine) ,0)                                                  overdue_fine," +
//                 "       nvl(min(c.bond_amt),0)                                                          bond_amt, " +
//                 "       nvl(min(x.before_discount), 0)                                                  before_discount, " +
//                 "       nvl(min(x.total_discount), 0)                                                  total_discount, " +
//                 "       round(nvl(min(c.bond_amt)/((if(c.rate_config_type != 10,min(c.actual_financing_amt),avg(rorp" +
//                 " .capital)*12)" +
//                 "         +  if(c.rate_config_type != 10,min(c.buyout_amt),0)) - nvl(min(x.total_discount),0)),0),2) " +
//                 " bond_rate," +
//                 "   " +
//                 "       rorp.term, " +
//                 "       (case when min(repay_status) =5 then avg(rorp.capital) " +
//                 "        when min(repay_status) =1 then (avg(capital)- avg(real_repay_capital) -avg(reduce_amt)) end)" +
//                 " real_capital, " +
//                 "       avg(rorp.capital)                                                                 capital, " +
//                 "       min(rorp.is_deleted) is_deleted ," +
//                 "             min  (repay_status)    repay_status, " +
//                 "             min(overdue)    overdue, " +
//                 "             if(real_repay_time is null ,1,0) real_repay_time_status," +
//                 "        min(c.renew_total_rent) renew_total_rent," +
//                 "        max(rorp.term) max_term," +
//                 "         min(renew_term) renew_term," +
//                 "       if(min(ro.status) = 15 and datediff(date(getdate()), date(min(ro.rent_end_date))) > 7, 1, 0) " +
//                 " renew_status,   " +
//                 // 续租日期就是父订单的账单截止日
//                 "       datediff(date(getdate()),date(min(ro.rent_end_date)),'mm') renew_day" +
//                 " FROM rent_order ro " +
//                 "         INNER JOIN rent_order_repayment_plan rorp ON ro.id = rorp.order_id " +
//                 "         LEFT join (SELECT min(ext) ext, order_id " +
//                 "                    FROM rent_order_flow b " +
//                 "                    WHERE b.ds = " + ds + " " +
//                 "                      AND b.biz_type = 3 " +
//                 "                      AND b.is_deleted = 0 " +
//                 "                      AND b.pay_status = 10 " +
//                 "                      and (b.mark_refund IS NULL OR b.mark_refund = 0) " +
//                 "                      AND b.flow_type = 1 " +
//                 "                      AND b.refunded_amt = 0 " +
//                 "                      AND b.ext IS NOT NULL " +
//                 "                      AND b.ext != '' " +
//                 "                    GROUP BY order_id) g ON ro.id = g.order_id " +
//                 "         INNER JOIN rent_order_finance_detail c ON ro.id = c.order_id " +
//                 "         LEFT join ( select order_id," +
//                 "                     sum (if(isbefore = 1, amt1 + amt2, 0) ) total_discount, " +
//                 "                        sum (if(isbefore = 1, yuji_amt1 + yuji_amt2, 0)) before_discount from (" +
//                 "                       SELECT x.order_id, " +
//                 "                           if(min(x.bind_order_time) IS NOT NULL AND min(y.payment_time) >= " +
//                 "                                   min(x.bind_order_time), 1, 0)  isbefore, " +
//                 "                           if(x.type IN (1, 3) , max(x.write_off_amt), 0)       amt1, " +
//                 "                           if(x.type IN (2, 4) , sum(x.write_off_amt), 0)       amt2, " +
//                 "                           if(x.type IN (1, 3) AND min(x.use_status = 2), " +
//                 "                                   max(x.write_off_amt), 0)       yuji_amt1, " +
//                 "                           if(x.type IN (2, 4) AND min(x.use_status = 2), " +
//                 "                                   sum(x.write_off_amt), 0)       yuji_amt2 " +
//                 "                    FROM rent_customer_coupon x " +
//                 "                             INNER JOIN rent_order y ON x.order_id = y.id " +
//                 "                    WHERE x.order_id > 0 " +
//                 "                      AND x.scene = 1 " +
//                 "                      AND x.is_deleted = 0 " +
//                 "                      AND y.is_deleted = 0 " +
//                 "                      AND x.ds = " + ds + " " +
//                 "                      AND y.ds = " + ds + " " +
//                 "                    GROUP BY x.order_id, x.type )" +
//                 "               group by order_id) x ON ro.id = x.order_id " +
//                 "         INNER JOIN cl_loan cl ON cl.loanno = ro.no " +
//                 "         INNER JOIN serial_no sn ON sn.businessno = ro.no " +
//                 "         INNER join (SELECT ro.id " +
//                 "                     FROM rent_order ro " +
//                 "         INNER JOIN    rent_order_logistics rol ON      ro.id = rol.order_id" +
//                 "                     WHERE ro.ds = " + ds +
//                 "                       AND rol.ds = " + ds +
//                 "                       AND ro.merchant_id = 100 " +
//                 "                                       AND     rol.sign_time IS NOT NULL " +
//                 "                       AND ro.parent_id = 0 " +
//                 "                       AND ro.termination != 5 " +
//                 "                       AND  ro.is_deleted = 0   " +
//                 "                       AND ro.type = 1 " +
//                 "                       AND date(ro.rent_start_date) >= '2020-10-01' " +
//                 "                   ORDER BY ro.id " +
//                 "                     limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + ") z ON ro.id = z.id " +
//                 "  left join (SELECT rc.hitValue, sn.businessNo" +
//                 "                    FROM serial_no sn" +
//                 "                             INNER JOIN" +
//                 "                         rc_risk_access_rule_result_2023 rc on sn.id = rc.serialnoId" +
//                 "                             INNER JOIN rc_risk_strategy_rule_set rr on rr.id = rc.ruleId" +
//                 "                    WHERE " +
//                 "                       rr.scene=3 and  rc.hitValue!='' and rr.masterModel=1 " +
//                 // "                      and sn.riskStrategy= 'qnvip_d' " +
//                 "                      and rc.ds = " + ds +
//                 "                      and rr.ds = " + ds +
//                 "                      and sn.ds = " + ds +
//                 " ) aa on ro.no = aa.businessNo " +
//                 " WHERE ro.ds = " + ds +
//                 "  AND rorp.ds = " + ds +
//                 "  AND rorp.is_deleted = 0 " +
//                 "  AND c.ds = " + ds +
//                 "  AND cl.ds = " + ds +
//                 "  AND sn.ds = " + ds +
//                 " GROUP BY date(ro.rent_start_date), ro.mini_type, rorp.order_id,ro.no, rate_config_type, " +
//                 "         IF(ext_json IS NOT NULL AND rate_config_type = 10, '1', '0'), rorp.term, cl.riskOpinion, " +
//                 "           sn.riskStrategy,  cl.artificialAuditorId, " +
//                 "     repay_date," +
//                 "    real_repay_time," +
//                 "       (c.bond_amt/(if(c.rate_config_type != 10,c.actual_financing_amt,rorp.capital*12)" +
//                 "           + if(c.rate_config_type != 10,c.buyout_amt,0)) - x.total_discount)" +
//                 " ORDER BY date(ro.rent_start_date); ";
//         List<Record> records = odpsUtil.querySql(sql.concat(";"));
//         saveData(records, overdueDay);
//     }
//
//     public void updateScore(String ds) {
//         String sql = "SELECT rc.hitvalue hit_value, ro.id\n" +
//                 " FROM cl_loan cl\n" +
//                 "          inner join rent_order ro on ro.no = cl.loanno\n" +
//                 "         INNER JOIN serial_no sn ON cl.loanno = sn.businessNo\n" +
//                 "         INNER JOIN rc_risk_access_rule_result_2021 rc on sn.id = rc.serialnoId\n" +
//                 "         INNER JOIN rc_risk_strategy_rule_set rr on rr.ruleId = rc.ruleId\n" +
//                 " WHERE rr.scene = 201\n" +
//                 "  and rc.hitValue != ''\n" +
//                 // "  and sn.riskStrategy = 'qnvip_d'\n" +
//                 "  and sn.ds = " + ds +
//                 "  and ro.ds = " + ds +
//                 "  and rr.ds = " + ds +
//                 "  and cl.ds = " + ds +
//                 "  and cl.createTime >= '2021-10-01 00:00:00'\n" +
//                 "  and cl.createTime < '2022-01-01 00:00:00'\n" +
//                 " group by ro.id, rc.hitvalue";
//         List<Record> records = odpsUtil.querySql(sql.concat(";"));
//         Map<Long, Integer> no2score = Maps.newHashMap();
//         for (Record record : records) {
//             String hitValue = record.getString("hit_value");
//             Integer score = -1;
//             if (StringUtils.isNotBlank(hitValue)) {
//                 if (hitValue.startsWith("{")) {
//                     JSONObject jsonObject = JSONUtil.parseObj(hitValue);
//                     score = jsonObject.get("score", Integer.class);
//                 } else {
//                     List<Map> maps = JSONUtil.toList(hitValue, Map.class);
//                     Map map = maps.get(0);
//                     Object str = map.get("Y100015");
//                     JSONObject jsonObject = JSONUtil.parseObj(str);
//                     score = jsonObject.get("score", Integer.class);
//                 }
//             }
//             no2score.put(Long.parseLong(record.getString("id")), score);
//         }
//         List<Long> collect = new ArrayList<>(no2score.keySet());
//         List<RiskGeneralOrderOverdueDO> list = riskGeneralOrderOverdueService.getList(collect);
//         List<RiskGeneralOrderOverdueDO> finalList = list.stream().peek(o -> {
//             Long orderId = o.getOrderId();
//             Integer score = no2score.get(orderId);
//             o.setScore(score);
//         }).collect(Collectors.toList());
//         riskGeneralOrderOverdueService.updateBatchById(finalList);
//     }
//
//     private void saveData(List<Record> records, Integer overdueDay) {
//         List<RiskOrderOverdueDO> resList = fetchList(records, overdueDay);
//         riskOrderOverdueService.saveBatch(resList);
//         //  通用列表
//         // List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay);
//         // todo 当前时间>最后还款时间并且status = 15,为租后待买断订单,按照续租处理,生成假续租订单
//         // todo 取出最大续租期数以及续租总租金
//         // todo 只要更新续租订单的renewAmt字段为续租总租金,以及更新Pmaxterm就行
//         List<RiskGeneralOrderOverdueDO> generalList = fetchGeneralList(records, overdueDay)
//                 .stream()
//                 .peek(o -> {
//                     if (o.getRenewStatus() == 1) {
//                         Integer maxTerm = o.getMaxTerm();
//                         Integer renewTerm = o.getRenewTerm();
//                         Integer renewDay = o.getRenewDay();
//                         BigDecimal parentNotReturnAmt = getParentNotReturnAmt(o, maxTerm);
//                         int newMaxTerm = maxTerm + renewTerm;
//                         o.setMaxTerm(newMaxTerm);
//
//                         for (int i = 1; i <= renewTerm; i++) {
//                             // 将假续租订单的逾期未还金额设置为,租后待买断订单的最后一期逾期未还金额
//                             setTermNotRepayVal(o, maxTerm + i, parentNotReturnAmt);
//                             // 设置续租总租金
//                             setRenewAmt(o, maxTerm + i, o.getRenewAmt1());
//                         }
//                         // 设置逾期情况,判断续租待买断订单的逾期时间
//                         for (int i = 1, day = renewDay + 1; i <= day && maxTerm + i <= newMaxTerm; i++) {
//                             setOverdueVal(o, maxTerm + i, 1, BigDecimal.ZERO);
//                         }
//                     }
//                 }).collect(Collectors.toList());
//         riskGeneralOrderOverdueService.saveBatch(generalList);
//     }
//
//     private List<RiskOrderOverdueDO> fetchList(List<Record> records, Integer overdueDay) {
//         List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);
//
//         List<RiskOrderOverdueDO> resList = new ArrayList<>();
//         Map<Long, List<RepaymentPlanDO>> orderId2List =
//                 planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));
//
//         for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
//             Long orderId = entry.getKey();
//             List<RepaymentPlanDO> value = entry.getValue();
//             RiskOrderOverdueDO riskOrderOverdueDO = null;
//             Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
//             Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
//                     value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
//             for (RepaymentPlanDO repaymentPlanDO : value) {
//                 if (riskOrderOverdueDO == null) {
//                     riskOrderOverdueDO = new RiskOrderOverdueDO();
//                     resList.add(riskOrderOverdueDO);
//                     riskOrderOverdueDO.setOrderId(orderId);
//                     riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
//                     riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
//                     riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
//                     riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
//                     riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
//                     riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
//                     riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
//                     riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
//                     riskOrderOverdueDO.setDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
//                     // 未使用售前优惠
//                     riskOrderOverdueDO.setBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
//                     // 总售前优惠金额
//                     // riskOrderOverdueDO.setBeforeDiscount(repaymentPlanDO.getTotalDiscount());
//                     riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRate());
//                     riskOrderOverdueDO.setOverdueDay(overdueDay);
//                     riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
//                 }
//
//                 int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
//                 int term = repaymentPlanDO.getTerm();
//                 //对当前期逾期情况做修正
//                 if (isOverdue == 2) {
//                     isOverdue = updateOverdueStatus(term2PlanDo, term);
//                     repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
//                     repaymentPlanDO.setOverdue(5);
//                 }
//                 LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
//                 riskOrderOverdueDO.setCountDay(rentStartDay);
//                 riskOrderOverdueDO.setMaxDay(maxTerm);
//                 riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
//                 setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
//                 RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
//
//                 if (isOverdue == 1) {
//                     Map<String, Object> resMap = new HashMap<>();
//                     resMap.put("term", BigDecimal.valueOf(term));
//                     resMap.put("capital", BigDecimal.ZERO);
//                     LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
//
//                     LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                     LocalDate maxRepayDate =
//                             LocalDate.from(maxTerPlanDO.getRepayDate().plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//
//                     resMap.put("endTime", localDate);
//                     resMap.put("maxRepayDate", maxRepayDate);
//
//                     // 获取逾期未还金额
//                     fetchCapital(term2PlanDo, resMap);
//                     // 设置对应期数未还金额
//                     BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
//                     setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
//                 } else {
//                     // 未逾期，逾期未还金额为0
//                     setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
//                 }
//             }
//         }
//         return resList;
//     }
//
//     private RepaymentPlanDO getPlanDo(List<RepaymentPlanDO> value, Integer maxTerm, Map<Integer, List<RepaymentPlanDO>> term2PlanDo) {
//         List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(maxTerm);
//         RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
//         if (value.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         return planDO;
//     }
//
//
//     private List<RiskGeneralOrderOverdueDO> fetchGeneralList(List<Record> records, Integer overdueDay) {
//         List<RepaymentPlanDO> planList = getRepaymentPlanDOS(records);
//
//         List<RiskGeneralOrderOverdueDO> resList = new ArrayList<>();
//         Map<Long, List<RepaymentPlanDO>> orderId2List =
//                 planList.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getOrderId));
//
//         // 把租后待买断的订单先当作续租处理,如果真买断了,就走原来的逻辑,如果一直未买断,也可以按照续租的方式计算逾期率
//         for (Map.Entry<Long, List<RepaymentPlanDO>> entry : orderId2List.entrySet()) {
//             Long orderId = entry.getKey();
//             List<RepaymentPlanDO> value = entry.getValue();
//             RiskGeneralOrderOverdueDO riskOrderOverdueDO = null;
//             Integer maxTerm = value.stream().max(Comparator.comparing(RepaymentPlanDO::getTerm)).get().getTerm();
//             Map<Integer, List<RepaymentPlanDO>> term2PlanDo =
//                     value.stream().collect(Collectors.groupingBy(RepaymentPlanDO::getTerm));
//
//             for (RepaymentPlanDO repaymentPlanDO : value) {
//                 if (riskOrderOverdueDO == null) {
//                     riskOrderOverdueDO = new RiskGeneralOrderOverdueDO();
//                     resList.add(riskOrderOverdueDO);
//                     riskOrderOverdueDO.setOrderId(orderId);
//                     riskOrderOverdueDO.setMiniType(repaymentPlanDO.getMiniType());
//                     riskOrderOverdueDO.setScore(repaymentPlanDO.getScore());
//                     riskOrderOverdueDO.setFinanceType(repaymentPlanDO.getFinanceType());
//                     riskOrderOverdueDO.setAuditType(repaymentPlanDO.getAuditType());
//                     riskOrderOverdueDO.setPlatform(repaymentPlanDO.getPlatform());
//                     riskOrderOverdueDO.setRiskLevel(repaymentPlanDO.getRiskLevel());
//                     riskOrderOverdueDO.setRiskStrategy(repaymentPlanDO.getRiskStrategy());
//                     riskOrderOverdueDO.setRentTotal(repaymentPlanDO.getRentTotal());
//                     riskOrderOverdueDO.setBondAmt(repaymentPlanDO.getBondAmt());
//                     riskOrderOverdueDO.setPDiscountReturnAmt(repaymentPlanDO.getDiscountReturnAmt());
//                     riskOrderOverdueDO.setPBeforeDiscount(repaymentPlanDO.getBeforeDiscount());
//                     riskOrderOverdueDO.setBondRate(repaymentPlanDO.getBondRateStr());
//                     riskOrderOverdueDO.setOverdueDay(overdueDay);
//                     riskOrderOverdueDO.setBuyoutAmt(repaymentPlanDO.getBuyoutAmt());
//                     riskOrderOverdueDO.setPMaxTerm(maxTerm);
//                     riskOrderOverdueDO.setMaxTerm(maxTerm);
//                     riskOrderOverdueDO.setRenewDay(repaymentPlanDO.getRenewDay());
//                     riskOrderOverdueDO.setRenewStatus(repaymentPlanDO.getRenewStatus());
//                     riskOrderOverdueDO.setRenewTerm(repaymentPlanDO.getRenewTerm());
//                     riskOrderOverdueDO.setNo(repaymentPlanDO.getNo());
//                 }
//
//                 int isOverdue = Integer.parseInt(repaymentPlanDO.getIsOverdue());
//                 int term = repaymentPlanDO.getTerm();
//                 //对当前期逾期情况做修正
//                 if (isOverdue == 2) {
//                     isOverdue = updateOverdueStatus(term2PlanDo, term);
//                     repaymentPlanDO.setIsOverdue(String.valueOf(isOverdue));
//                     repaymentPlanDO.setOverdue(5);
//                 }
//                 LocalDateTime rentStartDay = repaymentPlanDO.getRentStartDate();
//                 riskOrderOverdueDO.setCountDay(rentStartDay);
//                 setOverdueVal(riskOrderOverdueDO, term, isOverdue, repaymentPlanDO.getOverdueFine());
//                 setRenewAmt(riskOrderOverdueDO, term, repaymentPlanDO.getRenewTotalRent());
//                 RepaymentPlanDO maxTerPlanDO = getPlanDo(value, maxTerm, term2PlanDo);
//                 if (isOverdue == 1) {
//                     Map<String, Object> resMap = new HashMap<>();
//                     resMap.put("term", BigDecimal.valueOf(term));
//
//                     resMap.put("capital", BigDecimal.ZERO);
//                     LocalDateTime repayDate = repaymentPlanDO.getRepayDate();
//                     // 封账日期
//                     LocalDate localDate = LocalDate.from(repayDate.plusMonths(1).withDayOfMonth(2).plusDays(overdueDay));
//                     resMap.put("endTime", localDate);
//                     // 获取逾期未还金额
//                     fetchCapital(term2PlanDo, resMap);
//                     // 设置对应期数未还金额
//                     BigDecimal decimal = CalculateUtil.toDecimal(resMap.get("capital"));
//                     setTermNotRepayVal(riskOrderOverdueDO, term, decimal);
//                 } else {
//                     // 未逾期，逾期未还金额为0
//                     setTermNotRepayVal(riskOrderOverdueDO, term, BigDecimal.ZERO);
//                 }
//             }
//         }
//         return resList;
//     }
//
//     private List<RepaymentPlanDO> getRepaymentPlanDOS(List<Record> records) {
//         List<RepaymentPlanDO> planList = Lists.newArrayList();
//         if (CollUtil.isNotEmpty(records)) {
//             planList = records.stream().map(domain -> {
//                 RepaymentPlanDO repaymentPlanDO = new RepaymentPlanDO();
//                 Long orderId = Long.valueOf(domain.getString("order_id"));
//                 Integer term = Integer.valueOf(domain.getString("term"));
//                 Integer isDeleted = Integer.valueOf(domain.getString("is_deleted"));
//                 Integer miniType = Integer.valueOf(domain.getString("mini_type"));
//                 Integer repayStatus = Integer.valueOf(domain.getString("repay_status"));
//                 Integer renewStatus = Integer.valueOf(domain.getString("renew_status"));
//                 Integer renewTerm = Integer.valueOf(domain.getString("renew_term"));
//                 Integer renewDay = Integer.valueOf(domain.getString("renew_day"));
//                 Integer realRepayTimeStatus = Integer.valueOf(domain.getString("real_repay_time_status"));
//                 String platform = domain.getString("platform");
//                 String no = domain.getString("no");
//                 Integer overdue = Integer.valueOf(domain.getString("overdue"));
//                 Integer financeType = Integer.valueOf(domain.getString("finance_type"));
//                 String riskLevel = domain.getString("risk_level");
//                 String riskStrategy = domain.getString("risk_strategy");
//                 String auditType = domain.getString("audit_type");
//                 // 获取分数
//                 String hitValue = domain.getString("hit_value");
//                 Integer score = -1;
//                 if (StringUtils.isNotBlank(hitValue)) {
//                     if (hitValue.startsWith("{")) {
//                         JSONObject jsonObject = JSONUtil.parseObj(hitValue);
//                         score = jsonObject.get("score", Integer.class);
//                     } else {
//                         List<Map> maps = JSONUtil.toList(hitValue, Map.class);
//                         Map map = maps.get(0);
//                         Object str = map.get("Y100018");
//                         JSONObject jsonObject = JSONUtil.parseObj(str);
//                         score = jsonObject.get("score", Integer.class);
//                     }
//                 }
//                 Integer forcedConversion = Integer.valueOf(domain.getString("forcedconversion"));
//                 String isOverdue = domain.getString("is_overdue");
//                 BigDecimal capital = stringToDecimal(domain.getString("capital"));
//                 BigDecimal realCapital = stringToDecimal(domain.getString("real_capital"));
//                 BigDecimal renewTotalRent = stringToDecimal(domain.getString("renew_total_rent"));
//                 Integer maxTerm = Integer.valueOf(domain.getString("max_term"));
//
//
//                 LocalDateTime rentStartDay = LocalDateTime.of(LocalDate.parse(domain.getString("rent_start_date"),
//                         dateFormatter), LocalTime.MIN);
//                 LocalDateTime repayDate = LocalDateTime.of(LocalDate.parse(domain.getString("repay_date"),
//                         dateFormatter), LocalTime.MIN);
//
//                 LocalDateTime realRepayTime = null;
//                 if (!domain.getString("real_repay_time").equals("\\N")) {
//                     realRepayTime = LocalDateTime.parse(domain.getString("real_repay_time"), YYYY_MM_DD_HH_MM_SS);
//                 }
//
//                 BigDecimal discountReturnAmt = stringToDecimal(domain.getString("discount_return_amt"));
//                 BigDecimal rentTotal = stringToDecimal(domain.getString("rent_total"));
//                 BigDecimal buyoutAmt = stringToDecimal(domain.getString("buyout_amt"));
//                 BigDecimal overdueFine = stringToDecimal(domain.getString("overdue_fine"));
//                 BigDecimal bondAmt = stringToDecimal(domain.getString("bond_amt"));
//                 double bondRate = Double.parseDouble(domain.getString("bond_rate"));
//                 BigDecimal mul = CalculateUtil.mul(new BigDecimal(bondRate).setScale(2, BigDecimal.ROUND_HALF_UP), 100);
//                 String bondRateStr = fetchInterval(mul.doubleValue());
//                 BigDecimal beforeDiscount = stringToDecimal(domain.getString("before_discount"));
//                 BigDecimal totalDiscount = stringToDecimal(domain.getString("total_discount"));
//                 repaymentPlanDO.setCapital(capital);
//                 repaymentPlanDO.setMaxTerm(maxTerm);
//                 repaymentPlanDO.setScore(score);
//                 repaymentPlanDO.setNo(no);
//                 repaymentPlanDO.setRenewDay(renewDay);
//                 repaymentPlanDO.setBondRateStr(bondRateStr);
//                 repaymentPlanDO.setRenewTotalRent(renewTotalRent);
//                 repaymentPlanDO.setRealRepayTimeStatus(realRepayTimeStatus);
//                 repaymentPlanDO.setRealCapital(realCapital);
//                 repaymentPlanDO.setRepayDate(repayDate);
//                 repaymentPlanDO.setRepayStatus(repayStatus);
//                 repaymentPlanDO.setRealRepayTime(realRepayTime);
//                 repaymentPlanDO.setOrderId(orderId);
//                 repaymentPlanDO.setTerm(term);
//                 repaymentPlanDO.setOverdue(overdue);
//                 repaymentPlanDO.setIsDeleted(isDeleted);
//                 repaymentPlanDO.setBuyoutAmt(buyoutAmt);
//                 repaymentPlanDO.setBondRate(bondRate);
//                 repaymentPlanDO.setRenewStatus(renewStatus);
//                 repaymentPlanDO.setRenewTerm(renewTerm);
//                 repaymentPlanDO.setIsOverdue(isOverdue);
//                 repaymentPlanDO.setRentStartDate(rentStartDay);
//                 repaymentPlanDO.setMiniType(miniType);
//                 repaymentPlanDO.setFinanceType(String.valueOf(FinanceTypeEnum.getFinanceType(financeType, 0,
//                         forcedConversion)));
//                 repaymentPlanDO.setAuditType(auditType);
//                 repaymentPlanDO.setPlatform(platform);
//                 repaymentPlanDO.setRiskLevel(riskLevel);
//                 repaymentPlanDO.setRiskStrategy(riskStrategy);
//                 repaymentPlanDO.setRentTotal(rentTotal);
//                 repaymentPlanDO.setDiscountReturnAmt(discountReturnAmt);
//                 repaymentPlanDO.setOverdueFine(overdueFine);
//                 repaymentPlanDO.setBondAmt(bondAmt);
//                 repaymentPlanDO.setBeforeDiscount(beforeDiscount);
//                 repaymentPlanDO.setTotalDiscount(totalDiscount);
//                 return repaymentPlanDO;
//             }).collect(Collectors.toList());
//         }
//         return planList;
//     }
//
//     /**
//      * 判断区间
//      *
//      * @param d
//      * @return
//      */
//     private String fetchInterval(Double d) {
//         RangeMap<Double, String> level = TreeRangeMap.create();
//         level.put(Range.closed(0D, 5D), "(0%-5%],1");
//         level.put(Range.openClosed(5D, 10D), "(5%-10%],2");
//         level.put(Range.openClosed(10D, 15D), "(10%-15%],3");
//         level.put(Range.openClosed(15D, 20D), "(15%-20%],4");
//         level.put(Range.openClosed(20D, 25D), "(20%-25%],5");
//         level.put(Range.openClosed(25D, 30D), "(25%-30%],6");
//         level.put(Range.openClosed(30D, 35D), "(30%-35%],7");
//         level.put(Range.openClosed(35D, 40D), "(35%-40%],8");
//         level.put(Range.openClosed(40D, 45D), "(40%-45%],9");
//         level.put(Range.openClosed(45D, 50D), "(45%-50%],10");
//         level.put(Range.openClosed(50D, 55D), "(50%-55%],11");
//         level.put(Range.openClosed(55D, 60D), "(55%-60%],12");
//         level.put(Range.openClosed(60D, 65D), "(60%-65%],13");
//         level.put(Range.openClosed(65D, 70D), "(65%-70%],14");
//         level.put(Range.openClosed(70D, 75D), "(70%-75%],15");
//         level.put(Range.openClosed(75D, 80D), "(75%-80%],16");
//         level.put(Range.openClosed(80D, 85D), "(80%-85%],17");
//         level.put(Range.openClosed(85D, 90D), "(85%-90%],18");
//         level.put(Range.openClosed(90D, 95D), "(90%-95%],19");
//         level.put(Range.openClosed(95D, 100D), "(95%-100%],20");
//
//         return level.get(d);
//     }
//
//
//     private int updateOverdueStatus(Map<Integer, List<RepaymentPlanDO>> term2PlanDo, int term) {
//         List<RepaymentPlanDO> repaymentPlanDOList = term2PlanDo.get(--term);
//         RepaymentPlanDO planDO = repaymentPlanDOList.get(0);
//         if (repaymentPlanDOList.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : repaymentPlanDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         int isOverdue = Integer.parseInt(planDO.getIsOverdue());
//         // 上一期逾期并且未还款,这一期即使还没到,算逾期
//         // 上一期逾期并且还款,这一期还没到,不算逾期
//         // 上一期不逾期,这一期还没到也就是还没逾期,不算逾期
//         // 如果当前期逾期,不管有没有到封账日,都算逾期
//         int repayStatus = planDO.getRepayStatus();
//         return isOverdue == 1 ? (repayStatus == 5 ? 0 : 1) : 0;
//     }
//
//
//     private BigDecimal stringToDecimal(String val) {
//         if ("\\N".equals(val)) {
//             val = "0";
//         }
//         return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
//     }
//
//
//     /**
//      * 递归查找，最早的的逾期期数
//      *
//      * @param map term为key，还款计划为val
//      * @param
//      */
//     public static void fetchTerm(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
//         int key = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         List<RepaymentPlanDO> planDOList = map.get(key);
//         RepaymentPlanDO planDO = planDOList.get(0);
//         if (planDOList.size() > 1) {
//             for (RepaymentPlanDO repaymentPlanDO : planDOList) {
//                 if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                     planDO = repaymentPlanDO;
//                 }
//             }
//         }
//         if (planDO != null) {
//             String isOverdue = planDO.getIsOverdue();
//             Integer repayStatus = planDO.getRepayStatus();
//             Integer overdue = planDO.getOverdue();
//             LocalDateTime realRepayTime = planDO.getRealRepayTime();
//             BigDecimal term = CalculateUtil.toDecimal(resMap.get("term"));
//
//             // //  本期逾期了判断上一期是否逾期,上一期未逾期，那么逾期期数就是本期
//             // // 本期逾期了判断上一期是否逾期,上一期逾期，并且未还,继续向上判断,直到找到已还款的那一期,那一期+1就是逾期未还金额
//             // // 本期逾期了判断上一期是否逾期,上一期逾期并且已经还款,逾期金额用本期算
//             LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
//                     dateFormatter), LocalTime.MAX);
//
//             if ("1".equals(isOverdue)) {
//                 //先判断本期的状态是否已逾期未还款
//                 if (repayStatus == 1 && overdue == 5) {
//                     if (term.compareTo(new BigDecimal(1)) == 0) {
//                         resMap.put("term", term);
//                     } else {
//                         // 递归判断判断上一期的订单是否逾期
//                         term = term.subtract(BigDecimal.valueOf(1));
//                         resMap.put("term", term);
//                         fetchTerm(map, resMap);
//                     }
//                 } else {
//                     // 如果已还款，但是还是逾期(本身实际还款时间大于本期封账日)
//                     // 还要判断递归到的这一期的时间还款时间，是否大于最外层这一期的封账日，是的话向上递归，不是的话加一停止递归
//                     // 再递归往上判断
//                     if (realRepayTime != null && realRepayTime.compareTo(endTime) > 0) {
//                         if (term.compareTo(new BigDecimal(1)) == 0) {
//                             resMap.put("term", term);
//                         } else {
//                             term = term.subtract(BigDecimal.valueOf(1));
//                             resMap.put("term", term);
//                             fetchTerm(map, resMap);
//                         }
//                     } else {
//                         term = term.add(BigDecimal.valueOf(1));
//                         resMap.put("term", term);
//                     }
//                 }
//
//             } else {
//                 term = term.add(BigDecimal.valueOf(1));
//                 resMap.put("term", term);
//             }
//
//
//         }
//
//     }
//
//     public static void fetchCapital(Map<Integer, List<RepaymentPlanDO>> map, Map<String, Object> resMap) {
//         int sourceTerm = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         LocalDateTime endTime = LocalDateTime.of(LocalDate.parse(String.valueOf(resMap.get("endTime")),
//                 dateFormatter), LocalTime.MAX);
//         resMap.put("level", 1);
//         if (sourceTerm > 1) {
//             fetchTerm(map, resMap);
//         }
//         int term = CalculateUtil.toDecimal(resMap.get("term")).intValue();
//         for (Map.Entry<Integer, List<RepaymentPlanDO>> entry : map.entrySet()) {
//             List<RepaymentPlanDO> value = entry.getValue();
//             RepaymentPlanDO planDO = value.get(0);
//             if (value.size() > 1) {
//                 for (RepaymentPlanDO repaymentPlanDO : value) {
//                     if (Integer.valueOf(0).equals(repaymentPlanDO.getIsDeleted())) {
//                         planDO = repaymentPlanDO;
//                     }
//                 }
//             }
//             if (entry.getKey() >= term) {
//                 BigDecimal capital;
//                 if (term == entry.getKey()) {
//                     // 实际还款时间大于这一期封账日,那么这一期将当没还
//                     if (planDO.getRealRepayTime() == null) {
//                         capital = planDO.getCapital();
//                     } else if (planDO.getRealRepayTime() != null && planDO.getRealRepayTime().compareTo(endTime) > 0) {
//                         capital = planDO.getCapital();
//                     } else {
//                         capital = planDO.getRealCapital();
//                     }
//                 } else {
//                     capital = planDO.getCapital();
//                 }
//                 resMap.put("capital", CalculateUtil.toDecimal(resMap.get("capital")).add(capital));
//             }
//         }
//     }
//
//
//     private void setOverdueVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
//                                BigDecimal overdueFine) {
//         if (term == 1) {
//             riskOrderOverdueDO.setIsOverdue1(isOverdue);
//             riskOrderOverdueDO.setOverdueFine1(overdueFine);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setIsOverdue2(isOverdue);
//             riskOrderOverdueDO.setOverdueFine2(overdueFine);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setIsOverdue3(isOverdue);
//             riskOrderOverdueDO.setOverdueFine3(overdueFine);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setIsOverdue4(isOverdue);
//             riskOrderOverdueDO.setOverdueFine4(overdueFine);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setIsOverdue5(isOverdue);
//             riskOrderOverdueDO.setOverdueFine5(overdueFine);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setIsOverdue6(isOverdue);
//             riskOrderOverdueDO.setOverdueFine6(overdueFine);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setIsOverdue7(isOverdue);
//             riskOrderOverdueDO.setOverdueFine7(overdueFine);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setIsOverdue8(isOverdue);
//             riskOrderOverdueDO.setOverdueFine8(overdueFine);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setIsOverdue9(isOverdue);
//             riskOrderOverdueDO.setOverdueFine9(overdueFine);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setIsOverdue10(isOverdue);
//             riskOrderOverdueDO.setOverdueFine10(overdueFine);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setIsOverdue11(isOverdue);
//             riskOrderOverdueDO.setOverdueFine11(overdueFine);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setIsOverdue12(isOverdue);
//             riskOrderOverdueDO.setOverdueFine12(overdueFine);
//         }
//     }
//
//     private void setRenewAmt(RiskGeneralOrderOverdueDO riskOrderOverdueDO,
//                              int term,
//                              BigDecimal renewAmt) {
//         if (term == 1) {
//             riskOrderOverdueDO.setRenewAmt1(renewAmt);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setRenewAmt2(renewAmt);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setRenewAmt3(renewAmt);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setRenewAmt4(renewAmt);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setRenewAmt5(renewAmt);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setRenewAmt6(renewAmt);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setRenewAmt7(renewAmt);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setRenewAmt8(renewAmt);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setRenewAmt9(renewAmt);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setRenewAmt10(renewAmt);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setRenewAmt11(renewAmt);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setRenewAmt12(renewAmt);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setRenewAmt13(renewAmt);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setRenewAmt14(renewAmt);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setRenewAmt15(renewAmt);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setRenewAmt16(renewAmt);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setRenewAmt17(renewAmt);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setRenewAmt18(renewAmt);
//         } else if (term == 19) {
//             riskOrderOverdueDO.setRenewAmt19(renewAmt);
//         } else if (term == 20) {
//             riskOrderOverdueDO.setRenewAmt20(renewAmt);
//         } else if (term == 21) {
//             riskOrderOverdueDO.setRenewAmt21(renewAmt);
//         } else if (term == 22) {
//             riskOrderOverdueDO.setRenewAmt22(renewAmt);
//         } else if (term == 23) {
//             riskOrderOverdueDO.setRenewAmt23(renewAmt);
//         } else if (term == 24) {
//             riskOrderOverdueDO.setRenewAmt24(renewAmt);
//         } else if (term == 25) {
//             riskOrderOverdueDO.setRenewAmt25(renewAmt);
//         } else if (term == 26) {
//             riskOrderOverdueDO.setRenewAmt26(renewAmt);
//         } else if (term == 27) {
//             riskOrderOverdueDO.setRenewAmt27(renewAmt);
//         } else if (term == 28) {
//             riskOrderOverdueDO.setRenewAmt28(renewAmt);
//         } else if (term == 29) {
//             riskOrderOverdueDO.setRenewAmt29(renewAmt);
//         } else if (term == 30) {
//             riskOrderOverdueDO.setRenewAmt30(renewAmt);
//         }
//     }
//
//     private void setOverdueVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, Integer isOverdue,
//                                BigDecimal overdueFine) {
//         if (term == 1) {
//             riskOrderOverdueDO.setIsOverdue1(isOverdue);
//             riskOrderOverdueDO.setOverdueFine1(overdueFine);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setIsOverdue2(isOverdue);
//             riskOrderOverdueDO.setOverdueFine2(overdueFine);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setIsOverdue3(isOverdue);
//             riskOrderOverdueDO.setOverdueFine3(overdueFine);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setIsOverdue4(isOverdue);
//             riskOrderOverdueDO.setOverdueFine4(overdueFine);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setIsOverdue5(isOverdue);
//             riskOrderOverdueDO.setOverdueFine5(overdueFine);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setIsOverdue6(isOverdue);
//             riskOrderOverdueDO.setOverdueFine6(overdueFine);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setIsOverdue7(isOverdue);
//             riskOrderOverdueDO.setOverdueFine7(overdueFine);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setIsOverdue8(isOverdue);
//             riskOrderOverdueDO.setOverdueFine8(overdueFine);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setIsOverdue9(isOverdue);
//             riskOrderOverdueDO.setOverdueFine9(overdueFine);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setIsOverdue10(isOverdue);
//             riskOrderOverdueDO.setOverdueFine10(overdueFine);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setIsOverdue11(isOverdue);
//             riskOrderOverdueDO.setOverdueFine11(overdueFine);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setIsOverdue12(isOverdue);
//             riskOrderOverdueDO.setOverdueFine12(overdueFine);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setIsOverdue13(isOverdue);
//             riskOrderOverdueDO.setOverdueFine13(overdueFine);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setIsOverdue14(isOverdue);
//             riskOrderOverdueDO.setOverdueFine14(overdueFine);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setIsOverdue15(isOverdue);
//             riskOrderOverdueDO.setOverdueFine15(overdueFine);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setIsOverdue16(isOverdue);
//             riskOrderOverdueDO.setOverdueFine16(overdueFine);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setIsOverdue17(isOverdue);
//             riskOrderOverdueDO.setOverdueFine17(overdueFine);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setIsOverdue18(isOverdue);
//             riskOrderOverdueDO.setOverdueFine18(overdueFine);
//         } else if (term == 19) {
//             riskOrderOverdueDO.setIsOverdue19(isOverdue);
//             riskOrderOverdueDO.setOverdueFine19(overdueFine);
//         } else if (term == 20) {
//             riskOrderOverdueDO.setIsOverdue20(isOverdue);
//             riskOrderOverdueDO.setOverdueFine20(overdueFine);
//         } else if (term == 21) {
//             riskOrderOverdueDO.setIsOverdue21(isOverdue);
//             riskOrderOverdueDO.setOverdueFine21(overdueFine);
//         } else if (term == 22) {
//             riskOrderOverdueDO.setIsOverdue22(isOverdue);
//             riskOrderOverdueDO.setOverdueFine22(overdueFine);
//         } else if (term == 23) {
//             riskOrderOverdueDO.setIsOverdue23(isOverdue);
//             riskOrderOverdueDO.setOverdueFine23(overdueFine);
//         } else if (term == 24) {
//             riskOrderOverdueDO.setIsOverdue24(isOverdue);
//             riskOrderOverdueDO.setOverdueFine24(overdueFine);
//         } else if (term == 25) {
//             riskOrderOverdueDO.setIsOverdue25(isOverdue);
//             riskOrderOverdueDO.setOverdueFine25(overdueFine);
//         } else if (term == 26) {
//             riskOrderOverdueDO.setIsOverdue26(isOverdue);
//             riskOrderOverdueDO.setOverdueFine26(overdueFine);
//         } else if (term == 27) {
//             riskOrderOverdueDO.setIsOverdue27(isOverdue);
//             riskOrderOverdueDO.setOverdueFine27(overdueFine);
//         } else if (term == 28) {
//             riskOrderOverdueDO.setIsOverdue28(isOverdue);
//             riskOrderOverdueDO.setOverdueFine28(overdueFine);
//         } else if (term == 29) {
//             riskOrderOverdueDO.setIsOverdue29(isOverdue);
//             riskOrderOverdueDO.setOverdueFine29(overdueFine);
//         } else if (term == 30) {
//             riskOrderOverdueDO.setIsOverdue30(isOverdue);
//             riskOrderOverdueDO.setOverdueFine30(overdueFine);
//         }
//     }
//
//     private void setTermNotRepayVal(RiskOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
//         if (term == 1) {
//             riskOrderOverdueDO.setTerm1(decimal);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setTerm2(decimal);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setTerm3(decimal);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setTerm4(decimal);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setTerm5(decimal);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setTerm6(decimal);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setTerm7(decimal);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setTerm8(decimal);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setTerm9(decimal);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setTerm10(decimal);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setTerm11(decimal);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setTerm12(decimal);
//         }
//     }
//
//     private void setTermNotRepayVal(RiskGeneralOrderOverdueDO riskOrderOverdueDO, int term, BigDecimal decimal) {
//         if (term == 1) {
//             riskOrderOverdueDO.setTerm1(decimal);
//         } else if (term == 2) {
//             riskOrderOverdueDO.setTerm2(decimal);
//         } else if (term == 3) {
//             riskOrderOverdueDO.setTerm3(decimal);
//         } else if (term == 4) {
//             riskOrderOverdueDO.setTerm4(decimal);
//         } else if (term == 5) {
//             riskOrderOverdueDO.setTerm5(decimal);
//         } else if (term == 6) {
//             riskOrderOverdueDO.setTerm6(decimal);
//         } else if (term == 7) {
//             riskOrderOverdueDO.setTerm7(decimal);
//         } else if (term == 8) {
//             riskOrderOverdueDO.setTerm8(decimal);
//         } else if (term == 9) {
//             riskOrderOverdueDO.setTerm9(decimal);
//         } else if (term == 10) {
//             riskOrderOverdueDO.setTerm10(decimal);
//         } else if (term == 11) {
//             riskOrderOverdueDO.setTerm11(decimal);
//         } else if (term == 12) {
//             riskOrderOverdueDO.setTerm12(decimal);
//         } else if (term == 13) {
//             riskOrderOverdueDO.setTerm13(decimal);
//         } else if (term == 14) {
//             riskOrderOverdueDO.setTerm14(decimal);
//         } else if (term == 15) {
//             riskOrderOverdueDO.setTerm15(decimal);
//         } else if (term == 16) {
//             riskOrderOverdueDO.setTerm16(decimal);
//         } else if (term == 17) {
//             riskOrderOverdueDO.setTerm17(decimal);
//         } else if (term == 18) {
//             riskOrderOverdueDO.setTerm18(decimal);
//         } else if (term == 19) {
//             riskOrderOverdueDO.setTerm19(decimal);
//         } else if (term == 20) {
//             riskOrderOverdueDO.setTerm20(decimal);
//         } else if (term == 21) {
//             riskOrderOverdueDO.setTerm21(decimal);
//         } else if (term == 22) {
//             riskOrderOverdueDO.setTerm22(decimal);
//         } else if (term == 23) {
//             riskOrderOverdueDO.setTerm23(decimal);
//         } else if (term == 24) {
//             riskOrderOverdueDO.setTerm24(decimal);
//         } else if (term == 25) {
//             riskOrderOverdueDO.setTerm25(decimal);
//         } else if (term == 26) {
//             riskOrderOverdueDO.setTerm26(decimal);
//         } else if (term == 27) {
//             riskOrderOverdueDO.setTerm27(decimal);
//         } else if (term == 28) {
//             riskOrderOverdueDO.setTerm28(decimal);
//         } else if (term == 29) {
//             riskOrderOverdueDO.setTerm29(decimal);
//         } else if (term == 30) {
//             riskOrderOverdueDO.setTerm30(decimal);
//         }
//     }
//
//     private BigDecimal getParentNotReturnAmt(RiskGeneralOrderOverdueDO parentDO, Integer term) {
//         BigDecimal amt = BigDecimal.ZERO;
//         if (term == 1) {
//             amt = parentDO.getTerm1();
//         } else if (term == 2) {
//             amt = parentDO.getTerm2();
//         } else if (term == 3) {
//             amt = parentDO.getTerm3();
//         } else if (term == 4) {
//             amt = parentDO.getTerm4();
//         } else if (term == 5) {
//             amt = parentDO.getTerm5();
//         } else if (term == 6) {
//             amt = parentDO.getTerm6();
//         } else if (term == 7) {
//             amt = parentDO.getTerm7();
//         } else if (term == 8) {
//             amt = parentDO.getTerm8();
//         } else if (term == 9) {
//             amt = parentDO.getTerm9();
//         } else if (term == 10) {
//             amt = parentDO.getTerm10();
//         } else if (term == 11) {
//             amt = parentDO.getTerm11();
//         } else if (term == 12) {
//             amt = parentDO.getTerm12();
//         }
//         return amt;
//     }
// }
//
//
//
