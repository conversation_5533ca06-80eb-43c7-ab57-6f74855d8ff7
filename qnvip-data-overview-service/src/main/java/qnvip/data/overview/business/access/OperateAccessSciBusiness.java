package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.OperateAccessSciDO;
import qnvip.data.overview.service.access.OperateAccessSciService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateAccessSciBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateAccessSciService accessSciService;

    void initMap(Map<OperateAccessSciDO, OperateAccessSciDO> scene2Do, OperateAccessSciDO sourceDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!scene2Do.containsKey(sourceDO)) {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            domain.setCountDay(countDay);
            domain.setDateType(sourceDO.getDateType());
            domain.setMiniType(sourceDO.getMiniType());
            scene2Do.put(sourceDO, domain);
        }

    }


    /**
     * 定时调度任务
     */
    public void runCore(String ds) {

        try {
            Map<OperateAccessSciDO, OperateAccessSciDO> scene2Do = new HashMap<>();
            CompletableFuture<List<OperateAccessSciDO>> f6 = CompletableFuture.supplyAsync(()->getThisMonthUsrData(ds));
            CompletableFuture<List<OperateAccessSciDO>> f7 = CompletableFuture.supplyAsync(()->getThisMonthGmvData(ds));
            CompletableFuture<List<OperateAccessSciDO>> f8 = CompletableFuture.supplyAsync(()->getThisMonthAgoUsrData(ds));
            CompletableFuture<List<OperateAccessSciDO>> f9 = CompletableFuture.supplyAsync(()->getThisMonthAgoGmvData(ds));
            CompletableFuture<List<OperateAccessSciDO>> f10 =
                    CompletableFuture.supplyAsync(()->getGmvData(ds));
            CompletableFuture.allOf(f6, f7, f8, f9, f10).join();
            // 上个月
            getUsr(scene2Do, f6);
            getGmv(scene2Do, f7);
            getDayGmv(scene2Do, f10);
            // 上上个月
            getUsr(scene2Do, f8);
            getGmv(scene2Do, f9);
            // 写入mysql
            Collection<OperateAccessSciDO> values = scene2Do.values();
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            accessSciService.removeDataByTime(countDay);
            accessSciService.saveBatch(values);

        } catch (Exception e) {
            log.error("OperateAccessSourceBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void getUsr(Map<OperateAccessSciDO, OperateAccessSciDO> scene2Do, CompletableFuture<List<OperateAccessSciDO>> future) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSciDO coreDO : future.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSciDO domain = scene2Do.get(coreDO);
            domain.setTolUsrCnt(coreDO.getTolUsrCnt());
            domain.setKeepUsrCnt(coreDO.getKeepUsrCnt());
            domain.setKeepRate(coreDO.getKeepRate());
        }
    }

    private void getGmv(Map<OperateAccessSciDO, OperateAccessSciDO> scene2Do, CompletableFuture<List<OperateAccessSciDO>> future) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSciDO coreDO : future.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSciDO domain = scene2Do.get(coreDO);
            domain.setGmv(coreDO.getGmv());
            domain.setPayCnt(coreDO.getPayCnt());
        }
    }


    private void getDayGmv(Map<OperateAccessSciDO, OperateAccessSciDO> scene2Do,
                           CompletableFuture<List<OperateAccessSciDO>> future) throws InterruptedException, java.util.concurrent.ExecutionException {
        for (OperateAccessSciDO coreDO : future.get()) {
            initMap(scene2Do, coreDO);
            OperateAccessSciDO domain = scene2Do.get(coreDO);
            domain.setGmvByDay(coreDO.getGmvByDay());
            domain.setPayCntByDay(coreDO.getPayCntByDay());
        }
    }

    /**
     * 获取上个月用户信息
     */
    private List<OperateAccessSciDO> getThisMonthUsrData(String ds) {
        String sql = "select mini_type,count(distinct a.customer_third_id)                      tol_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null))       keep_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null)) / count(distinct a.customer_third_id) keep_rate" +
                " from (select count(distinct date (report_time)) aa, customer_third_id,mini_type" +
                "      from dataview_track_enter_applets" +
                "      where ds = "+ds +
                "        and date (report_time) between dateadd(to_date(${fromTime}),-30,'dd') and dateadd(to_date(${fromTime})" +
                " ,-1,'dd')" +
                "        and mini_type in (1, 2, 4, 5, 7)" +
                "        and action_type = 1" +
                " group by customer_third_id ,mini_type) a" +
                "   group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            String tolUsrCnt = record.getString("tol_usr_cnt");
            String keepUsrCnt = record.getString("keep_usr_cnt");
            String keepRate = record.getString("keep_rate");
            String miniType = record.getString("mini_type");
            domain.setTolUsrCnt(Long.valueOf(tolUsrCnt));
            domain.setKeepRate(Double.valueOf(keepRate));
            domain.setKeepUsrCnt(Long.valueOf(keepUsrCnt));
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setDateType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取上个月支付信息
     */
    private List<OperateAccessSciDO> getThisMonthGmvData(String ds) {
        String sql = "select count(distinct out_trade_no) pay_cnt," +
                "       sum(pay_amt) gmv," +
                "       mini_type" +
                " from rent_alipay_order_pay" +
                " where is_deleted = 0" +
                " and mini_type in (1, 2, 4, 5, 7)" +
                " and status = 10" +
                " and biz_type in (1,4)" +
                " and pay_type in (1,4,5)" +
                " and status = 10" +
                " and ds = "+ds +
                " and date(create_time) between dateadd(to_date(${fromTime}),-30,'dd') and dateadd(to_date(${fromTime}),-1 ," +
                " 'dd')" +
                " group by mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            String payCnt = record.getString("pay_cnt");
            String gmv = record.getString("gmv");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayCnt(Long.valueOf(payCnt));
            domain.setGmv(new BigDecimal(gmv));
            domain.setDateType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取上个月用户信息
     */
    private List<OperateAccessSciDO> getThisMonthAgoUsrData(String ds) {
        String sql = "select mini_type,count(distinct a.customer_third_id)                      tol_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null))       keep_usr_cnt," +
                "       count(distinct if(a.aa > 1, a.customer_third_id, null)) / count(distinct a.customer_third_id) keep_rate" +
                " from (select count(distinct date (report_time)) aa, customer_third_id,mini_type" +
                "      from dataview_track_enter_applets" +
                "      where ds = "+ds +
                "        and date (report_time) between dateadd(to_date(${fromTime}),-60,'dd') and dateadd(to_date(${fromTime})" +
                " ,-31,'dd')" +
                "        and mini_type in (1, 2, 4, 5, 7)" +
                "        and action_type = 1" +
                " group by customer_third_id ,mini_type) a" +
                "   group by a.mini_type";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(record -> {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            String tolUsrCnt = record.getString("tol_usr_cnt");
            String keepUsrCnt = record.getString("keep_usr_cnt");
            String keepRate = record.getString("keep_rate");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setTolUsrCnt(Long.valueOf(tolUsrCnt));
            domain.setKeepRate(Double.valueOf(keepRate));
            domain.setKeepUsrCnt(Long.valueOf(keepUsrCnt));
            domain.setDateType(2);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取上上个月支付信息
     */
    private List<OperateAccessSciDO> getThisMonthAgoGmvData(String ds) {
        String sql = "select count(distinct out_trade_no) pay_cnt," +
                "       sum(pay_amt) gmv," +
                "       mini_type " +
                " from rent_alipay_order_pay" +
                " where is_deleted = 0" +
                " and mini_type in (1, 2, 4, 5, 7)" +
                " and status = 10" +
                " and biz_type in (1,4)" +
                " and pay_type in (1,4,5)" +
                " and ds = "+ds +
                " and date(create_time) between dateadd(to_date(${fromTime}),-60,'dd') and dateadd(to_date(${fromTime}),-31 ," +
                " 'dd')" +
                "  group by mini_type";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(record -> {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            String payCnt = record.getString("pay_cnt");
            String gmv = record.getString("gmv");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayCnt(Long.valueOf(payCnt));
            domain.setGmv(new BigDecimal(gmv));
            domain.setDateType(2);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取昨日支付信息
     */
    private List<OperateAccessSciDO> getGmvData(String ds) {
        String sql = "select count(distinct out_trade_no) pay_cnt," +
                "       sum(pay_amt) gmv," +
                "       mini_type " +
                " from rent_alipay_order_pay" +
                " where is_deleted = 0" +
                " and mini_type in (1, 2, 4, 5, 7)" +
                " and status = 10" +
                " and biz_type in (1,4)" +
                " and pay_type in (1,4,5)" +
                " and ds = "+ds +
                " and create_time between ${fromTime} and ${toTime}" +
                "  group by mini_type";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        String format = SqlUtils.processTemplate(sql, key2value);
        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }

        return records.stream().map(record -> {
            OperateAccessSciDO domain = new OperateAccessSciDO();
            String payCnt = record.getString("pay_cnt");
            String gmv = record.getString("gmv");
            String miniType = record.getString("mini_type");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setPayCntByDay(Long.valueOf(payCnt));
            domain.setGmvByDay(new BigDecimal(gmv));
            domain.setDateType(2);
            return domain;
        }).collect(Collectors.toList());
    }
}
