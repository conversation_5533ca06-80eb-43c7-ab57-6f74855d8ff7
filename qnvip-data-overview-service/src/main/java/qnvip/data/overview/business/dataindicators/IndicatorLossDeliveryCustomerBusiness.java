package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorLossDeliveryCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorLossDeliveryCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/2/10
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLossDeliveryCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorLossDeliveryCustomerService indicatorLossDeliveryCustomerService;


    /**
     * 启动计算任务（计算30天的数据）
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorLossDeliveryCustomerDO> miniType2Map = new HashMap<>();
        countRegisterAndNotOrder(ds, sTime, eTime, miniType2Map);
        countLtNhRegisterCount(ds, sTime, eTime, miniType2Map);
        countPlanTerm(ds, sTime, eTime, miniType2Map);
        countPlanByFreeBond(ds, sTime, eTime, miniType2Map);
        countServiceOrder(ds, sTime, eTime, true, miniType2Map);
        countServiceOrder(ds, sTime, eTime, false, miniType2Map);
        ArrayList<IndicatorLossDeliveryCustomerDO> list = Lists.newArrayList();
        for (IndicatorLossDeliveryCustomerDO value : miniType2Map.values()) {
            value.setPlanFirstTotal(value.getPlanFirstOneTerm() + value.getPlanFirstTwoTerm());
            value.setPlanSecondTotal(value.getPlanSecondOneTerm() + value.getPlanSecondTwoTerm());
            list.add(value);
        }
        if (CollUtil.isNotEmpty(list)) {
            indicatorLossDeliveryCustomerService.saveOrUpdateBatch(list,2000);
        }
    }


    /**
     * 未发货人数中 N小时内注册人数（一天天查询）
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorLossDeliveryCustomerDO> collectMap) {
        String sql = " select a.mini_type " +
                "     , to_char(a.access_time, 'yyyy-mm-dd') day " +
                "     , count(distinct b.id)                 count " +
                "from ( " +
                "         select customer_third_id " +
                "              , min(report_time) as access_time " +
                "              , mini_type " +
                "         from ( " +
                "                  select a.customer_third_id " +
                "                       , a.report_time " +
                "                       , a.mini_type " +
                "                  from dataview_track_enter_applets a " +
                "                           inner join rent_customer c " +
                "                                      on get_json_object(c.bindinfo, '$[0].miniUserId') = a" +
                ".customer_third_id " +
                "                           inner join ( " +
                "                      select customer_id " +
                "                           , to_char(create_time, 'yyyy-mm-dd') day " +
                "                           , mini_type " +
                "                      from ( " +
                "                               select a.customer_id " +
                "                                    , a.mini_type " +
                "                                    , a.create_time " +
                "                               from rent_order a " +
                "                                        left join rent_order_audit b " +
                "                                                  on a.id = b.order_id " +
                "                               where a.is_deleted = 0 " +
                "                                 and a.parent_id = 0 " +
                "                                 and a.type = 1 " +
                "                                 and a.merchant_id = 100 " +
                "                                 and a.biz_type = 2 " +
                "                                 and a.create_time between '" + sTime + "' " +
                "                                   and '" + eTime + "' " +
                "                                 and a.payment_time is not null " +
                "                                 and a.id not in (select a.id " +
                "                                                  from rent_order a " +
                "                                                           inner join rent_order_logistics b on a.id" +
                " = b.order_id " +
                "                                                  where a.merchant_id = 100 " +
                "                                                    and a.type = 1 " +
                "                                                    and a.parent_id = 0 " +
                "                                                    and a.biz_type = 2 " +
                "                                                    and a.create_time between '" + sTime + "' " +
                " and '" + eTime + "' " +
                "                                                    and a.ds = " + ds +
                "                                                    and b.ds = " + ds +
                ") " +
                "                                 and a.ds = " + ds +
                "                                 and b.ds = " + ds +
                "                               order by a.create_time desc " +
                "                           ) " +
                "                      group by customer_id " +
                "                             , mini_type " +
                "                             , to_char(create_time, 'yyyy-mm-dd') " +
                "                  ) b " +
                "                                      on a.mini_type = b.mini_type " +
                "                                          and c.id = b.customer_id " +
                "                                          and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "                  where a.action_type = 1 " +
                "                    and a.ds = " + ds +
                "                    and c.ds = " + ds +
                "                    and a.report_time between '" + sTime + "' " +
                "                      and '" + eTime + "' " +
                "                  order by a.report_time desc " +
                "              ) " +
                "         group by customer_third_id " +
                "                , mini_type " +
                "                , to_char(report_time, 'yyyy-mm-dd') " +
                "     ) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                " where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds =  " + ds +
                " group by a.mini_type " +
                "       , to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            initMap(collectMap, miniType, day);
            IndicatorLossDeliveryCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(count);
        }
    }


    /**
     * 未发货人数中 已注册未下单人数(首次下单)
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countRegisterAndNotOrder(String ds, String sTime, String eTime,
                                          Map<String, IndicatorLossDeliveryCustomerDO> collectMap) {
        String sql = " select c.mini_type, c.day, count(distinct a.id) count " +
                "from rent_customer a " +
                "     inner join ( " +
                "        select min(id) id,min(create_time) create_time,customer_id from ( " +
                "            select id,customer_id,create_time from rent_order where ds = " + ds + " order by id desc" +
                "        ) group by customer_id,to_char(create_time,'yyyy-mm-dd') " +
                "     ) b on a.id = b.customer_id " +
                "     inner join ( " +
                "        select a.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day,a.customer_id " +
                "            from rent_order a " +
                "                     left join rent_order_audit b " +
                "                               on a.id = b.order_id " +
                "            where a.is_deleted = 0 " +
                "              and a.parent_id = 0 " +
                "              and a.type = 1 " +
                "              and a.merchant_id = 100 " +
                "              and a.biz_type = 2 " +
                "              and a.create_time between '" + sTime + "' " +
                "                and '" + eTime + "' " +
                "              and a.payment_time is not null " +
                "              and a.id not in ( " +
                "                select a.id " +
                "                from rent_order a " +
                "                         inner join rent_order_logistics b " +
                "                                    on a.id = b.order_id " +
                "                where a.merchant_id = 100 " +
                "                  and a.type = 1 " +
                "                  and a.parent_id = 0 " +
                "                  and a.biz_type = 2 " +
                "                  and a.create_time between '" + sTime + "' " +
                "                    and '" + eTime + "' " +
                "                  and a.ds = " + ds +
                "                  and b.ds = " + ds +
                "            ) " +
                "              and a.ds = " + ds +
                "              and b.ds = " + ds +
                "         group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "            , a.mini_type,a.customer_id " +
                "     ) c on a.id = c.customer_id and to_char(b.create_time, 'yyyy-mm-dd') = c.day " +
                " where to_char(a.create_time, 'yyyy-mm-dd') != to_char(b.create_time, 'yyyy-mm-dd') " +
                "    and a.ds = " + ds +
                " group by c.mini_type, c.day; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossDeliveryCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterAndNotOrder(count);
        }
    }


    /**
     * 未发货人数中 服务中/结束客户数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countServiceOrder(String ds, String sTime, String eTime, Boolean inServiceFlag,
                                   Map<String, IndicatorLossDeliveryCustomerDO> collectMap) {
        String sql = " select  mini_type " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "        ,count(distinct a.customer_id) count " +
                "from    ( " +
                "            select  mini_type " +
                "                    ,max(a.create_time) create_time " +
                "                    ,a.customer_id " +
                "            from    rent_order a " +
                "            left join rent_order_audit b " +
                "            on      a.id = b.order_id " +
                "            where   a.is_deleted = 0 " +
                "            and     a.parent_id = 0 " +
                "            and     a.type = 1 " +
                "            and     a.merchant_id = 100 " +
                "            and     a.biz_type = 2 " +
                "            and     a.create_time between '" + sTime + "' " +
                "            and     '" + eTime + "' " +
                "            and     a.payment_time is not null " +
                "            and     a.id not in ( select a.id from rent_order a inner join rent_order_logistics b on" +
                " a.id = b.order_id where a.merchant_id = 100 and a.type = 1 and a.parent_id = 0 and a.biz_type = 2 " +
                " and a.create_time between '" + sTime + "' and '" + eTime + "' and a.ds = " + ds + " and b" +
                ".ds = " + ds + " ) " +
                "            and     a.ds = " + ds +
                "            and     b.ds = " + ds +
                "            group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "                     ,a.mini_type,a.customer_id " +
                "        ) a inner " +
                "join    ( " +
                "            select  min(create_time) create_time " +
                "                    ,min(termination) termination " +
                "                    ,customer_id " +
                "            from    ( " +
                "                        select  termination " +
                "                                ,customer_id " +
                "                                ,create_time " +
                "                        from    rent_order " +
                "                        where   customer_id in ( select customer_id from rent_order where is_deleted" +
                " = 0 and merchant_id = 100 and parent_id = 0 and type = 1 and biz_type = 2 and payment_time is not " +
                "null and ds = " + ds + " ) " +
                "                        and     ds = " + ds + " " +
                "                        order by create_time asc " +
                "                    )  " +
                "            group by customer_id " +
                "        ) c " +
                "on      a.customer_id = c.customer_id " +
                "and     to_char(a.create_time,'yyyy-mm-dd') = to_char(c.create_time,'yyyy-mm-dd') " +
                "where   c.termination " + (inServiceFlag ? "!=" : "=") + " 5 " +
                "group by mini_type " +
                "         ,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Integer count = Integer.valueOf(record.getString("count"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorLossDeliveryCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (inServiceFlag) {
                updateDO.setInServiceCount(count);
            } else {
                updateDO.setOutServiceCount(count);
            }
        }
    }


    /**
     * 未发货人数，保证金总期数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanTerm(String ds, String sTime, String eTime,
                               Map<String, IndicatorLossDeliveryCustomerDO> collectMap) {
        String sql = " select mini_type,c.rate_config_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion" +
                "     , c.bond_rate " +
                "     ,count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join rent_order_audit b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id=c.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.id not in ( " +
                "    select a.id " +
                "    from rent_order a " +
                "             inner join rent_order_logistics b " +
                "                        on a.id = b.order_id " +
                "    where a.merchant_id = 100 " +
                "      and a.type = 1 " +
                "      and a.parent_id = 0 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' " +
                "        and '" + eTime + "' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                ") " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by to_char(a.create_time, 'yyyy-mm-dd'),c.rate_config_type " +
                "       , a.mini_type,c.bond_rate,IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorLossDeliveryCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThirdOneTerm(Optional.ofNullable(updateDO.getPlanThirdOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanThirdTwoTerm(Optional.ofNullable(updateDO.getPlanThirdTwoTerm()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanFirstOneTerm(Optional.ofNullable(updateDO.getPlanFirstOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanFirstTwoTerm(Optional.ofNullable(updateDO.getPlanFirstTwoTerm()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanSecondOneTerm(Optional.ofNullable(updateDO.getPlanSecondOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanSecondTwoTerm(Optional.ofNullable(updateDO.getPlanSecondTwoTerm()).orElse(0) + count);
                }
            }
        }
    }


    /**
     * 未发货人数中是否免押分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanByFreeBond(String ds, String sTime, String eTime,
                                     Map<String, IndicatorLossDeliveryCustomerDO> collectMap) {
        String sql = " select mini_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , c.rate_config_type " +
                "     , c.bond_free_status " +
                "     , count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join rent_order_audit b on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c on a.id=c.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.id not in ( " +
                "    select a.id " +
                "    from rent_order a " +
                "             inner join rent_order_logistics b " +
                "                        on a.id = b.order_id " +
                "    where a.merchant_id = 100 " +
                "      and a.type = 1 " +
                "      and a.parent_id = 0 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' " +
                "        and '" + eTime + "' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                ") " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "       , a.mini_type " +
                "       , c.bond_free_status " +
                "       , c.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            initMap(collectMap, miniType, day);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer bondFreeStatus = Integer.valueOf(record.getString("bond_free_status"));
            IndicatorLossDeliveryCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanOneFreeBond(updateDO.getPlanOneFreeBond() + count);
                } else {
                    updateDO.setPlanOneNotFreeBond(updateDO.getPlanOneNotFreeBond() + count);
                }
            } else {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanTwoFreeBond(updateDO.getPlanTwoFreeBond() + count);
                } else {
                    updateDO.setPlanTwoNotFreeBond(updateDO.getPlanTwoNotFreeBond() + count);
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorLossDeliveryCustomerDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorLossDeliveryCustomerDO ioid = new IndicatorLossDeliveryCustomerDO();
            ioid.setMiniType(miniType);
            ioid.setCountDay(countDay);
            ioid.setLtNhRegisterCount(0);
            ioid.setRegisterAndNotOrder(0);
            ioid.setInServiceCount(0);
            ioid.setOutServiceCount(0);
            ioid.setPlanFirstTotal(0);
            ioid.setPlanFirstOneTerm(0);
            ioid.setPlanFirstTwoTerm(0);
            ioid.setPlanSecondTotal(0);
            ioid.setPlanSecondOneTerm(0);
            ioid.setPlanSecondTwoTerm(0);
            ioid.setPlanThirdOneTerm(0);
            ioid.setPlanThirdTwoTerm(0);
            ioid.setPlanOneFreeBond(0);
            ioid.setPlanOneNotFreeBond(0);
            ioid.setPlanTwoFreeBond(0);
            ioid.setPlanTwoNotFreeBond(0);
            miniType2Map.put(key, ioid);
        }
    }


    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}