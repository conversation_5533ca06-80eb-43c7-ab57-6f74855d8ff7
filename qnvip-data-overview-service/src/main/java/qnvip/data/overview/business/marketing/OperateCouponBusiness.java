package qnvip.data.overview.business.marketing;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.marketing.OperateCouponCoreDO;
import qnvip.data.overview.service.marketing.OperateCouponCoreService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCouponBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCouponCoreService couponCoreService;

    void initMap(Map<Long, OperateCouponCoreDO> activityId2Map,
                 OperateCouponCoreDO item) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!activityId2Map.containsKey(item.getCouponId())) {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            domain.setCountDay(countDay);
            domain.setName(item.getName());
            domain.setActivityBeginTime(item.getActivityBeginTime());
            domain.setActivityEndTime(item.getActivityEndTime());
            domain.setType(item.getType());
            domain.setValidityDays(item.getValidityDays());
            domain.setActivityName(item.getActivityName());
            domain.setUseType(item.getUseType());
            domain.setGrantCount(item.getGrantCount());
            domain.setScene(item.getScene());
            domain.setReceiveCount(item.getReceiveCount());
            domain.setCouponId(item.getCouponId());
            activityId2Map.put(item.getCouponId(), domain);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCore() {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            CompletableFuture<List<OperateCouponCoreDO>> f1 =
                    CompletableFuture.supplyAsync(this::getOrderCount);
            CompletableFuture<List<OperateCouponCoreDO>> f2 =
                    CompletableFuture.supplyAsync(this::getGoodsCnt);
            CompletableFuture<List<OperateCouponCoreDO>> f4 =
                    CompletableFuture.supplyAsync(this::getRiskPassCount);
            CompletableFuture<List<OperateCouponCoreDO>> f5 =
                    CompletableFuture.supplyAsync(this::getPayCount);
            CompletableFuture<List<OperateCouponCoreDO>> f6 =
                    CompletableFuture.supplyAsync(this::getCouponInfo);

            CompletableFuture.allOf(f1, f2, f4, f5, f6)
                    .join();
            Map<Long, OperateCouponCoreDO> id2Do = new HashMap<>();

            setActivityInfo(f6, id2Do);
            setOrderCount(f1, id2Do);
            setRiskCount(f4, id2Do);
            setPayCount(f5, id2Do);
            setGoodsCnt(f2, id2Do);
            List<OperateCouponCoreDO> collect = new ArrayList<>(id2Do.values());
            couponCoreService.removeDataByTime(countDay);
            couponCoreService.saveBatch(collect);
        } catch (Exception e) {
            log.error("OperateCouponBusiness.runCore error:{}", e.getMessage());
        }
    }

    private void setActivityInfo(CompletableFuture<List<OperateCouponCoreDO>> future,
                                 Map<Long, OperateCouponCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateCouponCoreDO core : future.get()) {
            initMap(id2Do, core);
        }
    }

    private void setPayCount(CompletableFuture<List<OperateCouponCoreDO>> future,
                             Map<Long, OperateCouponCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateCouponCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateCouponCoreDO domain = id2Do.get(core.getCouponId());
            domain.setPayCount(core.getPayCount());
            domain.setUseCount(core.getUseCount());
        }
    }

    private void setRiskCount(CompletableFuture<List<OperateCouponCoreDO>> future,
                              Map<Long, OperateCouponCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateCouponCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateCouponCoreDO domain = id2Do.get(core.getCouponId());
            domain.setRiskPassCount(core.getRiskPassCount());
        }
    }

    private void setGoodsCnt(CompletableFuture<List<OperateCouponCoreDO>> future,
                             Map<Long, OperateCouponCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateCouponCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateCouponCoreDO domain = id2Do.get(core.getCouponId());
            domain.setAssociatedGoodsCnt(core.getAssociatedGoodsCnt());
        }
    }

    private void setOrderCount(CompletableFuture<List<OperateCouponCoreDO>> future,
                               Map<Long, OperateCouponCoreDO> id2Do)
            throws InterruptedException, ExecutionException {
        for (OperateCouponCoreDO core : future.get()) {
            initMap(id2Do, core);
            OperateCouponCoreDO domain = id2Do.get(core.getCouponId());
            domain.setOrderCount(core.getOrderCount());
            domain.setGmv(core.getGmv());
            domain.setAssociatedUserCnt(core.getAssociatedUserCnt());
        }
    }

    /**
     * 活动订单量
     */
    private List<OperateCouponCoreDO> getOrderCount() {
        String sql = "select rc.id," +
                "       count(ro.id) num," +
                "       count(distinct ro.customer_id) uv," +
                "        sum(rofd.rent_total) amount" +
                " from rent_order ro" +
                "         inner join rent_customer_coupon rco on rco.order_id = ro.id" +
                "         inner join rent_coupon rc on rco.coupon_id = rc.id" +
                "          inner join rent_order_finance_detail rofd on ro.id =rofd.order_id" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and rc.update_time between ${monthFromTime} and ${toTime}" +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rco.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rc.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rofd.ds = to_char(getdate(), 'yyyymmdd') " +
                " group by rc.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            String id = record.getString("id");
            String orderCount = Optional.ofNullable(record.getString("num")).orElse("0");
            String amount = Optional.ofNullable(record.getString("amount")).orElse("0");
            String uv = Optional.ofNullable(record.getString("uv")).orElse("0");
            domain.setCountDay(countDay);
            domain.setOrderCount(Long.valueOf(orderCount));
            domain.setGmv(new BigDecimal(amount));
            domain.setCouponId(Long.valueOf(id));
            domain.setAssociatedUserCnt(Long.valueOf(uv));
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 通审单量
     */
    private List<OperateCouponCoreDO> getRiskPassCount() {
        String sql = "select rc.id," +
                "       count(ro.id) num" +
                " from rent_order ro" +
                "         inner join rent_order_audit rda on ro.id = rda.order_id " +
                "         inner join rent_customer_coupon rco on rco.order_id = ro.id " +
                "         inner join rent_coupon rc on rco.coupon_id = rc.id " +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and rda.audit_status = 1" +
                "  and rda.type = 2" +
                "  and rc.update_time between ${monthFromTime} and ${toTime}" +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rco.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rc.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rda.ds = to_char(getdate(), 'yyyymmdd')" +
                " group by rc.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setCouponId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 付款单量
     */
    private List<OperateCouponCoreDO> getPayCount() {
        String sql = "select rc.id," +
                "       count(ro.id) num," +
                "       sum(rc.discount_amount) amount" +
                " from rent_order ro" +
                "         inner join rent_customer_coupon rco on rco.order_id = ro.id " +
                "         inner join rent_coupon rc on rco.coupon_id = rc.id" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "  and ro.status in (1, 5, 15, 60)" +
                "  and rc.update_time between ${monthFromTime} and ${toTime}" +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rco.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rc.ds = to_char(getdate(), 'yyyymmdd')" +
                " group by rc.id";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            String amount = Optional.ofNullable(record.getString("amount")).orElse("0");
            domain.setCountDay(countDay);
            domain.setPayCount(Long.valueOf(num));
            domain.setUseCount(Long.valueOf(num));
            domain.setMarketingCosts(new BigDecimal(amount));
            domain.setCouponId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 关联商品数
     */
    private List<OperateCouponCoreDO> getGoodsCnt() {
        String sql = "select rc.id," +
                "       count(ri.id) num" +
                " from rent_order ro" +
                "         inner join rent_order_item rdi on ro.id = rdi.order_id" +
                "         inner join rent_item ri on ri.id = rdi.item_id" +
                "         inner join rent_customer_coupon rco on rco.order_id = ro.id " +
                "         inner join rent_coupon rc on rco.coupon_id = rc.id" +
                " where ro.is_deleted = 0" +
                "  and ro.parent_id = 0" +
                "  and ro.type = 1" +
                "  and ri.main_category = 2" +
                "  and ro.merchant_id = 100" +
                "  and rc.update_time between ${monthFromTime} and ${toTime}" +
                "  and ro.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and ri.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rco.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rdi.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and rc.ds = to_char(getdate(), 'yyyymmdd')" +
                " group by rc.id;";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            String id = record.getString("id");
            String num = Optional.ofNullable(record.getString("num")).orElse("0");
            domain.setCountDay(countDay);
            domain.setAssociatedGoodsCnt(Long.valueOf(num));
            domain.setCouponId(Long.valueOf(id));
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 优惠券
     * 信息
     */
    private List<OperateCouponCoreDO> getCouponInfo() {
        String sql = "select rc.name," +
                "       rc.valid_begin_time activity_begin_time," +
                "       rc.valid_end_time     activity_end_time," +
                "       rc.validity_days," +
                "       ra.short_name," +
                "       rc.scene," +
                "       rc.use_type," +
                "       rc.type," +
                "       rc.grant_count," +
                "       rc.receive_count," +
                "       rc.id" +
                " from rent_coupon rc" +
                "         left join rent_activity ra on ra.id = rc.activity_id " +
                "    and   ra.ds = to_char(getdate(), 'yyyymmdd')" +
                "    where rc.ds = to_char(getdate(), 'yyyymmdd')";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateCouponCoreDO domain = new OperateCouponCoreDO();
            String id = record.getString("id");
            String shortName = record.getString("short_name");
            String activityBeginTimeStr = record.getString("activity_begin_time");
            String activityEndTimeStr = record.getString("activity_end_time");
            LocalDateTime activityBeginTime =
                    "\\N".equals(activityBeginTimeStr) ? null : DateUtils.stringToDate(activityBeginTimeStr);
            LocalDateTime activityEndTime =
                    "\\N".equals(activityEndTimeStr) ? null : DateUtils.stringToDate(activityEndTimeStr);

            String type = record.getString("type");
            String validityDays = record.getString("validity_days");
            String name = record.getString("name");
            String scene = record.getString("scene");
            String useType = record.getString("use_type");
            String grantCount = record.getString("grant_count");
            String receiveCount = record.getString("receive_count");
            domain.setCountDay(countDay);
            domain.setCouponId(Long.valueOf(id));
            domain.setActivityName(shortName);
            domain.setName(name);
            domain.setType(Integer.valueOf(type));
            domain.setValidityDays(Integer.valueOf(validityDays));
            domain.setScene(scene);
            domain.setUseType(Integer.valueOf(useType));
            domain.setGrantCount(Long.valueOf(grantCount));
            domain.setReceiveCount(Long.valueOf(receiveCount));
            domain.setActivityBeginTime(activityBeginTime);
            domain.setActivityEndTime(activityEndTime);
            return domain;
        }).collect(Collectors.toList());
    }

}
