package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerPhoneDO;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerPhoneService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerPhoneBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerPhoneService phoneService;

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            phoneService.removeDataByTime(countDay);
            placeTheOrder(ds);
            riskPass(ds);
            onPay(ds);
            signIn(ds);
        } catch (Exception e) {
            log.error("OperateCustomerPhoneBusiness runCore error :{}", e.getMessage());
        }
    }

    /**
     * 下单口径
     */
    private void placeTheOrder(String ds) {
        String sql = "select a.mini_type," +
                "       a.device_brand," +
                "       a.device_model," +
                "       count(a.id) num" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                " where c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and a.action_type = 1" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and c.create_time between ${fromTime} and ${toTime}" +
                "group by a.mini_type, a.device_brand, a.device_model";
        assemble(sql, CustomerTypeEnum.PLACE_THE_ORDER.getTypeCode());
    }

    /**
     * 通审口径
     */
    private void riskPass(String ds) {
        String sql = "select a.mini_type," +
                "       a.device_brand," +
                "       a.device_model," +
                "       count(a.id) num" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on a.id = d.order_id" +
                " where c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and a.action_type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.audit_status =1" +
                "  and d.type =2" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.device_brand, a.device_model";

        assemble(sql, CustomerTypeEnum.RISK_PASS.getTypeCode());
    }

    /**
     * 支付
     */
    private void onPay(String ds) {
        String sql = "select a.mini_type," +
                "       a.device_brand," +
                "       a.device_model," +
                "       count(a.id) num" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on a.id = d.order_id" +
                " where c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and a.action_type = 1" +
                "  and c.merchant_id = 100" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and c.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.device_brand, a.device_model";

        assemble(sql, CustomerTypeEnum.ON_PAY.getTypeCode());
    }

    /**
     * 签收
     */
    private void signIn(String ds) {
        String sql = "select a.mini_type," +
                "       a.device_brand," +
                "       a.device_model," +
                "       count(a.id) num" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_logistics d on a.id = d.order_id" +
                "" +
                " where c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and a.action_type = 1" +
                "  and c.merchant_id = 100" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and d.ds = " + ds +
                "  and d.sign_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.device_brand, a.device_model";

        assemble(sql, CustomerTypeEnum.SIGN_IN.getTypeCode());
    }

    private void assemble(String sql, Integer type) {


        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OperateCustomerPhoneDO> collect = records.stream().map(record -> {
            OperateCustomerPhoneDO domain = new OperateCustomerPhoneDO();
            String miniType = record.getString("mini_type");
            String deviceBrand = record.getString("device_brand");
            String deviceModel = record.getString("device_model");
            String num = record.getString("num");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setType(type);
            domain.setAmount(Long.valueOf(num));
            domain.setBrand(deviceBrand);
            domain.setModel(deviceModel);
            domain.setCountDay(countDay);
            return domain;
        }).collect(Collectors.toList());
        phoneService.saveBatch(collect);
    }


}
