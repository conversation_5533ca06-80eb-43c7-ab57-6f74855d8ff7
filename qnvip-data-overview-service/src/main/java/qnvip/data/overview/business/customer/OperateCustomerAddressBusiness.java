package qnvip.data.overview.business.customer;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.customer.OperateCustomerAddressDO;
import qnvip.data.overview.enums.CustomerAddrTypeEnum;
import qnvip.data.overview.enums.CustomerTypeEnum;
import qnvip.data.overview.service.customer.OperateCustomerAddressService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/18
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateCustomerAddressBusiness {

    private final OdpsUtil odpsUtil;

    private final OperateCustomerAddressService addressService;

    /**
     * 定时调度任务
     */
    public void runCore() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        addressService.removeDataByTime(countDay);
        placeTheOrder();
        riskPass();
        onPay();
        signIn();
    }


    /**
     * 下单口径，收货地址
     */
    private void placeTheOrder() {
        String sql = "select c.receiver_province," +
                "       c.receiver_city," +
                "       a.mini_type," +
                "       count(a.id) num" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, c.receiver_province, c.receiver_city";

        assemble(sql, CustomerTypeEnum.PLACE_THE_ORDER.getTypeCode());
    }

    /**
     * 通审口径
     */
    private void riskPass() {
        String sql = "select c.receiver_province," +
                "       c.receiver_city," +
                "       a.mini_type," +
                "       count(a.id) num" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                "         inner join rent_order_audit d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and merchant_id = 100" +
                "  and d.type = 2" +
                "  and d.audit_status = 1" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, c.receiver_province, c.receiver_city";

        assemble(sql, CustomerTypeEnum.RISK_PASS.getTypeCode());
    }

    /**
     * 支付
     */
    private void onPay() {
        String sql = "select c.receiver_province," +
                "       c.receiver_city," +
                "       a.mini_type," +
                "       count(a.id) num" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                "         inner join rent_order_audit d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and merchant_id = 100" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, c.receiver_province, c.receiver_city";

        assemble(sql, CustomerTypeEnum.ON_PAY.getTypeCode());
    }

    /**
     * 签收
     */
    private void signIn() {
        String sql = "select c.receiver_province," +
                "       c.receiver_city," +
                "       a.mini_type," +
                "       count(a.id) num" +
                " from rent_order a" +
                "         inner join rent_customer b on a.customer_id = b.id" +
                "         inner join rent_order_receiver c on a.id = c.order_id" +
                "         inner join rent_order_logistics d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.parent_id = 0" +
                "  and merchant_id = 100" +
                "  and a.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and b.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and c.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.ds = to_char(getdate(), 'yyyymmdd')" +
                "  and d.sign_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, c.receiver_province, c.receiver_city";

        assemble(sql, CustomerTypeEnum.SIGN_IN.getTypeCode());
    }

    private void assemble(String sql, Integer type) {

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        List<OperateCustomerAddressDO> collect = records.stream().map(record -> {
            OperateCustomerAddressDO domain = new OperateCustomerAddressDO();
            String miniType = record.getString("mini_type");
            String receiverCity = record.getString("receiver_city");
            String receiverProvince = record.getString("receiver_province");
            String num = record.getString("num");

            domain.setMiniType(Integer.valueOf(miniType));
            domain.setType(type);
            domain.setAmount(Long.valueOf(num));
            domain.setAddrType(CustomerAddrTypeEnum.SHIPPING_ADDRESS.getCode());
            domain.setProvinces(receiverProvince);
            domain.setCity(receiverCity);
            domain.setCountDay(countDay);
            return domain;
        }).collect(Collectors.toList());
        addressService.saveBatch(collect);
    }


}
