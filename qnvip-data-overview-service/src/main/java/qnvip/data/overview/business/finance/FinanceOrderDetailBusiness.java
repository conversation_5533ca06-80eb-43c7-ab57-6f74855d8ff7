package qnvip.data.overview.business.finance;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.business.web.FishOrderBusiness;
import qnvip.data.overview.domain.finance.AppBiRentOrderLabelDO;
import qnvip.data.overview.domain.finance.FinanceOrderDetailDO;
import qnvip.data.overview.enums.OrderStatusEnum;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.finance.AppBiRentOrderLabelService;
import qnvip.data.overview.service.finance.FinanceOrderDetailService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.DingDingAlarmUtil;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class FinanceOrderDetailBusiness {

    private static final int PAGE_SIZE = 5000;
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final FinanceOrderDetailService financeOrderDetailService;
    private final FinanceReportBusiness financeReportBusiness;
    private final AppBiRentOrderLabelService appBiRentOrderLabelService;

    private final DingDingAlarmUtil dingDingAlarmUtil;


    public void execCount(String ds) {
        String testerIds = financeReportBusiness.getTesterIds(ds);
        financeOrderDetailService.deleteAll();
        countOrderDetail(ds, testerIds);
    }


    private void countOrderDetail(String ds, String testerIds) {
        String prefix1 = " select count(1) count ";
        String prefix2 = " select c.rate_config_type    rate_config_type, " +
                "       IF(a.parent_id > 0, 1, 0) isrenew, " +
                "       IF(!ISNULL(c.ext_json) and c.rate_config_type in( 10,30), '1', '0') forcedconversion, " +
                "       a.no, " +
                "       date(a.rent_start_date)                                     rent_start_date, " +
                "       date(a.rent_end_date)                                       rent_end_date, " +
                "       date(kk.rent_start_date)                                    parent_day, " +
                "       date(kk.rent_end_date)                                      parent_end_date, " +
                "       a.mini_type, " +
                "       e.mobile, " +
                "       if(b.equipment_state is null or b.equipment_state = '\\N', 0 , b.equipment_state)    second_hand_type, " +
                "       if(b.equipment_type is null or b.equipment_type = '\\N', 0 , b.equipment_type )  item_equipment_type, " +
                "       if(ros.order_source_tag is not null and ros.order_source_tag = 1, 1 , 0)  zw_drainage_flag, " +
                "       e.real_name, " +
                "       e.id_card_no, " +
                "       b.short_name, " +
                "       b.color, " +
                "       b.item_model ||' '|| b.spec_title as model, " +
                "       b.size, " +
                "       nvl(ak.capital_name,'') capital_name," +
                "       bk.insurance_amt ," +
                "       a.status, " +
                // 商户订单如果真实采购价为0就取运营采购价
                "       if(a.merchant_id in (100, 10000107),b.actual_supply_price,if(nvl(b.actual_supply_price,0)=0,b.operating_purchase_price,b.actual_supply_price))" +
                "       actual_supply_price," +
                "       bd.operating_purchase_price," +
                "       if(c.rate_config_type not in( 10,30) , c.actual_financing_amt,d.avg_capital*12) rent_total, " +
                "       d.report_reduce_amt," +
                "       d.avg_capital, " +
                "       c.buyout_amt  buyout_amt, " +
                "       c.repayment_term  term, " +
                "       xx.buyout_amt  old_buyout_amt, " +
                "       if(a.merchant_id in (100, 10000107),1,0) business_type,  " +
                "       c.act_bond_amt, " +
                "       g.plan_coupon_amt, " +
                "       j.manage_write_off_amt, " +
                "       j.before_write_off_amt, " +
                "       c.activity_discount_amt total_before_write_off_amt," +
                "       if(g.plan_deduction_amount2 is null,g.plan_deduction_amount1,g.plan_deduction_amount2) " +
                "                                                                          plan_deduction_rent," +
                "       if(g.buyout_deduction_amount2 is null,g.buyout_deduction_amount1,g.buyout_deduction_amount2) " +
                "                                                                          bond_deduct_buyout," +
                "       g.return_damage_amt," +
                "       g.bond_deduct_damage_amt," +
                "       d.real_repay_capital, " +
                // 3+N租金买断金部分
                "       if(c.rate_config_type in( 10,30) and a.status=220,c.actual_buyout_amt,0)   rent_buyout_amt," +
                "       if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and k.buyoutActFlowAmt is not null,k.buyoutActFlowAmt - k.discountReturnAmt,0)              " +
                " user_actual_buyout_amt, " +
//                "       if(c.rate_config_type!=10,k.bond_deduct_buyout,0)                  bond_deduct_buyout, " +
                "       if(c.rate_config_type not in( 10,30)  and c.repayment_term != 6 and k.buyoutCouponAmount is not null and k.buyoutCouponId is not null and k.buyoutCouponId > 0 ,k.buyoutCouponAmount,0)              " +
                " manage_buyout_discount, " +
                "       if(c.rate_config_type not in( 10,30)  and c.repayment_term != 6 and k.buyoutCouponAmount is not null and k.buyoutCouponId = 0 ,k.buyoutCouponAmount,0)             " +
                " offline_buyout_discount, " +
                "       if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(k.buyoutReduceAmount) and k.buyoutReduceAmount > 0 ,k.buyoutReduceAmount,0)  buyout_reduce_amount,     " +
                "       if(c.rate_config_type not in( 10,30) and c.repayment_term != 6 and !isnull(k.buyoutRebateDiscountAmt) ,k.buyoutRebateDiscountAmt,0)  buyout_rebate_discount_amt ,     " +
                "       c.bond_refund_amount, " +
                "       d.surplus_capital                                          surplus_capital," +
                "       if(c.rate_config_type not in( 10,30) and a.status in (15, 310) and i.id is null, c.buyout_amt, 0) " +
                "                                                                  surplus_buyout_amt," +
                "       c.surplus_bond_amt                                         surplus_bond_amt," +

                "       c.premium_amt                                              premium_amt," +
                "       c.paid_premium_amt                                         paid_premium_amt," +

                "       if(h.current_overdue_days > 0, 1, 0)                       overdue_status, " +
                "       if(h.current_overdue_days > 0, d.overdue_fine, 0)          overdue_fine, " +
                "       if(i.id is not null, i.no, '' )                            renew_order_no, " +
                "       if(i.id is not null, c.act_bond_amt, 0)                    renew_bond_amt, " +
                "       if(i.id is not null, c.buyout_amt, 0)                      renew_capital, " +
                "       if(i.id is not null, i.actual_financing_amt, 0)            renew_total_rent, " +
                "       if(i.id is not null, i.repayment_term, 0)                  renew_term, " +
                "       if(i.id is not null,i.real_repay_capital,0)                act_renew_rent_amt, " +
                "       if(i.id is not null,i.real_overdue_fine,0)                 act_renew_overdue_fine, " +
                "       if(i.plan_deduction_amount2 is null,i.plan_deduction_amount1,i.plan_deduction_amount2)       " +
                "                                                                  plan_deduction_amount," +
                "       if(i.id is not null,i.capital,0)                           renew_surplus_rent_amt, " +
                "       if(i.id is not null and i.current_overdue_days>0,1,0)      renew_overdue_status, " +
                "       if(i.id is not null and i.current_overdue_days>0,i.overdue_fine,0)      renew_overdue_fine," +
                "       c.renew_term renew_term_6n , c.month_repayment month_repayment_6n ";


        String body = " from rent_order a " +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1 and b.type_class !=1 inner join rent_order_source ros on ros.order_id = a.id and ros.ds = " + ds + " " +
                "LEFT JOIN (\n" +
                "\n" +
                "select\n" +
                "id,\n" +
                "if(first_category_name LIKE '%二手专区%','二手手机','') second_hand_type\n" +
                "from (select rent_item.id\n" +
                "\t         ,concat_ws(',',collect_set(rent_category2.name) ) first_category_name\n" +
                "\t   from(select rent_item.id\n" +
                "                  ,first_category\n" +
                "          from rent_item\n" +
                "          lateral view explode(split(first_category_ids,',')) tmp1 AS first_category\n" +
                "          where ds = '20230810') rent_item\n" +
                "\t\t  left join rent_category \trent_category2\n" +
                "\t\t      ON rent_item.first_category = rent_category2.id AND rent_category2.ds = '20230810'\n" +
                "\t\tgroup by rent_item.id\n" +
                ") \n" +
                ") t\n" +
                "on t.id = b.item_id" +
                "          left join ( select order_id,sum(operating_purchase_price) operating_purchase_price from rent_order_item bd  " +
                "           where bd.item_type = 10 and is_deleted =0 and bd.ds = " + ds + " group by order_id) bd on a.id = bd" +
                ".order_id\n" +
                "         inner join rent_order_finance_detail c on a.id = c.order_id " +
                "         inner join (select rp.order_id, " +
                "                            avg(capital)                                     avg_capital, " +
                "                            sum(IF(repay_status = 5, real_repay_capital, 0)) " +
                "                                   real_repay_capital, " +
                "                            sum(IF(repay_status =1, capital, 0))  surplus_capital, " +
                "                            sum(IF(repay_status =1 , rp.overdue_fine, 0))      overdue_fine, " +
                "                            sum(report_reduce_amt)      report_reduce_amt " +
                "                     from rent_order_repayment_plan rp" +
                "                     inner join rent_order_finance_detail rf on rp.order_id=rf.order_id" +
                "                     where rp.is_deleted = 0 " +
                "                       and rp.ds = " + ds +
                "                       and rf.ds = " + ds +
                "                     group by rp.order_id) d on a.id = d.order_id " +
                "         left join rent_order_finance_detail xx on a.parent_id = xx.order_id and xx.ds = " + ds +
                "         left join rent_customer e on a.customer_id = e.id and e.ds = " + ds +
                "         left join rent_order_finance_detail f on a.parent_id = f.order_id and f.is_deleted = 0 and " +
                "                   f.ds = " + ds +
                "         left join (select " +
                "                   sum(if(biz_type in (3,34) ,get_json_object(ext, '$.discountReturnAmt'), null)) " +
                "                     discount_return_amt," +
                "                   sum(if(rate_config_type <>30 and biz_type in (3,34), get_json_object(ext, '$planCouponAmount'),null)) " +
                "                     plan_coupon_amt, " +
                "                   sum(if(biz_type in (3,34),get_json_object(ext,'$.planDeductionAmount'),null)) " +
                "                     plan_deduction_amount1," +
                "                   sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=1,flow_amt,null)) " +
                "                     plan_deduction_amount2," +
                "                   sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=2,flow_amt,null)) " +
                "                     bond_deduct_damage_amt," +
                "                   sum(if(biz_type in (3,34),get_json_object(ext," +
                "                           '$.buyoutDeductionAmount'),null))  buyout_deduction_amount1," +
                "                   sum(if(biz_type=23 and get_json_object(ext," +
                "                           '$.deductionType')=3,flow_amt,null))  buyout_deduction_amount2," +
                "                   sum(if(biz_type=2,flow_amt,0))  return_damage_amt," +
                "               rof.order_id " +
                "               from rent_order_flow  rof" +
                "                inner join rent_order_finance_detail  rofd on rof.order_id=rofd.order_id and rofd.is_deleted = 0" +
                "                   and rofd.ds = " + ds +
                "                                   where rof.biz_type in (3,23,34) " +
                "                                   and rof.pay_status = 10 " +
                "                                   and rof.flow_type =1 " +
                "                                   and (rof.mark_refund is null or mark_refund = 0) " +
                "                                   and rof.refunded_amt = 0 " +
                "                                   and rof.ext is not null " +
                "                                   and rof.ext != '' " +
                "                                   and rof.is_deleted = 0 " +
                "                                   and rof.ds = " + ds +
                "                          group by rof.order_id) g on a.id = g.order_id" +
                "         inner join rent_order_overdue_stat h on a.id = h.order_id " +
                "         left join (select sum(if(biz_type in (3),get_json_object(ext,'$.buyoutActAmt'),0)) buyoutActFlowAmt,\n" +
                "                       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutCouponAmount') is not null,get_json_object(ext,'$.buyoutCouponAmount'),0)) buyoutCouponAmount,\n" +
                "                       sum(if(biz_type in (3) and get_json_object(ext,'$.discountReturnAmt') is not null,get_json_object(ext,'$.discountReturnAmt'),0)) discountReturnAmt,\n" +
                "                       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutReduceAmount') is not null,get_json_object(ext,'$.buyoutReduceAmount'),0)) buyoutReduceAmount,\n" +
                "                       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutCouponId') is not null ,get_json_object(ext,'$.buyoutCouponId'),0)) buyoutCouponId,\n" +
                "                       sum(if(biz_type in (3) and get_json_object(ext,'$.buyoutRebateDiscountAmt') is not null,get_json_object(ext,'$.buyoutRebateDiscountAmt'),0)) buyoutRebateDiscountAmt,\n" +
                "                       order_id\n" +
                "                       from rent_order_flow\n" +
                "                       where biz_type = 3\n" +
                "                         and pay_status = 10\n" +
                "                         and flow_type = 1\n" +
                "                         and is_deleted = 0\n" +
                "                         and get_json_object(ext,'$.buyoutActAmt') >= 0\n" +
                "                         and (mark_refund is null or mark_refund = 0)\n" +
                "                         and ext is not null  and ext != '' and ds = " + ds + "\n" +
                "                         GROUP BY order_id ) k on a.id = k.order_id " +
                "         left join (select x.id, x.parent_id, y.actual_financing_amt,y.repayment_term,x.no, " +
                "                           z.real_repay_capital,z.capital,z.real_overdue_fine,z.overdue_fine, " +
                "                           m.current_overdue_days,n.plan_deduction_amount1,n.plan_deduction_amount2," +
                "                           y.act_bond_amt " +
                "                    from rent_order x " +
                "                             inner join rent_order_finance_detail y on x.id = y.order_id " +
                "                             inner join (select order_id, " +
                "                                                sum(IF(repay_status = 5, " +
                "                                           real_repay_capital, 0)) real_repay_capital, " +
                "                                                sum(IF(repay_status =1, capital, 0))  capital, " +
                "                                                sum(overdue_fine)                overdue_fine, " +
                "                                                sum(real_overdue_fine)           real_overdue_fine " +
                "                                         from rent_order_repayment_plan " +
                "                                         where is_deleted = 0 and ds = " + ds +
                "                                         group by order_id) z on x.id = z.order_id " +
                "                             inner join rent_order_overdue_stat m on x.id = m.order_id " +
                "       left join (select sum(if(biz_type=3,get_json_object(ext,'$.planDeductionAmount'),0)) " +
                "                     plan_deduction_amount1," +
                "                       sum(if(biz_type=23 and get_json_object(ext,'$.deductionType')=1,flow_amt,0))" +
                "                     plan_deduction_amount2, " +
                "               order_id from rent_order_flow  " +
                "                                   where biz_type in (3,23) " +
                "                                   and pay_status = 10 " +
                "                                   and flow_type =1 " +
                "                                   and (mark_refund is null or mark_refund = 0) " +
                "                                   and refunded_amt = 0 " +
                "                                   and ext is not null " +
                "                                   and ext != '' " +
                "                                   and is_deleted = 0 " +
                "                                   and ds = " + ds +
                "                          group by order_id) n on x.id = n.order_id" +
                "                    where x.parent_id > 0 " +
                "                      and x.is_deleted = 0 " +
                "                      and y.is_deleted = 0 " +
                "                      and m.is_deleted = 0 " +
                "                      and x.ds = " + ds +
                "                      and y.ds = " + ds +
                "                      and m.ds = " + ds + ") i on a.id = i.parent_id " +
                "         left join (select order_id, " +
                "                            sum(manage_write_off_amt)  manage_write_off_amt, " +
                "                            sum(before_write_off_amt)  before_write_off_amt, " +
                "                            min(total_before_write_off_amt) total_before_write_off_amt" +
                "                     from (select x.order_id, " +
                "                                  if(ISNULL(min(x.bind_order_time)) or " +
                "                                         min(y.payment_time) < min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1," +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1," +
                "                                            x.write_off_amt, 0)), 0), 0) manage_write_off_amt," +
                "                                  if(!ISNULL(min(x.bind_order_time)) and " +
                "                                         min(y.payment_time) >= min(x.bind_order_time), " +
                "                                         if(x.type in (1, 3), if(min(x.use_status) = 1, " +
                "                                            max(x.write_off_amt), 0), 0) + " +
                "                                         if(x.type in (2, 4), sum(if(x.use_status = 1, " +
                "                                            x.write_off_amt, 0)), 0), 0) before_write_off_amt," +
                "                                  min(z.total_before_write_off_amt)  total_before_write_off_amt " +
                "                           from rent_customer_coupon x " +
                "                                    inner join rent_order y on x.order_id = y.id " +
                "                                    inner join (select x.order_id,sum(write_off_amt) total_before_write_off_amt\n" +
                "                                        from (select order_id\n" +
                "                                                   , nvl(x.write_off_amt, 0)                                                                                               write_off_amt\n" +
                "                                                   , if(x.type in (2, 4), 1, row_number() over( partition by x.order_id,x.use_term order by nvl(x.write_off_amt,0) desc )) rowid\n" +
                "                                              from rent_customer_coupon x\n" +
                "                                                       inner join rent_order y on x.order_id = y.id\n" +
                "                                              where x.order_id > 0\n" +
                "                                                and x.scene = 1\n" +
                "                                                and x.is_deleted = 0\n" +
                "                                                and x.ds = " + ds +
                "                                                and x.bind_order_time is not null\n" +
                "                                                and y.payment_time >= x.bind_order_time\n" +
                "                                                and y.ds = " + ds +
                "                                                and y.is_deleted = 0) x\n" +
                "                                        where x.rowid=1\n" +
                "                                        group by x.order_id) z on z.order_id= y.id" +
                "                           where x.order_id > 0 and x.scene = 1 " +
                "                             and x.is_deleted = 0 " +
                "                             and y.is_deleted = 0 " +
                "                             and x.ds = " + ds +
                "                             and y.ds = " + ds +
                "                           group by x.order_id, x.type) " +
                "                     group by order_id) j on a.id = j.order_id " +
                "             left join ( select id,rent_start_date,rent_end_date " +
                "                           from rent_order " +
                "                           where ds = " + ds + " ) kk on kk.id = a.parent_id " +
                "             left join (select order_id , ltrim(rtrim(wm_concat(',', if(finance_flag = 10, capital_name, '')),','),',') capital_name" +
                "                           from rent_order_capital_relation" +
                "                           where ds = " + ds + "" +
                "                           and is_deleted = 0" +
                "                           and order_amt_time is not null" +
                "                           group by order_id) ak on a.id = ak.order_id" +
                "             left join (select order_id, sum(flow_amt) insurance_amt\n" +
                "                from rent_order_flow\n" +
                "                where biz_type = 14\n" +
                "                  and pay_status = 10\n" +
                "                  and flow_type = 1\n" +
                "                  and is_deleted = 0\n" +
                "                  and mark_refund = 0\n" +
                "                  and ds =  " + ds + "" +
                "                group by order_id) bk on a.id = bk.order_id" +
                " where  a.payment_time is not null " +
                "  and a.termination != 5 " +
                "  and a.biz_type = 2 " +
                "  and a.type = 1 " +
                "  and a.customer_id not in " +
                "      (" + testerIds + ")" +
                "  and a.mini_type not in (" + FinanceReportBusiness.EXCEPT_MINI_TYPE + ")" +
                "  and a.status in (" + FinanceReportBusiness.EFFECTIVE_ORDER_STATUS + ") " +
                "  and c.rate_config_type not in (" + FinanceReportBusiness.EXCEPT_FINANCE_TYPE + ") " +
                "  and a.is_deleted = 0 " +
                "  and b.is_deleted = 0 " +
                "  and c.is_deleted = 0 " +
                "  and h.is_deleted = 0 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and e.ds = " + ds +
                "  and h.ds = " + ds +
                "  and ros.ds = " + ds;


        try {
            String countSql = prefix1 + body + ";";
            List<Record> countRecords = odpsUtil.querySql(countSql);
            Integer count = Integer.valueOf(countRecords.get(0).getString("count"));
            // 计算页数
            int pageNO = count / PAGE_SIZE;
            if (count % PAGE_SIZE > 0) {
                pageNO++;
            }
            for (int i = 0; i < pageNO; i++) {
                String suffix = " order by a.id asc limit " + i * PAGE_SIZE + "," + PAGE_SIZE;
                String detailSql = prefix2 + body + suffix + ";";
                FishOrderBusiness.threadPoolExecutor.execute(() -> queryAndSaveDetail(detailSql));
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            dingDingAlarmUtil.dataViewProExceptionAlarm("countOrderDetail异常", e);
        }
    }


    private void queryAndSaveDetail(String detailSql) {
        List<Record> records = odpsUtil.querySql(detailSql);
        List<FinanceOrderDetailDO> resList = new ArrayList<>(records.size());
        Map<String, AppBiRentOrderLabelDO> orderDetailMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(records)) {
            Set<String> orderNoList = records.stream().map(record -> record.getString("no")).collect(Collectors.toSet());
            orderDetailMap = appBiRentOrderLabelService.getMapByOrderNos(orderNoList);
        }
        Map<String, AppBiRentOrderLabelDO> finalOrderDetailMap = orderDetailMap;
        records.forEach(k -> {
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(k.getString("rent_start_date")),
                    LocalTime.MIN);
            LocalDateTime rentEndDate = LocalDateTime.of(LocalDate.parse(k.getString("rent_end_date")),
                    LocalTime.MIN);
            LocalDateTime parentDay = stringTOLocalDate(k.getString("parent_day"));
            LocalDateTime parentEndDate = stringTOLocalDate(k.getString("parent_end_date"));
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            Integer rateConfigType = Integer.valueOf(k.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer isRenew = Integer.valueOf(k.getString("isrenew"));
            String secondHandType = k.getString("second_hand_type");
            Integer itemEquipmentType = Integer.valueOf(k.getString("item_equipment_type"));
            Integer zwFlag = Integer.valueOf(k.getString("zw_drainage_flag"));
            String orderNo = k.getString("no");
            String mobile = k.getString("mobile");
            String realName = k.getString("real_name");
            String idCardNo = k.getString("id_card_no");
            String shortName = k.getString("short_name");
            String color = k.getString("color");
            String model = k.getString("model");
            String size = k.getString("size");
            Integer status = Integer.valueOf(k.getString("status"));
            Integer term = Integer.valueOf(k.getString("term"));
            Integer businessType = Integer.valueOf(k.getString("business_type"));
            BigDecimal actSupplyPrice = stringToDecimal(k.getString("actual_supply_price"));
            // 协议总租金
            BigDecimal rentTotal = stringToDecimal(k.getString("rent_total"));
            // BigDecimal reportReduceAmt = stringToDecimal(k.getString("report_reduce_amt"));
            BigDecimal rentBuyoutAmt = stringToDecimal(k.getString("rent_buyout_amt"));
            BigDecimal monthCapital = stringToDecimal(k.getString("avg_capital"));
            BigDecimal buyoutAmt = stringToDecimal(k.getString("buyout_amt"));
            BigDecimal actBondAmt = stringToDecimal(k.getString("act_bond_amt"));
            BigDecimal realRepayCapital = stringToDecimal(k.getString("real_repay_capital"));
            BigDecimal operatingPurchasePrice = stringToDecimal(k.getString("operating_purchase_price"));
            // realRepayCapital = realRepayCapital.add(reportReduceAmt);
            BigDecimal planCouponAmt = stringToDecimal(k.getString("plan_coupon_amt"));
            BigDecimal manageWriteOffAmt = stringToDecimal(k.getString("manage_write_off_amt"));
            BigDecimal beforeWriteOffAmt = stringToDecimal(k.getString("before_write_off_amt"));
            BigDecimal totalBeforeWriteOffAmt = stringToDecimal(k.getString("total_before_write_off_amt"));
            // 主订单保证金抵扣租金
            BigDecimal planDeductionRent = stringToDecimal(k.getString("plan_deduction_rent"));
            BigDecimal bondRefundAmount = stringToDecimal(k.getString("bond_refund_amount"));
            BigDecimal surplusCapital = stringToDecimal(k.getString("surplus_capital"));
            BigDecimal surplusBuyoutAmt = stringToDecimal(k.getString("surplus_buyout_amt"));
            BigDecimal surplusBondAmt = stringToDecimal(k.getString("surplus_bond_amt"));

            // 溢价金
            BigDecimal premiumAmt = stringToDecimal(k.getString("premium_amt"));
            BigDecimal paidPremiumAmt = stringToDecimal(k.getString("paid_premium_amt"));

            Integer overdueStatus = Integer.valueOf(k.getString("overdue_status"));
            BigDecimal overdueFine = stringToDecimal(k.getString("overdue_fine"));
            String renewOrderNo = k.getString("renew_order_no");
            String capitalName = k.getString("capital_name");
            BigDecimal bondDeductDamageAmt = stringToDecimal(k.getString("bond_deduct_damage_amt"));
            BigDecimal insuranceAmt = stringToDecimal(k.getString("insurance_amt"));
            // 续租实收保证金
            BigDecimal renewBondAmt = stringToDecimal(k.getString("renew_bond_amt"));
            BigDecimal oldBuyoutAmt = stringToDecimal(k.getString("old_buyout_amt"));
            BigDecimal renewCapital = stringToDecimal(k.getString("renew_capital"));
            BigDecimal renewTotalRent = stringToDecimal(k.getString("renew_total_rent"));
            Integer renewTerm = Integer.valueOf(k.getString("renew_term"));
            BigDecimal actRenewRentAmt = stringToDecimal(k.getString("act_renew_rent_amt"));
            BigDecimal actRenewOverdueFine = stringToDecimal(k.getString("act_renew_overdue_fine"));
            BigDecimal planDeductionAmount = stringToDecimal(k.getString("plan_deduction_amount"));
            BigDecimal renewSurplusRentAmt = stringToDecimal(k.getString("renew_surplus_rent_amt"));
            Integer renewOverdueStatus = Integer.valueOf(k.getString("renew_overdue_status"));
            BigDecimal renewOverdueFine = stringToDecimal(k.getString("renew_overdue_fine"));

            // 用户实际支付买断金
            BigDecimal userActualBuyoutAmt = stringToDecimal(k.getString("user_actual_buyout_amt"));
            // 保证金抵扣买断金（3+x保证金抵扣租金）
            BigDecimal bondDeductBuyout = stringToDecimal(k.getString("bond_deduct_buyout"));
            // 运营减免买断金
            BigDecimal manageBuyoutDiscount = stringToDecimal(k.getString("manage_buyout_discount"));
            // 售后减免买断金
            BigDecimal offlineBuyoutDiscount = stringToDecimal(k.getString("offline_buyout_discount"));
            BigDecimal returnDamageAmt = stringToDecimal(k.getString("return_damage_amt"));


            // 续租期数
            BigDecimal renewTerm6n = stringToDecimal(k.getString("renew_term_6n"));
            // 月租金
            BigDecimal monthRepayment6n = stringToDecimal(k.getString("month_repayment_6n"));
            // 买断金售后减免
            BigDecimal buyoutReduceAmount = stringToDecimal(k.getString("buyout_reduce_amount"));
            BigDecimal buyoutRebateDiscountAmt = stringToDecimal(k.getString("buyout_rebate_discount_amt"));


            // Integer financeType = FinanceTypeEnum.getFinanceType(rateConfigType, isRenew, forcedConversion);
            Integer financeType = rateConfigType;
            FinanceOrderDetailDO detailDO = new FinanceOrderDetailDO();
            detailDO.setRentStartDate(rentStartDate);
            detailDO.setRentEndDate(rentEndDate);
            detailDO.setParentDay(parentDay);
            detailDO.setParentEndDate(parentEndDate);
            detailDO.setMiniType(miniType);
            detailDO.setFinanceType(rateConfigType);
            detailDO.setSecondHandType(secondHandType);
            detailDO.setOrderNo(orderNo);
            detailDO.setCustomerName(realName);
            detailDO.setCustomerIdCardNo(idCardNo);
            detailDO.setCustomerMobile(mobile);
            detailDO.setOrderStatus(status);
            detailDO.setCapitalName(capitalName);
            detailDO.setTerm(term);
            detailDO.setShortName(shortName);
            detailDO.setColor(color);
            detailDO.setModel(model);
            detailDO.setSize(size);
            detailDO.setScreenRiskCapital(insuranceAmt);
            detailDO.setActSupplyPrice(actSupplyPrice);
            detailDO.setTotalRent(rentTotal);
            detailDO.setMonthRent(monthCapital);
            detailDO.setBondDeductRent(planDeductionRent);
            detailDO.setBeforeDiscount(beforeWriteOffAmt);
            detailDO.setBusinessType(businessType);
            detailDO.setTotalBeforeWriteOffAmt(totalBeforeWriteOffAmt);
            detailDO.setAfterDiscount(planCouponAmt);
            detailDO.setOperatingPurchasePrice(operatingPurchasePrice);
            detailDO.setManageDiscount(manageWriteOffAmt);
            detailDO.setEndReturnDamageAmt(returnDamageAmt);
            if (actBondAmt.compareTo(BigDecimal.ZERO) > 0) {
                detailDO.setBondAmt(actBondAmt);
            } else {
                detailDO.setBondAmt(renewBondAmt);
            }
            if ((status.equals(200) || status.equals(210)) && !rateConfigType.equals(10)) {
                if (isRenew == 0) {
                    detailDO.setBuyoutReturnCapitalAmt(buyoutAmt);
                }
                detailDO.setRentReturnDiscount(CalculateUtil.sub(detailDO.getTotalRent(), realRepayCapital));
            }
            detailDO.setSurplusBondAmt(surplusBondAmt);
            detailDO.setSurplusRentAmt(surplusCapital);

            // 溢价金
            detailDO.setPremiumAmt(premiumAmt);
            detailDO.setPaidPremiumAmt(paidPremiumAmt);

            detailDO.setBondDeductDamageAmt(bondDeductDamageAmt);
            if (financeType.equals(FinanceTypeEnum.TEN.getType()) ||
                    financeType.equals(FinanceTypeEnum.TWELVE_OO_ONE.getType())) {
                detailDO.setActBuyoutAmt(BigDecimal.ZERO);
                detailDO.setBuyoutAmt(BigDecimal.ZERO);
                if (status.equals(OrderStatusEnum.ORDER_STATUS_BUYOUT.getStatus())) {
                    detailDO.setEndDiscountAmt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                } else if (status.equals(200) || status.equals(210)) {
                    detailDO.setEndReturnNotRepayAmt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                    detailDO.setSurplusRentAmt(BigDecimal.ZERO);
                }
                detailDO.setBondDeductRent(CalculateUtil.add(detailDO.getBondDeductRent(), bondDeductBuyout));
            } else if (financeType.equals(FinanceTypeEnum.THIRTY.getType())) {
                detailDO.setActBuyoutAmt(BigDecimal.ZERO);
                detailDO.setBuyoutAmt(BigDecimal.ZERO);
                if (status.equals(OrderStatusEnum.ORDER_STATUS_BUYOUT.getStatus())) {
                    detailDO.setEndDiscountFor6Amt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                } else if (status.equals(200) || status.equals(210)) {
                    detailDO.setEndReturnNotRepayFor6Amt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                    detailDO.setSurplusRentAmt(BigDecimal.ZERO);
                }
                detailDO.setBondDeductRent(CalculateUtil.add(detailDO.getBondDeductRent(), bondDeductBuyout));
            } else {
                detailDO.setActBuyoutAmt(userActualBuyoutAmt);
                detailDO.setBuyoutAmt(buyoutAmt);
                detailDO.setBondDeductBuyoutAmt(bondDeductBuyout);
                detailDO.setBuyoutReduceAfterCount(offlineBuyoutDiscount.add(buyoutReduceAmount).add(buyoutRebateDiscountAmt));
                detailDO.setBuyoutReduceMarketingAmt(manageBuyoutDiscount);
            }
            detailDO.setActRentAmt(CalculateUtil.sub(CalculateUtil.add(realRepayCapital, rentBuyoutAmt),
                    CalculateUtil.add(CalculateUtil.add(detailDO.getBondDeductRent(), detailDO.getBeforeDiscount()),
                            CalculateUtil.add(detailDO.getManageDiscount(), detailDO.getAfterDiscount()))));
            detailDO.setBondRefundAmt(bondRefundAmount);
            detailDO.setSurplusBuyoutAmt(surplusBuyoutAmt);
            detailDO.setOverdueStatus(overdueStatus);
            detailDO.setOverdueFine(overdueFine);
            detailDO.setRenewOrderNo(renewOrderNo);
            detailDO.setRenewBondAmt(renewBondAmt);
            detailDO.setRenewCapital(renewCapital);
            detailDO.setOldBuyoutAmt(oldBuyoutAmt);
            detailDO.setRenewTotalRent(renewTotalRent);
            detailDO.setRenewTerm(renewTerm);
            detailDO.setActRenewRentAmt(actRenewRentAmt);
            detailDO.setActRenewOverdueFine(actRenewOverdueFine);
            detailDO.setRenewBondDeductRent(planDeductionAmount);
            detailDO.setRenewSurplusRentAmt(renewSurplusRentAmt);
            detailDO.setRenewOverdueStatus(renewOverdueStatus);
            detailDO.setRenewOverdueFine(renewOverdueFine);
            detailDO.setActTotalAmt(CalculateUtil.add(detailDO.getActRentAmt(), detailDO.getActBuyoutAmt()));

            detailDO.setItemEquipmentType(itemEquipmentType);
            detailDO.setZwDrainageFlag(zwFlag);
            if (finalOrderDetailMap.containsKey(orderNo)) {
                AppBiRentOrderLabelDO appBiRentOrderLabelDO = finalOrderDetailMap.get(orderNo);
                detailDO.setOrderAttributionCode(appBiRentOrderLabelDO.getSecondOrderBelonging());
                detailDO.setQuotientName(appBiRentOrderLabelDO.getQuotientName());
            }

            if (term == 6) {
                BigDecimal maxTotalRent = (new BigDecimal(term)
                        .add(renewTerm6n))
                        .multiply(monthRepayment6n);
                detailDO.setTotalRent(maxTotalRent);
                detailDO.setActBuyoutAmt(BigDecimal.ZERO);
                detailDO.setBuyoutAmt(BigDecimal.ZERO);


                if (status.equals(OrderStatusEnum.ORDER_STATUS_BUYOUT.getStatus())) {
                    detailDO.setEndDiscountFor6Amt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                } else if (status.equals(200) || status.equals(210)) {
                    detailDO.setEndReturnNotRepayFor6Amt(CalculateUtil.sub(detailDO.getTotalRent(),
                            CalculateUtil.add(realRepayCapital, rentBuyoutAmt)));
                    detailDO.setSurplusRentAmt(BigDecimal.ZERO);
                }
                detailDO.setBondDeductRent(CalculateUtil.add(detailDO.getBondDeductRent(), bondDeductBuyout));
            }
            resList.add(detailDO);

        });
        financeOrderDetailService.saveBatch(resList, 2000);
    }


    private BigDecimal stringToDecimal(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return new

                BigDecimal(val).

                setScale(2, RoundingMode.HALF_UP);
    }

    private LocalDateTime stringTOLocalDate(String val) {
        if (val.equals("\\N")) {
            return null;
        }
        return LocalDateTime.of(LocalDate.parse(val), LocalTime.MIN);
    }

}