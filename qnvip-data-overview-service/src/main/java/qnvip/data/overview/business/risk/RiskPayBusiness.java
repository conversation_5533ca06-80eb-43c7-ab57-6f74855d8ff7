package qnvip.data.overview.business.risk;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.risk.RiskMerchantSituationDO;
import qnvip.data.overview.domain.risk.RiskRentSituationDO;
import qnvip.data.overview.domain.risk.RiskSituationDO;
import qnvip.data.overview.service.risk.RiskMerchantSituationService;
import qnvip.data.overview.service.risk.RiskRentSituationService;
import qnvip.data.overview.service.risk.RiskSituationService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 风控大盘-更新支付信息
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @date 2023/08/24
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskPayBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    private final OdpsUtil odpsUtil;

    private final RiskSituationService riskSituationService;
    private final RiskMerchantSituationService merchantSituationService;
    private final RiskRentSituationService rentSituationService;


    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        List<Map<String, Object>> list = Lists.newArrayList();
        // 计算全部平台
        getBigDataPassTotal(list, ds);
        getInstallmentPayCount(list, ds);

        List<CompletableFuture<List<Map<String, String>>>> futureList =
                list.stream().map(this::getData).collect(Collectors.toList());
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
        List<Map<String, String>> totalList = Lists.newArrayList();
        futureList.forEach(future -> {
            try {
                totalList.addAll(future.get());
            } catch (Exception e) {
                log.error("RiskSituationBusiness runCore error,{}", e.getMessage());
            }
        });

        Map<String, List<Map<String, String>>> channel2list =
                totalList.stream().collect(Collectors.groupingBy(map -> map.get("time")));
        channel2list.forEach((time, timeList) -> {
            RiskSituationDO riskSituationDO = new RiskSituationDO();
            RiskMerchantSituationDO merchantSituationDO = new RiskMerchantSituationDO();
            RiskRentSituationDO rentSituationDO = new RiskRentSituationDO();
            Map<String, String> merged = new HashMap<>();
            timeList.forEach(merged::putAll);
            String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
            String payUserMerchant = Optional.ofNullable(merged.get("pay_user_merchant")).orElse("0");
            String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");
            String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
            String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
            String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
            String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
            String installmentPayCount = Optional.ofNullable(merged.get("installment_pay_count")).orElse("0");
            String installmentPay = Optional.ofNullable(merged.get("installment_pay")).orElse("0");
            String installmentPayCountWhole = Optional.ofNullable(merged.get("installment_pay_count_whole")).orElse("0");
            String channel = Optional.ofNullable(merged.get("business_channel")).orElse("-100");
            String installmentPayClose = Optional.ofNullable(merged.get("installment_pay_close")).orElse("0");
            String installmentPayCreditAuditClose = Optional.ofNullable(merged.get("installment_pay_credit_audit_close")).orElse("0");

            String installmentOtherPay = Optional.ofNullable(merged.get("installment_other_pay")).orElse("0");
            String installmentAliPay = Optional.ofNullable(merged.get("installment_ali_pay")).orElse("0");
            String InstallmentWeixinPay = Optional.ofNullable(merged.get("installment_weixin_pay")).orElse("0");
            String paySelfCount = Optional.ofNullable(merged.get("pay_self_count")).orElse("0");
            String payUserSelfClose = Optional.ofNullable(merged.get("pay_user_self_close")).orElse("0");

            LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(merged.get("time"), dateFormatter), LocalTime.MIN);
            riskSituationDO.setCountDay(countDay);
            riskSituationDO.setBusinessChannel(Integer.valueOf(channel));
            riskSituationDO.setPayUser(NumberUtil.add(payUser, payUserMerchant).longValue());
            riskSituationDO.setPayUserClose(NumberUtil.add(payUserClose, installmentPayClose).longValue());
            riskSituationDO.setPayCreditAuditClose(NumberUtil.add(payCreditAuditClose, installmentPayCreditAuditClose).longValue());
            riskSituationDO.setAliPay(NumberUtil.add(aliPay, installmentAliPay).longValue());
            riskSituationDO.setWeiXinPay(NumberUtil.add(weixinPay, InstallmentWeixinPay).longValue());
            riskSituationDO.setOtherPay(NumberUtil.add(otherPay, installmentOtherPay).longValue());
            riskSituationDO.setInstallmentPayCount(stringToDecimal(installmentPay).longValue());

            merchantSituationDO.setCountDay(countDay);
            merchantSituationDO.setBusinessChannel(Integer.valueOf(channel));
            merchantSituationDO.setPayUser(Long.valueOf(installmentPay));
            merchantSituationDO.setPayCount(Long.valueOf(installmentPayCount));
            merchantSituationDO.setPayUserWhole(Long.valueOf(installmentPayCountWhole));
            merchantSituationDO.setPayUserClose(Long.valueOf(installmentPayClose));
            merchantSituationDO.setAliPay(Long.valueOf(installmentAliPay));
            merchantSituationDO.setWeiXinPay(Long.valueOf(InstallmentWeixinPay));
            merchantSituationDO.setOtherPay(Long.valueOf(installmentOtherPay));
            merchantSituationDO.setPayCreditAuditClose(Long.valueOf(installmentPayCreditAuditClose));

            rentSituationDO.setCountDay(countDay);
            rentSituationDO.setBusinessChannel(Integer.valueOf(channel));
            rentSituationDO.setPayUser(Long.valueOf(payUser));
            rentSituationDO.setPayUserClose(Long.valueOf(payUserClose));
            rentSituationDO.setAliPay(Long.valueOf(aliPay));
            rentSituationDO.setWeiXinPay(Long.valueOf(weixinPay));
            rentSituationDO.setOtherPay(Long.valueOf(otherPay));
            rentSituationDO.setPayUserMerchant(Long.valueOf(payUserMerchant));
            rentSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
            rentSituationDO.setPaySelfCount(Long.valueOf(paySelfCount));
            rentSituationDO.setPayUserSelfClose(Long.valueOf(payUserSelfClose));
            riskSituationService.updatePayCount(riskSituationDO);
            merchantSituationService.updatePayCount(merchantSituationDO);
            rentSituationService.updatePayCount(rentSituationDO);
        });
    }

    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4, RoundingMode.HALF_UP);
    }

    /**
     * 执行人行风控、反欺诈通过、评分通过数、成功支付数
     *
     * @param list
     */
    private void getBigDataPassTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT date(payment_time) time,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and\n" +
                "                                   merchant_id in (100,10000107) and roi.common_rent_flag <> 10 THEN" +
                " customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and  merchant_id not in( 100,10000107)  THEN " +
                " customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_merchant,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination=5 and roi.common_rent_flag <> 10 THEN customer_id\n" +
                "                              ELSE NULL END ))                                                      " +
                "            AS pay_user_close,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN  merchant_id in( 100,10000107) and a.termination=5 THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_user_self_close,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN \n" +
                "                                   merchant_id in( 100,10000107) THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_self_count,\n" +
                "          (count(if(roi.risk_auth_status = 5 and a.termination=5 and roi.common_rent_flag <> 10, customer_id, null)) +\n" +
                "           count(if(roi.risk_auth_tag like '%未接通%' and a.termination=5 and roi.common_rent_flag <> 10, customer_id,\n" +
                "                    null)))                                                                                    as pay_credit_audit_close,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'WECHAT' and merchant_id in (100,10000107)  and roi.common_rent_flag <> 10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                       " +
                "          +\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'WECHAT' and merchant_id   not in (100,10000107) " +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as weixin_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'ALIPAY' and merchant_id in (100,10000107)  and roi.common_rent_flag <> 10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                       " +
                "          +\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'ALIPAY' and merchant_id" +
                " not in (100,10000107) " +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as ali_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code not in ('ALIPAY', 'WECHAT') and merchant_id in (100,10000107)  and roi.common_rent_flag <> 10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                       " +
                "          +\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code not in ('ALIPAY', 'WECHAT') and merchant_id not in (100,10000107) " +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as other_pay\n" +
                "   FROM rent_order a\n" +
                "            inner join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "            inner JOIN rent_order_infomore roi on roi.order_id = a.id " +
                " and roi.ds = " + ds +
                "   where a.parent_id =0\n" +
                "     AND a.ds = " + ds +
                "     and a.is_deleted=0\n" +
                "   and a.type =1\n" +
                "     AND date(payment_time) between date_sub(getdate(), 7) and date(getdate())\n" +
                "   GROUP BY date(payment_time);";
        list.add(packageParam(sql, "pay_user", "pay_user_merchant", "pay_user_close", "weixin_pay", "ali_pay",
                "other_pay", "time", "pay_credit_audit_close","pay_user_self_close","pay_self_count"));
    }


    /**
     * 分期购支付人数
     *
     * @param list
     */
    private void getInstallmentPayCount(List<Map<String, Object>> list, String ds) {

        String sql = "select" +
                "         date(cash_deposit_pay_time) time," +
                "   count(DISTINCT if(so.closing_time is null, customer_id, null))                                           AS                             installment_pay," +
                "   count(if(so.closing_time is null, so.order_uid, null))                                                   AS                             installment_pay_count," +
                "   count(DISTINCT if(sof.repayment_period_count = 1 and so.closing_time is null, customer_id, null)) AS      installment_pay_count_whole," +
                "   count(DISTINCT (if(smf.platform_code = 'ALIPAY'  and so.closing_time is null, customer_id," +
                "                      null)))                                                                           installment_ali_pay," +
                "   count(DISTINCT (if(smf.platform_code = 'WECHAT' and so.closing_time is null, customer_id," +
                "                      null)))                                                                           installment_weixin_pay," +
                "   count(DISTINCT" +
                "         (if(smf.platform_code not in ('ALIPAY', 'WECHAT') and so.closing_time is null, customer_id, null))) installment_other_pay," +
                "   count(DISTINCT" +
                "         (if(so.closing_time is not null, customer_id, null)))                                          installment_pay_close," +
                "                  count(DISTINCT ( " +
                "                              case " +
                "                                  when so.credit_audit_status =30 and so.closing_time is not null then customer_id" +
                "                                  else null end " +
                "                              )) as installment_pay_credit_audit_close" +
                "   " +
                "   from sh_order so" +
                "            inner join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted=0  AND sof.ds = " + ds +
                "            inner join sh_mini_config smf on so.mini_type = smf.mini_type AND smf.ds = " + ds +
                "   where so.ds = " + ds +
                "       and (so.test_role=0 or so.test_role is null) "+
                "     AND date(so.cash_deposit_pay_time) between date_sub(getdate(), 7) and date(getdate())" +
                "    group by date (cash_deposit_pay_time);";
        list.add(packageParam(sql, "installment_pay", "installment_pay_count_whole", "installment_pay_count", "time",
                "installment_weixin_pay",
                "installment_ali_pay",
                "installment_other_pay", "installment_pay_close", "installment_pay_credit_audit_close"));
    }


    private CompletableFuture<List<Map<String, String>>> getData(Map<String, Object> param2condition) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            // HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            // String format = SqlUtils.processTemplate(sql, key2value);
            List<Record> records = odpsUtil.querySql(sql);
            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            return records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = record.getString(column);
                    column2value.put(column, value);
                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
        });
    }

    private Map<String, Object> packageParam(String sql, String... columns) {
        HashMap<String, Object> param2condition = Maps.newHashMap();
        param2condition.put("sql", sql);
        param2condition.put("column", columns);
        return param2condition;
    }
}
