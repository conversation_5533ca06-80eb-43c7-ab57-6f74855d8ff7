package qnvip.data.overview.business.risk;

import cn.hutool.core.convert.Convert;
import com.alibaba.ttl.TtlWrappers;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.risk.RiskRentSituationDO;
import qnvip.data.overview.service.risk.RiskRentSituationService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * 风控大盘-租赁
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @date 2023/08/24
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RiskRentBusiness {

    private final OdpsUtil odpsUtil;

    private final RiskRentSituationService riskSituationService;

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            List<Map<String, Object>> list = Lists.newArrayList();

            getRiskUser(list, ds);
            getBigDataPass(list, ds);
            getValidUser(list, ds);
            getSendCount(list, ds);
            getMargin(list, ds);
            getPayUserByMiniType(list, ds);
            // 计算全部平台
            getRiskUserTotal(list, ds);
            getBigDataPassTotal(list, ds);
            getValidUserTotal(list, ds);
            getSendCountTotal(list, ds);
            getMarginTotal(list, ds);
            getPayUser(list, ds);
            List<CompletableFuture<List<Map<String, String>>>> futureList =
                    list.stream().map(this::getData).collect(Collectors.toList());
            CompletableFuture.allOf(futureList.toArray(new CompletableFuture[futureList.size()])).join();
            List<Map<String, String>> totalList = Lists.newArrayList();
            futureList.forEach(future -> {
                try {
                    totalList.addAll(future.get());
                } catch (Exception e) {
                    log.error("RiskSituationBusiness runCore error,{}", e.getMessage(), e);
                }
            });

            Map<String, List<Map<String, String>>> channel2list =
                    totalList.stream().collect(Collectors.groupingBy(map -> map.get("business_channel")));
            LinkedList<RiskRentSituationDO> dos = Lists.newLinkedList();
            channel2list.forEach((channel, channelList) -> {
                RiskRentSituationDO riskRentSituationDO = new RiskRentSituationDO();
                Map<String, String> merged = new HashMap<>();
                channelList.forEach(merged::putAll);
                String bigDataPass = Optional.ofNullable(merged.get("big_data_pass")).orElse("0");
                String centralBankPass = Optional.ofNullable(merged.get("central_bank_pass")).orElse("0");
                String centralBankInquiry = Optional.ofNullable(merged.get("central_bank_inquiry")).orElse("0");
                String payUser = Optional.ofNullable(merged.get("pay_user")).orElse("0");
                String payUserMerchant = Optional.ofNullable(merged.get("pay_user_merchant")).orElse("0");
                String validUser = Optional.ofNullable(merged.get("valid_user")).orElse("0");
                String passUser = Optional.ofNullable(merged.get("pass_user")).orElse("0");
                String riskUser = Optional.ofNullable(merged.get("risk_user")).orElse("0");
                String margin = Optional.ofNullable(merged.get("margin")).orElse("0");
                String totalAmount = Optional.ofNullable(merged.get("total_amount")).orElse("0");
                String buyOutAmt = Optional.ofNullable(merged.get("buy_out_amt")).orElse("0");

                String marginMerchant = Optional.ofNullable(merged.get("margin_merchant")).orElse("0");
                String totalAmountMerchant = Optional.ofNullable(merged.get("total_amount_merchant")).orElse("0");
                String buyOutAmtMerchant = Optional.ofNullable(merged.get("buy_out_amt_merchant")).orElse("0");

                String sendCount = Optional.ofNullable(merged.get("send_count")).orElse("0");
                String paySelfCount = Optional.ofNullable(merged.get("pay_self_count")).orElse("0");
                String payUserSelfClose = Optional.ofNullable(merged.get("pay_user_self_close")).orElse("0");
                String merchantSendCount = Optional.ofNullable(merged.get("merchant_send_count")).orElse("0");
                String payUserClose = Optional.ofNullable(merged.get("pay_user_close")).orElse("0");
                String payCreditAuditClose = Optional.ofNullable(merged.get("pay_credit_audit_close")).orElse("0");
                String curDayNum = Optional.ofNullable(merged.get("cur_day_num")).orElse("0");
                String otherPay = Optional.ofNullable(merged.get("other_pay")).orElse("0");
                String aliPay = Optional.ofNullable(merged.get("ali_pay")).orElse("0");
                String weixinPay = Optional.ofNullable(merged.get("weixin_pay")).orElse("0");

                totalAmount = "\\N".equals(totalAmount) ? "0" : totalAmount;
                buyOutAmt = "\\N".equals(buyOutAmt) ? "0" : buyOutAmt;
                margin = "\\N".equals(margin) ? "0" : margin;

                marginMerchant = "\\N".equals(marginMerchant) ? "0" : marginMerchant;
                totalAmountMerchant = "\\N".equals(totalAmountMerchant) ? "0" : totalAmountMerchant;
                buyOutAmtMerchant = "\\N".equals(buyOutAmtMerchant) ? "0" : buyOutAmtMerchant;

                riskRentSituationDO.setCountDay(countDay);

                riskRentSituationDO.setTotalAmt(new BigDecimal(totalAmount));
                riskRentSituationDO.setBuyOutAmt(new BigDecimal(buyOutAmt));
                riskRentSituationDO.setMargin(new BigDecimal(margin));
                riskRentSituationDO.setBusinessChannel(Integer.valueOf(channel));
                riskRentSituationDO.setRiskUser(Long.valueOf(riskUser));
                riskRentSituationDO.setValidUser(Long.valueOf(validUser));
                riskRentSituationDO.setPassUser(Long.valueOf(passUser));
                riskRentSituationDO.setCentralBankInquiry(Long.valueOf(centralBankInquiry));
                riskRentSituationDO.setCentralBankPass(Long.valueOf(centralBankPass));
                riskRentSituationDO.setBigDataPass(Long.valueOf(bigDataPass));
                riskRentSituationDO.setPayUser(Long.valueOf(payUser));
                riskRentSituationDO.setSendCount(Long.valueOf(sendCount));
                riskRentSituationDO.setMerchantSendCount(Long.valueOf(merchantSendCount));
                riskRentSituationDO.setPayUserClose(Long.valueOf(payUserClose));
                riskRentSituationDO.setPayCreditAuditClose(Long.valueOf(payCreditAuditClose));
                riskRentSituationDO.setCurDayCount(Integer.valueOf(curDayNum));
                riskRentSituationDO.setAliPay(Long.valueOf(aliPay));
                riskRentSituationDO.setWeiXinPay(Long.valueOf(weixinPay));
                riskRentSituationDO.setOtherPay(Long.valueOf(otherPay));
                riskRentSituationDO.setPaySelfCount(Long.valueOf(paySelfCount));
                riskRentSituationDO.setPayUserSelfClose(Long.valueOf(payUserSelfClose));
                riskRentSituationDO.setPayUserMerchant(Long.valueOf(payUserMerchant));
                riskRentSituationDO.setMarginMerchant(new BigDecimal(marginMerchant));
                riskRentSituationDO.setTotalAmtMerchant(new BigDecimal(totalAmountMerchant));
                riskRentSituationDO.setBuyOutAmtMerchant(new BigDecimal(buyOutAmtMerchant));
                dos.add(riskRentSituationDO);
            });
            riskSituationService.saveBatch(dos);
        } catch (Exception e) {
            log.error("================RiskSituationBusiness ERROR==================== error:{}", e.getMessage(), e);
        }

    }

    public static void main(String[] args) {
        BigDecimal bigDecimal = new BigDecimal("0");
        System.out.println("bigDecimal = " + bigDecimal);
    }

    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUser(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type               business_channel," +
                "       count(DISTINCT customerId)       risk_user," +
                "       COUNT(DISTINCT (CASE" +
                "                           WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN cl.customerId" +
                "                           ELSE 0 END)) pass_user" +
                " from cl_loan cl" +
                "             INNER JOIN rent_order a on cl.loanno = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " WHERE  cl.ds = " + ds +
                "  and cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                "  and cl.parentNo = ''" +
                " group by a.mini_type";
        list.add(packageParam(sql, "risk_user", "pass_user", "business_channel"));
    }

    /**
     * 有效用户
     *
     * @param list
     */
    private void getValidUser(List<Map<String, Object>> list, String ds) {
        String sql = "select count(DISTINCT (s.userId)) valid_user, a.mini_type business_channel " +
                " from serial_no s" +
                "             INNER JOIN rent_order a on s.businessNo = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " where s.userId not in (select userId from serial_no" +
                "    where riskStrategy !=''" +
                "        and createTime >= to_char(date_sub(${fromTime},  30 ),'yyyy-mm-dd')" +
                "        and createTime <= ${fromTime}" +
                "        and ds = to_char(getdate(), 'yyyymmdd')" +
                " )" +
                " and s.ds = " + ds +
                "  and s.createTime >= ${fromTime} and s.createTime <= ${toTime}" +
                "  and s.ds = " + ds +
                "  and s.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " group by a.mini_type ;";
        list.add(packageParam(sql, "valid_user", "business_channel"));
    }

    /**
     * 发货数量
     *
     * @param list
     */
    private void getSendCount(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT (CASE WHEN l.sendStatus = 5 THEN l.customerId ELSE " +
                " NULL END)) send_count ,ro.mini_type business_channel" +
                "  from cl_loan l" +
                "         inner join rent_order ro on l.loanno = ro.no"+
                "             INNER JOIN rent_order_infomore b on b.order_id = ro.id and b.common_rent_flag <>10 and b.ds = " + ds +
                "  where l.parentNo = ''" +
                "  and l.ds = " + ds +
                "  and ro.ds = " + ds +
                "   and ro.merchant_id in( 100,********)"+
                "  and ro.is_deleted=0"+
                "  and date(sendtime) = ${sendTime} " +
                "  and l.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " group by ro.mini_type ;";
        list.add(packageParam(sql, "send_count", "business_channel"));
    }



    /**
     * 获取大数据通过人数
     *
     * @param list
     */
    private void getBigDataPass(List<Map<String, Object>> list, String ds) {
        String sql = "select a.mini_type business_channel," +
                "       count(DISTINCT (case when cl.riskStatus>16 and sn.rhtime >= ${fromTime} " +
                "  and sn.rhtime <= ${toTime} then cl.customerId else null end)" +
                "           )  as central_bank_inquiry," +
                "       count(DISTINCT (case" +
                "                           when riskStatus in (20, 21, 25) and" +
                "                                cl.createtime >= ${fromTime}" +
                "                               and cl.createtime <= ${toTime}" +
                "                               and" +
                "                                to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =" +
                "                                to_char(cl.createtime, 'yyyy-mm-dd')" +
                "                                then cl.customerId" +
                "                           else null end)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (" +
                "           case when cl.opinionAmount <> '' and sn.rhtime >= ${fromTime}" +
                "   and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as central_bank_pass," +
                "       count(DISTINCT (" +
                "           case when cl.artificialAuditStatus = 10 and sn.rhtime >= ${fromTime}" +
                "       and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as big_data_pass" +
                " from cl_loan cl" +
                "         inner join serial_no sn" +
                "                    on sn.businessNo = cl.loanNo" + "  and sn.ds = " + ds +
                " left JOIN rent_order a on      cl.loanno = a.no" + "  and a.ds = " + ds +
                "          left join rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                   inner JOIN rent_order_infomore roi on roi.order_id = a.id and roi.common_rent_flag <>10 and roi.ds = " + ds +
                " where  cl.parentNo = ''" +
                "  and cl.ds =" + ds +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " group by a.mini_type";
        list.add(packageParam(sql, "central_bank_inquiry", "big_data_pass",  "central_bank_pass",
                "business_channel",  "cur_day_num"));
    }

    /**
     * 获取大数据通过人数
     *
     * @param list
     */
    private void getPayUser(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT \n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and\n" +
                "                                   merchant_id in( 100,********) and roi.common_rent_flag <>10 THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN \n" +
                "                                   merchant_id in( 100,********) THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_self_count,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and merchant_id not in ( 100,********)  THEN " +
                "customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_merchant,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination=5 and roi.common_rent_flag <>10 THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_close,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN  merchant_id in( 100,********) and a.termination=5 THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_user_self_close,\n" +
                "          (count(if(roi.risk_auth_status = 5 and a.termination=5, customer_id, null)) +\n" +
                "           count(if(roi.risk_auth_tag like '%未接通%' and a.termination=5, customer_id,\n" +
                "                    null)))                                                                                    as pay_credit_audit_close,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'WECHAT' and roi.common_rent_flag <>10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as weixin_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'ALIPAY' and roi.common_rent_flag <>10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as ali_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and\n" +
                "                                   rmc.platform_code not in ('ALIPAY', 'WECHAT') and roi.common_rent_flag <>10 then customer_id\n" +
                "                              else null end))                                                                  as other_pay\n" +
                "   FROM rent_order a\n" +
                "            inner join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "            inner JOIN rent_order_infomore roi on roi.order_id = a.id  " +
                " and roi.ds = " + ds +
                "   where a.parent_id =0\n" +
                "     AND a.ds = " + ds +
                "     and a.is_deleted=0\n" +
                "   and a.type =1\n" +
                "     AND payment_time between ${fromTime} and ${toTime};";
        list.add(packageParam(sql, "pay_user", "pay_user_merchant", "pay_user_close", "weixin_pay", "ali_pay",
                "other_pay",  "pay_credit_audit_close","pay_user_self_close","pay_self_count"));

    }


    /**
     * 获取大数据通过人数
     *
     * @param list
     */
    private void getPayUserByMiniType(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type business_channel,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and\n" +
                "                                   merchant_id  in ( 100,********)  and roi.common_rent_flag <>10 " +
                "THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN \n" +
                "                                   merchant_id in( 100,********) THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_self_count,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN  merchant_id in( 100,********) and a.termination=5 THEN customer_id\n" +
                "                              ELSE NULL END))                                                       " +
                "           AS pay_user_self_close,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination<>5 and merchant_id not in ( 100,********)  THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_merchant,\n" +
                "          count(DISTINCT (CASE\n" +
                "                              WHEN a.termination=5  and roi.common_rent_flag <>10 THEN customer_id\n" +
                "                              ELSE NULL END))                                                                  AS pay_user_close,\n" +
                "          (count(if(roi.risk_auth_status = 5 and a.termination=5 and roi.common_rent_flag <>10, customer_id , null)) +\n" +
                "           count(if(roi.risk_auth_tag like '%未接通%' and a.termination=5 and roi.common_rent_flag <>10, customer_id,\n" +
                "                    null)))                                                                                    as pay_credit_audit_close,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'WECHAT' and roi.common_rent_flag <>10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as weixin_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and rmc.platform_code = 'ALIPAY' and roi.common_rent_flag <>10" +
                "                                  then customer_id\n" +
                "                              else null end))                                                                  as ali_pay,\n" +
                "          count(DISTINCT (case\n" +
                "                              when a.termination<>5 and\n" +
                "                                   rmc.platform_code not in ('ALIPAY', 'WECHAT') and roi.common_rent_flag <>10 then customer_id\n" +
                "                              else null end))                                                                  as other_pay\n" +
                "   FROM rent_order a\n" +
                "            inner join rent_mini_config rmc on a.mini_type = rmc.mini_type\n" +
                "            inner JOIN rent_order_infomore roi on roi.order_id = a.id " +
                " and roi.ds = " + ds +
                "   where a.parent_id =0\n" +
                "     AND a.ds = " + ds +
                "     and a.is_deleted=0\n" +
                "   and a.type =1\n" +
                "     AND payment_time between ${fromTime} and ${toTime}" +
                "   group by a.mini_type" +
                ";";
        list.add(packageParam(sql, "pay_user", "pay_user_merchant", "pay_user_close", "weixin_pay", "ali_pay",
                "other_pay",  "pay_credit_audit_close","business_channel"));

    }

    /**
     * 风控用户 基础通过用户
     *
     * @param list
     */
    private void getRiskUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT count(DISTINCT customerId)       risk_user," +
                "       COUNT(DISTINCT (CASE" +
                "                           WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN cl.customerId" +
                "                           ELSE 0 END)) pass_user" +
                " from cl_loan cl" +
                "             INNER JOIN rent_order a on cl.loanno = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " WHERE  cl.ds = " + ds +
                "  and cl.createTime >= ${fromTime}" +
                "  and cl.createTime <= ${toTime}" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                "  and cl.parentNo = '';";
        list.add(packageParam(sql, "risk_user", "pass_user"));
    }

    /**
     * 有效用户
     *
     * @param list
     */
    private void getValidUserTotal(List<Map<String, Object>> list, String ds) {
        String sql = " select count(DISTINCT(s.userId)) valid_user" +
                " from serial_no s" +
                "             INNER JOIN rent_order a on s.businessNo = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " where s.userId not in (" +
                "    select userId from serial_no where riskStrategy !=''" +
                "        and createTime >= to_char(date_sub( ${fromTime}, 30),'yyyy-mm-dd')" +
                "        and createTime <= ${fromTime}" +
                "        and ds = to_char(getdate(), 'yyyymmdd')" +
                "        )" +
                " and s.createTime >= ${fromTime}" +
                " and s.createTime <= ${toTime}" +
                "  and s.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " and s.ds = " + ds;
        list.add(packageParam(sql.concat(";"), "valid_user"));
    }

    /**
     * 发货数
     *
     * @param list
     */
    private void getSendCountTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select COUNT(DISTINCT if(merchant_id  in ( 100,********), customer_id, null))  send_count," +
                "       COUNT(DISTINCT if(merchant_id not in ( 100,********), customer_id, null)) merchant_send_count" +
                " from rent_order ro" +
                "         inner join rent_order_logistics rol on ro.id = rol.order_id and rol.ds = " + ds+
                "             INNER JOIN rent_order_infomore b on b.order_id = ro.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " where ro.parent_id = 0" +
                "  and ro.ds = " + ds+
                "  and ro.is_deleted = 0" +
                "  and ro.termination <> 5" +
                "  and ro.type = 1" +
                "  and date (send_time) = ${sendTime};";
        list.add(packageParam(sql, "send_count", "merchant_send_count"));
    }

    /**
     * 执行人行风控、反欺诈通过、评分通过数
     *
     * @param list
     */
    private void getBigDataPassTotal(List<Map<String, Object>> list, String ds) {
        String sql = "select " +
                "       count(DISTINCT (case when cl.riskStatus>16 and sn.rhtime >= ${fromTime} " +
                "  and sn.rhtime <= ${toTime} then cl.customerId else null end)" +
                "           )  as central_bank_inquiry," +
                "       count(DISTINCT (case" +
                "                           when riskStatus in (20, 21, 25) and" +
                "                                cl.createtime >= ${fromTime}" +
                "                               and cl.createtime <= ${toTime}" +
                "                               and" +
                "                                to_char(to_date(sn.rhtime, 'yyyy-mm-dd hh:mi:ss'), 'yyyy-mm-dd') =" +
                "                                to_char(cl.createtime, 'yyyy-mm-dd')" +
                "                                then cl.customerId" +
                "                           else null end)" +
                "           ) as cur_day_num," +
                "       count(DISTINCT (" +
                "           case when cl.opinionAmount <> '' and sn.rhtime >= ${fromTime}" +
                "   and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as central_bank_pass," +
                "       count(DISTINCT (" +
                "           case when cl.artificialAuditStatus = 10 and sn.rhtime >= ${fromTime}" +
                "       and sn.rhtime <= ${toTime} then cl.customerId else null end" +
                "           )) as big_data_pass," +
                "       count(DISTINCT (" +
                "           case when cl.payStatus = 1  and cl.closeStatus = 0 and merchant_id  in ( 100,********) and cl" +
                " .paytime >= ${fromTime}" +
                "               and cl.paytime <= ${toTime}  then cl.customerId else null end" +
                "           )) as pay_user," +
                "       count(DISTINCT (" +
                "           case when cl.payStatus = 1  and cl.closeStatus = 0 and merchant_id not in ( 100,********) and cl" +
                " .paytime >= ${fromTime}" +
                "               and cl.paytime <= ${toTime}  then cl.customerId else null end" +
                "           )) as pay_user_merchant," +
                "        count(DISTINCT (" +
                "           case" +
                "               when cl.payStatus = 1 and cl.closeStatus = 1 and cl.paytime >= ${fromTime}" +
                "                   and cl.paytime <= ${toTime} then cl.customerId" +
                "               else null end" +
                "           )) as pay_user_close," +
                "    (count(if(roi.risk_auth_status = 5 and cl.closeStatus = 1 and\n" +
                "                    cl.paytime >= ${fromTime}\n" +
                "                   and cl.paytime <= ${toTime}, cl.customerId, null))\n" +
                "           +count(if(roi.risk_auth_tag like '%未接通%' and cl.closeStatus = 1 and\n" +
                "                    cl.paytime >= ${fromTime}\n" +
                "                   and cl.paytime <= ${toTime}, cl.customerId, null)))\n" +
                "           as pay_credit_audit_close,"+
                "       count(DISTINCT (" +
                "           case" +
                "               when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code='WECHAT' and" +
                "                    cl.paytime >= ${fromTime}" +
                "                   and cl.paytime <= ${toTime} then cl.customerId" +
                "               else null end" +
                "           ))        as weixin_pay," +
                "              count(DISTINCT (" +
                "                  case" +
                "                      when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code='ALIPAY' and" +
                "                           cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime} then cl.customerId" +
                "                      else null end" +
                "                  )) as ali_pay," +
                "              count(DISTINCT (" +
                "                  case" +
                "                      when cl.payStatus = 1 and cl.closeStatus = 0 and rmc.platform_code not in('ALIPAY','WECHAT') and" +
                "                           cl.paytime >= ${fromTime}" +
                "                          and cl.paytime <= ${toTime} then cl.customerId" +
                "                      else null end" +
                "                  )) as other_pay" +
                " from cl_loan cl" +
                "         inner join serial_no sn" +
                "                    on sn.businessNo = cl.loanNo" + "  and sn.ds = " + ds +
                " left JOIN rent_order a on      cl.loanno = a.no" + "  and a.ds = " + ds +
                "          left join rent_mini_config rmc on a.mini_type = rmc.mini_type" +
                "                   inner JOIN rent_order_infomore roi on roi.order_id = a.id and roi.common_rent_flag <>10 and roi.ds = " + ds +
                " where  cl.parentNo = ''" +
                "  and cl.ds =" + ds +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34);";
        list.add(packageParam(sql.concat(";"), "central_bank_inquiry", "big_data_pass",
                "central_bank_pass", "pay_user", "pay_user_close", "cur_day_num", "weixin_pay", "ali_pay", "other_pay"
                ,"pay_credit_audit_close","pay_user_merchant"));
    }

    /**
     * 获取比率
     *
     * @param list
     */
    private void getMarginTotal(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT SUM(if(merchant_id  in ( 100,********), cl.performanceBond, null)) margin," +
                "       SUM(if(merchant_id  in ( 100,********), cl.loanAmount, null))      total_amount," +
                "       sum(if(merchant_id  in ( 100,********), rr.brokenAmount, null))    buy_out_amt," +
                "       SUM(if(merchant_id not in ( 100,********), cl.performanceBond, null)) margin_merchant," +
                "       SUM(if(merchant_id not in ( 100,********), cl.loanAmount, null))      total_amount_merchant," +
                "       sum(if(merchant_id not in ( 100,********), rr.brokenAmount, null))    buy_out_amt_merchant" +
                " FROM cl_loan cl" +
                "    INNER JOIN rc_assess_record rr ON rr.loanId=cl.id" +
                "    INNER JOIN rent_order a on cl.loanno = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                " and cl.ds =" + ds +
                " and rr.ds = " + ds;
        list.add(packageParam(sql.concat(";"), "margin", "total_amount", "buy_out_amt",
                "margin_merchant","total_amount_merchant","buy_out_amt_merchant"));
    }


    /**
     * 获取总保证金
     *
     * @param list
     */
    private void getMargin(List<Map<String, Object>> list, String ds) {
        String sql = "SELECT a.mini_type business_channel,SUM(if(merchant_id  in ( 100,********), cl.performanceBond, null)) " +
                "margin," +
                "       SUM(if(merchant_id  in ( 100,********), cl.loanAmount, null))      total_amount," +
                "       sum(if(merchant_id  in ( 100,********), rr.brokenAmount, null))    buy_out_amt," +
                "       SUM(if(merchant_id not in ( 100,********), cl.performanceBond, null)) margin_merchant," +
                "       SUM(if(merchant_id not in ( 100,********), cl.loanAmount, null))      total_amount_merchant," +
                "       sum(if(merchant_id not in ( 100,********), rr.brokenAmount, null))    buy_out_amt_merchant"+
                " FROM cl_loan cl" +
                "    INNER JOIN rc_assess_record rr ON rr.loanId=cl.id" +
                "             INNER JOIN rent_order a on cl.loanno = a.no and a.ds = " + ds +
                "             INNER JOIN rent_order_infomore b on b.order_id = a.id and b.common_rent_flag <>10 and b.ds = " + ds +
                " WHERE cl.paytime >= ${fromTime} and cl.paytime <= ${toTime}" +
                " and cl.payStatus=1" +
                " and cl.closeStatus=0" +
                " and cl.opinionAmount !=''" +
                " and cl.artificialAuditorId=1" +
                "  and cl.businessChannel in (1,7,10,11,12,14,15,23,25,26,27,29,31,32,34)" +
                " and cl.ds = " + ds +
                " and rr.ds = " + ds +
                " group by a.mini_type";
        list.add(packageParam(sql, "margin", "total_amount", "buy_out_amt",
                "margin_merchant","total_amount_merchant","buy_out_amt_merchant", "business_channel"));
    }


    private CompletableFuture<List<Map<String, String>>> getData(Map<String, Object> param2condition) {
        return CompletableFuture.supplyAsync(TtlWrappers.wrapSupplier(() -> {
            String sql = (String) param2condition.get("sql");
            List<String> columns = Convert.toList(String.class, param2condition.get("column"));
            HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");
            String format = SqlUtils.processTemplate(sql, key2value);
            List<Record> records = odpsUtil.querySql(format.concat(";"));
            if (CollectionUtils.isEmpty(records)) {
                return Lists.newArrayList();
            }
            List<Map<String, String>> collect = records.stream().map(record -> {
                Map<String, String> column2value = Maps.newHashMap();
                for (String column : columns) {
                    String value = record.getString(column);
                    column2value.put(column, value);
                }
                if (!column2value.containsKey("business_channel")) {
                    column2value.put("business_channel", "-5");
                }
                return column2value;
            }).collect(Collectors.toList());
            return collect;

        }));
    }

    private Map<String, Object> packageParam(String sql, String... columns) {
        HashMap<String, Object> param2condition = Maps.newHashMap();
        param2condition.put("sql", sql);
        param2condition.put("column", columns);
        return param2condition;
    }
}
