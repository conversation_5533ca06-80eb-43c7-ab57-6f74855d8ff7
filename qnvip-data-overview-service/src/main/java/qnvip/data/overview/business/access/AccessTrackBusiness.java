package qnvip.data.overview.business.access;

import com.aliyun.odps.data.Record;
import com.blinkfox.zealot.bean.SqlInfo;
import com.blinkfox.zealot.core.ZealotKhala;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.access.AccessCoreDO;
import qnvip.data.overview.domain.access.AccessFlowStructureDO;
import qnvip.data.overview.domain.access.AccessPageAccessDO;
import qnvip.data.overview.enums.EventTrackSqlParamEnum;
import qnvip.data.overview.service.access.AccessCoreService;
import qnvip.data.overview.service.access.AccessFlowStructureService;
import qnvip.data.overview.service.access.AccessPageAccessService;
import qnvip.data.overview.util.ObjectUtils;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/9
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class AccessTrackBusiness {


    // TODO 引入一个线程池

    private final OdpsUtil odpsUtil;

    private final AccessCoreService accessCoreService;

    private final AccessPageAccessService accessPageAccessService;

    private final AccessFlowStructureService accessFlowStructureService;

    /**
     * 组装核心指标参数
     */
    public void runCore() {

        try {
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

            // 访问用户数and访问次数
            CompletableFuture<Map<Integer, AccessCoreDO>> f1 =
                    CompletableFuture.supplyAsync(this::miniType2PvUv);
            // 新增访问用户数
            CompletableFuture<Map<Integer, AccessCoreDO>> f2 =
                    CompletableFuture.supplyAsync(this::miniType2uvNew);
            // 复返用户数
            CompletableFuture<Map<Integer, AccessCoreDO>> f3 =
                    CompletableFuture.supplyAsync(this::miniType2uvOld);
            // 商品访客数
            CompletableFuture<Map<Integer, AccessCoreDO>> f4 =
                    CompletableFuture.supplyAsync(this::miniType2GoodsUv);
            // 平均停留时长，访问小程序停留时间总和/访问小程序的用户数(uv)
            // 总停留时长
            CompletableFuture<Map<Integer, AccessCoreDO>> f5 =
                    CompletableFuture.supplyAsync(this::miniType2keepTime);
            // 跳失率,总跳失数/总访问次数(pv)
            CompletableFuture<Map<Integer, AccessCoreDO>> f6 =
                    CompletableFuture.supplyAsync(this::miniType2JumpLoss);

            CompletableFuture.allOf(f1, f2, f3, f4, f5, f6).join();
            /* 根据miniType合并处理 */
            Stream.of(f1.get(), f2.get(), f3.get(), f4.get(), f5.get(), f6.get()).
                    flatMap(x -> x.entrySet().stream()).
                    collect(Collectors.toMap(Map.Entry::getKey, value -> Lists.newArrayList(value.getValue()),
                            (List<AccessCoreDO> newValueList, List<AccessCoreDO> oldValueList) -> {
                                oldValueList.addAll(newValueList);
                                return oldValueList;
                            })).
                    forEach((key, value) -> {
                        AccessCoreDO accessCoreDO = new AccessCoreDO();
                        value.forEach(v -> {
                            try {
                                ObjectUtils.merge(accessCoreDO, v, ((field, pair) -> pair.getLeft() + "," + pair.getRight()));
                            } catch (IllegalAccessException | InvocationTargetException e) {
                                log.error("merge Do error {}", e.getMessage());
                            }
                        });
                        accessCoreDO.setCountDay(countDay);
                        accessCoreDO.setMiniType(key);
                        accessCoreService.saveOrUpdate(accessCoreDO);
                    });
        } catch (Exception e) {
            log.error("coreDataFormat error {}", e.getMessage());
        }
    }

    /**
     * 流量构成
     */
    public void runFlowStructure() {

        String sql = "select e.mini_type, " +
                "                       nvl(e.pv, 0)   as   pv, " +
                "                       nvl(e.uv, 0)   as   uv, " +
                "                       nvl(e.old, 0)  as   oldCustomer, " +
                "                       nvl(e.new, 0)  as   newCustomer, " +
                "                       nvl(f.orderPv, 0) as orderPv, " +
                "                       nvl(f.orderUv, 0) as  orderUv " +
                "                 from (select c.mini_type, " +
                "                             c.pv, " +
                "                             c.uv, " +
                "                             total    old, " +
                "                             newCount new " +
                "                      from (select a.mini_type, a.pv, a.uv, b.total " +
                "                            from (select source_mini_type as mini_type " +
                "                                       , count(distinct customer_third_id) uv " +
                "                                       , count(customer_third_id)          pv " +
                "                                  from dataview_track_enter_applets " +
                "                                  where action_type = 1  " +
                "                                    and create_time >= ${fromTime} " +
                "                                    and create_time <= ${toTime}" +
                "                                    and ds = ${dsStr}" +
                "                                  group by source_mini_type) a " +
                "                                     left join " +
                "                                 (select d.mini_type, count(*) total " +
                "                                  from (select source_mini_type as mini_type, count(*) cnt " +
                "                                        from dataview_track_enter_applets " +
                "                                        where action_type = 1  " +
                "                                          and create_time >= ${fromTime} " +
                "                                          and create_time <= ${toTime} " +
                "                                            and ds = ${dsStr}" +
                "                                        group by source_mini_type, customer_third_id " +
                "                                       ) d " +
                "                                  where d.cnt >= 2 " +
                "                                  group by mini_type) b " +
                "                                 on a.mini_type = b.mini_type " +
                "                           ) c " +
                "                               left join " +
                "                           (select mini_type, count(distinct customer_third_id) newCount " +
                "                            from dataview_track_new_access " +
                "                            where 1 = 1 " +
                "                              and create_time >= ${fromTime} " +
                "                              and create_time <= ${toTime} " +
                "                             and ds = ${dsStr}" +
                "                            group by mini_type) d " +
                "                           on c.mini_type = d.mini_type) e " +
                "                 " +
                "                         left join " +
                "                         (select mini_type, count(*) orderPv, count(distinct customer_third_id) orderUv " +
                "                          from dataview_track_submit_order_click " +
                "                          where 1 = 1 " +
                "                            and create_time >= ${fromTime} " +
                "                            and create_time <= ${toTime} " +
                "                            and ds = ${dsStr}" +
                "                          group by mini_type) f " +
                "                     on e.mini_type = f.mini_type";

        HashMap<String, Object> key2value = Maps.newHashMap();
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.forEach(record -> {
            AccessFlowStructureDO domain = new AccessFlowStructureDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String oldUv = record.getString("oldcustomer");
            String newUv = record.getString("newcustomer");
            String orderPv = record.getString("orderpv");
            String orderUv = record.getString("orderuv");
            domain.setCountDay(countDay);
            domain.setUv(Long.parseLong(uv));
            domain.setPv(Long.parseLong(pv));
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setUvOld(Long.parseLong(oldUv));
            domain.setUvNew(Long.parseLong(newUv));
            domain.setPvTrading(Long.parseLong(orderPv));
            domain.setUvTrading(Long.parseLong(orderUv));
            accessFlowStructureService.saveOrUpdate(domain);
        });

    }


    /**
     * 页面分析,当前页面的访问总时长/当前页面的访问人数
     */
    public void runPageAccess() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        SqlInfo sqlInfo = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc().
                        concat(",").
                        concat(EventTrackSqlParamEnum.ENTER_PAGE_CODE.name()).
                        concat(",").
                        concat(EventTrackSqlParamEnum.AVG_KEEP_TIME.getDesc())).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_PAGE_STATISTICS.name()).
                where(EventTrackSqlParamEnum.ACTION_LEAVE.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                groupBy(EventTrackSqlParamEnum.ENTER_PAGE_CODE.name().concat(",").concat(EventTrackSqlParamEnum.MINI_TYPE.name()).concat(";")).
                end();
        String sql = SqlUtils.formart(sqlInfo);
        List<Record> records = odpsUtil.querySql(sql);

        if (CollectionUtils.isEmpty(records)) {
            return;
        }

        records.forEach(record -> {
            AccessPageAccessDO domain = new AccessPageAccessDO();
            String miniType = record.getString("mini_type");
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String avgKeepTime = record.getString("avgkeeptime");
            String enterPageCode = record.getString("enter_page_code");
            domain.setCountDay(countDay);
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setEnterPageCode(Long.valueOf(enterPageCode));
            domain.setUv(Long.parseLong(uv));
            domain.setPv(Long.parseLong(pv));
            domain.setAvgKeepTime(new BigDecimal(avgKeepTime));
            accessPageAccessService.saveOrUpdate(domain);
        });

    }


    /**
     * 总跳失数
     *
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2JumpLoss() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo6 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.COUNT.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_JUMP_LOSS.name()).
                where(EventTrackSqlParamEnum.CONDITION.getDesc()).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                and("ds = " + dsStr).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();

        String sql6 = SqlUtils.formart(sqlInfo6);
        List<Record> records = odpsUtil.querySql(sql6);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            String miniType = record.getString("mini_type");
            String total = record.getString("cnt");
            accessCoreDO.setTotalJumpLoss(Long.parseLong(total));
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    /**
     * 获取总停留时长
     *
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2keepTime() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo5 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.SUM_KEEP_TIME.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_LEAVE.getDesc()).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                and("ds = " + dsStr).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();

        String sql5 = SqlUtils.formart(sqlInfo5);
        List<Record> records = odpsUtil.querySql(sql5);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            String totalKeepTime = record.getString("sumkeeptime");
            String miniType = record.getString("mini_type");
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            accessCoreDO.setTotalKeepTime(new BigDecimal(totalKeepTime));
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    /**
     * 商品访客数
     *
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2GoodsUv() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo4 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_PAGE_STATISTICS.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                and(EventTrackSqlParamEnum.GOODS_DETAIL.getDesc()).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                and("ds = " + dsStr).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();
        String sql4 = SqlUtils.formart(sqlInfo4);
        List<Record> records = odpsUtil.querySql(sql4);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            String uv = record.getString("uv");
            String miniType = record.getString("mini_type");
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            accessCoreDO.setGoodsUv(Long.parseLong(uv));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    /**
     * 获取复访用户数
     */
    private Map<Integer, AccessCoreDO> miniType2uvOld() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo afterVisitSqlInfo = ZealotKhala.start().
                select(EventTrackSqlParamEnum.COUNT.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_LEAVE.getDesc()).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                and("ds = " + dsStr).
                groupBy(EventTrackSqlParamEnum.CUSTOMER_THIRD_ID.name().concat(", ").concat(EventTrackSqlParamEnum.MINI_TYPE.name())).
                end();
        String afterVisitSql = SqlUtils.formart(afterVisitSqlInfo);

        SqlInfo sqlInfo3 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.COUNT.getDesc()).
                from("(".concat(afterVisitSql).concat(") as tb")).
                where("tb.cnt >= 2").
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();

        String sql3 = SqlUtils.formart(sqlInfo3);
        List<Record> records = odpsUtil.querySql(sql3);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            String oldUv = record.getString("cnt");
            String miniType = record.getString("mini_type");
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            accessCoreDO.setUvOld(Long.parseLong(oldUv));
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    /**
     * 获取新增访问用户数
     *
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2uvNew() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo2 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_NEW_ACCESS.name()).
                where(EventTrackSqlParamEnum.CONDITION.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();
        String sql2 = SqlUtils.formart(sqlInfo2);
        List<Record> records = odpsUtil.querySql(sql2);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }
        return records.stream().map(record -> {
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            String uv = record.getString("uv");
            String miniType = record.getString("mini_type");
            accessCoreDO.setUvNew(Long.parseLong(uv));
            accessCoreDO.setMiniType(Integer.valueOf(miniType));
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));
    }

    /**
     * 获取全站PV和UV
     *
     * @return
     */
    private Map<Integer, AccessCoreDO> miniType2PvUv() {
        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        SqlInfo sqlInfo1 = ZealotKhala.start().
                select(EventTrackSqlParamEnum.PV_AND_UV.getDesc()).
                from(EventTrackSqlParamEnum.DATAVIEW_TRACK_ENTER_APPLETS.name()).
                where(EventTrackSqlParamEnum.ACTION_ENTER.getDesc()).
                and("ds = " + dsStr).
                andBetween(EventTrackSqlParamEnum.REPORT_TIME.name(), fromTime, toTime).
                groupBy(EventTrackSqlParamEnum.MINI_TYPE.name().concat(";")).
                end();
        String sql1 = SqlUtils.formart(sqlInfo1);
        List<Record> records = odpsUtil.querySql(sql1);
        if (CollectionUtils.isEmpty(records)) {
            return Maps.newHashMap();
        }

        return records.stream().map(record -> {
            AccessCoreDO accessCoreDO = new AccessCoreDO();
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            accessCoreDO.setUv(Long.parseLong(uv));
            accessCoreDO.setPv(Long.parseLong(pv));
            accessCoreDO.setMiniType(miniType);
            return accessCoreDO;
        }).collect(Collectors.toMap(AccessCoreDO::getMiniType, Function.identity()));

    }

    

}
