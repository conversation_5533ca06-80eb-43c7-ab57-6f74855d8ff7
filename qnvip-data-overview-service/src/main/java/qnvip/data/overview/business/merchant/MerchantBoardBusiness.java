package qnvip.data.overview.business.merchant;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.merchant.MerchantRealtimeBoardFlinkDO;
import qnvip.data.overview.service.merchant.MerchantRealtimeBoardFlinkService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MerchantBoardBusiness {

    private final OdpsUtil odpsUtil;
    private final MerchantRealtimeBoardFlinkService realtimeBoardFlinkService;

    public void execute() {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        String time = ThreadLocalCacheUtil.get("time");
        String prefix = "with dt as (select to_char(to_date(" + time + ", 'yyyy-mm-dd'), 'yyyymmdd') as ds)";
        String t1 = " t1 as (\n" +
                "    SELECT loan_no                                AS order_no\n" +
                "         , SUBSTR(b.create_time, 1, 10)           AS create_time\n" +
                "         , risk_level\n" +
                "         , shop_name\n" +
                "         , b.customer_id\n" +
                "         , IF(c.order_no IS NULL, '无效订单', '有效订单') AS effective_type\n" +
                "         , shunt_merchant_type\n" +
                "         , common_rent_flag\n" +
                "         , CASE\n" +
                "               WHEN sh_type = '商户线下引流'\n" +
                "                   OR (artificial_audit_status = 15 AND risk_level >= 14 AND risk_level <= 28)\n" +
                "                   OR common_rent_flag IN (10, 30)\n" +
                "                   OR (risk_fico_status IN (65, 80) AND artificial_audit_status = 15) THEN '可分流'\n" +
                "               ELSE '不可分流' END                    AS is_shunt_type\n" +
                "         , order_source\n" +
                "    FROM (SELECT cl.loanno                                   loan_no\n" +
                "               , cl.createtime                               create_time\n" +
                "               , cl.artificialauditstatus                    artificial_audit_status\n" +
                "               , cl.customerid                               customer_id\n" +
                "               , cl.riskficostatus                           risk_fico_status\n" +
                "               , IF(cl.businesschannel = 52, '商户线下引流', NULL) sh_type\n" +
                "          FROM cl_loan cl\n" +
                "          WHERE ds =  (select ds from dt)\n" +
                "            AND SUBSTR(cl.createtime, 1, 7) >= '2022-01'\n" +
                "            AND cl.parentno = ''\n" +
                "            AND cl.businesschannel NOT IN (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 17)) b\n" +
                "             LEFT JOIN (SELECT order_no\n" +
                "                             , b.order_id\n" +
                "                             , common_rent_flag\n" +
                "                             , risk_level\n" +
                "                             , shop_name\n" +
                "                             , CASE\n" +
                "                                   WHEN b.merchant_id IN (10000076, 100, 10000107) THEN '自营分流'\n" +
                "                                   WHEN b.merchant_id NOT IN (10000076, 100, 10000107) AND b.merchant_id IS NOT NULL\n" +
                "                                       THEN '商户分流'\n" +
                "                                   ELSE '未分流' END shunt_merchant_type\n" +
                "\n" +
                "                             , (case\n" +
                "                                    when merchant_drainage_tag = 15 or merchant_drainage_tag = 20\n" +
                "                                        then 1\n" +
                "                                    when merchant_drainage_tag IN (0, 10, 15, 20)\n" +
                "                                        AND a.merchant_id not in (10000076, 100, 10000107)\n" +
                "                                        and merchant_transfer = 0 then 2\n" +
                "                                    when merchant_drainage_tag = 5\n" +
                "                                        AND a.merchant_id not in (10000076, 100, 10000107)\n" +
                "                                        then 3\n" +
                "            end\n" +
                "            )                                     order_source\n" +
                "                        FROM (SELECT ro.no order_no\n" +
                "                                   , id\n" +
                "                                   , merchant_transfer\n" +
                "                                   , merchant_id\n" +
                "                              FROM rent_order ro\n" +
                "                              WHERE ds =  (select ds from dt)) a\n" +
                "                                 INNER JOIN (SELECT order_id\n" +
                "                                                  , common_rent_flag\n" +
                "                                                  , risk_level\n" +
                "                                             FROM rent_order_infomore\n" +
                "                                             WHERE ds =  (select ds from dt)) c\n" +
                "                                            ON a.id = c.order_id\n" +
                "                                 LEFT JOIN (SELECT *\n" +
                "                                            FROM (\n" +
                "                                                     SELECT create_time\n" +
                "                                                          , merchant_id\n" +
                "                                                          , order_id\n" +
                "                                                          , ROW_NUMBER() OVER (PARTITION BY order_id ORDER BY create_time ) AS rk\n" +
                "                                                     FROM rent_order_merchant_transfer_log\n" +
                "                                                     WHERE ds =  (select ds from dt)\n" +
                "                                                       AND status IN (5, 10, 15)\n" +
                "                                                       AND SUBSTR(create_time, 1, 7) >= '2022-01') b\n" +
                "                                            WHERE rk = 1) b\n" +
                "                                           ON a.id = b.order_id\n" +
                "                                 LEFT JOIN (SELECT order_id\n" +
                "                                                 , merchant_drainage_tag\n" +
                "                                            FROM rent_order_source\n" +
                "                                            WHERE ds =  (select ds from dt)) d\n" +
                "                                           ON a.id = d.order_id\n" +
                "                                 LEFT JOIN (\n" +
                "                            SELECT CASE\n" +
                "                                       WHEN shop_name = '人人享租' OR shop_name = '人人享租1' OR shop_name = '人人享租2' OR\n" +
                "                                            shop_name = '人人享租3' THEN '人人享租'\n" +
                "                                       WHEN shop_name = '郎力严选8' OR shop_name = '郎力严选6' OR shop_name = '郎力严选租赁'\n" +
                "                                           THEN '郎力严选'\n" +
                "                                       WHEN shop_name = '首占租机' OR shop_name = '首占租机1' " +
                "                                           THEN '首占租机'\n" +
                "                                       ELSE shop_name END AS shop_name\n" +
                "                                 , id\n" +
                "                            FROM rent_merchant\n" +
                "                            WHERE ds =  (select ds from dt)) g\n" +
                "                                           ON g.id = b.merchant_id\n" +
                "    ) c\n" +
                "                       ON b.loan_no = c.order_no\n" +
                ")";
        String t2 = "t2 as (\n" +
                "    SELECT loan_no                                AS order_no\n" +
                "         , SUBSTR(b.create_time, 1, 10)           AS create_time\n" +
                "         , risk_level\n" +
                "         , shop_name\n" +
                "         , SUBSTR(c.rent_start_date, 1, 10)          rent_start_date\n" +
                "         , (CASE\n" +
                "                WHEN transfer_status = 10 OR audit_status = 1\n" +
                "                    THEN SUBSTR(c.payment_time, 1, 10)\n" +
                "                ELSE NULL END)                    AS payment_time\n" +
                "         , SUBSTR(b.send_time, 1, 10)                send_time\n" +
                "         , sh_audit_status\n" +
                "         , SUBSTR(c.audit_time, 1, 10)               audit_time\n" +
                "         , b.customer_id\n" +
                "         , transfer_status\n" +
                "         , order_status\n" +
                "         , IF(c.order_no IS NULL, '无效订单', '有效订单') AS effective_type\n" +
                "         , sh_type\n" +
                "         , pay_type\n" +
                "         , send_type\n" +
                "         , common_rent_flag\n" +
                "         , order_source\n" +
                "    FROM (SELECT cl.loanno                                              loan_no\n" +
                "               , cl.createtime                                          create_time\n" +
                "               , cl.customerid                                          customer_id\n" +
                "               , cl.sendtime                                            send_time\n" +
                "               , IF(cl.businesschannel = 52, '商户线下引流', NULL)            sh_type\n" +
                "               , IF(paystatus = 1 AND closestatus = 0, '支付', '未支付')  AS pay_type\n" +
                "               , IF(sendstatus = 5 AND closestatus = 0, '发货', '未发货') AS send_type\n" +
                "          FROM cl_loan cl\n" +
                "          WHERE ds = (select ds from dt)\n" +
                "            AND SUBSTR(cl.createtime, 1, 7) >= '2023-06'\n" +
                "            AND cl.parentno = ''\n" +
                "            AND cl.businesschannel NOT IN (28, 51, 1013, 1014, 1015, 1016, 1017, 1018, 1019, 17)) b\n" +
                "             LEFT JOIN (SELECT order_no\n" +
                "                             , b.order_id\n" +
                "                             , common_rent_flag\n" +
                "                             , risk_level\n" +
                "                             , payment_time\n" +
                "                             , audit_status\n" +
                "                             , IF(b.merchant_id IS NOT NULL, b.merchant_id, a.merchant_id)                          merchant_id" +
                "                             , order_status\n" +
                "                             , transfer_status\n" +
                "                             , (CASE\n" +
                "                                    WHEN transfer_status = 10 OR audit_status = 1 THEN '审核通过'\n" +
                "                                    WHEN transfer_status = 15 OR audit_status = 2 THEN '审核拒绝'\n" +
                "                                    ELSE '其他' END)                                       AS sh_audit_status\n" +
                "                             , rent_start_date\n" +
                "                             , CASE\n" +
                "                                   WHEN b.create_time IS NULL AND merchant_transfer = 0 AND\n" +
                "                                        a.merchant_id NOT IN (10000076, 100, 10000107) THEN f.create_time\n" +
                "                                   WHEN b.create_time IS NOT NULL THEN b.create_time END AS audit_time\n" +
                "                             , CASE\n" +
                "                                   WHEN b.merchant_id IN (10000076, 100, 10000107) THEN '自营分流'\n" +
                "                                   WHEN b.merchant_id NOT IN (10000076, 100, 10000107) AND b.merchant_id IS NOT NULL\n" +
                "                                       THEN '商户分流'\n" +
                "                                   ELSE '未分流' END                                           shunt_merchant_type\n" +
                "                             , (case\n" +
                "                                    when merchant_drainage_tag = 15 or merchant_drainage_tag = 20\n" +
                "                                        then 1\n" +
                "                                    when merchant_drainage_tag IN (0, 10, 15, 20)\n" +
                "                                        AND a.merchant_id not in (10000076, 100, 10000107)\n" +
                "                                        and merchant_transfer = 0 then 2\n" +
                "                                    when merchant_drainage_tag = 5\n" +
                "                                        AND a.merchant_id not in (10000076, 100, 10000107)\n" +
                "                                        then 3\n" +
                "            end\n" +
                "            )                                                                               order_source\n" +
                "                        FROM (SELECT ro.no     order_no\n" +
                "                                   , id\n" +
                "                                   , merchant_transfer\n" +
                "                                   , rent_start_date\n" +
                "                                   , merchant_id\n" +
                "                                   , payment_time\n" +
                "                                   , status as order_status\n" +
                "                              FROM rent_order ro\n" +
                "                              WHERE ds = (select ds from dt)\n" +
                "                                ) a\n" +
                "                                 -- 取共租条件\n" +
                "                                 INNER JOIN (SELECT order_id\n" +
                "                                                  , common_rent_flag\n" +
                "                                                  , risk_level\n" +
                "                                             FROM rent_order_infomore\n" +
                "                                             WHERE ds = (select ds from dt)) c\n" +
                "                                            ON a.id = c.order_id\n" +
                "                                 LEFT JOIN (SELECT *\n" +
                "                                            FROM (\n" +
                "                                                     SELECT create_time\n" +
                "                                                          , merchant_id\n" +
                "                                                          , order_id\n" +
                "                                                          , status transfer_status\n" +
                "                                                     FROM rent_order_merchant_transfer_log\n" +
                "                                                     WHERE ds = (select ds from dt)\n" +
                "                                                       AND status IN (5, 10, 15)\n" +
                "                                                       AND merchant_id NOT IN (10000076, 100, 10000107)\n" +
                "                                                       AND SUBSTR(create_time, 1, 7) >= '2022-01') b) b\n" +
                "                                           ON a.id = b.order_id\n" +
                "                                 LEFT JOIN (SELECT order_id\n" +
                "                                                 , merchant_drainage_tag\n" +
                "                                            FROM rent_order_source\n" +
                "                                            WHERE ds = (select ds from dt)) d\n" +
                "                                           ON a.id = d.order_id\n" +
                "                                 LEFT JOIN (SELECT order_id\n" +
                "                                            FROM rent_order_logistics\n" +
                "                                            WHERE ds = (select ds from dt)\n" +
                "                                              AND is_deleted = 0) e\n" +
                "                                           ON a.id = e.order_id\n" +
                "                                 LEFT JOIN (SELECT order_id\n" +
                "                                                 , create_time\n" +
                "                                                 , type audit_type\n" +
                "                                                 , audit_status\n" +
                "                                            FROM rent_order_audit\n" +
                "                                            WHERE ds = (select ds from dt)\n" +
                "                                              AND type = 200) f\n" +
                "                                           ON a.id = f.order_id\n" +
                "    ) c\n" +
                "                       ON b.loan_no = c.order_no\n" +
                "             LEFT JOIN (\n" +
                "                            SELECT CASE\n" +
                "                                       WHEN shop_name = '人人享租' OR shop_name = '人人享租1' OR shop_name = '人人享租2' OR\n" +
                "                                            shop_name = '人人享租3' THEN '人人享租'\n" +
                "                                       WHEN shop_name = '郎力严选8' OR shop_name = '郎力严选6' OR shop_name = '郎力严选租赁'\n" +
                "                                           THEN '郎力严选'\n" +
                "                                       WHEN shop_name = '首占租机' OR shop_name = '首占租机1' " +
                "                                           THEN '首占租机'\n" +
                "                                       ELSE shop_name END AS shop_name\n" +
                "                                 , id\n" +
                "                            FROM rent_merchant\n" +
                "                            WHERE ds = (select ds from dt)) g\n" +
                "                                           ON g.id = c.merchant_id\n" +
                ")";
        String detailSql = "select nvl(a.risk_level, b.risk_level)     risk_level,\n" +
                "       nvl(a.order_source, b.order_source) order_source,\n" +
                "       nvl(nvl(a.shop_name, b.shop_name),'')       shop_name,\n" +
                "       nvl(a.is_on_rent, b.is_on_rent)     is_on_rent,\n" +
                "       nvl(a.申请人数, 0)                      apply_user,\n" +
                "       nvl(a.可分流人数, 0)                     can_shunt_user,\n" +
                "       nvl(b.实际分流人数, 0)                    real_shunt_user,\n" +
                "       nvl(b.实际分流订单数, 0)                   real_shunt_order_cnt,\n" +
                "       nvl(b.审核订单, 0)                      audit_order_cnt,\n" +
                "       nvl(b.当日审核当日通过, 0)                  cur_audit_order_cnt,\n" +
                "       nvl(b.待审核订单, 0)                     wait_audit_order_cnt,\n" +
                "       nvl(b.审核通过订单, 0)                    pass_order_cnt,\n" +
                "       nvl(b.审核通过人数, 0)                    pass_order_user,\n" +
                "       nvl(b.支付人数, 0)                      pay_cnt,\n" +
                "       nvl(b.发货订单, 0)                      sent_order_cnt,\n" +
                "       nvl(b.发货人数, 0)                      send_order_user,\n" +
                "       nvl(b.起租日发货订单, 0)                   rent_start_sent_order_cnt\n" +
                "from (\n" +
                "         select " + time + "                                                                   as count_day,\n" +
                "                shop_name,\n" +
                "                risk_level,\n" +
                "                (case when order_source is null then 4 else order_source end)                order_source,\n" +
                "                if(common_rent_flag not in (10, 30), '0', '1')                                    is_on_rent,\n" +
                "                count(distinct (customer_id))                                                  as 申请人数,\n" +
                "                count(distinct case when is_shunt_type = '可分流' then customer_id else null end) as 可分流人数\n" +
                "         from t1\n" +
                "         where effective_type = '有效订单'\n" +
                "           and create_time = " + time + "\n" +
                "         group by shop_name, risk_level, order_source, if(common_rent_flag not in (10, 30), '0', '1')\n" +
                "     ) a\n" +
                "         full join (\n" +
                "    select " + time + "                                                                                  as count_day,\n" +
                "           shop_name,\n" +
                "           risk_level,\n" +
                "           (case when order_source is null then 4 else order_source end)                                   order_source,\n" +
                "           if(common_rent_flag not in (10, 30), '0', '1')                                                   is_on_rent,\n" +
                "           count(distinct if(create_time = " + time + ", order_no, null))                           as 实际分流订单数,\n" +
                "           count(distinct if(create_time = " + time + ", customer_id, null))                           as 实际分流人数,\n" +
                "           count(distinct if(create_time = " + time + " and audit_time = " + time + ", order_no, null))  as 审核订单,\n" +
                "           count(distinct if(create_time = " + time + " and transfer_status = 5, order_no, null))  as 待审核订单,\n" +
                "           count(distinct if(create_time = " + time + " and audit_time = " + time + ", order_no, null))        as 当日审核当日通过,\n" +
                "           count(distinct if(audit_time = " + time + " and sh_audit_status = '审核通过', order_no, null))    as 审核通过订单,\n" +
                "           count(distinct if(audit_time = " + time + " and sh_audit_status = '审核通过', customer_id, null)) as 审核通过人数,\n" +
                "           count(distinct if(payment_time = " + time + " and order_status <> 30, customer_id, null))                            as 支付人数,\n" +
                "           count(distinct if(rent_start_date = " + time + " and send_type = '发货' and order_status <> 30, order_no, null))       as 起租日发货订单,\n" +
                "           count(distinct if(send_time = " + time + " and send_type = '发货' and order_status <> 30, order_no, null))             as 发货订单,\n" +
                "           count(distinct if(send_time = " + time + " and send_type = '发货' and order_status <> 30, customer_id, null))          as 发货人数\n" +
                "    from t2\n" +
                "    where effective_type = '有效订单'\n" +
                "    group by shop_name, risk_level, order_source, if(common_rent_flag not in (10, 30), '0', '1')\n" +
                ") b on a.risk_level = b.risk_level and a.order_source = b.order_source and a.shop_name = b.shop_name and\n" +
                "       a.is_on_rent = b.is_on_rent";
        String countSql = "select nvl(a.risk_level, b.risk_level)     risk_level,\n" +
                "       nvl(a.order_source, b.order_source) order_source,\n" +
                "       nvl(a.shop_name, b.shop_name)       shop_name,\n" +
                "       nvl(a.is_on_rent, b.is_on_rent)     is_on_rent,\n" +
                "       nvl(a.申请人数, 0)                      apply_user,\n" +
                "       nvl(a.可分流人数, 0)                     can_shunt_user,\n" +
                "       nvl(a.实际分流人数, 0)                    real_shunt_user,\n" +
                "       nvl(a.实际分流订单数, 0)                   real_shunt_order_cnt,\n" +
                "       nvl(b.审核订单, 0)                      audit_order_cnt,\n" +
                "       nvl(b.当日审核当日通过, 0)                  cur_audit_order_cnt,\n" +
                "       nvl(a.实际分流订单数, 0) - nvl(b.审核订单, 0)  wait_audit_order_cnt,\n" +
                "       nvl(b.审核通过订单, 0)                    pass_order_cnt,\n" +
                "       nvl(b.审核通过人数, 0)                    pass_order_user,\n" +
                "       nvl(b.支付人数, 0)                      pay_cnt,\n" +
                "       nvl(b.发货订单, 0)                      sent_order_cnt,\n" +
                "       nvl(b.发货人数, 0)                      send_order_user,\n" +
                "       nvl(b.起租日发货订单, 0)                   rent_start_sent_order_cnt\n" +
                "from (\n" +
                "         select " + time + "                                                                   as count_day,\n" +
                "                ''                                                                                shop_name,\n" +
                "                -1                                                                                risk_level,\n" +
                "                -1                                                                                order_source,\n" +
                "                -1                                                                                is_on_rent,\n" +
                "                count(distinct (customer_id))                                                  as 申请人数,\n" +
                "                count(distinct case when is_shunt_type = '可分流' then customer_id else null end) as 可分流人数,\n" +
                "                count(distinct case\n" +
                "                                   when is_shunt_type = '可分流' and\n" +
                "                                        (shunt_merchant_type = '自营分流' or shunt_merchant_type = '商户分流')\n" +
                "                                       then customer_id\n" +
                "                                   else null end)                                              as 实际分流人数,\n" +
                "                count(distinct case\n" +
                "                                   when is_shunt_type = '可分流' and\n" +
                "                                        (shunt_merchant_type = '自营分流' or shunt_merchant_type = '商户分流')\n" +
                "                                       then order_no\n" +
                "                                   else null end)                                              as 实际分流订单数\n" +
                "         from t1\n" +
                "         where effective_type = '有效订单'\n" +
                "           and create_time = " + time + "\n" +
                "     ) a\n" +
                "         full join (\n" +
                "    select " + time + "                                                                     as count_day,\n" +
                "           ''                                                                                  shop_name,\n" +
                "           -1                                                                                  risk_level,\n" +
                "           -1                                                                                  order_source,\n" +
                "           -1                                                                                  is_on_rent,\n" +
                "           count(distinct if(create_time = " + time + ", order_no, null))                    as `审核订单`,\n" +
                "           count(distinct if(create_time = " + time + " and audit_time = " + time + ", order_no, null))   as 当日审核当日通过,\n" +
                "           count(distinct if(audit_time = " + time + " and sh_audit_status = '审核通过', order_no,\n" +
                "                             null))                                                         as 审核通过订单,\n" +
                "           count(distinct if(audit_time = " + time + " and sh_audit_status = '审核通过', customer_id,\n" +
                "                             null))                                                         as 审核通过人数,\n" +
                "           count(distinct\n" +
                "                 if(payment_time = " + time + " and order_status <> 30, customer_id, null)) as 支付人数,\n" +
                "           count(distinct if(rent_start_date = " + time + " and send_type = '发货' and order_status <> 30, order_no,\n" +
                "                             null))                                                         as 起租日发货订单,\n" +
                "           count(distinct if(send_time = " + time + " and send_type = '发货' and order_status <> 30, order_no,\n" +
                "                             null))                                                         as 发货订单,\n" +
                "           count(distinct if(send_time = " + time + " and send_type = '发货' and order_status <> 30, customer_id,\n" +
                "                             null))                                                         as 发货人数\n" +
                "    from t2\n" +
                "    where effective_type = '有效订单'\n" +
                "      and audit_time is not null\n" +
                ") b on a.risk_level = b.risk_level and a.order_source = b.order_source and a.shop_name = b.shop_name and\n" +
                "       a.is_on_rent = b.is_on_rent";
        String detail = prefix + "," + t1 + "," + t2 + detailSql;
        String count = prefix + "," + t1 + "," + t2 + countSql;
        List<MerchantRealtimeBoardFlinkDO> countList = assemble(countDay, count);
        List<MerchantRealtimeBoardFlinkDO> detailList = assemble(countDay, detail);
        countList.addAll(detailList);
        if (CollUtil.isNotEmpty(countList)) {
            realtimeBoardFlinkService.removeDataByTime(countDay);
            realtimeBoardFlinkService.saveBatch(countList);
        }
    }

    private List<MerchantRealtimeBoardFlinkDO> assemble(LocalDateTime countDay, String sql) {
        List<Record> records = odpsUtil.querySql(sql + ";");
        List<MerchantRealtimeBoardFlinkDO> collect = records.stream().map(record -> {
            MerchantRealtimeBoardFlinkDO domain = new MerchantRealtimeBoardFlinkDO();
            domain.setCountDay(countDay);
            domain.setMerchantName(record.getString("shop_name"));
            domain.setApplyUser(Integer.valueOf(record.getString("apply_user")));
            domain.setCanShuntUser(Integer.valueOf(record.getString("can_shunt_user")));
            domain.setRealShuntUser(Integer.valueOf(record.getString("real_shunt_user")));
            domain.setRealShuntOrderCnt(Integer.valueOf(record.getString("real_shunt_order_cnt")));
            domain.setWaitAuditOrderCnt(Integer.valueOf(record.getString("wait_audit_order_cnt")));
            domain.setAuditOrderCnt(Integer.valueOf(record.getString("audit_order_cnt")));
            domain.setCurAuditOrderCnt(Integer.valueOf(record.getString("cur_audit_order_cnt")));
            domain.setPassOrderCnt(Integer.valueOf(record.getString("pass_order_cnt")));
            domain.setPassOrderUser(Integer.valueOf(record.getString("pass_order_user")));
            domain.setPayCnt(Integer.valueOf(record.getString("pay_cnt")));
            domain.setSentOrderCnt(Integer.valueOf(record.getString("sent_order_cnt")));
            domain.setSendOrderUser(Integer.valueOf(record.getString("send_order_user")));
            domain.setRentStartSentOrderCnt(Integer.valueOf(record.getString("rent_start_sent_order_cnt")));
            domain.setIsOnRent(Integer.valueOf(record.getString("is_on_rent")));
            domain.setRiskLevel(Integer.valueOf(record.getString("risk_level")));
            domain.setOrderSource(Integer.valueOf(record.getString("order_source")));
            return domain;
        }).collect(Collectors.toList());
        return collect;
    }

    private String stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "";
        }
        return val;
    }

}



