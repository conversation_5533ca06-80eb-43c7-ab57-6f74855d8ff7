package qnvip.data.overview.business.whole.report;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.report.WholeRentReportDO;
import qnvip.data.overview.service.whole.report.WholeRentReportService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeRentReportBusiness {
    
    private final OdpsUtil odpsUtil;
    private final WholeRentReportService wholeRentReportService;

    public void execData(String ds) {
        String countTime =
                LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).minusDays(1).format(WholeLawReportBusiness.YYYY_MM_DD);
        String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);
        if (StringUtils.isBlank(ds)) {
            ds = LocalDate.now().format(WholeLawReportBusiness.YYYYMMDD);
            countTime = LocalDate.now().minusDays(1).format(WholeLawReportBusiness.YYYY_MM_DD);
            time = LocalDate.now().format(WholeLawReportBusiness.YYYY_MM_DD);
        }
        countTime = "'" + countTime + "'";
        time = "'" + time + "'";
        String sql = "with dt as (" +
                "       select " + ds + " as ds" +
                "   )," +
                "        count_day as (" +
                "            select " + countTime + " as time" +
                "        )" +
                "   select a.time                           count_day," +
                "          nvl(a.createorder, 0)            order_cnt," +
                "          nvl(a.createuser, 0)             order_uv," +
                "          nvl(a.audituser, 0)              audit_uv," +
                "          nvl(a.payorder, 0)               pay_uv," +
                "          nvl(a.sendorder, 0)              send_uv," +
                "          nvl(a.signorder, 0)              sign_uv," +
                "          nvl(a.insuorder1, 0)             screen_amt," +
                "          nvl(a.comptclose, 0)             os_closed," +
                "          nvl(a.userclose, 0)              user_closed," +
                "          nvl(a.cossuser, 0)               pay_closed," +
                "          nvl(a.总融, 0)                     send_finance_amt," +
                "          nvl(a.采购价, 0)                    purchase_price," +
                "          nvl(a.分流订单, 0)                   merchant_cnt," +
                "          nvl(a.分流接单量, 0)                  merchant_receive," +
                "          nvl(a.分流支付订单, 0)                 merchant_pay," +
                "          nvl(a.分流发货, 0)                   merchant_send," +
                "          nvl(a.分流签收, 0)                   merchant_sign," +
                "          nvl(a.机审, 0)                     machine_audit," +
                "          nvl(a.48小时发货量, 0)                hour48_send_cnt," +
                "          nvl(a.电销后支付, 0)                  call_pay," +
                "          nvl(a.payorder, 0)-nvl(a.电销后支付, 0) before_call_pay," +
                "          nvl(b.应还订单量, 0)                  repay_cnt," +
                "          nvl(b.未逾期订单, 0)                  un_overdue_cnt," +
                "          nvl(b.已逾期订单, 0)                  overdue_cnt," +
                "          nvl(b.全量应还总额1, 0)                whole_repay_amt," +
                "          nvl(b.未逾期应还总额, 0)                un_overdue_repay_amt," +
                "          nvl(b.未逾期实还总额2, 0)               un_overdue_real_pay_amt," +
                "          nvl(b.已逾期应还总额, 0)                overdue_repay_amt," +
                "          nvl(b.已逾期实还总额, 0)                overdue_real_pay_amt," +
                "          nvl(b.入催订单, 0)                   collection_cnt," +
                "          nvl(b.首逾, 0)                     first_overdue," +
                "          nvl(b.首期订单, 0)                   first_cnt," +
                "          nvl(b.售前金额, 0)                   before_amt," +
                "          nvl(b.全量应还总额xu1, 0)              renew_whole_repay_amt," +
                "          nvl(b.未逾期实还总额xu2, 0)             renew_un_overdue_real_pay_amt," +
                "          nvl(b.应还订单量xu, 0)                renew_repay_cnt," +
                "          nvl(b.未逾期订单xu, 0)                renew_un_overdue_cnt," +
                "          nvl(b.首逾xu, 0)                   renew_first_overdue," +
                "          nvl(b.首期订单xu, 0)                 renew_first_cnt," +
                "          nvl(b.售前金额xu, 0)                 renew_before_amt," +
                "          nvl(c.phone14_finance_amt, 0)    phone14_finance_amt," +
                "          nvl(c.phone14_purchase_price, 0) phone14_purchase_price," +
                "          nvl(c.phone14_cnt, 0)            phone14_cnt," +
                "          nvl(d.应买断订单, 0)                  buyout_cnt," +
                "          nvl(d.当日买断, 0)                   now_buyout_uv," +
                "          nvl(d.7日买断, 0)                   day_buyout_cnt," +
                "          nvl(d.28日买断, 0)                  month_buyout_cnt," +
                "          nvl(d.续租订单, 0)                   renew_cnt," +
                "          nvl(d.续租订单_上周, 0)                week_renew_cnt," +
                "          nvl(d.归还订单, 0)                   return_cnt," +
                "          nvl(d.归还订单_上周, 0)                week_return_cnt," +
                "          nvl(d.当日总买断, 0)                  now_buyout_cnt," +
                "          nvl(e.merchant_new_add_month, 0) merchant_new_add_month," +
                "          nvl(e.merchant_new_add, 0)       merchant_new_add," +
                "          nvl(e.merchant_dynamic, 0)       merchant_dynamic," +
                "          nvl(e.merchant_dynamic_month, 0) merchant_dynamic_month," +
                "          nvl(e.merchant_cnt, 0)           merchant_total" +
                "   from (" +
                "            select a.time       time," +
                "                   a.createorder," +
                "                   a.createuser," +
                "                   b.audituser," +
                "                   c.payorder," +
                "                   d.sendorder," +
                "                   e.signorder," +
                "                   h.insuorder1," +
                "                   k.comptclose," +
                "                   k.userclose," +
                "                   m.cossuser," +
                "                   n.总融," +
                "                   n.采购价," +
                "                   a.分流订单," +
                "                   a.分流接单量," +
                "                   a.分流支付订单," +
                "                   d.分流发货," +
                "                   e.分流签收," +
                "                   b.audituser1 机审," +
                "                   d.48小时发货量," +
                "                   c.电销后支付" +
                "            from (select date(ro.create_time)                                                                 time," +
                "                         count(distinct ro.id)                                                                createorder," +
                "                         count(distinct rc.id_card_no)                                                        createuser," +
                "                         count(distinct if(ro.merchant_id not in(100,10000107) or ro.merchant_transfer = 15," +
                "   ro.id, null))  分流订单," +
                "                         count(distinct if(ro.merchant_transfer = 10 and ro.merchant_id not in(100,10000107), ro.id, null)) 分流接单量," +
                "                         count(distinct" +
                "                               if(ro.merchant_transfer = 10 and ro.merchant_id not in(100,10000107) and ro.payment_time is not null," +
                "                                  ro.id," +
                "                                  null))                                                                      分流支付订单" +
                "                  from rent_order ro" +
                "                           left join rent_customer rc on ro.customer_id = rc.id" +
                "                  where ro.is_deleted = 0" +
                "                    and ro.type = 1" +
                "                    and ro.parent_id = 0" +
                "                    and ro.ds = (select ds from dt)" +
                "                    and rc.ds = (select ds from dt)" +
                "                    and date(ro.create_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                  group by date(ro.create_time)) a" +
                "                     left join (select date(ro.create_time)                                                   time," +
                "                                       count(distinct if(roa.type = 2, rc.id_card_no, null))                  audituser1," +
                "                                       (count(distinct if(roi.risk_auth_status = 5, rc.id_card_no, null))) as audituser" +
                "                                from rent_order_audit roa" +
                "                                         left join rent_order ro on roa.order_id = ro.id" +
                "                                         left join rent_customer rc on ro.customer_id = rc.id" +
                "                                         left join rent_order_infomore roi on roi.order_id = ro.id" +
                "                                where ro.is_deleted = 0" +
                "                                  and roa.audit_status = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and rc.ds = (select ds from dt)" +
                "                                  and roa.ds = (select ds from dt)" +
                "                                  and roi.ds = (select ds from dt)" +
                "                                  and date(ro.create_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(ro.create_time)) b" +
                "                               on a.time = b.time" +
                "                     left join (select date(ro.payment_time)                                         time," +
                "                                       count(distinct if(ro.merchant_id  in(100,10000107), rc.id_card_no, null)) payorder" +
                "                                        ," +
                "                                       count(distinct rc.id_card_no)," +
                "                                       count(distinct" +
                "                                             if(rol.type = 10 AND (ROL.mobile_service_operate_time < ro.payment_time OR" +
                "                                                                   service_operate_time < ro.payment_time), rc.id_card_no," +
                "                                                null))                                               电销后支付" +
                "                                from rent_order ro" +
                "                                         left join rent_customer rc on ro.customer_id = rc.id" +
                "                                         left join rent_service_allot rol on ro.id = rol.order_id" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.merchant_id  in(100,10000107)" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and rc.ds = (select ds from dt)" +
                "                                  and rol.ds = (select ds from dt)" +
                "                                  and date(ro.payment_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(ro.payment_time)) c on a.time = c.time" +
                "                     left join (select date(rol.send_time)                                    time," +
                "                                       count(distinct if(ro.merchant_id  in(100,10000107), ro.id, null))  sendorder," +
                "                                       count(distinct if(ro.merchant_id not in(100,10000107), ro.id, null)) 分流发货," +
                "                                       count(distinct" +
                "                                             if(datediff(rol.send_time, ro.payment_time, 'hh') <= 48 AND" +
                "                                                ro.merchant_id in(100,10000107)," +
                "                                                rc.id_card_no, null))                         48小时发货量" +
                "                                from rent_order ro" +
                "                                         left join rent_order_logistics rol on ro.id = rol.order_id" +
                "                                         left join rent_customer rc on ro.customer_id = rc.id" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and rol.ds = (select ds from dt)" +
                "                                  and rc.ds = (select ds from dt)" +
                "                                  and date(rol.send_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(rol.send_time)) d on a.time = d.time" +
                "                     left join (select date(rol.sign_time)                                    time," +
                "                                       count(distinct if(ro.merchant_id  in(100,10000107), ro.id, null))  signorder," +
                "                                       count(distinct if(ro.merchant_id not in(100,10000107), ro.id, null)) 分流签收" +
                "                                from rent_order ro" +
                "                                         left join rent_order_logistics rol on ro.id = rol.order_id" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and rol.ds = (select ds from dt)" +
                "                                  and date(rol.sign_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(rol.sign_time)) e on a.time = e.time" +
                "                     left join (select date(ro.payment_time)                             time," +
                "                                       sum(if(roi.is_deleted = 0, roi.insurance_amt, 0)) insuorder1" +
                "                                from rent_order ro" +
                "                                         left join rent_order_insurance roi on ro.id = roi.order_id" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.status not in (20, 30)" +
                "                                  and roi.type is not null" +
                "                                  and roi.giving_flag = 0" +
                "                                  and ro.termination = 1" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and roi.ds = (select ds from dt)" +
                "                                  and date(ro.payment_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(ro.payment_time)) h on a.time = h.time" +
                "                     left join (select date(roi.termination_time)                                      time," +
                "                                       count(distinct if(roi.termination_type = 1, roi.order_id, null)) -" +
                "                                       count(distinct if(roa.biz_desc = '用户取消订单', roi.order_id, null)) comptclose," +
                "                                       count(distinct if(roa.biz_desc = '用户取消订单', roi.order_id, null)) userclose" +
                "                                from rent_order_infomore roi" +
                "                                         left join rent_order ro on ro.id = roi.order_id" +
                "                                         left join rent_order_audit roa on roi.order_id = roa.order_id" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.merchant_id  in(100,10000107)" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.termination = 5" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and roi.ds = (select ds from dt)" +
                "                                  and roa.ds = (select ds from dt)" +
                "                                  and date(roi.termination_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(roi.termination_time)) k on a.time = k.time" +
                "                     left join (select date(ro.payment_time)                                        time," +
                "                                       count(distinct ro.customer_id) -" +
                "                                       count(distinct if(ro.termination = 1, ro.customer_id, null)) cossuser" +
                "                                from rent_order ro" +
                "                                where ro.is_deleted = 0" +
                "                                  and ro.type = 1" +
                "                                  and ro.parent_id = 0" +
                "                                  and ro.merchant_id  in(100,10000107)" +
                "                                  and ro.ds = (select ds from dt)" +
                "                                  and date(ro.payment_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                group by date(ro.payment_time)) m on a.time = m.time" +
                "                     left join (select a.time, sum(a.总融) 总融, sum(a.采购价) 采购价" +
                "                                from (select date(rol.send_time)           time," +
                "                                             ro.customer_id," +
                "                                             min(rofd.total_financing_amt) 总融," +
                "                                             min(operating_purchase_price) 采购价" +
                "                                      from rent_order ro" +
                "                                               left join rent_order_audit roa on ro.id = roa.order_id" +
                "                                               left join rent_order_finance_detail rofd on ro.id = rofd.order_id" +
                "                                               left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "                                               left join rent_order_logistics rol on ro.id = rol.order_id" +
                "                                               left join rent_order_item r on ro.id = r.order_id and r.item_type = 1" +
                "                                      where date(rol.send_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                        and ro.merchant_id  in(100,10000107)" +
                "                                        and ro.parent_id = 0" +
                "                                        and ro.type = 1" +
                "                                        and ro.is_deleted = 0" +
                "                                        and ro.ds = (select ds from dt)" +
                "                                        and roa.ds = (select ds from dt)" +
                "                                        and rofd.ds = (select ds from dt)" +
                "                                        and rol.ds = (select ds from dt)" +
                "                                        and r.ds = (select ds from dt)" +
                "                                      group by date(rol.send_time), ro.customer_id) a" +
                "                                group by a.time" +
                "            ) n on a.time = n.time) a" +
                "            left join (" +
                "       select date(repay_date)                                                                             time," +
                "              count(distinct if(ro.parent_id = 0, rorp.order_id, null))                                    应还订单量," +
                "              count(distinct if(overdue = 1 and ro.parent_id = 0, rorp.order_id, null))                    未逾期订单," +
                "              count(distinct if(overdue = 5, rorp.order_id, null))                                         已逾期订单," +
                "              sum(if(ro.parent_id = 0, capital, 0))                                                        全量应还总额1," +
                "              sum(if(overdue = 1, capital, 0))                                                             未逾期应还总额," +
                "              sum(if(overdue = 1 and ro.parent_id = 0, real_repay_capital, 0))                             未逾期实还总额2," +
                "              sum(if(overdue = 5, capital, 0))                                                             已逾期应还总额," +
                "              sum(if(overdue = 5, real_repay_capital, 0))                                                  已逾期实还总额," +
                "              count(distinct" +
                "                    if(overdue = 5 and" +
                "                       (((date(repay_date)) + 1 < date(real_repay_time)) or (date(real_repay_time) is null))," +
                "                       rorp.order_id, null))                                                               入催订单," +
                "              count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))  首逾," +
                "              count(distinct if(rorp.term = 1 and ro.parent_id = 0, rorp.order_id, null))                  首期订单," +
                "              sum(if(use_status = 1 and ro.parent_id = 0, discount_amount, 0))                             售前金额," +
                "              sum(if(ro.parent_id != 0, capital, 0))                                                       全量应还总额xu1," +
                "              sum(if(overdue = 1 and ro.parent_id != 0, real_repay_capital, 0))                            未逾期实还总额xu2," +
                "              count(distinct if(ro.parent_id != 0, rorp.order_id, null))                                   应还订单量xu," +
                "              count(distinct if(overdue = 1 and ro.parent_id != 0, rorp.order_id, null))                   未逾期订单xu," +
                "              count(distinct if(overdue = 5 and rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null)) 首逾xu," +
                "              count(distinct if(rorp.term = 1 and ro.parent_id != 0, rorp.order_id, null))                 首期订单xu," +
                "              sum(if(use_status = 1 and ro.parent_id != 0, discount_amount, 0))                            售前金额xu" +
                "   " +
                "       from rent_order_repayment_plan as rorp" +
                "                left join rent_order ro" +
                "                          on ro.id = rorp.order_id" +
                "                left join rent_customer_coupon rcc on rcc.order_id = rorp.order_id" +
                "       where date(repay_date) between date_sub(" + time + "" +
                "           , 180)" +
                "           and date_sub(" + time + "" +
                "               , 1)" +
                "         and ro.is_deleted = 0" +
                "         and ro.termination = 1" +
                "         and ro.type = 1" +
                "         and ro.ds = (select ds from dt)" +
                "         and rorp.ds = (select ds from dt)" +
                "         and rcc.ds = (select ds from dt)" +
                "       group by time" +
                "       order by time" +
                "   ) b on a.time = b.time" +
                "            left join" +
                "        (" +
                "            select a.time, sum(a.总融) phone14_finance_amt, sum(a.采购价) phone14_purchase_price, count(distinct id) phone14_cnt" +
                "            from (select date(rol.send_time)           time," +
                "                         ro.id," +
                "                         min(rofd.total_financing_amt) 总融," +
                "                         min(actual_supply_price) 采购价" +
                "                  from rent_order ro" +
                "                           left join rent_order_audit roa" +
                "                                     on ro.id = roa.order_id" +
                "                           left join rent_order_finance_detail rofd on ro.id = rofd.order_id" +
                "                           left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "                           left join rent_order_logistics rol on ro.id = rol.order_id" +
                "                           left join rent_order_item r on ro.id = r.order_id" +
                "                  where date(rol.send_time) between date_sub(" + time + ", 180)" +
                "                      and date_sub(" + time + ", 1)" +
                "                    and ro.merchant_id  in(100,10000107)" +
                "                    and ro.parent_id = 0" +
                "                    and ro.type = 1" +
                "                    and ro.is_deleted = 0" +
                "                    and r.item_type = 1" +
                "                    and ro.termination = 1" +
                "                    and ro.ds = (select ds from dt)" +
                "                    and roa.ds = (select ds from dt)" +
                "                    and rofd.ds = (select ds from dt)" +
                "                    and rol.ds = (select ds from dt)" +
                "                    and r.ds = (select ds from dt)" +
                "                    and lower(r.short_name) like '%iphone14promax%'" +
                "                    and r.size = '256G'" +
                "                  group by date(rol.send_time), ro.id) a" +
                "            group by a.time" +
                "        ) c on a.time = c.time" +
                "            left join (" +
                "       select a.time," +
                "              a.应买断订单," +
                "              a.当日买断," +
                "              b.`7日买断`," +
                "              b.`28日买断`," +
                "              a.续租订单," +
                "              a.续租订单_上周," +
                "              a.归还订单," +
                "              a.归还订单_上周," +
                "              b.当日总买断" +
                "       from (select date(rent_end_date)                                                                time," +
                "                    count(distinct ro.id)                                                              应买断订单," +
                "                    count(distinct if(date(rent_end_date) >= date(roi.buyout_time), ro.customer_id, null)) 当日买断," +
                "                    count(distinct if(ro.status in (320, 310), ro.customer_id, null))                  续租订单," +
                "                    count(distinct" +
                "                          if(ro.status in (320, 310) and date(renew_time) <= date_sub(" + time + ", 31), ro.customer_id," +
                "                             null))                                                                    续租订单_上周," +
                "                    count(distinct if(ro.status in (200, 210), ro.customer_id, null))                  归还订单," +
                "                    count(distinct" +
                "                          if(ro.status in (200, 210) and date(return_time) <= date_sub(" + time + ", 31), ro.customer_id," +
                "                             null))                                                                    归还订单_上周" +
                "             from rent_order ro" +
                "                      left join rent_order_infomore roi on ro.id = roi.order_id" +
                "             where ro.is_deleted = 0" +
                "               and ro.termination = 1" +
                "               and ro.type = 1" +
                "               and ro.parent_id = 0" +
                "               and ro.ds = (select ds from dt)" +
                "               and roi.ds = (select ds from dt)" +
                "               and date(rent_end_date) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "             group by time" +
                "             order by time) a" +
                "                left join (select buytime," +
                "                                  count(distinct id)              当日总买断," +
                "                                  count(if(time2 < 8, id, null))  7日买断," +
                "                                  count(if(time2 < 29, id, null)) 28日买断" +
                "                           from (select ro.id," +
                "                                        date(rent_end_date)," +
                "                                        date(roi.buyout_time)                          buytime," +
                "                                        datediff(roi.buyout_time, rent_end_date, 'dd') time2" +
                "                                 from rent_order ro" +
                "                                          left join rent_order_infomore roi on ro.id = roi.order_id" +
                "                                 where ro.is_deleted = 0" +
                "                                   and ro.termination = 1" +
                "                                   and ro.type = 1" +
                "                                   and ro.parent_id = 0" +
                "                                   and date(roi.buyout_time) between date_sub(" + time + ", 180) and date_sub(" + time + ", 1)" +
                "                                   and ro.ds = (select ds from dt)" +
                "                                   and roi.ds = (select ds from dt)" +
                "                                 group by ro.id, rent_end_date, roi.buyout_time" +
                "                                 order by ro.id) a" +
                "                           group by buytime" +
                "       ) b on a.time = b.buytime" +
                "   ) d on a.time = d.time" +
                "            left join (" +
                "       select x.time," +
                "              x.merchant_new_add_month," +
                "              x.merchant_new_add," +
                "              x.merchant_cnt," +
                "              y.merchant_dynamic," +
                "              z.merchant_dynamic_month" +
                "       from (select count(distinct if(date(rm.create_time) between date_sub(" + time + ", 31) and date_sub(" + time + ", 1), id," +
                "                                      null))                                                     merchant_new_add_month," +
                "                    count(distinct if(date(rm.create_time) between date_sub(" + time + ", 7) and date_sub(" + time + ", 1), id," +
                "                                      null))                                                     merchant_new_add," +
                "                    count(distinct if(date(rm.create_time) <= date_sub(" + time + ", 1), id, null)) merchant_cnt," +
                "                    (select time from count_day)                                                 time" +
                "             from rent_merchant rm" +
                "             where is_deleted = 0" +
                "               and rm.sale_type = 2" +
                "               and rm.ds = (select ds from dt)) x" +
                "                left join (" +
                "           select count(distinct a.id)         merchant_dynamic," +
                "                  (select time from count_day) time" +
                "           from (" +
                "                    select date(ro.create_time)                                       time," +
                "                           rm.id," +
                "                           count(distinct if(ro.merchant_transfer = 10, ro.id, null)) a" +
                "                    from rent_merchant rm" +
                "                             left join rent_order ro" +
                "                                       on ro.merchant_id = rm.id" +
                "                    where rm.is_deleted = 0" +
                "                      and ro.merchant_id not in(100,10000107)" +
                "                      and ro.parent_id = 0" +
                "                      and ro.ds = (select ds from dt)" +
                "                      and rm.ds = (select ds from dt)" +
                "                      and date(ro.create_time) between date_sub(" + time + ", 7) and date_sub(" + time + ", 1)" +
                "                    group by time, rm.id) a" +
                "           where a.a > 0" +
                "       ) y on x.time = y.time" +
                "                left join" +
                "            (" +
                "                select count(distinct a.id)         merchant_dynamic_month," +
                "                       (select time from count_day) time" +
                "                from (" +
                "                         select date(ro.create_time)                                       time," +
                "                                rm.id," +
                "                                count(distinct if(ro.merchant_transfer = 10, ro.id, null)) a" +
                "                         from rent_merchant rm" +
                "                                  left join rent_order ro" +
                "                                            on ro.merchant_id = rm.id" +
                "                         where rm.is_deleted = 0" +
                "                           and ro.merchant_id not in(100,10000107)" +
                "                           and ro.parent_id = 0" +
                "                           and ro.ds = (select ds from dt)" +
                "                           and rm.ds = (select ds from dt)" +
                "                           and date(ro.create_time) between date_sub(" + time + ", 31) and date_sub(" + time + ", 1)" +
                "                         group by time, rm.id) a" +
                "                where a.a > 0" +
                "            ) z on x.time = z.time" +
                "   ) e on e.time = a.time" +
                "   ;";
        List<Record> records = odpsUtil.querySql(sql);
        String finalDs = ds;
        List<WholeRentReportDO> collect = records.stream().map((record) -> {
            LocalDate countDay = LocalDate.parse(record.getString("count_day"), WholeLawReportBusiness.YYYY_MM_DD);
            WholeRentReportDO wholeRentReportDO = new WholeRentReportDO();
            wholeRentReportDO.setCountDay(countDay);
            wholeRentReportDO.setDs(finalDs);
            wholeRentReportDO.setOrderCnt(Integer.valueOf(record.getString("order_cnt")));
            wholeRentReportDO.setOrderUv(Integer.valueOf(record.getString("order_uv")));
            wholeRentReportDO.setAuditUv(Integer.valueOf(record.getString("audit_uv")));
            wholeRentReportDO.setPayUv(Integer.valueOf(record.getString("pay_uv")));
            wholeRentReportDO.setSendUv(Integer.valueOf(record.getString("send_uv")));
            wholeRentReportDO.setSignUv(Integer.valueOf(record.getString("sign_uv")));
            wholeRentReportDO.setScreenAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("screen_amt"))));
            wholeRentReportDO.setOsClosed(Integer.valueOf(record.getString("os_closed")));
            wholeRentReportDO.setUserClosed(Integer.valueOf(record.getString("user_closed")));
            wholeRentReportDO.setPayClosed(Integer.valueOf(record.getString("pay_closed")));
            wholeRentReportDO.setSendFinanceAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("send_finance_amt"))));
            wholeRentReportDO.setPurchasePrice(BigDecimal.valueOf(Double.parseDouble(record.getString("purchase_price"))));
            wholeRentReportDO.setMerchantCnt(Integer.valueOf(record.getString("merchant_cnt")));
            wholeRentReportDO.setMerchantReceive(Integer.valueOf(record.getString("merchant_receive")));
            wholeRentReportDO.setMerchantPay(Integer.valueOf(record.getString("merchant_pay")));
            wholeRentReportDO.setMerchantSend(Integer.valueOf(record.getString("merchant_send")));
            wholeRentReportDO.setMerchantSign(Integer.valueOf(record.getString("merchant_sign")));
            wholeRentReportDO.setMachineAudit(Integer.valueOf(record.getString("machine_audit")));
            wholeRentReportDO.setHour48SendCnt(Integer.valueOf(record.getString("hour48_send_cnt")));
            wholeRentReportDO.setAfterCallPay(Integer.valueOf(record.getString("call_pay")));
            wholeRentReportDO.setRepayCnt(Integer.valueOf(record.getString("repay_cnt")));
            wholeRentReportDO.setUnOverdueCnt(Integer.valueOf(record.getString("un_overdue_cnt")));
            wholeRentReportDO.setOverdueCnt(Integer.valueOf(record.getString("overdue_cnt")));
            wholeRentReportDO.setBeforeCallPay(Integer.valueOf(record.getString("before_call_pay")));
            wholeRentReportDO.setWholeRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("whole_repay_amt"))));
            wholeRentReportDO.setUnOverdueRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "un_overdue_repay_amt"))));
            wholeRentReportDO.setUnOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "un_overdue_real_pay_amt"))));
            wholeRentReportDO.setOverdueRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "overdue_repay_amt"))));
            wholeRentReportDO.setOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "overdue_real_pay_amt"))));
            wholeRentReportDO.setCollectionCnt(Integer.valueOf(record.getString("collection_cnt")));
            wholeRentReportDO.setFirstOverdue(Integer.valueOf(record.getString("first_overdue")));
            wholeRentReportDO.setFirstCnt(Integer.valueOf(record.getString("first_cnt")));
            wholeRentReportDO.setBeforeAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("before_amt"))));
            wholeRentReportDO.setRenewWholeRepayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "renew_whole_repay_amt"))));
            wholeRentReportDO.setRenewUnOverdueRealPayAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "renew_un_overdue_real_pay_amt"))));
            wholeRentReportDO.setRenewRepayCnt(Integer.valueOf(record.getString("renew_repay_cnt")));
            wholeRentReportDO.setRenewUnOverdueCnt(Integer.valueOf(record.getString("renew_un_overdue_cnt")));
            wholeRentReportDO.setRenewFirstOverdue(Integer.valueOf(record.getString("renew_first_overdue")));
            wholeRentReportDO.setRenewFirstCnt(Integer.valueOf(record.getString("renew_first_cnt")));
            wholeRentReportDO.setRenewBeforeAmt(BigDecimal.valueOf(Double.parseDouble(record.getString("renew_before_amt"))));
            wholeRentReportDO.setPhone14FinanceAmt(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "phone14_finance_amt"))));
            wholeRentReportDO.setPhone14PurchasePrice(BigDecimal.valueOf(Double.parseDouble(record.getString(
                    "phone14_purchase_price"))));
            wholeRentReportDO.setPhone14Cnt(Integer.valueOf(record.getString("phone14_cnt")));
            wholeRentReportDO.setBuyoutCnt(Integer.valueOf(record.getString("buyout_cnt")));
            wholeRentReportDO.setNowBuyoutUv(Integer.valueOf(record.getString("now_buyout_uv")));
            wholeRentReportDO.setDayBuyoutCnt(Integer.valueOf(record.getString("day_buyout_cnt")));
            wholeRentReportDO.setMonthBuyoutCnt(Integer.valueOf(record.getString("month_buyout_cnt")));
            wholeRentReportDO.setRenewCnt(Integer.valueOf(record.getString("renew_cnt")));
            wholeRentReportDO.setWeekRenewCnt(Integer.valueOf(record.getString("week_renew_cnt")));
            wholeRentReportDO.setReturnCnt(Integer.valueOf(record.getString("return_cnt")));
            wholeRentReportDO.setWeekReturnCnt(Integer.valueOf(record.getString("week_return_cnt")));
            wholeRentReportDO.setNowBuyoutCnt(Integer.valueOf(record.getString("now_buyout_cnt")));
            wholeRentReportDO.setMerchantDynamic(Integer.valueOf(record.getString("merchant_dynamic")));
            wholeRentReportDO.setMerchantNewAdd(Integer.valueOf(record.getString("merchant_new_add")));
            wholeRentReportDO.setMerchantTotal(Integer.valueOf(record.getString("merchant_total")));
            wholeRentReportDO.setMerchantDynamicMonth(Integer.valueOf(record.getString("merchant_dynamic_month")));
            wholeRentReportDO.setMerchantNewAddMonth(Integer.valueOf(record.getString("merchant_new_add_month")));
            return wholeRentReportDO;
        }).collect(Collectors.toList());
        wholeRentReportService.removeDataByDs(ds);
        wholeRentReportService.saveBatch(collect);
    }


}



