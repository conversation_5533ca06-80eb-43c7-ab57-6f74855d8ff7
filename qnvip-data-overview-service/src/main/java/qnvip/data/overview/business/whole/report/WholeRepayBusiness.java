package qnvip.data.overview.business.whole.report;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeRepayDO;
import qnvip.data.overview.service.whole.report.WholeRepayService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.ThreadPoolUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

import static qnvip.data.overview.business.whole.report.WholeLawReportBusiness.YYYY_MM;
import static qnvip.data.overview.business.whole.report.WholeLawReportBusiness.YYYY_MM_DD;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeRepayBusiness {

    private final OdpsUtil odpsUtil;
    private final WholeRepayService wholeRepayService;
    private final Integer PAGE_SIZE = 10000;
    ;
    private final Integer RENT = 0;
    private final Integer MERCHANT = 1;
    public static final Integer CNT = 0;
    public static final Integer AMT = 1;
    public static final ThreadPoolUtils repayBusiness = ThreadPoolUtils.getInstance("WholeRepayBusiness");

    @Deprecated
    public void execJob() {
        wholeRepayService.deleteAll();
        wholeRepayService.removeMongoDO();
        repayBusiness.execute(execRentCntData());
        repayBusiness.execute(execRentAmtData());
        repayBusiness.execute(execMerchantCntData());
        repayBusiness.execute(execMerchantAmtData());
        List<CompletableFuture<Void>> futureList = repayBusiness.getCompletableFutures();
        CompletableFuture.allOf(futureList.toArray(new CompletableFuture[0])).join();
    }

    /**
     * 租赁回款订单情况
     */
    private Runnable execRentCntData() {
        return () -> {
            String sql = "   select date (repay_date) count_day," +
                    "       ro.mini_type," +
                    "       if(parent_id = 0, 0, 1) order_type," +
                    "       platform_code platform," +
                    "       if(nvl(cl.quotientname, '') = '', '未标记上导流商', cl.quotientname) quotient_name," +
                    "       count (distinct order_id) repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 0, order_id, null)) ahead," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 1, order_id, null)) cur," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 2, order_id, null)) t1_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 3, order_id, null)) t2_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 4, order_id, null)) t3_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 5, order_id, null)) t4_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 6, order_id, null)) t5_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 7, order_id, null)) t6_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 8, order_id, null)) t7_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 9, order_id, null)) t8_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 10, order_id, null)) t9_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 11, order_id, null)) t10_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 12, order_id, null)) t11_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 13, order_id, null)) t12_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 14, order_id, null)) t13_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 15, order_id, null)) t14_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 16, order_id, null)) t15_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 17, order_id, null)) t16_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 18, order_id, null)) t17_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 19, order_id, null)) t18_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 20, order_id, null)) t19_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 21, order_id, null)) t20_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 22, order_id, null)) t21_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 23, order_id, null)) t22_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 24, order_id, null)) t23_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 25, order_id, null)) t24_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 26, order_id, null)) t25_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 27, order_id, null)) t26_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 28, order_id, null)) t27_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 29, order_id, null)) t28_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 30, order_id, null)) t29_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 31, order_id, null)) t30_repay," +
                    "       count (distinct if(datediff(real_repay_time, repay_date, 'dd') < 60, order_id, null)) m2_repay" +
                    "   from rent_order_repayment_plan as rorp" +
                    "       left join rent_order ro" +
                    "   on ro.id = rorp.order_id and ro.ds = (select ds from dt) and ro.is_deleted = 0" +
                    "       and ro.termination = 1" +
                    "       and ro.type = 1" +
                    "       inner join cl_loan cl on cl.loanno = ro.no and cl.ds = (select ds from dt)" +
                    "       inner join rent_mini_config rmc on rmc.mini_type=ro.mini_type" +
                    "   where date (repay_date) >= '2021-01-01'" +
                    "     and date (repay_date) <= date(lastday(getdate()))" +
                    "     and rorp.is_deleted = 0" +
                    "     and rorp.ds = (select ds from dt)" +
                    "   group by count_day, ro.mini_type, if(parent_id = 0, 0, 1), cl.quotientname,  platform_code" +
                    "   order by count_day, ro.mini_type, if(parent_id = 0, 0, 1), cl.quotientname, platform_code ";
            getRecords(sql, RENT, CNT);
        };
    }

    /**
     * 租赁回款金额情况
     */
    private Runnable execRentAmtData() {
        return () -> {
            String sql = "select date(repay_date)                                                                            count_day," +
                    "          ro.mini_type," +
                    "          if(parent_id = 0, 0, 1)                                                                     order_type," +
                    "          platform_code                                                                               platform," +
                    "          if(nvl(cl.quotientname, '') = '', '未标记上导流商', cl.quotientname)                               quotient_name," +
                    "          nvl(sum(capital), 0)                                                                        repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 0, real_repay_capital, null)), 0)  ahead," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 1, real_repay_capital, null)), 0)  cur," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 2, real_repay_capital, null)), 0)  t1_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 3, real_repay_capital, null)), 0)  t2_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 4, real_repay_capital, null)), 0)  t3_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 5, real_repay_capital, null)), 0)  t4_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 6, real_repay_capital, null)), 0)  t5_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 7, real_repay_capital, null)), 0)  t6_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 8, real_repay_capital, null)), 0)  t7_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 9, real_repay_capital, null)), 0)  t8_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 10, real_repay_capital, null)), 0) t9_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 11, real_repay_capital, null)), 0) t10_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 12, real_repay_capital, null)), 0) t11_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 13, real_repay_capital, null)), 0) t12_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 14, real_repay_capital, null)), 0) t13_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 15, real_repay_capital, null)), 0) t14_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 16, real_repay_capital, null)), 0) t15_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 17, real_repay_capital, null)), 0) t16_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 18, real_repay_capital, null)), 0) t17_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 19, real_repay_capital, null)), 0) t18_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 20, real_repay_capital, null)), 0) t19_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 21, real_repay_capital, null)), 0) t20_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 22, real_repay_capital, null)), 0) t21_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 23, real_repay_capital, null)), 0) t22_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 24, real_repay_capital, null)), 0) t23_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 25, real_repay_capital, null)), 0) t24_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 26, real_repay_capital, null)), 0) t25_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 27, real_repay_capital, null)), 0) t26_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 28, real_repay_capital, null)), 0) t27_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 29, real_repay_capital, null)), 0) t28_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 30, real_repay_capital, null)), 0) t29_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 31, real_repay_capital, null)), 0) t30_repay," +
                    "          nvl(sum(if(datediff(real_repay_time, repay_date, 'dd') < 60, real_repay_capital, null)), 0)  m2_repay" +
                    "   from rent_order_repayment_plan as rorp" +
                    "            left join rent_order ro" +
                    "                      on ro.id = rorp.order_id and ro.ds = (select ds from dt) and ro.is_deleted = 0" +
                    "                          and ro.termination = 1" +
                    "                          and ro.type = 1" +
                    "            inner join cl_loan cl on cl.loanno = ro.no and cl.ds = (select ds from dt)" +
                    "            inner join rent_mini_config rmc on rmc.mini_type = ro.mini_type" +
                    "   where date(repay_date) >= '2021-01-01'" +
                    "     and date (repay_date) <= date(lastday(getdate()))" +
                    "     and rorp.is_deleted = 0" +
                    "     and rorp.ds = (select ds from dt)" +
                    "   group by count_day, ro.mini_type, if(parent_id = 0, 0, 1), cl.quotientname, " +
                    "            platform_code" +
                    "   order by count_day, ro.mini_type, if(parent_id = 0, 0, 1), cl.quotientname, " +
                    "            platform_code ";
            getRecords(sql, RENT, AMT);
        };
    }

    /**
     * 分期回款订单情况
     */
    private Runnable execMerchantCntData() {
        return () -> {
            String sql = "" +
                    " select date (normal_repay_date) count_day," +
                    "    so.mini_type," +
                    "    platform_code platform," +
                    "    1 order_type," +
                    "    if(nvl(sdq.name, '') = '', '未标记上导流商', sdq.name) quotient_name," +
                    "    count (distinct so.order_uid) repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 0, so.order_uid, null)) ahead," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 1, so.order_uid, null)) cur," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 2, so.order_uid, null)) t1_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 3, so.order_uid, null)) t2_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 4, so.order_uid, null)) t3_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 5, so.order_uid, null)) t4_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 6, so.order_uid, null)) t5_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 7, so.order_uid, null)) t6_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 8, so.order_uid, null)) t7_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 9, so.order_uid, null)) t8_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 10,so. order_uid, null)) t9_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 11, so.order_uid, null)) t10_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 12, so.order_uid, null)) t11_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 13, so.order_uid, null)) t12_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 14, so.order_uid, null)) t13_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 15, so.order_uid, null)) t14_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 16, so.order_uid, null)) t15_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 17, so.order_uid, null)) t16_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 18, so.order_uid, null)) t17_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 19, so.order_uid, null)) t18_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 20, so.order_uid, null)) t19_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 21, so.order_uid, null)) t20_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 22, so.order_uid, null)) t21_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 23, so.order_uid, null)) t22_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 24, so.order_uid, null)) t23_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 25, so.order_uid, null)) t24_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 26, so.order_uid, null)) t25_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 27, so.order_uid, null)) t26_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 28, so.order_uid, null)) t27_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 29, so.order_uid, null)) t28_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 30, so.order_uid, null)) t29_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 31, so.order_uid, null)) t30_repay," +
                    "    count (  distinct  if(datediff(real_repay_date, normal_repay_date, 'dd') < 60, so.order_uid, null)) m2_repay" +
                    " from sh_repayment_plan srp" +
                    "    left join sh_order so" +
                    " on srp.order_uid = so.order_uid" +
                    "    and so.closing_time is null and so.is_deleted = 0 and so.ds = (select ds from dt)" +
                    "    left join sh_order_extra_info soei on so.order_uid = soei.order_uid and soei.ds = (select ds from dt)" +
                    "    left join sh_drainage_quotient sdq on soei.drainage_quotient_uid = sdq.drainage_quotient_uid and sdq.ds = (select ds from dt)" +
                    "    inner join sh_mini_config smc on smc.mini_type = so.mini_type and smc.ds = (select ds from dt)" +
                    " where srp.ds=(select ds from dt)" +
                    "     and date (normal_repay_date) <= date(lastday(getdate()))" +
                    " group by count_day, so.mini_type,   sdq.name, platform_code" +
                    " order by count_day, so.mini_type, sdq.name,platform_code";
            getRecords(sql, MERCHANT, CNT);
        };
    }

    /**
     * 分期回款金额情况
     */
    private Runnable execMerchantAmtData() {
        return () -> {
            String sql = "select date (normal_repay_date) count_day," +
                    "    so.mini_type," +
                    "    platform_code platform," +
                    "    1 order_type," +
                    "    if(nvl(sdq.name, '') = '', '未标记上导流商', sdq.name) quotient_name," +
                    "    nvl(sum (payable_amount),0) repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 0, real_repay_amount, null)),0) ahead," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 1, real_repay_amount, null)),0) cur," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 2, real_repay_amount, null)),0) t1_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 3, real_repay_amount, null)),0) t2_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 4, real_repay_amount, null)),0) t3_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 5, real_repay_amount, null)),0) t4_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 6, real_repay_amount, null)),0) t5_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 7, real_repay_amount, null)),0) t6_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 8, real_repay_amount, null)),0) t7_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 9, real_repay_amount, null)),0) t8_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 10, real_repay_amount, null)),0) t9_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 11, real_repay_amount, null)),0) t10_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 12, real_repay_amount, null)),0) t11_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 13, real_repay_amount, null)),0) t12_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 14, real_repay_amount, null)),0) t13_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 15, real_repay_amount, null)),0) t14_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 16, real_repay_amount, null)),0) t15_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 17, real_repay_amount, null)),0) t16_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 18, real_repay_amount, null)),0) t17_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 19, real_repay_amount, null)),0) t18_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 20, real_repay_amount, null)),0) t19_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 21, real_repay_amount, null)),0) t20_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 22, real_repay_amount, null)),0) t21_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 23, real_repay_amount, null)),0) t22_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 24, real_repay_amount, null)),0) t23_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 25, real_repay_amount, null)),0) t24_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 26, real_repay_amount, null)),0) t25_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 27, real_repay_amount, null)),0) t26_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 28, real_repay_amount, null)),0) t27_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 29, real_repay_amount, null)),0) t28_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 30, real_repay_amount, null)),0) t29_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 31, real_repay_amount, null)),0) t30_repay," +
                    "    nvl(sum (if(datediff(real_repay_date, normal_repay_date, 'dd') < 60, real_repay_amount, null)),0) m2_repay" +
                    "   from sh_repayment_plan srp" +
                    "       left join sh_order so" +
                    "   on srp.order_uid = so.order_uid" +
                    "       and so.closing_time is null and so.is_deleted = 0 and so.ds = (select ds from dt)" +
                    "       left join sh_order_extra_info soei on so.order_uid = soei.order_uid and soei.ds = (select ds from dt)" +
                    "       left join sh_drainage_quotient sdq on soei.drainage_quotient_uid = sdq.drainage_quotient_uid and sdq.ds = (select ds from dt)" +
                    "       inner join sh_mini_config smc on smc.mini_type = so.mini_type and smc.ds = (select ds from dt)" +
                    "   where srp.ds=(select ds from dt)" +
                    "     and date (normal_repay_date) <= date(lastday(getdate()))" +
                    "   group by count_day, so.mini_type, sdq.name,platform_code" +
                    "   order by count_day, so.mini_type, sdq.name,  platform_code";
            getRecords(sql, MERCHANT, AMT);
        };
    }

    private void getRecords(String sql, Integer businessType, Integer repayType) {
        final String prefix = "with dt as (select date_format(now(), 'yyyymmdd') as ds) ";
        final String count = "select count(*) num from ( %s )";
        final String last = " limit %d,%d";
        String countSql = prefix + String.format(count, sql) + ";";
        Integer size = getCount(countSql);
        int times = size / PAGE_SIZE;
        if (size % PAGE_SIZE != 0) {
            times += 1;
        }
        int startPage = 0;
        for (int i = 0; i < times; i++) {
            String limitSql = prefix + sql + String.format(last, startPage * PAGE_SIZE, PAGE_SIZE) + ";";
            List<Record> records = odpsUtil.querySql(limitSql);
            handleData(records, businessType, repayType);
            startPage++;
        }
    }

    private void handleData(List<Record> records, Integer businessType, Integer repayType) {
        List<WholeRepayDO> collect = records.stream().map((record) -> {
            LocalDate countDay = LocalDate.parse(record.getString("count_day"), YYYY_MM_DD);
            String countMonth = countDay.format(YYYY_MM);
            WholeRepayDO rentReportDO = new WholeRepayDO();
            rentReportDO.setCountDay(countDay)
                    .setMiniType(Integer.valueOf(record.getString("mini_type")))
                    .setBusinessType(businessType)
                    .setCountMonth(countMonth)
                    .setOrderType(Integer.valueOf(record.getString("order_type")))
                    .setRepayType(repayType)
                    .setPlatform(record.getString("platform"))
                    .setQuotientName(record.getString("quotient_name"))
                    .setRepay(BigDecimal.valueOf(Double.parseDouble(record.getString("repay"))))
                    // .setAhead(BigDecimal.valueOf(Double.parseDouble(record.getString("ahead"))))
                    .setT0Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("cur"))))
                    .setT1Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t1_repay"))))
                    .setT2Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t2_repay"))))
                    .setT3Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t3_repay"))))
                    .setT4Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t4_repay"))))
                    .setT5Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t5_repay"))))
                    .setT6Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t6_repay"))))
                    .setT7Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t7_repay"))))
                    .setT8Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t8_repay"))))
                    .setT9Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t9_repay"))))
                    .setT10Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t10_repay"))))
                    .setT11Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t11_repay"))))
                    .setT12Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t12_repay"))))
                    .setT13Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t13_repay"))))
                    .setT14Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t14_repay"))))
                    .setT15Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t15_repay"))))
                    .setT16Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t16_repay"))))
                    .setT17Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t17_repay"))))
                    .setT18Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t18_repay"))))
                    .setT19Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t19_repay"))))
                    .setT20Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t20_repay"))))
                    .setT21Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t21_repay"))))
                    .setT22Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t22_repay"))))
                    .setT23Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t23_repay"))))
                    .setT24Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t24_repay"))))
                    .setT25Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t25_repay"))))
                    .setT26Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t26_repay"))))
                    .setT27Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t27_repay"))))
                    .setT28Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t28_repay"))))
                    .setT29Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t29_repay"))))
                    .setT30Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("t30_repay"))))
                    .setM2Repay(BigDecimal.valueOf(Double.parseDouble(record.getString("m2_repay"))));
            return rentReportDO;
        }).collect(Collectors.toList());
        wholeRepayService.saveBatch(collect);
        wholeRepayService.save2Mongo(collect);
    }

    private Integer getCount(String sql) {
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }


}



