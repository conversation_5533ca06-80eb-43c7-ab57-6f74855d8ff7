package qnvip.data.overview.business.overdue;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.business.whole.report.WholeLawReportBusiness;
import qnvip.data.overview.domain.overdue.RepayPerformanceOverdueRepayDO;
import qnvip.data.overview.service.overdue.RepayPerformanceOverdueRepayService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/12/21
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class RepayPerformanceBusiness {

    private final OdpsUtil odpsUtil;

    private final RepayPerformanceOverdueRepayService service;

    public void excute(String ds) {
        String time = LocalDate.parse(ds, WholeLawReportBusiness.YYYYMMDD).format(WholeLawReportBusiness.YYYY_MM_DD);
        String sql = "with dt as (select " + ds + " as ds) ," +
                "  calc_repay_info AS (SELECT capital\n" +
                "                               ,order_id\n" +
                "                               ,overdue\n" +
                "                               ,overdue_fine\n" +
                "                               ,real_repay_capital\n" +
                "                               ,SUBSTRING(real_repay_time,1,10) AS real_repay_date\n" +
                "                               ,real_overdue_fine\n" +
                "                               ,reduce_amt\n" +
                "                               ,withhold_type\n" +
                "                               ,repay_date\n" +
                "                               ,repay_status\n" +
                "                               ,IF((overdue=5 OR SUBSTRING(real_repay_time,1,10)>SUBSTRING(repay_date,1,10)) AND (repay_status=1 OR SUBSTRING(real_repay_time,1,10) ='" + time + "'),IF(repay_status=10,real_repay_capital+report_reduce_amt,capital),0) AS collect_amt\n" +
                "                               ,IF((overdue=5 OR SUBSTRING(real_repay_time,1,10)>SUBSTRING(repay_date,1,10)) AND SUBSTRING(real_repay_time,1,10)='" + time + "',real_repay_capital+report_reduce_amt,0) AS collect_repaied_amt\n" +
                "                               ,IF((overdue=5 OR SUBSTRING(real_repay_time,1,10)>SUBSTRING(repay_date,1,10)) AND (repay_status=1 OR SUBSTRING(real_repay_time,1,10)='" + time + "'),report_reduce_amt,0) AS collect_reduce_amt\n" +
                "                               ,IF((overdue=5 OR SUBSTRING(real_repay_time,1,10)>SUBSTRING(repay_date,1,10)) AND SUBSTRING(real_repay_time,1,10)='" + time + "' AND withhold_type IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_active_capital\n" +
                "                               ,IF((overdue=5 OR SUBSTRING(real_repay_time,1,10)>SUBSTRING(repay_date,1,10)) AND SUBSTRING(real_repay_time,1,10)='" + time + "' AND withhold_type NOT IN (0,1,3,6,7,9),real_repay_capital+report_reduce_amt,0) AS collect_withhold_capital\n" +
                "                               ,term\n" +
                "                         FROM rent_order_repayment_plan\n" +
                "                         WHERE ds=(select ds from dt)\n" +
                "                              AND is_deleted=0)\n" +
                "\n" +
                "    ,calc_overdue_repay_detail AS (SELECT overdue.*\n" +
                "         , repay.repay_status\n" +
                "         , repay.real_repay_date\n" +
                "         , repay.overdue\n" +
                "         , repay.term\n" +
                "         -- 6+6买断到期后会计算买断金,买断金按照实还期数计算剩余价值,不再统计账单应还\n" +
                "         , IF(overdue.finance_title like '%6+6%' AND overdue.rent_end_date2 < overdue.reference_ds, 0,\n" +
                "              collect_amt)              AS collect_amt\n" +
                "         , IF(overdue.finance_title like '%6+6%' AND overdue.rent_end_date2 < overdue.reference_ds, 0,\n" +
                "              collect_repaied_amt)      AS collect_repaied_amt\n" +
                "         , IF(overdue.finance_title like '%6+6%' AND overdue.rent_end_date2 < overdue.reference_ds, 0,\n" +
                "              collect_reduce_amt)       AS collect_reduce_amt\n" +
                "         , IF(overdue.finance_title like '%6+6%' AND overdue.rent_end_date2 < overdue.reference_ds, 0,\n" +
                "              collect_active_capital)   AS collect_active_capital\n" +
                "         , IF(overdue.finance_title like '%6+6%' AND overdue.rent_end_date2 < overdue.reference_ds, 0,\n" +
                "              collect_withhold_capital) AS collect_withhold_capital\n" +
                "         , IF((repay.overdue=5 OR repay.real_repay_date>SUBSTRING(repay.repay_date,1,10)) AND (repay.repay_status = 1 OR repay.real_repay_date = overdue.reference_ds),\n" +
                "              overdue.parent_order_no,\n" +
                "              NULL)                     AS collect_orderno\n" +
                "         , IF((repay.overdue=5 OR repay.real_repay_date>SUBSTRING(repay.repay_date,1,10)) AND repay.real_repay_date = overdue.reference_ds, overdue.parent_order_no,\n" +
                "              NULL)                     AS collect_repaied_orderno\n" +
                "         , IF((repay.overdue=5 OR repay.real_repay_date>SUBSTRING(repay.repay_date,1,10)) AND (repay.repay_status = 1 OR repay.real_repay_date = overdue.reference_ds)\n" +
                "                AND repay.collect_reduce_amt>0, overdue.parent_order_no, NULL) AS collect_reduce_orderno\n" +
                "         , IF((repay.overdue=5 OR repay.real_repay_date>SUBSTRING(repay.repay_date,1,10)) AND repay.real_repay_date = overdue.reference_ds AND\n" +
                "              repay.withhold_type IN (0, 1, 3, 6, 7, 9), overdue.parent_order_no,\n" +
                "              NULL)                     AS collect_active_orderno\n" +
                "         , IF((repay.overdue=5 OR repay.real_repay_date>SUBSTRING(repay.repay_date,1,10)) AND repay.real_repay_date = overdue.reference_ds AND\n" +
                "              repay.withhold_type NOT IN (0, 1, 3, 6, 7, 9), overdue.parent_order_no,\n" +
                "              NULL)                     AS collect_withhold_orderno\n" +
                "         , CASE\n" +
                "               WHEN ((infomore.buyout_time IS NULL AND infomore.return_time IS NULL AND extend.extend_no IS NULL) OR\n" +
                "                     SUBSTRING(infomore.buyout_time,1,10) = overdue.reference_ds) \n" +
                "                   THEN (CASE\n" +
                "                             WHEN overdue.rent_end_date2 < overdue.reference_ds \n" +
                "                             -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                                 THEN (CASE\n" +
                "                                           WHEN ((overdue.finance_title = '3+X' OR overdue.finance_title LIKE '%6+6%')\n" +
                "                                               AND overdue.last_term = repay.term)\n" +
                "                                               OR\n" +
                "                                                (overdue.finance_title != '3+X' AND overdue.finance_title NOT LIKE '%6+6%'\n" +
                "                                                    AND extend.extend_no IS NULL AND repay.term = overdue.total_term)\n" +
                "                                               THEN overdue.parent_order_no\n" +
                "                                           ELSE NULL END)\n" +
                "                             ELSE NULL END)\n" +
                "               ELSE NULL END            AS collect_buyout_orderno\n" +
                "\n" +
                "         , CASE\n" +
                "               WHEN ((infomore.buyout_time IS NULL AND infomore.return_time IS NULL AND extend.extend_no IS NULL) OR\n" +
                "                     SUBSTRING(infomore.buyout_time,1,10) = overdue.reference_ds) \n" +
                "                   THEN (CASE\n" +
                "                             WHEN overdue.rent_end_date2 < overdue.reference_ds \n" +
                "                             -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                                 THEN (CASE\n" +
                "                                           WHEN ((overdue.finance_title = '3+X' OR overdue.finance_title LIKE '%6+6%')\n" +
                "                                               AND overdue.last_term = repay.term)\n" +
                "                                               OR\n" +
                "                                                (overdue.finance_title != '3+X' AND overdue.finance_title NOT LIKE '%6+6%'\n" +
                "                                                    AND extend.extend_no IS NULL AND repay.term = overdue.total_term)\n" +
                "                                               THEN overdue.buyout_amt\n" +
                "                                           ELSE 0 END)\n" +
                "                             ELSE 0 END)\n" +
                "               ELSE 0 END               AS collect_buyout\n" +
                "\n" +
                "         , CASE\n" +
                "               WHEN SUBSTRING(infomore.buyout_time,1,10) = overdue.reference_ds \n" +
                "                   THEN (CASE\n" +
                "                             WHEN overdue.rent_end_date2 < overdue.reference_ds \n" +
                "                             -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                                 THEN (CASE\n" +
                "                                           WHEN ((overdue.finance_title = '3+X' OR overdue.finance_title LIKE '%6+6%')\n" +
                "                                               AND overdue.last_term = repay.term)\n" +
                "                                               OR\n" +
                "                                                (overdue.finance_title != '3+X' AND overdue.finance_title NOT LIKE '%6+6%'\n" +
                "                                                    AND extend.extend_no IS NULL AND repay.term = overdue.total_term)\n" +
                "                                               THEN overdue.buyout_amt\n" +
                "                                           ELSE 0 END)\n" +
                "                             ELSE 0 END)\n" +
                "               ELSE 0 END               AS repaied_buyout\n" +
                "\n" +
                "         , CASE\n" +
                "               WHEN SUBSTRING(infomore.buyout_time,1,10) = overdue.reference_ds \n" +
                "                   THEN (CASE\n" +
                "                             WHEN overdue.rent_end_date2 < overdue.reference_ds \n" +
                "                             -- 针对账单关联,关联到认定买断的最后一期上\n" +
                "                                 THEN (CASE\n" +
                "                                           WHEN ((overdue.finance_title = '3+X' OR overdue.finance_title LIKE '%6+6%')\n" +
                "                                               AND overdue.last_term = repay.term)\n" +
                "                                               OR\n" +
                "                                                (overdue.finance_title != '3+X' AND overdue.finance_title NOT LIKE '%6+6%'\n" +
                "                                                    AND extend.extend_no IS NULL AND repay.term = overdue.total_term)\n" +
                "                                               THEN overdue.parent_order_no\n" +
                "                                           ELSE NULL END)\n" +
                "                             ELSE NULL END)\n" +
                "               ELSE NULL END            AS repaied_buyout_orderno\n" +
                "\n" +
                "         , CASE\n" +
                "               WHEN  overdue.parent_id = 0 THEN repay.term +10\n" +
                "               WHEN  overdue.parent_id > 0 THEN repay.term +22\n" +
                "        END                             AS stat_time_zone\n" +
                "    FROM (select *,'" + time + "' as reference_ds from dataview_repay_overdue_repay_order where ds=(select ds from dt)) overdue\n" +
                "             JOIN calc_repay_info repay\n" +
                "             ON overdue.order_id=repay.order_id\n" +
                "             LEFT JOIN (SELECT id, no as extend_no, parent_id FROM rent_order WHERE ds=(select ds from dt)) extend\n" +
                "                  ON overdue.order_id = extend.parent_id\n" +
                "             LEFT JOIN (SELECT order_id\n" +
                "                             , NULLIF(TRIM(buyout_time), '')      AS buyout_time\n" +
                "                             , NULLIF(TRIM(return_succ_time), '') AS return_time\n" +
                "                        FROM rent_order_infomore\n" +
                "                        WHERE ds=(select ds from dt)) infomore\n" +
                "                       ON overdue.order_id = infomore.order_id AND overdue.parent_id = 0)\n" +
                "\n" +
                "-- 实时部分处理逻辑\n" +
                "SELECT overdue_level\n" +
                "      ,stat_time_zone\n" +
                "      ,stat_type\n" +
                "      ,collection_item\n" +
                "      ,repaied_item\n" +
                "      ,if(collection_item = 0, 0, repaied_item / collection_item)  AS repaied_ratio\n" +
                "      ,reduce_item\n" +
                "      ,if(collection_item = 0, 0, reduce_item / collection_item)  AS reduce_ratio\n" +
                "      ,active_capital_item\n" +
                "      ,if(collection_item = 0, 0, active_capital_item / collection_item)  AS active_capital_ratio\n" +
                "      ,withhold_capital_item\n" +
                "      ,if(collection_item = 0, 0, withhold_capital_item / collection_item)  AS withhold_capital_ratio\n" +
                "      ,collection_item - repaied_item AS unpay_item\n" +
                "      ,if(collection_item = 0, 0, (collection_item - repaied_item) / collection_item)  AS unpay_ratio\n" +
                "      ,repaied_buyout_item\n" +
                "      ,if(collection_item = 0, 0, repaied_buyout_item / collection_item)  AS repaied_buyout_ratio\n" +
                "FROM\n" +
                "(SELECT overdue_level\n" +
                "      ,COALESCE(stat_time_zone,0)     AS stat_time_zone\n" +
                "      ,1                              AS stat_type  -- 数量\n" +
                "      ,COUNT(DISTINCT collect_orderno) AS collection_item   -- 应催金额(已还+未还)\n" +
                "      ,COUNT(DISTINCT collect_repaied_orderno) AS repaied_item      -- 已还金额(包含减免金额)\n" +
                "      ,COUNT(DISTINCT collect_reduce_orderno) AS reduce_item -- 减免金额\n" +
                "      ,COUNT(DISTINCT collect_active_orderno) AS active_capital_item   -- 主动还租金\n" +
                "      ,COUNT(DISTINCT collect_withhold_orderno) AS withhold_capital_item -- 代扣还租金\n" +
                "      ,COUNT(DISTINCT repaied_buyout_orderno) AS repaied_buyout_item -- 代扣买断金\n" +
                "FROM (SELECT collect_orderno\n" +
                "            ,collect_repaied_orderno\n" +
                "            ,collect_reduce_orderno\n" +
                "            ,collect_active_orderno\n" +
                "            ,collect_withhold_orderno\n" +
                "            ,repaied_buyout_orderno\n" +
                "            ,stat_time_zone\n" +
                "            ,overdue_level\n" +
                "      FROM calc_overdue_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4\n" +
                "      UNION ALL\n" +
                "      SELECT collect_buyout_orderno AS collect_orderno\n" +
                "            ,repaied_buyout_orderno AS collect_repaied_orderno\n" +
                "            ,NULL                   AS reduce_orderno\n" +
                "            ,NULL                   AS collect_active_orderno\n" +
                "            ,NULL                   AS collect_withhold_orderno\n" +
                "            ,repaied_buyout_orderno AS repaied_buyout_orderno\n" +
                "            ,100                    AS stat_time_zone\n" +
                "            ,overdue_level\n" +
                "      FROM calc_overdue_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4)\n" +
                "GROUP BY overdue_level, stat_time_zone\n" +
                "GROUPING SETS ((overdue_level, stat_time_zone), (overdue_level))\n" +
                "UNION ALL\n" +
                "SELECT overdue_level\n" +
                "      ,COALESCE(stat_time_zone,0)     AS stat_time_zone\n" +
                "      ,0                              AS stat_type  -- 金额\n" +
                "      ,SUM(collect_amt) AS collection_item   -- 应催金额(已还+未还)\n" +
                "      ,SUM(collect_repaied_amt)   AS repaied_item -- 已还金额(包含减免金额)\n" +
                "      ,SUM(collect_reduce_amt) AS reduce_item -- 减免金额\n" +
                "      ,SUM(collect_active_capital) AS active_capital_item   -- 主动还租金\n" +
                "      ,SUM(collect_withhold_capital) AS withhold_capital_item   -- 代扣还租金\n" +
                "      ,SUM(repaied_buyout) AS repaied_buyout_item -- 已还买断金\n" +
                "FROM (SELECT collect_amt\n" +
                "            ,collect_repaied_amt\n" +
                "            ,collect_reduce_amt\n" +
                "            ,collect_active_capital\n" +
                "            ,collect_withhold_capital\n" +
                "            ,0 as repaied_buyout\n" +
                "            ,stat_time_zone\n" +
                "            ,overdue_level\n" +
                "      FROM calc_overdue_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4\n" +
                "      UNION ALL\n" +
                "      SELECT collect_buyout AS collect_amt\n" +
                "            ,repaied_buyout AS collect_repaied_amt\n" +
                "            ,0              AS collect_reduce_amt\n" +
                "            ,0              AS collect_active_capital\n" +
                "            ,0              AS collect_withhold_capital\n" +
                "            ,repaied_buyout AS repaied_buyout\n" +
                "            ,100            AS stat_time_zone\n" +
                "            ,overdue_level  AS overdue_level\n" +
                "      FROM calc_overdue_repay_detail\n" +
                "      WHERE stat_time_zone NOT BETWEEN 1 AND 4) tmp2\n" +
                "GROUP BY overdue_level, stat_time_zone\n" +
                "GROUPING SETS ((overdue_level, stat_time_zone), (overdue_level))) tmp3";

        List<Record> records = odpsUtil.querySql(sql.concat(";"));
        LocalDate localDate = DateUtils.stringToLocateDate(time);
        List<RepayPerformanceOverdueRepayDO> collect = records.stream().map(o -> {
            RepayPerformanceOverdueRepayDO domain = new RepayPerformanceOverdueRepayDO();
            domain.setOverdueType(o.getString("overdue_level"));
            domain.setStatTimeZone(Long.valueOf(o.getString("stat_time_zone")));
            domain.setStatType(Long.valueOf(o.getString("stat_type")));
            domain.setCollectionItem(new BigDecimal(o.getString("collection_item")));
            domain.setRepaiedItem(new BigDecimal(o.getString("repaied_item")));
            domain.setRepaiedRatio(new BigDecimal(o.getString("repaied_ratio")));
            domain.setReduceItem(new BigDecimal(o.getString("reduce_item")));
            domain.setReduceRatio(new BigDecimal(o.getString("reduce_ratio")));
            domain.setActiveItem(new BigDecimal(o.getString("active_capital_item")));
            domain.setActiveRatio(new BigDecimal(o.getString("active_capital_ratio")));
            domain.setWithholdItem(new BigDecimal(o.getString("withhold_capital_item")));
            domain.setWithholdRatio(new BigDecimal(o.getString("withhold_capital_ratio")));
            domain.setUnpayItem(new BigDecimal(o.getString("unpay_item")));
            domain.setUnpayRatio(new BigDecimal(o.getString("unpay_ratio")));
            domain.setActiveBuyoutItem(new BigDecimal(o.getString("repaied_buyout_item")));
            domain.setActiveBuyoutRatio(new BigDecimal(o.getString("repaied_buyout_ratio")));
            domain.setCountDay(time);
            return domain;
        }).collect(Collectors.toList());
        if (CollUtil.isNotEmpty(collect)) {
            service.removeDataByTime(localDate);
            service.saveBatch(collect);
        }
    }

}
