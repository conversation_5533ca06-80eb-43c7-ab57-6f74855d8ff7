package qnvip.data.overview.business.risk;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RiskRenewalBusinessDO;
import qnvip.data.overview.enums.finance.FinanceTypeEnum;
import qnvip.data.overview.service.order.RentOrderService;
import qnvip.data.overview.service.risk.RiskRenewalBusinessService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * 风控大盘-vintage-续租
 * create by gw on 2022/3/24
 *
 * <AUTHOR>
 * @date 2023/08/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskRenewalBusiness {

    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private static final Integer PAGE_SIZE = 10000;


    private final OdpsUtil odpsUtil;
    private final RiskRenewalBusinessService riskBusinessService;


    public void execCount(String ds) {

        try {
            riskBusinessService.deleteAll();
            Integer size = getCount(ds);
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                List<RiskRenewalBusinessDO> list = countRepayTask(ds,startPage);
                riskBusinessService.saveBatch(list);
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }


    /**
     * 风控经营
     *
     * @param ds
     */
    private List<RiskRenewalBusinessDO> countRepayTask(String ds, int startPage) {

        String sql = "SELECT  date(z.rent_start_date) count_day" +
                "         ,ro.mini_type" +
                "      ,IF(settle_date is not null, '1','0') is_settle, " +
                "       nvl(auto_renewal ,1)  auto_renewal ," +
                "         (" +
                "             CASE    WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "                     WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信'" +
                "                     WHEN ro.mini_type = 2 THEN '字节跳动'" +
                "                     WHEN ro.mini_type = 3 THEN 'app'" +
                "             END" +
                "         ) platform" +
                "         ,c.rate_config_type finance_type" +
                "         ,cl.riskOpinion risk_level" +
                "         ,sn.riskStrategy risk_strategy" +
                "         ,IF(artificialAuditorId=1, '自动风控','人工审核') audit_type" +
                "         ,IF(" +
                "             ext_json IS NOT NULL AND c.rate_config_type = 10" +
                "             ,'1'" +
                "             ,'0'" +
                "         ) AS forcedconversion" +
                "         ,nvl(count(z.NO), 0) total_order_cnt" +
                "         ,sum(if(c.rate_config_type != 10,c.actual_financing_amt,d.capital*12)) rent_total" +
                "         ,nvl(sum(discounts_total), 0) discounts_total" +
                "         ,nvl(sum(bond_amt), 0) bond_amt_total" +
                "        ,nvl(min(rorpp.bond_rate), 0) bond_rate" +
                " FROM  (SELECT id,parent_id,no,rent_start_date" +
                "                      FROM rent_order ro" +
                "                      WHERE ds =  " + ds +
                "                        AND merchant_id = 100" +
                "                        AND ro.parent_id > 0" +
                "                           and biz_type = 2" +
                "                        AND termination != 5" +
                "                        AND is_deleted = 0" +
                "                        AND ro.type = 1" +
                "                         AND date(rent_start_date) >= '2020-10-01'" +
                "                      ORDER BY id) z inner join rent_order ro ON ro.id = z.parent_id" +
                " LEFT join (" +
                "               SELECT  min(ext) ext" +
                "                       ,order_id" +
                "               FROM    rent_order_flow b" +
                "               WHERE   b.ds =  " + ds +
                "               AND     b.biz_type = 3" +
                "               AND     b.is_deleted = 0" +
                "               AND     b.pay_status = 10" +
                "               and     (b.mark_refund IS NULL OR b.mark_refund = 0)" +
                "               AND     b.flow_type = 1" +
                "               AND     b.refunded_amt = 0" +
                "               AND     b.ext IS NOT NULL" +
                "               AND     b.ext != ''" +
                "               GROUP BY order_id" +
                "           ) g" +
                " ON      z.id = g.order_id INNER" +
                " JOIN    rent_order_finance_detail c" +
                " ON      z.id = c.order_id" +
                " inner join rent_order_infomore roi on roi.order_id =z.id" +
                " INNER join (" +
                "                SELECT  order_id" +
                "                        ,avg(capital) capital" +
                "                FROM    rent_order_repayment_plan" +
                "                WHERE   is_deleted = 0" +
                "                AND     ds =  " + ds +
                "                GROUP BY order_id" +
                "            ) d" +
                " ON      z.id = d.order_id" +
                " LEFT join (" +
                "               SELECT  order_id" +
                "                       ,sum(if(isbefore=1,amt1+amt2,0)) discounts_total" +
                "               FROM    (" +
                "                           SELECT  x.order_id" +
                "                                   ,if(" +
                "                                       min(x.bind_order_time) IS NOT NULL AND min(y.payment_time) >=" +
                " min(x.bind_order_time)" +
                "                                       ,1" +
                "                                       ,0" +
                "                                   ) isbefore" +
                "                                   ,if(x.type IN (1, 3), max(x.write_off_amt), 0) amt1" +
                "                                   ,if(x.type IN (2, 4), sum(x.write_off_amt), 0) amt2" +
                "                           FROM    rent_customer_coupon x INNER" +
                "                           JOIN    rent_order y" +
                "                           ON      x.order_id = y.id" +
                "                           WHERE   x.order_id > 0" +
                "                           AND     x.scene = 1" +
                "                           AND     x.is_deleted = 0" +
                "                           AND     y.is_deleted = 0" +
                "                           AND     x.ds =  " + ds +
                "                           AND     y.ds =  " + ds +
                "                           GROUP BY x.order_id" +
                "                                    ,x.type" +
                "                       )" +
                "               GROUP BY order_id" +
                "           ) x" +
                " ON      z.id = x.order_id " +
                "           INNER JOIN (select nvl(min(b.bond_amt) / (if(min(b.rate_config_type) != 10,min (b.actual_financing_amt),  avg (capital) * 12) +\n" +
                "                                                  if(min(b.rate_config_type != 10), min (b.buyout_amt), 0) - min (xx.total_discount)),0) bond_rate,\n" +
                "                            a.order_id" +
                "                     from rent_order_repayment_plan a" +
                "                              INNER JOIN rent_order_finance_detail b ON a.order_id = b.order_id" +
                "                              LEFT join (select order_id," +
                "                                                if(min(isbefore) = 1, min(amt1) + min(amt2), 0)           total_discount," +
                "                                                if(min(isbefore) = 1, min(yuji_amt1) + min(yuji_amt2), 0) before_discount" +
                "                                         from (SELECT x.order_id," +
                "                                                      if(min(x.bind_order_time) IS NOT NULL AND" +
                "                                                         min(y.payment_time) >= min(x.bind_order_time), 1, 0) isbefore," +
                "                                                      if(x.type IN (1, 3), max(x.write_off_amt), 0)           amt1," +
                "                                                      if(x.type IN (2, 4), sum(x.write_off_amt), 0)           amt2," +
                "                                                      if(x.type IN (1, 3) AND min(x.use_status = 2)," +
                "                                                         max(x.write_off_amt), 0)                             yuji_amt1," +
                "                                                      if(x.type IN (2, 4) AND min(x.use_status = 2)," +
                "                                                         sum(x.write_off_amt), 0)                             yuji_amt2" +
                "                                               FROM rent_customer_coupon x" +
                "                                                        INNER JOIN rent_order y ON x.order_id = y.id" +
                "                                               WHERE x.order_id > 0" +
                "                                                 AND x.scene = 1" +
                "                                                 AND x.is_deleted = 0" +
                "                                                and x.ds =" + ds +
                "                                                and y.ds=" + ds +
                "                                                 AND y.is_deleted = 0" +
                "                                               GROUP BY x.order_id, x.type) d" +
                "                                         group by order_id) xx ON b.order_id = xx.order_id" +
                "                            where a.ds=" + ds +
                "                            and b.ds=" + ds +
                "                     group by a.order_id" +
                " ) rorpp  ON z.parent_id = rorpp.order_id" +
                " INNER JOIN    cl_loan cl" +
                " ON      cl.loanno = ro.no" +
                " INNER JOIN serial_no sn" +
                " ON      sn.businessno = ro.no" +
                " WHERE" +
                "         c.is_deleted = 0" +
                " AND     ro.ds =  " + ds +
                " AND     c.ds =  " + ds +
                " AND     cl.ds =  " + ds +
                " AND     sn.ds =  " + ds +
                " AND     roi.ds =  " + ds +
                " GROUP BY date(z.rent_start_date)" +
                "          ,ro.mini_type" +
                "          ,c.rate_config_type" +
                "          ,IF(ext_json IS NOT NULL AND c.rate_config_type = 10, '1', '0')" +
                "          ,cl.riskOpinion" +
                "          ,sn.riskStrategy" +
                "          ,cl.artificialAuditorId" +
                "          ,IF(settle_date is not null, '1','0')" +
                "           ,auto_renewal" +
                " ORDER BY date(z.rent_start_date)" +
                " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";

        List<Record> records = odpsUtil.querySql(sql);
        List<RiskRenewalBusinessDO> resList = new ArrayList<>();
        records.forEach(k -> {
            LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("count_day"), dateFormatter),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            String platform = k.getString("platform");
            Integer financeType = Integer.valueOf(k.getString("finance_type"));
            String riskLevel = k.getString("risk_level");
            String riskStrategy = k.getString("risk_strategy");
            String auditType = k.getString("audit_type");
            Integer isSettle = Integer.valueOf(k.getString("is_settle"));
            Integer autoRenewal = Integer.valueOf(k.getString("auto_renewal"));
            Integer forcedConversion = Integer.valueOf(k.getString("forcedconversion"));
            Integer totalOrderCnt = Integer.valueOf(k.getString("total_order_cnt"));
            BigDecimal rentTotal = stringToDecimal(k.getString("rent_total"));
            BigDecimal discountsTotal = stringToDecimal(k.getString("discounts_total"));
            BigDecimal bondAmtTotal = stringToDecimal(k.getString("bond_amt_total"));
            Double bondRate = Double.parseDouble(k.getString("bond_rate"));

            RiskRenewalBusinessDO repayDO = new RiskRenewalBusinessDO();
            repayDO.setCountDay(countDay);
            repayDO.setMiniType(miniType);
            repayDO.setPlatform(platform);
            repayDO.setRiskLevel(riskLevel);
            repayDO.setRiskStrategy(riskStrategy);
            repayDO.setAuditType(auditType);
            repayDO.setTotalOrderCnt(totalOrderCnt);
            repayDO.setRentTotal(rentTotal);
            repayDO.setDiscountsTotal(discountsTotal);
            repayDO.setBondAmtTotal(bondAmtTotal);
            repayDO.setBondRate(bondRate);
            repayDO.setIsSettle(isSettle);
            repayDO.setRenewWay(autoRenewal);
            repayDO.setFinanceType(FinanceTypeEnum.getFinanceType(financeType, 0, forcedConversion));
            resList.add(repayDO);
        });
        return resList;
    }

    /**
     * 风控经营
     *
     * @param ds
     */
    private Integer getCount(String ds) {

        String sql = "select count(*) num from (SELECT  date(z.rent_start_date) count_day" +
                "         ,ro.mini_type" +
                "         ,(" +
                "             CASE    WHEN ro.mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝'" +
                "                     WHEN ro.mini_type IN (6, 8, 10, 11) THEN '微信'" +
                "                     WHEN ro.mini_type = 2 THEN '字节跳动'" +
                "                     WHEN ro.mini_type = 3 THEN 'app'" +
                "             END" +
                "         ) platform" +
                "         ,c.rate_config_type finance_type" +
                "         ,cl.riskOpinion risk_level" +
                "         ,sn.riskStrategy risk_strategy" +
                "         ,IF(artificialAuditorId=1, '自动风控','人工审核') audit_type" +
                "         ,IF(" +
                "             ext_json IS NOT NULL AND c.rate_config_type = 10" +
                "             ,'1'" +
                "             ,'0'" +
                "         ) AS forcedconversion" +
                "      ,IF(settle_date is not null, '1','0') is_settle " +
                "          , auto_renewal" +
                "         ,nvl(count(z.NO), 0) total_order_cnt" +
                "         ,sum(if(c.rate_config_type != 10,c.actual_financing_amt,d.capital*12)) rent_total" +
                "         ,nvl(sum(discounts_total), 0) discounts_total" +
                "         ,nvl(sum(bond_amt), 0) bond_amt_total" +
                " FROM  (SELECT id,parent_id,no,rent_start_date" +
                "                      FROM rent_order ro" +
                "                      WHERE ds =  " + ds +
                "                        AND merchant_id = 100" +
                "                        AND ro.parent_id > 0" +
                "                        AND termination != 5" +
                "                        AND is_deleted = 0" +
                "                        AND ro.type = 1" +
                "                         AND date(rent_start_date) >= '2020-10-01'" +
                "                      ORDER BY id) z inner join rent_order ro ON ro.id = z.parent_id" +
                " LEFT join (" +
                "               SELECT  min(ext) ext" +
                "                       ,order_id" +
                "               FROM    rent_order_flow b" +
                "               WHERE   b.ds =  " + ds +
                "               AND     b.biz_type = 3" +
                "               AND     b.is_deleted = 0" +
                "               AND     b.pay_status = 10" +
                "               and     (b.mark_refund IS NULL OR b.mark_refund = 0)" +
                "               AND     b.flow_type = 1" +
                "               AND     b.refunded_amt = 0" +
                "               AND     b.ext IS NOT NULL" +
                "               AND     b.ext != ''" +
                "               GROUP BY order_id" +
                "           ) g" +
                " ON      z.id = g.order_id INNER" +
                " JOIN    rent_order_finance_detail c" +
                " ON      z.id = c.order_id" +
                " inner join rent_order_infomore roi on roi.order_id =z.id" +
                " INNER join (" +
                "                SELECT  order_id" +
                "                        ,avg(capital) capital" +
                "                FROM    rent_order_repayment_plan" +
                "                WHERE   is_deleted = 0" +
                "                AND     ds =  " + ds +
                "                GROUP BY order_id" +
                "            ) d" +
                " ON      z.id = d.order_id" +
                " LEFT join (" +
                "               SELECT  order_id" +
                "                       ,sum(if(isbefore=1,amt1+amt2,0)) discounts_total" +
                "               FROM    (" +
                "                           SELECT  x.order_id" +
                "                                   ,if(" +
                "                                       min(x.bind_order_time) IS NOT NULL AND min(y.payment_time) >=" +
                " min(x.bind_order_time)" +
                "                                       ,1" +
                "                                       ,0" +
                "                                   ) isbefore" +
                "                                   ,if(x.type IN (1, 3), max(x.write_off_amt), 0) amt1" +
                "                                   ,if(x.type IN (2, 4), sum(x.write_off_amt), 0) amt2" +
                "                           FROM    rent_customer_coupon x INNER" +
                "                           JOIN    rent_order y" +
                "                           ON      x.order_id = y.id" +
                "                           WHERE   x.order_id > 0" +
                "                           AND     x.scene = 1" +
                "                           AND     x.is_deleted = 0" +
                "                           AND     y.is_deleted = 0" +
                "                           AND     x.ds =  " + ds +
                "                           AND     y.ds =  " + ds +
                "                           GROUP BY x.order_id" +
                "                                    ,x.type" +
                "                       )" +
                "               GROUP BY order_id" +
                "           ) x" +
                " ON      z.id = x.order_id INNER" +
                " JOIN    cl_loan cl" +
                " ON      cl.loanno = ro.no" +
                " INNER JOIN serial_no sn" +
                " ON      sn.businessno = ro.no" +
                " WHERE" +
                "         c.is_deleted = 0" +
                " AND     ro.ds =  " + ds +
                " AND     c.ds =  " + ds +
                " AND     cl.ds =  " + ds +
                " AND     sn.ds =  " + ds +
                " AND     roi.ds =  " + ds +
                " GROUP BY date(z.rent_start_date)" +
                "          ,ro.mini_type" +
                "          ,c.rate_config_type" +
                "          ,IF(ext_json IS NOT NULL AND c.rate_config_type = 10, '1', '0')" +
                "          ,cl.riskOpinion" +
                "          ,sn.riskStrategy" +
                "          ,cl.artificialAuditorId" +
                "          ,IF(settle_date is not null, '1','0')" +
                "           ,auto_renewal" +
                " ORDER BY date(z.rent_start_date));";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    private BigDecimal stringToDecimal(String val) {
        if ("\\N".equals(val)) {
            val = "0";
        }
        return new BigDecimal(val).setScale(4,RoundingMode.HALF_UP);
    }

    
}


