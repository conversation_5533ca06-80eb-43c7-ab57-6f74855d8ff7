package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.order.ScreenRiskDO;
import qnvip.data.overview.service.order.ScreenRiskService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;

/**
 * 碎屏险业务
 * <AUTHOR>
 * @Date 2021/10/8 11:26 上午
 */
@Component
@RequiredArgsConstructor
public class ScreenRiskBusiness {

    private final OdpsUtil odpsUtil;
    private final ScreenRiskService screenRiskService;


    /**
     * 启动碎屏险计算
     * @param countDay
     * @param sTime
     * @param eTime
     */
    public void runScreenCount(String dsStr,LocalDateTime countDay,String sTime, String eTime){
        ScreenRiskDO screenRiskDO = new ScreenRiskDO();
        screenRiskDO.setCountDay(countDay);
        screenGMVAndCount(dsStr,screenRiskDO,sTime,eTime);
        screenOrderCount(dsStr,screenRiskDO,sTime,eTime);
        screenPayCount(dsStr,screenRiskDO,sTime,eTime);
        screenRiskService.saveOrUpdate(screenRiskDO);
    }


    /**
     * 已签收订单数 及 GMV
     * @param sTime
     * @param eTime
     * @return
     */
    public ScreenRiskDO screenGMVAndCount(String dsStr,ScreenRiskDO screenRiskDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select NVL(sum(c.insurance_amt),0) orderGMV," +
                " NVL(sum(c.mouth_charge),0) costPrice ,count(1) as num from  rent_order a inner join rent_order_item" +
                " b on a.id = b.order_id inner join rent_order_logistics d on d.id = b.logistics_id inner join " +
                " rent_order_insurance c on b.order_id = c.order_id where a.biz_type = 2 and a.type = 1 and " +
                " a.merchant_id = 100 and a.is_screen_risk_payed = 1 and d.sign_time is not null and " +
                " b.item_type = 1 and c.giving_flag = 0 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        sb.append(" and d.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and d.sign_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and d.sign_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        screenRiskDO.setSaleCount(Long.parseLong(recordList.get(0).getString("num")));
        screenRiskDO.setOrderGmv(new BigDecimal(recordList.get(0).getString("ordergmv")));
        screenRiskDO.setCostPrice(new BigDecimal(recordList.get(0).getString("costprice")));
        return screenRiskDO;
    }


    /**
     * 碎屏险下单量
     * @param sTime
     * @param eTime
     * @return
     */
    public ScreenRiskDO screenOrderCount(String dsStr,ScreenRiskDO screenRiskDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from  rent_order a inner join rent_order_item b " +
                " on a.id = b.order_id inner join rent_order_insurance c on b.order_id = c.order_id where " +
                " a.biz_type = 2 and a.merchant_id = 100 and a.type = 1 and a.is_screen_risk_payed = 1 and " +
                " b.item_type = 1 and c.giving_flag = 0 and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.create_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.create_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        screenRiskDO.setOrderCount(Long.parseLong(recordList.get(0).getString("num")));
        return screenRiskDO;
    }


    /**
     * 碎屏险支付单量
     * @param sTime
     * @param eTime
     * @return
     */
    public ScreenRiskDO screenPayCount(String dsStr,ScreenRiskDO screenRiskDO,String sTime, String eTime) {
        StringBuilder sb = new StringBuilder(" select count(1) as num from  rent_order a inner join rent_order_item b " +
                " on a.id = b.order_id inner join rent_order_insurance c on b.order_id = c.order_id where " +
                " a.biz_type = 2 and a.type = 1 and a.merchant_id = 100 and a.is_screen_risk_payed = 1 and " +
                " b.item_type = 1 and c.giving_flag = 0 and a.payment_time is not null and a.is_deleted = 0 ");
        sb.append(" and a.ds = ").append(dsStr);
        sb.append(" and b.ds = ").append(dsStr);
        sb.append(" and c.ds = ").append(dsStr);
        if (sTime != null) {
            sb.append(" and a.payment_time >= ").append("'").append(sTime).append("'");
        }
        if (eTime != null) {
            sb.append(" and a.payment_time <= ").append("'").append(eTime).append("'");
        }
        sb.append(";");
        List<Record> recordList = odpsUtil.querySql(sb.toString());
        screenRiskDO.setPayCount(Long.parseLong(recordList.get(0).getString("num")));
        return screenRiskDO;
    }



}