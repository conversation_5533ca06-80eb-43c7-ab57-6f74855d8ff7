package qnvip.data.overview.business.risk;

import cn.hutool.json.JSONUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.risk.RiskQuotientDO;
import qnvip.data.overview.service.risk.RiskQuotientService;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class RiskQuotientBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final Integer PAGE_SIZE = 5000;


    private final OdpsUtil odpsUtil;
    private final RiskQuotientService riskQuotientService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("RiskQuotientBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    public void execCount(String ds) {

        try {
            riskQuotientService.delete();
            Integer size = getCount(ds);
            int times = size / PAGE_SIZE;
            if (size % PAGE_SIZE != 0) {
                times += 1;
            }
            int startPage = 0;
            for (int i = 0; i < times; i++) {
                int finalStartPage = startPage;
                threadPoolExecutor.execute(() -> {
                    List<RiskQuotientDO> list = countRepayTask(ds, finalStartPage);
                    riskQuotientService.saveBatch(list);
                });
                startPage++;
            }
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
    }




    /**
     * 风控导流商
     *
     * @param ds
     */
    private List<RiskQuotientDO> countRepayTask(String ds, int startPage) {
        try {
            String sql = "select customer_id,\n" +
                    "    date(create_time) create_time,\n" +
                    "    mini_type,\n" +
                    "    no,\n" +
                    "       (CASE " +
                    "            WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝' " +
                    "            WHEN mini_type IN (6, 8, 10, 11) THEN '微信' " +
                    "            WHEN mini_type = 2 THEN '字节跳动' " +
                    "            WHEN mini_type = 3 THEN 'app' END)                                      platform, " +
                    "      if(nvl(quotient_name, '') ='','未标记上导流商',quotient_name) quotient_name,\n" +
                    "    if(nvl(scene, '')='','未标记上场景值',scene) scene,\n" +
                    "    risk_status,\n" +
                    "    risk_strategy,\n" +
                    "    if(nvl(quotient_scene_name, '')='','未标记上渠道名称',quotient_scene_name) quotient_scene_name,\n" +
                    "    min(tag) tag\n" +
                    " from (\n" +
                    "    select first_value(customerid) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc," +
                    " cl.createtime desc) customer_id,\n" +
                    "    first_value(cl.createtime) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) create_time,\n" +
                    "        first_value(mini_type) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) mini_type,\n" +
                    "    first_value(cl.quotientname) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_name,\n" +
                    "        first_value(no) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) no,\n" +
                    "    first_value(cl.scene) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) scene,\n" +
                    "    first_value (cl.artificialAuditStatus) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_status,\n" +
                    "    first_value(riskStrategy) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_strategy,\n" +
                    "        first_value (cl.quotientscenename) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_scene_name,\n" +
                    "           wm_concat( '%', concat_ws('&',concat_ws('_', concat_ws('_', concat_ws('_', b.scene, xsShow), result),masterModel), tag)) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='', 99, 0) asc, cl.createtime desc) tag\n" +
                    "    from cl_loan cl\n" +
                    "    left join serial_no sn\n" +
                    "    on cl.loanno = sn.businessno\n" +
                    "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid\n" +
                    "        inner join rent_order c on cl.loanno = c.no\n\n" +
                    "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id\n" +
                    "    where cl.ds = " + ds + "" +
                    "    and sn.ds = " + ds + "" +
                    "    and a.ds =" + ds + "" +
                    "    and b.ds = " + ds + "" +
                    "    and c.ds = " + ds + "" +
                    "    and cl.businesschannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34) " +
                    "    and date (cl.createtime) >=date_sub(getdate()" +
                    "     , 30)" +
                    "   and date (cl.createtime) <= date_sub (getdate(),0)" +
                    "    and parentno = ''\n" +
                    "       and quotientname<> ''\n" +
                    "       and quotientscene is not null \n" +
                    "         and quotientscene <> '' \n" +
                    "    )\n" +
                    " group by customer_id, create_time, quotient_name, scene, risk_status, quotient_scene_name," +
                    "   risk_strategy,mini_type,no\n" +
                    " order by customer_id" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";

            List<Record> records = odpsUtil.querySql(sql);
            List<RiskQuotientDO> resList = new ArrayList<>();
            records.forEach(k -> {
                LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("create_time"), dateFormatter),
                        LocalTime.MIN);
                Long customerId = Long.valueOf(k.getString("customer_id"));
                String platform = k.getString("platform");
                String no = k.getString("no");
                Integer miniType = Integer.valueOf(k.getString("mini_type"));
                String quotientName = k.getString("quotient_name");
                String scene = k.getString("scene");
                Integer riskStatus = Integer.valueOf(k.getString("risk_status"));
                String riskStrategy = k.getString("risk_strategy");
                String channelName = k.getString("quotient_scene_name");
                String tag = k.getString("tag");
                String[] split = tag.split("%");
                Set<String> collect = Arrays.stream(split).collect(Collectors.toSet());
                String jsonStr = JSONUtil.toJsonStr(collect);
                RiskQuotientDO repayDO = new RiskQuotientDO();
                repayDO.setCountDay(countDay);
                repayDO.setNo(no);
                repayDO.setCustomerId(customerId);
                repayDO.setPlatform(platform);
                repayDO.setBusinessChannel(miniType);
                repayDO.setQuotientName(quotientName);
                repayDO.setRiskStrategy(riskStrategy);
                repayDO.setChannelName(channelName);
                repayDO.setRiskStatus(riskStatus);
                repayDO.setScene(scene);
                repayDO.setTag(jsonStr);
                resList.add(repayDO);
            });
            return resList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 跑历史数据
     *
     * @param ds
     */
    private List<RiskQuotientDO> countOldTask(String ds, int startPage) {
        try {
            String sql = "select customer_id,\n" +
                    "    date(create_time) create_time,\n" +
                    "    mini_type,\n" +
                    "    no,\n" +
                    "       (CASE " +
                    "            WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝' " +
                    "            WHEN mini_type IN (6, 8, 10, 11) THEN '微信' " +
                    "            WHEN mini_type = 2 THEN '字节跳动' " +
                    "            WHEN mini_type = 3 THEN 'app' END)                                      platform, " +
                    "      if(nvl(quotient_name, '') ='','未标记上导流商',quotient_name) quotient_name,\n" +
                    "    if(nvl(scene, '')='','未标记上场景值',scene) scene,\n" +
                    "    risk_status,\n" +
                    "    risk_strategy,\n" +
                    "    if(nvl(quotient_scene_name, '')='','未标记上渠道名称',quotient_scene_name) quotient_scene_name,\n" +
                    "    min(tag) tag\n" +
                    " from (\n" +
                    "    select first_value(customerid) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) customer_id,\n" +
                    "    first_value(cl.createtime) over (partition by customerid , date (cl.createtime) ,cl.quotientname order by if" +
                    "(cl.quotientname='',99,0) asc,cl.createtime desc) create_time,\n" +
                    "        first_value(mini_type) over (partition by customerid , date (cl.createtime) ,cl.quotientname order by if" +
                    "(cl.quotientname='',99,0) asc,cl.createtime desc) mini_type,\n" +
                    "        first_value(no) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) no,\n" +
                    "    first_value(cl.quotientname) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_name,\n" +
                    "    first_value(cl.scene) over (partition by customerid , date (cl.createtime) ,cl.quotientname order by if(cl" +
                    ".quotientname='',99,0) asc,cl.createtime desc) scene,\n" +
                    "    first_value (cl.artificialAuditStatus) over (partition by customerid , date (cl.createtime) " +
                    ",cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_status,\n" +
                    "    first_value(riskStrategy) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_strategy,\n" +
                    "        first_value (cl.quotientscenename) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_scene_name,\n" +
                    "            wm_concat( '%', concat_ws('&',concat_ws('_', concat_ws('_', concat_ws('_', b.scene, xsShow), result),masterModel), tag)) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='', 99, 0) asc, cl.createtime desc) tag\n" +
                    "    from cl_loan cl\n" +
                    "    left join serial_no sn\n" +
                    "    on cl.loanno = sn.businessno\n" +
                    "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid\n" +
                    "        inner join rent_order c on cl.loanno = c.no\n\n" +
                    "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id\n" +
                    "    where cl.ds = " + ds + "" +
                    "    and sn.ds = " + ds + "" +
                    "    and a.ds =" + ds + "" +
                    "    and b.ds = " + ds + "" +
                    "    and c.ds = " + ds + "" +
                    "    and cl.businesschannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34) " +
                    "    and date (cl.createtime) >='2022-01-01'" +
                    "    and parentno = ''\n" +
                    "       and quotientname<> ''\n" +
                    "       and quotientscene is not null \n" +
                    "         and quotientscene <> '' \n" +
                    "    )\n" +
                    " group by customer_id, create_time, quotient_name, scene, risk_status,quotient_scene_name, " +
                    "   risk_strategy,mini_type,no\n" +
                    " order by customer_id" +
                    " limit " + startPage * PAGE_SIZE + "," + PAGE_SIZE + "; ";

            List<Record> records = odpsUtil.querySql(sql);
            List<RiskQuotientDO> resList = new ArrayList<>();
            records.forEach(k -> {
                LocalDateTime countDay = LocalDateTime.of(LocalDate.parse(k.getString("create_time"), dateFormatter),
                        LocalTime.MIN);
                Long customerId = Long.valueOf(k.getString("customer_id"));
                String platform = k.getString("platform");
                Integer miniType = Integer.valueOf(k.getString("mini_type"));
                String quotientName = k.getString("quotient_name");
                String scene = k.getString("scene");
                String no = k.getString("no");
                Integer riskStatus = Integer.valueOf(k.getString("risk_status"));
                String channelName = k.getString("quotient_scene_name");
                String riskStrategy = k.getString("risk_strategy");
                String tag = k.getString("tag");
                String[] split = tag.split("%");
                Set<String> collect = Arrays.stream(split).collect(Collectors.toSet());
                String jsonStr = JSONUtil.toJsonStr(collect);
                RiskQuotientDO repayDO = new RiskQuotientDO();
                repayDO.setCountDay(countDay);
                repayDO.setNo(no);
                repayDO.setCustomerId(customerId);
                repayDO.setPlatform(platform);
                repayDO.setChannelName(channelName);
                repayDO.setBusinessChannel(miniType);
                repayDO.setQuotientName(quotientName);
                repayDO.setRiskStrategy(riskStrategy);
                repayDO.setRiskStatus(riskStatus);
                repayDO.setScene(scene);
                repayDO.setTag(jsonStr);
                resList.add(repayDO);
            });
            return resList;
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     *
     *
     * @param ds
     */
    private Integer getCount(String ds) {

        String sql = "select count(*) num from (select customer_id,\n" +
                "    date(create_time) create_time,\n" +
                "    mini_type,\n" +
                "    no,\n" +
                "       (CASE " +
                "            WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝' " +
                "            WHEN mini_type IN (6, 8, 10, 11) THEN '微信' " +
                "            WHEN mini_type = 2 THEN '字节跳动' " +
                "            WHEN mini_type = 3 THEN 'app' END)                                      platform, " +
                "      if(nvl(quotient_name, '') ='','未标记上导流商',quotient_name) quotient_name,\n" +
                "    if(nvl(scene, '')='','未标记上场景值',scene) scene,\n" +
                "    risk_status,\n" +
                "    risk_strategy,\n" +
                "    if(nvl(quotient_scene_name, '')='','未标记上渠道名称',quotient_scene_name) quotient_scene_name,\n" +
                "    min(tag) tag\n" +
                " from (\n" +
                "    select first_value(customerid) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) customer_id,\n" +
                "    first_value(cl.createtime) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) create_time,\n" +
                "        first_value(no) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) no,\n" +
                "        first_value(mini_type) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) mini_type,\n" +
                "    first_value(cl.quotientname) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_name,\n" +
                "    first_value(cl.scene) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) scene,\n" +
                "    first_value (cl.artificialAuditStatus) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_status,\n" +
                "    first_value(riskStrategy) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_strategy,\n" +
                "        first_value (cl.quotientscenename) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_scene_name,\n" +
                "            wm_concat( '%', concat_ws('&',concat_ws('_', concat_ws('_', concat_ws('_', b.scene, xsShow), result),masterModel), tag)) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='', 99, 0) asc, cl.createtime desc) tag\n" +
                "    from cl_loan cl\n" +
                "    left join serial_no sn\n" +
                "    on cl.loanno = sn.businessno\n" +
                "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid\n" +
                "        inner join rent_order c on cl.loanno = c.no\n\n" +
                "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id\n" +
                "    where cl.ds = " + ds + "" +
                "    and sn.ds = " + ds + "" +
                "    and a.ds =" + ds + "" +
                "    and b.ds = " + ds + "" +
                "    and c.ds = " + ds + "" +
                "    and cl.businesschannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34) " +
                "    and date (cl.createtime) >=date_sub(getdate()" +
                "     , 30)" +
                "   and date (cl.createtime) <= date_sub (getdate(),0)" +
                "    and parentno = ''\n" +
                "       and quotientname<> ''\n" +
                "       and quotientscene is not null \n" +
                "         and quotientscene <> '' \n" +
                "    )\n" +
                " group by customer_id, create_time, quotient_name, scene, risk_status,quotient_scene_name, " +
                "   risk_strategy,mini_type,no" +
                " order by customer_id )" +
                "                 ;";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }

    /**
     * 风控经营
     *
     * @param ds
     */
    private Integer getOldCount(String ds) {

        String sql = "select count(*) num from (select customer_id,\n" +
                "    date(create_time) create_time,\n" +
                "    mini_type,\n" +
                "     no,\n" +
                "       (CASE " +
                "            WHEN mini_type IN (1, 4, 5, 7, 9, 126) THEN '支付宝' " +
                "            WHEN mini_type IN (6, 8, 10, 11) THEN '微信' " +
                "            WHEN mini_type = 2 THEN '字节跳动' " +
                "            WHEN mini_type = 3 THEN 'app' END)                                      platform, " +
                "      if(nvl(quotient_name, '') ='','未标记上导流商',quotient_name) quotient_name,\n" +
                "    if(nvl(scene, '')='','未标记上场景值',scene) scene,\n" +
                "    risk_status,\n" +
                "    risk_strategy,\n" +
                "    if(nvl(quotient_scene_name, '')='','未标记上渠道名称',quotient_scene_name) quotient_scene_name,\n" +
                "    min(tag) tag\n" +
                " from (\n" +
                "    select first_value(customerid) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) customer_id,\n" +
                "    first_value(cl.createtime) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) create_time,\n" +
                "        first_value(no) over (partition by customerid, date (cl.createtime) ,cl.quotientname  order by if(cl.quotientname='',99,0) asc,cl.createtime desc) no,\n" +
                "        first_value(mini_type) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) mini_type,\n" +
                "    first_value(cl.quotientname) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_name,\n" +
                "    first_value(cl.scene) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) scene,\n" +
                "    first_value (cl.artificialAuditStatus) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_status,\n" +
                "    first_value(riskStrategy) over (partition by customerid , date (cl.createtime),cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) risk_strategy,\n" +
                "        first_value (cl.quotientscenename) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='',99,0) asc,cl.createtime desc) quotient_scene_name,\n" +
                "            wm_concat( '%', concat_ws('&',concat_ws('_', concat_ws('_', concat_ws('_', b.scene, xsShow), result),masterModel), tag)) over (partition by customerid, date (cl.createtime) ,cl.quotientname order by if(cl.quotientname='', 99, 0) asc, cl.createtime desc) tag\n" +
                "    from cl_loan cl\n" +
                "    left join serial_no sn\n" +
                "    on cl.loanno = sn.businessno\n" +
                "    left join rc_risk_access_rule_result_2023 a on sn.id = a.serialnoid\n" +
                "        inner join rent_order c on cl.loanno = c.no\n\n" +
                "    left join rc_risk_strategy_rule_set b on a.ruleid = b.id\n" +
                "    where cl.ds = " + ds + "" +
                "    and sn.ds = " + ds + "" +
                "    and a.ds =" + ds + "" +
                "    and b.ds = " + ds + "" +
                "    and c.ds = " + ds + "" +
                "    and cl.businesschannel in (1,7,10,11,12,14,15,23,25,26,28,27,29,31,32,34) " +
                "    and date (cl.createtime) >='2022-01-01'" +
                "    and parentno = ''\n" +
                "       and quotientname<> ''\n" +
                "       and quotientscene is not null \n" +
                "         and quotientscene <> '' \n" +
                "    )\n" +
                " group by customer_id, create_time, quotient_name, scene, risk_status,quotient_scene_name,no, " +
                "   risk_strategy,mini_type )" +
                "                 ;";
        List<Record> records = odpsUtil.querySql(sql);
        return Integer.valueOf(records.get(0).getString("num"));
    }


}


