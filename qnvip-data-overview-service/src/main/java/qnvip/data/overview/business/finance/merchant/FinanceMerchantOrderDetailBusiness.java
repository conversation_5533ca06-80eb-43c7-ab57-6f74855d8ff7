package qnvip.data.overview.business.finance.merchant;

import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.business.web.FishOrderBusiness;
import qnvip.data.overview.domain.finance.FinanceMerchantOrderDetailDO;
import qnvip.data.overview.service.finance.FinanceMerchantOrderDetailService;
import qnvip.data.overview.util.OdpsUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * create by gw on 2022/3/21
 */
@Slf4j
@Service
@RequiredArgsConstructor
@Deprecated
public class FinanceMerchantOrderDetailBusiness {

    private static final int PAGE_SIZE = 10000;
    private static final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final FinanceMerchantOrderDetailService financeOrderDetailService;

    public void countOrderDetail(String ds) {
        financeOrderDetailService.deleteAll();
        String prefix1 = " select count(1) count from (";

        String prefix2 = "select so.order_uid,\n" +
                "       mobile,\n" +
                "       real_name,\n" +
                "       id_card_no,\n" +
                "       so.status,\n" +
                "       date(rent_start_time)                                            rent_start_date,\n" +
                "       mini_type,\n" +
                "       get_json_object(min(soi.spec_snapshot), '$.purchasePrice') act_supply_price,\n" +
                "       min(total_rent_amount)                                     total_rent_amount,\n" +
                "       min(real_pay_cash_deposit)                                 total_cash_deposit,\n" +
                "       if(min(sod.status) = 20, min(pre_discount_amt), 0)                  before_discount_money,\n" +
                "       sum(if(srp.status = 10, real_repay_amount, 0))             real_repay_amount,\n" +
                "       sum(if(srp.status = 10, cash_deposit_deduct_amount, 0))    cash_deposit_deduct_amount,\n" +
                "       if(min(current_overdue_days) > 0, 1, 0)                             is_overdue,\n" +
                "       sum(if(current_overdue_days > 0 and srp.status <> 10, payable_amount, 0))               overdue_surplus_rent_amt,\n" +
                "       (min(total_rent_amount)-sum(if(srp.status = 10, real_repay_amount, 0))-sum(if(current_overdue_days > 0 and srp.status <> 10, payable_amount, 0)))\n" +
                "                   surplus_rent_amt,\n" +
                "       if(min(current_overdue_days) > 0, min(sof.surplus_cash_deposit), 0) no_refund_bondamt,\n" +
                "       sum(if(srp.status <> 10, overdue_retention_fine, 0)) overdue_retention_fine," +
                "       premium_real_amt ," +
                "       premium_refund_amt";


        String body = " from sh_order so\n" +
                "         inner join sh_repayment_plan srp on so.order_uid = srp.order_uid and srp.is_deleted = 0\n" +
                "         inner join sh_order_finance sof on so.order_uid = sof.order_uid and sof.is_deleted = 0\n" +
                "         inner join sh_order_item soi on so.order_uid = soi.order_uid and soi.is_deleted = 0\n" +
                "         inner join sh_customer sc on so.customer_id = sc.id and sc.is_deleted = 0\n" +
                "         left join (select order_uid, pre_discount_amt, status\n" +
                "                    from sh_order_discount\n" +
                "                    where is_deleted = 0\n" +
                "                      and ds = " + ds + ") sod on sod.order_uid = so.order_uid\n" +
                "         left join (select order_uid, current_overdue_days\n" +
                "                    from sh_order_overdue\n" +
                "                    where is_deleted = 0\n" +
                "                      and ds = " + ds + ") soo on so.order_uid = soo.order_uid\n" +
                "where so.ds = \n" + ds +
                "  and sof.ds = \n" + ds +
                "  and srp.ds = \n" + ds +
                "  and sc.ds = \n" + ds +
                "  and soi.ds = \n" + ds +
                "  and so.status in(90,115,120)"  +
                "  and so.is_deleted = 0\n" +
                "  and so.closing_time is null\n" +
                " group by so.order_uid, mobile, real_name, id_card_no, so.status, rent_start_time, mini_type";

        String countSql = prefix1 + prefix2 + body + ");";
        List<Record> countRecords = odpsUtil.querySql(countSql);
        int count = Integer.parseInt(countRecords.get(0).getString("count"));
        //计算页数
        int pageNO = count / PAGE_SIZE;
        if (count % PAGE_SIZE > 0) {
            pageNO++;
        }
        for (int i = 0; i < pageNO; i++) {
            String suffix = " order by so.order_uid asc limit " + i * PAGE_SIZE + "," + PAGE_SIZE;
            String detailSql = prefix2 + body + suffix + ";";
            FishOrderBusiness.threadPoolExecutor.execute(() -> queryAndSaveDetail(detailSql));
        }

    }


    private void queryAndSaveDetail(String detailSql) {
        List<Record> records = odpsUtil.querySql(detailSql);
        List<FinanceMerchantOrderDetailDO> resList = new ArrayList<>(records.size());
        records.forEach(k -> {
            LocalDateTime rentStartDate = LocalDateTime.of(LocalDate.parse(k.getString("rent_start_date")),
                    LocalTime.MIN);
            Integer miniType = Integer.valueOf(k.getString("mini_type"));
            // Integer financeTemplateUid = Integer.valueOf(k.getString("finance_template_uid"));
            Integer isOverdue = Integer.valueOf(k.getString("is_overdue"));
            Integer status = Integer.valueOf(k.getString("status"));
            String orderNo = k.getString("order_uid");
            String mobile = k.getString("mobile");
            String realName = k.getString("real_name");
            String idCardNo = k.getString("id_card_no");
            //协议总租金
            BigDecimal actSupplyPrice = stringToDecimal(k.getString("act_supply_price"));
            BigDecimal rentTotal = stringToDecimal(k.getString("total_rent_amount"));
            BigDecimal actBondAmt = stringToDecimal(k.getString("total_cash_deposit"));
            // BigDecimal realRepayCapital = stringToDecimal(k.getString("real_repay_capital"));
            BigDecimal beforeDiscountMoney = stringToDecimal(k.getString("before_discount_money"));
            //主订单保证金抵扣租金
            BigDecimal realRepayAmount = stringToDecimal(k.getString("real_repay_amount"));
            BigDecimal cashDepositDeductAmount = stringToDecimal(k.getString("cash_deposit_deduct_amount"));
            // BigDecimal couponDiscountMoney = stringToDecimal(k.getString("coupon_discount_money"));
            BigDecimal surplusRentAmt = stringToDecimal(k.getString("surplus_rent_amt"));
            BigDecimal overdueSurplusRentAmt = stringToDecimal(k.getString("overdue_surplus_rent_amt"));
            BigDecimal noRefundBondAmt = stringToDecimal(k.getString("no_refund_bondamt"));
            BigDecimal overdueRetentionFine = stringToDecimal(k.getString("overdue_retention_fine"));
            // 溢价金
            BigDecimal premiumRealAmt = stringToDecimal(k.getString("premium_real_amt"));
            BigDecimal premiumRefundAmt = stringToDecimal(k.getString("premium_refund_amt"));

            FinanceMerchantOrderDetailDO detailDO = new FinanceMerchantOrderDetailDO();
            detailDO.setBondAmt(actBondAmt);
            detailDO.setBeforeDiscount(beforeDiscountMoney);
            detailDO.setActRentAmt(realRepayAmount);
            detailDO.setBondDeductRent(cashDepositDeductAmount);
            // detailDO.setAfterDiscount(couponDiscountMoney);
            detailDO.setSurplusRentAmt(surplusRentAmt);
            detailDO.setOverdueSurplusRentAmt(overdueSurplusRentAmt);
            detailDO.setOverdueStatus(isOverdue);
            detailDO.setNoRefundBondAmt(noRefundBondAmt);
            detailDO.setOverdueFine(overdueRetentionFine);

            detailDO.setRentStartDate(rentStartDate);
            detailDO.setMiniType(miniType);
            // detailDO.setFinanceType(financeTemplateUid);
            detailDO.setOrderNo(orderNo);
            detailDO.setCustomerName(realName);
            detailDO.setCustomerIdCardNo(idCardNo);
            detailDO.setCustomerMobile(mobile);
            detailDO.setOrderStatus(status);
            String dsStr = LocalDate.now().minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            detailDO.setDs(dsStr);
            detailDO.setActSupplyPrice(actSupplyPrice);
            detailDO.setTotalRent(rentTotal);
            resList.add(detailDO);

        });
        financeOrderDetailService.saveBatch(resList, 2000);
    }


    private BigDecimal stringToDecimal(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return new

                BigDecimal(val).

                setScale(2, RoundingMode.HALF_UP);
    }

    private LocalDateTime stringTOLocalDate(String val) {
        if (val.equals("\\N")) {
            return null;
        }
        return LocalDateTime.of(LocalDate.parse(val), LocalTime.MIN);
    }

}