package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateOrderConversionDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.order.OperateOrderConversionService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/29
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateOrderConversionTotalBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateOrderConversionService operateOrderConversionService;

    void initMap(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map,
                 OperateOrderConversionDO orderDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do = Maps.newHashMap();
        Map<Integer, OperateOrderConversionDO> type2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(orderDO.getMiniType())) {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            domain.setCountDay(countDay);
            domain.setMiniType(orderDO.getMiniType());
            domain.setMerchantId(orderDO.getMerchantId());
            domain.setType(orderDO.getType());
            type2Do.put(orderDO.getType(), domain);
            merchantId2Do.put(orderDO.getMerchantId(), type2Do);
        } else {
            merchantId2Do = miniType2Map.get(orderDO.getMiniType());
            if (!merchantId2Do.containsKey(orderDO.getMerchantId())) {
                OperateOrderConversionDO domain = new OperateOrderConversionDO();
                domain.setCountDay(countDay);
                domain.setMiniType(orderDO.getMiniType());
                domain.setMerchantId(orderDO.getMerchantId());
                domain.setType(orderDO.getType());
                type2Do.put(orderDO.getType(), domain);
                merchantId2Do.put(orderDO.getMerchantId(), type2Do);
            } else {
                type2Do = merchantId2Do.get(orderDO.getMerchantId());
                if (!type2Do.containsKey(orderDO.getType())) {
                    OperateOrderConversionDO domain = new OperateOrderConversionDO();
                    domain.setCountDay(countDay);
                    domain.setMiniType(orderDO.getMiniType());
                    domain.setMerchantId(orderDO.getMerchantId());
                    domain.setType(orderDO.getType());
                    type2Do.put(orderDO.getType(), domain);
                    merchantId2Do.put(orderDO.getMerchantId(), type2Do);
                }
            }
        }
        miniType2Map.put(orderDO.getMiniType(), merchantId2Do);
    }


    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        try {
            Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map = new HashMap<>();
            CompletableFuture<List<OperateOrderConversionDO>> f1 = CompletableFuture.supplyAsync(()->getOrderUvCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f2 = CompletableFuture.supplyAsync(()->getRiskPassUvCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f3 = CompletableFuture.supplyAsync(()->getPayUvCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f4 = CompletableFuture.supplyAsync(()->getSendUvCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f5 = CompletableFuture.supplyAsync(()->getSignCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f6 = CompletableFuture.supplyAsync(()->getRiskControlCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f7 = CompletableFuture.supplyAsync(()->getPayCountByCreateTm(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f8 = CompletableFuture.supplyAsync(()->getSendCountOther(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f9 = CompletableFuture.supplyAsync(()->getSignCountOther(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f10 = CompletableFuture.supplyAsync(()->getOrderUvInvalidCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f11 =
                    CompletableFuture.supplyAsync(()->getUvInvalidRiskPassCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f12 =
                    CompletableFuture.supplyAsync(()->getOrderUvInvalidPayCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f13 =
                    CompletableFuture.supplyAsync(()->getOrderUvInvalidSignCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f14 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f15 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidRiskPayCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f16 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidPayCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f17 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidSignCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f18 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualityCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f19 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualityRiskPassCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f20 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualityPayCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f21 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualitySignCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f22 =
                    CompletableFuture.supplyAsync(()->getUvQuality(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f23 =
                    CompletableFuture.supplyAsync(()->getUvValid(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f24 =
                    CompletableFuture.supplyAsync(()->getUvInvalid(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f25 =
                    CompletableFuture.supplyAsync(()->getUv(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f26 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualitySendCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f27 =
                    CompletableFuture.supplyAsync(()->getOrderUvInvalidSendCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f28 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidSendCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f29 =
                    CompletableFuture.supplyAsync(()->getOrderUvValidRiskCtlCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f30 =
                    CompletableFuture.supplyAsync(()->getOrderUvInValidRiskCtlCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f31 =
                    CompletableFuture.supplyAsync(()->getOrderUvQualityRiskCtlCount(ds));
            CompletableFuture<List<OperateOrderConversionDO>> f32 =
                    CompletableFuture.supplyAsync(()->getRiskPassCountOther(ds));
            CompletableFuture.
                    allOf(f1, f2, f3, f4, f5, f6, f7, f8, f9, f10, f11, f12, f13, f14
                            , f15, f16, f17, f18, f19, f20, f21, f22, f23, f24, f25,
                            f26, f27, f28, f29, f30, f31, f32).join();

            List<OperateOrderConversionDO> orderUvCountList = f1.get();
            List<OperateOrderConversionDO> riskPassUvCountList = f2.get();
            List<OperateOrderConversionDO> payUvCountList = f3.get();
            List<OperateOrderConversionDO> sendUvCount = f4.get();
            List<OperateOrderConversionDO> signCount = f5.get();
            List<OperateOrderConversionDO> riskControlCount = f6.get();
            List<OperateOrderConversionDO> payCountByCreateTm = f7.get();
            List<OperateOrderConversionDO> sendCountOther = f8.get();
            List<OperateOrderConversionDO> signCountOther = f9.get();
            List<OperateOrderConversionDO> orderUvInvalidCount = f10.get();
            List<OperateOrderConversionDO> uvInvalidRiskPassCount = f11.get();
            List<OperateOrderConversionDO> orderUvInvalidPayCount = f12.get();
            List<OperateOrderConversionDO> orderUvInvalidSignCount = f13.get();
            List<OperateOrderConversionDO> orderUvValidCount = f14.get();
            List<OperateOrderConversionDO> orderUvValidRiskCount = f15.get();
            List<OperateOrderConversionDO> orderUvValidPayCount = f16.get();
            List<OperateOrderConversionDO> orderUvValidSignCount = f17.get();
            List<OperateOrderConversionDO> orderUvQualityList = f18.get();
            List<OperateOrderConversionDO> orderUvQualityRiskPassList = f19.get();
            List<OperateOrderConversionDO> orderUvQualityPayList = f20.get();
            List<OperateOrderConversionDO> orderUvQualitySignList = f21.get();
            List<OperateOrderConversionDO> uvQuality = f22.get();
            List<OperateOrderConversionDO> uvValid = f23.get();
            List<OperateOrderConversionDO> uvInvalid = f24.get();
            List<OperateOrderConversionDO> uv = f25.get();
            List<OperateOrderConversionDO> sendUvQualityCount = f26.get();
            List<OperateOrderConversionDO> sendUvInValidCount = f27.get();
            List<OperateOrderConversionDO> sendUvValidCount = f28.get();
            List<OperateOrderConversionDO> riskControlValidCount = f29.get();
            List<OperateOrderConversionDO> riskControlInValidCount = f30.get();
            List<OperateOrderConversionDO> riskControlQualityCount = f31.get();
            List<OperateOrderConversionDO> riskPassCountOther = f32.get();

            initUvCount(miniType2Map, uv);
            initOrderCount(miniType2Map, orderUvCountList);
            initRiskPassCount(miniType2Map, riskPassUvCountList);
            initPayCount(miniType2Map, payUvCountList);
            initSendCount(miniType2Map, sendUvCount);
            initRiskCtlCount(miniType2Map, riskControlCount);
            initSignCount(miniType2Map, signCount);

            for (OperateOrderConversionDO coreDO : payCountByCreateTm) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                        miniType2Map.get(coreDO.getMiniType());
                Map<Integer, OperateOrderConversionDO> type2Do =
                        merchantId2Do.get(coreDO.getMerchantId());
                OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
                operateOrderConversionDO.setPayCountOther(coreDO.getPayCountOther());
            }
            for (OperateOrderConversionDO coreDO : sendCountOther) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                        miniType2Map.get(coreDO.getMiniType());
                Map<Integer, OperateOrderConversionDO> type2Do =
                        merchantId2Do.get(coreDO.getMerchantId());
                OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
                operateOrderConversionDO.setSendCountOther(coreDO.getSendCountOther());
            }
            for (OperateOrderConversionDO coreDO : signCountOther) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                        miniType2Map.get(coreDO.getMiniType());
                Map<Integer, OperateOrderConversionDO> type2Do =
                        merchantId2Do.get(coreDO.getMerchantId());
                OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
                operateOrderConversionDO.setSignCountOther(coreDO.getSignCountOther());
            }

            for (OperateOrderConversionDO coreDO : riskPassCountOther) {
                initMap(miniType2Map, coreDO);
                Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                        miniType2Map.get(coreDO.getMiniType());
                Map<Integer, OperateOrderConversionDO> type2Do =
                        merchantId2Do.get(coreDO.getMerchantId());
                OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
                operateOrderConversionDO.setRiskPassCountOther(coreDO.getRiskPassCountOther());
            }
            initOrderCount(miniType2Map, orderUvInvalidCount);
            initRiskPassCount(miniType2Map, uvInvalidRiskPassCount);
            initPayCount(miniType2Map, orderUvInvalidPayCount);
            initSignCount(miniType2Map, orderUvInvalidSignCount);
            initUvCount(miniType2Map, uvInvalid);
            initSendCount(miniType2Map, sendUvValidCount);
            initRiskCtlCount(miniType2Map, riskControlValidCount);

            initOrderCount(miniType2Map, orderUvValidCount);
            initRiskPassCount(miniType2Map, orderUvValidRiskCount);
            initPayCount(miniType2Map, orderUvValidPayCount);
            initSignCount(miniType2Map, orderUvValidSignCount);
            initUvCount(miniType2Map, uvValid);
            initSendCount(miniType2Map, sendUvInValidCount);
            initRiskCtlCount(miniType2Map, riskControlInValidCount);

            initOrderCount(miniType2Map, orderUvQualityList);
            initRiskPassCount(miniType2Map, orderUvQualityRiskPassList);
            initPayCount(miniType2Map, orderUvQualityPayList);
            initSignCount(miniType2Map, orderUvQualitySignList);
            initUvCount(miniType2Map, uvQuality);
            initSendCount(miniType2Map, sendUvQualityCount);
            initRiskCtlCount(miniType2Map, riskControlQualityCount);

            // 写入mysql
            LinkedList<OperateOrderConversionDO> list = Lists.newLinkedList();

            for (Map.Entry<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> entry :
                    miniType2Map.entrySet()) {
                for (Map.Entry<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do :
                        entry.getValue().entrySet()) {
                    for (Map.Entry<Integer, OperateOrderConversionDO> type2Do : merchantId2Do.getValue().entrySet()) {
                        OperateOrderConversionDO value = type2Do.getValue();
                        list.add(value);
                    }
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            operateOrderConversionService.removeDataByTime(countDay, MiniTypeEnum.TOTAl.getCode());
            operateOrderConversionService.saveBatch(list);
        } catch (Exception e) {
            log.error("OperateOrderConversionTotalBusiness.runCore error:{}", e.getMessage());
        }

    }

    private void initRiskCtlCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> riskControlCount) {
        for (OperateOrderConversionDO coreDO : riskControlCount) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setRiskControlCount(coreDO.getRiskControlCount());
        }
    }

    private void initSendCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> sendUvCount) {
        for (OperateOrderConversionDO coreDO : sendUvCount) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setSendCount(coreDO.getSendCount());
            operateOrderConversionDO.setSendUvCount(coreDO.getSendUvCount());
            operateOrderConversionDO.setMerchantSendCount(coreDO.getMerchantSendCount());
        }
    }

    private void initPayCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> orderUvQualityPayList) {
        for (OperateOrderConversionDO coreDO : orderUvQualityPayList) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setPayCount(coreDO.getPayCount());
            operateOrderConversionDO.setPayUvCount(coreDO.getPayUvCount());
        }
    }

    private void initRiskPassCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> orderUvValidRiskCount) {
        for (OperateOrderConversionDO coreDO : orderUvValidRiskCount) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setRiskPassUvCount(coreDO.getRiskPassUvCount());
            operateOrderConversionDO.setRiskPassCount(coreDO.getRiskPassCount());
        }
    }

    private void initOrderCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> orderUvQualityList) {
        for (OperateOrderConversionDO coreDO : orderUvQualityList) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setOrderUvCount(coreDO.getOrderUvCount());
            operateOrderConversionDO.setOrderCount(coreDO.getOrderCount());
        }
    }

    private void initSignCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> orderUvQualitySignList) {
        for (OperateOrderConversionDO coreDO : orderUvQualitySignList) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setSignCount(coreDO.getSignCount());
        }
    }

    private void initUvCount(Map<Integer, Map<String, Map<Integer, OperateOrderConversionDO>>> miniType2Map, List<OperateOrderConversionDO> uv) {
        for (OperateOrderConversionDO coreDO : uv) {
            initMap(miniType2Map, coreDO);
            Map<String, Map<Integer, OperateOrderConversionDO>> merchantId2Do =
                    miniType2Map.get(coreDO.getMiniType());
            Map<Integer, OperateOrderConversionDO> type2Do =
                    merchantId2Do.get(coreDO.getMerchantId());
            OperateOrderConversionDO operateOrderConversionDO = type2Do.get(coreDO.getType());
            operateOrderConversionDO.setUvCount(coreDO.getUvCount());
        }
    }


    /**
     * todo 后面不分组，算总数
     * 总下单用户数
     */
    private List<OperateOrderConversionDO> getOrderUvCount(String ds) {
        String sql = "select  count(distinct a.customer_id) as uv," +
                "             count(no) as num" +
                "      from rent_order a" +
                "      where a.parent_id = 0" +
                "        and a.is_deleted = 0" +
                "        and a.merchant_id = 100" +
                "        and a.type = 1" +
                "        and a.ds = "+ds +
                "        and a.create_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String uv = record.getString("uv");
            String num = record.getString("num");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUvCount(Long.valueOf(uv));
            domain.setOrderCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 通审用户数 todo 后面不分组，算总数
     */
    private List<OperateOrderConversionDO> getRiskPassUvCount(String ds) {
        String sql = " select count(distinct ro.id) num," +
                "       count(distinct ro.customer_id) uv" +
                "         from rent_order ro" +
                "                  left join rent_order_audit roa" +
                "                            on ro.id = roa.order_id" +
                "         where " +
                "            ro.parent_id = 0" +
                "           and ro.type = 1" +
                "           and ro.merchant_id = 100" +
                "           and ro.ds = "+ds +
                "           and roa.ds = "+ds +
                "           and roa.type = 2" +
                "           and roa.audit_status = 1" +
                "           and ro.is_deleted = 0" +
                "           and roa.operate_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String uv = record.getString("uv");
            String num = record.getString("num");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassUvCount(Long.valueOf(uv));
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * DAC(付款用户数)todo 后面不分组，算总数
     */
    private List<OperateOrderConversionDO> getPayUvCount(String ds) {
        String sql = "select   count(distinct a.customer_id) as pay_uv_count," +
                "       count(a.id) as pay_count" +
                " from rent_order a" +
                " where" +
                " a.is_deleted = 0" +
                "  and a.parent_id = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String uv = record.getString("pay_uv_count");
            String num = record.getString("pay_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPayUvCount(Long.valueOf(uv));
            domain.setPayCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 发货用户数
     */
    private List<OperateOrderConversionDO> getSendUvCount(String ds) {
        String sql = "select   count(distinct if(a.merchant_id = 100, a.customer_id, null)) as send_uv_count," +
                "       count(if(a.merchant_id in (100,10000107) a.no, null))                   as send_count," +
                "       count(if(a.merchant_id <> 100, a.no, null))                  as merchant_send_count" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1" +
                "         inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and c.send_time is not null" +
                "  and a.is_deleted = 0" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and c.send_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String uv = record.getString("send_uv_count");
            String num = record.getString("send_count");
            String merchantSendCount = record.getString("merchant_send_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendUvCount(Long.valueOf(uv));
            domain.setSendCount(Long.valueOf(num));
            domain.setMerchantSendCount(Long.valueOf(merchantSendCount));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取签收订单数 todo 后面不分组，算总数
     */
    private List<OperateOrderConversionDO> getSignCount(String ds) {
        String sql = "select count(a.customer_id) as sign_count" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1" +
                "         inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and c.sign_time is not null" +
                "  and a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and c.sign_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("sign_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSignCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 审核订单量
     */
    private List<OperateOrderConversionDO> getRiskControlCount(String ds) {
        String sql = "select  count(distinct a.id)            as num" +
                " from rent_order a" +
                "         left join rent_order_audit b on a.id = b.order_id" +
                " where a.parent_id = 0" +
                "  and a.type = 1" +
                "  and b.type = 2" +
                "  and a.merchant_id = 100" +
                "  and b.audit_status in (0, 1, 2)" +
                "  and a.is_deleted = 0" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and b.operate_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("num");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskControlCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 付款订单(订单创建口径)
     */
    private List<OperateOrderConversionDO> getPayCountByCreateTm(String ds) {
        String sql = "select count(customer_id) as pay_count" +
                " from rent_order a" +
                " where a.is_deleted = 0" +
                "  and a.parent_id = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("pay_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPayCountOther(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 通审用户数(订单创建口径) todo 后面不分组，算总数
     */
    private List<OperateOrderConversionDO> getRiskPassCountOther(String ds) {
        String sql = "select count(ro.customer_id) num" +
                "         from rent_order ro" +
                "                  left join rent_order_audit roa" +
                "                            on ro.id = roa.order_id" +
                "         where ro.biz_type = 2" +
                "           and ro.parent_id = 0" +
                "           and ro.type = 1" +
                "  and ro.merchant_id = 100" +
                "           and roa.type = 2" +
                "           and ro.ds = "+ds +
                "           and roa.ds = "+ds +
                "           and roa.audit_status = 1" +
                "           and ro.is_deleted = 0" +
                "           and ro.create_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("num");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassCountOther(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 发货订单数(订单创建口径)
     */
    private List<OperateOrderConversionDO> getSendCountOther(String ds) {
        String sql = "select  count(customer_id) as send_count" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1" +
                "         inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("send_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendCountOther(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 签收订单数(订单创建口径)
     */
    private List<OperateOrderConversionDO> getSignCountOther(String ds) {
        String sql = "select  count(customer_id) as sign_count" +
                " from rent_order a" +
                "         inner join rent_order_item b on a.id = b.order_id and b.item_type = 1" +
                "         inner join rent_order_logistics c on b.logistics_id = c.id" +
                " where a.parent_id = 0" +
                "  and a.payment_time is not null" +
                "  and c.sign_time is not null" +
                "  and a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("sign_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendCountOther(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(4);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效UV下单用户数
     */
    private List<OperateOrderConversionDO> getOrderUvInvalidCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.create_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) < 19000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUvCount(Long.valueOf(count));
            domain.setOrderCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效UV通审用户数
     */
    private List<OperateOrderConversionDO> getUvInvalidRiskPassCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.type = 2" +
                "  and d.audit_status = 1" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) < 19000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setRiskPassUvCount(Long.valueOf(count));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效UV付款用户数
     */
    private List<OperateOrderConversionDO> getOrderUvInvalidPayCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.is_deleted = 0" +
                "        and c.status in (1, 5, 15, 60)" +
                "        and c.payment_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) < 19000 ) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPayUvCount(Long.valueOf(count));
            domain.setPayCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效审核用户数
     */
    private List<OperateOrderConversionDO> getOrderUvInValidRiskCtlCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.type = 2" +
                "  and d.audit_status in (0, 1, 2)" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) < 19000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskControlCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效发货用户数
     */
    private List<OperateOrderConversionDO> getOrderUvInvalidSendCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.send_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) < 19000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendUvCount(Long.valueOf(count));
            domain.setSendCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 无效UV签收用户数
     */
    private List<OperateOrderConversionDO> getOrderUvInvalidSignCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) < 19000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSignCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质下单用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.create_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 19000 and  sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUvCount(Long.valueOf(count));
            domain.setOrderCount(Long.valueOf(num));
            domain.setType(2);
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质通审用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidRiskPayCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.audit_status = 1" +
                "  and d.type = 2" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) >= 19000 and  sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassUvCount(Long.valueOf(count));
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setType(2);
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质付款用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidPayCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.is_deleted = 0" +
                "        and c.status in (1, 5, 15, 60)" +
                "        and c.payment_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 19000" +
                "         and sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPayUvCount(Long.valueOf(count));
            domain.setPayCount(Long.valueOf(num));
            domain.setType(2);
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质量审核用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidRiskCtlCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.type = 2" +
                "  and d.audit_status in (0, 1, 2)" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) >= 19000 and  sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskControlCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质量发货用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidSendCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.send_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 19000 and  sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendUvCount(Long.valueOf(count));
            domain.setSendCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(2);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 有效低质签收用户数
     */
    private List<OperateOrderConversionDO> getOrderUvValidSignCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10001,10002,10003,10004,10009,10010,10011,10012,10014) " +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 19000 and  sum(keep_alive_time) < 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSignCount(Long.valueOf(num));
            domain.setType(2);
            domain.setMerchantId("100");
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量下单用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualityCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10004,10009)" +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.create_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setOrderUvCount(Long.valueOf(count));
            domain.setOrderCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量通审用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualityRiskPassCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10004,10009)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.audit_status = 1" +
                "  and d.type = 2" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskPassUvCount(Long.valueOf(count));
            domain.setRiskPassCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量审核用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualityRiskCtlCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select  a.customer_third_id," +
                "      c.id" +
                " from dataview_track_page_statistics a" +
                "         inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "         inner join rent_order c on c.customer_id = b.id" +
                "         inner join rent_order_audit d on c.id = d.order_id" +
                " where a.enter_page_code in (10004,10009)" +
                "  and a.action_type = 2" +
                "  and c.is_deleted = 0" +
                "  and c.parent_id = 0" +
                "  and c.type = 1" +
                "  and c.merchant_id = 100" +
                "  and d.type = 2" +
                "  and d.audit_status in (0, 1, 2)" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.operate_time between ${fromTime} and ${toTime}" +
                " group by a.customer_third_id, c.id" +
                " having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setRiskControlCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量付款用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualityPayCount(String ds) {
        String sql = "select count(distinct a.customer_third_id) uv," +
                "       count(a.id)                         order_count" +
                " from (select a.customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "      where a.enter_page_code in (10004,10009)" +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and c.is_deleted = 0" +
                "        and c.status in (1, 5, 15, 60)" +
                "        and c.payment_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String count = record.getString("uv");
            String num = record.getString("order_count");

            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setPayUvCount(Long.valueOf(count));
            domain.setPayCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量签收用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualitySignCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10004, 10009)" +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.sign_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String count = record.getString("uv");
            String num = record.getString("order_count");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSignCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效高质量发货用户数
     */
    private List<OperateOrderConversionDO> getOrderUvQualitySendCount(String ds) {
        String sql = "select count (distinct a.customer_third_id) uv," +
                "       count(a.id) order_count" +
                " from (select customer_third_id," +
                "             c.id" +
                "      from dataview_track_page_statistics a" +
                "               inner join rent_customer b on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id" +
                "               inner join rent_order c on c.customer_id = b.id" +
                "               inner join rent_order_logistics e on c.id = e.order_id" +
                "      where a.enter_page_code in (10004, 10009)" +
                "        and a.action_type = 2" +
                "        and c.is_deleted = 0" +
                "        and c.parent_id = 0" +
                "        and c.merchant_id = 100" +
                "        and c.type = 1" +
                "        and a.ds = "+ds +
                "        and b.ds = "+ds +
                "        and c.ds = "+ds +
                "        and e.ds = "+ds +
                "        and e.send_time between ${fromTime} and ${toTime}" +
                "      group by a.customer_third_id, c.id" +
                "      having sum(keep_alive_time) >= 67000) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String count = record.getString("uv");
            String num = record.getString("order_count");

            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setSendUvCount(Long.valueOf(count));
            domain.setSendCount(Long.valueOf(num));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 高质量UV
     */
    private List<OperateOrderConversionDO> getUvQuality(String ds) {
        String sql = "select count(distinct (case when rc.mobile is not null then  rc.id else null end))      valid_uv" +
                "      from dataview_track_page_statistics dtpa" +
                "               left join rent_customer rc on get_json_object(rc.bindinfo, '$[0].miniUserId') = dtpa.customer_third_id\n" +
                "      where " +
                "         dtpa.report_time between ${fromTime} and ${toTime}" +
                "        and dtpa.ds = " + ds +
                "        and rc.ds = " + ds;

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String uv = record.getString("valid_uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvCount(Long.valueOf(uv));
            domain.setMerchantId("100");
            domain.setType(3);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 无效UV
     */
    private List<OperateOrderConversionDO> getUvInvalid(String ds) {
        String sql = "select count(distinct customer_third_id) uv" +
                " from (select customer_third_id" +
                "      from dataview_track_page_statistics" +
                "      where enter_page_code in (10001, 10002, 10003, 10004, 10009, 10010, 10011, 10012, 10014)" +
                "        and action_type = 2" +
                "        and report_time between ${fromTime} and ${toTime}" +
                "        and ds = "+ds +
                "      group by customer_third_id" +
                "      having sum(keep_alive_time) < 19000" +
                "     ) a";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();
            String uv = record.getString("uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvCount(Long.valueOf(uv));
            domain.setMerchantId("100");
            domain.setType(1);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效低质量UV
     */
    private List<OperateOrderConversionDO> getUvValid(String ds) {
        String sql = "select count(distinct customer_third_id) uv" +
                " from (select customer_third_id" +
                "      from dataview_track_page_statistics" +
                "      where enter_page_code in (10001, 10002, 10003, 10004, 10009, 10010, 10011, 10012, 10014)" +
                "        and action_type = 2" +
                "        and report_time between ${fromTime} and ${toTime}" +
                "        and ds = "+ds +
                "      group by customer_third_id" +
                "      having sum(keep_alive_time) >= 19000" +
                "         and sum(keep_alive_time) < 67000" +
                "     ) a";
        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String uv = record.getString("uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvCount(Long.valueOf(uv));
            domain.setMerchantId("100");
            domain.setType(2);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * UV
     */
    private List<OperateOrderConversionDO> getUv(String ds) {
        String sql = "select count(customer_third_id)          duv," +
                "       count(distinct customer_third_id) uv" +
                " from dataview_track_enter_applets" +
                " where action_type = 1 " +
                "  and ds = "+ds +
                "  and report_time between ${fromTime} and ${toTime}";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");

        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateOrderConversionDO domain = new OperateOrderConversionDO();

            String uv = record.getString("uv");
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setUvCount(Long.valueOf(uv));
            domain.setMerchantId("100");
            domain.setType(4);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            return domain;
        }).collect(Collectors.toList());
    }


}
