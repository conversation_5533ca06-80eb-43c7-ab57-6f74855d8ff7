package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.WholeLinkSignDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkSignService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkSignBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkSignService wholeLinkSignService;

    private AtomicInteger atomicInteger = new AtomicInteger();
    private ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkSignBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkSignDO> miniType2Map, WholeLinkSignDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkSignDO ac = new WholeLinkSignDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkSignDO> miniType2Map = new HashMap<>();
            CompletableFuture<List<WholeLinkSignDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getUnSendUV8DByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f2 =
                    CompletableFuture.supplyAsync(() -> getUnSendUV8D(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getSendUVByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getSendUV(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f9 =
                    CompletableFuture.supplyAsync(() -> getSendCloseByMIniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f10 =
                    CompletableFuture.supplyAsync(() -> getSendClose(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f11 =
                    CompletableFuture.supplyAsync(() -> getUnSendUV48hByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkSignDO>> f12 =
                    CompletableFuture.supplyAsync(() -> getUnSendUV48h(ds, hour), threadPoolExecutor);
            CompletableFuture.allOf(f3, f4, f5, f6, f7, f8, f9, f10).join();
            assembleValue(miniType2Map, f1.get(), "unSendUv8days");
            assembleValue(miniType2Map, f2.get(), "unSendUv8days");
            assembleValue(miniType2Map, f3.get(), "sendUv", "signUv");
            assembleValue(miniType2Map, f4.get(), "sendUv", "signUv");
            assembleValue(miniType2Map, f5.get(), "signTop5ChannelUv");
            assembleValue(miniType2Map, f6.get(), "signTop5ChannelUv");
            assembleValue(miniType2Map, f7.get(), "signTop5SceneUv");
            assembleValue(miniType2Map, f8.get(), "signTop5SceneUv");
            assembleValue(miniType2Map, f9.get(), "aliSendUv", "wechatSendUv", "otherSendUv", "sendCloseUv");
            assembleValue(miniType2Map, f10.get(), "aliSendUv", "wechatSendUv", "otherSendUv", "sendCloseUv");
            assembleValue(miniType2Map, f11.get(), "unSendUv48h");
            assembleValue(miniType2Map, f12.get(), "unSendUv48h");
            List<WholeLinkSignDO> list = Lists.newArrayList(miniType2Map.values());
            LocalDate now = LocalDate.now();
            wholeLinkSignService.removeByHour(hour, now);
            wholeLinkSignService.saveBatch(list);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkSignDO> miniType2Map,
                               List<WholeLinkSignDO> list,
                               String... fields) throws Exception {

        for (WholeLinkSignDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkSignDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 待发货
     */
    private List<WholeLinkSignDO> getSendUVByMiniType(String ds, Integer hour) {
        String sql = "select ro.mini_type,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()), customer_id, null))) send_uv,\n" +
                "          count(distinct (if(date(rol.sign_time) = date(getdate()), customer_id, null))) sign_uv\n" +
                "   from rent_order ro\n" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id\n" +
                "   where ro.is_deleted = 0\n" +
                "     and ro.type = 1\n" +
                "     and ro.parent_id = 0\n" +
                "     \n" +
                "     and ro.ds=\n" + ds +
                "     and rol.ds=\n" + ds +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long sendUv = Long.parseLong(record.getString("send_uv"));
            Long signUv = Long.parseLong(record.getString("sign_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setSendUv(sendUv);
            domain.setSignUv(signUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 待发货
     */
    private List<WholeLinkSignDO> getSendUV(String ds, Integer hour) {
        String sql = "select \n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()), customer_id, null))) send_uv,\n" +
                "          count(distinct (if(date(rol.sign_time) = date(getdate()), customer_id, null))) sign_uv\n" +
                "   from rent_order ro\n" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id\n" +
                "   where ro.is_deleted = 0\n" +
                "     and ro.type = 1\n" +
                "     and ro.parent_id = 0\n" +
                "     \n" +
                "     and ro.ds=\n" + ds +
                "     and rol.ds=\n" + ds +
                "   ;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long sendUv = Long.parseLong(record.getString("send_uv"));
            Long signUv = Long.parseLong(record.getString("sign_uv"));
            domain.setSendUv(sendUv);
            domain.setSignUv(signUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 8日未发货
     */
    private List<WholeLinkSignDO> getUnSendUV8DByMiniType(String ds, Integer hour) {
        String sql = "select mini_type,count(distinct ro.id) un_send_uv_8days\n" +
                "   from (select no, id, payment_time ,mini_type\n" +
                "         from rent_order\n" +
                "         where\n" +
                "             ds = \n" + ds +
                "           and is_deleted = 0\n" +
                "           and type = 1\n" +
                "           and parent_id = 0\n" +
                "           and merchant_id = 100\n" +
                "           and termination = 1\n" +
                "           and date (payment_time) >= dateadd(date(getdate()), -8, 'dd')) ro\n" +
                "            left join\n" +
                "        (select order_id, send_time\n" +
                "         from rent_order_logistics\n" +
                "         where ds = " + ds + ") rol\n" +
                "        on ro.id = rol.order_id\n" +
                "   where rol.send_time is null\n" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long unSendUv8days = Long.parseLong(record.getString("un_send_uv_8days"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setUnSendUv8days(unSendUv8days);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 8日未发货
     */
    private List<WholeLinkSignDO> getUnSendUV8D(String ds, Integer hour) {
        String sql = "select count(distinct ro.id) un_send_uv_8days\n" +
                "   from (select no, id, payment_time \n" +
                "         from rent_order\n" +
                "         where\n" +
                "             ds = \n" + ds +
                "           and is_deleted = 0\n" +
                "           and type = 1\n" +
                "           and parent_id = 0\n" +
                "           and merchant_id = 100\n" +
                "           and termination = 1\n" +
                "           and date (payment_time) >= dateadd(date(getdate()), -8, 'dd')) ro\n" +
                "            left join\n" +
                "        (select order_id, send_time\n" +
                "         from rent_order_logistics\n" +
                "         where ds = " + ds + ") rol\n" +
                "        on ro.id = rol.order_id\n" +
                "   where rol.send_time is null;\n";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long unSendUv8days = Long.parseLong(record.getString("un_send_uv_8days"));
            domain.setUnSendUv8days(unSendUv8days);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 48h未发货
     */
    private List<WholeLinkSignDO> getUnSendUV48hByMiniType(String ds, Integer hour) {
        String sql = "select mini_type,count(distinct ro.id) un_send_uv_48h\n" +
                "   from (select no, id, payment_time ,mini_type\n" +
                "         from rent_order\n" +
                "         where\n" +
                "             ds = \n" + ds +
                "           and is_deleted = 0\n" +
                "           and type = 1\n" +
                "           and parent_id = 0\n" +
                "           and merchant_id = 100\n" +
                "           and termination = 1\n" +
                "           and date (payment_time) >= dateadd(dateadd(date(getdate()), -1, 'dd'), -2, 'dd')) ro\n" +
                "            left join\n" +
                "        (select order_id, send_time\n" +
                "         from rent_order_logistics\n" +
                "         where ds = " + ds + ") rol\n" +
                "        on ro.id = rol.order_id\n" +
                "   where rol.send_time is null\n" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long unSendUv48h = Long.parseLong(record.getString("un_send_uv_48h"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setUnSendUv48h(unSendUv48h);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 48H未发货
     */
    private List<WholeLinkSignDO> getUnSendUV48h(String ds, Integer hour) {
        String sql = "select count(distinct ro.id) un_send_uv_48h\n" +
                "   from (select no, id, payment_time \n" +
                "         from rent_order\n" +
                "         where\n" +
                "             ds = \n" + ds +
                "           and is_deleted = 0\n" +
                "           and type = 1\n" +
                "           and parent_id = 0\n" +
                "           and merchant_id = 100\n" +
                "           and termination = 1\n" +
                "           and date (payment_time) >= dateadd(dateadd(date(getdate()), -1, 'dd'), -2, 'dd')) ro\n" +
                "            left join\n" +
                "        (select order_id, send_time\n" +
                "         from rent_order_logistics\n" +
                "         where ds = " + ds + ") rol\n" +
                "        on ro.id = rol.order_id\n" +
                "   where rol.send_time is null;\n";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long unSendUv48h = Long.parseLong(record.getString("un_send_uv_48h"));
            domain.setUnSendUv48h(unSendUv48h);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * top渠道
     */
    private List<WholeLinkSignDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "                                 left join rent_order_logistics rol on ro.id = rol.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and rol.ds = " + ds +
                "                 and date(rol.send_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(rol.send_time) time ,ro.mini_type, COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and date(rol.send_time) = date(getdate())" +
                "     and hour(rol.send_time) <= " + hour +
                "   GROUP BY date(rol.send_time),ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setSignTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkSignDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (SELECT quotient_id" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "                                 left join rent_order_logistics rol on ro.id = rol.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and rol.ds = " + ds +
                "                 and date(rol.send_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "               GROUP BY `quotient_id`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(rol.send_time) time , COUNT(distinct ro.customer_id) " +
                "   order_top5_channel_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id" +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and date(rol.send_time) = date(getdate())" +
                "   GROUP BY date(rol.send_time);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setSignTop5ChannelUv(orderTop5ChannelUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkSignDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "                        left join rent_order_logistics rol on ro.id = rol.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and rol.ds = " + ds +
                "                 and date(rol.send_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 5)" +
                "   SELECT date(rol.send_time) time ,ro.mini_type, COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id " +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(rol.send_time) = date(getdate())" +
                "   GROUP BY date(rol.send_time),ro.mini_type" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setSignTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkSignDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "" +
                "   with a1 as (SELECT mini_scene" +
                "               FROM `rent_order` ro" +
                "                        left join `rent_order_source` ros on ro.id = ros.order_id" +
                "                        left join rent_order_logistics rol on ro.id = rol.order_id" +
                "               where ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "                 and rol.ds = " + ds +
                "                 and date(rol.send_time) =  dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.mini_type in (1, 4, 5, 7, 9)" +
                "               GROUP BY `mini_scene`" +
                "               order by COUNT(distinct ro.customer_id) desc" +
                "               limit 1,5)" +
                "   SELECT date(rol.send_time) time , COUNT(distinct ro.customer_id)  order_top5_scene_uv" +
                "   FROM rent_order ro" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id " +
                "   where ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and ro.mini_type in (1, 4, 5, 7, 9)" +
                "     and date(rol.send_time) = date(getdate())" +
                "   GROUP BY date(rol.send_time)" +
                "   order by COUNT(distinct ro.customer_id) desc;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(DateUtils.stringToLocateDate(record.getString("time")),
                    LocalTime.MIN);
            Long orderTop5SceneUv = stringToLong(record.getString("order_top5_scene_uv"));
            domain.setSignTop5SceneUv(orderTop5SceneUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 已发货关闭
     */
    private List<WholeLinkSignDO> getSendCloseByMIniType(String ds, Integer hour) {
        String sql = "select ro.mini_type,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and rmc.platform_code = 'ALIPAY',\n" +
                "                             customer_id, null)))                                                ali_send_uv,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and rmc.platform_code = 'WECHAT',\n" +
                "                             customer_id, null)))                                                wechat_send_uv,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and\n" +
                "                             rmc.platform_code not in ('WECHAT', 'ALIPAY'), customer_id, null))) other_send_uv,\n" +
                "          count(distinct\n" +
                "                (if(date(roi.termination_time) = date(getdate()), customer_id, null)))           send_close_uv\n" +
                "   from rent_order ro\n" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id\n" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type\n" +
                "            left join rent_order_infomore roi on ro.id = roi.order_id\n" +
                "   where ro.is_deleted = 0\n" +
                "     and ro.type = 1\n" +
                "     and ro.parent_id = 0\n" +
                "     \n" +
                "     and ro.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and roi.ds = " + ds +
                "     and rol.send_time is not null\n" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long aliSendUv = stringToLong(record.getString("ali_send_uv"));
            Long wechatSendUv = stringToLong(record.getString("wechat_send_uv"));
            Long otherSendUv = stringToLong(record.getString("other_send_uv"));
            Long sendCloseUv = stringToLong(record.getString("send_close_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setAliSendUv(aliSendUv);
            domain.setWechatSendUv(wechatSendUv);
            domain.setOtherSendUv(otherSendUv);
            domain.setSendCloseUv(sendCloseUv);
            domain.setMiniType(miniType);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 已发货关闭
     */
    private List<WholeLinkSignDO> getSendClose(String ds, Integer hour) {
        String sql = "select \n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and rmc.platform_code = 'ALIPAY',\n" +
                "                             customer_id, null)))                                                ali_send_uv,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and rmc.platform_code = 'WECHAT',\n" +
                "                             customer_id, null)))                                                wechat_send_uv,\n" +
                "          count(distinct (if(date(rol.send_time) = date(getdate()) and\n" +
                "                             rmc.platform_code not in ('WECHAT', 'ALIPAY'), customer_id, null))) other_send_uv,\n" +
                "          count(distinct\n" +
                "                (if(date(roi.termination_time) = date(getdate()), customer_id, null)))           send_close_uv\n" +
                "   from rent_order ro\n" +
                "            left join rent_order_logistics rol on ro.id = rol.order_id\n" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type\n" +
                "            left join rent_order_infomore roi on ro.id = roi.order_id\n" +
                "   where ro.is_deleted = 0\n" +
                "     and ro.type = 1\n" +
                "     and ro.parent_id = 0\n" +
                "     \n" +
                "     and ro.ds = " + ds +
                "     and rol.ds = " + ds +
                "     and roi.ds = " + ds +
                "     and rol.send_time is not null;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkSignDO domain = new WholeLinkSignDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long aliSendUv = stringToLong(record.getString("ali_send_uv"));
            Long wechatSendUv = stringToLong(record.getString("wechat_send_uv"));
            Long otherSendUv = stringToLong(record.getString("other_send_uv"));
            Long sendCloseUv = stringToLong(record.getString("send_close_uv"));
            domain.setAliSendUv(aliSendUv);
            domain.setWechatSendUv(wechatSendUv);
            domain.setOtherSendUv(otherSendUv);
            domain.setSendCloseUv(sendCloseUv);
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    private Long stringToLong(String val) {
        if (val.equals("\\N")) {
            val = "0";
        }
        return Long.parseLong(val);
    }
}



