package qnvip.data.overview.business.alarm;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import qnvip.data.overview.dto.BuyoutRateExceptionDTO;
import qnvip.data.overview.dto.ProfitsExceptionDTO;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.rent.common.config.alarm.DingDingRobot;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * create by gw on 2021/12/30
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ProfitsQueryBusiness {

    private final OdpsUtil odpsUtil;
    private final DingDingRobot dingDingRobot;


    public List<BuyoutRateExceptionDTO> queryAndCompareBuyoutRate(String ds, String sTime, String eTime,
                                                                  BigDecimal startVal,
                                                                  BigDecimal endVal) {
        String sql = "select a.no order_id,b.buyout_amt buyout_amt,b.finance_template_title, " +
                " c.create_act_supply_price create_act_supply_price, " +
                "       (b.buyout_amt / c.create_act_supply_price) rate " +
                " from rent_order a " +
                "         inner join rent_order_finance_detail b " +
                "                    on a.id = b.order_id " +
                "                        and b.is_deleted = 0 " +
                "         inner join rent_order_item c on a.id = c.order_id and c.item_type = 1 " +
                " where a.parent_id = 0 " +
                " AND c.create_act_supply_price != 0 " +
                "  and a.payment_time between '" + sTime + "' and '" + eTime + "' " +
                "  and ((b.buyout_amt / c.create_act_supply_price) < " + startVal +
                "           or (b.buyout_amt / c.create_act_supply_price) > " + endVal + ") " +
                "  and a.is_deleted = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and b.rate_config_type != 10 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds + ";";
        List<Record> records = odpsUtil.querySql(sql);
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        List<BuyoutRateExceptionDTO> list = new ArrayList<>();
        for (Record record : records) {
            try {
                BuyoutRateExceptionDTO dto = new BuyoutRateExceptionDTO();
                dto.setOrderId(record.getString("order_id"));
                dto.setFinanceName(record.getString("finance_template_title"));
                dto.setBuyoutAmt(new BigDecimal(record.getString("buyout_amt")));
                dto.setSupplyPrice(new BigDecimal(record.getString("create_act_supply_price")));
                dto.setBuyoutRate(new BigDecimal(record.getString("rate")).setScale(4, RoundingMode.HALF_UP));
                list.add(dto);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                dingDingRobot.exceptionAlarm("odps查询买断金占比异常:" + e.getMessage());
                continue;
            }
        }
        return list;
    }


    public List<ProfitsExceptionDTO> queryAndCompareProfitsRate(String ds, String sTime, String eTime,
                                                                BigDecimal startVal,
                                                                BigDecimal endVal) {
        String sql = "select a.no order_id, b.total_financing_amt total_financing_amt,b.finance_template_title," +
                " c.actual_supply_price actual_supply_price,  " +
                "       (b.total_financing_amt - c.actual_supply_price) / c.actual_supply_price profits  " +
                " from rent_order a  " +
                "         inner join rent_order_finance_detail b  " +
                "                    on a.id = b.order_id  " +
                "                        and b.is_deleted = 0  " +
                "         inner join rent_order_item c on a.id = c.order_id and c.item_type = 1  " +
                " where a.parent_id = 0  " +
                "  and a.payment_time between '" + sTime + "' and '" + eTime + "' " +
                "  and ((b.total_financing_amt - c.actual_supply_price) / c.actual_supply_price < " + startVal + " or" +
                "       (b.total_financing_amt - c.actual_supply_price) / c.actual_supply_price > " + endVal + ")  " +
                "  and a.is_deleted = 0  " +
                "  and a.type = 1  " +
                "  and a.merchant_id = 100" +
                "  and c.actual_supply_price > 0" +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds + ";";
        List<Record> records = odpsUtil.querySql(sql);
        if (CollUtil.isEmpty(records)) {
            return null;
        }
        List<ProfitsExceptionDTO> list = new ArrayList<>();
        for (Record record : records) {
            try {
                ProfitsExceptionDTO dto = new ProfitsExceptionDTO();
                dto.setOrderId(record.getString("order_id"));
                dto.setFinanceName(record.getString("finance_template_title"));
                dto.setTotalWorth(new BigDecimal(record.getString("total_financing_amt")));
                dto.setActualSupplyPrice(new BigDecimal(record.getString("actual_supply_price")));
                dto.setProfits(new BigDecimal(record.getString("profits")).setScale(4, RoundingMode.HALF_UP));
                list.add(dto);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
                dingDingRobot.qnvipRentManagerAlarm("odps查询毛利率异常:" + e.getMessage());
                continue;
            }
        }
        return list;
    }


}