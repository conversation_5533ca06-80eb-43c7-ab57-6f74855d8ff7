package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorBeforeSaleOrderDO;
import qnvip.data.overview.service.dataindicators.IndicatorBeforeSaleOrderService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * create by gw on 2022/1/13
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorBeforeSaleOrderBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorBeforeSaleOrderService indicatorBeforeSaleOrderService;


    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorBeforeSaleOrderDO> collectMap = new HashMap<>();
        riskApprovedCount(ds, sTime, eTime, collectMap);
        riskRefusedCount(ds, sTime, eTime, collectMap);
        orderPayCount(ds, sTime, eTime, collectMap);
        orderDeliveryCount(ds, sTime, eTime, collectMap);
        orderCancelDeliveryCount(ds, sTime, eTime, collectMap);
        ArrayList<IndicatorBeforeSaleOrderDO> list = Lists.newArrayList();
        for (IndicatorBeforeSaleOrderDO value : collectMap.values()) {
            value.setNotPayCount(Math.max(value.getRiskApprovedCount() - value.getPayCount(),0));
            list.add(value);
        }
        if (CollUtil.isNotEmpty(list)) {
            indicatorBeforeSaleOrderService.saveOrUpdateBatch(list, 2000);
        }
    }


    /**
     * 风控通过人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void riskApprovedCount(String ds, String sTime, String eTime,
                                  Map<String, IndicatorBeforeSaleOrderDO> collectMap) {
        String sql = "select a.mini_type,to_char(a.create_time,'yyyy-mm-dd') day,count(distinct a.customer_id) count " +
                "from rent_order a " +
                "         inner join cl_loan b on a.no = b.loanno " +
                "where a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "  and b.riskstatus = 20 " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd');";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskApprovedCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 风控拒绝人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void riskRefusedCount(String ds, String sTime, String eTime, Map<String,
            IndicatorBeforeSaleOrderDO> collectMap) {
        String sql = "select  a.mini_type " +
                "        ,to_char(a.create_time,'yyyy-mm-dd') day " +
                "        ,count(distinct a.customer_id) count " +
                " from    rent_order a inner " +
                " join    rent_order_audit b " +
                " on      a.id = b.order_id " +
                " where   a.is_deleted = 0 " +
                " and     a.merchant_id = 100 " +
                " and     a.parent_id = 0 " +
                " and     a.type = 1 " +
                " and     a.biz_type = 2 " +
                " and     a.create_time between '" + sTime + "' " +
                " and     '" + eTime + "' " +
                " and     b.type = 2 " +
                " and     b.audit_status=2 " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by a.mini_type " +
                "         ,to_char(a.create_time,'yyyy-mm-dd') " +
                ";";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRiskRefusedCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 支付人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void orderPayCount(String ds, String sTime, String eTime, Map<String,
            IndicatorBeforeSaleOrderDO> collectMap) {
        String sql = " select  mini_type " +
                "        ,to_char(a.create_time, 'yyyy-mm-dd') day " +
                "        ,count(distinct a.customer_id) count " +
                "from    rent_order a " +
                "inner join rent_order_audit b on  a.id = b.order_id " +
                "where   a.is_deleted = 0 " +
                " and     a.parent_id = 0 " +
                " and     a.type = 1 " +
                " and     a.merchant_id = 100 " +
                " and     a.biz_type = 2 " +
                " and     a.create_time between '" + sTime + "' " +
                " and     '" + eTime + "' " +
                " and     a.payment_time is not null " +
                " and     a.ds = " + ds +
                " and     b.ds = " + ds +
                " group by to_char(a.create_time, 'yyyy-mm-dd'),a.mini_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setPayCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 发货人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void orderDeliveryCount(String ds, String sTime, String eTime,
                                   Map<String, IndicatorBeforeSaleOrderDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct customer_id) " +
                "count " +
                "from rent_order a " +
                "         inner join rent_order_logistics b on a.id = b.order_id " +
                "where a.merchant_id = 100 " +
                "  and a.type = 1 " +
                "  and a.parent_id = 0 " +
                "  and a.biz_type = 2     " +
                "  and a.payment_time is not null " +
                "  and a.create_time between  '" + sTime + "' " +
                "  and     '" + eTime + "' " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setDeliveryCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 发货取消人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void orderCancelDeliveryCount(String ds, String sTime, String eTime,
                                         Map<String, IndicatorBeforeSaleOrderDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct customer_id) " +
                "count " +
                "from rent_order a " +
                "         inner join rent_order_logistics b on a.id = b.order_id " +
                "where a.merchant_id = 100 " +
                "  and a.type = 1 " +
                "  and a.parent_id = 0 " +
                "  and a.biz_type = 2     " +
                "  and a.status = 30 " +
                "  and a.create_time between  '" + sTime + "' " +
                "  and     '" + eTime + "' " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  group by a.mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorBeforeSaleOrderDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setCancelDeliveryCount(Integer.valueOf(record.getString("count")));
        }
    }


    private void initMap(Map<String, IndicatorBeforeSaleOrderDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorBeforeSaleOrderDO irpd = new IndicatorBeforeSaleOrderDO();
            irpd.setMiniType(miniType);
            irpd.setCountDay(countDay);
            irpd.setRiskApprovedCount(0);
            irpd.setRiskRefusedCount(0);
            irpd.setPayCount(0);
            irpd.setNotPayCount(0);
            irpd.setDeliveryCount(0);
            irpd.setCancelDeliveryCount(0);
            miniType2Map.put(key, irpd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}