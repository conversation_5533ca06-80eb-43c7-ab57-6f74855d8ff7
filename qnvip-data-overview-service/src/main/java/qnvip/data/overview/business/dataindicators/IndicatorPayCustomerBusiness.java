package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorPayCustomerDO;
import qnvip.data.overview.service.dataindicators.IndicatorPayCustomerService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/1/23
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorPayCustomerBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorPayCustomerService indicatorPayCustomerService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorPayCustomerDO> collectMap = new HashMap<>();
        countLtNhRegisterCount(ds, sTime, eTime, collectMap);
        countRegisterNotOrder(ds, sTime, eTime, collectMap);
        countPayOutServiceCount(ds, sTime, eTime, collectMap);
        countLtNdRefusedCount(ds, sTime, eTime, collectMap);
        countPlanTotal(ds, sTime, eTime, collectMap);
        countPlanByFreeBond(ds, sTime, eTime, collectMap);
        countPlanBondRate(ds, sTime, eTime, collectMap);
        List<IndicatorPayCustomerDO> list = new ArrayList<>(collectMap.values());
        if (CollUtil.isNotEmpty(list)) {
            indicatorPayCustomerService.saveOrUpdateBatch(list, 2000);
        }
        // for (IndicatorPayCustomerDO value : collectMap.values()) {
        //     indicatorPayCustomerService.saveOrUpdate(value);
        // }
    }


    /**
     * N小时内注册人数 刷30天数据
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countLtNhRegisterCount(String ds, String sTime, String eTime,
                                       Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select a.mini_type, to_char(a.access_time, 'yyyy-mm-dd') day, count(distinct b.id) count " +
                "from (select customer_third_id, min(report_time) as access_time, mini_type " +
                "      from ( " +
                "               select a.customer_third_id, a.report_time, a.mini_type " +
                "               from dataview_track_enter_applets a " +
                "                        inner join rent_customer c " +
                "                                   on get_json_object(c.bindinfo, '$[0].miniUserId') = a" +
                ".customer_third_id " +
                "                        inner join ( " +
                "                   select customer_id, to_char(create_time, 'yyyy-mm-dd') day, mini_type " +
                "                   from ( " +
                "                            select a.customer_id, a.mini_type, a.create_time " +
                "                            from rent_order a " +
                "                                     left join rent_order_audit b " +
                "                                               on a.id = b.order_id " +
                "                            where a.is_deleted = 0 " +
                "                              and a.parent_id = 0 " +
                "                              and a.type = 1 " +
                "                              and a.merchant_id = 100 " +
                "                              and a.biz_type = 2 " +
                "                              and a.create_time between '" + sTime + "' " +
                "                                and '" + eTime + "' " +
                "                              and a.payment_time is not null " +
                "                              and a.ds = " + ds +
                "                              and b.ds = " + ds +
                "                            order by a.create_time desc " +
                "                        ) " +
                "                   group by customer_id " +
                "                          , mini_type " +
                "                          , to_char(create_time, 'yyyy-mm-dd') " +
                "               ) b on a.mini_type = b.mini_type and c.id = b.customer_id " +
                "                   and b.day = to_char(a.report_time, 'yyyy-mm-dd') " +
                "               where a.action_type = 1 " +
                "                 and a.ds = " + ds +
                "                 and c.ds = " + ds +
                "                 and a.report_time between '" + sTime + "' " +
                "                   and '" + eTime + "' " +
                "               order by a.report_time desc " +
                "           ) " +
                "      group by customer_third_id, mini_type, to_char(report_time, 'yyyy-mm-dd')) a " +
                "         inner join rent_customer b " +
                "                    on get_json_object(b.bindinfo, '$[0].miniUserId') = a.customer_third_id " +
                "                        and to_char(a.access_time, 'yyyy-mm-dd') = to_char(b.create_time, " +
                "'yyyy-mm-dd') " +
                " where UNIX_TIMESTAMP(b.create_time) - UNIX_TIMESTAMP(a.access_time) < " + IndicatorBeforeSaleBusiness.N_MINUTES * 60 +
                "  and b.ds = " + ds +
                " group by a.mini_type, to_char(a.access_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNhRegisterCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 支付人数中，已注册未下单人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countRegisterNotOrder(String ds, String sTime, String eTime,
                                      Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct a.customer_id) " +
                "count " +
                "from ( " +
                "         select a.mini_type, a.customer_id, min(a.create_time) create_time " +
                "         from rent_order a " +
                "                  left join rent_order_audit b " +
                "                            on a.id = b.order_id " +
                "         where a.is_deleted = 0 " +
                "           and a.parent_id = 0 " +
                "           and a.type = 1 " +
                "           and a.merchant_id = 100 " +
                "           and a.biz_type = 2 " +
                "           and a.create_time between '" + sTime + "' " +
                "             and '" + eTime + "' " +
                "           and a.payment_time is not null " +
                "           and a.ds = " + ds +
                "           and b.ds = " + ds +
                "         group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "                , a.mini_type, a.customer_id " +
                ") a inner join rent_customer b on b.id = a.customer_id " +
                "    inner join ( " +
                "        select min(create_time) create_time,customer_id from ( " +
                "            select customer_id,create_time from rent_order where customer_id in ( " +
                "                select customer_id " +
                "                    from rent_order  " +
                "                    where is_deleted = 0 " +
                "                    and merchant_id=100 " +
                "                    and parent_id = 0 " +
                "                    and type = 1 " +
                "                    and biz_type = 2 " +
                "                    and create_time between '" + sTime + "' and '" + eTime + "' " +
                "                    and ds = " + ds +
                "            ) " +
                "            and ds = " + ds + " order by create_time desc " +
                "        ) group by customer_id " +
                "    ) c on a.customer_id = c.customer_id and to_char(a.create_time,'yyyy-mm-dd') =  to_char(c" +
                ".create_time,'yyyy-mm-dd') " +
                "    where DATEDIFF(a.create_time,b.create_time)>1 and b.ds= " + ds +
                "    group by mini_type,to_char(a.create_time,'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setRegisterNotOrder(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 支付人数中，服务结束客户
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countPayOutServiceCount(String ds, String sTime, String eTime,
                                        Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select mini_type, to_char(a.create_time, 'yyyy-mm-dd') day, count(distinct a.customer_id) " +
                "count " +
                "from ( " +
                "         select a.mini_type, a.customer_id, min(a.create_time) create_time " +
                "         from rent_order a " +
                "                  left join rent_order_audit b " +
                "                            on a.id = b.order_id " +
                "         where a.is_deleted = 0 " +
                "           and a.parent_id = 0 " +
                "           and a.type = 1 " +
                "           and a.merchant_id = 100 " +
                "           and a.biz_type = 2 " +
                "           and a.create_time between '" + sTime + "' " +
                "             and '" + eTime + "' " +
                "           and a.payment_time is not null " +
                "           and a.ds = " + ds +
                "           and b.ds = " + ds +
                "         group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "                , a.mini_type, a.customer_id " +
                "     ) a " +
                "         inner join ( " +
                "   select min(create_time) create_time, max(termination) termination,max(status) status,customer_id " +
                "    from ( " +
                "             select termination, customer_id, create_time,status " +
                "             from rent_order " +
                "             where customer_id in ( " +
                "                 select customer_id " +
                "                 from rent_order " +
                "                 where is_deleted = 0 " +
                "                   and merchant_id = 100 " +
                "                   and parent_id = 0 " +
                "                   and type = 1 " +
                "                   and biz_type = 2 " +
                "                   and payment_time is not null " +
                "                   and ds = " + ds +
                "             ) " +
                "               and ds = " + ds +
                "             order by create_time asc " +
                "         ) " +
                "    group by customer_id " +
                ") c on a.customer_id = c.customer_id and to_char(a.create_time, 'yyyy-mm-dd') = to_char(c" +
                ".create_time, 'yyyy-mm-dd') " +
                " where c.termination != 5 and c.status in (220,330) " +
                " group by mini_type, to_char(a.create_time, 'yyyy-mm-dd'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setPayOutServiceCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 支付人数中，风控通过未付/N天内被拒人数
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countLtNdRefusedCount(String ds, String sTime, String eTime,
                                      Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select mini_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , count(distinct a.customer_id)        count " +
                "from rent_order a " +
                "         left join rent_order_audit b " +
                "                   on a.id = b.order_id " +
                "         inner join ( " +
                "    select min(risktime) risktime " +
                "         , min(id)       order_id " +
                "         , customer_id " +
                "    from ( " +
                "             select b.risktime " +
                "                  , a.customer_id " +
                "                  , a.id " +
                "             from rent_order a " +
                "                      inner join cl_loan b " +
                "                                 on a.no = b.loanno " +
                "             where a.is_deleted = 0 " +
                "               and a.merchant_id = 100 " +
                "               and a.parent_id = 0 " +
                "               and a.type = 1 " +
                "               and a.biz_type = 2 " +
                "               and b.riskstatus in (15, 25) " +
                "               and a.ds = " + ds +
                "               and b.ds = " + ds +
                "             order by b.risktime asc " +
                "         ) " +
                "    group by customer_id " +
                ") c on a.customer_id = c.customer_id " +
                "where a.is_deleted = 0 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and datediff(a.create_time, c.risktime) > " + IndicatorBeforeSaleBusiness.N_DAYS +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                " group by to_char(a.create_time, 'yyyy-mm-dd') " +
                "       , a.mini_type;  ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            updateDO.setLtNdRefusedCount(Integer.valueOf(record.getString("count")));
        }
    }


    /**
     * 支付人数中，金融方案分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countPlanTotal(String ds, String sTime, String eTime,
                               Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select a.mini_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , c.rate_config_type " +
                "     , IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion " +
                "     , count(distinct a.customer_id)                             count " +
                "from rent_order a " +
                "         inner join rent_order_audit b " +
                "                    on a.id = b.order_id " +
                "         inner join rent_order_finance_detail c " +
                "                    on a.id = c.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by a.mini_type " +
                "       , to_char(a.create_time, 'yyyy-mm-dd') " +
                "       , c.rate_config_type" +
                "       , IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                updateDO.setPlanThirdTotal(Optional.ofNullable(updateDO.getPlanThirdTotal()).orElse(0) + count);
            } else if (rateConfigType == 10) {
                updateDO.setPlanFirstTotal(updateDO.getPlanFirstTotal() + count);
            } else {
                updateDO.setPlanSecondTotal(updateDO.getPlanSecondTotal() + count);
            }
        }
    }


    /**
     * 支付人数中是否免押分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    private void countPlanByFreeBond(String ds, String sTime, String eTime,
                                     Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select a.mini_type " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , b.rate_config_type " +
                "     , b.bond_free_status " +
                "     , count(1)                             count " +
                "from rent_order a " +
                "         inner join rent_order_finance_detail b " +
                "                    on a.id = b.order_id " +
                "         inner join rent_order_audit c on a.id = c.order_id " +
                "where a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                "  and a.is_deleted = 0 " +
                "  and a.merchant_id = 100 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                " group by a.mini_type " +
                "       , to_char(a.create_time, 'yyyy-mm-dd') " +
                "       , b.bond_free_status " +
                "       , b.rate_config_type; ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            initMap(collectMap, miniType, day);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer bondFreeStatus = Integer.valueOf(record.getString("bond_free_status"));
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanFirstFreeBond(updateDO.getPlanFirstFreeBond() + count);
                } else {
                    updateDO.setPlanFirstNotFreeBond(updateDO.getPlanFirstNotFreeBond() + count);
                }
            } else {
                if (bondFreeStatus == 0) {
                    updateDO.setPlanSecondFreeBond(updateDO.getPlanSecondFreeBond() + count);
                } else {
                    updateDO.setPlanSecondNotFreeBond(updateDO.getPlanSecondNotFreeBond() + count);
                }
            }
        }
    }


    /**
     * 支付人数中保证金期数分组
     *
     * @param ds
     * @param sTime
     * @param eTime
     * @param collectMap
     */
    public void countPlanBondRate(String ds, String sTime, String eTime,
                                  Map<String, IndicatorPayCustomerDO> collectMap) {
        String sql = " select a.mini_type,c.rate_config_type, " +
                "       to_char(a.create_time,'yyyy-mm-dd') day, " +
                "       IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0') as forcedconversion, " +
                "       c.bond_rate, " +
                "       count(distinct a.customer_id) count " +
                "from rent_order a " +
                "    inner join rent_order_finance_detail c on a.id=c.order_id " +
                "    inner join rent_order_audit d on a.id = d.order_id " +
                "where a.is_deleted = 0 " +
                "    and a.merchant_id = 100 " +
                "    and a.parent_id = 0 " +
                "    and a.type = 1 " +
                "    and a.biz_type = 2 " +
                "    and a.create_time between '" + sTime + "' and '" + eTime + "' " +
                "    and a.payment_time is not null " +
                "    and a.ds = " + ds +
                "    and c.ds = " + ds +
                "    and d.ds = " + ds +
                "    group by a.mini_type,to_char(a.create_time,'yyyy-mm-dd'),c.rate_config_type, " +
                "    c.bond_rate,IF(ISNOTNULL(c.ext_json) and c.repayment_term=3,'1','0'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            Integer count = Integer.valueOf(record.getString("count"));
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer forcedConversion = Integer.valueOf(record.getString("forcedconversion"));
            double bondRate = Double.parseDouble(record.getString("bond_rate"));
            initMap(collectMap, miniType, day);
            IndicatorPayCustomerDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (forcedConversion == 1) {
                if (bondRate == 1) {
                    updateDO.setPlanThirdOneTerm(Optional.ofNullable(updateDO.getPlanThirdOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanThirdTwoTerm(Optional.ofNullable(updateDO.getPlanThirdTwoTerm()).orElse(0) + count);
                }
            } else if (rateConfigType == 10) {
                if (bondRate == 1) {
                    updateDO.setPlanFirstOneTerm(Optional.ofNullable(updateDO.getPlanFirstOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanFirstTwoTerm(Optional.ofNullable(updateDO.getPlanFirstTwoTerm()).orElse(0) + count);
                }
            } else {
                if (bondRate == 1) {
                    updateDO.setPlanSecondOneTerm(Optional.ofNullable(updateDO.getPlanSecondOneTerm()).orElse(0) + count);
                } else {
                    updateDO.setPlanSecondTwoTerm(Optional.ofNullable(updateDO.getPlanSecondTwoTerm()).orElse(0) + count);
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorPayCustomerDO> miniType2Map, Integer miniType, LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorPayCustomerDO ivd = new IndicatorPayCustomerDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setPlanFirstTotal(0);
            ivd.setPlanFirstOneTerm(0);
            ivd.setPlanFirstFreeBond(0);
            ivd.setPlanFirstNotFreeBond(0);
            ivd.setPlanSecondTotal(0);
            ivd.setPlanSecondOneTerm(0);
            ivd.setPlanSecondTwoTerm(0);
            ivd.setPlanSecondFreeBond(0);
            ivd.setPlanSecondNotFreeBond(0);
            ivd.setPlanThirdOneTerm(0);
            ivd.setPlanThirdTwoTerm(0);
            ivd.setLtNhRegisterCount(0);
            ivd.setRegisterNotOrder(0);
            ivd.setPayInServiceCount(0);
            ivd.setPayOutServiceCount(0);
            ivd.setLtNdRefusedCount(0);
            ivd.setPlanThirdTotal(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }


}