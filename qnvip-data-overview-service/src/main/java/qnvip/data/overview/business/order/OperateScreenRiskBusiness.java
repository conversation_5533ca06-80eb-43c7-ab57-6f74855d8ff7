package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateScreenRiskDO;
import qnvip.data.overview.service.order.OperateScreenRiskService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/16
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateScreenRiskBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateScreenRiskService screenRiskService;

    void initMap(Map<Integer, Map<String, OperateScreenRiskDO>> miniType2Map, OperateScreenRiskDO screenRiskDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        Map<String, OperateScreenRiskDO> merchantId2Do = Maps.newHashMap();
        if (!miniType2Map.containsKey(screenRiskDO.getMiniType())) {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            domain.setCountDay(countDay);
            domain.setMiniType(screenRiskDO.getMiniType());
            domain.setMerchantId(screenRiskDO.getMerchantId());
            merchantId2Do.put(screenRiskDO.getMerchantId(), domain);
        } else {
            merchantId2Do = miniType2Map.get(screenRiskDO.getMiniType());
            if (!merchantId2Do.containsKey(screenRiskDO.getMerchantId())) {
                OperateScreenRiskDO domain = new OperateScreenRiskDO();
                domain.setCountDay(countDay);
                domain.setMiniType(screenRiskDO.getMiniType());
                domain.setMerchantId(screenRiskDO.getMerchantId());
                merchantId2Do.put(screenRiskDO.getMerchantId(), domain);
            }
        }
        miniType2Map.put(screenRiskDO.getMiniType(), merchantId2Do);
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, Map<String, OperateScreenRiskDO>> miniType2Map = new HashMap<>();
        CompletableFuture<List<OperateScreenRiskDO>> f1 = CompletableFuture.supplyAsync(()->getClaimRecord(ds));
        CompletableFuture<List<OperateScreenRiskDO>> f2 = CompletableFuture.supplyAsync(()->getPassPayCount(ds));
        CompletableFuture<List<OperateScreenRiskDO>> f3 = CompletableFuture.supplyAsync(()->getOrderCount(ds));
        CompletableFuture<List<OperateScreenRiskDO>> f4 = CompletableFuture.supplyAsync(()->getSignOrder(ds));
        CompletableFuture<List<OperateScreenRiskDO>> f5 = CompletableFuture.supplyAsync(()->getPassCount(ds));
        CompletableFuture.allOf(f1, f2, f3, f4, f5).join();
        try {
            List<OperateScreenRiskDO> claimsCount = f1.get();
            List<OperateScreenRiskDO> passPayCount = f2.get();
            List<OperateScreenRiskDO> orderCount = f3.get();
            List<OperateScreenRiskDO> signOrderCount = f4.get();
            List<OperateScreenRiskDO> passCount = f5.get();

            for (OperateScreenRiskDO coreDO : claimsCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateScreenRiskDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateScreenRiskDO operateScreenRiskDO = merchantId2Do.get(coreDO.getMerchantId());
                operateScreenRiskDO.setClaimsCount(coreDO.getClaimsCount());
                operateScreenRiskDO.setClaimsAmt(coreDO.getClaimsAmt());
            }
            for (OperateScreenRiskDO coreDO : passPayCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateScreenRiskDO> core = miniType2Map.get(coreDO.getMiniType());
                OperateScreenRiskDO operateScreenRiskDO = core.get(coreDO.getMerchantId());
                operateScreenRiskDO.setPassPayCount(coreDO.getPassPayCount());
                operateScreenRiskDO.setNmv(coreDO.getNmv());
            }
            for (OperateScreenRiskDO coreDO : orderCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateScreenRiskDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateScreenRiskDO operateScreenRiskDO = merchantId2Do.get(coreDO.getMerchantId());
                operateScreenRiskDO.setOrderCount(coreDO.getOrderCount());
            }
            for (OperateScreenRiskDO coreDO : signOrderCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateScreenRiskDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateScreenRiskDO operateScreenRiskDO = merchantId2Do.get(coreDO.getMerchantId());
                operateScreenRiskDO.setSignCount(coreDO.getSignCount());
            }
            for (OperateScreenRiskDO coreDO : passCount) {
                initMap(miniType2Map, coreDO);
                Map<String, OperateScreenRiskDO> merchantId2Do = miniType2Map.get(coreDO.getMiniType());
                OperateScreenRiskDO operateScreenRiskDO = merchantId2Do.get(coreDO.getMerchantId());
                operateScreenRiskDO.setOrderPassCount(coreDO.getOrderPassCount());
            }

            // 写入mysql
            LinkedList<OperateScreenRiskDO> list = Lists.newLinkedList();

            for (Map.Entry<Integer, Map<String, OperateScreenRiskDO>> entry : miniType2Map.entrySet()) {
                for (Map.Entry<String, OperateScreenRiskDO> merchantId2Do : entry.getValue().entrySet()) {
                    OperateScreenRiskDO value = merchantId2Do.getValue();
                    list.add(value);
                    // screenRiskService.saveOrUpdate(value);
                }
            }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            screenRiskService.removeDataByTime(countDay);
            screenRiskService.saveBatch(list);
        } catch (InterruptedException | ExecutionException e) {
            log.error("OperateAccessCoreBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 获取理赔订单，理赔金
     */
    private List<OperateScreenRiskDO> getClaimRecord(String ds) {
        String sql = "select count(a.id)   num," +
                "       sum(c.amount) total_amount," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id" +
                "         inner join rent_order_insurance_claim_record c on b.id = c.insurance_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.parent_id = 0" +
                "  and c.status = 10" +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and c.update_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String totalAmount = record.getString("total_amount");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setClaimsCount(Long.valueOf(num));
            domain.setClaimsAmt(new BigDecimal(totalAmount));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());


    }

    /**
     * 获取加购订单签收量
     */
    private List<OperateScreenRiskDO> getSignOrder(String ds) {
        String sql = "select count(a.id) num," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id" +
                "         inner join rent_order_audit c on a.id = c.order_id" +
                "         inner join rent_order_logistics d on a.id = d.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.parent_id = 0" +
                "  and a.type = 1" +
                "  and b.type in (1,2,3) " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and d.ds = "+ds +
                "  and d.sign_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setSignCount(Long.valueOf(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());


    }


    /**
     * 加购订单通审支付量
     */
    private List<OperateScreenRiskDO> getPassPayCount(String ds) {
        String sql = "select count(a.id)         num," +
                "       sum(b.mouth_charge) nmv," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id" +
                "         inner join rent_order_audit c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.parent_id = 0" +
                "  and a.type = 1 " +
                "  and c.audit_status = 1 " +
                "  and c.type = 2 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and a.payment_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String nmv = record.getString("nmv");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setNmv(new BigDecimal(nmv));
            domain.setPassPayCount(Long.valueOf(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 加购碎屏订单通审量
     */
    private List<OperateScreenRiskDO> getPassCount(String ds) {
        String sql = "select count(a.id) num," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id" +
                "         inner join rent_order_audit c on a.id = c.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.type = 1" +
                "  and b.type in (1,2,3) " +
                "  and a.parent_id = 0" +
                "  and c.audit_status = 1 " +
                "  and c.type = 2 " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and c.ds = "+ds +
                "  and c.operate_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderPassCount(Long.valueOf(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 碎屏险加购订单量
     */
    private List<OperateScreenRiskDO> getOrderCount(String ds) {
        String sql = "select count(a.id) num," +
                "       a.mini_type," +
                "       a.merchant_id" +
                " from rent_order a" +
                "         inner join rent_order_insurance b on a.id = b.order_id" +
                " where a.is_deleted = 0" +
                "  and a.merchant_id = 100" +
                "  and a.parent_id = 0" +
                "  and a.type = 1 " +
                "  and b.type in (1,2,3) " +
                "  and a.ds = "+ds +
                "  and b.ds = "+ds +
                "  and a.create_time between ${fromTime} and ${toTime}" +
                " group by a.mini_type, a.merchant_id";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateScreenRiskDO domain = new OperateScreenRiskDO();
            String miniType = record.getString("mini_type");
            String num = record.getString("num");
            String merchantId = record.getString("merchant_id");
            domain.setMiniType(Integer.valueOf(miniType));
            domain.setOrderCount(Long.valueOf(num));
            domain.setMerchantId(merchantId);
            return domain;
        }).collect(Collectors.toList());
    }


}
