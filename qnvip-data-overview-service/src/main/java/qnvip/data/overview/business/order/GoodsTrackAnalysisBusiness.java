package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Maps;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.GoodsAnalysisDO;
import qnvip.data.overview.service.order.GoodsAnalysisService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/10/13
 * @since 1.0.0
 */
@Component
@RequiredArgsConstructor
public class GoodsTrackAnalysisBusiness {


    private final OdpsUtil odpsUtil;
    private final GoodsAnalysisService goodsAnalysisService;

    /**
     *
     */
    public void runCore() {

        String dsStr = ThreadLocalCacheUtil.get("dsStr");
        String fromTime = ThreadLocalCacheUtil.get("fromTime");
        String toTime = ThreadLocalCacheUtil.get("toTime");
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");

        String sqlStr = "select a.item_id" +
                "     , b.name" +
                "     , count(distinct customer_third_id)                                  uv" +
                "     , count(customer_third_id)                                           pv" +
                "     , round(sum(keep_alive_time) / count(distinct customer_third_id), 2) avgkeeptime" +
                " from DATAVIEW_TRACK_PAGE_STATISTICS a" +
                "         inner join rent_item b" +
                "                    on a.item_id = b.id" +
                " where a.action_type = 2" +
                "  and a.ds = ${dsStr}" +
                "  and b.ds = ${dsStr}" +
                "  and a.enter_page_code = 10009" +
                "  and a.REPORT_TIME BETWEEN ${fromTime} AND ${toTime}" +
                " group by a.item_id" +
                "       , b.name";
        HashMap<String, Object> key2value = Maps.newHashMap();
        key2value.put("fromTime", fromTime);
        key2value.put("toTime", toTime);
        key2value.put("dsStr", dsStr);

        String sql = SqlUtils.processTemplate(sqlStr, key2value);
        List<Record> records = odpsUtil.querySql(sql.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return;
        }
        records.forEach(record -> {
            GoodsAnalysisDO domain = new GoodsAnalysisDO();
            String pv = record.getString("pv");
            String uv = record.getString("uv");
            String avgKeepTime = record.getString("avgkeeptime");
            String itemId = record.getString("item_id");
            String name = record.getString("name");
            domain.setItemId(Long.valueOf(itemId));
            domain.setCountDay(countDay);
            domain.setName(name);
            domain.setUv(Long.parseLong(uv));
            domain.setPv(Long.parseLong(pv));
            domain.setAvgKeepTime(new BigDecimal(avgKeepTime));

            goodsAnalysisService.saveOrUpdate(domain);
        });
    }
}