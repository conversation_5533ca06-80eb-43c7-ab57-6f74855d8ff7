package qnvip.data.overview.business.dataindicators;

import cn.hutool.core.collection.CollUtil;
import com.aliyun.odps.data.Record;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.dataindicators.IndicatorLossDeliveryQualityDO;
import qnvip.data.overview.service.dataindicators.IndicatorLossDeliveryQualityService;
import qnvip.data.overview.util.DateUtils;
import qnvip.data.overview.util.OdpsUtil;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * create by gw on 2022/2/11
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class IndicatorLossDeliveryQualityBusiness {

    private final OdpsUtil odpsUtil;
    private final IndicatorLossDeliveryQualityService indicatorLossDeliveryQualityService;


    /**
     * 启动计算任务
     *
     * @param ds
     * @param sTime
     * @param eTime
     */
    public void execCount(String ds, String sTime, String eTime) {
        Map<String, IndicatorLossDeliveryQualityDO> collectMap = new HashMap<>();
        countRiskLevelCount(ds, sTime, eTime, collectMap);
        List<IndicatorLossDeliveryQualityDO> list = new ArrayList<>(collectMap.values());
        if (CollUtil.isNotEmpty(list)) {
            indicatorLossDeliveryQualityService.saveOrUpdateBatch(list, 2000);
        }
        // for (IndicatorLossDeliveryQualityDO value : collectMap.values()) {
        //     indicatorLossDeliveryQualityService.saveOrUpdate(value);
        // }
    }


    public void countRiskLevelCount(String ds, String sTime, String eTime,
                                    Map<String, IndicatorLossDeliveryQualityDO> collectMap) {
        String sql = " select mini_type   " +
                "     , to_char(a.create_time, 'yyyy-mm-dd') day " +
                "     , get_json_object(b.operate_note,'$.riskLevel') risklevel " +
                "     ,c.rate_config_type " +
                "     , count(distinct a.customer_id)        count " +
                "from rent_order a " +
                "     left join rent_order_audit b on a.id = b.order_id " +
                "     inner join rent_order_finance_detail c on a.id=c.order_id " +
                "where a.is_deleted = 0 " +
                "  and a.parent_id = 0 " +
                "  and a.type = 1 " +
                "  and a.merchant_id = 100 " +
                "  and a.biz_type = 2 " +
                "  and a.create_time between '" + sTime + "' " +
                "    and '" + eTime + "' " +
                "  and a.payment_time is not null " +
                "  and a.id not in ( " +
                "    select a.id " +
                "    from rent_order a " +
                "         inner join rent_order_logistics b on a.id = b.order_id " +
                "    where a.merchant_id = 100 " +
                "      and a.type = 1 " +
                "      and a.parent_id = 0 " +
                "      and a.biz_type = 2 " +
                "      and a.create_time between '" + sTime + "' " +
                "        and '" + eTime + "' " +
                "      and a.ds = " + ds +
                "      and b.ds = " + ds +
                ") " +
                "  and get_json_object(b.operate_note,'$.riskLevel') is not null " +
                "  and a.ds = " + ds +
                "  and b.ds = " + ds +
                "  and c.ds = " + ds +
                " group by to_char(a.create_time, 'yyyy-mm-dd'), a.mini_type, " +
                "    c.rate_config_type,get_json_object(b.operate_note,'$.riskLevel'); ";
        List<Record> records = odpsUtil.querySql(sql);
        for (Record record : records) {
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            LocalDateTime day = LocalDateTime.of(LocalDate.parse(record.getString("day")), LocalTime.MIN);
            initMap(collectMap, miniType, day);
            Integer rateConfigType = Integer.valueOf(record.getString("rate_config_type"));
            Integer riskLevel = Integer.valueOf(record.getString("risklevel"));
            Integer count = Integer.valueOf(record.getString("count"));
            IndicatorLossDeliveryQualityDO updateDO = collectMap.get(getMapKey(miniType, day));
            if (rateConfigType == 10) {
                switch (riskLevel) {
                    case 1:
                    case 2:
                        updateDO.setPlanOneLevelOne(Optional.ofNullable(updateDO.getPlanOneLevelOne()).orElse(0) + count);
                        break;
                    case 3:
                    case 4:
                        updateDO.setPlanOneLevelTwo(Optional.ofNullable(updateDO.getPlanOneLevelTwo()).orElse(0) + count);
                        break;
                    case 5:
                    case 6:
                        updateDO.setPlanOneLevelThree(Optional.ofNullable(updateDO.getPlanOneLevelThree()).orElse(0) + count);
                        break;
                    case 7:
                    case 8:
                        updateDO.setPlanOneLevelFour(Optional.ofNullable(updateDO.getPlanOneLevelFour()).orElse(0) + count);
                        break;
                    default:
                        updateDO.setPlanOneLevelFive(Optional.ofNullable(updateDO.getPlanOneLevelFive()).orElse(0) + count);
                        break;
                }
            } else {
                switch (riskLevel) {
                    case 1:
                    case 2:
                        updateDO.setPlanTwoLevelOne(Optional.ofNullable(updateDO.getPlanTwoLevelOne()).orElse(0) + count);
                        break;
                    case 3:
                    case 4:
                        updateDO.setPlanTwoLevelTwo(Optional.ofNullable(updateDO.getPlanTwoLevelTwo()).orElse(0) + count);
                        break;
                    case 5:
                    case 6:
                        updateDO.setPlanTwoLevelThree(Optional.ofNullable(updateDO.getPlanTwoLevelThree()).orElse(0) + count);
                        break;
                    case 7:
                    case 8:
                        updateDO.setPlanTwoLevelFour(Optional.ofNullable(updateDO.getPlanTwoLevelFour()).orElse(0) + count);
                        break;
                    default:
                        updateDO.setPlanTwoLevelFive(Optional.ofNullable(updateDO.getPlanTwoLevelFive()).orElse(0) + count);
                        break;
                }
            }
        }
    }


    private void initMap(Map<String, IndicatorLossDeliveryQualityDO> miniType2Map, Integer miniType,
                         LocalDateTime countDay) {
        String key = getMapKey(miniType, countDay);
        if (!miniType2Map.containsKey(key)) {
            IndicatorLossDeliveryQualityDO ivd = new IndicatorLossDeliveryQualityDO();
            ivd.setMiniType(miniType);
            ivd.setCountDay(countDay);
            ivd.setPlanOneLevelOne(0);
            ivd.setPlanOneLevelTwo(0);
            ivd.setPlanOneLevelThree(0);
            ivd.setPlanOneLevelFour(0);
            ivd.setPlanOneLevelFive(0);
            ivd.setPlanTwoLevelOne(0);
            ivd.setPlanTwoLevelTwo(0);
            ivd.setPlanTwoLevelThree(0);
            ivd.setPlanTwoLevelFour(0);
            ivd.setPlanTwoLevelFive(0);
            miniType2Map.put(key, ivd);
        }
    }

    private String getMapKey(Integer miniType, LocalDateTime countDay) {
        return miniType + "_" + DateUtils.dateToString(countDay);
    }

}