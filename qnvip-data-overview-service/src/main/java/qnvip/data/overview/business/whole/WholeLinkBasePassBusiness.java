package qnvip.data.overview.business.whole;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.domain.whole.WholeLinkBasePassDO;
import qnvip.data.overview.enums.MiniTypeEnum;
import qnvip.data.overview.service.whole.WholeLinkBasePassService;
import qnvip.data.overview.util.CalculateUtil;
import qnvip.data.overview.util.OdpsUtil;

import java.lang.reflect.Field;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * create by gw on 2022/3/24
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WholeLinkBasePassBusiness {

    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
    private final DateTimeFormatter YYYY_MM_DD_HH_MM_SS = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private final OdpsUtil odpsUtil;
    private final WholeLinkBasePassService wholeLinkBasePassService;

    private final AtomicInteger atomicInteger = new AtomicInteger();
    private final ThreadPoolExecutor threadPoolExecutor = new ThreadPoolExecutor(10, 20, 0, TimeUnit.SECONDS,
            new LinkedBlockingQueue(1000), r -> {
        Thread t = new Thread(r);
        t.setName("WholeLinkBasePassBusiness-" + atomicInteger.incrementAndGet());
        return t;
    });

    void initMap(Map<String, WholeLinkBasePassDO> miniType2Map, WholeLinkBasePassDO domain) {
        String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
        if (!miniType2Map.containsKey(key)) {
            WholeLinkBasePassDO ac = new WholeLinkBasePassDO();
            ac.setMiniType(domain.getMiniType());
            ac.setCountDay(domain.getCountDay());
            ac.setCountHour(domain.getCountHour());
            miniType2Map.put(key, ac);
        }
    }


    /**
     * @param ds
     */
    public void execOldData(String ds, Integer hour) {
        try {
            Map<String, WholeLinkBasePassDO> miniType2Map = new HashMap<>();
            CompletableFuture<List<WholeLinkBasePassDO>> f1 =
                    CompletableFuture.supplyAsync(() -> getValidUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f2 =
                    CompletableFuture.supplyAsync(() -> getValidUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f3 =
                    CompletableFuture.supplyAsync(() -> getApplyUVByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f4 =
                    CompletableFuture.supplyAsync(() -> getApplyUV(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f5 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f6 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5ChannelUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f7 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUvByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f8 =
                    CompletableFuture.supplyAsync(() -> getOrderTop5SceneUv(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f11 =
                    CompletableFuture.supplyAsync(() -> getIphoneUVByMiniType(ds, hour), threadPoolExecutor);
            CompletableFuture<List<WholeLinkBasePassDO>> f12 =
                    CompletableFuture.supplyAsync(() -> getIphoneUV(ds, hour), threadPoolExecutor);
            CompletableFuture.allOf(f3, f4, f5, f6, f7, f8).join();
            assembleValue(miniType2Map, f1.get(), "basePassValidUv");
            assembleValue(miniType2Map, f2.get(), "basePassValidUv");
            assembleValue(miniType2Map, f3.get(), "applyUv", "allowUv", "aliAllowUv", "ageForbidUv", "areaForbidUv", "blacklistForbidUv");
            assembleValue(miniType2Map, f4.get(), "applyUv", "allowUv", "aliAllowUv", "ageForbidUv", "areaForbidUv",
                    "blacklistForbidUv");
            assembleValue(miniType2Map, f5.get(), "topChannelUv");
            assembleValue(miniType2Map, f6.get(), "topChannelUv");
            assembleValue(miniType2Map, f7.get(), "topSceneUv");
            assembleValue(miniType2Map, f8.get(), "topSceneUv");
            assembleValue(miniType2Map, f11.get(), "twelveOrderCnt", "iphone13pmOrderCnt", "iphoneOrderCnt");
            assembleValue(miniType2Map, f12.get(), "twelveOrderCnt", "iphone13pmOrderCnt", "iphoneOrderCnt");
            List<WholeLinkBasePassDO> list = Lists.newArrayList(miniType2Map.values());
            List<WholeLinkBasePassDO> collect = list.stream().peek(o -> {
                Long allowUv = o.getAllowUv();
                Long applyUv = o.getApplyUv();
                Long basePassValidUv = o.getBasePassValidUv();
                o.setBasePassValidRate(CalculateUtil.div(basePassValidUv, applyUv));
                o.setAllowRate(CalculateUtil.div(allowUv, applyUv));
            }).collect(Collectors.toList());
            LocalDate now = LocalDate.now();
            wholeLinkBasePassService.removeByHour(hour, now);
            wholeLinkBasePassService.saveBatch(collect);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
        }

    }

    private void assembleValue(Map<String, WholeLinkBasePassDO> miniType2Map,
                               List<WholeLinkBasePassDO> list,
                               String... fields) throws Exception {

        for (WholeLinkBasePassDO domain : list) {
            String key = domain.getCountDay() + "-" + domain.getCountHour() + "-" + domain.getMiniType();
            initMap(miniType2Map, domain);
            WholeLinkBasePassDO core = miniType2Map.get(key);
            for (String fieldName : fields) {
                //获取值
                Field field = domain.getClass().getDeclaredField(fieldName);
                field.setAccessible(true);
                // 设置值
                Field declaredField = core.getClass().getDeclaredField(fieldName);
                declaredField.setAccessible(true);
                declaredField.set(core, field.get(domain));
            }
        }
    }

    /**
     * 禁入\申请人数\准入通过
     */
    private List<WholeLinkBasePassDO> getApplyUVByMiniType(String ds, Integer hour) {
        String sql = "select" +
                "          ro.mini_type," +
                "          count(DISTINCT idcardNo)                                     apply_uv," +
                "          COUNT(DISTINCT (CASE" +
                "                              WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN idcardNo" +
                "                              ELSE 0 END))                             allow_uv," +
                "          COUNT(DISTINCT (CASE" +
                "                              WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' and rmc" +
                " .platform_code = 'ALIPAY' THEN idcardNo" +
                "                              ELSE 0 END))                             ali_allow_uv," +
                "          count(DISTINCT if(s.`ruleCode` = '8R004'  and rs.result = 5, idcardNo, null)) as age_forbid_uv," +
                "          count(DISTINCT if(s.`ruleCode` = '8R005'  and rs.result = 5, idcardNo, null)) as area_forbid_uv," +
                "          count(DISTINCT if(s.`ruleCode` = 'R998'  and rs.result = 5, idcardNo, null)) as blacklist_forbid_uv" +
                "   from cl_loan cl" +
                "            left join rent_order ro on cl.loanno = ro.no" +
                "            left join serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_risk_access_rule_result_2023 rs ON rs.serialnoId = sn.id" +
                "            left join rc_risk_strategy_rule_set s ON s.id = rs.ruleid" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)" +
                "     and cl.parentNo = ''" +
                "     and cl.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and sn.ds =" + ds +
                "     and rs.ds =" + ds +
                "     and s.ds =" + ds +
                "     and ru.ds =" + ds +
                "     and DATE(cl.createTime) = date(getdate())" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long applyUv = Long.parseLong(record.getString("apply_uv"));
            Long allowUv = Long.parseLong(record.getString("allow_uv"));
            Long ageForbidUv = Long.parseLong(record.getString("age_forbid_uv"));
            Long areaForbidUv = Long.parseLong(record.getString("area_forbid_uv"));
            Long blacklistForbidUv = Long.parseLong(record.getString("blacklist_forbid_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            Long aliAllowUv = Long.parseLong(record.getString("ali_allow_uv"));
            domain.setAliAllowUv(aliAllowUv);
            domain.setMiniType(miniType);
            domain.setApplyUv(applyUv);
            domain.setAllowUv(allowUv);
            domain.setAgeForbidUv(ageForbidUv);
            domain.setAreaForbidUv(areaForbidUv);
            domain.setBlacklistForbidUv(blacklistForbidUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 禁入\申请人数\准入通过
     */
    private List<WholeLinkBasePassDO> getApplyUV(String ds, Integer hour) {
        String sql = "select" +
                "          count(DISTINCT idcardNo)                                     apply_uv," +
                "          COUNT(DISTINCT (CASE" +
                "                              WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' THEN idcardNo" +
                "                              ELSE 0 END))                             allow_uv," +
                "          COUNT(DISTINCT (CASE" +
                "                              WHEN cl.riskStatus not in (1, 5, 6, 15) and cl.parentNo = '' and rmc" +
                " .platform_code = 'ALIPAY' THEN idcardNo" +
                "                              ELSE 0 END))                             ali_allow_uv," +
                "          count(DISTINCT if(s.`ruleCode` = '8R004'  and rs.result = 5, idcardNo, null)) as age_forbid_uv," +
                "          count(DISTINCT if(s.`ruleCode` = '8R005'  and rs.result = 5, idcardNo, null)) as area_forbid_uv," +
                "          count(DISTINCT if(s.`ruleCode` = 'R998'  and rs.result = 5, idcardNo, null)) as blacklist_forbid_uv" +
                "   from cl_loan cl" +
                "            left join rent_order ro on cl.loanno = ro.no" +
                "            left join serial_no sn ON cl.loanNo = sn.businessNo" +
                "            left join rc_risk_access_rule_result_2023 rs ON rs.serialnoId = sn.id" +
                "            left join rc_risk_strategy_rule_set s ON s.id = rs.ruleid" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_mini_config rmc on ro.mini_type = rmc.mini_type" +
                "   where cl.businessChannel in (1, 11, 14, 15, 23, 25, 27, 29)" +
                "     and cl.parentNo = ''" +
                "     and cl.ds =" + ds +
                "     and ro.ds =" + ds +
                "     and sn.ds =" + ds +
                "     and rs.ds =" + ds +
                "     and s.ds =" + ds +
                "     and ru.ds =" + ds +
                "     and DATE(cl.createTime) = date(getdate());";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long applyUv = Long.parseLong(record.getString("apply_uv"));
            Long allowUv = Long.parseLong(record.getString("allow_uv"));
            Long aliAllowUv = Long.parseLong(record.getString("ali_allow_uv"));
            Long ageForbidUv = Long.parseLong(record.getString("age_forbid_uv"));
            Long areaForbidUv = Long.parseLong(record.getString("area_forbid_uv"));
            Long blacklistForbidUv = Long.parseLong(record.getString("blacklist_forbid_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setApplyUv(applyUv);
            domain.setAllowUv(allowUv);
            domain.setAliAllowUv(aliAllowUv);
            domain.setAgeForbidUv(ageForbidUv);
            domain.setAreaForbidUv(areaForbidUv);
            domain.setBlacklistForbidUv(blacklistForbidUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 有效用户
     */
    private List<WholeLinkBasePassDO> getValidUvByMiniType(String ds, Integer hour) {
        String sql = "" +
                "select count(DISTINCT (s.userId)) valid_user,ro.mini_type" +
                "   from serial_no s" +
                "     left join rent_order ro on s.businessno = ro.no" +
                "   where s.userId not in (select userId" +
                "                          from serial_no" +
                "                          where riskStrategy != ''" +
                "                            and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) >= dateadd(date(getdate()), -30,'dd')" +
                "                            and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate())" +
                "                            and ds =" + ds +
                "   )" +
                "     and s.ds = " + ds +
                "     and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and ro.ds =" + ds +
                "     and s.businessChannel in (1,11,14,15,23,25,27,29)" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long basePassValidUv = Long.parseLong(record.getString("valid_user"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setBasePassValidUv(basePassValidUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 有效用户
     */
    private List<WholeLinkBasePassDO> getValidUv(String ds, Integer hour) {
        String sql = "" +
                "select count(DISTINCT (s.userId)) valid_user" +
                "   from serial_no s" +
                "     left join rent_order ro on s.businessno = ro.no" +
                "   where s.userId not in (select userId" +
                "                          from serial_no" +
                "                          where riskStrategy != ''" +
                "                            and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) >= dateadd(date(getdate()), -30,'dd')" +
                "                            and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) < date(getdate())" +
                "                            and ds =" + ds +
                "   )" +
                "     and s.ds = " + ds +
                "     and date(to_date(createtime, 'yyyy-MM-dd HH:mi:ss')) = date(getdate())" +
                "     and ro.ds =" + ds +
                "     and s.businessChannel in (1,11,14,15,23,25,27,29);";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long basePassValidUv = Long.parseLong(record.getString("valid_user"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setBasePassValidUv(basePassValidUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取iphone相关信息
     */
    private List<WholeLinkBasePassDO> getIphoneUVByMiniType(String ds, Integer hour) {
        String sql = "select date(cl.createTime)                                                                             time," +
                "                          ro.mini_type," +
                "                          count(distinct IF(lower(t2.name) LIKE '%iphone%', ro.customer_id, null))                                 order_iphone_order_cnt," +
                "                          count(distinct" +
                "                                IF(lower(t2.name) LIKE '%iphone14%', ro.customer_id, null))                                order_iphone13pm_order_cnt," +
                "                          count(distinct IF(t3.repayment_term = 12, ro.customer_id, null))                                 order_twelve_order_cnt" +
                "                   from rent_order ro" +
                "                            LEFT JOIN rent_order_item t2 on ro.id = t2.order_id AND t2.item_type = 1" +
                "                            left join rent_order_finance_detail t3 on ro.id = t3.order_id" +
                "                            left join cl_loan cl on cl.loanno = ro.no" +
                "                            left join  rc_user ru on cl.`customerId`=ru.id" +
                "                   where ro.is_deleted = 0" +
                "                     and ro.type = 1" +
                "                     and ro.parent_id = 0" +
                "                     and ro.ds =  " + ds +
                "                     and cl.ds =  " + ds +
                "                     and ru.ds =  " + ds +
                "                     and t2.ds =  " + ds +
                "                     and t3.ds =  " + ds +
                "                    and DATE(cl.createTime) = date(getdate())" +
                "                   group by date(cl.createTime), ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long orderIphoneOrderCnt = Long.parseLong(record.getString("order_iphone_order_cnt"));
            Long orderIphone13pmOrderCnt = Long.parseLong(record.getString("order_iphone13pm_order_cnt"));
            Long orderTwelveOrderCnt = Long.parseLong(record.getString("order_twelve_order_cnt"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setIphoneOrderCnt(orderIphoneOrderCnt);
            domain.setIphone13pmOrderCnt(orderIphone13pmOrderCnt);
            domain.setTwelveOrderCnt(orderTwelveOrderCnt);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * 获取iphone相关信息
     */
    private List<WholeLinkBasePassDO> getIphoneUV(String ds, Integer hour) {
        String sql = "select                                                                         " +
                "                          count(distinct IF(lower(t2.name) LIKE '%iphone%', ro.customer_id, null))                                 order_iphone_order_cnt," +
                "                          count(distinct" +
                "                                IF(lower(t2.name) LIKE '%iphone14%', ro.customer_id, null))                                order_iphone13pm_order_cnt," +
                "                          count(distinct IF(t3.repayment_term = 12, ro.customer_id, null))                                 order_twelve_order_cnt" +
                "                   from rent_order ro" +
                "                            LEFT JOIN rent_order_item t2 on ro.id = t2.order_id AND t2.item_type = 1" +
                "                            left join rent_order_finance_detail t3 on ro.id = t3.order_id" +
                "                            left join cl_loan cl on cl.loanno = ro.no" +
                "                            left join  rc_user ru on cl.`customerId`=ru.id" +
                "                   where ro.is_deleted = 0" +
                "                     " +
                "                     and ro.type = 1" +
                "                     and ro.parent_id = 0" +
                "                     and ro.ds =  " + ds +
                "                     and cl.ds =  " + ds +
                "                     and ru.ds =  " + ds +
                "                     and t2.ds =  " + ds +
                "                     and t3.ds =  " + ds +
                "                    and DATE(cl.createTime) = date(getdate());";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);
            Long orderIphoneOrderCnt = Long.parseLong(record.getString("order_iphone_order_cnt"));
            Long orderIphone13pmOrderCnt = Long.parseLong(record.getString("order_iphone13pm_order_cnt"));
            Long orderTwelveOrderCnt = Long.parseLong(record.getString("order_twelve_order_cnt"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setIphoneOrderCnt(orderIphoneOrderCnt);
            domain.setIphone13pmOrderCnt(orderIphone13pmOrderCnt);
            domain.setTwelveOrderCnt(orderTwelveOrderCnt);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }


    /**
     * top渠道
     */
    private List<WholeLinkBasePassDO> getOrderTop5ChannelUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and DATE(cl.createTime) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setTopChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top渠道
     */
    private List<WholeLinkBasePassDO> getOrderTop5ChannelUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.quotient_id," +
                "                      count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.quotient_id" +
                "               order by count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.quotient_id = ros.quotient_id" +
                "   where cl.parentNo = ''" +
                "     and DATE(cl.createTime) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setTopChannelUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkBasePassDO> getOrderTop5SceneUvByMiniType(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select  ro.mini_type,count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and DATE(cl.createTime) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0" +
                "   group by ro.mini_type;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            Integer miniType = Integer.valueOf(record.getString("mini_type"));
            domain.setMiniType(miniType);
            domain.setTopSceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * top5场景(不包含顶搜)
     */
    private List<WholeLinkBasePassDO> getOrderTop5SceneUv(String ds, Integer hour) {
        String sql = "with a1 as (select ros.mini_scene," +
                "                      count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end))" +
                "               from cl_loan cl" +
                "                        left join rc_user ru on cl.`customerId` = ru.id" +
                "                        left join rent_order ro on ro.no = cl.loanno" +
                "                        left join rent_order_source ros on ro.id = ros.order_id" +
                "               where cl.parentNo = ''" +
                "                 and DATE(cl.createTime) = dateadd(date(getdate()), -2, 'dd')" +
                "                 and ro.is_deleted = 0" +
                "                 " +
                "                 and ro.type = 1" +
                "                 and ro.parent_id = 0" +
                "                 and cl.ds = " + ds +
                "                 and ru.ds = " + ds +
                "                 and ro.ds = " + ds +
                "                 and ros.ds = " + ds +
                "               group by ros.mini_scene" +
                "               order by count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then idcardNo else null end)) desc" +
                "               limit 1,5)" +
                "   select count(DISTINCT (case when cl.riskStatus not in (1, 5, 6, 15) then cl" +
                " .customerId else null end)) order_top5_channel_uv" +
                "   from cl_loan cl" +
                "            left join rc_user ru on cl.`customerId` = ru.id" +
                "            left join rent_order ro on ro.no = cl.loanno" +
                "            left join rent_order_source ros on ro.id = ros.order_id" +
                "            inner join a1 on a1.mini_scene = ros.mini_scene" +
                "   where cl.parentNo = ''" +
                "     and DATE(cl.createTime) = date(getdate())" +
                "     and cl.ds = " + ds +
                "     and ru.ds = " + ds +
                "     and ro.ds = " + ds +
                "     and ros.ds = " + ds +
                "     and ro.is_deleted = 0" +
                "     " +
                "     and ro.type = 1" +
                "     and ro.parent_id = 0;";
        List<Record> records = odpsUtil.querySql(sql);
        return records.stream().map((record) -> {
            WholeLinkBasePassDO domain = new WholeLinkBasePassDO();
            LocalDateTime localDate = LocalDateTime.of(LocalDate.now(), LocalTime.MIN);

            Long orderTop5ChannelUv = Long.parseLong(record.getString("order_top5_channel_uv"));
            domain.setMiniType(MiniTypeEnum.TOTAl.getCode());
            domain.setTopSceneUv(orderTop5ChannelUv);
            domain.setCountDay(localDate);
            domain.setCountHour(hour);
            return domain;
        }).collect(Collectors.toList());
    }
}



