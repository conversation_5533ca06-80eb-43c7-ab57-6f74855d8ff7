package qnvip.data.overview.business.order;

import com.aliyun.odps.data.Record;
import com.google.common.collect.Lists;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import qnvip.data.overview.domain.order.OperateMarginDO;
import qnvip.data.overview.service.order.OperateMarginService;
import qnvip.data.overview.util.OdpsUtil;
import qnvip.data.overview.util.SqlUtils;
import qnvip.data.overview.util.ThreadLocalCacheUtil;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2021/11/22
 * @since 1.0.0
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class OperateMarginBusiness {
    private final OdpsUtil odpsUtil;

    private final OperateMarginService marginService;

    void initMap(Map<Integer, OperateMarginDO> level2Map, OperateMarginDO orderDO) {
        LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
        if (!level2Map.containsKey(orderDO.getLevel())) {
            OperateMarginDO domain = new OperateMarginDO();
            domain.setCountDay(countDay);
            domain.setLevel(orderDO.getLevel());
            level2Map.put(orderDO.getLevel(), domain);
        }
    }

    /**
     * 定时调度任务
     */
    public void runCore(String ds) {
        Map<Integer, OperateMarginDO> level2Map = new HashMap<>();
        CompletableFuture<List<OperateMarginDO>> f1 = CompletableFuture.supplyAsync(()->getPayCount(ds));
        CompletableFuture<List<OperateMarginDO>> f2 = CompletableFuture.supplyAsync(()->getRiskPassCount(ds));
        CompletableFuture<List<OperateMarginDO>> f3 = CompletableFuture.supplyAsync(()->getOrderCount(ds));
        CompletableFuture.allOf(f1, f2, f3).join();
        try {
            List<OperateMarginDO> payCount = f1.get();
            List<OperateMarginDO> riskPassCount = f2.get();
            List<OperateMarginDO> orderCount = f3.get();

            for (OperateMarginDO coreDO : payCount) {
                initMap(level2Map, coreDO);
                OperateMarginDO operateMarginDO = level2Map.get(coreDO.getLevel());
                operateMarginDO.setPayCount(coreDO.getPayCount());
            }
            for (OperateMarginDO coreDO : riskPassCount) {
                initMap(level2Map, coreDO);
                OperateMarginDO operateMarginDO = level2Map.get(coreDO.getLevel());
                operateMarginDO.setRiskPassCount(coreDO.getRiskPassCount());
            }
            for (OperateMarginDO coreDO : orderCount) {
                initMap(level2Map, coreDO);
                OperateMarginDO operateMarginDO = level2Map.get(coreDO.getLevel());
                operateMarginDO.setOrderCount(coreDO.getOrderCount());
            }

            // 写入mysql
            LinkedList<OperateMarginDO> operateMarginDOS = Lists.newLinkedList(level2Map.values());
            // for (Map.Entry<Integer, OperateMarginDO> entry : level2Map.entrySet()) {
            //     OperateMarginDO value = entry.getValue();
            //     marginService.saveOrUpdate(value);
            // }
            LocalDateTime countDay = ThreadLocalCacheUtil.get("countDay");
            marginService.removeDataByTime(countDay);
            marginService.saveBatch(operateMarginDOS);
        } catch (InterruptedException | ExecutionException e) {
            log.error("OperateAccessCoreBusiness.runCore error:{}", e.getMessage());
        }

    }

    /**
     * 获取支付单数
     */
    private List<OperateMarginDO> getPayCount(String ds) {
        String sql =
                " select c.level as level, count(1) as num" +
                        " from (select (case" +
                        "                  when b.act_bond_amt >= 0 and b.act_bond_amt < 100 THEN 1" +
                        "                  when b.act_bond_amt >= 100 and b.act_bond_amt < 1200 THEN 2" +
                        "                  when b.act_bond_amt >= 1200 and b.act_bond_amt < 2500 THEN 3" +
                        "                  when b.act_bond_amt >= 2500 and b.act_bond_amt < 3700 THEN 4" +
                        "                  when b.act_bond_amt >= 3700 and b.act_bond_amt < 5800 THEN 5" +
                        "                  ELSE 6 end) as level" +
                        "      from (select IF(b.act_bond_amt <= 0, b.bond_amt, b.act_bond_amt)           as act_bond_amt," +
                        "                   IF(a.payment_time is not null, a.payment_time, a.create_time) as payment_time" +
                        "            from rent_order a" +
                        "                     inner join rent_order_finance_detail b on a.id = b.order_id" +
                        "            where "+
                        "               a.merchant_id = 100" +
                        "              and a.payment_time is not null" +
                        "              and a.is_deleted = 0" +
                        "              and a.type = 1 " +
                        "              and a.merchant_id = 100 " +
                        "              and a.ds = "+ds +
                        "              and b.ds = "+ds +
                        "           ) b" +
                        "       where b.payment_time between ${fromTime} and ${toTime}" +
                        "    ) c" +
                        " group by c.level";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMarginDO domain = new OperateMarginDO();
            String level = record.getString("level");
            String num = record.getString("num");
            domain.setLevel(Integer.valueOf(level));
            domain.setPayCount(Long.parseLong(num));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 获取下单数（订单数）
     */
    private List<OperateMarginDO> getOrderCount(String ds) {
        String sql =
                "select c.level as level, count(1) as num" +
                        " from (select (case" +
                        "                  when b.act_bond_amt >= 0 and b.act_bond_amt < 100 THEN 1" +
                        "                  when b.act_bond_amt >= 100 and b.act_bond_amt < 1200 THEN 2" +
                        "                  when b.act_bond_amt >= 1200 and b.act_bond_amt < 2500 THEN 3" +
                        "                  when b.act_bond_amt >= 2500 and b.act_bond_amt < 3700 THEN 4" +
                        "                  when b.act_bond_amt >= 3700 and b.act_bond_amt < 5800 THEN 5" +
                        "                  ELSE 6 end) as level" +
                        "      from (select IF(b.act_bond_amt <= 0, b.bond_amt, b.act_bond_amt) as act_bond_amt" +
                        "            from rent_order a" +
                        "                     inner join rent_order_finance_detail b on a.id = b.order_id" +
                        "            where " +
                        "               a.merchant_id = 100" +
                        "              and a.is_deleted = 0" +
                        "               and a.type = 1 " +
                        "               and a.merchant_id = 100 " +
                        "               and a.ds = "+ds +
                        "               and b.ds = "+ds +
                        "              and a.create_time between ${fromTime} and ${toTime}" +
                        "           ) b) c" +
                        " group by c.level";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMarginDO domain = new OperateMarginDO();
            String level = record.getString("level");
            String num = record.getString("num");
            domain.setLevel(Integer.valueOf(level));
            domain.setOrderCount(Long.parseLong(num));
            return domain;
        }).collect(Collectors.toList());
    }

    /**
     * 通审订单
     */
    private List<OperateMarginDO> getRiskPassCount(String ds) {
        String sql =
                "select c.level as level, count(1) as num" +
                        " from (select (case" +
                        "                  when b.act_bond_amt >= 0 and b.act_bond_amt < 100 THEN 1" +
                        "                  when b.act_bond_amt >= 100 and b.act_bond_amt < 1200 THEN 2" +
                        "                  when b.act_bond_amt >= 1200 and b.act_bond_amt < 2500 THEN 3" +
                        "                  when b.act_bond_amt >= 2500 and b.act_bond_amt < 3700 THEN 4" +
                        "                  when b.act_bond_amt >= 3700 and b.act_bond_amt < 5800 THEN 5" +
                        "                  ELSE 6 end) as level" +
                        "      from (" +
                        "               select IF(b.act_bond_amt <= 0, b.bond_amt, b.act_bond_amt) as act_bond_amt" +
                        "               from rent_order ro" +
                        "                        inner join rent_order_audit roa on ro.id = roa.order_id" +
                        "                        inner join rent_order_finance_detail b on ro.id = b.order_id" +
                        "               where " +
                        "                   ro.merchant_id = 100" +
                        "                 and  ro.type = 1" +
                        "                 and ro.parent_id = 0" +
                        "                 and roa.type = 2" +
                        "                  and ro.ds = "+ds +
                        "                  and roa.ds = "+ds +
                        "                  and b.ds = "+ds +
                        "                 and roa.audit_status = 1" +
                        "                 and ro.is_deleted = 0" +
                        "                  and roa.operate_time between ${fromTime} and ${toTime}" +
                        "           ) b) c" +
                        " group by c.level";

        HashMap<String, Object> key2value = ThreadLocalCacheUtil.get("key2value");


        String format = SqlUtils.processTemplate(sql, key2value);

        List<Record> records = odpsUtil.querySql(format.concat(";"));

        if (CollectionUtils.isEmpty(records)) {
            return Lists.newArrayList();
        }
        return records.stream().map(record -> {
            OperateMarginDO domain = new OperateMarginDO();
            String level = record.getString("level");
            String num = record.getString("num");
            domain.setLevel(Integer.valueOf(level));
            domain.setRiskPassCount(Long.parseLong(num));
            return domain;
        }).collect(Collectors.toList());
    }

}
