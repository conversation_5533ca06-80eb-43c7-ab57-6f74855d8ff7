package qnvip.data.overview.business.rpc;

import com.qnvip.base.vo.req.JobTaskQueryReq;
import com.qnvip.base.vo.req.JobTaskReq;
import com.qnvip.base.vo.resp.JobTaskResp;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import qnvip.data.overview.feign.JobApiClient;

import java.util.List;

/**
 * JobApi服务类 - 使用OpenFeign替代Dubbo
 * Date: 2024/11/28
 * Time: 11:39
 * Description: 通过Feign客户端调用JobApi服务
 * <AUTHOR>
 */
@Slf4j
@Service("businessJobApiService")
@RequiredArgsConstructor
public class JobApiService {

    private final JobApiClient jobApiClient;

    /**
     * 插入任务记录
     */
    public Object insertJobTask(JobTaskReq jobTaskReq) {
        log.info("调用JobApi.insertJobTask，参数: {}", jobTaskReq);
        return jobApiClient.insertJobTask(jobTaskReq);
    }

    /**
     * 根据任务代码查询任务列表
     */
    public List<JobTaskResp> queryTaskByCode(String taskCode) {
        log.info("调用JobApi.queryTaskByCode，taskCode: {}", taskCode);
        return jobApiClient.queryTaskByCode(taskCode);
    }
}
