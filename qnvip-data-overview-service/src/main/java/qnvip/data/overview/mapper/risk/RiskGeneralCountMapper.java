package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import qnvip.data.overview.domain.risk.RiskGeneralCountDO;

@Mapper
public interface RiskGeneralCountMapper extends BaseMapper<RiskGeneralCountDO> {
    /**
    * 根据countDay批量删除
    *
    */
    @Delete("truncate table dataview_risk_general_count1")
    public void delete();

}
