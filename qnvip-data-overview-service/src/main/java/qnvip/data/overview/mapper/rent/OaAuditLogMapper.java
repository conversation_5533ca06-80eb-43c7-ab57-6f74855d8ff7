package qnvip.data.overview.mapper.rent;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import qnvip.data.overview.domain.rent.OaAuditLogDO;

import java.util.List;

/**
 * Created by zhanglei on 2023-10-20
 */

public interface OaAuditLogMapper extends BaseMapper<OaAuditLogDO> {
    @Select("select o.no from rent_order o, rent_oa_audit_log l where o.id = l.order_id and o.is_deleted = 0 and l.is_deleted = 0 and o.type = 1 and (l.`first_audit_allot_service_id`  > 1 or l.`second_audit_allot_service_id`  > 1) and l.artificial_audit_flag = 1 and o.no in(${param})")
    List<String> queryOrderNo(@Param("param") String param);
}
