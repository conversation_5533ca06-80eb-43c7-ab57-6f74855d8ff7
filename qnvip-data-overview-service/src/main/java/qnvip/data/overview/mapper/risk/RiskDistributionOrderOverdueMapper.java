package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import qnvip.data.overview.domain.risk.RiskDistributionOrderOverdueDO;

@Mapper
public interface RiskDistributionOrderOverdueMapper extends BaseMapper<RiskDistributionOrderOverdueDO> {
    /**
    * 根据countDay批量删除
    *
    */
    @Delete("truncate table dataview_risk_distribution_order_overdue_gray")
    public void deleteAll();

}
