package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import qnvip.data.overview.domain.risk.RiskMerchantCountDO;

@Mapper
public interface RiskMerchantCountMapper extends BaseMapper<RiskMerchantCountDO> {
    /**
    * 根据countDay批量删除
    *
    */
    @Delete("truncate table dataview_risk_merchant_count_gray ")
    public void deleteAll();

}
