package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import qnvip.data.overview.domain.risk.RiskDistributionCountDO;

@Mapper
public interface RiskDistributionCountMapper extends BaseMapper<RiskDistributionCountDO> {

    /**
     * 根据countDay批量删除
     *
     */
    @Delete("truncate table dataview_risk_distribution_count1")
    public void deleteAll();

}
