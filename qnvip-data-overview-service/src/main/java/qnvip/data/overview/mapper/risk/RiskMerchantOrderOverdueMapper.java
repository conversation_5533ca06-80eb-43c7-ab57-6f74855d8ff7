package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import qnvip.data.overview.domain.risk.RiskMerchantOrderOverdueDO;

@Mapper
public interface RiskMerchantOrderOverdueMapper extends BaseMapper<RiskMerchantOrderOverdueDO> {
    /**
    * 根据countDay批量删除
    *
    */
    @Delete("truncate table dataview_risk_merchant_order_overdue")
    public void deleteAll();

}
