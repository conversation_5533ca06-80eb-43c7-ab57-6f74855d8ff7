package qnvip.data.overview.mapper.risk;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;
import qnvip.data.overview.domain.risk.RiskGeneralOrderOverdueDO;
import qnvip.data.overview.mapper.base.EasyBaseMapper;

import java.util.List;
import java.util.Map;

@Mapper
public interface RiskGeneralOrderOverdueMapper extends EasyBaseMapper<RiskGeneralOrderOverdueDO> {
    /**
     * 删除之前的数据
     *
     */
    @Delete("truncate table dataview_risk_general_order_overdue_gray")
    void deleteAll();


    List<Map<String,String>> selectAllList();
}
