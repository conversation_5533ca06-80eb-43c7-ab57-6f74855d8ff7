package qnvip.data.overview.strategy.eventlog.event;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.eventlog.EventLogClickDO;
import qnvip.data.overview.dto.track.EventTrackDTO;
import qnvip.data.overview.enums.eventlog.EventLogEnum;
import qnvip.data.overview.service.eventlog.EventLogClickService;
import qnvip.data.overview.strategy.eventlog.AbstractEventLogStrategy;
import qnvip.data.overview.strategy.eventlog.EventLogStrategyFactory;
import qnvip.rent.common.util.CopierUtil;

import javax.annotation.Resource;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
public class ClickEventLogStrategy extends AbstractEventLogStrategy implements InitializingBean {

    @Resource
    public EventLogClickService eventLogClickService;

    @Override
    public void record(EventTrackDTO.InnerDTO baseDO) {
        EventLogClickDO clickDO = CopierUtil.copy(baseDO, EventLogClickDO.class);
        eventLogClickService.insert(clickDO);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        EventLogStrategyFactory.register(EventLogEnum.CLICK.name(), this);
    }
}
