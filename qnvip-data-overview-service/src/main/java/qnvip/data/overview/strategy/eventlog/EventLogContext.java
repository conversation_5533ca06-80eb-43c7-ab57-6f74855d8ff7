package qnvip.data.overview.strategy.eventlog;

import qnvip.data.overview.dto.track.EventTrackDTO;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
public class EventLogContext {

    private final AbstractEventLogStrategy strategy;



    public EventLogContext(String key) {
        this.strategy =  EventLogStrategyFactory.getByEnum(key);
    }


    public void record(EventTrackDTO.InnerDTO t) {
        strategy.record(t);
    }

}
