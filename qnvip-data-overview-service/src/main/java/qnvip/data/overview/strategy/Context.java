package qnvip.data.overview.strategy;

import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
public class Context {

    AbstractStrategy strategy;

    public Context(AbstractStrategy strategy) {
        this.strategy = strategy;
    }

    /**
     * 上下文接口
     */
    public void contextInterface() {
        strategy.algorithmInterface();
    }


    public Map<String,Object> getResult() {
        return strategy.algorithmInterface();
    }

}
