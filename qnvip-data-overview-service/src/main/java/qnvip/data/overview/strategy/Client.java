package qnvip.data.overview.strategy;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContextAware;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import qnvip.data.overview.enums.StrategyEnum;
import qnvip.data.overview.strategy.category.*;

import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
public class Client  {

    private Context context = null;

    public Map<String, Object> getData(StrategyEnum strategy) {
        switch (strategy) {
            case TWELVE:
                context = new Context(new Strategy12());
                break;
            case THIRTEEN:
                context = new Context(new Strategy13());
                break;
            case FOURTEEN:
                context = new Context(new Strategy14());
                break;
            case FIFTEEN:
                context = new Context(new Strategy15());
                break;
            case SIXTEEN:
                context = new Context(new Strategy16());
                break;
            case SEVENTEEN:
                context = new Context(new Strategy17());
                break;
            case EIGHTEEN:
                context = new Context(new Strategy18());
                break;
            case NINETEEN:
                context = new Context(new Strategy19());
                break;
            case TWENTY:
                context = new Context(new Strategy20());
                break;
            case TWENTY_ONE:
                context = new Context(new Strategy21());
                break;
            case TWENTY_TWO:
                context = new Context(new Strategy22());
                break;
            case TWENTY_THREE:
                context = new Context(new Strategy23());
                break;
            default:
                break;
        }
       return context.getResult();
    }


}
