package qnvip.data.overview.strategy;

import com.baomidou.dynamic.datasource.annotation.DS;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;
import qnvip.data.overview.mapper.order.RentOrderMapper;
import qnvip.data.overview.util.ApplicationContextUtil;

import javax.annotation.Resource;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */

public abstract class AbstractStrategy {


    public RentOrderMapper rentOrderMapper;

    public AbstractStrategy() {
        this.rentOrderMapper = (RentOrderMapper) ApplicationContextUtil.getObject("rentOrderMapper");
    }

    /**
     * 算法策略
     *
     * @return
     */
    public abstract Map<String, Object> algorithmInterface();


}
