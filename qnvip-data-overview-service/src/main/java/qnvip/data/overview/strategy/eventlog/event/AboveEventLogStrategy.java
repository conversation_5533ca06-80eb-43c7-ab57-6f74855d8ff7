package qnvip.data.overview.strategy.eventlog.event;

import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;
import qnvip.data.overview.domain.eventlog.EventLogAboveDO;
import qnvip.data.overview.dto.track.EventTrackDTO;
import qnvip.data.overview.enums.eventlog.EventLogEnum;
import qnvip.data.overview.service.eventlog.EventLogAboveService;
import qnvip.data.overview.strategy.eventlog.AbstractEventLogStrategy;
import qnvip.data.overview.strategy.eventlog.EventLogStrategyFactory;
import qnvip.rent.common.util.CopierUtil;

import javax.annotation.Resource;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
public class AboveEventLogStrategy extends AbstractEventLogStrategy implements InitializingBean {

    @Resource
    public EventLogAboveService eventLogAboveService;


    @Override
    public void record(EventTrackDTO.InnerDTO t) {
        EventLogAboveDO baseDO = CopierUtil.copy(t, EventLogAboveDO.class);
        eventLogAboveService.save(baseDO);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        EventLogStrategyFactory.register(EventLogEnum.ABOVE.name(), this);
    }
}
