package qnvip.data.overview.strategy.category;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import qnvip.data.overview.mapper.order.RentOrderMapper;
import qnvip.data.overview.strategy.AbstractStrategy;

import javax.annotation.Resource;
import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
@DependsOn("applicationContextUtil")
public class Strategy14 extends AbstractStrategy {


    @Override
    public Map<String, Object> algorithmInterface() {
        return rentOrderMapper.select14();
    }
}
