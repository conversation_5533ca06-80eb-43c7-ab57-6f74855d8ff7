package qnvip.data.overview.strategy.eventlog;

import cn.hutool.json.JSONUtil;
import org.springframework.stereotype.Component;
import qnvip.data.overview.dto.track.EventTrackDTO;
import qnvip.data.overview.enums.eventlog.EventLogEnum;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
public class EventLogClient {


    /**
     *
     * @param json json
     */
    public void record(String json) {
        EventTrackDTO.InnerDTO eventTrackDTO = JSONUtil.toBean(json, EventTrackDTO.InnerDTO.class);
        EventLogEnum eventLogEnum = EventLogEnum.getEnum(eventTrackDTO.getEventKey());
        EventLogContext context = new EventLogContext(eventLogEnum.name());
        context.record(eventTrackDTO);
    }
}
