package qnvip.data.overview.strategy.eventlog;

import cn.hutool.core.lang.Assert;
import com.google.common.collect.Maps;

import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/9/20
 * @since 1.0.0
 */
public class EventLogStrategyFactory {

    private static final Map<String, AbstractEventLogStrategy> strategy = Maps.newConcurrentMap();

    public static AbstractEventLogStrategy getByEnum(String key) {
        return strategy.get(key);
    }

    public static void register(String key, AbstractEventLogStrategy abstractEventLogStrategy) {
        Assert.notNull(key, "eventLogEnum can't be null");
        strategy.put(key, abstractEventLogStrategy);
    }

}
