package qnvip.data.overview.strategy.category;

import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;
import qnvip.data.overview.strategy.AbstractStrategy;

import java.util.Map;

/**
 * <>
 *
 * <AUTHOR>
 * @create 2022/3/8
 * @since 1.0.0
 */
@Component
@DependsOn("applicationContextUtil")
public class Strategy18 extends AbstractStrategy {


    @Override
    public Map<String, Object> algorithmInterface() {
        return rentOrderMapper.select18();
    }
}
